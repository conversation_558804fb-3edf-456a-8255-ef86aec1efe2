import React, { CSSProperties, useCallback, useEffect, useLayoutEffect, useMemo, useRef, useState } from "react";
import { Checkbox, TablePaginationConfig } from "antd";
import { ColumnsType, ColumnType, TableProps } from "antd/es/table";
import { useUpdateEffect } from "ahooks";
import cs from 'classnames';
import { isEqual } from "lodash";
import { useVT } from 'virtualizedtableforantd4';
// import { useVT } from './UseVT';
import { vt_opts } from '@/types/schemas/index';
import Table from '@/components/Table';
import { getTableScroll } from '@/utils/table';
import s from './index.module.scss';
import { ColSortItem } from "./ColSortSetting/ColSortSettingTable";

export type BaseColumnsType<T> = ColumnType<T> | {
	(dataSource: readonly T[]): ColumnType<T> | ColumnType<T>[]
}

export type BaseColumnsTypes<T> = BaseColumnsType<T>[]

export enum ColSetPlaceEnum {
	默认, // 默认显示
	表格上方, // 需要使用tableExtraFn
}
export interface BaseTableProps<T> extends Omit<TableProps<T>, 'columns'> {
	headerColSetId?: string;
	headerColSet?: {
		useDrawer?: Boolean;
		inPlace?: ColSetPlaceEnum;
		resizeId?: string;
		sortId?: string;
		initMap?: {[k: string]: number};
		initList?: ColSortItem[];
		onResizeChange?: (k: ColSortItem[]) => void;
		onReset?: (o: {[k: string]: number}, k: ColSortItem[]) => void;
		onSortChange?: (o: {[k: string]: number}) => void;
	};
	scrollExtraHeight?: number;
	cardHeight?: number;
	fullWindowScroll?: boolean;
	expandContextStickStyle?: CSSProperties,
	expandContext?: React.ReactNode;
	expandContextStyle?: CSSProperties | string;
	expandLayoutStyle?: CSSProperties | string;
	tableExpandContext?: React.ReactNode;
	tableContainerStyle?: CSSProperties;
	columns?: BaseColumnsTypes<T>;
	groupId?: string,
	cachePgination?: boolean,
	subRowSelection?: any,
	noPadding?: boolean,
	vtOptions?:vt_opts,
	innerTableStyle?:CSSProperties | string,
	paginationWrapStyle?:CSSProperties | string,
	offsetScroll?: number;
	hasVt?: boolean,
	noGap?: boolean,
	onFieldsChange?:(changedFields: any, allFields: any) => void
	showSelectedCount?: React.ReactNode
	tableExtra?: React.ReactNode
	tableExtraFn?: (v:React.ReactNode) => React.ReactNode
	emptyNode?: React.ReactNode,
	AUTO_VIRTUAL_THRESHOLD?:number
}

const parseKey = (key: string) => {
	const [id, index, length] = key.split("_");
	return {
		id,
		index: Number(index),
		length: Number(length)
	};
};

const stringifyKey = (id: string, index: string | number, length: string | number) => {
	return `${id}_${index}_${length}`;
};

const defaultPagination: TablePaginationConfig = {
	total: 0,
	defaultPageSize: 10,
	defaultCurrent: 10,
	showQuickJumper: true,
	showSizeChanger: true,
	pageSizeOptions: ["10", "20", "50", "100", "200"],
	showTotal: total => `共${total}条`,
	size: 'default'
};

function BaseTable<T extends object = any>(props: BaseTableProps<T>) {
	const [scrollY, setScrollY] = useState<any>(600);
	const { columns, expandContext, expandLayoutStyle, expandContextStyle, tableExpandContext, pagination, rowSelection, subRowSelection, noPadding, tableExtra, ...reset } = props;
	const defaultContextStyle = useMemo<CSSProperties>(
		() => ({
			display: "flex",
			justifyContent: "flex-start",
			marginBottom: "10px",
		}),
		[]
	);

	const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>(rowSelection?.defaultSelectedRowKeys || []);
	const getColumns = useCallback(() => {
		const c = columns?.reduce((prev, item) => {
			const { expandable = '' } = item;
			if (typeof item === 'function') {
				const _c = item(reset.dataSource);
				if (_c instanceof Array) {
					return prev.concat(..._c);
				}
				return prev.concat(_c);
			}
			// 针对 Table.EXPAND_COLUMN(控制展开列的顺序)需要做特殊处理
			if (expandable && expandable === 'EXPAND_COLUMN') {
				// Table.EXPAND_COLUMN
				return prev.concat(Table.EXPAND_COLUMN);
			}
			return prev.concat({
				align: 'left',
				...item
			});
		}, []);
		if (subRowSelection) {
			const { index = c.length, align = 'center', width = 50, title = '', onChange = () => { } } = subRowSelection;
			c.splice(index, 0, {
				align,
				width,
				title,
				render: (row: any) => {
					return <>{row.originNode}</>;
				}
			});
		}
		// c.forEach(col => {
		// 	if (!col.shouldCellUpdate) {
		// 		col.shouldCellUpdate = (recode: any, prevRecord: any) => {
		// 			return !isEqual(recode, prevRecord);
		// 		};
		// 	}
		// });
		return c;
	}, [columns, reset.dataSource]);

	useUpdateEffect(() => {
		setSelectedRowKeys([]);
	}, [reset.dataSource]);


	/**
	 * 若有rowSelection有设置selectedRowKeys则业务自己维护选择
	 * @param keys
	 * @param rows
	 */
	const setCustomSelectedRowKeys = (keys: any[], rows?: any[]) => {
		let itemRows = rows;
		if (!rowSelection?.selectedRowKeys) {
			setSelectedRowKeys(keys);
		}
		if (!rows) {
			itemRows = reset.dataSource.filter((d) => keys.includes(d[reset.rowKey as string]));
		}
		rowSelection.onChange?.(keys, itemRows);
	};

	const getCustomSelectedRowKeys = () => {
		return rowSelection?.selectedRowKeys || selectedRowKeys;
	};
	/**
	 * ----------------------------------------------------end
	 * @param type
	 * @param rowId
	 */

	const rowChange = (type: number, rowId: string) => {
		const { id, length } = (subRowSelection?.parseFactory ? subRowSelection?.parseFactory : parseKey)(rowId);
		let select = Array.from({ length }, (v, i) => {
			return stringifyKey(id, i, length);
		});
		if (type === 2) {
			setCustomSelectedRowKeys(getCustomSelectedRowKeys().filter(s => !select.includes(s)));
		} else {
			setCustomSelectedRowKeys(Array.from(new Set(getCustomSelectedRowKeys().concat(select))));
		}
	};
	const _renderRadioCell = (value: boolean, record: T, index: number, originNode: React.ReactNode) => {
		record['originNode'] = originNode;
		return rowSelection?.renderCell?.(value, record, index, <></>);
	};
	const _renderCell = (value: boolean, record: T, index: number, originNode: React.ReactNode) => {
		const parseFc = subRowSelection?.parseFactory ? subRowSelection?.parseFactory : parseKey;
		const { id, length } = parseFc(record[props.rowKey as string]);
		let count = 0;
		getCustomSelectedRowKeys().forEach(s => {
			const key = parseFc(s);
			if (key.id === id) {
				count++;
			}
		});
		let node: React.ReactNode;
		if (count === 0) {
			node = <Checkbox onChange={ (e) => { rowChange(0, record[props.rowKey as string]); } } checked={ false } />;
		} else if (count != length) {
			node = <Checkbox onChange={ (e) => { rowChange(1, record[props.rowKey as string]); } } indeterminate />;
		} else {
			node = <Checkbox onChange={ (e) => { rowChange(2, record[props.rowKey as string]); } } checked indeterminate={ false } />;
		}

		record['originNode'] = originNode;
		return rowSelection?.renderCell?.(value, record, index, node);
	};

	const _onChange = (keys: any[], rows: any[]) => {
		setCustomSelectedRowKeys(keys, rows);
	};

	const customSelectedRowKeys = useMemo(() => {
		return rowSelection?.selectedRowKeys || selectedRowKeys;
	}, [rowSelection?.selectedRowKeys, selectedRowKeys]);

	const expandStyle = useMemo(() => {
		const style = {
			contentClassName: cs(props.noGap ? s.expandContextNoGap : s.expandContext),
			contentStyle: {},
			layoutClassName: '',
			layoutStyle: {}
		};
		if (typeof expandContextStyle === 'string') {
			style.contentClassName = cs(props.noGap ? s.expandContextNoGap : s.expandContext, expandContextStyle);
		} else {
			style.contentStyle = { ...expandContextStyle };
		}
		if (typeof expandLayoutStyle === 'string') {
			style.layoutClassName = cs(expandLayoutStyle);
		} else {
			style.layoutStyle = { ...expandLayoutStyle };
		}

		return style;
	}, [expandContextStyle, expandLayoutStyle]);
	
	useEffect(() => {
		let resizeFn = () => {
			setScrollY(props?.scroll?.y || getTableScroll(64 + (props.scrollExtraHeight || 0), props.id));
		};
		window.addEventListener('resize', resizeFn);
		return () => {
			window.removeEventListener('resize', resizeFn);
		};
	}, []);

	useEffect(() => {
		setScrollY(props?.scroll?.y || getTableScroll(64 + (props.scrollExtraHeight || 0), props.id));
		return () => {};
	}, [props]);

	const [vt, setVT, ref] = useVT(() => ({
		overscanRowCount: 5,
		...(props?.vtOptions || {}),
		scroll: { ...(props?.scroll || {}), y: scrollY },
		fullWindowScroll: props.fullWindowScroll
	}), [props.dataSource, scrollY]);

	return (
		<div className={ cs(expandStyle.layoutClassName, props.fullWindowScroll ? s.fullWindowScrollStyle : '') } style={ expandStyle.layoutStyle }>
			{
				!!expandContext && (
					<div className={ cs(expandStyle.contentClassName) } style={ expandStyle.contentStyle }>
						{expandContext}
					</div>
				)
			}
			<div
				className={ cs('r-flex-1', 'r-bg-white', 'innerTableChange', s.innerTable, typeof props?.innerTableStyle === 'string' ? props?.innerTableStyle : '') }
				style={
					{ ...(noPadding ? { padding: 0 } : {}),
						...(typeof props?.innerTableStyle === 'object' ? props?.innerTableStyle : {}) }
				}
			>
				{
					tableExpandContext ? <>{ tableExpandContext }</> : <></>
				}
				{
					tableExtra ? <>{ tableExtra }</> : <></>
				}
				<div>
					<Table
						size="small"
						// bordered
						columns={ getColumns() }
						{ ...(rowSelection ? {
							rowSelection: {
								...rowSelection,
								...{
									renderCell: rowSelection.type === 'checkbox' ? _renderCell : _renderRadioCell,
									selectedRowKeys: customSelectedRowKeys,
									onChange: _onChange
								}
							}
						} : {}) }
						pagination={ typeof pagination === "boolean" ? pagination : { ...defaultPagination, ...(pagination || {}) } }
						{ ...reset }
						scroll={ { ...(props?.scroll || {}), y: scrollY } }
						components={ { ...vt, ...(props?.components || {}) } }
						sticky={ props.fullWindowScroll ? { offsetHeader: `calc(${props.cardHeight}px + 48px)` } : false }
					/>
				</div>
			</div>
		</div>
	);
}
export default BaseTable;
