import React, { useEffect, useRef, useState } from 'react';
import qs from 'qs';
import { useHistory } from 'react-router-dom';
import { Button } from 'antd';
import cs from 'classnames';
import Icon from '@/components/Icon';
import sendPoint from '@/utils/pointTrack/sendPoint';
import Pointer from '@/utils/pointTrack/constants';
import styles from './index.module.scss';
import icon1 from "./assets/icon1.png";
import icon2 from "./assets/icon2.png";
import icon3 from "./assets/icon3.png";
import icon4 from "./assets/icon4.png";
import icon5 from "./assets/icon5.png";
import icon6 from "./assets/icon6.png";
import icon7 from "./assets/icon7.png";
import icon8 from "./assets/icon8.png";
import icon9 from "./assets/icon9.png";
import icon10 from "./assets/icon10.png";
import icon11 from "./assets/icon11.png";
import platforms from "./assets/platforms.png";
import bannerItem from "./assets/banner-item.png";
import highlight from "./assets/highlight.png";
import { useScrollAnimation } from './components/useScrollAnimation';
import Channel from './components/channel';

const App: React.FC = () => {
	const [ref1, isVisible1] = useScrollAnimation();
	const [ref2, isVisible2] = useScrollAnimation();
	const [ref3, isVisible3] = useScrollAnimation();
	const [ref4, isVisible4] = useScrollAnimation();
	const painSolutionsRef = useRef<HTMLDivElement>(null);
	const productAdvantagesRef = useRef<HTMLDivElement>(null);
	const featuredFeaturesRef = useRef<HTMLDivElement>(null);
	const history = useHistory();
	// 获取当前渠道来源
	const getSourceFromUrl = () => {
		const query = qs.parse(history.location.search.substring(1));
		return query.source as string || "";
	};

	// 根据渠道来源获取对应的埋点
	const getPointerBySource = (pointerType: string) => {
		const source = getSourceFromUrl();
		// 根据不同渠道和埋点类型返回对应的埋点ID
		if (source === "360SEM") {
			// 360渠道埋点
			switch (pointerType) {
				case "页面展现": return "64661.65442.91035.91041";
				case "申请试用点击": return "64661.65442.91035.91042";
				case "联系客服点击": return "64661.65442.91035.91043";
				case "登录注册点击": return "64661.65442.91035.91044";
				case "首页tab点击": return "64661.65442.91035.91045";
				default: return "";
			}
		} else if (source === "dayin1") {
			// 番茄打印管家渠道埋点
			switch (pointerType) {
				case "页面展现": return "64661.65442.91034.91036";
				case "申请试用点击": return "64661.65442.91034.91037";
				case "联系客服点击": return "64661.65442.91034.91038";
				case "登录注册点击": return "64661.65442.91034.91039";
				case "首页tab点击": return "64661.65442.91034.91040";
				default: return "";
			}
		} else if (source === "baidu") {
			// 百度渠道埋点
			switch (pointerType) {
				case "页面展现": return "64661.65442.90643.90644";
				case "申请试用点击": return "64661.65442.90643.90645";
				case "联系客服点击": return "64661.65442.90643.90646";
				case "登录注册点击": return "64661.65442.90643.90647";
				case "首页tab点击": return "64661.65442.90643.90648";
				default: return "";
			}
		} else {
			// 纯官网落地页无后缀
			switch (pointerType) {
				case "页面展现": return "64661.65442.91046.91047";
				case "申请试用点击": return "64661.65442.91046.91048";
				case "联系客服点击": return "64661.65442.91046.91049";
				case "登录注册点击": return "64661.65442.91046.91050";
				case "首页tab点击": return "64661.65442.91046.91051";
				default: return "";
			}
		}
	};

	// 发送埋点的通用方法
	const sendCustomPoint = (pointerType: string) => {
		const pointId = getPointerBySource(pointerType);
		if (pointId) {
			sendPoint(pointId);
		} else {
			// 如果没有找到对应的埋点ID，使用默认埋点
			switch (pointerType) {
				case "页面展现": sendPoint(Pointer.落地页页面展现); break;
				case "申请试用点击": sendPoint(Pointer.申请试用点击); break;
				case "联系客服点击": sendPoint(Pointer.联系客服点击); break;
				case "登录注册点击": sendPoint(Pointer.登录注册点击); break;
				case "首页tab点击": sendPoint(Pointer.首页tab点击); break;
				default: break;
			}
		}
	};

	useEffect(() => {
		// 页面加载时发送页面展现埋点
		sendCustomPoint("页面展现");
		(function() { let _53code = document.createElement("script"); _53code.src = "https://tb.53kf.com/code/code/aec0d74e0c9f275aeb54ab1ea9f1ef170/1"; let s = document.getElementsByTagName("script")[0]; s.parentNode.insertBefore(_53code, s); }());
	}, []);

	// 处理申请试用逻辑
	const applyTrial = () => {
		// 获取 URL 中的 source 参数
		const source = getSourceFromUrl();
		// 根据 source 值生成对应的链接
		let url = "https://ding.cjfx.cn/f/cdfaq1c1"; // 默认链接
		if (source === "dayin1") {
			url = "https://ding.cjfx.cn/f/kus56gf3"; // 替换为 dayin1 对应的链接
		} else if (source === "baidu") {
			url = "https://ding.cjfx.cn/f/x40lp85x"; // 替换为 baidu 对应的链接
		} else if (source === "360SEM") {
			url = "https://ding.cjfx.cn/f/grtcay2h"; // 替换为 360SEM 对应的链接，待运营补充
		}
		// 发送埋点并打开链接
		sendCustomPoint("申请试用点击");
		window.open(url);
	};

	const askService = () => {
		sendCustomPoint("联系客服点击");
		window.open("https://tb.53kf.com/code/client/aec0d74e0c9f275aeb54ab1ea9f1ef170/1", "_blank", "width=800,height=600,right=0,top=1000");
	};

	const goLogin = () => {
		sendCustomPoint("登录注册点击");
		window.open(`https://erp.kuaidizs.cn/index.html#/login?source=${getSourceFromUrl()}`);
	};

	const goTo = (text) => {
		if (text === "首页") {
			sendCustomPoint("首页tab点击");
			window.open("https://www.kdzs.com/");
		} else if (text === "渠道招募") {
			window.open("#/channel");
		} else if (text !== "快递助手ERP") {
			const scrollOptions:any = { behavior: 'smooth', block: 'start' };
			switch (text) {
				case '核心功能':
					sendPoint(Pointer.核心场景tab点击);
					painSolutionsRef.current?.scrollIntoView(scrollOptions);
					break;
				case '产品优势':
					sendPoint(Pointer.产品优势tab点击);
					productAdvantagesRef.current?.scrollIntoView(scrollOptions);
					break;
				case '特色功能':
					sendPoint(Pointer.特色功能tab点击);
					featuredFeaturesRef.current?.scrollIntoView(scrollOptions);
					break;
				default:
					break;
			}
		}
	};
	return (
		<div className={ styles.appContainer }>
			{/* 顶部导航 */}
			<nav className={ styles.navBar }>
				<div className={ styles.navLogoContainer }>
					<Icon style={ { color: "#FD8204" } } size={ 216 } type="xinzhushouerplogo" />
					<div className={ styles.navLinks }>
						{[
							{ href: "#", text: "首页" },
							{ href: "#", text: "快递助手ERP" },
							{ href: "#", text: "核心功能" },
							{ href: "#", text: "产品优势" },
							{ href: "#", text: "特色功能" },
							{ href: "#/channel", text: "渠道招募" }
						].map((link, index) => (
							<div key={ index } className={ cs(styles.navLink, link.text === "快递助手ERP" && styles.navLinkActive) } onClick={ () => { goTo(link.text); } }>
								{link.text}
							</div>
						))}
					</div>
				</div>
				<div className={ styles.navButtons }>
					<Button size="large" type="primary" className={ styles.trialButton } onClick={ () => applyTrial() }>申请试用</Button>
					<Button size="large" className={ styles.loginButton } onClick={ () => goLogin() }>登录注册</Button>
				</div>
			</nav>
			{/* Hero区域 */}
			<div className={ styles.heroSection }>
				<div className={ styles.heroBg }>
					<div className={ styles.heroGradient } />
				</div>
				<div className={ styles.heroContent }>
					<div className={ styles.heroBgItem }>
						<img src={ bannerItem } alt="" width={ 430 } height={ 430 } />
					</div>
					<div className={ styles.heroTextContainer }>
						<h1 className={ styles.heroTitle }>快递助手ERP</h1>
						<p className={ styles.heroDescription }>
							电商ERP/Saas电商ERP系统/多平台多店铺统一管理/库存管理进销存/全渠道订单同步打印/AI制作商品资料/助力电商企业降本增效/提升效率提高利润/先免费试用！
						</p>
						<div className={ styles.heroPlans }>
							<div className={ cs(styles.heroPlan, styles.heroPlan1) }>
								<div className={ styles.heroPlanInfo }>
									<h3 className={ styles.heroPlanTitle }>标准版</h3>
									<p className={ styles.heroPlanDesc }>适用于侧重多平台订单管理的商家，此版本主打跨平台多店铺，简易库存，门槛低易上手</p>
								</div>
								<div className="r-flex r-jc-sb r-ai-c">
									<div className="r-fs-24" style={ { color: "#FF4D4F" } }>免费试用</div>
									<Button size="large" type="primary" className={ styles.heroPlanButton } onClick={ () => applyTrial() }>申请试用</Button>
								</div>
							</div>
							<div className={ cs(styles.heroPlan, styles.heroPlan2) }>
								<div className={ styles.heroPlanInfo }>
									<h3 className={ styles.heroPlanTitle }>高级版</h3>
									<p className={ styles.heroPlanDesc }>适用于有较高库存管理需求的商家，此版本主打高精度库存，更全面快销标签，物流预警</p>
								</div>
								<div className="r-flex r-jc-sb r-ai-c">
									<div className="r-fs-24" style={ { color: "#FF4D4F" } }>免费试用</div>
									<Button size="large" type="primary" className={ styles.heroPlanButton } onClick={ () => applyTrial() }>申请试用</Button>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			{/* 核心优势 */}
			<div className={ styles.coreAdvantages }>
				<div className={ styles.coreAdvantagesContainer }>
					{[
						{ icon: icon8, title: "A股电商SaaS第一股", desc: "服务400万商家" },
						{ icon: icon9, title: "领先的技术优势", desc: "国家高新企业" },
						{ icon: icon10, title: "13年大促锤炼", desc: "系统稳定不宕机" },
						{ icon: icon11, title: "订单管理类目TOP1", desc: "市场占有率领先" }
					].map((item, index) => (
						<div key={ index } className={ styles.coreAdvantageItem }>
							<div className={ styles.advantageIcon }>
								<img src={ item.icon } width={ 60 } height={ 60 } alt="" />
							</div>
							<div>
								<h3 className={ styles.advantageTitle }>{item.title}</h3>
								<p className={ styles.advantageDesc }>{item.desc}</p>
							</div>
						</div>
					))}
				</div>
			</div>
			{/* 痛点解决方案 */}
			<div ref={ painSolutionsRef } />
			<div id="painSolutions" ref={ ref1 } className={ cs(styles.painSolutions, { [styles.visible]: isVisible1 }) }>
				<div className={ styles.painSolutionsContainer }>
					<h2 className={ styles.sectionTitle }>痛点&解决方案</h2>
					<div className={ styles.solutionGrid }>
						{[
							{
								icon: "fas fa-question-circle",
								title: "多平台多店铺来回切换、繁琐易出错？",
								desc: "多平台、多店铺、不限账号，订单统一管理，聚合打单，目前支持淘宝、拼多多、抖音、快手、1688、京东、小红书、视频号、淘工厂等10+电商平台。"
							},
							{
								icon: "fas fa-warehouse",
								title: "售后订单积压，退货包裹信息复杂且分散，流程复杂？",
								desc: "多平台多店铺售后订单统一查询处理，扫描核对收货无误后支持自动退款，售后包裹信息流转清晰，效率大幅提升。"
							},
							{
								icon: "fas fa-truck",
								title: "商品sku数量大，配单拣货麻烦？",
								desc: "无需维护编码，AI生成商品资料，快捷开启精细化库存管理，支持货位找货，手机端扫描拣货，让拣货过程顺畅高效。"
							},
							{
								icon: "fas fa-chart-pie",
								title: "商家市场拿货时在多个档口间跑动，费时费力？",
								desc: "支持根据档口拿货路线，生成标签高效进行拿货，减少跑动时间；同时结合标签对账功能确保对账过程精准高效，避免对账混乱。"
							},
							{
								icon: "fas fa-chart-pie",
								title: "多平台多店铺销售数据统计起来麻烦？",
								desc: "支持按平台、店铺、货品等多维度统计销售数据，查看销售趋势。还支持实时销售数据大屏监控，为日常运营提供数据支持"
							},
							{
								icon: "fas fa-chart-pie",
								title: "库存同步不准，不是超额采购就是库存不足？",
								desc: "基于商家库存及进货情况开启库存同步策略，库存自动实时同步，避免超卖，并为采购进货提供依据。"
							}
						].map((solution, index) => (
							<div key={ index } className={ styles.solutionItem }>
								<div>
									<img alt="" width={ 40 } height={ 40 } src={ icon1 } />
								</div>
								<div>
									<h3 className={ styles.solutionTitle }>
										<i className={ solution.icon } />
										{solution.title}
									</h3>
									<p className={ styles.solutionDesc }>{solution.desc}</p>
								</div>
							</div>
						))}
					</div>
				</div>
			</div>
			{/* 支持平台 */}
			<div ref={ ref2 } className={ cs(styles.supportedPlatforms, { [styles.visible]: isVisible2 }) }>
				<div className={ styles.supportedPlatformsContainer }>
					<h2 className={ styles.sectionTitle }>支持主流电商平台</h2>
					<div className={ styles.platformGrid }>
						<img src={ platforms } alt="" height={ 204 } />
					</div>
					<div style={ { marginTop: 60 } }>
						<Button className="r-fs-24 r-pl-24 r-pr-24" style={ { height: 60 } } type="primary" size="large" onClick={ () => askService() }>咨询客服 了解更多</Button>
					</div>
				</div>
			</div>
			{/* 产品优势 */}
			<div ref={ productAdvantagesRef } />
			<div id="productAdvantages" ref={ ref3 } className={ cs(styles.productAdvantages, { [styles.visible]: isVisible3 }) }>
				<div className={ styles.productAdvantagesContainer }>
					<h2 className={ styles.sectionTitle }>快递助手ERP的优势</h2>
					<div className={ styles.productAdvantageGrid }>
						{[
							{
								icon: icon2,
								title: '多平台多店铺',
								desc: '集中管理多平台多店铺订单，告别手动切换平台打单'
							},
							{
								icon: icon3,
								title: '门槛低易上手',
								desc: '专为中小商家量身设计，注册即用，快速上手'
							},

							{
								icon: icon4,
								title: '简易库存管理',
								desc: '轻量化商品、库存管理，化繁为简，满足中小商家需求'
							},
							{
								icon: icon5,
								title: '专业实施培训',
								desc: (
									<div style={ { textAlign: "center" } }>
										<div>手把手培训，上手快</div>
										<div>7*14小时全年无休</div>
									</div>
								)
							},
							{
								icon: icon6,
								title: '亿级订单处理能力',
								desc: '13年技术沉淀，全链路APM监控预警'
							},

							{
								icon: icon7,
								title: '供销代发模式',
								desc: '支持绑定多分销商账号，实现分销订单自动推送至供应商'
							}
						].map((item, index) => (
							<div key={ index } className={ styles.productAdvantageItem }>
								<div className={ styles.productAdvantageContent }>
									<div><img alt="" width={ 60 } height={ 60 } src={ item.icon } /></div>
									<h3 className={ styles.productAdvantageTitle }>{item.title}</h3>
									<p className={ styles.productAdvantageDesc }>{item.desc}</p>
								</div>
							</div>
						))}
					</div>
				</div>
			</div>
			{/* 特色功能 */}
			<div ref={ featuredFeaturesRef } />
			<div ref={ ref4 } className={ cs(styles.featuredFeatures, { [styles.visible]: isVisible4 }) }>
				<div className={ styles.featuredFeaturesContainer }>
					<h2 className={ styles.sectionTitle }>特色亮点功能</h2>
					<div className={ styles.featuredFeaturesGrid }>
						<div className={ styles.featuredFeaturesImgContainer }>
							<div className={ styles.featuredFeaturesImg }><img src={ highlight } alt="" height={ 430 } /></div>
							<div className={ styles.featuredFeaturesInfo }>
								{[
									{
										title: "手机端小程序",
										desc: (
											<>
												<div>扫描快递单，查看订单商品价格、图片、订单状态，精准匹配发货</div>
												<div>手机查询发货记录、售后记录、库存状态，实时掌控店铺动态</div>
											</>
										)
									},
									{
										title: "分销代发",
										desc: (
											<>
												<div>支持手动/自动推单给供应商，发货后回传运单号给分销商</div>
												<div>供应商代发订单扣减库存，一键生成账单，结算简单快捷</div>
											</>
										)
									},
									{
										title: "数据大屏",
										desc: (
											<>
												<div>销售大屏：实时统计销售数据，精准把握销售动向</div>
												<div>物流大屏：实时监控包裹情况，精准分析快递效率</div>
											</>
										)
									},
									{
										title: "快销小标签",
										desc: (
											<>
												<div>跨平台多店铺融合打印，支持批量生成拿货标签</div>
												<div>支持档口拿货路线指引，提升档口取货效率</div>
											</>
										)
									},
									{
										title: "AI做资料",
										desc: (
											<>
												<div>自动化编码：告别手动输入，系统自动生成唯一商品编码，提升效率，避免错误</div>
												<div>批量生成：支持批量导入商品信息，一键生成编码，省时省力</div>
											</>
										)
									}
								].map((feature, index) => (
									<div key={ index } className={ cs(styles.featuredFeatureItem, styles[`featuredFeatureItem${index}`]) }>
										<h3 className={ styles.featuredFeatureTitle }>
											<i className={ styles.featuredFeatureTitleIcon } />
											{feature.title}
										</h3>
										<p className={ styles.featuredFeatureDesc }>{feature.desc}</p>
									</div>
								))}
							</div>
						</div>
					</div>
					<div className={ styles.functions }>
						{
							['多平台打单', '商品管理', '库存管理', '售后管理', '数据报表', '采购管理', '拿货小标签', '备货单', '订单排序', '异常监控', '预发货', '后置打印', '售后自动化', '库存预警'].map((item, index) => {
								return <Button key={ index }>{item}</Button>;
							})
						}
					</div>
					<div style={ { color: "#999" } }>
						更多好用功能，正在路上...
					</div>
				</div>
			</div>
			{/* 页脚部分，这里假设你可能还需要添加页脚，可根据实际情况修改 */}
			<footer className={ styles.footer }>
				<div className={ styles.footerContainer }>
					<Button className={ styles.footerButton1 } size="large" onClick={ () => applyTrial() }>申请试用</Button>
					<Button className={ styles.footerButton2 } size="large" onClick={ () => askService() }>在线客服</Button>
				</div>
			</footer>
		</div>
	);
};

export default App;
