import { computed, makeAutoObservable } from "mobx";
import { omitProps } from "..";
import { IPackage } from "@/pages/Trade/interface";
import userStore from "@/stores/user";
import { INSPECT_METHOD_STOCK, INSPECT_METHOD_ZERO_STOCK } from "@/pages/Trade/InspectSend/constants";
import { TradePrintSetGetInspectionConfigApi, TradePrintSetUpdateInspectionConfigApi, TradeUserGetProductContentCustomSetApi } from "@/apis/trade/inspectSend";
import { TradeUserAddUserCustomSetApi } from "@/apis/trade";

interface InspectedCount {
  total?: number;
	current?: number;
	isChecked?: boolean;
	rowData?:{[k: string]: any};
}
interface InspectedProgress {
  [k: string]: InspectedCount;
}

const defaultPackList = [{
	isActive: true,
	togetherId: "togetherId",
	trades: [{
		demo: true,
		orders: []
	}]
}];

class InspectSendStore {
	constructor(parentStore: omitProps) {
		makeAutoObservable(this);
		this.parentStore = parentStore;
	}

	@computed get inspectedCount() {
		let total = 0;
		let current = 0;
		
		for (let key in this.inspectedProgress) {
			const curGoodInspectInfo = this.inspectedProgress[key];
			total += curGoodInspectInfo?.total;
			current += curGoodInspectInfo?.current;
		}
		return {
			total,
			current,
			isForce: this.isForce,
		};
	}



	parentStore

	// 原有设置
	oldSetting:any = {}

	// 显示高级设置
	advancedSettingModalVisible = false;

	// 产品内容设置
	contentSettingModalVisible = false;

	contentSetting :any= {};

	// 本次扫描统计
	totalNum = {
		inspectionPackageNum: 0, // 验货包裹
		verifyGoodsNum: 0, // 核验商品
		sendSuccessNum: 0, // 发货成功
		sendFailNum: 0, // 发货失败
	}

	setTotalNum = (k:string, v:number) => {
		this.totalNum[k] += v;
	}

	resetTotalNum = (k:string, v:number) => {
		this.totalNum[k] = v;
	}

	clearTotalNum = () => {
		this.totalNum = {
			inspectionPackageNum: 0, // 验货包裹
			verifyGoodsNum: 0, // 核验商品
			sendSuccessNum: 0, // 发货成功
			sendFailNum: 0, // 发货失败
		};
	}

	setContentSettingModalVisible = (v:boolean) => {
		this.contentSettingModalVisible = v;
	}

	setAdvancedSettingModalVisible = (v:boolean) => {
		this.advancedSettingModalVisible = v;
	}

	// 高级设置
	advancedSettingConfig :any= {
		inspectBarCode: INSPECT_METHOD_STOCK.default,
		hasCheckItem: false,
		hasAutoSend: false
	};

	updateAdvancedSettingConfig = async(v) => {
		await TradePrintSetUpdateInspectionConfigApi({
			...this.oldSetting,
			newInspectionConfig: JSON.stringify(v),
			subUserInspectionConfig: {
				...this.oldSetting?.subUserInspectionConfig || {},
				videoMonitor: v?.videoMonitor
			}
		});
		this.advancedSettingConfig = v;
	}

	updateContentSettingConfig = async(v) => {
		await TradeUserAddUserCustomSetApi(v);
		this.contentSetting = v;
	}

	getAdvancedSettingConfig = async() => {
		// const config = await memoFn.getAdvancedSet();
		const config = await TradePrintSetGetInspectionConfigApi({});
		console.log('configconfig', config);
		this.oldSetting = config;
		const { isShowZeroStockVersion } = userStore;
		if (config?.newInspectionConfig) {
			try {
				const inspectionConfig = JSON.parse(config?.newInspectionConfig);
				this.advancedSettingConfig = inspectionConfig;
				this.setVideoMonitorState(!!config?.subUserInspectionConfig?.videoMonitor);
			} catch (e) {
				console.log(e);
			}
		} else if (isShowZeroStockVersion) {
			this.advancedSettingConfig["inspectMethod"] = INSPECT_METHOD_ZERO_STOCK.default;
		}
		return this.advancedSettingConfig;
	}

	getContentSettingConfig = async() => {
		// const config = await memoFn.getContentSet();
		const config = await TradeUserGetProductContentCustomSetApi({ });
		this.contentSetting = config;
		return config;
	}
	
	// 订单VO
	tradeList:IPackage[] = defaultPackList;

	videoMonitorState = false;

	setVideoMonitorState = (v:boolean) => {
		this.videoMonitorState = v;
	}

	selectedShootingDevices = [];

	setSelectedShootingDevices = (v) => {
		this.selectedShootingDevices = v;
	}

	nvrDevices = [];

	setNvrDevices = (v) => {
		this.nvrDevices = v;
	}

	setTradeList = (list:IPackage[]) => {
		this.tradeList = list;
	}

	// 当前订单是否已经有售后
	hasBeenAfterSale = false;

	setHasBeenAfterSale = (v:boolean) => {
		this.hasBeenAfterSale = v;
	}

	// 当前订单是否存在非待发货
	hasNoAwatingSend = false;

	setHasNoAwatingSend = (v:boolean) => {
		this.hasNoAwatingSend = v;
	}
	
	// 自动发货拦截各种状态统计
	interceptInfo = {
		refoundList: [], // 退款中
		sendList: [], // 已发货
		finishList: [], // 交易成功
		closeList: [], // 已关闭
		buyerMessageList: [], // 有留言
		sellerMemoList: [], // 有备注
		sellerFlagList: [], // 有旗帜
	}

	clearInterceptInfo = () => {
		this.interceptInfo = {
			refoundList: [], // 退款中
			sendList: [], // 已发货
			finishList: [], // 交易成功
			closeList: [], // 已关闭
			buyerMessageList: [], // 有留言
			sellerMemoList: [], // 有备注
			sellerFlagList: [], // 有旗帜
		};
	}

	setInterceptInfo = (key: string, v: any) => {
		this.interceptInfo[key].push(v);
	}
	// 单独维护一份已验数量的数据，这样依赖验货数量的地方都可以自动响应

	inspectedProgress: InspectedProgress= {};

	isForce:boolean = false;

	getInspectedCount = () => {
		let total = 0;
		let current = 0;
		
		for (let key in this.inspectedProgress) {
			const curGoodInspectInfo = this.inspectedProgress[key];
			total += curGoodInspectInfo?.total;
			current += curGoodInspectInfo?.current;
		}
		return {
			total,
			current,
			isForce: this.isForce,
		};
	}

	setInspectedProgress = (v, isForce = false) => {
		this.inspectedProgress = v;
		this.isForce = isForce || false;
	}

	getInspectedProgress = () => {
		return this.inspectedProgress;
	}


	selectedRowKeys = []
	
	setSelectedRowKeys = (keys) => {
		this.selectedRowKeys = keys;
	}

	// 是否忽略验货

	ignoreInspectOrder = {};

	setIgnoreInspectOrder = (v) => {
		this.ignoreInspectOrder = v;
	}

	getIgnoreInspectOrder = () => {
		return this.ignoreInspectOrder;
	}

	// 当前扫描到的商品货品信息
	curScanGoodInfo:{[k: string]: any} = {};

	setCurScanGoodInfo = (v) => {
		this.curScanGoodInfo = v;
	}

	// 页面整体loading

	showLoading = false;

	setShowLoading = (v:boolean) => {
		this.showLoading = v;
	}

	// 是否处于发货中

	sendPending = false;

	setSendPending= (v:boolean) => {
		this.sendPending = v;
	}

	initStore = () => {
		this.setTradeList(defaultPackList);
		this.setSendPending(false);
		this.setShowLoading(false);
		this.setHasNoAwatingSend(false);
		this.setHasBeenAfterSale(false);
		this.setInspectedProgress({});
		this.setIgnoreInspectOrder({});
		this.setCurScanGoodInfo({});
		// this.clearTotalNum();
	}

}

export default InspectSendStore;
