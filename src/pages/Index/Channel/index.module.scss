.navBar {
	height         : 80px;
	display        : flex;
	align-items    : center;
	justify-content: space-between;
	border-bottom  : 1px solid #e5e7eb;
	max-width      : 1200px;
	margin         : 0 auto;
}

// 工具类样式
.channel-wrap {
	// 主容器样式
	background-color: #fff;
}

.align-c {
	text-align: center;
}

.inline-block {
	display: inline-block;
}

.height-100 {
	height: 100%;
}

// 原有的channel样式保持不变
.channel-header {
	height         : 366px;
	background     : url('./assets/banner.png');
	background-size: cover;
}

.channel-header-l {
	vertical-align: top;
	margin-right  : 100px;
	text-align    : left;
}

.channel-header-test1 {
	font-size     : 48px;
	color         : #FFFFFF;
	line-height   : 48px;
	margin-top    : 30px;
	border-bottom : 1px solid #fff;
	padding-bottom: 25px;
}

.channel-header-test2 {
	font-size: 16px;
	color    : #FFFFFF;
}

.channel-header-label-box {
	margin-top: 65px;
}

.channel-header-label {
	display      : inline-block;
	font-size    : 14px;
	color        : #fff;
	text-align   : center;
	padding      : 3px 20px;
	border       : 1px solid #fff;
	border-radius: 20px;
}

.channel-header-label+.channel-header-label {
	margin-left: 20px;
}

.channel-header-zixun {
	display      : block;
	width        : 142px;
	height       : 42px;
	line-height  : 42px;
	font-size    : 16px;
	color        : #FD674F;
	text-align   : center;
	font-weight  : 500;
	margin-top   : 20px;
	background   : #FFFFFF;
	border-radius: 21px;
}

.channel-header-zixun:hover {
	color          : #FD674F;
	text-decoration: none;
}

.channel-tabs {
	background: #fff;
}

.channel-tabs-con {
	display        : flex;
	justify-content: space-around;
	width          : 1080px;
	padding        : 16px 0;
	margin         : 22px auto 0 auto;
	border-bottom  : 1px solid #e6e6e6;
}

.tabs-fixed {
	position  : fixed;
	z-index   : 99999;
	box-shadow: 0 3px 7px 0 rgba(66, 56, 49, 0.10);
	margin    : 0;
	left      : 0;
	right     : 0;
	top       : 0;
}

.tabs-fixed .channel-tabs-con {
	border: none;
}

.channel-tab {
	font-size      : 20px;
	color          : #3D4A5E;
	text-align     : center;
	cursor         : pointer;
	text-decoration: none;
}

.channel-tab:visited,
.channel-tab:link {
	color          : #3D4A5E;
	text-decoration: none;
}

.channel-tab:hover {
	color          : #FF730D;
	text-decoration: none;
}

.channel-tab.act {
	position: relative;
	color   : #FF730D;
}

.channel-tab.act::after {
	position     : absolute;
	content      : '';
	width        : 36px;
	height       : 3px;
	background   : #FF730D;
	border-radius: 2px;
	bottom       : -17px;
	left         : 0;
	right        : 0;
	margin       : 0 auto;
}

.channel-header-r img {
	margin-top: 30px;
}

.channel-part1 {
	background    : #fff;
	padding-bottom: 50px;
}

.channel-product {
	text-align: center;
	margin-top: 60px;
}

.channel-part2 {
	background    : #FBFBFB;
	padding-bottom: 32px;
}

.channel-list {
	display        : flex;
	flex-wrap      : wrap;
	justify-content: space-between;
	width          : 1080px;
	margin         : 44px auto 25px auto;
}

.channel-item {
	width     : 320px;
	height    : 175px;
	padding   : 20px 20px 17px 20px;
	background: #fff;
	box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
	box-sizing: border-box;
}

.channel-item-top {
	margin-bottom: 24px;
}

.channel-item-top img {
	margin-right: 16px;
}

.channel-item-top span {
	font-size: 20px;
	color    : #3D4A5E;
}

.channel-item-bottom span {
	font-size: 12px;
	color    : #7F8896;
}

.channel-part3 {
	padding-bottom: 80px;
}

.channel-plat {
	position  : relative;
	width     : 1080px;
	text-align: center;
	margin    : 60px auto 0 auto;

	img {
		width: 100%;
	}
}

.channel-we {
	margin-bottom: 30px;
}

.channel-we-con {
	display        : flex;
	align-items    : center;
	justify-content: space-between;
	width          : 1080px;
	margin         : 30px auto 0 auto;
}

.channel-we-text {
	width    : 484px;
	font-size: 16px;
	color    : #3D4A5E;
}

.channel-we-text div {
	text-indent: 32px;
}

.channel-we-img {
	width: 410px;
}

.channel-join-we {
	padding-bottom: 80px;
}

.channel-join-we-con {
	display        : flex;
	justify-content: center;
	width          : 1080px;
	margin         : 44px auto 0 auto;
}

.channel-join-we-btn {
	position     : relative;
	padding      : 8px 36px;
	font-size    : 24px;
	text-align   : center;
	font-weight  : 600;
	border-radius: 100px;
	cursor       : pointer;
}

.channel-join-we-solid-btn {
	color       : #FFFFFF;
	margin-right: 24px;
	background  : #FF730D;
}

.channel-join-we-hollow-btn {
	color     : #FF730D;
	background: #fff;
	border    : 1px solid #FF730D;
}

.channel-join-we-solid-btn:hover {
	color          : #fff;
	text-decoration: none;
}

.channel-join-we-solid-btn:visited,
.channel-join-we-solid-btn:link {
	color          : #fff;
	text-decoration: none;
}

.channel-join-we-QRCode {
	position  : absolute;
	padding   : 10px;
	background: #fff;
	border    : 2px solid #FF7002;
	z-index   : 9999;
	top       : -72px;
	left      : 160px;
	display   : none;
}

.channel-join-we-QRCode img {
	width: 172px;
}

.channel-join-we-hollow-btn:hover .channel-join-we-QRCode {
	display: block;
}

// 模块标题样式
.module-title {
	text-align : center;
	padding-top: 40px;
}

.module-title h3 {
	margin-top   : 33px;
	color        : #3D4A5E;
	position     : relative;
	z-index      : 999;
	font-size    : 24px;
	margin-bottom: 10px;
}

.module-title p {
	color   : #7F8896;
	position: relative;
}

.module-title p:after {
	content     : " ";
	position    : absolute;
	width       : 88px;
	height      : 3px;
	background  : #FF730D;
	bottom      : -6px;
	right       : 50%;
	margin-right: -44px;
}

.module-title h3:before {
	content        : '';
	position       : absolute;
	width          : 68px;
	height         : 47px;
	background     : url('./assets/title-icon.png');
	background-size: 68px 47px;
	top            : 50%;
	margin-top     : -45px;
	right          : 50%;
	margin-right   : -42px;
	z-index        : -1;
}

// 响应式样式
@media (max-width: 1200px) {
	.channel-tabs-con {
		width    : 90%;
		max-width: 1080px;
	}

	.channel-we-con,
	.channel-list,
	.channel-join-we-con,
	.channel-plat {
		width    : 90%;
		max-width: 1080px;
	}
}

@media (max-width: 768px) {
	.channel-header {
		height : auto;
		padding: 40px 20px;
	}

	.channel-header-l {
		margin-right : 0;
		margin-bottom: 30px;
	}

	.channel-header-test1 {
		font-size  : 32px;
		line-height: 32px;
	}

	.channel-tabs-con {
		flex-direction: column;
		padding       : 10px 0;
	}

	.channel-tab {
		margin-bottom: 10px;
	}

	.channel-we-con {
		flex-direction: column;
		text-align    : center;
	}

	.channel-we-text {
		width        : 100%;
		margin-bottom: 30px;
	}

	.channel-we-img {
		width    : 100%;
		max-width: 410px;
	}

	.channel-list {
		flex-direction: column;
		align-items   : center;
	}

	.channel-item {
		width        : 100%;
		max-width    : 320px;
		margin-bottom: 20px;
	}

	.channel-join-we-con {
		flex-direction: column;
		align-items   : center;
	}

	.channel-join-we-btn {
		margin-bottom: 20px;
	}
}