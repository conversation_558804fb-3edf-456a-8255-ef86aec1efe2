.container {
	padding: 20px 0;
}

.section {
	margin-bottom: 24px;
}

.sectionTitle {
	font-size: 16px;
	font-weight: 600;
	margin-bottom: 16px;
	color: #333;
}

.configOptions {
	display: flex;
	flex-direction: column;
	gap: 20px;
}

.optionGroup {
	border: 1px solid #d9d9d9;
	border-radius: 8px;
	padding: 16px;
	background: #fafafa;
	transition: all 0.3s ease;

	&:hover {
		border-color: #1890ff;
		background: #f0f8ff;
	}
}

.optionHeader {
	margin-bottom: 12px;

	:global(.ant-radio-wrapper) {
		font-size: 16px;
		font-weight: 500;
		color: #333;
	}
}

.optionContent {
	padding-left: 24px;
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.optionItem {
	display: flex;
	align-items: center;
	gap: 8px;
	font-size: 14px;
	color: #666;
	line-height: 1.5;
}

.optionNumber {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	width: 20px;
	height: 20px;
	border-radius: 50%;
	background: #1890ff;
	color: white;
	font-size: 12px;
	font-weight: 500;
	flex-shrink: 0;
}

// 选中状态样式
.optionGroup:has(:global(.ant-radio-checked)) {
	border-color: #1890ff;
	background: #f0f8ff;

	.optionNumber {
		background: #52c41a;
	}
}