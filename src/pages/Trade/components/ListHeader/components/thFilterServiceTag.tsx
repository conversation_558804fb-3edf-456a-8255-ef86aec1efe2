/*
 * @Author: 谭飞宁 <EMAIL>
 * @Date: 2022-05-30 16:54:21
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-08-22 15:39:51
 * @FilePath: /kdzs-erp-web/src/pages/Trade/components/ListHeader/components/thFilterServiceTag.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import React, { useEffect, useState } from 'react';
import { Button, Checkbox } from 'antd';
import _ from 'lodash';
import { tradeStore } from '@/stores';
import { ConsolidateType, FxgConsolidateType, filterConsolidateType, filterServiceTagType, getServiceFilterKey, serviceTagTypeMap } from '@/pages/Trade/constants';
import { KsConsolidateType, TradeToPackEnum } from '@/pages/Trade/utils';
import { HL_LABEL_PLAT } from '@/constants';
import { SendType } from '@/utils/enum/trade';

export interface IThFilterServiceTag {
    onClose: () => void;
	visible: boolean;
}

export interface IThFilterItem {
    name: string,
    value: string,
    isChecked: boolean
}


const ThFilterServiceTag = (props: IThFilterServiceTag) => {
	const { onClose, visible } = props;
	const [filterList, setFilterList] = useState<IThFilterItem[]>([]);
	const [prevFilterList, setPrevFilterList] = useState<IThFilterItem[]>([]);
	const handleOk = () => {
		setPrevFilterList(_.cloneDeep(filterList));
		let checkedList = filterList.filter(item => item.isChecked).map(item => item.value);
		const { tradeListStore: { setListCheckStatus, list: allList, setList } } = tradeStore;
		setListCheckStatus(0);
		let list = allList.map(item => {
			let isFilterByStatus = checkedList.length ? !checkedList.some(key => item[key]) : false;
			if (checkedList.length) {
				checkedList.forEach(check => {
					if (check.indexOf('promiseLogisticsList') > -1) {
						item.promiseLogisticsList?.forEach((c:any) => {
							if (c.exCode === check.split('-')[1]) {
								isFilterByStatus = false;
							}
						});
					}
					if (check.indexOf('duoduoWholesale') > -1 && item.trades.some(trade => trade.duoduoWholesale)) {
						isFilterByStatus = false;
					}
					if (check.indexOf('shipHold') > -1 && item.trades.some(trade => trade.shipHold)) {
						isFilterByStatus = false;
					}
				});
			}
			return {
				...item,
				isChecked: false,
				isExpand: false,
				isFilterByStatus,
				isFilter: item.isFilterByPlatform || isFilterByStatus,
			};
		});
		setList(list);
		onClose();
	};
	const handleChange = (index:number) => {
		setFilterList(prev => {
			prev[index].isChecked = !prev[index].isChecked;
			return [...prev];
		});
	};
	useEffect(() => {
		let _filterList:Array<IThFilterItem> = [];
		let allList = tradeStore.tradeListStore.list;
		let key2Name = {
			...HL_LABEL_PLAT,
			'hasRefund': '有退款',
			'urge': '催发货',
			'isPreSale': '含预售商品',
			'isPartShipped': '部分发货',
			'isPreShip': '自动发货',
			'isPending': '有挂起',
			'isMerge': '合单',
			'isSplitSend': '进行拆单发货',
			'labelstatus': '已生成商品标签',
			'isTotalPostFee': '含运费',
			'firstSend': '先发货',
			'hasTown': '乡镇',
			'hasSfExpressService': '加运费发顺丰',
			'hasGift': '含平台赠品',
			'changeAdderFlag': '改地址',
			'sendType7': '重新发货',
			'isSplit': '拆单',
			'bizWave': '已生成拣货波次',
			'jingxi': '京喜订单',

			'jdjc': '京仓订单',
			'jdyc': '云仓订单',
			'jdjp': '京配订单',
			...filterConsolidateType,
			'consolidate_fxg': '物流转运/集运',
			'ciphertextHandTrade': '密文手工单',
			'duoduoWholesale': '多多批发',
			'shipHold': '暂停发货',
			'promiseDeliveryTime': '优先发货',
			'wrapperDescription': '指定包材',
			'freeSF': '顺丰包邮',
			'isSysExchanged': '系统改商品',
			'isOnlineExchanged': '线上改商品',
			...TradeToPackEnum,
			'hlEncryptOrderTrue': '1688密文订单',
			'hlEncryptOrderFalse': '1688明文订单',
			'appointmentArrival': '承诺日达',
			'needSerialNumber': '商品识别码',
			'aliEntryServiceInfo': '定制服务',
			'hasDeliveryOnDoor': '送货上门',
			'directMailActivity': '直邮活动',
			...filterServiceTagType,
			'fullOrderShip': '整单发货',
			'appendSend': '追加包裹',
			'_brandDeliver': '品牌直发',
			'_ordinaryOrder': '普通履约',
			'_promiseLogistics': '商家指定物流',
			'_warehouseOrder': '多仓发货',
			'_clearanceOrder': '清仓订单'
		};
		let obj = {};
		allList.forEach(item => {
			if (item.serviceTagList?.includes('full_order_ship')) {
				item.fullOrderShip = true;
			}
			if (item.serviceTagList?.includes('brandDeliver')) {
				item._brandDeliver = true;
			}
			if (item.serviceTagList?.includes('ordinaryOrder')) {
				item._ordinaryOrder = true;
			}
			if (item.serviceTagList?.includes('promiseLogistics')) {
				item._promiseLogistics = true;
			}
			if (item.serviceTagList?.includes('warehouseOrder')) {
				item._warehouseOrder = true;
			}
			if (item.serviceTagList?.includes('clearanceOrder')) {
				item._clearanceOrder = true;
			}
			// 处理拆单标签
			if (item.trades.some(trade => trade.isSplit)) {
				item.isSplit = true;
			}
			if (item.trades.some(trade => trade.serviceTagList.includes('ciphertextHandTrade'))) {
				item.ciphertextHandTrade = true;
			}
			if (Array.isArray(item.waveNoList) && item.waveNoList.length > 0) {
				item.bizWave = true;
			}
			// jingxidingdan
			if (item.trades.some(trade => trade.serviceTagList.includes('jingxi'))) {
				item.jingxi = true;
			}
			// 处理拼多多集运订单
			const curPackConsolidateType = ConsolidateType[item.consolidateType];
			if (curPackConsolidateType) {
				item[curPackConsolidateType.key] = true;
			}

			// serviceTagList中，需要标签筛选的，例如偏远直邮
			Object.keys(serviceTagTypeMap).forEach(key => {
				if (item.serviceTagList.includes(key)) {
					item[getServiceFilterKey(key)] = true;
				}
			});

			// 重新发货
			if (item.sendType === SendType.重新发货) {
				item.sendType7 = true;
			}

			// 1688回流订单
			const hlLabel = HL_LABEL_PLAT[item.hlPlatformType];
			if (hlLabel) {
				item[item.hlPlatformType] = true;
				item[item.hlEncryptOrder ? 'hlEncryptOrderTrue' : 'hlEncryptOrderFalse'] = true;
			}

			// 处理抖音物流中转
			const fxgConsolidateType = FxgConsolidateType[item.consolidateType];
			if (fxgConsolidateType) {
				item['consolidate_fxg'] = true;
			}

			const ksConsolidateType = KsConsolidateType[item.consolidateType];
			if (ksConsolidateType) {
				item['consolidate_fxg'] = true;
			}

			if (item.consolidateType == 'tb_gfjy') {
				item['consolidate_fxg'] = true;
			}
			Object.keys(key2Name).forEach(key => {
				if (item[key]) {
					obj[key2Name[key]] = key;
				}
			});

			if (item.promiseLogisticsList.length > 0) {
				item.promiseLogisticsList?.forEach((item:any) => {
					if (item.company) {
						obj[`指定:${item.company}`] = `promiseLogisticsList-${item.exCode}`;
					}
				});
			}
		});
		Object.keys(obj).forEach(key => {
			_filterList.push({
				name: key,
				value: obj[key],
				isChecked: false
			});
		});
		setFilterList(_filterList);
	}, []);
	return (
		visible ? (
			<>
				<div className="ant-modal-mask" />
				<div className="th-filter-container">
					<div className="th-filter-title">
						<span className="th-filter-main-title">订单标签筛选</span>
						<span className="th-filter-sub-title">（仅筛选当前页）</span>
					</div>
					<div className="th-filter-body">
						{filterList.length ? filterList.map((item, index) => (
							<label className="th-filter-item" key={ item.name }>
								<Checkbox type="checkbox" checked={ item.isChecked } onChange={ () => { handleChange(index); } } />
								<span className="th-filter-label r-ml-16">{item.name}</span>
							</label>
						)) : <span className="th-filter-item r-jc-c">本页暂无可筛选内容</span>}
					</div>
					<div className="th-filter-footer">
						<Button size="small" className="r-mr-16 r-c-666" onClick={ onClose }>取消</Button>
						<Button type="primary" size="small" onClick={ handleOk }>确定</Button>
					</div>
				</div>
			</>
		) : null
	);
};


export default ThFilterServiceTag;
