.welfareGiftModal {
	:global {
		.ant-modal-content {
			padding: 17px;
		}
		
		.ant-modal-body {
			padding: 0;
		}
	}
}

.modalContent {
	position: relative;
	text-align: center;
	background-color: #fff;
}

.titleSection {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 17px;
}

.iconWrapper {
	margin-right: 8px;
}

.icon {
	width: 32px;
	height: 32px;
}

.title {
	font-size: 20px;
	line-height: 30px;
	font-weight: bold;
	color: rgba(0, 0, 0, 0.85);
}

.mainContent {
	font-size: 14px;
	line-height: 22px;
	margin-bottom: 4px;
	color: rgba(0, 0, 0, 0.85);
}

.highlightUserType {
	color: #FD8204;
}

.freeText {
	color: rgba(0, 0, 0, 0.85);
	font-weight: bold;
}

.giftCount {
	color: #ff4d4f;
	font-size: 14px;
	font-weight: bold;
}

.validityInfo {
	font-size: 12px;
	line-height: 20px;
	color: rgba(0, 0, 0, 0.45);
	margin-bottom: 16px;
}

.claimButton {
	width: 88px;
	font-size: 14px;
	height: 32px;
}