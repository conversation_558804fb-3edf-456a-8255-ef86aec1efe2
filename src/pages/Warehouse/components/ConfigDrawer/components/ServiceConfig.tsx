import React, { useState, useEffect } from 'react';
import { Form, Radio, Input, Select, Button } from 'antd';
import { PlusOutlined, ReloadOutlined } from '@ant-design/icons';
import s from './index.module.scss';
import { getUserExpressTemplate } from '@/apis/user'; // 导入获取运费模板的API
import { PLAT_ALI, PLAT_FXG, PLAT_KS, PLAT_PDD, PLAT_SPH, PLAT_TB, PLAT_TM } from '@/constants';

interface ServiceConfigProps {
  form: any;
  onChange?: (values: { freightTemplate: string }) => void;
  platform?: string; // 平台参数
  productId?: string; // 商品ID参数
  isAli?: boolean;
}

const optionList = [
	{
		label: '请选择运费模板',
		value: '2'
	}, {
		label: '卖家包邮',
		value: '1'
	}
];

const ServiceConfig: React.FC<ServiceConfigProps> = ({ 
	form, 
	onChange,
	platform,
	productId,
	isAli
}) => {
	const [templateOptions, setTemplateOptions] = useState<Array<{id?: string, name?: string}>>([]);
	const [loading, setLoading] = useState(false);

	// 获取运费模板列表
	const fetchExpressTemplates = async() => {
		setLoading(true);
		try {
			const data = {
				shopId: productId,
				platform,
				pageSize: 100,
				pageNo: 1
			};
			const response = await getUserExpressTemplate(data);
			if (response.list && Array.isArray(response.list)) {
				console.log(response.list, 'response1111');
				setTemplateOptions(response.list);
			} 
		} catch (error) {
			// 接口失败时设置默认选项
			console.log(error, 'error');
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		// 获取运费模板列表
		fetchExpressTemplates();
	}, [platform, productId]);


	// 处理添加按钮点击事件
	const handleAddClick = () => {
		// 根据平台打开不同的URL
		let url = '';
    
		if (platform) {
			switch (platform.toLowerCase()) {
				case PLAT_FXG:
					url = `https://fxg.jinritemai.com/ffa/morder/logistics/freight-list`;
					break;
				case PLAT_PDD:
					url = `https://mms.pinduoduo.com/orders/order/carriage/list?msfrom=mms_sidenav`;
					break;
				case PLAT_TB:
				case PLAT_TM:
					url = `https://myseller.taobao.com/home.htm/consign-tools-group/freightTemplate`;
					break;
				case PLAT_ALI:
					url = `https://air.1688.com/app/ctf-page/logistics-setting/freight-template.html?spm=a28888.publish.freight.1.74de3793Peqhc9&type=customTemplate`;
					break;
				case PLAT_KS:
					url = `https://s.kwaixiaodian.com/zone/supply/address/freight-template/list`;
					break;
				case PLAT_SPH:
					url = `https://store.weixin.qq.com/shop/address/deliveryMgmt`;
					break;
				default:
					console.warn('未知平台:', platform);
					return;
			}
			// 在新标签页中打开URL
			window.open(url, '_blank');
		} else {
			console.log(productId, 'productId');
		}
	};

	return (
		<div className={ s.serviceConfigContainer }>
			<Form.Item label="运费模板：" className={ s.serviceFormItem }>
				<div className={ s.serviceFormControl }>
					{
						platform === PLAT_ALI && (
							<Form.Item name="freightTemplateType" noStyle>
								<Select 
									style={ { width: 140 } } 
									className={ s.serviceSelect }
									loading={ loading }
									dropdownStyle={ { zIndex: 1100 } } 
									placeholder="请选择"
								>
									{optionList.map(template => (
										<Select.Option key={ template.value } value={ template.value }>
											{template.label}
										</Select.Option>
									))}
								</Select>
							</Form.Item>
						)
					}
					<Form.Item
						shouldUpdate={ (prevValues, currentValues) => prevValues.freightTemplateType !== currentValues.freightTemplateType }
						noStyle
					>
						{({ getFieldValue }) => {
							const shouldShow = (platform === PLAT_ALI && getFieldValue('freightTemplateType') === '2') 
								|| platform !== PLAT_ALI;
							
							return shouldShow ? (
								<>
									<span className={ s.serviceLabel }>统一设置为</span>
				
									<Form.Item name="freightTemplate" noStyle>
										<Select 
											style={ { width: 180 } } 
											className={ s.serviceSelect }
											loading={ loading }
											dropdownStyle={ { zIndex: 1100 } } 
											placeholder="请选择"
										>
											{templateOptions.map(template => (
												<Select.Option key={ template.id } value={ template.id }>
													{template.name}
												</Select.Option>
											))}
										</Select>
									</Form.Item>
				
									<Button 
										type="link" 
										className={ s.serviceAddBtn }
										onClick={ fetchExpressTemplates }
										title="刷新运费模板"
									>
										<ReloadOutlined style={ { marginTop: '2px' } } />
									</Button>
									<Button 
										type="link" 
										className={ s.serviceAddBtn }
										onClick={ handleAddClick }
									>
										<PlusOutlined style={ { marginTop: '2px' } } /> 添加
									</Button>
								</>
							) : null;
						}}
					</Form.Item>
					
				</div>
			</Form.Item>
		</div>
	);
};

export default ServiceConfig;
