import React, { useState, useEffect } from 'react';
import { Button, message, Modal, Tooltip } from 'antd';
import { observer } from 'mobx-react';
import styles from './index.module.scss';
import { QueryPlatformItemCountLimitInfo } from '@/apis/user'; // 假设有这个API
import Icon from '../Icon';
import { showPayDrawer } from '@/components-biz/PayDrawer';
import { PAY_TYPE } from '@/components-biz/PayDrawer/interface';
import userStore from '@/stores/user';

interface LimitInfoBarProps {
	/** 自定义类名 */
	className?: string;
	/** 自定义样式 */
	style?: React.CSSProperties;
}

// ○ currentCount 当前平台商品数量
// ○ limitCount 平台商品上限数量
// ○ residueCount 剩余平台商品上限数量
// ○ checkFlag 是否开启用户 T校验 F不校验
interface Idata {
	checkFlag?: boolean;
	currentCount?: string;
	limitCount?: string;
	residueCount?: string;
}

/**
 * 平台商品限制信息条组件
 * 内部管理数据，外部不需要传值
 */
const LimitInfoBar: React.FC<LimitInfoBarProps> = ({
	className = '',
	style = {},
}) => {
	// 内部状态
	const [data, setData] = useState<Idata>({});
	const [loading, setLoading] = useState<boolean>(true);
	const [visible, setVisible] = useState(false);
	const { userInfo } = userStore;
	// 查看详情
	const handleViewClick = () => {
		setVisible(true);
	};
	const fetchData = async() => {
		try {
			setLoading(true);
			// 这里替换为实际的API调用
			const response = await QueryPlatformItemCountLimitInfo({});
			if (response) {
				setData(response);
				if (Number(response.residueCount || 0) < 0) {
					setVisible(true);
				}
			}

		} catch (error) {
			console.error('获取限制信息失败:', error);

		} finally {
			setLoading(false);
		}
	};
	// 获取数据
	useEffect(() => {
		fetchData();
	}, []);

	const showResidueCount = () => {
		// 如果 residueCount 为 undefined 或 null，默认为 0
		if (data.residueCount === undefined || data.residueCount === null) {
			return '0';
		}
		// 将 residueCount 转换为数字
		const residueCountNum = Number(data.residueCount);
		// 如果是负数，返回绝对值
		if (residueCountNum < 0) {
			return Math.abs(residueCountNum).toString();
		}
		// 否则直接返回原值
		return data.residueCount;
	};

	const showModal = () => {
		// 是否超限
		const isExc:boolean = Number(data.residueCount || 0) < 0;

		return (
			<Modal
				centered
				title="平台商品数量"
				visible={ visible }
				onCancel={ () => setVisible(false) }
				footer={ [
					<Button key="cancel" onClick={ () => setVisible(false) }>
						取消
					</Button>,
					<Button key="submit" type="primary" onClick={ () => setVisible(false) }>
						确定
					</Button>
				] }
				width={ 640 }
				getContainer={ document.body }
				className={ styles.limitModal }
			>
				<div className={ styles.limitInfoBox }>
					<div className={ styles.infoRow }>
						<div className={ styles.infoItem }>
							<div className={ styles.infoLabel }>当前账号平台商品数量上限</div>
							<div className={ styles.infoValue }>{data.limitCount?.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}</div>
						</div>
						{
							isExc ? (
								<div className={ styles.infoItem }>
									<div className={ styles.infoLabelExc }>
										<Icon style={ { fontSize: 16, color: '#FAAD14', marginRight: '4px', } } type="sanjiaotishi" />
										已超出
									</div>
									<div className={ styles.infoValueExc }>{showResidueCount().replace(/\B(?=(\d{3})+(?!\d))/g, ',')}</div>
								</div>
							) : (
								<div className={ styles.infoItem }>
									<div className={ styles.infoLabel }>当前剩余</div>
									<div className={ styles.infoValue }>{showResidueCount().replace(/\B(?=(\d{3})+(?!\d))/g, ',')}</div>
								</div>
							)
						}
					</div>
				</div>
				<div className={ styles.tipBox }>
					<div className={ styles.tipItem }>
						<div className={ styles.tipIcon }>
							<Icon style={ { fontSize: 24 } } type="didanchaxun" />
						</div>
						<div className={ styles.tipContent }>
							<div className={ styles.tipTitle }>通过筛选查找并清理当前无用商品</div>
							<div className={ styles.tipDesc }>举例：如180天前创建，已下架或无销量的商品</div>
						</div>
					</div>
					<div className={ styles.tipItem }>
						<div className={ styles.tipIcon }>
							<Icon style={ { fontSize: 24 } } type="lianxikefu" />
						</div>
						<div className={ styles.tipContent }>
							<div className={ styles.tipTitle }>联系销售</div>
							<div className={ styles.tipDesc }>如需提高更多单量上限，请联系销售或直接购买</div>
						</div>
						{
							[1, 2, 3].includes(userInfo?.level)
								? <Button type="link" style={ { padding: 0, margin: '12px' } } onClick={ () => showPayDrawer({ type: PAY_TYPE.平台商品数量 }) }>前往订购</Button>
								: null
						}
					</div>
				</div>
			</Modal>
		);
	};

	const overData = () => {
		const residueCount = Number(data.residueCount || 0);
		if (residueCount >= 0) {
			return (
				<div className={ styles.item }>
					<span className={ styles.label }>剩余</span>
					<span className={ `${styles.value}` }>
						{(data.residueCount || '0').replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
					</span>
					<span className={ styles.viewButton } onClick={ handleViewClick }>
						查看
					</span>
				</div>
			);
		} else if (residueCount < 0) {
			return (
				<div className={ styles.item }>
					<span className={ `${styles.value}` }>
						已超限
					</span>
					<span className={ styles.viewButton } onClick={ handleViewClick }>
						查看
					</span>
				</div>
			);
		}
	};

	// 加载中或出错时不显示
	if (loading) {
		return null;
	}
	return (
		<div className={ `${styles.container} ${className}` } style={ style }>
			<Tooltip
				placement="top"
				title={ `当前账号平台商品上限${data.limitCount || 0}` }
			>
				<div className={ styles.content }>
					<div className={ styles.item }>
						<span className={ styles.label }>当前平台商品数</span>
						<span className={ styles.value }>{(data.currentCount || '0').replace(/\B(?=(\d{3})+(?!\d))/g, ',')}</span>
					</div>
					{overData()}
				</div>
			</Tooltip>
			{showModal()}
		</div>
	);
};

export default observer(LimitInfoBar);

