import { computed, configure, makeAutoObservable, runInAction } from "mobx";
import { MessageType } from "antd/lib/message";
import _, { cloneDeep, isBoolean } from "lodash";
import message from "@/components/message";
import { IChoiceItem, IListCheckStatus, IPackage } from "@/pages/Trade/interface";
import { PLAT_ALI, PLAT_C2M, PLAT_TB, PLAT_XHS, PRINT_MAP } from "@/constants";
import { MergeAction, manualMergeTradeHelper } from "@/pages/Trade/components/ManualMergeTrade/helper";
import { handlePackageList, isPackCheckDisable, limitedPackTips, noGoodsLinkWarning } from "@/pages/Trade/utils";
import { omitProps, Store } from "..";
import { TradeExReachResultMap } from "@/utils/trade/judgeExpressReach";
import { TradeQueryStockInfoApi } from "@/apis/trade";
import { templateType4PlatformTypeEnum } from "@/pages/Trade/constants";
import { TradeChoiceType } from "@/utils/enum/trade";
import { dealPlatAsHandPlat, transferOtherToHand } from "@/components-biz/ShopListSelect/shopListUtils";
import event from '@/libs/event';
import { TradeDictInsertDictApi, TradeDictQueryDictApi } from "@/apis/trade/search";

export enum 排序 {
	不排序 = 0,
	升序 = 1,
	降序 = 2
}
class TradeListStore {
	constructor(parentStore: omitProps) {
		makeAutoObservable(this);
		this.parentStore = parentStore;
		// 初始化时获取行高配置
	}

	get hideByPendingCount():number {
		let count = 0;
		this.list.filter(item => item.isHideByPending).forEach(pack => {
			count += pack.trades.length;
		});
		return count;
	}

	get hideByRefundCount():number {
		let count = 0;
		this.list.filter(item => item.isHideByRefund).forEach(pack => {
			count += pack.trades.length;
		});
		return count;
	}

	get hideByDropShipCount():number {
		let count = 0;
		this.list.filter(item => item.isHideByDropShip).forEach(pack => {
			count += pack.trades.length;
		});
		return count;
	}

	get hideByWaveCount():number {
		let count = 0;
		this.list.filter(item => item.isHideByWave).forEach(pack => {
			count += pack.trades.length;
		});
		return count;
	}

	get hideCount() {
		return this.hideByPendingCount + this.hideByRefundCount + this.hideByDropShipCount + this.hideByWaveCount;
	}

	get checkedCount() {
		let checkedList = this.list.filter(item => item.isChecked);
		let checkedListLen = checkedList.length;
		runInAction(() => {
			let status: IListCheckStatus = IListCheckStatus.半选;
			if (checkedListLen && checkedListLen === this.list.filter(item => !item.isHideByRefund && !item.isHideByDropShip && !item.isHideByPending && !isPackCheckDisable(item)).length) {
				status = IListCheckStatus.全选;
			} else if (checkedListLen === 0) {
				status = IListCheckStatus.不选;
			}
			this.listCheckStatus = status;
		});
		return checkedListLen;
	}

	parentStore: Partial<Store>;

	list: IPackage[] = [];

	tempTradeList: IPackage[] = [];

	setTempTradeList = (val: IPackage[]) => {
		this.tempTradeList = val;
	};

	tempHotGoodsList: IPackage[] = [];

	setTempHotGoodsList = (val: IPackage[]) => {
		this.tempHotGoodsList = val;
	}

	isRowHeightConfig: boolean = undefined;

	getIsRowHeightConfig = (): boolean => {
		if (this.isRowHeightConfig === undefined) {
			try {
				TradeDictQueryDictApi({ userDictEnum: 'TRADE_PRINT_ROW_HEIGHT_CONFIG' }).then(res => {
					const { isRowHeightConfig } = JSON.parse(res.value);
					this.isRowHeightConfig = isRowHeightConfig || false;
				}).catch(() => {
					this.isRowHeightConfig = false;
				});
			} catch (error) {
				this.isRowHeightConfig = false;
			}
		}
		return this.isRowHeightConfig;
	}

	setIsRowHeightConfig = (val: boolean) => {
		this.isRowHeightConfig = val;
		TradeDictInsertDictApi({
			userDictEnum: 'TRADE_PRINT_ROW_HEIGHT_CONFIG',
			value: JSON.stringify({ isRowHeightConfig: val })
		});
	}

	// 记录上一次列表点击的index
	oldIndex: number = -1;

	setList = (list: IPackage[] | any) => {
		console.log('lis3333t', list);

		if (typeof list !== 'function') {
			this.list = list;
			let needUpdateIndex = this.isUseVirtualList || false;
			if (needUpdateIndex) {
				this.updateListIndex();
			}
		}
	}

	getListByTogetherId = (togetherIds: string[] | string) => {
		let _list: IPackage[] = [];
		if (typeof togetherIds === 'string') {
			return [this.list.find(item => item.togetherId === togetherIds)];
		} else {
			togetherIds.forEach(id => {
				let item = this.list.find(item => item.togetherId === id);
				if (item) _list.push(item);
			});
			return _list;
		}
	}

	manualMergeTradeSplice = (newSplitTrades: IPackage[], packages: IPackage) => {
		const index = this.list.findIndex(p => p.togetherId === packages.togetherId);
		this.list[index] = newSplitTrades[0];
		this.list.splice(index + 1, 0, ...newSplitTrades.splice(1));
		this.setList([...this.list]);
	}

	manualMergeTradeSpliceBatch = (splitTradeObj: {oldTrade: IPackage, newTrade: IPackage}[]) => {
		console.log('splitTradeObj', splitTradeObj);
		splitTradeObj.forEach(item => {
			const index = this.list.findIndex(p => p.togetherId === item.oldTrade.togetherId);
			this.list[index] = item.newTrade[0];
			this.list.splice(index + 1, 0, ...item.newTrade.splice(1));
			console.log('this.list', this.list);
		});
		console.log('this.list', this.list);
		this.setList([...this.list]);
	}

	// 更新BIC订单码
	setBicOrderCodeToList = (orderList: {tid: string, orderCode:string, togetherId: string}[]) => {
		runInAction(() => {
			orderList?.forEach(item => {
				let index = this.list.findIndex(i => i.togetherId == item.togetherId);
				if (index !== -1) {
					this.list[index].orderCode = item.orderCode; // 后端不放在pack

					this.list[index].trades.forEach(trade => {
						if (trade.orders.some(order => order.isChecked)) {
							trade.orderCode = item.orderCode;
						}
					});
				}
			});
			this.setList([...this.list]);
		});
	}

	// 更新bic已送检
	setBicInspectedToList = (orderList: {tid: string, togetherId: string}[]) => {
		runInAction(() => {
			orderList?.forEach(item => {
				let index = this.list.findIndex(i => i.togetherId == item.togetherId);
				if (index !== -1) {
					this.list[index].serviceTagList = [...(this.list[index]?.serviceTagList || []), 'bicInspected']; // BIC已送检订单
				}
			});
			this.setList([...this.list]);
		});
	}

	// 拆单
	splitOrder = (newSplitTrades: IPackage[], tidList: string[], pack: IPackage) => {
		if (!newSplitTrades?.length) return;
		console.log('newSplitTrades', newSplitTrades, tidList, pack);
		// 先插入
		const index = this.list.findIndex(p => p.togetherId === (pack.togetherId || pack.tid));
		console.log('indexindexindexindex', index, newSplitTrades, tidList, pack.togetherId, pack.tid);

		this.list[index] = newSplitTrades[0];
		this.list.splice(index + 1, 0, ...newSplitTrades.splice(1) || []);
		// 后删除
		this.list = this.list.filter(p => !tidList.includes(p.tids[0]));
		console.log('this.list', cloneDeep(this.list));
		this.setList([...this.list]);
	}

	// 关闭订单
	closeSplitOrder = (newSplitTrades: IPackage[], tidList: string[], pack: IPackage) => {
		if (!newSplitTrades?.length) return;
		// 先插入
		const index = this.list.findIndex(p => p.togetherId === (pack.togetherId || pack.tid));
		console.log('indexindexindexindex', index, newSplitTrades, tidList, pack.togetherId, pack.tid);
		this.list[index] = newSplitTrades[0];
		this.list.splice(index + 1, 0, ...newSplitTrades.splice(1) || []);
		// // 后删除
		// this.list = this.list.filter(p => !tidList.includes(p.tids[0]));
		// console.log('this.list', cloneDeep(this.list));
		this.setList([...this.list]);
	}

	// 手工订单编辑
	editHandTrade = async(pack: IPackage) => {
		const index = this.list.findIndex(p => p.togetherId === pack.togetherId);
		let _list = await handlePackageList([pack], false, this.parentStore?.storeSearchParams, false);
		_list[0].index = index;
		this.list.splice(index, 1, ..._list);
		this.setList([...this.list]);
	}

	// 手工订单删除
	deleteTradeSplice = (togetherId: string, tid: string = "") => {
		const index = this.list.findIndex(p => p.togetherId === togetherId);
		if (tid) {
			this.list[index].trades = this.list[index].trades?.filter(trade => trade.tid !== tid);
		} else {
			this.list.splice(index, 1);
		}
		this.setList([...this.list]);
	}

	// 手工订单批量删除
	deleteBatchTradeSplice = (successList = []) => {
		const list = this.list.filter(i => !successList.includes(i.togetherId));
		this.setList([...list]);
	}

	listCheckStatus: IListCheckStatus = IListCheckStatus.不选;

	setListCheckStatus = (status: IListCheckStatus) => {
		this.listCheckStatus = status;
	}

	batchOperateLoading: MessageType = null;

	setBatchOperateLoading = (val: null | MessageType) => {
		this.batchOperateLoading = val;
	}

	handleChoiceChange = (choiceItem: IChoiceItem) => {
		let prev = this.list.filter(item => !item.isFilter && !item.isHideByPending && !item.isHideByRefund && !item.isHideByDropShip) || [];
		let hasCheckedList: IPackage[] = this.list.filter(item => item.isChecked);
		let reGetSidsList: IPackage[] = [];
		let { isMergePrint, selectedTempGroup, selectedTemp, setting } = this.parentStore;
		let userTemplateList = selectedTempGroup.userTemplateList;
		let kddType = selectedTemp?.KddType;
		// 平台类型
		let platform = templateType4PlatformTypeEnum[kddType] || '';
		// 异常订单标识 锁 noGoodsLink

		const comparePlatformType = (pack: IPackage) => {
			if (isMergePrint) {
				let orderPlatSetting = setting?.groupPrintSetJsonString?.orderMatchSetting || [];
				let orderTemp = userTemplateList && userTemplateList.find((item:any) => {
					if (dealPlatAsHandPlat(pack.platform, null, pack)) {
						// let itemPlatform = pack.platform;
						// if (itemPlatform === 'other') itemPlatform = 'hand';
						let orderSettingType = orderPlatSetting.find(o => o.platform == transferOtherToHand(pack.platform, pack));
						return item.expressType == orderSettingType?.bindControlType;
					}
					  // 小红书平台特殊处理，支持新版模板
					  if (pack.platform === PLAT_XHS) {
						return [13, 16].includes(Number(item.expressType));
					}
					return item.expressType == PRINT_MAP[pack.platform];
				});
				return !!orderTemp;
			} else {
				return platform === pack.platform || dealPlatAsHandPlat(pack.platform, null, pack) || (pack.platform === PLAT_ALI && platform === PLAT_TB) || [PLAT_C2M].includes(pack.platform);
			}
		};

		const noTradeTipFn = (checkCount: number) => {
			if (checkCount === 0) {
				message.info('当前没有符合勾选的订单');
			}
		};

		if (prev.length) {
			let checkCount = 0;
			switch (choiceItem.type) {
				case TradeChoiceType.半选:
					// 半选 过滤挂起订单
					prev.forEach((item) => {
						if (comparePlatformType(item) && !item.isPending && !isPackCheckDisable(item)) {
							item.isChecked = true;
							checkCount++;
						}
					});
					break;
				case TradeChoiceType.勾选有货的:
					// 半选 过滤挂起订单
					prev.forEach((item) => {
						if ((!platform || comparePlatformType(item)) && !item.isPending && !isPackCheckDisable(item)) {
							const orderCheck = (order) => {
								if (
									['TRADE_CLOSED', 'TRADE_CLOSED_BY_TAOBAO'].includes(order.status)
									|| ['REFUND_SUCCESSED', 'REFUND_ING'].includes(order.refundStatus)
									|| (order.status === 'WAIT_BUYER_CONFIRM_GOODS' && !order.firstSend)
									|| order.status === 'TRADE_FINISHED'
									|| order.dropShipping
									|| order.noGoodsLink
									|| (order.noGoods && !order?.ignore)
								) {
									return false;
								}
								return true;
							};
							let isCheckItem = item.trades.some(trade => trade.orders.some(orderCheck));
							if (isCheckItem) {
								item.trades.forEach((oItem) => {
									oItem.orders.forEach(order => {
										order.isChecked = orderCheck(order);
									});
								});
								this.parentStore?.tradeOptStore?.handleProductOrders(item);
								this.parentStore?.tradeOptStore?.handlePackInfoPrintContent(item);
								checkCount++;
							}
							item.isChecked = isCheckItem;
						}
					});
					noTradeTipFn(checkCount);
					break;
				case TradeChoiceType.勾选可达订单:
					// 仅选中可达订单
					prev.forEach((item) => {
						if (comparePlatformType(item) && !item.isPending && item._canReach === 1 && !isPackCheckDisable(item)) {
							item.isChecked = true;
							checkCount++;
						} else {
							item.isChecked = false;
						}
					});
					noTradeTipFn(checkCount);
					break;

				case TradeChoiceType.全选:
					// 全选
					prev.forEach((item) => {
						if (isPackCheckDisable(item)) {
							item.isChecked = false;
						} else {
							item.isChecked = true;
							checkCount++;
						}
					});
					noTradeTipFn(checkCount);
					break;

				case TradeChoiceType.反选:
					// 反选
					prev.forEach((item) => {
						if (item.isPending || isPackCheckDisable(item)) {
							item.isChecked = false;
						} else {
							item.isChecked = !item.isChecked;
						}
						if (item.isChecked) checkCount++;
					});
					break;

				case TradeChoiceType.不选:
					// 不选
					prev.forEach((item) => {
						item.isChecked = false;
					});
					break;
				case TradeChoiceType.勾选未打印快递单:
					// 未打印快递单
					prev.forEach((item) => {
						if (comparePlatformType(item) && !item.isPending && item.waybillPrintStatus !== 'already' && !isPackCheckDisable(item)) {
							item.isChecked = true;
							checkCount++;
						} else {
							item.isChecked = false;
						}
					});
					noTradeTipFn(checkCount);
					break;
				case TradeChoiceType.勾选未打印发货单:
					// 未打印发货单
					prev.forEach((item) => {
						if (!item.isPending && item.shipListPrintStatus !== 'already' && !isPackCheckDisable(item)) {
							item.isChecked = true;
							checkCount++;
						} else {
							item.isChecked = false;
						}
					});
					noTradeTipFn(checkCount);
					break;
				case TradeChoiceType.取消勾选指定订单:
					// 取消被勾选订单选中状态
					// 如 不匹配模版的订单取消勾选
					prev.forEach((item) => {
						const { togetherId } = item;
						if (choiceItem.cancelChoiceOrders.includes(togetherId)) {
							item.isChecked = false;
						}
						if (item.isChecked) checkCount++;
					});
					break;
				case TradeChoiceType.勾选指定订单:
					// 选中订单
					prev.forEach((item) => {
						const { togetherId } = item;
						if (choiceItem.cancelChoiceOrders.includes(togetherId)) {
							item.isChecked = true;
						}
						if (item.isChecked) checkCount++;
					});
					break;
				case TradeChoiceType.勾选子订单:
					prev.forEach((item) => {
						const { togetherId } = item;
						/**
						 * Q: 为什么需要checkedOrderList？
						 * A: 如果子订单是空的，trade是不需要勾选的
						 */
						const checkedOrderList = [];
						if (choiceItem.cancelChoiceOrders.includes(togetherId)) {
							item.trades.forEach((oItem) => {
								oItem.orders.forEach(order => {
									if (choiceItem.oidList?.includes(order.oid)) {
										order.isChecked = true;
									}
									if (order.isChecked) {
										checkedOrderList.push(order);
									}
								});
							});
							item.isChecked = checkedOrderList.length > 0;
							if (item.isChecked) {
								reGetSidsList.push(item);
							}
						}
						this.parentStore?.tradeOptStore?.handleProductOrders(item);
						if (item.isChecked) checkCount++;
					});
					break;
				case TradeChoiceType.取消勾选子订单:
					prev.forEach((item) => {
						const { togetherId } = item;
						/**
						 * Q: 为什么需要checkedOrderList？
						 * A: 取消勾选子订单并不意味着全部取消，可能是部分取消，如果是部分取消，那主订单还是需要勾选的
						 */
						const checkedOrderList = [];
						if (choiceItem.cancelChoiceOrders.includes(togetherId)) {
							item.trades.forEach((oItem) => {
								oItem.orders.forEach(order => {
									if (choiceItem?.oidList?.includes(order.oid)) {
										order.isChecked = false;
									}
									if (order.isChecked) {
										checkedOrderList.push(order);
									}
								});
							});
							item.isChecked = checkedOrderList.length > 0;
							if (item.isChecked) {
								reGetSidsList.push(item);
							}
						}
						this.parentStore?.tradeOptStore?.handleProductOrders(item);
						if (item.isChecked) checkCount++;
					});
					break;
				case TradeChoiceType.小标签生成_取消勾选指定订单:
					// 取消被勾选订单选中状态 传入tid 根据tid查到 togetherId 再将所有合单取消勾选
					{
						let _cancelChoiceOrders = [...choiceItem.cancelChoiceOrders];
						prev.forEach((item) => {
							const { togetherId } = item;
							_cancelChoiceOrders.forEach((cancelItem, index) => {
								if (togetherId.includes(cancelItem)) {
									item.isChecked = false;
									_cancelChoiceOrders.splice(index, 1);
								}
							});
							if (item.isChecked) checkCount++;
						});
					}
					break;
				// case TradeChoiceType.勾选非疫情订单:
				// 	// 仅选中非疫情地区订单
				// 	prev.forEach((item) => {
				// 		if (!item.noGoodsLink && !item.isPending && item.epidemicMark === 0 && !item.isLimited) {
				// 			item.isChecked = true;
				// 			checkCount++;
				// 		} else {
				// 			item.isChecked = false;
				// 		}
				// 	});
				// 	noTradeTipFn(checkCount);
				// 	break;
				case TradeChoiceType.更新小标签标识:
					// 更改是否生成标签标识
					{
						let successTidOids = { ...choiceItem.successTidOids };
						prev.forEach((item) => {

							const { togetherId } = item;
							Object.keys(successTidOids).forEach((cancelId, index) => {
								if (togetherId.includes(cancelId)) {
									item.trades.forEach(tradeItem => {
										if (tradeItem.tid === cancelId) {
											item.labelstatus = 1;
											tradeItem.orders.forEach((oItem) => {
												if (successTidOids[cancelId].includes(oItem.oid)) oItem.labelstatus = 1;
											});
											delete successTidOids[cancelId];
										}
									});
								}
							});

						});
					}
					break;
				case TradeChoiceType.更新先发货标识:
					// 更新先发货标识
					{
						prev.forEach((item) => {
							if (item.isChecked) {
								item.firstSend = false;
								item.trades.forEach(tradeItem => {
									// if (tradeItem.isChecked) {
									tradeItem.firstSend = false;
									tradeItem.orders.forEach((oItem) => {
										if (oItem.isChecked) oItem.firstSend = false;
									});
									// }
								});
							}
						});
					}
					break;
				case TradeChoiceType.更新商品识别码:
					{
						prev.forEach((item) => {
							item.trades.forEach(tradeItem => {
								tradeItem.orders.forEach((oItem) => {
									const val = choiceItem?.productIdCodeMap?.[`${tradeItem.tid}_${oItem.oid}`];
									if (val) oItem.productIdCode = val == -1 ? "" : val;
									// oItem.productIdCode = val == -1 || "";
								});
							});
						});
					}
					break;
				case TradeChoiceType.勾选前N个:
					// 勾选前N个包裹
					{
						const selectCount = choiceItem.selectCount || 0;
						let selectedCount = 0;
						// 先取消所有选择
						prev.forEach((item) => {
							item.isChecked = false;
						});
						// 从前往后勾选，跳过挂起/隐藏的包裹
						for (let i = 0; i < prev.length && selectedCount < selectCount; i++) {
							const item = prev[i];
							if (!item.isPending && !isPackCheckDisable(item)) {
								item.isChecked = true;
								selectedCount++;
								checkCount++;
							}
						}
					}
					break;
				default:
					break;
			}

			if (checkCount === 0 && choiceItem.type === TradeChoiceType.半选) {
				let isChooseTemplate = isMergePrint ? !!userTemplateList : !!selectedTemp.KddType;
				if (isChooseTemplate) {
					message.info('没有符合勾选的订单，将自动全选');
				}
				prev.forEach((item) => {
					if (isPackCheckDisable(item)) {
						item.isChecked = false;
					} else {
						item.isChecked = true;
						checkCount++;
					}
				});
			}

			setTimeout(() => {
				// let status: IListCheckStatus = IListCheckStatus.半选;
				// if (checkCount && checkCount === prev.filter(item => !item.isLimited && !item.pOrderUnfinished).length) {
				// 	status = IListCheckStatus.全选;
				// } else if (checkCount === 0) {
				// 	status = IListCheckStatus.不选;
				// }
				// console.log('status', status);
				// this.setListCheckStatus(status);
				// 单号回显
				if (!choiceItem.noUpdateSids) {
					let curCheckedList = prev.filter((i) => i.isChecked);
					curCheckedList = curCheckedList.filter(i => {
						let index = hasCheckedList.findIndex(item => item.togetherId === i.togetherId);
						return index === -1;
					});
					reGetSidsList.forEach(i => {
						let index = curCheckedList.findIndex(item => item.togetherId === i.togetherId);
						if (index === -1) {
							curCheckedList.push(i);
						}
					});
					console.log(curCheckedList, reGetSidsList);
					// console.log(prev.length, curCheckedList.length);
					this.parentStore?.tradePrintStore?.waybillEcho(curCheckedList);
				}
			}, 0);

		}
	}

	isShiftDown:Boolean = false;

	keyDown = () => {
		if (document.onkeydown && document.onkeyup) return;
		document.onkeydown = null;
		document.onkeyup = null;
		// 键盘按下事件
		document.onkeydown = (e:{keyCode:number}) => {
			let e1:any = e || window.event;
			// 键盘按键判断:左箭头-37;上箭头-38；右箭头-39;下箭头-40  回车：13   ctrl：17   shift：16
			if (e1.keyCode === 16) this.isShiftDown = true;
		};
		// 键盘抬起事件
		document.onkeyup = (e) => {
			let e1:any = e || window.event;
			if (e1.keyCode === 16) this.isShiftDown = false;
		};

		window.addEventListener('blur', () => {
			this.isShiftDown = false;
		});
	};

	cancelKeyDown = () => {
		console.log('取消监听');
		// document.onkeydown = null;
		// // 键盘抬起事件
		// document.onkeyup = null;
		// this.isShiftDown = false;
		this.oldIndex = -1;
	}

	dealShift = (params: {pack: IPackage, index: number}) => {
		const { pack, index } = params;
		// 按下shift 时选择
		if (this.isShiftDown) {
			let start = -1;
			let end = -1;
			// 找到开始和结束位置
			if (index > this.oldIndex) {
				start = this.oldIndex;
				end = index;
			} else {
				start = index;
				end = this.oldIndex;
			}
			// 判断是选中还是取消选中
			const renderList = manualMergeTradeHelper.tradeFactory(this.list).filter(item => !item.isFilter && !item.isHideByPending && !item.isHideByRefund && !item.isHideByDropShip);
			const startReal = Math.min(start, this.list.findIndex(item => item.togetherId === renderList[start].togetherId));
			const endReal = Math.min(end, this.list.findIndex(item => item.togetherId === renderList[end].togetherId));
			const checked = this.list.find(item => item.togetherId === renderList[this.oldIndex].togetherId).isChecked;
			let startIndex = startReal;
			// 将shift选中的部分批量选中或取消选中
			for (let i = startReal; i <= endReal; i++) {
				while (this.list[startIndex].togetherId !== renderList[i].togetherId) {
					startIndex++;
				}
				this.list[startIndex].isChecked = checked;
				if (this.list[startIndex].isChecked) {
					this.parentStore?.tradePrintStore?.waybillEcho([this.list[startIndex]]);
				}
			}
		} else {
			this.oldIndex = index;
			this.togglePackageSelectStatus(pack);
		}
	}

	togglePackageSelectStatus = (pack: IPackage) => {
		if (isPackCheckDisable(pack)) {
			limitedPackTips();
		} else {
			pack.isChecked = !pack.isChecked;

			if (pack.isChecked) {
				this.parentStore?.tradePrintStore?.waybillEcho([pack]);
			}
		}
	}

	toggleTogetherDetail = (pack: IPackage) => {
		pack.isExpand = !pack.isExpand;
		pack.trades.forEach(trade => {
			if (trade.hidePartShipOrder === false) {
				trade.hidePartShipOrder = true;
			}

			if (trade.isHideFinishedGoods === false) {
				trade.isHideFinishedGoods = true;
			}

			if (trade.isHideClosedGoods === false) {
				trade.isHideClosedGoods = true;
			}

			if (trade.isHideRefundingGoods === false) {
				trade.isHideRefundingGoods = true;
			}

			if (trade.isHideDropShippingGoods === false) {
				trade.isHideDropShippingGoods = true;
			}
		});
	}

	addHandOrder = async(pack: IPackage) => {
		// let _list = await handlePackageList([pack]);
		let _list = await handlePackageList([pack], false, this.parentStore?.storeSearchParams, false);
		this.setList([_list[0], ...this.list]);
	}

	calcListCheckStatus = () => {
		let checkCount = 0;
		let listLen = this.list.length;
		checkCount = (this.list as IPackage[]).filter((item) => {
			return item.isChecked;
		}).length;
		let status: IListCheckStatus = IListCheckStatus.半选;
		if (checkCount === listLen) {
			status = IListCheckStatus.全选;
		} else if (checkCount === 0) {
			status = IListCheckStatus.不选;
		}
		this.setListCheckStatus(status);
	}

	handleAllExpand = (expanded: boolean) => {
		this.setBatchOperateLoading(message.loading(`详情${expanded ? '展开' : '收起'}中`));
		setTimeout(() => {
			runInAction(() => {
				this.list.forEach(item => {
					item.isExpand = expanded;
					if (!expanded && item.hidePartShipOrder === false) {
						item.hidePartShipOrder = true;
					}
				});
				this.setList([...this.list]);
			});
		});
	}

	handleUpdateList = (params: any) => {
		let prev = this.list;
		if (prev) {
			const { type, data, needCheck, } = params;
			let checkCount = 0;
			let listLen = prev.length;
			let i;
			console.log('handleUpdateList', params);

			switch (type) {
				case 'updateList':
					if (data.needExpand) {
						this.setBatchOperateLoading(message.loading(`详情${data.expandValue ? '展开' : '收起'}中`));
						setTimeout(() => {
							this.setList(prev.map((item) => ({ ...item, isExpand: data.expandValue })));
						}, 50);
					} else if (data.list) {
						checkCount = (data.list as IPackage[]).filter((item) => {
							return item.isChecked;
						}).length;
						if (data.needCalcCheckStatus) {
							let status: IListCheckStatus = IListCheckStatus.半选;
							if (checkCount === listLen) {
								status = IListCheckStatus.全选;
							} else if (checkCount === 0) {
								status = IListCheckStatus.不选;
							}
							this.setListCheckStatus(status);
						}
						this.setList(data.list);
					}
					break;
				case 'updatePackInfo':
					i = prev.findIndex((item) => item.togetherId === data.togetherId);
					if (i > -1) {
						prev[i] = data.packInfo;
						if (prev[i].isChecked) {
							this.parentStore?.tradePrintStore?.waybillEcho([prev[i]]);
						}
					}
					checkCount = prev.filter((item) => item.isChecked).length;
					if (data.needCalcCheckStatus) {
						let status: IListCheckStatus = IListCheckStatus.半选;
						if (checkCount === listLen) {
							status = IListCheckStatus.全选;
						} else if (checkCount === 0) {
							status = IListCheckStatus.不选;
						}
						this.setListCheckStatus(status);
					}

					this.setList([...prev]);
					break;
				// 只更新数据不进行其他操作
				case 'updatePackInfoPure':
					i = prev.findIndex((item) => item.togetherId === data.togetherId);
					if (i > -1) {
						prev[i] = data.packInfo;
					}
					this.setList([...prev]);
					break;

				case MergeAction.合并:
					this.setList(data.list);
					break;
				/**
				 * 订单 处理异常 数据更新
				 * data 数组里面的tids需要整笔合单的tid
				 */
				case 'updateTradesByDealAbnormal':
				case 'batchUpdateTrade':
					handlePackageList(data, false, this.parentStore?.storeSearchParams).then(_data => {
						let isError = false;
						_data.forEach(pack => {
							let index = prev.findIndex(item => item.togetherId === pack.togetherId);
							if (index !== -1) {
								prev[index] = {
									...prev[index],
									...pack,
									packSmartExpressTemplate: pack.packSmartExpressTemplate,
									smartExpressTemplate: pack.smartExpressTemplate,
									isChecked: needCheck // handlePackageList方法中把isChecked改成了false。对于需要勾选的订单来说，参数传递needCheck就行
								};
							} else {
								isError = true; // 找不到这笔合单，可能被自动拆单了
							}
						});
						this.setList([...prev]);

						// 重新查询下
						if (isError) {
							event.emit('tradeSetting.handleReSearch');
						}
					});
					break;
				// 更新列表勾选状态-取消错误勾选
				case 'updateTradeSelectStatus':
					data.forEach(o => {
						let index = prev.findIndex(it => it.togetherId == o);
						if (index !== -1) {
							prev[index] = {
								...prev[index],
								isChecked: false
							};
						}
					});
					this.setList([...prev]);
					break;
				default:
					break;
			}
		}
	}

	updateExReachCb = (res: TradeExReachResultMap) => {
		this.list.forEach(item => {
			let result = res[item.md5];
			if (result && result.reachItem) {
				item._canReach = result.canReach;
				item._reachItem = result.reachItem;
			} else if (result && !result.reachItem) {
				item._canReach = null;
				item._reachItem = null;
			}
		});
	}

	// * 可达进度
	exReachProgessObj = {
		totalCount: 0,
		completeCount: 0,
		showProgressModal: false,
	}

	updateExReachProgress = (val: Partial<typeof this.exReachProgessObj> = {
		totalCount: 0,
		completeCount: 0,
		showProgressModal: false
	}) => {
		this.exReachProgessObj = {
			...this.exReachProgessObj,
			...val,
		};
	}

	// * 打印方法队列
	printActQueue: Array<Function> = [];

	updatePrintActQueue = (queue: typeof this.printActQueue) => {
		this.printActQueue = queue;
	}


	// * 是否展示过滤词
	isShowKeywordSetting: boolean = false;

	setIsShowKeywordSetting = (val: boolean) => {
		this.isShowKeywordSetting = val;
	}

	// * 是否展示打印内容
	isShowPrintStyleSetting: boolean = false;

	setIsShowPrintStyleSetting = (val: boolean) => {
		this.isShowPrintStyleSetting = val;
	}

	// * 是否使用虚拟列表
	isUseVirtualList: boolean = false;

	setIsUseVirtualList = (val: boolean) => {
		this.isUseVirtualList = val;
	}

	updateListIndex = () => {
		console.log('updateListIndex');
		let _index = 0;
		this.list.forEach((item) => {
			if (
				!item.isHideByPending
				&& !item.isHideByRefund
				&& !item.isFilter
				&& !item.isHideByDropShip
				&& !item.isHideByWave
			) {
				item.index = _index++;
			}
		});
	}

	itemStockObj:object = {}

	getItemStockNum = () => {
		try {
			let reqObj = {};
			this.list.forEach(pack => {
				pack.trades.forEach(trade => {
					trade.orders.forEach(order => {
						if (order.systemNumIid && order.systemSkuId) {
							let _id = `${order.systemNumIid}_${order.systemSkuId}`;
							reqObj[_id] = { sysItemId: order.systemNumIid, sysSkuId: order.systemSkuId };
						}
					});
				});
			});
			let reqArr = [];
			for (let item in reqObj) {
				reqArr.push(reqObj[item]);
			}
			TradeQueryStockInfoApi(reqArr).then((res) => {
				let _itemStockObj = {};
				res.forEach((item) => {
					_itemStockObj[`${item.sysItemId}_${item.sysSkuId}`] = item.salableItemStock;
				});
				this.itemStockObj = _itemStockObj;
			}).catch((err) => {
				console.log("TradeQueryStockInfoApi err:", err);
			});
		} catch (error) {
			console.log('error:', error);
		}
	}

	// * 异常订单检测打印阶段临时忽略的key
	tempAbnormalIgnoreKey: string[] = [];

	setTempAbnormalIgnoreKey = (val: string[]) => {
		this.tempAbnormalIgnoreKey = val;
	}

	// * 编辑简称/别名 10s限制
	lastEditGoodsTime: number = 0;

	setLastEditGoodsTime = (val: number) => {
		this.lastEditGoodsTime = val;
	}

	// 重新发货
	isBatchResend: boolean = false;

	setIsBatchResend = ((val: boolean) => {
		this.isBatchResend = val;
	})

	// 追加包裹
	isBatchAppendSend: boolean = false;

	setIsBatchAppendSend = ((val: boolean) => {
		this.isBatchAppendSend = val;
	})

	// 打印BIC订单码
	isBatchPrintBIC: boolean = false;

	setIsBatchPrintBIC = ((val: boolean) => {
		this.isBatchPrintBIC = val;
	})

	// BIC订单发货
	isBatchSendBIC: boolean = false;

	setIsBatchSendBIC = ((val: boolean) => {
		this.isBatchSendBIC = val;
	})

	// 批量生成波次
	isBatchCreateWave: boolean = false;

	setIsBatchCreateWave = ((val: boolean) => {
		this.isBatchCreateWave = val;
	})

	// 列表排序 排序
	changListSort = ({ key, sortType }) => {
		if (sortType == 排序.升序) {
			this.list.sort((a, b) => {
				let nameA = a[key];
				let nameB = b[key];
				if (nameA < nameB) {
					return 1;
				}
				if (nameA > nameB) {
					return -1;
				}
				return 0;
			});
		} else if (sortType == 排序.降序) {
			this.list.sort((a, b) => {
				let nameA = a[key];
				let nameB = b[key];
				if (nameA < nameB) {
					return -1;
				}
				if (nameA > nameB) {
					return 1;
				}
				return 0;
			});
		}
		this.setList([...this.list]);
		// console.log('this.list:::', this.list.map((item) => (item[key])));
	}

	diffStyleNumMap = {
		singleItemNum: 0,
		singleStyleMultiNum: 0,
		multiStyleMultiNum: 0
	}

	setDiffStyleNumMap = (data) => {
		this.diffStyleNumMap = data;
	}

}

export default TradeListStore;
