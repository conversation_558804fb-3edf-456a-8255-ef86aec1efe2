const fs = require('fs');
const path = require('path');

function htmlTemplatePlugin(options = {}) {
	const { env, version, buildByOss } = options;

	return {
		name: 'html-template-plugin',

		transformIndexHtml: {
			enforce: 'pre',
			transform(html, context) {
				if (!buildByOss) return html;

				// 计算构建相关的变量
				const buildPrefix = `/${env.APP_BUILD_ENV}/${version}`;
				const basePath = buildByOss
					? `${env.APP_BUILD_PATH || ''}${buildPrefix}/`
					: '/';

				// 注入模板变量到 HTML 中
				const templateVars = `
<script>
	// 构建时注入的模板变量
	window.__TEMPLATE_VARS__ = {
		version: '${version}',
		buildEnv: '${env.APP_BUILD_ENV}',
		basePath: '${basePath}',
		buildPrefix: '${buildPrefix}'
	};
</script>`;

				// 在 head 结束标签前注入变量
				html = html.replace('</head>', `${templateVars}\n</head>`);

				return html;
			}
		},

		// OSS 模式下，构建完成后将 HTML 文件复制到 dist 根目录
		writeBundle(options, bundle) {
			if (!buildByOss) return;

			const outputDir = options.dir;
			const htmlFileName = buildByOss ? 'index-oss.html' : 'index.html';
			const sourcePath = path.join(outputDir, htmlFileName);
			const targetPath = path.join(outputDir, '../../index.html');

			// 确保源文件存在
			if (fs.existsSync(sourcePath)) {
				// 确保目标目录存在
				const targetDir = path.dirname(targetPath);
				if (!fs.existsSync(targetDir)) {
					fs.mkdirSync(targetDir, { recursive: true });
				}

				// 复制文件
				fs.copyFileSync(sourcePath, targetPath);
				console.log(`✅ HTML file copied: ${sourcePath} -> ${targetPath}`);

				// 删除源文件（可选，保持目录整洁）
				fs.unlinkSync(sourcePath);
			}
		}
	};
}

module.exports = htmlTemplatePlugin;
