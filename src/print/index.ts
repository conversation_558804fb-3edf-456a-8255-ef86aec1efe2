import { salePropertyEnum } from '@/pages/Trade/ScanPrint/constants';
import _, { isEmpty } from "lodash";
import message from "@/components/message";
import { tradeStore, foreignManageStore } from '@/stores';
import waveStore from "@/stores/warehouse/Wave";
import tradeSetStore from "@/stores/trade/tradeSet";
import scanPrintStore from "@/stores/trade/scanPrint";
import { formatTradeData } from '@/utils/print/formatData';
import { saveReprintLog } from "@/apis/report/kddLog";
import userStore from '@/stores/user';
import { handleHlOrder, sendDelivery } from '@/pages/Trade/components/BottomCom/utils';
import event from '@/libs/event';
import { ItemTakeGoodsLabelUpdateOrderPrintStatusApi } from '@/apis/trade/takeGoodsLabel';
import { ItemTakeGoodsLabelUpdateOrderPrintStatusRequest } from '@/types/trade/takeGoodsLabel';
import { PrintTypeEnum, TradeChoiceType, TradeStatus } from "@/utils/enum/trade";
import { IKddTemp } from "@/types/schemas/report";
import { PRINT_BATCH_ABNORMAL_ORDER_SET_IGNORE_KEY, TEMP_MAP, TEMP_NAME } from "@/constants";
import voiceSuccess from '@/assets/mp3/成功.mp3';
import voiceDealFail from '@/assets/mp3/处理失败.mp3';
import { focusLabelInput, playAudio } from "@/pages/Trade/ScanPrint/utils";
import { pageLoading } from "@/components/PageLoading";
import { isSourceScm } from "@/components-biz/ShopListSelect/shopListUtils";
import { weightUnit } from "@/pages/Index/Settings/System/constants";
import sendPoint from '@/utils/pointTrack/sendPoint';
import Pointer from '@/utils/pointTrack/constants';

// 获取发货内容打印样式，一期不做，但是需要先写死配置
async function _getPrintStyle() {
	let printContentSet = await tradeSetStore.getPrintContentSet();
	return printContentSet;
}

const customLogPost = (type: string, dataType: string, data: any = {}) => {
	let title = '';

	switch (type) {
		case 'scanPrintKdd':
			title = '扫描打印';
			break;
		case 'kdd':
			title = '批量打印快递单';
			break;
		case 'hotGoodsScanPrint':
			title = '爆款标签打印';
			break;
		case 'ydh':
			title = '运单号';
			break;
		case 'printBhdXbq':
			title = '打印备货单小标签';
			break;
		case 'thdxbq':
			title = '退货单小标签';
			break;
		case 'wave':
			title = '波次打印';
			break;
		default:
			break;
	}

	if (title) {
		window.errorCollection?.customMessageUpload({
			type: `${title}【${dataType}】`,
			data
		});
	} else {
		window.errorCollection?.customMessageUpload({
			type: `${dataType}`,
			data
		});
	}
};
const closeLoading = () => pageLoading.destroy();

export default {
	/**
	 * @desc 判断能否打印当前模板
	 * @param plat 查询订单时的平台
	 * @param curKddType 所选模板的kddType
	 */
	judgeValidKddType(plat, curKddType) {
		const map = {
			PDD: [7],
		};
		return (map[plat] || []).includes(curKddType);
	},
	/**
	 * @desc 批量打印快递单、爆款标签打印、运单号
	 */
	async batchPrintKdd({
		fjrInfoMap,
		fhdFjrInfoMap,
		orderList,
		temp,
		printOp = 1,
		orderPlat,
		printType,
		firstSendMode,
		tradeType = "",
		choosedPrinter,
		isMerge,
		scanFjrInfo
	}: {
		orderList: any,
		temp?: any,
		printOp?: number,
		orderPlat?: string,
		fjrInfoMap?: any,
		fhdFjrInfoMap?: any,
		printType: string,
		firstSendMode?: boolean // 是否为先发货
		tradeType?: "crossBorder" | "" // 订单类型 crossBorder 为跨境订单
		choosedPrinter?: string, // 爆款标签： 打印机
		isMerge?: boolean, // 爆款标签：融合打印
		scanFjrInfo?: any,
	}) {
		// TODO 后续使用全局通用逻辑
		// 不能使用mobx中的响应数据，会出现用户还没保存时，数据已更新，只获取用户保存后的数据
		// let setting: any = JSON.parse(sessionStorage.getItem('common.advanced.set'));
		const { setting } = tradeStore;

		const printAPI = window.printAPI;

		const printStyle = await _getPrintStyle();
		await handleHlOrder(orderList);

		let formatTradeResult: any = formatTradeData({
			isKg: userStore.userSetting?.weightUnit == weightUnit.显示kg,
			packages: orderList,
			isGetFhdInfo: false,
			firstSendMode,
			showCombinationInfo: setting.showCombinationInfo == 2
		});

		let customLogTitle = printType;
		if (printType == 'scanPrintKdd') {
			customLogTitle = 'hotGoodsScanPrint';
		}
		if (formatTradeResult.isError) {
			console.log('formatTradeResult Error');
			customLogPost(customLogTitle, `batchPrintKdd: formatTradeData error 发现重复tid`, {
				orderList,
				printType,
				firstSendMode,
				tradeType,
				choosedPrinter,
				isMerge
			});
			return;
		}
		const batchPrintParams = {
			templateInfo: temp || {},
			orderList: formatTradeResult.orderList,
			fjrInfoMap,
			fhdFjrInfoMap,
			printOp,
			printType,
			printStyle,
			canUseNewWaybillCodeSet: true, // 能使用新单号打印
			canChooseWaybillCodeNumSet: setting.displayGenYdNum == 2, // 可以选择多单号
			displayPrintNumSet: setting.displayPrintNum == 2, // 显示打印机打印份数
			// choosePrinterNotiSet: true, // 展示选择打印机弹出框
			sendAfterPrint: true,
			isAutoAddPrintNum: setting.addYdNoMemo == 2,
			isNotAddPrintNumOnlyOne: setting.addYdNoOverOne == 2,
			firstSendMode: !!firstSendMode,
			tradeType,
			showCombinationInfo: setting.showCombinationInfo == 2, // 打印子货品
			choosedPrinter,
			scanFjrInfo,
			isMerge,
		};

		console.log('%c [ 打印快递单 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', batchPrintParams, printType);
		// 批打流程
		const batchPrintKddFn = batchPrintParams => printAPI.batchPrintKdd(batchPrintParams, {
			// 返回控件是否连接正常
			afterCheckComponentHook: ({ type, isSuccess, downUrl }) => {
				console.log('======afterCheckComponentHook', isSuccess);
				if (!isSuccess) {
					closeLoading();
				}
			},
			// 选择模板后出发
			afterChooseTempHook: (tempId, templateInfo) => {
				closeLoading();
				// tradeType = 'crossBorder' 说明是跨境页面过来的打印
				if (tradeType && tradeType === 'crossBorder') {
					let kddTempList = foreignManageStore.getKddTempList();
					let temp = kddTempList.find(o => o.Exid === templateInfo.Exid);
					let order = null;
					foreignManageStore.setSelectedTemp(temp);
					if (orderList.length === 1) {
						order = orderList[0];
					}
					if (userStore.userSetting?.extraSetting?.abnormalExpressArriveCheckNew?.printBeforeCheck) {
						foreignManageStore.tradeListStore.updateExReachProgress({
							showProgressModal: true,
						});

						// * 需要等可达回显完再触发打印
						foreignManageStore.tradeListStore.updatePrintActQueue([() => {
							event.emit("printBatch.printAct", {
								printType: firstSendMode ? PrintTypeEnum.运单号 : PrintTypeEnum.快递单,
								templateInfo: temp,
								singleOrder: order,
							});
						}]);
					} else {
						event.emit("printBatch.printAct", {
							printType: firstSendMode ? PrintTypeEnum.运单号 : PrintTypeEnum.快递单,
							templateInfo: temp,
							singleOrder: order,
						});
					}
				} else {
					let kddTempList = tradeStore.getKddTempList();
					let temp = kddTempList.find(o => o.Exid === templateInfo.Exid);
					let order = null;
					tradeStore.setSelectedTemp(temp);
					if (orderList.length === 1) {
						order = orderList[0];
					}
					if (userStore.userSetting?.extraSetting?.abnormalExpressArriveCheckNew?.printBeforeCheck && userStore.userSetting?.openAbnormalCheck === 0) {
						tradeStore.tradeListStore.updateExReachProgress({
							showProgressModal: true,
						});

						// * 需要等可达回显完再触发打印
						tradeStore.tradeListStore.updatePrintActQueue([() => {
							event.emit("printBatch.printAct", {
								printType: firstSendMode ? PrintTypeEnum.运单号 : PrintTypeEnum.快递单,
								singleOrder: order,
								templateInfo: temp,
								isCheckAbnormal: true,
							});
						}]);
					} else {
						event.emit("printBatch.printAct", {
							printType: firstSendMode ? PrintTypeEnum.运单号 : PrintTypeEnum.快递单,
							singleOrder: order,
							templateInfo: temp,
							isCheckAbnormal: true,
						});
					}
				}
				const isBreak = true;
				// // isBreak如果是true，就不会进行后续流程
				return {
					isBreak,
				};

			},
			afterChooseTempGroupHook: (tempId) => {
				closeLoading();
				if (tradeType && tradeType === 'crossBorder') {
					let kddTempList = foreignManageStore.getKddTempList();
					let temp = kddTempList.find(o => o.id === tempId);
					foreignManageStore.setSelectedTempGroup(temp);

					if (userStore.userSetting?.extraSetting?.abnormalExpressArriveCheckNew?.printBeforeCheck && userStore.userSetting?.openAbnormalCheck === 0) {
						foreignManageStore.tradeListStore.updateExReachProgress({
							showProgressModal: true,
						});
						// * 需要等可达回显完再触发打印
						foreignManageStore.tradeListStore.updatePrintActQueue([() => {
							event.emit("foreignManage.printBatch.printAct", {
								printType: firstSendMode ? PrintTypeEnum.运单号 : PrintTypeEnum.快递单,
								templateInfo: temp,
							});
						}]);
					} else {
						event.emit("foreignManage.printBatch.printAct", {
							printType: firstSendMode ? PrintTypeEnum.运单号 : PrintTypeEnum.快递单,
							templateInfo: temp,
						});
					}
				} else {
					let kddTempList = tradeStore.getKddTempList();
					let temp = kddTempList.find(o => o.id === tempId);
					tradeStore.setSelectedTempGroup(temp);

					if (userStore.userSetting?.extraSetting?.abnormalExpressArriveCheckNew?.printBeforeCheck && userStore.userSetting?.openAbnormalCheck === 0) {
						tradeStore.tradeListStore.updateExReachProgress({
							showProgressModal: true,
						});
						// * 需要等可达回显完再触发打印
						tradeStore.tradeListStore.updatePrintActQueue([() => {
							event.emit("printBatch.printAct", {
								printType: firstSendMode ? PrintTypeEnum.运单号 : PrintTypeEnum.快递单,
								templateInfo: temp,
							});
						}]);
					} else {
						event.emit("printBatch.printAct", {
							printType: firstSendMode ? PrintTypeEnum.运单号 : PrintTypeEnum.快递单,
							templateInfo: temp,
						});
					}
				}
				const isBreak = true;
				// // isBreak如果是true，就不会进行后续流程
				return {
					isBreak,
				};
			},
			afterChooseFjrHook: modifyDefaultFjr => {
				closeLoading();
				console.log('======afterChooseFjrHook');
				// _saveDefaultFjr(modifyDefaultFjr, 'kdd');
			},
			// 获取单号后，返回单号数据
			afterGetWaybillCodeHook: async (elecNolist, temp, isNewEleNo) => {
				closeLoading();
				console.log('======afterGetWaybillCodeHook');
				// 更新回显的单号
				// window.Tatami.pub('printCenter.updateElecNoAfterPrint', elecNolist, temp);
				// 单号数据扣除扣除
				// if (temp.wd?.branchName && temp.wd?.quantity && isNewEleNo) {
				// 	let num = parseInt(temp.wd?.quantity, 10);
				// 	temp.wd.quantity = num - 1;
				// }
				tradeStore.tradePrintStore.handleUpdateElecNoAfterPrint({
					waybillCoedList: elecNolist,
					temp
				});

				// 剩余网点展示
				const wdList = await window.printAPI.batchGetBranchSet({
					kddList: temp?.userTemplateList || [temp],
					updateExid: temp.Exid
				});
				if (temp.userTemplateList && temp.userTemplateList.length) {
					temp.userTemplateList.forEach(o => {
						if (wdList[o.userTemplateId] && !!wdList[o.userTemplateId]?.branchName) {
							o.wd = wdList[o.userTemplateId];
							wdList[o.userTemplateId].tempType = TEMP_NAME[o.expressType];
							let oldIndex = temp.wd.findIndex(v => v.tempType == TEMP_NAME[o.expressType]);
							temp.wd[oldIndex] = wdList[o.userTemplateId];
						}
					});
					temp.wd = temp.wd.sort((a, b) => a.quantity - b.quantity);
				} else {
					temp.wd = wdList[temp.Mode_ListShowId];

				}
				// 剩余单号先不处理
				// if(printAPI.configHasElecNum(temp.KddType)) {
				// 这个事件要用Tatami来派发
				// 更新快递单列表界面，主要用来更新剩余单号
				// window.Tatami.pub('printCenter.noTempUpdateTemp', {
				//     updateExCode:temp.ExCode
				// });
				// }
			},
			afterSavePrintMarkHook: ({ markTradeList, isConcatFhd }) => {
				closeLoading();
				console.log('======afterSavePrintMarkHook');
				// 更新打印标记UI
				// window.Tatami.pub('printCenter.afterPrintUpdate', markTradeList, printType);
				tradeStore.tradePrintStore.handlePrintUpdate({
					markTradeList,
					printType
				});
				if (isConcatFhd) {
					tradeStore.tradePrintStore.handlePrintUpdate({
						markTradeList,
						printType: 'fhd'
					});
				}
				if (printType === 'kdd') {
					console.log('快递单 更新先发货标识');
					tradeStore.tradeListStore.handleChoiceChange({
						type: TradeChoiceType.更新先发货标识,
						noUpdateSids: true,
					});
				}
			},
			// 保存底单日志后
			afterSavePrintLogHook: (dealedOrderList, otherSet, templateInfo) => {
				// 打印后发货
				closeLoading();
				console.log('打印后发货 orderList', orderList);
				console.log('======afterSavePrintLogHook');
				const { userSetting } = userStore;
				if (userSetting.remindShip) {
					event.emit(PRINT_BATCH_ABNORMAL_ORDER_SET_IGNORE_KEY);
					if (orderList.length === 1) {
						sendDelivery({
							togetherId: orderList[0].togetherId,
							afterPrint: true
						});
					} else {
						sendDelivery({ afterPrint: true });
					}
				}
			},
			afterShipFirstGetWaybillCodeHook: () => {
				closeLoading();
				console.log('申请运单号后发货');
				console.log('======afterShipFirstGetWaybillCodeHook');
				const { userSetting } = userStore;
				if (userSetting.remindShip) {
					if (orderList.length === 1) {
						sendDelivery({
							togetherId: orderList[0].togetherId,
						});
					} else {
						sendDelivery({});
					}
				}
			},
			httpWhiteListHook: (() => {
				closeLoading();
				// return _this.getPrivMethod('_signHttpUser');
			}),
			notifyPrintHook: (data, compType) => {
				closeLoading();
				console.log('======notifyPrintHook');
			},
			afterGetElecErrorHook: errorOrderList => {
				closeLoading();
				console.log('以下几个订单打印出错啦', errorOrderList);
				console.log('======afterGetElecErrorHook');
				customLogPost(customLogTitle, `batchPrintKdd afterGetElecErrorHook: 当订单申请单号失败后，返回失败订单列表`, {
					errorOrderList,
					printType,
					firstSendMode,
					tradeType,
					choosedPrinter,
					isMerge
				});
			},
			// 打印异常跳过的hook
			afterContinuePrintHook: (errorOrderList: any) => {
				closeLoading();
				console.log('======afterContinuePrintHook');
				const { tradeListStore: { handleUpdateList } } = tradeStore;
				handleUpdateList({
					type: "updateTradeSelectStatus",
					data: errorOrderList
				});

				customLogPost(customLogTitle, `batchPrintKdd afterContinuePrintHook: 当订单申请单号失败后，点击继续打印，返回失败订单id集合`, {
					errorOrderList,
					printType,
					firstSendMode,
					tradeType,
					choosedPrinter,
					isMerge
				});
			},
			afterAutoAddPrintNum: (dealedOrderList, waybillCodeCount) => {
				closeLoading();
				console.log('afterAutoAddPrintNum', dealedOrderList, waybillCodeCount);
				console.log('======afterAutoAddPrintNum');
				const { tradeOptStore: { addYdNoToMemo }, setting: res } = tradeStore;
				if (res.addYdNoMemo === 2) {
					if (res?.printSetExpandDTO?.addYdNoShipped === 2) {
						// 仅已发货或者线上已发货订单需要备注，先过滤一遍
						dealedOrderList = dealedOrderList?.filter(order => {
							return order.onlineShip || order.sendStatus === 'WAIT_BUYER_CONFIRM_GOODS' || order.firstSend;
						});
					}
					if (res.addYdNoOverOne === 1 || (res.addYdNoOverOne === 2 && waybillCodeCount > 1)) {
						// 屏蔽分销的订单，不修改订单备注。
						// dealedOrderList = dealedOrderList?.filter(order => !isSourceScm(order));
						if (dealedOrderList?.length == 0) {
							return;
						}
						addYdNoToMemo({
							dealedOrderList,
						});
					}
				}
			},
		});

		batchPrintKddFn(batchPrintParams);
	},
	/**
	 * @desc 批量打印发货单
	 */
	async batchPrintFhd({
		temp,
		orderList,
		fjrInfoMap,
		tradeType = ""
	}) {
		// TODO 后续使用全局通用逻辑
		// 不能使用mobx中的响应数据，会出现用户还没保存时，数据已更新，只获取用户保存后的数据
		let setting: any = JSON.parse(sessionStorage.getItem('common.advanced.set'));
		let selectTemp = setting?.groupPrintSetJsonString?.showFhdTempList === 2 ? tradeStore.selectedFhdTemp : null;
		const printAPI = window.printAPI;
		let formatTradeResult: any = formatTradeData({
			packages: orderList,
			isGetFhdInfo: true,
			isKg: userStore.userSetting?.weightUnit == 2,
			showCombinationInfo: setting.showCombinationInfo == 2
		});
		if (formatTradeResult.isError) {
			console.log('formatTradeResult Error');
			return;
		}

		// const batchPrintParams = fhdMock;
		const batchPrintParams = {
			templateInfo: selectTemp,
			orderList: formatTradeResult.orderList,
			fjrInfoMap,
			printOp: 1,
			printType: 'fhd',
			isBIC: "",
			fhdDecrypt: 0,
			displayPrintNumSet: setting.displayPrintNum == 2, // 显示打印机打印份数
			showCombinationInfo: setting.showCombinationInfo == 2 // 打印子货品
			// choosePrinterNotiSet: true, // 展示选择打印机弹出框
		};

		// 发货单 批打流程
		printAPI.batchPrintFhd(batchPrintParams, {
			afterCheckComponentHook: () => {
			},
			afterChooseTempHook: (tempId) => {
				// 该平台暂无该功能
				// rc.pub('printCenter.updateFhdTemp', tempId);
			},
			afterChooseFjrHook: (modifyDefaultFjr) => {
				// _this.getPrivMethod('_saveDefaultFjr', modifyDefaultFjr, 'fhd');
			},
			afterSavePrintMarkHook: ({ markTradeList }) => {
				// 更新打印标记UI
				// window.Tatami.pub('printCenter.afterPrintUpdate', markTradeList, printType);
				if (tradeType && tradeType === 'crossBorder') {
					foreignManageStore.tradePrintStore.handlePrintUpdate({
						markTradeList,
						printType: 'fhd'
					});
				} else {
					tradeStore.tradePrintStore.handlePrintUpdate({
						markTradeList,
						printType: 'fhd'
					});
				}

			},
			afterSavePrintLogHook() {
				console.log('发货单打印标记完成');
				// 开启自动先发货需要调用先发货
				let shipAfterPrintFhd = localStorage.getItem('shipAfterPrintFhd') === 'true'
				if (shipAfterPrintFhd) event.emit("printBatch.printAct", { printType: 'ydh' });

			}

		});
	},

	/**
	 * @desc 批量打印商品条码/吊牌
	 */
	async batchPrintGoodsBarCodeOrTag({
		orderList,
		printType,
		source = ""
	}) {
		const printAPI = window.printAPI;
		let isPrintSubGoods = false
		// 打印子货品传给打印的type不变，新增一个组合货品打印子货品的标识
		if (['zhpdp', 'zhptm'].includes(printType)) {
			const printTypeObj = {
				zhpdp: 'dpd',
				zhptm: 'tmd',
			}
			printType = printTypeObj[printType]
			isPrintSubGoods = true
		}
		const batchPrintParams = {
			orderList,
			printType,
			source,
			isPrintSubGoods
		};
		printAPI.printGoodsBarCodeOrTag(batchPrintParams, {
			// 打印完成回调
			afterSavePrintLogHook: (list) => {

			},
			// 选择模板回调
			afterChooseTempHook: (tempId) => {
				let kddTempList = tradeStore.getBhdXbqTempList();
				let temp = kddTempList.find(o => o.Mode_ListShowId == tempId);
				tradeStore.setSelectedBxdXbqTemp(temp);


				event.emit("printBatchXbq.printAct", temp);
				const isBreak = true;

				// // isBreak如果是true，就不会进行后续流程
				return {
					isBreak,
				};
			},
		});
	},
	/**
	 * @desc 批量打印采购单
	 */
	async batchPrintPurchaseOrder({
		orderList,
		printType
	}) {
		const printAPI = window.printAPI;

		const batchPrintParams = {
			orderList,
			printType
		};
		printAPI.batchPrintPurchaseOrder(batchPrintParams, {
			// 打印完成回调
			afterSavePrintLogHook: (list) => {

			},
			// 选择模板回调
			afterChooseTempHook: (tempId) => {
				// let kddTempList = tradeStore.getBhdXbqTempList();
				// let temp = kddTempList.find(o => o.Mode_ListShowId == tempId);
				// tradeStore.setSelectedBxdXbqTemp(temp);


				// event.emit("printBatchXbq.printAct", temp);
				// const isBreak = true;

				// // // isBreak如果是true，就不会进行后续流程
				// return {
				// 	isBreak,
				// };
			},
		});
	},

	/**
	 * @desc 批量打印出库单
	 */
	async batchPrintOutboundOrder({
		orderList,
		printType
	}) {
		const printAPI = window.printAPI;

		const batchPrintParams = {
			orderList,
			printType
		};
		printAPI.batchPrintOutboundOrder(batchPrintParams, {
			// 打印完成回调
			afterSavePrintLogHook: (list) => {

			},
			// 选择模板回调
			afterChooseTempHook: (tempId) => {
				// let kddTempList = tradeStore.getBhdXbqTempList();
				// let temp = kddTempList.find(o => o.Mode_ListShowId == tempId);
				// tradeStore.setSelectedBxdXbqTemp(temp);


				// event.emit("printBatchXbq.printAct", temp);
				// const isBreak = true;

				// // // isBreak如果是true，就不会进行后续流程
				// return {
				// 	isBreak,
				// };
			},
		});
	},

	/**
	 * @desc 批量打印退货清单
	 */
	async batchPrintThqdList({
		orderList,
		printType
	}) {
		const printAPI = window.printAPI;

		const batchPrintParams = {
			orderList,
			printType
		};
		printAPI.batchPrintThqdList(batchPrintParams, {
			// 打印完成回调
			afterSavePrintLogHook: (list) => {
				sendPoint(Pointer.售后_售后扫描登记_退货标签记录_打印退货清单);
			},
			// 选择模板回调
			afterChooseTempHook: (tempId) => {

			},
		});
	},
	/**
	 * @desc 批量打印小标签
	 */
	async batchPrintBhdXbq({
		temp,
		tempId,
		hotTempId,
		hotOrderList,
		orderList,
		review,
		printer,
		printType,
		takeGoodsLabelPrintSource = '', // 打印的来源页面
	}) {
		const { getScanPrintSetting, getSortCodeSetting } = scanPrintStore;
		const scanPrintSetting = await getScanPrintSetting();
		const sortCodeSetting = await getSortCodeSetting();
		const printAPI = window.printAPI;

		const batchPrintParams = {
			tempId: tempId || temp?.Mode_ListShowId,
			orderList,
			hotTempId,
			hotOrderList,
			scanPrintSetting,
			sortCodeSetting,
			hasView: !!review,
			selectedPrinter: printer
		};

		console.log('%c [ 传给打印中心的数据 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', batchPrintParams);

		// 发货单 批打流程
		printAPI.printBhdXbq(batchPrintParams, {
			// 打印小标签完成回调
			afterSavePrintLogHook: (list) => {
				tradeStore.goodsTagListStore.afterSavePrint({ list, selectList: orderList, printType, takeGoodsLabelPrintSource });
				customLogPost('printBhdXbq', '打印小标签完成回调', { list });
			},
			// afterCheckComponentHook: () => {
			// },
			afterChooseTempHook: (tempId) => {
				let kddTempList = tradeStore.getBhdXbqTempList();
				let temp = kddTempList.find(o => o.Mode_ListShowId == tempId);
				tradeStore.setSelectedBxdXbqTemp(temp);

				event.emit("printBatchXbq.printAct", temp);
				const isBreak = true;

				// // isBreak如果是true，就不会进行后续流程
				return {
					isBreak,
				};
			},

		});
	},
	/**
	 * @desc 批量打印BIC订单码
	 */
	async batchPrintBicOrderCode({
		orderList,
		review,
		printer
	}) {
		const printAPI = window.printAPI;

		const batchPrintParams = {
			orderList,
			hasView: review,
			selectedPrinter: printer,
		};
		console.log('%c [ 批量打印BIC订单码 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', batchPrintParams);

		printAPI.batchPrintBicOrderCodeList(batchPrintParams, {
			// 打印完成回调
			afterSavePrintLogHook: (list) => {
				// 列表回显订单码已打印
			},
		});
	},
	/**
	 * @desc 批量打印直播标签
	 */
	async batchPrintZbd({
		temp,
		tempId,
		orderList,
		review,
		printer,
		printType,
		isLiveTag
	}: any) {
		const { getScanPrintSetting, getSortCodeSetting } = scanPrintStore;
		const scanPrintSetting = await getScanPrintSetting();
		const sortCodeSetting = await getSortCodeSetting();
		const printAPI = window.printAPI;
		const printStyle = await _getPrintStyle();

		const batchPrintParams = {
			tempId: tempId || temp?.Mode_ListShowId,
			orderList,
			scanPrintSetting,
			sortCodeSetting,
			hasView: !!review,
			selectedPrinter: printer,
			printStyle,
			isLiveTag
		};

		console.log('%c [ 传给打印中心的数据 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', batchPrintParams);

		// 直播标签 批打流程
		printAPI.batchPrintZbd(batchPrintParams, {
			// 打印直播标签完成回调
			afterSavePrintLogHook: (list) => {
				tradeStore.liveTagListStore.afterSavePrint({ list, selectList: orderList, printType });
			},
		});
	},
	/**
	 * @desc 批量打印退货小标签
	 */
	async printThdXbq({
		temp,
		tempId,
		orderList,
		review,
		printer,
		takeGoodsLabelPrintSource = '', // 打印的来源页面
	}) {
		const { getScanPrintSetting, getSortCodeSetting } = scanPrintStore;
		const scanPrintSetting = await getScanPrintSetting();
		const sortCodeSetting = await getSortCodeSetting();
		const printAPI = window.printAPI;

		const batchPrintParams = {
			tempId: tempId || temp?.Mode_ListShowId,
			orderList,
			scanPrintSetting,
			sortCodeSetting,
			hasView: !!review,
			selectedPrinter: printer
		};

		// 发货单 批打流程
		printAPI.printThdXbq(batchPrintParams, {
			// 打印完成回调
			afterSavePrintLogHook: (list) => {
				tradeStore.goodsTagListStore.afterSavePrint({ list, selectList: orderList, printType: "thd", takeGoodsLabelPrintSource });
				customLogPost('thdxbq', '打印退货标签完成', { list: list?.map(item => {
					return {
						fj_ewm_number: item?.fj_ewm_number,
						bk_txm: item?.bk_txm,
						th_number_txm: item?.th_number_txm,
						labelStatus: item?.labelStatus,
						bk_print_time: item?.bk_print_time,
						print_time: item?.print_time,
					}
				}) });
			},
			// afterCheckComponentHook: () => {
			// },
			afterChooseTempHook: (tempId) => {
				let kddTempList = tradeStore.getBhdXbqTempList();
				let temp = kddTempList.find(o => o.Mode_ListShowId == tempId);
				tradeStore.setSelectedBxdXbqTemp(temp);


				event.emit("printBatchXbq.printAct", temp);
				const isBreak = true;

				// // isBreak如果是true，就不会进行后续流程
				return {
					isBreak,
				};
			},
			// afterChooseFjrHook: (modifyDefaultFjr) => {
			// 	// _this.getPrivMethod('_saveDefaultFjr', modifyDefaultFjr, 'fhd');
			// },
			// afterSavePrintMarkHook: ({ markTradeList }) => {
			// 	// 更新打印标记UI
			// 	// window.Tatami.pub('printCenter.afterPrintUpdate', markTradeList, printType);
			// 	tradeStore.tradePrintStore.handlePrintUpdate({
			// 		markTradeList,
			// 		printType: 'fhd'
			// 	});
			// },

		});
	},
	/**
 * @desc 批量打印退货小标签
 */
	async printBhbq({
		temp,
		tempId,
		orderList,
		review,
		printer,
		takeGoodsLabelPrintSource = '', // 打印的来源页面
	}) {
		const { getScanPrintSetting, getSortCodeSetting } = scanPrintStore;
		const scanPrintSetting = await getScanPrintSetting();
		const sortCodeSetting = await getSortCodeSetting();
		const printAPI = window.printAPI;
		orderList = orderList.flatMap(({ skuCounts, ...rest }) =>
			skuCounts.map(sku => ({
				...rest,
				...sku
			}))
		);
		const batchPrintParams = {
			tempId: tempId || temp?.Mode_ListShowId,
			orderList,
			scanPrintSetting,
			sortCodeSetting,
			hasView: !!review,
			selectedPrinter: printer
		};

		// 发货单 批打流程
		printAPI.printBhdBhbq(batchPrintParams, {
			// 打印完成回调
			afterSavePrintLogHook: (list) => {
				// tradeStore.goodsTagListStore.afterSavePrint({ list, selectList: orderList, printType: "thd", takeGoodsLabelPrintSource });
				// customLogPost('thdxbq', '打印退货标签完成', { list: list?.map(item => item?.fj_ewm_number) });
			},
			// afterCheckComponentHook: () => {
			// },
			afterChooseTempHook: (tempId) => {
				// let kddTempList = tradeStore.getBhdXbqTempList();
				// let temp = kddTempList.find(o => o.Mode_ListShowId == tempId);
				// tradeStore.setSelectedBxdXbqTemp(temp);


				// event.emit("printBatchXbq.printAct", temp);
				// const isBreak = true;

				// // // isBreak如果是true，就不会进行后续流程
				// return {
				// 	isBreak,
				// };
			},
		});
	},
	/**
	 *   重打快递单
	 * @param {Array} orderList
	 */
	async batchReprintKdd({
		fjrInfoMap,
		orderList,
		temp,
		// printOp = 1,
	}: {
		orderList: any,
		temp?: any,
		// printOp?: number,
		fjrInfoMap?: any,
	}, callback: any) {

		let formatTradeResult: any = formatTradeData({
			packages: orderList,
			isGetFhdInfo: false,
			isKg: userStore.userSetting?.weightUnit == weightUnit.显示kg,
		});
		if (formatTradeResult.isError) {
			console.log('formatTradeResult Error');
			return;
		}

		const rePrintData = {
			printType: 'kdd',
			orderList: formatTradeResult.orderList,
			fjrInfoMap,
		};
		window.printAPI.logReprint(rePrintData, {
			afterSavePrintLogHook: (info: any, fjrInfo: any) => {
				console.log('重打回传的info:', info, fjrInfo);
				let batch: any; let batchCount: any; let bgPrintInfoList;
				if (Array.isArray(info) && info[0]) {
					batch = info[0].print_batch;
					let batchNumArr = (info[0].print_count || '').split('/');
					batchCount = batchNumArr[1];
				}
				bgPrintInfoList = orderList.map((item: any, index: number) => {
					if (info[index]) {
						let batchNumArr = ((info[index].print_count) || '').split('/');
						let batchOrder = batchNumArr[0];
						return {
							logId: item.id,
							buyerOpenUid: item.buyerOpenUid,
							batchOrder,
							sendName: fjrInfo.name,
							batchCount,
							printBatch: batch,
							ydNo: item.exNumber,
							printFlag: info[index]?.printFlag,
						};
					} else {
						return {
							logId: item.id,
							batchOrder: '',
						};
					}
				});
				saveReprintLog({
					jsonParam: JSON.stringify(bgPrintInfoList)
				}).then(res => {
					callback && callback(res);
				}).catch(err => {
					message.error(err.message || '底单日志保存失败');
				});
			},
		});
	},
	/**
	 *   扫描打印快递单
	 * @param {Array} orderList
	 */
	async scanPrintKdd({
		orderList,
		templateInfo,
		choosedPrinter,
		choosedTagPrinter,
		fjrInfo,
		isMerge,
		printType = 'kdd',
		// printOp = 1,
		historySids = [],
		isForcePrint = false,
		templateInfoDetail,
		defaultAddress,
		goodsDescription,
		packagingDescription,
		waybillCodeCount,
		printNums,
		scanType, // scanInvoiceModel 老版验单发货
		isMustUseOldCode, // 强制原单号一致性校验
		logReprint, // 扫描打印先发货底单重打
		printWebSource,// 打印来源判断：1表示扫描打印
	}: {
		orderList: any,
		templateInfo?: any,
		fjrInfo: any,
		choosedPrinter: string,
		choosedTagPrinter?: string,
		isMerge: boolean,
		historySids?: string[],
		isForcePrint?: boolean
		printType?: string
		templateInfoDetail?: any,
		defaultAddress?: any,
		goodsDescription?: string
		packagingDescription?: string
		waybillCodeCount?: number
		printNums?: number,
		scanType?: String,
		isMustUseOldCode?: boolean,
		logReprint?: boolean,
		printWebSource?: string
	}) {
		const { getScanPrintSetting } = scanPrintStore;
		const scanPrintSetting = await getScanPrintSetting();
		const { setting } = tradeStore;
		const printStyle = await _getPrintStyle();
		await handleHlOrder(orderList);
		let formatTradeResult: any = formatTradeData({
			packages: orderList,
			isGetFhdInfo: false,
			isScan: true,
			isForcePrint,
			historySids,
			printFrom: 'scanPrintKdd', // 传个参数告诉来源
		});
		let customLogTitle = 'scanPrintKdd';
		if (formatTradeResult.isError) {
			console.log('formatTradeResult Error');
			customLogPost(customLogTitle, 'formatTradeResult Error', {
				formatTradeResult,
				scanType,
				printType
			});
			return;
		}
		const scanPrintData = {
			printType: 'kdd',
			isMustUseOldCode,
			orderList: formatTradeResult.orderList,
			templateInfo,
			printStyle,
			choosedPrinter,
			choosedTagPrinter,
			isPrintTag: !!scanPrintSetting?.scanSet?.isPrintTag,
			fjrInfo,
			printOp: 6,
			isMerge,
			canUseNewWaybillCodeSet: false, // 能使用新单号打印
			canChooseWaybillCodeNumSet: false, // 可以选择多单号
			displayPrintNumSet: setting.displayPrintNum == 2, // 显示打印机打印份数
			sendAfterPrint: true,
			isAutoAddPrintNum: setting.addYdNoMemo == 2,
			isNotAddPrintNumOnlyOne: setting.addYdNoOverOne == 2,
			templateInfoDetail,
			defaultAddress,
			goodsDescription,
			packagingDescription,
			waybillCodeCount,
			printNums,
			scanType,
			logReprint,
			printWebSource
		};
		console.log('扫描打印快递单 scanPrintData ：：', _.cloneDeep(scanPrintData));
		// debugger;
		window.printAPI.scanPrintKdd(scanPrintData, {
			afterSavePrintMarkHook: ({ markTradeList }) => {
				console.log('打印标记更新:', markTradeList);

				tradeStore.tradePrintStore.handlePrintUpdate({
					markTradeList,
					printType
				});
			},
			afterGetWaybillCodeHook: async (elecNolist, temp, isNewEleNo) => {
				console.log('更新回显单号：：', elecNolist, temp, isNewEleNo);
				tradeStore.tradePrintStore.handleUpdateElecNoAfterPrint({
					waybillCoedList: elecNolist,
					temp
				});

				// 剩余网点展示
				const wdList = await window.printAPI.batchGetBranchSet({
					kddList: [temp],
					updateExid: temp.Exid
				});
				temp.wd = wdList[temp.Mode_ListShowId];
			},
			// 获取运单号报错的订单
			afterGetElecErrorHook: errorOrderList => {
				console.log('以下几个订单扫描打印获取运单号出错啦', errorOrderList);

				customLogPost(customLogTitle, 'afterGetElecErrorHook 获取运单号失败', {
					errorOrderList,
					scanType,
					printType
				});

				if (errorOrderList.length) {
					const { id } = scanPrintStore.searchScanPrintFormSetting;
					const _labelIds = errorOrderList[0].labelIds || [];
					if (_labelIds.includes(id)) {
						scanPrintStore.scanPrintTempInfo.scanInfoMsg = '获取运单号失败；';
						scanPrintStore.scanPrintTempInfo.scanInfoClassName = 'scan-info-content-error';
					}
					playAudio(voiceDealFail);
				}
			},
			// results 打印成功或失败 true 成功 false 失败
			afterSavePrintLogHook: async (orderList: any, results) => {
				console.log('打印后回调：：', orderList, results);

				if (results && orderList?.length) {
					try {
						const { scanPrintFormSetting, shipData, scanPrintSetting, scanPrintOrderInfo, forceScanPrepOrderIds } = scanPrintStore;
						const { id } = scanPrintStore.searchScanPrintFormSetting;
						console.log('打印后回调：：scanPrintSetting', scanPrintSetting, scanPrintOrderInfo, forceScanPrepOrderIds, shipData, orderList);
						let params: ItemTakeGoodsLabelUpdateOrderPrintStatusRequest['updateLabelOrderStatusInfos'] = [];
						console.log('打印后回调：：1112221133333', shipData?.[orderList[0].togetherId]);
						let tempShipData = shipData?.[orderList[0].togetherId];
						console.log('打印后回调：：11122211', tempShipData);
						tempShipData?.trades?.forEach(trade => {
							let oids: string[] = [];
							let firstSendOids: string[] = [];
							const oidLabelList = []
							console.log('打印后回调：：11111', trade, trade.orders);
							trade.orders.forEach(order => {
								console.log('打印后回调：：scanPrintSetting order', order, forceScanPrepOrderIds?.has(order.oid), forceScanPrepOrderIds, scanPrintOrderInfo, Array.from(scanPrintOrderInfo?.[order.oid] || []));
								if (order.isChecked) {
									oids.push(order.oid);
									order.firstSend && (firstSendOids.push(order.oid));
									console.log('打印后回调：：1111122222333', scanPrintSetting?.scanSet?.showOutStockComplete, forceScanPrepOrderIds?.has(order.oid), scanPrintOrderInfo?.[order.oid]);
									if (scanPrintSetting?.scanSet?.showOutStockComplete && forceScanPrepOrderIds?.has(order.oid)) {
										const labelIdList = Array.from(scanPrintOrderInfo?.[order.oid] || [])
										oidLabelList.push({ oid: order.oid, labelIdList })
									}
								}
							});
							if (oids.length) {
								params.push({
									tid: trade.tid,
									oids,
									firstSendOids,
									oidLabelList
								});
							}
						});
						console.log('打印后回调：：2222', orderList, results);
						// **批量更新小标签打印状态**
						try {
							await ItemTakeGoodsLabelUpdateOrderPrintStatusApi({
								updateLabelOrderStatusInfos: params,
							});
						} catch (apiError) {
							console.error('小标签打印状态更新失败:', apiError);
							playAudio(voiceDealFail);
							scanPrintStore.scanPrintTempInfo.scanInfoMsg = '打印成功；更新打印状态失败';
							customLogPost(customLogTitle, 'afterSavePrintLogHook 小标签打印状态更新失败', {
								error: apiError,
								scanType,
								printType,
								labelId: id,
							});
							return; // 直接返回，避免后续逻辑执行
						}

						playAudio(voiceSuccess);

						if (orderList[0].labelIds && orderList[0].labelIds.includes(id)) {
							scanPrintStore.scanPrintTempInfo.scanInfoMsg = '打印成功；';
							scanPrintStore.updateInfoStatus({ sids: orderList[0].allYdNos.join(','), tidOidsObj: orderList[0]?.tidOidsObj });
						}

						tempShipData.sids = [orderList[0].allYdNos.join(',')];
						// 如果勾选的订单都是，已关闭，已发货，交易成功，不允许自动发货
						let allowSend = false;
						const disallowSendStatus = [
							TradeStatus.交易成功,
							TradeStatus.交易关闭,
							TradeStatus.交易自动关闭,
							TradeStatus.等待买家确认收货
						];
						tempShipData.trades.forEach(trade => {
							trade.orders.forEach(order => {
								if (order.isChecked) {
									// 开启了缺货强制备齐才传递
									console.log('send', scanPrintSetting?.scanSet?.showOutStockComplete, forceScanPrepOrderIds?.has(order.oid), scanPrintOrderInfo?.[order.oid]);
									if (scanPrintSetting?.scanSet?.showOutStockComplete && forceScanPrepOrderIds?.has(order.oid)) {
										order.scanPrintLabelId = scanPrintOrderInfo?.[order.oid]
										order.isForceScan = true
									}
									const isDisallowSendStatus = disallowSendStatus.includes(order.status);
									if (!isDisallowSendStatus) {
										allowSend = true;
									}
								}
							});
						});
						console.log('%c [ 要发货的数据 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', forceScanPrepOrderIds, scanPrintOrderInfo, tempShipData);
						pageLoading.destroy();
						focusLabelInput();
						// 过滤以后，如果没有可发货的订单，就不触发发货
						if (scanPrintFormSetting.autoSend && allowSend) {
							try {
								// 构造扫描打印专用的模板信息
								const scanPrintTemplateInfo = {
									id: templateInfo?.Mode_ListShowId || templateInfo?.id,
									exId: !isMerge ? templateInfo?.Exid : '',
									exCode: templateInfo?.ExCode || templateInfo?.exCode,
									exName: templateInfo?.ExcodeName || templateInfo?.groupName,
									isMergePrint: !!isMerge
								};

								await sendDelivery({
									afterPrint: true,
									type: 'scanPrint',
									scanPrintList: [tempShipData],
									isSendAbnormal: true,
									_scanLabelId:id,
									scanPrintTemplateInfo // 传递扫描打印模板信息
								});

								customLogPost(customLogTitle, 'afterSavePrintLogHook 打印后自动发货', {
									tempShipData,
									scanType,
									printType,
									labelId: id,
								});
							} catch (sendError) {
								console.error('打印后自动发货失败:', sendError);
								playAudio(voiceDealFail);
								scanPrintStore.scanPrintTempInfo.scanInfoMsg = '打印成功； 发货处理失败；';
								customLogPost(customLogTitle, 'afterSavePrintLogHook 打印后自动发货失败', {
									tempShipData,
									scanType,
									printType,
									labelId: id,
									error: sendError,
								});
								return;
							}
						} else {
							customLogPost(customLogTitle, 'afterSavePrintLogHook 过滤以后没有自动发货', {
								tempShipData,
								allowSend,
								autoSend: scanPrintFormSetting?.autoSend,
								autoSend2: scanPrintSetting?.scanSet?.autoSend,
								scanPrintSearchSetting: localStorage.getItem('scanPrintSearchSetting'),
								subUserId: userStore?.userInfo?.subUserId,
								scanType,
								printType,
								labelId: id,
							});
						}
					} catch (error) {
						playAudio(voiceDealFail);
						pageLoading.destroy();
						focusLabelInput();

						customLogPost(customLogTitle, 'afterSavePrintLogHook 遇到错误', {
							orderList,
							error,
							scanType,
							printType,
							labelId: scanPrintStore?.searchScanPrintFormSetting?.id,
						});
					}

				} else if (!results && orderList?.length) {
					pageLoading.destroy();
					focusLabelInput();
					console.log('打印失败后返回的数据：', orderList, results);
					const { id } = scanPrintStore.searchScanPrintFormSetting;
					const _labelIds = orderList[0].labelIds.labelIds || [];
					if (_labelIds.includes(id)) {
						scanPrintStore.scanPrintTempInfo.scanInfoMsg = '打印失败；';
						scanPrintStore.scanPrintTempInfo.scanInfoClassName = 'scan-info-content-error';
					}
					playAudio(voiceDealFail);

					customLogPost(customLogTitle, 'afterSavePrintLogHook 打印失败后返回的数据', {
						orderList,
						labelId: id,
						scanType,
						printType
					});
				}
			},
			// 任何打印流程中断的钩子
			printInterruptNotify: () => {
				console.log('printInterruptNotify');
				pageLoading.destroy();
				focusLabelInput();
				customLogPost(customLogTitle, 'printInterruptNotify 任何打印流程中断的钩子', {
					orderList,
					scanType,
					printType
				});
			}
		});
	},

	/**
	 * @desc 批量打印入库单
	 */
	async batchPrintWarehousingEntry({
		orderList,
		printType
	}) {
		const printAPI = window.printAPI;

		const batchPrintParams = {
			orderList,
			printType
		};
		printAPI.printGoodsBarCodeOrTag(batchPrintParams, {
			// 打印完成回调
			afterSavePrintLogHook: (list) => {

			},
			// 选择模板回调
			afterChooseTempHook: (tempId) => {
				// let kddTempList = tradeStore.getBhdXbqTempList();
				// let temp = kddTempList.find(o => o.Mode_ListShowId == tempId);
				// tradeStore.setSelectedBxdXbqTemp(temp);


				// event.emit("printBatchXbq.printAct", temp);
				// const isBreak = true;

				// // // isBreak如果是true，就不会进行后续流程
				// return {
				// 	isBreak,
				// };
			},
		});
	},
	/**
	 * 显示模板服务
	 * @param arg
	 */
	showTemplateMain(arg: {
		printType: string,
		tempId?: string,
		fjrMap?: any,
		groupTempID?: any

	}) {
		window.printAPI.showTemplateMain(arg);
	},
	/**
	 *
	 * 检查快递助手打印控件
	 */
	async checkKdzsPrintComponent(isShow=false) {
		const installResult = window.printAPI?.checkPrintComponentByKdzs({ isShow });
		return installResult;
	},
	/**
	 * 检查lodop安装
	 * isShow 传false 会显示未安装lodop弹框
	 */
	async checkLodopProgress(isShow) {
		const installResult = window.printAPI?.checkLodopProgress(isShow);
		return installResult;
	},
	/**
	 * 获取打印机列表
	 *
	 */

	async getPrinterList(controlType) {
		const { printers, dPrinter } = await window.printAPI?.getPrinterList(controlType);
		if (printers?.length) {
			const printersList = scanPrintStore.printersList;
			if (!printersList?.length) {
				scanPrintStore.setPrintersList(printers);
			}
		}
		scanPrintStore.setDefaultPrinter(dPrinter);
		return printers;
	},

	async chooseTempalteDialog(isMergePrint: boolean = false, kddTempList: IKddTemp[] = null, orderList: any = []) {
		const res: IKddTemp = await window.printAPI?.chooseTempalteDialog(isMergePrint, kddTempList, orderList);
		return res;
	},

	// 获取单快递模板列表
	async getKddTemplateList(isRequestApi = true, isOutRequest = true) {
		const kddTemplateList = await window.printAPI?.getKddTempList(true, true);
		if (kddTemplateList) {
			kddTemplateList.forEach(i => {
				i.platform = TEMP_MAP[i.KddType];
			});
			tradeStore.setExpressTemplateList(kddTemplateList);
		}
		return kddTemplateList || [];
	},

	// 获取标签模板列表
	async getBqTempList(printType, isRequestApi?: boolean) {
		try {
			const bqTempList = await waitForPrintAPIToLoad(printType);
			return bqTempList || [];
		} catch (error) {
			console.log("bqTempList error:", error);
		}

	},

	// 获取直播标签模板列表
	async getZbdTempList(printType, isRequestApi?: boolean) {
		try {
			const zbdTempList = await waitForPrintAPIToLoad(printType);
			return zbdTempList || [];
		} catch (error) {
			console.log("zbdTempList error:", error);
		}

	},

	/**
	 * @desc 批量波次打印
	 */
	async batchPrintWaveKdd({
		orderList,
		fjrInfoMap,
		from, // 来源
	}) {
		const printAPI = window.printAPI;
		let formatTradeResult: any = formatTradeData({
			packages: orderList,
			isGetFhdInfo: false,
			isKg: userStore.userSetting?.weightUnit == weightUnit.显示kg,
		});
		const batchPrintParams = {
			orderList: formatTradeResult.orderList,
			fjrInfoMap,
		};
		console.log('%c [ 批量波次打印 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', batchPrintParams);

		printAPI.concentratePrint(batchPrintParams, {
			// 返回控件是否连接正常
			afterCheckComponentHook: ({ type, isSuccess, downUrl }) => {
				console.log('======afterCheckComponentHook', isSuccess);
				if (!isSuccess) {
					closeLoading();
				}
			},
			// 保存底单日志后 （打印走极速模式，默认为是成功了）
			afterSavePrintLogHook: () => {
				console.log('======afterSavePrintLogHook');
				closeLoading();



				// 打印成功提示
				// playAudio(voiceSuccess);

				// 记录打印日志
				customLogPost('wave', 'afterSavePrintLogHook 打印成功', {
					from,
					orderList: orderList.map(order => ({
						waveNo: order?.waveNo,
						packageNo: order?.packageNo,
						exNumber: order?.exNumber,
						exName: order?.exName,
					}))
				});


				const { userSetting } = userStore;
				// 打印后是否提醒发货
				if (userSetting.remindShip) {
					event.emit(PRINT_BATCH_ABNORMAL_ORDER_SET_IGNORE_KEY); // 这个起到什么作用？
					if (orderList.length > 0) {
						waveStore.afterPrintAutoWaveSend(orderList, from);
					}
				} else {
					// 根据来源发送不同的事件通知
					switch (from) {
						case 'waveList':
							// 波次列表页面打印完成后通知
							event.emit('waveList.afterPrint', { packageNoList: orderList?.map(d => d.packageNo) });
							break;
						case 'waveDetail':
							// 波次详情页面打印完成后通知
							event.emit('waveDetail.afterPrint', { packageNoList: orderList?.map(d => d.packageNo) });

							// 波次列表也更新下
							// event.emit('waveList.updateWaveList');
							break;
						default:
							break;
					}
				}
			},
		});
	},

};

async function waitForPrintAPIToLoad(printType): Promise<any> {
	if (window.__printAPI) {
		return window.printAPI.getBqTempList(printType);
	} else {
		await new Promise(resolve => setTimeout(resolve, 100));
		return waitForPrintAPIToLoad(printType); // 递归调用自身直到条件满足
	}
}

