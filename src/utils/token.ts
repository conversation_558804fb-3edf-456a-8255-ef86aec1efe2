import userStore from "@/stores/user";
import { memoClean } from "./memo";
import { getStrParam } from './index';
import { isViteDevMode } from "@/constants";

export const getToken = () => {
	const key = getStrParam('userId');
	return sessionStorage.qnquerystring || localStorage.getItem(key);
};

export const setToken = (token: string, key?: string) => {
	const userId = getStrParam('userId');
	sessionStorage.qnquerystring = token;
	localStorage[key || userId] = token;
};

export const logout = () => {
	console.log('退出登录');
	const currentPath = window.location.hash?.replace('#', '') || '/';
	clearToken();
	import('./memo').then(res => {
		res.memoClean();

		// 记录当前路径，重新登录后跳转使用
		if (!['/login', '/register', '/forgetPwd'].includes(currentPath)) {
			sessionStorage.setItem('redirectAfterLogin', currentPath);
		}

		let href = '/index.html#/login';
		if (isViteDevMode()) {
			href = '/#/login';
		}
		location.href = href;
	});
};

export const clearToken = () => {
	const key = getStrParam('userId');
	localStorage.removeItem(key);
	sessionStorage.removeItem('qnquerystring');
};
