@import "antd/dist/antd.less";
@import "@raycloud/sass";
@import "./assets/styles/ant-theme.less";


* {
    ::-webkit-scrollbar {
        cursor: pointer;
        height: 10px;
        width: 10px;
    }

    ::-webkit-scrollbar-thumb {
        transition: all 0.3s;
        border-radius: 0;
        border-style: dashed;
        background-color: rgba(0, 0, 0, 0.25);
        border-color: transparent;
        border-width: 1px;
        background-clip: padding-box;
        border-radius: 10px;
    }

    ::-webkit-scrollbar-thumb:hover {
        transition: all 0.3s;
        background: rgba(0, 0, 0, 0.4);
    }
}

/**
*项目中需要特殊定义的一些全局样式
*/
.icon {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
}

body {
    min-width: 1150px;
    height: 100%;
    min-width: 1200px;
    overflow-y: auto;
    font-size: 14px;
    box-sizing: border-box;
    background: #f5f5f5;
}

.form-item-mb-8 {
    .ant-form-item {
        margin-bottom: 8px;
    }
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: bold;
}

h1,
h2,
h3,
h4,
h5,
h6,
dl,
dd {
    margin: 0;
}

.g-tooltips {
    .ant-tooltip-inner {
        color: #000;
    }
}

.g-card-border {
    background: #ffffff;
    border: 1px solid #eef4ff;
    box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.04);
    border-radius: 4px;
    border-radius: 4px;
}

.g-card-title {
    font-size: 16px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 24px;
}

.g-c-blue {
    color: #3089dc;
}

.g-nopermission-opt {
    // width: fit-content;
    cursor: not-allowed;
}

.g-inline-flex {
    display: inline-flex;
}

.r-trade-identifier {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    padding: 0 4px;
    height: 20px;
    margin-right: 6px;
    border-radius: 2px;
    font-family: PingFangSC-Regular;
    font-size: 12px;
    font-weight: 400;
    color: #ffffff;
    line-height: 20px;
}

.autoSize {
    width: 100%;
    height: 100%;
    font-size: 10px;
}

.g-width-fit {
    width: fit-content;
}

.r-width-fit {
    width: fit-content;
    padding: 0 4px;
}

.r-trade-smart-express {
    &:extend(.r-trade-identifier);
    background: #2296f5;
}

.r-trade-taobao {
    &:extend(.r-trade-identifier);
    background: #ffa940;
}

.r-trade-hand {
    &:extend(.r-trade-identifier);
    background: #fd8204;
}

.r-trade-doudian {
    &:extend(.r-trade-identifier);
    background: rgba(33, 31, 38, 0.85);
}

.r-trade-ktt {
    &:extend(.r-trade-identifier);
    background: #07C160;
}

.r-trade-pdd {
    &:extend(.r-trade-identifier);
    background: #eb2121;
}

.r-trade-tm {
    &:extend(.r-trade-identifier);
    background: #000;
}

.r-trade-ksxd {
    &:extend(.r-trade-identifier);
    background: #fc5125;
}

.r-trade-sph {
    &:extend(.r-trade-identifier);
    background: #fc5125;
}

.r-trade-xhs {
    &:extend(.r-trade-identifier);
    background: #ff0035;
}

.r-trade-c2m {
    &:extend(.r-trade-identifier);
    background: #218af4;
}

.r-trade-scm {
    &:extend(.r-trade-identifier);
    background: #f5222d;
}

.r-trade-exchange {
    &:extend(.r-trade-identifier);
    background: #00a6f0;
}

.r-trade-add {
    &:extend(.r-trade-identifier);
    background: #00a6f0;
    width: auto;
}

.r-trade-modified-online {
    &:extend(.r-trade-identifier);
    width: auto;
    background: #00a6f0;
}

.r-trade-refund {
    &:extend(.r-trade-identifier);
    background: #f5222d;
}

.r-trade-empty-goods {
    &:extend(.r-width-fit);
    &:extend(.r-trade-identifier);
    background: #8c8c8c;
}

.r-trade-goods-warn,
.r-trade-preship,
.r-trade-merge {
    &:extend(.r-trade-identifier);
    &:extend(.r-width-fit);
    background: #fa8c16;
}

.r-trade-no-relate {
    &:extend(.r-trade-identifier);
    background: #f5222d;
}

.r-trade-has-taged {
    &:extend(.r-trade-identifier);
    background: #75ad03;
}

.r-trade-first-send {
    &:extend(.r-trade-identifier);
    background: #20b7e6;
}

.r-trade-no-goods {
    &:extend(.r-trade-identifier);
    &:extend(.r-width-fit);
    background: #fa8c16;
}

.r-trade-split {
    &:extend(.r-trade-identifier);
    background: #2f54eb;
}

.r-trade-is-gift {
    &:extend(.r-trade-identifier);
    background: #02a7f0;
}

// 系统赠品
.r-trade-is-sysGift {
    &:extend(.r-trade-identifier);
    background: #FF4D4F;
    width: auto;
}

//波次
.r-trade-is-waveNo {
    &:extend(.r-trade-identifier);
    background: #fa8c16;
    width: auto;
}

.r-trade-pending-icon {
    position: absolute;
    left: -28px;
    top: 50%;
    transform: translateY(-50%);
    margin-top: 14px;
    height: 12px;
    width: 12px;
}

.r-trade-refund {
    &:extend(.r-trade-identifier);
    background: #f5222d;
}

.r-trade-isPreSale {
    &:extend(.r-width-fit);
    border: 1px solid #f5222d;
    color: #f5222d;
}

// 组合
.r-warehouse-combined {
    &:extend(.r-trade-identifier);
    background: #e02020;
}

.r-warehouse-combined-new {
    &:extend(.r-trade-identifier);
    background: #FAAD14;
    width: 50px;
}

.g-clickable {
    cursor: pointer;
    color: #0089fa;
}

.kdzs-section {
    background-color: #ffffff;
    margin: 8px 0;
    padding: 8px 16px;
}

.kdzs-section-small {
    background-color: #ffffff;
    margin: 8px 0;
    padding: 8px 24px;
}

.g-bg-f0f2f5 {
    background: #f0f2f5;
}

.c-modal-height-900 {
    height: 750px;
}

.r-btn-primary {
    color: #fff;
    background: #fd8204;
    border: 1px solid #fd8204;

    &:hover {
        background: #fd8204;
        opacity: 0.8;
        color: #fff;
        border: 1px solid #fd8204;
    }

    &:focus {
        color: #fff;
        background: #fd8204;
        border: 1px solid #fd8204;
    }
}

.r-btn-green {
    color: #fff;
    background: #00b77f;
    border: 1px solid #00b77f;

    &:hover {
        background: #00b77f;
        opacity: 0.8;
        color: #fff;
        border: 1px solid #00b77f;
    }

    &:focus {
        color: #fff;
        background: #00b77f;
        border: 1px solid #00b77f;
    }
}

.r-btn-blue {
    color: #fff;
    border: 1px solid #20b7e6;
    background: #20b7e6;

    &:hover {
        opacity: 0.8;
        border: 1px solid #20b7e6;
        background: #20b7e6;
        color: #fff;
    }

    &:focus {
        color: #fff;
        border: 1px solid #20b7e6;
        background: #20b7e6;
    }
}

.edit-qianbi-icon {
    background: url(https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/xcx/tb/img/print/zizhuban_icon_xz.png) no-repeat -72px -22px;
    display: inline-block;
    width: 18px;
    height: 18px;
    cursor: pointer;
    vertical-align: middle;
}

.add-beizhu-icon {
    position: relative;
    top: 5px;
    background: url(https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/xcx/tb/img/print/zizhuban_icon_xz.png) no-repeat;
    display: inline-block;
    background-position: -21px -22px;
    width: 22px;
    height: 20px;
}

.trade-mark-wrapper {
    .mark {
        display: inline-block;
        width: 8px;
        height: 8px;
        font-size: 0;
        line-height: 0;
        margin-right: 5px;
        background-image: url(https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/xcx/tb/img/print/group.png);
    }

    .mark_dotred {
        background-position: -339px -12px;
    }

    .mark_dotyellow {
        background-position: -351px -12px;
    }

    .mark_dotgreen {
        background-position: -363px -12px;
    }

    .mark_dotblue {
        background-position: -375px -12px;
    }

    .mark_dotpurple {
        background-position: -387px -12px;
    }
}

.userset_icons,
.mark_dot,
.memoNameDialogCon {
    display: flex;

    span {
        display: block;
        width: 12px;
        height: 12px;
        font-size: 0;
        line-height: 0;
        margin-right: 5px;
        background-size: 540px;
        background-image: url(https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/xcx/tb/img/print/group.png);
        // margin-top: 7px;
    }

    span.userset_kdd {
        background-position: -299px -28px;
    }

    span.userset_kdd_gray,
    .userset_fhd_gray {
        background-position: -284px -28px;
    }

    .mark_dotred {
        background-position: -404px -14px;
    }

    .mark_dotyellow {
        background-position: -418px -14px;
    }

    .mark_dotgreen {
        background-position: -432px -14px;
    }

    .mark_dotblue {
        background-position: -446px -14px;
    }

    .mark_dotpurple {
        background-position: -460px -14px;
    }

    .mark_dotred_sel {
        background-position: -404px top;
    }

    .mark_dotyellow_sel {
        background-position: -418px top;
    }

    .mark_dotgreen_sel {
        background-position: -432px top;
    }

    .mark_dotblue_sel {
        background-position: -446px top;
    }

    .mark_dotpurple_sel {
        background-position: -460px top;
    }

    .mark_half_dotred {
        background: url(https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/xcx/tb/img/print/group.png) -454px -237px;
        background-size: 540px;
    }

    .mark_half_dotyellow {
        background: url(https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/xcx/tb/img/print/group.png) -468px -237px;
        background-size: 540px;
    }

    .mark_half_dotgreen {
        background: url(https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/xcx/tb/img/print/group.png) -482px -237px;
        background-size: 540px;
    }

    .mark_half_dotblue {
        background: url(https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/xcx/tb/img/print/group.png) -496px -237px;
        background-size: 540px;
    }

    .mark_half_dotpurple {
        background: url(https://kdzs-xcx-front.oss-cn-zhangjiakou.aliyuncs.com/xcx/tb/img/print/group.png) -510px -237px;
        background-size: 540px;
    }
}

.r-fc-white-85 {
    color: rgba(255, 255, 255, 0.85) !important;
}

.r-fc-black-85 {
    color: rgba(0, 0, 0, 0.85) !important;
}

.r-fc-black-65 {
    color: rgba(0, 0, 0, 0.65) !important;
}

.r-fc-black-45 {
    color: rgba(0, 0, 0, 0.45) !important;
}

.r-fc-black-25 {
    color: rgba(0, 0, 0, 0.25) !important;
}

.r-lh-22 {
    line-height: 22px;
}

// table合并单元格样式修改
/*  合并单元格样式调整  */

#root {
    .ant-table-tbody>tr>td .c-table-merge-row {
        display: flex;
        padding: 0px;
        border-bottom: 1px solid #e8e8e8;
        line-height: 40px;
        margin: 0 -8px;
    }

    .ant-table-tbody>tr>td>.c-table-merge-row:last-child {
        margin-bottom: -8px;
        border: 0;
    }

    .ant-table-tbody>tr>td>.c-table-merge-row:first-child {
        margin-top: -8px;
    }

    .ant-table-tbody>tr>td .c-table-merge-row:hover {
        //   background-color: #fafafa;
    }

    .aftersale-expand-table {
        .ant-table-wrapper:only-child .ant-table {
            margin: 0 !important;
        }
    }
}

.g-platform-icon-box {
    width: 20px;
    height: 20px;
    line-height: 20px;
    background: #ffa940;
    border-radius: 2px;
    color: #fff;
    display: inline-block;
}

.r-btn-1890FF {
    border: 1px solid #1890ff;
    background: #1890ff;
    color: #fff;

    &:hover {
        opacity: 0.8;
        border: 1px solid #1890ff;
        background: #1890ff;
        color: #fff;
    }

    &:focus {
        background: #1890ff;
        border-color: #1890ff;
        color: #fff;
    }
}

.r-btn-52C41A {
    border: 1px solid #52c41a;
    background: #52c41a;
    color: #fff;

    &:hover {
        opacity: 0.8;
        border: 1px solid #52c41a;
        background: #52c41a;
        color: #fff;
    }

    &:focus {
        background: #52c41a;
        border-color: #52c41a;
        color: #fff;
    }
}

.r-btn-5CDBD3 {
    border: 1px solid #5cdbd3;
    background: #5cdbd3;
    color: #fff;

    &:hover {
        opacity: 0.8;
        border: 1px solid #5cdbd3;
        background: #5cdbd3;
        color: #fff;
    }

    &:focus {
        background: #5cdbd3;
        border-color: #5cdbd3;
        color: #fff;
    }
}

.r-input-val-change {
    background: #fff5eb;
    border: 1px solid #f5821f;
}

.no-check-icon-select {
    .ant-select-item-option-state {
        display: none !important;
    }
}

.r-lh-12 {
    line-height: 12px;
}

.r-bg-F5821F {
    background: #f5821f !important;
}

.r-bg-52C41A {
    background: #52c41a !important;
}

.r-wb-bw {
    word-break: break-word;
}

.r-wb-ka {
    word-break: keep-all;
}

.r-fc-1890FF {
    color: #1890ff !important;
}

.r-l-preWrap {
    white-space: pre-wrap;
}

.r-fc-F5821F {
    color: #f5821f !important;
}

.c-aftersale-modal-label {
    width: 100px;
    text-align: right;
    color: rgba(0, 0, 0, 0.85);
    line-height: 22px;
    font-weight: 400;
}

.c-aftersale-modal-table-title {
    background: rgb(250, 250, 250);
    margin-bottom: 0 !important;
    line-height: 1 !important;
    padding: 16px;
}

.r-ws-nw {
    white-space: nowrap;
}

.r-va-m {
    vertical-align: middle !important;
}

.c-aftersale-table {
    border-bottom: 1px solid #e9e9e9 !important;
}

.r-link-with-hover {
    &:hover {
        cursor: pointer;
        color: #fd8204;
        text-decoration: underline;
    }
}

.rightBottomPopup {
    position: fixed;
    z-index: 999;
    bottom: 0;
    right: 0;

    .sys-banner-container .close-btn {
        top: 16px;
    }
}

.trade-status-success {
    color: #008000;
}

.trade-status-error {
    color: #ff0000;
}

.r-c-FF4D4F {
    color: #ff4d4f;
}

.r-c-F5222D {
    color: #f5222d;
}

.r-c-black85 {
    color: rgba(0, 0, 0, 0.85);
}

.r-c-black65 {
    color: rgba(0, 0, 0, 0.65);
}

.r-c-black45 {
    color: rgba(0, 0, 0, 0.45);
}

// 新虚拟列表行点击样式
.art-table-row {
    &.r-row-checked {
        .art-table-cell {
            background-color: #FFF7E6 !important;
        }
    }

    &.r-row-locked {
        .art-table-cell {
            background-color: #C1D3E4 !important;
        }
    }

    &.r-row-default {
        &:hover {
            .art-table-cell {
                background-color: #F5F5F5 !important;
            }
        }
    }

    &.hovered-group {
        .r-row-group {
            background-color: #F5F5F5 !important;
        }
    }

    &.hovered-group-all{
        .art-table-cell{
            background-color: #F5F5F5 !important;
        }
    }

}


.r-label-require::before {
    display: inline-block;
    margin-right: 4px;
    color: #ff4d4f;
    font-size: 14px;
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: "*";
}

.modify-memo-flag-item {
    height: 32px;
    border-radius: 16px;
    font-size: 12px;
    line-height: 14px;
    padding: 0 8px;
    display: flex;
    align-items: center;
    border: 1px solid #999;
    color: #999;

    &.active {
        border: 1px solid #f5222d;
        color: #f5222d;
    }

    &:nth-last-child {
        margin-right: 0;
    }
}

.r-wangwang {
    display: inline-block;
    background: url("https://img.alicdn.com/tps/i1/T15AD7FFFaXXbJnvQ_-130-60.gif");
    background-position: -80px 0;
    width: 20px;
    height: 20px;
    cursor: pointer;
    flex-shrink: 0;

    &.pdd {
        background: url("./assets/image/user/拼多多联系买家.png") no-repeat;
        background-position: 0 0;
        background-size: 100%;
    }
}

.r-trade-warn-filled {
    font-size: 12px;
    border-radius: 2px;
    white-space: nowrap;
    height: 20px;
    font-weight: 400;
    padding: 0 6px;
    box-sizing: border-box;
    line-height: 12px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 2px;
    margin-left: 4px;
    cursor: pointer;
    background: #f5821f;
    color: #ffffff;
}

.r-flex-shrink {
    flex-shrink: 0;
}

// 生成 gap 工具类
.generate-gaps(@prefix, @start, @end, @step) {
    .loop(@i) when (@i =< @end) {
        .@{prefix}-@{i} {
            gap: @i * 1px;
        }

        .loop(@i + @step);
    }

    .loop(@start);
}

// 生成 gap 工具类，从 2px 到 24px，步长为 2
.generate-gaps(r-gap, 2, 24, 2);