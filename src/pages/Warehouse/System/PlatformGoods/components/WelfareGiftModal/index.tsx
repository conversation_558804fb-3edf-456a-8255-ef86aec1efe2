import React, { useEffect, useState } from 'react';
import { Mo<PERSON>, Button, message } from 'antd';
import xianshi from '@/assets/image/icon_限时@2x.png'; 
import sendPoint from '@/utils/pointTrack/sendPoint';
import Pointer from '@/utils/pointTrack/constants';
import s from './index.module.scss';
import { ActivityGiftLimit, GetUserActivityInfo } from '@/apis/user';

interface WelfareGiftModalProps {
	visible: boolean;
	onCancel: () => void;
	onClaim?: () => void; // 领取回调
	title?: string;
	userType?: string;
	giftCount?: number;
	validityDays?: number;
}

const WelfareGiftModal: React.FC<WelfareGiftModalProps> = ({
	visible,
	onCancel,
	onClaim,
	title = "限时福利赠送",
	userType = "活动新用户",
	giftCount = 5,
	validityDays = 5
}) => {
	useEffect(() => {
		if (visible) {
			sendPoint(Pointer.临时额度赠送弹窗_展示);
		}	
	}, [visible]);
	const handleClaim = async() => {
		// 发送埋点
		sendPoint(Pointer.临时额度领取_点击);

		onClaim?.();
		// 关闭弹窗
		onCancel();
	};

	return (
		<Modal
			visible={ visible }
			footer={ null }
			width={ 480 }
			centered
			onCancel={ onCancel }
			className={ s.welfareGiftModal }
			zIndex={ 1000 }
		>
			<div className={ s.modalContent }>
				{/* 标题区域 */}
				<div className={ s.titleSection }>
					{/* 图标 */}
					<div className={ s.iconWrapper }>
						<img
							src={ xianshi }
							alt="限时福利"
							className={ s.icon }
						/>
					</div>
					<span className={ s.title }>
						{title}
					</span>
				</div>

				{/* 主要内容 */}
				<div className={ s.mainContent }>
					您是
					<span className={ s.highlightUserType }>
						{userType}
					</span>
					，
					<span className={ s.freeText }>
						免费
					</span>
					获得
					<span className={ s.giftCount }>
						{giftCount}
					</span>
					个商品铺货额度
				</div>

				{/* 有效期信息 */}
				<div className={ s.validityInfo }>
					有效期：自领取之日内{validityDays}天有效
				</div>

				{/* 立即领取按钮 */}
				<Button
					type="primary"
					className={ s.claimButton }
					onClick={ handleClaim }
				>
					立即领取
				</Button>
			</div>
		</Modal>
	);
};

export default WelfareGiftModal;
