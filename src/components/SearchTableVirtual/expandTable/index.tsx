import React, { memo, useState, ReactNode, useEffect, useRef } from 'react';
import { Checkbox, Button, Pagination } from 'antd';
import { useTablePipeline, features } from 'ali-react-table-threshold';
import type { PaginationProps } from 'antd';
import Icon from '@/components/Icon';
import { AntdBaseTable as Table } from '../antd.tb';
import { Nav } from './nav';
import '../index.module.scss';

/** 表格列的类型定义 */
interface ColumnType {
	key?: string;
	dataIndex?: string;
	title?: string;
	[key: string]: any;
}

/**
 * Props for the ExpandTable component, a versatile table component with expandable rows,
 * selectable rows, sorting, and pagination support.
 *
 * @template T - The type of the data records.
 *
 * @property {T[]} dataSource - The data records to be displayed in the table.
 * @property {ColumnType[]} columns - Definitions for each column in the table.
 * @property {keyof T} rowKey - The unique identifier key for each row.
 * @property {object} [expandable] - Configuration for expandable rows.
 * @property {(record: T) => ReactNode} expandable.expandedRowRender - Function to render expanded row content.
 * @property {object} [rowSelection] - Configuration for row selection.
 * @property {(string | number)[]} rowSelection.selectedRowKeys - Selected row keys.
 * @property {(selectedRowKeys: (string | number)[]) => void} rowSelection.onChange - Callback for row selection change.
 * @property {(props: any) => void} [onExpand] - Callback when a row expand action is triggered.
 * @property {boolean} isLoading - Whether the table is currently loading data.
 * @property {object} [components] - Custom components for table internals.
 * @property {React.ComponentType} components.LoadingIcon - Icon component to show while loading.
 * @property {React.ComponentType} components.EmptyContent - Component for empty content.
 * @property {React.ComponentType} components.LoadingContentWrapper - Wrapper for loading content.
 * @property {React.ComponentType} components.TableBody - Custom table body component.
 * @property {React.ComponentType} components.Row - Custom row component.
 * @property {React.ComponentType} components.Cell - Custom cell component.
 * @property {number} [insertPosition] - Position for inserting the expandable column.
 *                                       Options: 0 (last), 1 (first), -1 (second-last), undefined (no insert).
 * @property {object} [sort] - Sorting configuration.
 * @property {(nextSorts: { sortField: string, sortOrder: 'desc' | 'asc' | 'none' }) => void} sort.onSortChange - Callback for sorting change.
 * @property {string} sort.sortKey - Key of the column to be sorted.
 * @property {PaginationProps & { align: 'start' | 'center' | 'end' }} [pagination] - Pagination settings with alignment options.
 */
interface ExpandTableProps<T> {
	dataSource: T[];
	columns: ColumnType[];
	rowKey: keyof T;
	outerOpenKeys: Set<string | number>;
	expandable?: {
		expandedRowRender: (record: T, ...args:any[]) => ReactNode;
	};
	rowSelection?: {
		selectedRowKeys: (string | number)[];
		onChange?: (selectedRowKeys: (string | number)[]) => void;
	};
	onExpand?: (props: any) => void;
	isLoading?: boolean;
	components?: {
		LoadingIcon?: React.ComponentType;
		EmptyContent?: React.ComponentType;
		LoadingContentWrapper?: React.ComponentType;
		TableBody?: React.ComponentType;
		Row?: React.ComponentType;
		Cell?: React.ComponentType;
	};
	insertPosition?: number;
	sort?: {
		onSortChange: (nextSort: { sortField: string, sortOrder: 'desc' | 'asc' | 'none' }) => void;
		sortKey: string;
	};
	pagination?: PaginationProps & {
		align?: 'start' | 'center' | 'end';
	};
	paginationStyle?: {
		bottom: number // 距离页面底部的高度
	}
}

const ExpandTable = memo(<T extends Record<string, any>>({
	dataSource = [],
	columns,
	rowKey,
	expandable,
	rowSelection,
	onExpand: externalOnExpand,
	isLoading = false,
	components = {},
	insertPosition,
	sort,
	pagination,
	paginationStyle,
	outerOpenKeys,
}: ExpandTableProps<T>) => {
	const [openKeys, setOpenKeys] = useState<Set<string | number>>(new Set());

	useEffect(() => {
		setOpenKeys(new Set(outerOpenKeys));
	}, [outerOpenKeys]);
	
	// 初始化列
	const getColumns = () => columns.map(column => ({
		...column,
		code: column.key ?? column.dataIndex,
		name: column.title,
	}));

	const pipeline = useTablePipeline({ components: rowSelection ? { Checkbox } : {} })
		.input({
			dataSource,
			columns: getColumns(),
		})
		.primaryKey(rowKey as string);

	const handleExpand = (record: T) => {
		const key = record[rowKey] as string | number;
		setOpenKeys(prev => {
			const newKeys = new Set(prev);
			newKeys.has(key) ? newKeys.delete(key) : newKeys.add(key);
			return newKeys;
		});
		externalOnExpand?.(pipeline.getProps());
	};

	// 多选功能
	if (rowSelection) {
		pipeline.use(features.multiSelect({
			checkboxPlacement: 'start',
			clickArea: 'row',
			highlightRowWhenSelected: true,
			value: rowSelection.selectedRowKeys as string[],
			checkboxColumn: { lock: true },
			onChange: rowSelection.onChange,
		}));
	}

	const detailRef = useRef(null);
	// 展开功能
	pipeline.use(features.rowDetail({
		stopClickEventPropagation: false,
		clickArea: 'icon',
		openKeys: Array.from(openKeys) as string[],
		hasDetail: () => Boolean(expandable?.expandedRowRender),
		rowDetailMetaKey: 'expand_detail',
		renderDetail(row, rowIndex) {
			// 计算 maxWidth
			const rowSelectionWidth = 50; // 假设 rowSelection 的宽度
			const expandColumnWidth = 50; // 假设展开列的宽度
			const paddingMargin = 16; // 其他边距
			let maxWidth = `calc(100vw - ${rowSelectionWidth}px - ${paddingMargin}px)`;
			if (expandable) {
				let lastColumnWidth = 0;
				if (insertPosition === -1) {
					lastColumnWidth = columns[columns.length - 1].width; // 获取倒数第一列的宽度
					maxWidth = `calc(100vw - ${rowSelectionWidth}px - ${paddingMargin}px - ${expandColumnWidth}px - ${lastColumnWidth}px)`;
				} else {
					maxWidth = `calc(100vw - ${rowSelectionWidth}px - ${paddingMargin}px - ${expandColumnWidth}px)`;
				}
			}

			// setTimeout(() => {
			// 	const parentElement = detailRef.current?.parentElement;
			// 	if (parentElement) {
			// 		// 确保只增加一次 colspan
			// 		if (!parentElement.hasAttribute('data-colspan-adjusted')) {
			// 			// 获取并增加 colspan
			// 			const currentColSpan = parseInt(parentElement.getAttribute('colspan')) || 1;
			// 			parentElement.setAttribute('colspan', currentColSpan + 1);
			// 			parentElement.setAttribute('data-colspan-adjusted', 'true');
			// 		}

			// 		// 仅移除当前元素的兄弟元素一次
			// 		const siblingElements = Array.from(parentElement.parentElement?.children || []).filter(
			// 			sibling => sibling !== parentElement
			// 		);
			// 		siblingElements.forEach(sibling => {
			// 			sibling.style.position = 'unset';
			// 			sibling.remove();
			// 		});
			// 	}
			// }, 0);

			return (
				<div
					style={ {
						margin: '-8px 0px 0px',
						width: '100%',
						backgroundColor: '#fff',
						gridColumn: '1 / -1', // 使用grid让展开行跨越所有列
					} }
					ref={ detailRef }
				>
					<div style={ { maxWidth } }>
						{expandable?.expandedRowRender?.(row, rowIndex, undefined, true)}
					</div>
				</div>
			);
		},
	}));

	// 排序功能
	if (sort) {
		pipeline.use(features.sort({
			onChangeSorts: (nextSort) => {
				sort.onSortChange({
					sortField: nextSort[0]?.code,
					sortOrder: nextSort[0]?.order || 'none',
				});
				setTimeout(() => {
					const paths = document.querySelector('path[fill="#23A3FF"]');
					paths?.setAttribute('fill', '#FF5733');
				}, 0);
			},
			defaultSorts: [{ code: sort.sortKey, order: 'none' as const }],
			orders: ['desc', 'asc', 'none'],
			mode: 'single',
		}));
	}

	// 添加扩展列
	// Add expandable column
	pipeline.mapColumns((cols) => {
		if (!cols.length) return cols;
		const expandColumn = {
			width: 54,
			code: 'expand',
			name: '',
			render: (text, record) => {
				if (record['expand_detail']) {
					return null;
				}
				return (
					<Button
						type="link"
						style={ { padding: 0, width: '100%' } }
						onClick={ (e) => {
							e.stopPropagation();
							const newExpandState = !record['expand'];
							record['expand'] = newExpandState;
							handleExpand(record);
						} }
					>
						<Icon type={ openKeys.has(record[rowKey] as any) ? 'shouqi' : 'zhankai' } size={ 14 } />
					</Button>
				);
			}
		};

		switch (insertPosition) {
			case 0:
				cols.push(expandColumn);
				break;
			case 1:
				cols.unshift(expandColumn);
				break;
			case -1:
				cols.splice(cols.length - 1, 0, expandColumn);
				break;
			default:
				break;
		}

		if (insertPosition !== undefined) {
			cols.slice(-2).forEach((col) => {
				col.lock = 'right';
			});
		}

		return cols;
	});

	useEffect(() => {
		setTimeout(() => {
			const paths = document.querySelector('path[fill="#23A3FF"]');
			paths?.setAttribute('fill', '#FF5733');
		}, 0);
	}, [columns]);


	return (
		<>
			<Table
				{ ...pipeline.getProps() }
				isStickyHeader
				bordered
				stickyTop={ 92 }
				isLoading={ isLoading }
				components={ components as any }
				paginationStyle={ paginationStyle || (pagination ? { bottom: 44, originBottom: 0 } : {}) }
			/>
			{pagination && (
				<Nav align={ pagination.align || 'center' }>
					<Pagination size="small" { ...pagination } />
				</Nav>
			)}
		</>
	);
});

export default ExpandTable;
