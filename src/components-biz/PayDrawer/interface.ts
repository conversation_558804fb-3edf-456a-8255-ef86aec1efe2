/*
 * @Author: 唐荣洋 <EMAIL>
 * @Date: 2025-08-26 16:35:57
 * @Description:
 */
import dayjs from 'dayjs';
import warningPng from '@/assets/image/pay/warning.png';
import warningPackPng from '@/assets/image/pay/warning_pack.png';

import dataPng from '@/assets/image/pay/data.png';
import dataPackPng from '@/assets/image/pay/data_pack.png';

import itemPng from '@/assets/image/pay/item.png';
import itemPackPng from '@/assets/image/pay/item_pack.png';

import printPng from '@/assets/image/pay/print.png';
import printPackPng from '@/assets/image/pay/print_pack.png';

import shopPng from '@/assets/image/pay/shop.png';
import shopPackPng from '@/assets/image/pay/shop_pack.png';
import Pointer from '@/utils/pointTrack/constants';

export enum PAY_TYPE {
	平台商品数量 = 'PLATFORM_ITEM_NUM',
	存储时长 = 'DATA_STORAGE_TIME',
	打印订单数 = 'PRINT_TRADE_NUM',
	物流预警单量 = 'LOGISTIC_WARNING_NUM',
	最大店铺数 = 'SHOP_NUM',
}

export enum PAY_ITEM {
	// 商品
	PLATFORM_ITEM_20W = 'PLATFORM_ITEM_20W',
	PLATFORM_ITEM_NO_LIMIT = 'PLATFORM_ITEM_NO_LIMIT',
	// 数据
	DATA_STORAGE_6_MONTH = 'DATA_STORAGE_6_MONTH',
	DATA_STORAGE_12_MONTH = 'DATA_STORAGE_12_MONTH',
	DATA_STORAGE_24_MONTH = 'DATA_STORAGE_24_MONTH',
	// 打印
	PRINT_TRADE_NUM_NO_LIMIT = 'PRINT_TRADE_NUM_NO_LIMIT',
	// 物流预警
	LOGISTIC_WARNING_NUM_50W = 'LOGISTIC_WARNING_NUM_50W',
	LOGISTIC_WARNING_NUM_100W = 'LOGISTIC_WARNING_NUM_100W',
	// 店铺
	SHOP_NUM_1 = 'SHOP_NUM_1',
	SHOP_NUM_2 = 'SHOP_NUM_2',
	SHOP_NUM_3 = 'SHOP_NUM_3',
}

export enum PAY_CYCLE {
	EXPIRE_TIME = 100,
	ONE_YEAR = 2,
}

export const DISPLAY_MAP = {
	[PAY_TYPE.平台商品数量]: {
		title: '平台商品加量包',
		subTitle: '若您的平台资料数量已达上限，可订阅该加量包提高上限。',
		icon: itemPng,
		packIcon: itemPackPng,
		showPoint: Pointer.平台商品加量包_页面展现,
		clickPoint: Pointer.平台商品加量包_页面_点击立即订购,
		version: [{ title: '20万个', value: PAY_ITEM.PLATFORM_ITEM_20W }, { title: '不限额度', value: PAY_ITEM.PLATFORM_ITEM_NO_LIMIT }],
		cycle: [{ title: '同软件到期时间', value: PAY_CYCLE.EXPIRE_TIME }, { title: '1年', value: PAY_CYCLE.ONE_YEAR }]
	},
	[PAY_TYPE.存储时长]: {
		title: '数据存储加量包',
		subTitle: '若您需要延长订单存储时长，可订阅该加量包提高上限。涉及功能包括订单打印、底单查询、打印记录、发货记录。',
		icon: dataPng,
		packIcon: dataPackPng,
		showPoint: Pointer.存储时长加量包_页面展现,
		clickPoint: Pointer.存储时长加量包_页面_点击立即订购,
		version: [{ title: '6个月', value: PAY_ITEM.DATA_STORAGE_6_MONTH }, { title: '12个月', value: PAY_ITEM.DATA_STORAGE_12_MONTH }, { title: '24个月', value: PAY_ITEM.DATA_STORAGE_24_MONTH }],
		cycle: [{ title: '同软件到期时间', value: PAY_CYCLE.EXPIRE_TIME }, { title: '1年', value: PAY_CYCLE.ONE_YEAR }]
	},
	[PAY_TYPE.打印订单数]: {
		title: '打印订单数加量包',
		subTitle: '若您的打印订单数已达上限，可订阅该加量包提高上限。',
		icon: printPng,
		packIcon: printPackPng,
		showPoint: Pointer.打印订单数加量包_页面展现,
		clickPoint: Pointer.打印订单数加量包_页面_点击立即订购,
		version: [{ title: '不限单量', value: PAY_ITEM.PRINT_TRADE_NUM_NO_LIMIT }],
		cycle: [{ title: '同软件到期时间', value: PAY_CYCLE.EXPIRE_TIME }, { title: '1年', value: PAY_CYCLE.ONE_YEAR }]
	},
	[PAY_TYPE.物流预警单量]: {
		title: '物流预警加量包',
		subTitle: '若您的物流预警数已达上限，可订阅该加量包提高上限。',
		icon: warningPng,
		packIcon: warningPackPng,
		showPoint: Pointer.物流预警加量包_页面展现,
		clickPoint: Pointer.物流预警加量包_页面_点击立即订购,
		version: [{ title: '50万单', value: PAY_ITEM.LOGISTIC_WARNING_NUM_50W }, { title: '100万单', value: PAY_ITEM.LOGISTIC_WARNING_NUM_100W }],
		cycle: [{ title: '同软件到期时间', value: PAY_CYCLE.EXPIRE_TIME }]
	},
	[PAY_TYPE.最大店铺数]: {
		title: '店铺数加量包',
		subTitle: '若您的绑定店铺数已达上限，可订阅该加量包提高上限。',
		icon: shopPng,
		packIcon: shopPackPng,
		showPoint: Pointer.店铺数加量包_页面展现,
		clickPoint: Pointer.店铺数加量包_页面_点击立即订购,
		version: [{ title: '1个店铺', value: PAY_ITEM.SHOP_NUM_1 }, { title: '2个店铺', value: PAY_ITEM.SHOP_NUM_2 }, { title: '3个店铺', value: PAY_ITEM.SHOP_NUM_3 }],
		cycle: [{ title: '同软件到期时间', value: PAY_CYCLE.EXPIRE_TIME }]
	},
};

export const calculatePrice = (type: PAY_TYPE, version: PAY_ITEM, cycle: PAY_CYCLE, expireTime: string) => {
	// 计算剩余天数
	const now = new Date();
	const expireDate = new Date(expireTime);
	const remainingDays = dayjs(expireDate).diff(dayjs(), 'day');
	console.log('remainingDays', remainingDays);
	// 平台商品数量
	if (type === PAY_TYPE.平台商品数量) {
		if (version === PAY_ITEM.PLATFORM_ITEM_20W) {
			if (cycle === PAY_CYCLE.EXPIRE_TIME) {
				// 同软件到期时间
				if (remainingDays <= 365) {
					return 1000 * 100; // 转换为分
				} else {
					return Math.ceil(1000 * (remainingDays / 365)) * 100;
				}
			} else if (cycle === PAY_CYCLE.ONE_YEAR) {
				// 一年
				return 1000 * 100;
			}
		} else if (version === PAY_ITEM.PLATFORM_ITEM_NO_LIMIT) {
			if (cycle === PAY_CYCLE.EXPIRE_TIME) {
				// 同软件到期时间
				if (remainingDays <= 365) {
					return 3000 * 100;
				} else {
					return Math.ceil(3000 * (remainingDays / 365)) * 100;
				}
			} else if (cycle === PAY_CYCLE.ONE_YEAR) {
				// 一年
				return 3000 * 100;
			}
		}
	}
	
	// 存储时长
	if (type === PAY_TYPE.存储时长) {
		if (version === PAY_ITEM.DATA_STORAGE_6_MONTH) {
			if (cycle === PAY_CYCLE.EXPIRE_TIME) {
				if (remainingDays <= 365) {
					return 1000 * 100;
				} else {
					return Math.ceil(1000 * (remainingDays / 365)) * 100;
				}
			} else if (cycle === PAY_CYCLE.ONE_YEAR) {
				return 1000 * 100;
			}
		} else if (version === PAY_ITEM.DATA_STORAGE_12_MONTH) {
			if (cycle === PAY_CYCLE.EXPIRE_TIME) {
				if (remainingDays <= 365) {
					return 2000 * 100;
				} else {
					return Math.ceil(2000 * (remainingDays / 365)) * 100;
				}
			} else if (cycle === PAY_CYCLE.ONE_YEAR) {
				return 2000 * 100;
			}
		} else if (version === PAY_ITEM.DATA_STORAGE_24_MONTH) {
			if (cycle === PAY_CYCLE.EXPIRE_TIME) {
				if (remainingDays <= 365) {
					return 4000 * 100;
				} else {
					return Math.ceil(4000 * (remainingDays / 365)) * 100;
				}
			} else if (cycle === PAY_CYCLE.ONE_YEAR) {
				return 4000 * 100;
			}
		}
	}
	
	// 打印订单数
	if (type === PAY_TYPE.打印订单数) {
		if (version === PAY_ITEM.PRINT_TRADE_NUM_NO_LIMIT) {
			if (cycle === PAY_CYCLE.EXPIRE_TIME) {
				if (remainingDays <= 365) {
					return 3000 * 100;
				} else {
					return Math.ceil(3000 * (remainingDays / 365)) * 100;
				}
			} else if (cycle === PAY_CYCLE.ONE_YEAR) {
				return 3000 * 100;
			}
		}
	}
	
	// 物流预警单量
	if (type === PAY_TYPE.物流预警单量) {
		if (version === PAY_ITEM.LOGISTIC_WARNING_NUM_50W) {
			if (cycle === PAY_CYCLE.EXPIRE_TIME) {
				if (remainingDays <= 365) {
					return 1000 * 100;
				} else {
					return Math.ceil(1000 * (remainingDays / 365)) * 100;
				}
			}
		} else if (version === PAY_ITEM.LOGISTIC_WARNING_NUM_100W) {
			if (cycle === PAY_CYCLE.EXPIRE_TIME) {
				if (remainingDays <= 365) {
					return 2000 * 100;
				} else {
					return Math.ceil(2000 * (remainingDays / 365)) * 100;
				}
			}
		}
	}
	
	// 最大店铺数
	if (type === PAY_TYPE.最大店铺数) {
		if (version === PAY_ITEM.SHOP_NUM_1) {
			if (cycle === PAY_CYCLE.EXPIRE_TIME) {
				if (remainingDays <= 365) {
					return 800 * 100;
				} else {
					return Math.ceil(800 * (remainingDays / 365)) * 100;
				}
			}
		} else if (version === PAY_ITEM.SHOP_NUM_2) {
			if (cycle === PAY_CYCLE.EXPIRE_TIME) {
				if (remainingDays <= 365) {
					return 1600 * 100;
				} else {
					return Math.ceil(1600 * (remainingDays / 365)) * 100;
				}
			}
		} else if (version === PAY_ITEM.SHOP_NUM_3) {
			if (cycle === PAY_CYCLE.EXPIRE_TIME) {
				if (remainingDays <= 365) {
					return 2400 * 100;
				} else {
					return Math.ceil(2400 * (remainingDays / 365)) * 100;
				}
			}
		}
	}
	
	return 0;
};