import React, { useCallback, useEffect, useState } from 'react';
import { Modal } from 'antd';
import _ from 'lodash';
import { observer } from 'mobx-react';
import message from '@/components/message';
import ShowStyle from './ShowSetting';
import ExpressSetting from './ExpressSetting';
import PrintSetting from './PrintSetting';
import './index.scss';
import sendPoint from '@/utils/pointTrack/sendPoint';
import Pointer from '@/utils/pointTrack/constants';
import scanPrintStore from '@/stores/trade/scanPrint';
import ScanSetting from './ScanSetting';
import { ItemTakeGoodsLabelConfigEditApi } from '@/apis/trade/takeGoodsLabel';
import { IScanConfig } from '@/types/trade/takeGoodsLabel';
import { defaultScanConfig } from '../../constants';
import { getCitiesAndAreas, getSelValueByAreaJson } from '@/pages/Trade/components/SearchContainer/AddressSelect/addOrUpdateAddressItemModal';
import { getTreeShapeAddress, TreeShapeAddress } from '@/pages/Trade/components/SearchContainer/AddressSelect/addressUtils';
import { CHINA_ID } from '@/constants';
import ScanTips from './ScanTips';
import timer, { logTypeEnum } from '@/libs/timer';

interface IScanPrintSettingProps {
}

const ScanPrintSetting = observer((props: IScanPrintSettingProps) => {
	const { scanPrintSetting, 
		setIsShowScanPrintSettingModal,
		 setScanPrintSetting,
		 videoMonitorState,
		 setVideoMonitorState,
		 } = scanPrintStore;
	const [setting, setSetting] = useState<IScanConfig>(null);

	const [treeShapeAddress, setTreeShapeAddress] = useState<Array<TreeShapeAddress>>([]);

	const divStyle: React.CSSProperties = {
		position: 'relative',
		paddingTop: 0,
		paddingBottom: 0
	};

	useEffect(() => {
		getTreeShapeAddress(CHINA_ID).then((res) => {
			setTreeShapeAddress(res);
		});
	}, []);

	useEffect(() => {
		if (scanPrintSetting?.scanSet) {
			const _scanSet = { ...scanPrintSetting?.scanSet };
			_scanSet.memoOrMessageKeyWordsNoAutoPrintVal = (_scanSet.memoOrMessageKeyWordsNoAutoPrintVal || '').split(',').join('\n');
			setSetting(_scanSet as IScanConfig);
		} else if (scanPrintSetting) {
			scanPrintSetting.scanSet = defaultScanConfig;
			setSetting(scanPrintSetting.scanSet);
		}
	}, [scanPrintSetting]);

	const getMessageString = (val:string) => {
		const validValue = val.trim();
		const arr = validValue.split('\n');
		const maxInputLength = 1000;
		const maxInputNum = 100;
		// 校验长度是否合法
		if (validValue.length > maxInputLength) {
			message.error(`最多输入${maxInputLength}个字数，请重新输入`);
			return false;
		}
		// 校验数量是否合
		if (arr.length > maxInputNum) {
			message.error(`当前输入${arr.length}条，最大添加数量${maxInputNum}条`);
			return false;
		}
		const v = arr.join(',');
		return v;
	};
	
	// 保存
	const handleConfirm = () => {
		
		// scanPrintSetting
		const _setting = { ...setting };
		// !! 这里添加两条ES日志
		// 已打印不自动打印操作变动
		if (_setting.printedKddNoAutoPrint !== scanPrintSetting.scanSet.printedKddNoAutoPrint) {
			const isOpen = _setting.printedKddNoAutoPrint == 1;
			timer.start('已打印不自动打印操作变动');
			timer.stop(`已打印不自动打印操作变动`, {
				type: logTypeEnum['小标签'],
				desc: `已打印不自动打印操作变动:${isOpen ? '关闭->开启' : '开启->关闭'}`
			});
			console.log(`已打印不自动打印操作变动:${isOpen ? '关闭->开启' : '开启->关闭'}`);
		}
		// 已发货不自动打印操作变动
		if (_setting.alreadySendNoAutoPrint !== scanPrintSetting.scanSet.alreadySendNoAutoPrint) {
			const isOpen = _setting.alreadySendNoAutoPrint == 1;
			timer.start('已发货不自动打印操作变动');
			timer.stop(`已发货不自动打印操作变动`, {
				type: logTypeEnum['小标签'],
				desc: `已发货不自动打印操作变动:${isOpen ? '关闭->开启' : '开启->关闭'}`
			});
			console.log(`已发货不自动打印操作变动:${isOpen ? '关闭->开启' : '开启->关闭'}`);
		}


		
		if (_setting.memoOrMessageKeyWordsNoAutoPrintVal) {
			const messageString = getMessageString(_setting.memoOrMessageKeyWordsNoAutoPrintVal);
			if (messageString) {
				_setting.memoOrMessageKeyWordsNoAutoPrintVal = messageString;
			} else {
				return;
			}
		}
		//  else {
		// 	_setting.memoOrMessageKeyWordsNoAutoPrintVal = "";
		// }

		if (_setting.mixScanMode !== scanPrintSetting.scanSet.mixScanMode && _setting.mixScanMode) {
			sendPoint(Pointer.开启混扫);
		}

		if (_setting.selectedProvinceNoAutoPrint && _setting.selectIds) {
			const {
				areaJson
			} = getCitiesAndAreas(_setting.selectIds, treeShapeAddress);
			_setting.areaJson = areaJson;
		}
		delete _setting.selectIds;

		let params = {
			...scanPrintSetting,
			scanSet: JSON.stringify(_setting),
			subUserScanSet: {
				...scanPrintSetting?.subUserScanSet,
				videoMonitor: _setting.videoMonitor ? 1 : 0,
			},
		};
		console.log('params', params);
		ItemTakeGoodsLabelConfigEditApi({ ...params }).then(res => {
			setScanPrintSetting({
				...scanPrintSetting,
				scanSet: _setting,
			});
			setVideoMonitorState(!!_setting.videoMonitor);
			setIsShowScanPrintSettingModal(false);
		});
	};
	
	/**
     * change事件
     * @param e type: string/number时，取值用
     * @param key 字段名
     */
	const handleInputChange = (e: any, key: string) => {
		// console.log(e, key, 'handleInputChange');
		
		// 如果是勾选状态，才触发打点
		if (e.target?.checked) {
			switch (key) {
				case 'isHidePendingTrade':
					sendPoint(Pointer.订单_订单打印_高级设置_显示样式_隐藏挂起订单);
					break;
				case 'isHidePartShipTrade':
					sendPoint(Pointer.订单_订单打印_高级设置_显示样式_隐藏已发货子订单隐藏);
					break;
				case 'isHideRefundTrade':
					sendPoint(Pointer.订单_订单打印_高级设置_显示样式_订单中全部商品全部退款时隐藏);
					break;
				// case 'showEpidemicArea':
				// 	sendPoint(Pointer.订单_订单打印_高级设置_显示样式_疫情提醒);
				// 	break;
				case 'addYdNoMemo':
					sendPoint(Pointer.订单_订单打印_高级设置_打印设置_打印后自动追加快递单号到备注);
					break;
				case 'filterOrder':
					sendPoint(Pointer.订单_订单打印_高级设置_个性化设置_开启宝贝筛选);
					break;
				default:
					break;
			}
		}

		let newSetting = _.cloneDeep(setting);
		if (key === 'memoOrMessageKeyWordsNoAutoPrintVal') { // Textarea
			newSetting[key] = e.target.value;
		} else if (key === 'noAutoPrintFlags' || key === 'selectIds') { // Checkbox group
			newSetting[key] = e;
		} else if (['buyerMessageFlagShowStyle'].includes(key)) { // Radio   留言备注旗帜展示方式
			newSetting[key] = e.target.value;
		} else if (['videoMonitor'].includes(key)) { 
			newSetting[key] = e ? 1 : 0;
			setVideoMonitorState(!!e);
		} else { // Checkbox
			newSetting[key] = e.target.checked ? 1 : 0;
		}
		setSetting(newSetting);
	};

	return (
		<Modal
			centered
			visible
			title="扫描设置"
			width={ 640 }
			okText="保存"
			bodyStyle={ divStyle }
			onOk={ handleConfirm }
			onCancel={ () => { setIsShowScanPrintSettingModal(false); } }
			maskClosable={ false }
			destroyOnClose
		>
			<div className="contentBox">
				<div className="setContent">
					{setting && (
						<>
							<ShowStyle setting={ setting } handleInputChange={ handleInputChange } />
							<ExpressSetting setting={ setting } handleInputChange={ handleInputChange } />
							<PrintSetting setting={ setting } handleInputChange={ handleInputChange } />
							<ScanTips setSetting={ setSetting } setting={ setting } handleInputChange={ handleInputChange } />
							<ScanSetting setting={ setting } treeShapeAddress={ treeShapeAddress } handleInputChange={ handleInputChange } />
						</>
					)}
				</div>
			</div>
		</Modal>
	);
});

export default ScanPrintSetting;
