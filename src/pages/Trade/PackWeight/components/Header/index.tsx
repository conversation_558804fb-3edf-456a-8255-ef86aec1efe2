import { Button, Input, Modal } from "antd";
import dayjs from "dayjs";
import { observer } from "mobx-react";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { QueryInspectionShippingOrders<PERSON>pi, SaveWeighing<PERSON><PERSON>ordA<PERSON>, UpdateWeighing<PERSON><PERSON>ord<PERSON><PERSON> } from "@/apis/trade/search";
import voiceSendSuccess from '@/assets/mp3/发货成功.mp3';
import voiceHandleFail from '@/assets/mp3/处理失败.mp3';
import voiceScan from '@/assets/mp3/扫描.mp3';
import voiceScanRepeat from '@/assets/mp3/重复扫描.mp3';
import IconHandleRender from "@/components/Icon/custom-icon";
import message from '@/components/message';
import event from "@/libs/event";
import memoFn from "@/libs/memorizeFn";
import { playAudio, playAudioAsync, textTransformAudioPlay } from "@/pages/Trade/ScanPrint/utils";
import { sendDelivery } from "@/pages/Trade/components/BottomCom/utils";
import { calcPackTotalNum } from "@/pages/Trade/utils";
import { tradeStore } from "@/stores";
import packWeightStore from "@/stores/trade/packWeight";
import tradeSetStore from "@/stores/trade/tradeSet";
import { TradePrintContentSetRule } from "@/types/trade/tradeSet";
import { getMirrorEnum } from "@/utils";
import Pointer from "@/utils/pointTrack/constants";
import sendPoint from "@/utils/pointTrack/sendPoint";
import { genePrintContent } from "@/utils/trade/printContent";
import { TRADE_SEND_STATUS, WeighSendEventBus } from "../../constants";
import { IWsObj, connectSocket, getValLen } from "../../utils";
import styles from "./index.module.scss";
import { formatWeightDisplay } from '@/components/Input/InputNumber/WeightInput';
import videoMonitorStore from "@/stores/trade/videoMonitor";

let weighRecordId = '';
const MAX_INPUT_WEIGHT = 99999;
let wsObj: IWsObj;
let tradeList = [];
let theoreticalWeight = 0;
const Header = () => {
	const {
		expressTemplateList,
	} = tradeStore;

	const {
		setTheoreticalWeight,
		advancedSettingConfig,
		setAdvancedSettingModalVisible,
		setTradeList,
		getAdvancedSettingConfig,
		showLoading,
		setShowLoading,
		sendPending, setSendPending,
		initStore,
		videoMonitorState,
		selectedShootingDevices,
		setSelectedShootingDevices,
		nvrDevices, setNvrDevices
	} = packWeightStore;
	const { 
		clearVideoMonitor,
		startVideoMonitor,
		videoMonitor
	} = videoMonitorStore;
	getMirrorEnum(TRADE_SEND_STATUS);
	const weightInputRef = useRef();
	const expressInputRef = useRef();
	const [expressNo, setExpressNo] = useState('');
	const [weightNum, setWeightNum] = useState('');
	const [templateObj, setTemplateObj] = useState({});
	// const isKg = userStore?.userSetting?.weightUnit == weightUnit.显示kg;
	const isKg = true;

	const customLogPost = (dataType: string, data:any = {}) => {
		window.errorCollection?.customMessageUpload({
			type: `包裹称重【${dataType}】`,
			data: {
				advancedSettingConfig,
				sendPending,
				expressNo,
				weightNum,
				templateObj,
				...data
			}
		});
	};

	const expressInputFocus = () => {
		expressInputRef?.current?.focus();
		expressInputRef?.current?.select();
	};

	const weightInputFocus = () => {
		weightInputRef?.current?.focus();
		weightInputRef?.current?.select();
	};

	const getPrintContent = async(packList) => {
		// * 生成打印内容
		let printContent : TradePrintContentSetRule = null;
		let keyWords:string[] = null;
		let advancedSetRes;
		try {
			printContent = await tradeSetStore.getPrintContentSet();
			const filterWord = await tradeSetStore.getFilterWord();
			keyWords = filterWord.map(item => item.delKey);
			advancedSetRes = await memoFn.getAdvancedSet();
		} catch (error) {
			console.error('获取发货内容设置失败', error);
			customLogPost('获取发货内容设置失败', { error, packList });
		}
		packList.forEach(pack => {
			pack.printContent = genePrintContent(pack, printContent, keyWords, advancedSetRes);
		});
	};

	const handleRes = async(packList) => {
		/**
		 * 原订单数据处理注意事项：
		 * 1.把不属于当前快递单的order全部过滤掉，只保留快递单当时申请时对应的order
		 * 2.对过滤后的order需要做发货状态校验和售后状态校验
		 * 3.过滤后的order只要有一个是已发货或者有售后状态，就要提示
		 * 4.pack和order需要加上isChecked = true
		 * 5.pack需要加上expressTemplate和sids，发货需要
		 * 6.把所有的货品找出来，如果用户扫描条码或者货号的时候，出现了多个不同的货品，需要弹窗让用户选择
		 * 7.根据order的发货状态，显示主订单的发货状态
		 * 8.如果只扫描到一笔订单，默认展开卡片，如果扫描到多笔订单，已发货的订单不默认展开
		 */
		const orderList = [];
		let splitSend = false; // 是否是拆单发货
		let totalWeight = 0;

		packList.forEach(pack => {
			pack["isActive"] = true;
			// 勾选上，发货需要
			pack["isChecked"] = true;
			// 给pack加上expressTemplate和sids属性，发货需要
			const expressTemplateInfo = {};

			pack.trades.forEach(trade => {
				const newOrders = [];
				// 已发货订单
				const allSendOrders = [];
				// 待发货订单
				const needSendOrders = [];

				trade.orders.forEach(order => {
					// 对应注意事项1
					if (order.inspectionShippingOrder) {
						// 有售后
						if (!["NOT_REFUND"].includes(order.refundStatus)) {
							pack.hasBeenAfterSale = true;
						}
						// 待发货
						if (["WAIT_SELLER_SEND_GOODS"].includes(order.status)) {
							needSendOrders.push(order);
						}
						// 已发货
						if (["WAIT_BUYER_CONFIRM_GOODS", "SELLER_CONSIGNED_PART"].includes(order.status)) {
							if (packList.length > 1) {
								pack["isActive"] = false;
							}
							pack.hasBeenSend = true;
							allSendOrders.push(order);
						}
						const templateInfo = templateObj[order.templateId] || {};
						expressTemplateInfo["exId"] = order.templateId;
						expressTemplateInfo["exCode"] = order.inspectionExCode;
						expressTemplateInfo["kddType"] = order.inspectionKddType;
						expressTemplateInfo["exName"] = templateInfo.ExcodeName;
						pack["expressTemplate"] = expressTemplateInfo;
						pack["sids"] = [order.inspectionExNumber];
						newOrders.push({
							...order,
							templateInfo,
							isChecked: true, // 这里必须需要给order设置isChecked。因为其他很多公共方法都有判断
						});
						orderList.push(order);
						if (order.weight) {
							totalWeight += +order.weight;
						}
					} else { // 只要有一个order不属于当前快递单，就说明是要拆单发货
						splitSend = true;
					}
				});
				// 赋值pack，好显示订单是不是已发货
				if (trade.status === 'SELLER_CONSIGNED_PART') {
					pack["sendStatus"] = TRADE_SEND_STATUS["部分发货"];
				} else if (allSendOrders.length && allSendOrders.length === newOrders.length) {
					pack["sendStatus"] = TRADE_SEND_STATUS["已发货"];
				} else if (needSendOrders.length && needSendOrders.length === newOrders.length) {
					pack["sendStatus"] = TRADE_SEND_STATUS["待发货"];
				}
				trade.splitSend = splitSend;
				trade.orders = newOrders;
			 });

			calcPackTotalNum(pack);
		});
		packList.sort((a, b) => { return +new Date(a.payTime) - +new Date(b.payTime); });
		await getPrintContent(packList);
		setTradeList(packList);
		tradeList = packList;
		setTheoreticalWeight(totalWeight);
		theoreticalWeight = totalWeight;
		actionNext();
	};

	const expressNoOnChange = (e) => {
		setExpressNo(e.target.value);
	};

	const weightNumOnBlur = (e) => {
		let val = e.target.value;
		if (val == '') {
			return;
		}
		const len = getValLen(val);
		if (len < 7) {
			setWeightNum(formatWeightDisplay(isKg, val * 1000) as string);
		}
	};

	const weightNumOnChange = (e) => {
		if (e.target.value == '') {
			setWeightNum('');
			return;
		}
		let val = e.target.value;
		const len = getValLen(val);
		if (len >= 7) {
			return;
		}
		if (val >= 0 && val <= MAX_INPUT_WEIGHT) {
			setWeightNum(String(val));
		} else if (val > MAX_INPUT_WEIGHT) {
			setWeightNum(String(MAX_INPUT_WEIGHT));
		}
	};
	const onSearchTradeByExpressNo = async() => {
		if (sendPending) {
			return;
		}
		const isIpcModel = selectedShootingDevices?.length > 0; 
		const isVideoMonitor = videoMonitor && videoMonitorState;
		clearVideoMonitor(isVideoMonitor, isIpcModel);
		if (tradeList?.[0]?.sids?.includes(expressNo)) {
			playAudio(voiceScanRepeat);
			return;
		}
		if (expressNo) {
			initStore();
			const params = {
				sid: expressNo,
				startTime: dayjs().subtract(1, 'M').startOf('day').format("YYYY-MM-DD HH:mm:ss"),
				endTime: dayjs().endOf("day").format("YYYY-MM-DD HH:mm:ss"),
				pageNo: 1,
				pageSize: 200
			};
			setShowLoading(true);
			try {
				const res = await QueryInspectionShippingOrdersApi(params);
				if (res?.data?.list?.length > 0) {
					playAudio(voiceScan);
					await handleRes(res.data.list);

					const outSid = expressNo?.trim();
					const texts = [`快递单号：${outSid}`];
					const isKd = true;
					const data = {
						outSid,
						orderList: res.data.list,
						isIpcModel,
						isVideoMonitor,
						nvrDevices,
						selectedShootingDevices,
						texts,
						isKd
					};
					// 开启监控
					startVideoMonitor(data, 4);
			
				} else {
					tradeList = [];
					expressInputFocus();
					message.error("未查询到订单");
					customLogPost('未查询到订单', { params });
				}
			} catch (error) {
				tradeList = [];
				playAudio(voiceHandleFail);
				customLogPost('处理失败', { error });
			}
			setShowLoading(false);
		}

	};


	const shippingInitiated = () => {
		setSendPending(true);
		sendDelivery({
			isSendAbnormal: true,
			type: 'weighSend',
			scanPrintList: tradeList,
			noTemplate: true,
		});
		customLogPost('触发发货', { tradeList });
	};

	// 重量提示弹窗
	const checkTip = () => {
		return new Promise((resolve, reject) => {
			if (advancedSettingConfig?.isWeightOnly) {
				Modal.warn({
					title: "校验订单重量异常",
					content: '包裹实际重量与订单理论重量不符，请仔细确认',
					onOk: () => reject(),
					okText: '知道了'
				});
				textTransformAudioPlay("重量异常");
				customLogPost('包裹实际重量与订单理论重量不符，请仔细确认', { transWeight });
			} else {
				Modal.confirm({
					title: "校验订单重量异常",
					content: '包裹实际重量与订单理论重量不符，请确认是否发货',
					onOk: () => {
						resolve(true);
						customLogPost('包裹实际重量与订单理论重量不符，强制发货', { transWeight });
					},
					onCancel: () => {
						reject();
						customLogPost('包裹实际重量与订单理论重量不符，取消发货', { transWeight });
					},
					cancelText: "取消",
					okText: "强制发货"
				});
				textTransformAudioPlay("重量异常");
				customLogPost('包裹实际重量与订单理论重量不符，请确认是否发货', { transWeight });
			}
		});
	};

	const transWeight = useMemo(() => {
		return isKg ? +weightNum * 1000 : +weightNum;
	}, [weightNum, isKg]);

	// 校验重量
	const checkWeightNum = async() => {
		if (advancedSettingConfig?.isWeightCheck) {
			const { widthDownNum = 0, widthUpNum = 0, widthDownRate, widthUpRate, weightCheckType } = advancedSettingConfig;
			let upNum = theoreticalWeight + widthUpNum;
			let downNum = theoreticalWeight - widthDownNum;
			if (weightCheckType === 'weightRate') {
				upNum = theoreticalWeight + theoreticalWeight * widthUpRate * 0.01;
				downNum = theoreticalWeight - theoreticalWeight * widthDownRate * 0.01;
			}
			console.log('downNum: ', upNum, downNum, transWeight);
			if (+transWeight > upNum || +transWeight < downNum) {
				await checkTip();
			}
		}
	};

	// 处理称重
	const handleWeigh = async(hasMessage = false) => {
		customLogPost('处理称重', { hasMessage });
		await checkWeightNum();
		const params = {
			weight: transWeight,
			exNumber: expressNo,
			weightShipFlag: 0,
			orderIds: tradeList?.[0]?.togetherId,
		};
		const res = await SaveWeighingRecordApi(params);
		weighRecordId = res;
		if (hasMessage) {
			message.success('称重完成');
			weightInputFocus();
		}
	};

	const checkTradeFail = () => {
		const isAfterSale = tradeList.some(pack => pack?.hasBeenAfterSale);
		if (isAfterSale) {
			textTransformAudioPlay("有退款");
			message.warning("重量已记录，快递单对应的订单已处理售后状态，请前往订单打印页面处理发货");
			customLogPost('重量已记录，快递单对应的订单已处理售后状态，请前往订单打印页面处理发货', { tradeList: tradeList?.filter(pack => pack?.hasBeenAfterSale) });
			return true;
		}
		const isAllSend = tradeList.every(pack => [TRADE_SEND_STATUS["发货成功"], TRADE_SEND_STATUS["已发货"]].includes(pack?.sendStatus));
		if (isAllSend) {
			textTransformAudioPlay("有异常");
			message.warning("重量已记录，该快递单对应的订单已发货，无需再次发货");
			customLogPost('重量已记录，该快递单对应的订单已发货，无需再次发货', { tradeList });
			return true;
		}
		const isPartSend = tradeList.some(pack => [TRADE_SEND_STATUS["发货成功"], TRADE_SEND_STATUS["已发货"], TRADE_SEND_STATUS["部分发货"]].includes(pack?.sendStatus));
		if (isPartSend) {
			textTransformAudioPlay("有异常");
			message.warning("重量已记录，该快递单对应的订单部分商品已发货，请前往订单打印页面处理发货");
			customLogPost('重量已记录，该快递单对应的订单部分商品已发货，请前往订单打印页面处理发货', { tradeList: tradeList.filter(pack => [TRADE_SEND_STATUS["发货成功"], TRADE_SEND_STATUS["已发货"], TRADE_SEND_STATUS["部分发货"]].includes(pack?.sendStatus)) });
			return true;
		}
		return false;
	};

	const willSend = async() => {
		if (sendPending) {
			return;
		}
		await handleWeigh();
		if (checkTradeFail()) {
			return;
		}
		shippingInitiated();
	};

	const updateWeightStatus = async() => {
		await UpdateWeighingRecordApi({ id: weighRecordId });
	};

	const handleSendRes = (res) => {
		/**
		 * 单笔发货的时候触发提示音效
		 * 多笔发货暂时不考虑
		 */
		setSendPending(false);
		const togetherId2SendRes = {}; // 把togetherId对应的发货结果保存起来
		weightInputFocus();
		if (res?.length === 1) {
			const sendInfoRes = res[0];
			if (sendInfoRes.result == 103) {
				playAudioAsync(voiceHandleFail, 500);
			} else {
				setWeightNum('');
				setExpressNo('');
				playAudioAsync(voiceSendSuccess, 500);
			}
		}

		res.forEach(item => {
			togetherId2SendRes[item.togetherId] = item;
		});

		if (res?.length > 0) {
			const tradeListTemp = [...tradeList];
			tradeListTemp.forEach(pack => {
				const curTradeSendRes = togetherId2SendRes[pack.togetherId];
				// 100发货成功，101, 部分发货成功，102 拆单发货成功，103发货失败
				if (curTradeSendRes?.result != 103) {
					pack['sendStatus'] = TRADE_SEND_STATUS["发货成功"];
					updateWeightStatus();
				} else {
					const errorMsg = curTradeSendRes?.subTradeInfos[0]?.message;
					pack["sendFailMsg"] = errorMsg;
					pack['sendStatus'] = TRADE_SEND_STATUS["发货失败"];
				}
			});
			setTradeList(tradeListTemp);
		}
	};

	// 不合格的重量
	const isInvalidWeight = () => {
		if (weightNum == '') {
			message.info('请先输入重量后进行记录');
			return true;
		}
		if (+weightNum <= 0 || +weightNum > MAX_INPUT_WEIGHT) {
			message.info(`重量仅限正数（0-${MAX_INPUT_WEIGHT}）`);
			return true;
		}
		return false;
	};

	const weightEnter = () => {
		if (isInvalidWeight()) return;
		wsObj?.wsHandler?.close();
		setTimeout(expressInputFocus);
	};

	const actionNext = () => {
		if (isInvalidWeight()) return;
		if (advancedSettingConfig?.isWeightOnly) { // 仅称重不发货
			handleWeigh(true);
		} else {
			willSend();
		}
	};

	const handleNext = () => {
		sendPoint(Pointer.包裹称重_称重发货);
		if (tradeList[0]?.sids.includes(expressNo)) {
			actionNext();
		} else {
			onSearchTradeByExpressNo();
			customLogPost('发货，扫描快递单号');
		}
	};

	const weightFocus = () => {
		let timeWeight = { };
		wsObj = {
			wsHandler: null,
			comWeight: null,
			weightChange: (data) => {
				setWeightNum(data);
				if (+data == 0) return;
				if (timeWeight[data]) {
					// 称重器录入重量3秒后，重量值不产生任何变化，鼠标光标自动跳转到扫描怏递单号输入框内
					if (+new Date() > timeWeight[data] + 3000) {
						wsObj.wsHandler?.close();
						expressInputFocus();
					}
				} else {
					timeWeight = { [data]: +new Date() };
				}
			}
		};
		connectSocket(wsObj);
	};

	useEffect(() => {
		const templateObj = {};
		expressTemplateList.forEach(i => {
			templateObj[i.Mode_ListShowId] = i;
		});
		setTemplateObj(templateObj);
	}, [expressTemplateList]);

	// 主要处理发货结果

	useEffect(() => {
		event.on(WeighSendEventBus.HANDLE_SEND_RES, handleSendRes);
		return () => {
			event.off(WeighSendEventBus.HANDLE_SEND_RES, handleSendRes);
		};
	}, [tradeList]);

	// 监听是否已经全部验货完毕


	useEffect(() => {
		initStore();
		getAdvancedSettingConfig();
		return () => {
			wsObj?.wsHandler?.close();
		};
	}, []);
	return (
		<div className={ styles["search-wrap"] }>
			<div>
				<div>
					<Input
						ref={ weightInputRef }
						value={ weightNum }
						placeholder={ `包裹重量(${isKg ? "kg" : "g"})` }
						allowClear
						onBlur={ weightNumOnBlur }
						suffix={ weightNum ? (isKg ? "kg" : "g") : null }
						onChange={ weightNumOnChange }
						onPressEnter={ weightEnter }
						onFocus={ weightFocus }
					/>
				</div>
				<div>
					<Input
						ref={ expressInputRef }
						value={ expressNo }
						placeholder="扫描快递单号"
						allowClear
						onChange={ expressNoOnChange }
						onPressEnter={ onSearchTradeByExpressNo }
					/>
				</div>
				<div>
					<Button
						type="primary"
						onClick={ handleNext }
						loading={ sendPending || showLoading }
					>
						{advancedSettingConfig?.isWeightOnly
							? "包裹称重"
							: "称重发货"}
					</Button>
				</div>
			</div>
			<div>
				<IconHandleRender
					onClick={ () => {
						sendPoint(Pointer.包裹称重_高级设置);
						setAdvancedSettingModalVisible(true);
					} }
					text="高级设置"
					type="icon-gaojishezhi"
					hoverType="icon-gaojishezhi_fill"
				/>
			</div>
		</div>
	);

};
export default observer(Header);
