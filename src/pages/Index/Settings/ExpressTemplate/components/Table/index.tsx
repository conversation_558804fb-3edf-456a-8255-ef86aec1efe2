import React, { useEffect, useState, useImperativeHandle, useMemo, useCallback } from "react";
import { Button, Popover, Table, Input, Form, message } from "antd";
import { PlusCircleFilled, MinusCircleFilled } from '@ant-design/icons';
import { cloneDeep, differenceBy, isEqual, template } from "lodash";
import cs from 'classnames';
import { observer } from "mobx-react-lite";
import styles from './index.module.scss';
import useGetState from "@/utils/hooks/useGetState";
import memoFn from '@/libs/memorizeFn';
import InputPrice from '@/components/Input/InputNumber/InputPrice';
import SelectExpressModal from '../SelectExpressModal';
import SelectAreaModal from "../SelectAreaModal";
import SelectTemplate from "@/components/SelectTemplate";
import BaseCheckGroupTable from "@/components/SearchTable/BaseCheckGroupTable";

const maxWeightStr = "无穷大";
const maxWeightNum = 99999;
const defaultTemplate = [
	{
		logisticsId: '',
		pricingType: 2,
		key: 0,
		detailList: [
			{
				addressArray: "ALL",
				key: 0,
				freightList: JSON.stringify([{
					key: 0,
					leftNum: 0,
					rightNum: maxWeightStr,
					freightNum: "",
					freightPrice: "",
					everyNum: "",
					everyPrice: "" }]),
			}
		]
	}
];

let uniqueExpressIncrementKey = 0;
let uniqueAreaIncrementKey = 0;
let uniqueWeightIncrementKey = 0;
const TempTable = (props) => {
	// * showVirtualTable 根据是否展示虚拟表格做区分
	const { type, cRef, data, loading, disabledExpressCodeList = [], expressSelectedTemplateListObj = {}, tableKey, showVirtualTable = false } = props;
	const [form] = Form.useForm();
	const [validDisabledExpressCodeList, setValidDisabledExpressCodeList] = useState([]);
	const [disabledAddressList, setDisabledAddressList] = useState([]);
	const [selectedExpressCode, setSelectedExpressCode] = useState('');
	const [allExpressList, setAllExpressList, getAllExpressList] = useGetState([]);
	const [dataSource, setDataSource, getDataSource] = useGetState([]);
	const [curEditArea, setCurEditArea] = useState([]);
	const [needDeleteDetailIds, setNeedDeleteDetailIds, getNeedDeleteDetailIds] = useGetState([]);
	const [templateOriginData, setTemplateOriginData, getTemplateOriginData] = useGetState([]);
	const [expressModalVisible, setExpressModalVisible] = useState(false);
	const [selectAreaModalVisible, setSelectAreaModalVisible] = useState(false);
	const [needDeletePrintTemplateIds, setNeedDeletePrintTemplateIds, getNeedDeletePrintTemplateIds] = useGetState([]);
	const [selectTemplateModalVisible, setSelectTemplateModalVisible] = useState(false);
	const [selectTemplateModalTitle, setSelectTemplateModalTitle] = useState("");
	const [selectedTemplateList, setSelectedTemplateList] = useState([]);
	const [disabledSelectedTemplateList, setDisabledSelectedTemplateList] = useState([]);
	const [curOptRow, setCurOptRow] = useState<{[k:string]:any}>({});
	const onOptRow = (record, type) => {
		const { rowKey, lastRowKey } = record;
		const theTemplateOriginData = cloneDeep(getTemplateOriginData());
		theTemplateOriginData.forEach(expressItem => {
			const { detailList } = expressItem;
			// 1.找出当前操作行对应的快递公司
			if (expressItem.key === record.expressKey) {
				detailList.forEach(detailItem => {
					// 2.找出当前操作行对应快递公司下对应的省份
					if (detailItem.key === record.areaKey) {
						const freightListObj = JSON.parse(detailItem.freightList);
						const curRowRightNum = form.getFieldValue(`${rowKey}@right`);
						if (type === 'plus') {
							uniqueWeightIncrementKey += 1;
							let addFreightItem = {
								rightNum: maxWeightStr,
								key: uniqueWeightIncrementKey
							};
							if (curRowRightNum == maxWeightStr) {
								form.setFieldsValue({ [`${rowKey}@right`]: "" });
							} else {
								addFreightItem["leftNum"] = curRowRightNum;
							}
							freightListObj.push(addFreightItem);
						}
						if (type === "minus") {
							freightListObj.splice(-1, 1);
							// 减去一条规则时右值必须是最大值
							setTimeout(() => {
								form.setFieldsValue({ [`${lastRowKey}@right`]: maxWeightStr });
							});
						}
						detailItem.freightList = JSON.stringify(freightListObj);
					}
				});
			}
		});
		setTemplateOriginData(theTemplateOriginData);

	};

	const onLeftChange = (v, record) => {
		const { lastRowKey } = record;
		if (lastRowKey) {
			form.setFieldsValue({ [`${lastRowKey}@right`]: v });

			if (showVirtualTable) {
				let index = getDataSource().findIndex(i => i.rowKey === lastRowKey);
				
				setDataSource(prev => {
					const newDataSource = [...prev];
					newDataSource[index] = {
						...newDataSource[index],
						[`${lastRowKey}@right`]: v
					};
					return newDataSource;
				});
			}
		}
	};

	const onRightChange = (v, record) => {
		const { nextRowKey } = record;
		if (nextRowKey) {
			form.setFieldsValue({ [`${nextRowKey}@left`]: v });

			if (showVirtualTable) {
				let index = getDataSource().findIndex(i => i.rowKey === nextRowKey);
				  
				   setDataSource(prev => {
					const newDataSource = [...prev];
					newDataSource[index] = {
						...newDataSource[index],
						[`${nextRowKey}@left`]: v
					};
					return newDataSource;
				});
			}
		}
	};

	// 点击编辑物流公司
	const onEditExpress = (record) => {
		setCurOptRow(record);
		setSelectedExpressCode(record.logisticsId); // 设置勾选的物流公司
		setExpressModalVisible(true);
	};

	const handleDisabledSelectedTemplateList = (row, curRowSelectedTemplateList) => {
		// 找出当前公司已经选择过的快递模板
		let curExpressSelectedTemplateList = [];
		if (expressSelectedTemplateListObj) {
			curExpressSelectedTemplateList = expressSelectedTemplateListObj[row.logisticsId] || [];
		}
		// 如果是type是新建，那么当前快递公司选择过的模板都禁用
		if (type === 'add') {
			setDisabledSelectedTemplateList(curExpressSelectedTemplateList); // 设置禁用的快递模板
		} else if (type === 'edit') { // 如果是type是编辑，那么除了当前行以外的行选择过的模板都禁用
			curExpressSelectedTemplateList = curExpressSelectedTemplateList.filter(templateId => !curRowSelectedTemplateList.includes(templateId));
			setDisabledSelectedTemplateList(curExpressSelectedTemplateList); // 设置禁用的快递模板
		}


	};

	// 点击编辑模板
	const onEditTemplate = (record) => {
		if (!record.logisticsId) {
			message.error("请先选择快递公司");
			return;
		}
		setCurOptRow(record);
		const selectedExpressInfo = record?.expressList[0];
		const selectTemplateModalTitle = `选择【${selectedExpressInfo?.exName || ""}】快递模板`;
		const selectedTemplateList = record.printTemplateList.map(template => template.printTemplateId);
		setSelectedExpressCode(record.logisticsId); // 设置勾选的物流公司
		setSelectTemplateModalTitle(selectTemplateModalTitle);
		setSelectTemplateModalVisible(true);
		setSelectedTemplateList(selectedTemplateList); // 设置已选择的快递模板
		handleDisabledSelectedTemplateList(record, selectedTemplateList);
	};

	const weightLeftRules = (record) => {
		const { lastRowKey } = record;
		return (
			[
				({ getFieldValue }) => ({
					validator(_, value) {
						let curRowRight = getFieldValue(`${record.rowKey}@right`);
						if (curRowRight === maxWeightStr) {
							curRowRight = maxWeightNum;
						}
						if (["", null, undefined].includes(value)) {
							return Promise.reject(new Error("不能为空"));
						}
						if (Number(value) === Number(maxWeightNum)) {
							return Promise.reject(new Error(`不能等于最大值${maxWeightNum}`));
						}
						if (lastRowKey) {
							let lastRowRightValue = getFieldValue(`${lastRowKey}@right`);
							if (lastRowRightValue === maxWeightStr) {
								lastRowRightValue = maxWeightNum;
							}
							if (Number(value) < Number(lastRowRightValue)) {
								return Promise.reject(new Error("不能低于上个规则的最大区间值"));
							}
						}
						if (!["", null, undefined].includes(curRowRight) && !(Number(value) < Number(curRowRight))) {
							return Promise.reject(new Error("不能大于或等于当前规则的最大的值"));
						}
						return Promise.resolve();
					},
				})
			]
		);

	};
		// 找出当前快递公司已选择的省市
	const handleSelectedCities = (curOptRow) => {
		const existedAddressArray = [];
		const theTemplateOriginData = cloneDeep(getTemplateOriginData());
		theTemplateOriginData.forEach(expressItem => {
			// 找出当前操作行对应的快递公司的数据
			if (expressItem.key === curOptRow.expressKey) {
				expressItem.detailList.forEach(detailItem => {
					if (detailItem.addressArray && detailItem.addressArray !== "ALL") {
						// 当前行的省份不需要禁用，别的行需要禁用
						if (detailItem.addressArray !== curOptRow.addressArray) {
							const addressArray = JSON.parse(detailItem.addressArray);
							existedAddressArray.push(...addressArray);
						}
					}
				});
			}
		});
		setDisabledAddressList(existedAddressArray);
	};

	// 添加运送地区
	const onAddArea = (record) => {
		setCurOptRow(record);
		setCurEditArea([]);
		handleSelectedCities(record);
		setSelectAreaModalVisible(true);
	};

	// 点击编辑地区
	const onEditArea = (record) => {
		setCurOptRow(record);
		setCurEditArea(JSON.parse(record.addressArray));
		handleSelectedCities(record);
		setSelectAreaModalVisible(true);
	};

	// 添加指定区域
	const onClickAddArea = () => {
		uniqueAreaIncrementKey += 1;
		uniqueWeightIncrementKey += 1;
		const theTemplateOriginData = cloneDeep(getTemplateOriginData());
		theTemplateOriginData.forEach(template => {
			template.detailList.push({
				addressArray: null,
				key: uniqueAreaIncrementKey,
				freightList: JSON.stringify(
					[{
						key: uniqueWeightIncrementKey,
						leftNum: 0,
						rightNum: maxWeightStr,
						freightNum: "",
						freightPrice: "",
						everyNum: "",
						everyPrice: ""
					}]
				),
			});
		});
		setTemplateOriginData(theTemplateOriginData);
		if (showVirtualTable) {
			setTimeout(() => {
				document.querySelector(`#${tableKey} .ant-table-body`)?.scrollTo(0, 1999999);
			}, 300);
		}
	};

	// 渲染快递公司列
	const renderExpressCompanyCol = (text, record, index) => {
		const { expressList, freightIndex } = record;
		return ((index === 0 && showVirtualTable) || !showVirtualTable) ? (
			<div className={ styles['express-edit-container'] }>
				<div className="express-content">
					{expressList.map(i => i.exName).join('，')}
				</div>
				<div className="edit-content" hidden={ type === "read" }>
					<span className={ styles["link-text"] } onClick={ () => (type === "add" ? onEditExpress(record) : null) }>
						{!expressList.length ? <span className="kdzs-label-required">选择快递公司</span> : (type === "add" ? "编辑" : "")}
					</span>
				</div>

			</div>
		) : '';
	};

	const handleExpressCompanyColSpan = (record, index) => {
		const { freightIndex } = record;
		return ({
			rowSpan: showVirtualTable ? 1 : (index === 0 ? dataSource.length : 0),
			style: showVirtualTable ? (freightIndex === 0 ? {} : { borderTop: 0 }) : ''
		});
	};

	const renderTemplateCol = (text, record, index) => {
		const { printTemplateList, freightIndex } = record;
		return ((index === 0 && showVirtualTable) || !showVirtualTable) ? (
			<div className={ styles['express-edit-container'] }>
				<div className="express-content">
					{printTemplateList.map(i => i.printTemplateName).join('，')}
				</div>
				<div className="edit-content" hidden={ type === "read" }>
					<span className={ styles["link-text"] } onClick={ () => onEditTemplate(record) }>
						{!printTemplateList.length ? "选择模板" : "编辑"}
					</span>
				</div>

			</div>
		) : '';
	};

	// 处理运送地区列
	const renderAddressCol = (text, record, index) => {
		const { addressListStr, freightIndex } = record;
		return ((freightIndex === 0 && showVirtualTable) || !showVirtualTable) ? (
			<div className={ styles['address-edit-container'] }>
				<div className="address-content">
					{addressListStr === "ALL" ? "全国（默认）" : addressListStr}
				</div>
				<div className="edit-content" hidden={ type === "read" }>
					{
						!addressListStr && (
							<span className={ styles["link-text"] } onClick={ () => onAddArea(record) }>
								添加运送地区
							</span>
						)
					}
					{
						addressListStr && addressListStr !== "ALL" && (
							<span className={ styles["link-text"] } onClick={ () => onEditArea(record) }>
								编辑
							</span>
						)
					}
				</div>

			</div>
		) : '';

	};
	const handleAddressColSpan = (record, index) => {
		const { freightIndex, freightLength } = record;
		return ({
			rowSpan: showVirtualTable ? 1 : (freightIndex === 0 ? freightLength : 0),
			style: showVirtualTable ? (freightIndex === 0 ? {} : { borderTop: 0 }) : ''
		});
	};

	// 处理重量范围列
	const renderWeightCol = (text, record, index) => {
		const { rowKey, freightLength, freightIndex, leftNum = "" } = record;
		let rightNum = record.rightNum;
		if (Number(rightNum) === maxWeightNum) {
			rightNum = maxWeightStr;
		}
		const showMinus = freightLength > 1 && freightLength - 1 === freightIndex;
		const showPlus = freightLength - 1 === freightIndex;
		if (type === "read") { // 如果只有一条规则，展示0-无穷大
			if (freightLength === 1) {
				return (
					<div>
						<span>{0}</span>
						<span> - </span>
						<span>{maxWeightStr}</span>
					</div>
				);
			} else if (freightIndex === 0) { // 否则第一条展示0-区间
				return (
					<div>
						<span>{0}</span>
						<span> - </span>
						<span>{rightNum}</span>
					</div>
				);
			} else if (freightLength - 1 === freightIndex) { // 最后一条展示大于
				return `大于${leftNum}`;
			}
			return ( // 中间的展示区间值
				<div>
					<span>{leftNum}</span>
					<span> - </span>
					<span>{rightNum}</span>
				</div>
			);
		}
		return (
			<div className={ styles['weight-container'] }>
				<span className="form-item-container">
					<Form.Item
						initialValue={ leftNum }
						name={ `${rowKey}@left` }
						rules={ weightLeftRules(record) }
					>
						<InputPrice onChange={ v => onLeftChange(v, record) } disabled={ freightIndex === 0 } precision={ 3 } className={ styles["table-input"] } suffix="kg" />
					</Form.Item>
					<span className="r-m-lr-4">-</span>
					<Form.Item
						initialValue={ rightNum }
						name={ `${rowKey}@right` }
						rules={ [{ required: true, message: "不能为空" }] }
					>
						<InputPrice onChange={ v => onRightChange(v, record) } disabled={ freightLength - 1 === freightIndex } precision={ 3 } className={ styles["table-input"] } suffix="kg" />
					</Form.Item>

				</span>
				<span className="icon-container">
					<PlusCircleFilled hidden={ !showPlus } onClick={ () => onOptRow(record, 'plus') } />
					<MinusCircleFilled hidden={ !showMinus } onClick={ () => onOptRow(record, 'minus') } />
				</span>
			</div>
		);


	};
	// 渲染首重列
	const renderFreightNumCol = (text, record, index) => {
		const { freightNum = "", rowKey } = record;
		if (type === "read") {
			return (
				<div>
					{freightNum}
				</div>
			);
		}
		return (
			<Form.Item
				initialValue={ freightNum }
				name={ `${rowKey}@freightNum` }
				rules={ [{ required: true, message: "不能为空" }] }
				required
			>
				<InputPrice suffix="kg" precision={ 3 } className={ styles["table-input"] } />
			</Form.Item>
		);

	};

	// 渲染首费列
	const renderFreightPriceCol = (text, record, index) => {
		const { freightPrice = "", rowKey } = record;
		if (type === "read") {
			return (
				<div>
					{freightPrice}
				</div>
			);
		}
		return (
			<Form.Item
				initialValue={ freightPrice }
				name={ `${rowKey}@freightPrice` }
				rules={ [{ required: true, message: "不能为空" }] }
			>
				<InputPrice suffix="元" precision={ 2 } className={ styles["table-input"] } />
			</Form.Item>
		);

	};
	// 渲染续重列
	const renderEveryNumCol = (text, record, index) => {
		const { everyNum = "", rowKey } = record;
		if (type === "read") {
			return (
				<div>
					{ everyNum }
				</div>
			);
		}
		return (
			<Form.Item
				initialValue={ everyNum }
				name={ `${rowKey}@everyNum` }
				rules={ [{ required: true, message: "不能为空" }] }
			>
				<InputPrice suffix="kg" precision={ 3 } className={ styles["table-input"] } />
			</Form.Item>
		);

	};
		// 渲染续费列
	const renderEveryPriceCol = (text, record, index) => {
		const { everyPrice = "", rowKey } = record;
		if (type === "read") {
			return (
				<div>
					{everyPrice}
				</div>
			);
		}
		return (
			<Form.Item
				initialValue={ everyPrice }
				name={ `${rowKey}@everyPrice` }
				rules={ [{ required: true, message: "不能为空" }] }
			>
				<InputPrice suffix="元" precision={ 2 } className={ styles["table-input"] } />
			</Form.Item>
		);

	};

	// 删除配送地区
	const onDeleteArea = (record) => {
		const theTemplateOriginData = cloneDeep(getTemplateOriginData());
		theTemplateOriginData.forEach(expressItem => {
			if (expressItem.key === record.expressKey) {
				expressItem.detailList.forEach((detailItem, idx) => {
					if (detailItem.key === record.areaKey) {
						// 只收集被删除的那一行的 id，避免重复
						if (detailItem.id) {
							const _needDeleteDetailIds = cloneDeep(getNeedDeleteDetailIds());
							_needDeleteDetailIds.push(detailItem.id);
							setNeedDeleteDetailIds(_needDeleteDetailIds);
						}
						expressItem.detailList.splice(idx, 1);
					}
				});
			}
		});
		setTemplateOriginData(theTemplateOriginData);
	};

	const renderOptCol = (_, record) => {
		if (record.addressArray === "ALL") {
			return <span style={ { whiteSpace: "nowrap" } }>删除</span>;
		}
		return ((record.freightIndex === 0 && showVirtualTable) || !showVirtualTable) ? (
			<span className={ styles["link-text"] } onClick={ () => onDeleteArea(record) }>删除</span>
		) : '';
	};
	const columns = useMemo(() => {
		const defaultColumns = [
			{
				title: "快递公司",
				className: "va-middle",
				onCell: handleExpressCompanyColSpan,
				render: renderExpressCompanyCol,
				width: 160,
			},
			{
				title: "快递模板",
				className: "va-middle",
				onCell: handleExpressCompanyColSpan,
				render: renderTemplateCol,
				width: 300,
			},
			{
				title: "运送地区",
				className: "va-middle",
				onCell: handleAddressColSpan,
				render: renderAddressCol,
				width: 300,
			}, {
				title: "重量范围（kg）",
				width: 300,
				render: renderWeightCol,
				onCell: () => {
					return {
						style: { borderLeft: showVirtualTable ? '1px solid #f0f0f0' : 'auto' }
					};
				}
			}, {
				title: "首重（kg）",
				render: renderFreightNumCol,
				width: 130,
			}, {
				title: "首费（元）",
				render: renderFreightPriceCol,
				width: 130,
			}, {
				title: "续重（kg）",
				render: renderEveryNumCol,
				width: 130,
			}, {
				title: "续费（元）",
				render: renderEveryPriceCol,
				width: 130,
			}, {
				title: "操作",
				width: 50,
				className: cs("opt-col", "va-middle"),
				onCell: handleAddressColSpan,
				render: renderOptCol,
			}
		];
		if (type === "read") {
			defaultColumns.splice(-1, 1);
		}
		return defaultColumns;
	}, [type, handleExpressCompanyColSpan, handleAddressColSpan, handleAddressColSpan]);
	const tableFooter = () => (
		<div className="r-flex r-ai-c r-jc-c">
			<div className={ styles["link-text"] } onClick={ onClickAddArea }>添加指定区域</div>
		</div>
	);

	const getAreaStrByAreaObj = (addressArray) => {
		let addressString = "";
		try {
			const addressObj = JSON.parse(addressArray);
			const provinceStrList = [];
			addressObj.forEach(province => {
				let provinceStr = province.name;
				const citiesList = province.childList;
				if (citiesList.length) {
					const citiesStr = citiesList.map(city => city.name).join("，");
					provinceStr = `${province.name}(${citiesStr})`;
				}
				provinceStrList.push(provinceStr);

			});
			addressString = provinceStrList.join("、");
		} catch (e) {
			addressString = addressArray;
		}
		return addressString;
	};

	// 将接口原数据转换成table的dataSourc
	const handleDataSource = () => {
		const theTemplateOriginData = cloneDeep(getTemplateOriginData());
		const dataSource = [];
		theTemplateOriginData.forEach(expressItem => {
			const dataSourceItem = {};
			const { logisticsId, detailList, templateId, printTemplateList = [] } = expressItem;
			const expressList = getExpressListByCode(logisticsId);
			detailList.forEach((detailItem, detailItemIndex) => {
				const { freightList, addressArray, } = detailItem;
				const addressListStr = getAreaStrByAreaObj(addressArray);
				const theFreightList = JSON.parse(freightList);
				theFreightList.forEach((freightItem, freightIndex) => {
					// formItem的公共key组成：快递公司ID_地址_当前规则下标
					const lastFreight = theFreightList[freightIndex - 1];
					const nextFreight = theFreightList[freightIndex + 1];
					const lastRowKey = `${expressItem.key}_${detailItem.key}_${lastFreight?.key}`;
					const nextRowKey = `${expressItem.key}_${detailItem.key}_${nextFreight?.key}`;
					const rowKey = `${expressItem.key}_${detailItem.key}_${freightItem.key}`;
					dataSource.push({
						...dataSourceItem,
						...freightItem,
						addressArray,
						rowKey,
						expressList,
						templateId,
						printTemplateList,
						logisticsId, // 用快递公司ID当前row所对应的快递公司
						addressListStr, // 用省份的拼接符区分当前row所对应的省份公司
						expressKey: expressItem.key, // 当前行的物流公司在所处模板中的key
						areaKey: detailItem.key, // 当前行的省份在所处物流公司中的key
						freightKey: freightItem.key, // 当前行的规则在所处省份中的key
						lastRowKey: freightIndex === 0 ? '' : lastRowKey,
						nextRowKey: freightIndex === theFreightList.length - 1 ? '' : nextRowKey,
						freightIndex, // 合并行需要用到
						detailItemIndex, // 删除配送地区行需要用到
						freightLength: theFreightList.length, // 当前规则所处的物流公司及省份对应的配置数量
						originData: detailItem,
					});
				});
			});
		});
		setDataSource(dataSource);
		return dataSource;
		function getExpressListByCode(logisticsId) {
			if (!logisticsId) {
				return [];
			}
			const logisticsCodeList = logisticsId.split(',');
			const expressList = [];
			getAllExpressList().forEach(expressItem => {
				if (logisticsCodeList.includes(expressItem.exCode)) {
					expressList.push(expressItem);
				}
			});
			return expressList;
		}
	};
	const expressModalOnOk = (selectedExpressInfo) => {
		const theTemplateOriginData = cloneDeep(getTemplateOriginData());
		theTemplateOriginData.forEach(expressItem => {
			// 找出当前操作行对应的快递公司的数据
			if (expressItem.key === curOptRow.expressKey) {
				// 快递公司如果改变了，需要清空快递模板
				if (expressItem.logisticsId != selectedExpressInfo.exCode) {
					expressItem.printTemplateList = [];
				}
				expressItem.exName = selectedExpressInfo.exName;
				expressItem.logisticsId = selectedExpressInfo.exCode;
			}
		});
		setTemplateOriginData(theTemplateOriginData);
	};
	const expressModalAfterClose = () => {
		setExpressModalVisible(false);
	};

	const selectAreaModalOnOk = (selectedAreaData) => {
		setSelectAreaModalVisible(false);
		const theTemplateOriginData = cloneDeep(getTemplateOriginData());
		theTemplateOriginData.forEach(expressItem => {
			const { detailList } = expressItem;
			// 1.找出当前操作行对应的快递公司
			if (expressItem.key === curOptRow.expressKey) {
				detailList.forEach(detailItem => {
					// 2.找出当前操作行对应快递公司下对应的省份
					if (detailItem.key === curOptRow.areaKey) {
						detailItem.addressArray = JSON.stringify(selectedAreaData);
					}
				});
			}
		});
		setTemplateOriginData(theTemplateOriginData);
	};

	const selectedTemplateModalOnOk = (selectTemplateList) => {
		// 把删除的和新增的快递模板找出来，后端需要
		const originTemplateList = curOptRow.printTemplateList.filter(template => template.id); // 有id的就是原模板
		const originTemplateIdList = originTemplateList.map(template => template.printTemplateId);
		const newTemplateIdList = selectTemplateList.map(template => template.Mode_ListShowId);
		const deletedTemplateIdList = differenceBy(originTemplateIdList, newTemplateIdList); // 已删除的模板Mode_ListShowId
		const needDeletePrintTemplateIds = [];
		originTemplateList.forEach(originTemplate => {
			if (deletedTemplateIdList.includes(originTemplate.printTemplateId)) {
				needDeletePrintTemplateIds.push(originTemplate.id);
			}

		});
		setNeedDeletePrintTemplateIds(needDeletePrintTemplateIds);
		setSelectTemplateModalVisible(false);
		const printTemplateList = selectTemplateList.map(template => {
			const isOldTemplate = originTemplateList.find(originTemplate => originTemplate.printTemplateId == template.Mode_ListShowId);
			const printTemplateItem = {
				templateId: curOptRow.templateId,
				printTemplateId: template.Mode_ListShowId,
				printTemplateName: template.ExcodeName
			};
			if (isOldTemplate) {
				// 如果模板之前就存在，需要把id传给后端
				printTemplateItem["id"] = isOldTemplate.id;
			}
			return printTemplateItem;
		});
		const theTemplateOriginData = cloneDeep(getTemplateOriginData());
		theTemplateOriginData.forEach(expressItem => {
			expressItem.printTemplateList = printTemplateList;
		});
		setTemplateOriginData(theTemplateOriginData);
	};

	const selectAreaModalOnCancel = () => {
		setSelectAreaModalVisible(false);
	};

	const handleUniqueKey = (data) => {
		// 将需要编辑的模板数据的物流公司和省份以及重量配置加上唯一key，因为formItem非常依赖这个
		data.forEach(expressItem => {
			expressItem.key = uniqueExpressIncrementKey++;
			expressItem.detailList.forEach(areaItem => {
				areaItem.key = uniqueAreaIncrementKey++;
				const freightListObj = JSON.parse(areaItem.freightList);
				freightListObj.forEach(freightItem => {
					freightItem.key = uniqueWeightIncrementKey++;
				});
				areaItem.freightList = JSON.stringify(freightListObj);
			});
		});
		setTemplateOriginData(data);

	};

	const getTemplateData = () => {
		let errorMsg = "";
		const hasNoSelectedExpress = getTemplateOriginData().some(expressItem => !expressItem.logisticsId);
		const hasNoSelectedArea = getDataSource().some(item => !item.addressListStr);
		if (hasNoSelectedArea) {
			errorMsg = "请添加完配送地区再重新保存！";
		}
		if (hasNoSelectedExpress) {
			errorMsg = "请选择快递公司再重新保存！";
		}
		if (errorMsg) {
			message.error(errorMsg);
			return;
		}
		return new Promise((resolve, reject) => {
			const fn = (values) => {
				const templateData = {};
				const theTemplateOriginData = cloneDeep(getTemplateOriginData());
				/**
			 * 每个dataSourceItem里面都包含了一个logisticsId和addressArray字段，
			 * 这两个字段可以区分出来当前表单所对应的配送地区以及对应的物流公司
			 * 所以将dataSource转换成{物流公司:{配送地址:[每一行表单数据项]}}的形式
			 * 好处是将表单数据映射到templateOriginData时减少遍历次数
			 * */
				getDataSource().forEach(dataSourceItem => {
					const { logisticsId, addressArray, rowKey } = dataSourceItem;
					const leftNum = values[`${rowKey}@left`];
					let rightNum = values[`${rowKey}@right`];
					if (rightNum === maxWeightStr) {
						rightNum = maxWeightNum;
					}
					const freightNum = values[`${rowKey}@freightNum`];
					const freightPrice = values[`${rowKey}@freightPrice`];
					const everyNum = values[`${rowKey}@everyNum`];
					const everyPrice = values[`${rowKey}@everyPrice`];
					if (!templateData[logisticsId]) {
						templateData[dataSourceItem.logisticsId] = {};
					}
					if (!templateData[logisticsId][addressArray]) {
						templateData[logisticsId][addressArray] = [];
					}
					templateData[logisticsId][addressArray].push({
						leftNum, rightNum, freightNum, freightPrice, everyNum, everyPrice
					});
				});
				theTemplateOriginData.forEach(template => {
					const curTemplateData = templateData[template.logisticsId];
					template.detailList.forEach(detail => {
						const curAddressData = curTemplateData[detail.addressArray];
						detail.freightList = JSON.stringify(curAddressData);
					});
				});
				handleUniqueKey(theTemplateOriginData);
				resolve({
					templateData: theTemplateOriginData,
					needDeleteDetailIds: getNeedDeleteDetailIds(),
					needDeletePrintTemplateIds: getNeedDeletePrintTemplateIds(),
				});
				setNeedDeleteDetailIds([]);
			};
	
			if (showVirtualTable) {
				// 【报表】运费模板保存不生效（加急）,因为在虚拟列表的时候数据处理有问题
				const todoFieldsMap = {
					'@right': 'rightNum',
					'@left': 'leftNum',
					'@everyNum': 'everyNum',
					'@everyPrice': 'everyPrice',
					'@freightNum': 'freightNum',
					'@freightPrice': 'freightPrice',
				};
				 // 先获取当前 dataSource 中的所有值
				 const currentValues = {};
				 getDataSource().forEach(item => {
					 Object.keys(todoFieldsMap).forEach(key => {
						 let formItemName = `${item.rowKey}${key}`;
						 currentValues[formItemName] = form.getFieldValue(formItemName) ?? item[todoFieldsMap[key]];
					 });
				 });
			 
				 // 设置表单值并验证
				 form.setFieldsValue(currentValues);
				 form.validateFields(Object.keys(currentValues)).then(() => {
					 fn(currentValues);
				 }).catch(err => {
					 console.error('表单验证失败:', err);
				 });
				
			} else {
				form.validateFields().then(values => {
					fn(values);
				});
			}
		});
	};

	useEffect(() => {
		handleDataSource();
	}, [templateOriginData, getTemplateOriginData, getAllExpressList, allExpressList]);

	useEffect(() => {
		switch (type) {
			case "read":
				handleUniqueKey(data);
				break;
			case "add":
				setTemplateOriginData(defaultTemplate);
				break;
			default:
				break;
		}
	}, [type, data]);
	useEffect(() => {
		if (type === 'edit') {
			handleUniqueKey(data);
		}
		/**
		 * 这里不能加data的监听，因为form的数据changge的时候并没有更新到源数据
		 * 如果这里监听了data，会出现点击保存的时候，表单会恢复默认的值的情况
		 */
	}, [type]);

	useEffect(() => {
		memoFn.getExpressList().then(res => {
			setAllExpressList(res);
		});
	}, []);

	useImperativeHandle(cRef, () => {
		return {
			getTemplateData,
		};
	}, []);

	const onChange = (changedFields, allFields) => {
		if (showVirtualTable) {
			 const fieldName = changedFields[0].name[0];
			const value = changedFields[0].value;
			const rowKey = fieldName?.split('@')[0];
			const index = dataSource.findIndex(i => i.rowKey === rowKey);
        
			// 同步更新 form 和 dataSource
			form.setFieldsValue({
				[fieldName]: value
			});
        
			setDataSource(prev => {
				const newDataSource = [...prev];
				newDataSource[index] = {
					...newDataSource[index],
					[fieldName]: value,
					// 同时更新对应的原始数据字段
					[fieldName.split('@')[1]]: value
				};
				return newDataSource;
			});
		}
	};

	return (
		<div>
			<Form
				form={ form }
				onFieldsChange={ onChange }
			>
				{showVirtualTable ? (
					<BaseCheckGroupTable
						loading={ loading }
						pagination={ false }
						className={ styles.table }
						dataSource={ dataSource }
						columns={ columns }
						{
							...type === "read" ? {} : { footer: tableFooter }
						}
						bordered
						groupId={ tableKey }
						id={ tableKey }
						scroll={ {
							y: 400
						} }
					/>
				) : (
					<Table
						loading={ loading }
						pagination={ false }
						className={ styles.table }
						dataSource={ dataSource }
						columns={ columns }
						{
							...type === "read" ? {} : { footer: tableFooter }
						}
						bordered
						id={ tableKey }
						scroll={ { x: 1300 } }
					/>
				) }
			</Form>
			{/* 选择快递公司 */}
			<SelectExpressModal
				visible={ expressModalVisible }
				onOk={ expressModalOnOk }
				afterClose={ expressModalAfterClose }
				value={ selectedExpressCode }
				// disabledList={ validDisabledExpressCodeList }
			/>

			{/* 选择配送区域 */}
			{
				selectAreaModalVisible && (
					<SelectAreaModal
						disabledArea={ disabledAddressList }
						selectedArea={ curEditArea }
						onOk={ selectAreaModalOnOk }
						onCancel={ selectAreaModalOnCancel }
					/>
				)
			}

			{/* 选择模板 */}
			{
				selectTemplateModalVisible && (
					<SelectTemplate
						value={ selectedTemplateList }
						title={ selectTemplateModalTitle }
						expressCode={ selectedExpressCode }
						onOk={ selectedTemplateModalOnOk }
						disabledTemplateList={ disabledSelectedTemplateList }
						onCancel={ () => setSelectTemplateModalVisible(false) }
					/>
				)
			}


		</div>
	);
};
export default observer(TempTable);
