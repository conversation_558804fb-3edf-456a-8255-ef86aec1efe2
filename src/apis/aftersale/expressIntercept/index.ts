import axios, { instance } from '@/utils/request';

import { 
	AddLogisticsInterceptRequest, 
	AddLogisticsInterceptResponse, 
	BatchDelLogisticsInterceptRequest, 
	BatchDelLogisticsInterceptResponse, 
	MarkLogisticsInterceptStatusRequest, 
	MarkLogisticsInterceptStatusResponse, 
	SelectLogisticsInterceptListRequest, 
	SelectLogisticsInterceptListResponse, 
	SelectLogisticsInterceptTabCountRequest, 
	SelectLogisticsInterceptTabCountResponse,
	SelectLogisticsInterceptListV2Request,
	SelectLogisticsInterceptListV2Response
} from '@/types/aftersale/expressIntercept';


/**
* 查询快递拦截列表
*/
export function SelectLogisticsInterceptListApi(data: SelectLogisticsInterceptListRequest) {
	return axios.post<unknown, SelectLogisticsInterceptListResponse>('/aftersale/selectLogisticsInterceptList', data).then(res => res.data);
}
/**
* 查询快递拦截列表V2
*/
export function SelectLogisticsInterceptListV2Api(data: SelectLogisticsInterceptListV2Request) {
	return axios.post<unknown, SelectLogisticsInterceptListV2Response>('/aftersale/selectLogisticsInterceptListV2', data).then(res => res.data);
}
/**
* 删除快递拦截列表
*/
export function BatchDelLogisticsInterceptApi(data: BatchDelLogisticsInterceptRequest) {
	return axios.post<unknown, BatchDelLogisticsInterceptResponse>('/aftersale/batchDelLogisticsIntercept', data).then(res => res.data);
}

/**
* 快递拦截tab数量统计
*/
export function SelectLogisticsInterceptTabCountApi(data: SelectLogisticsInterceptTabCountRequest) {
	return axios.post<unknown, SelectLogisticsInterceptTabCountResponse>('/aftersale/selectLogisticsInterceptTabCount', data).then(res => res.data);
}

/**
* 新增拦截快递
*/
export function AddLogisticsInterceptApi(data: AddLogisticsInterceptRequest) {
	return axios.post<unknown, AddLogisticsInterceptResponse>('/aftersale/addLogisticsIntercept', data).then(res => res.data);
}

/**
* 标记拦截状态
*/
export function MarkLogisticsInterceptStatusApi(data: MarkLogisticsInterceptStatusRequest) {
	return axios.post<unknown, MarkLogisticsInterceptStatusResponse>('/aftersale/markLogisticsInterceptStatus', data).then(res => res.data);
}

export function IndexCommonUploadFileApi(data: any) {
	return instance({
		method: 'POST',
		headers: {
			'Content-Type': 'multipart/form-data'
		},
		url: '/index/common/uploadFile',
		data
	});
}