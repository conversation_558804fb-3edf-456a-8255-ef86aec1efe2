<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<title>快递助手ERP</title>
	<link rel="icon" href="/favicon.ico" />
</head>

<body>
	<div id="root" style="width: 100%"></div>
</body>

<script>
	function isIE() {
		return !!window.ActiveXObject || "ActiveXObject" in window;
	}
	if (isIE()) {
		document.body.innerHTML = "";
		let img = document.createElement("img");
		img.src = "./static/ie.png";
		img.style.width = "100%";
		document.body.appendChild(img);
	}
</script>

<script>
	function appendScript(url, obj) {
		const { scriptKeys, onload, fallbackSrc } = obj || {};
		const script = document.createElement("script");

		script.src = url;

		if (scriptKeys) {
			Object.keys(scriptKeys).forEach((key) => {
				script[key] = scriptKeys[key];
			});
		}

		document.body.appendChild(script);

		if (onload) {
			script.onload = () => {
				onload();
			};
		}
		if (fallbackSrc) {
			script.onerror = () => {
				script.parentNode.removeChild(script);
				appendScript(fallbackSrc);
			}
		}
	}

	function appendLink(url, obj) {
		const { ref, fallbackHref } = obj || {};
		const link = document.createElement("link");

		link.href = url;
		link.rel = ref || "stylesheet";
		link.type = "text/css";

		document.head.appendChild(link);
		if (fallbackHref) {
			link.onerror = () => {
				link.parentNode.removeChild(link);
				appendLink(fallbackHref, { ref });
			};
		}
	}

	function createLastedAssets(newVersion, newHash) {
		// 获取构建时注入的模板变量
		const templateVars = window.__TEMPLATE_VARS__ || {};
		const { version: currentVersion, buildEnv, buildPrefix } = templateVars;

		if (newVersion && newHash) {
			// 动态版本资源加载 - 当 API 返回新版本时使用
			console.log('加载动态版本:', newVersion, newHash);

			// 构建动态路径的正则表达式
			const versionRegex = new RegExp(`\\/${buildEnv}\\/[\\d.]+\\/`, 'g');
			// 支持不同长度的 hash (通常是8位，但也可能是其他长度)
			const hashRegex = /\.[a-f0-9]{6,}\.(js|css)/g;

			// 1. 移除现有的资源标签
			const existingScripts = Array.from(document.querySelectorAll('script[src*="/js/"]'));
			const existingModulePreloads = Array.from(document.querySelectorAll('link[rel="modulepreload"]'));
			const existingStylesheets = Array.from(document.querySelectorAll('link[rel="stylesheet"][href*="/css/"]'));

			// 移除旧的脚本标签
			existingScripts.forEach(script => script.remove());
			existingModulePreloads.forEach(link => link.remove());
			existingStylesheets.forEach(link => link.remove());

			// 2. 加载新版本的资源
			// 处理 JS 文件 - 按照依赖顺序加载
			const jsFiles = [];
			existingScripts.forEach(script => {
				if (script.src && script.src.includes('/js/')) {
					const newSrc = script.src
						.replace(versionRegex, `/${buildEnv}/${newVersion}/`)
						.replace(hashRegex, `.${newHash}.$1`);
					jsFiles.push({ newSrc, fallbackSrc: script.src });
				}
			});

			// 按顺序加载 JS 文件
			jsFiles.forEach((file, index) => {
				setTimeout(() => {
					appendScript(file.newSrc, { fallbackSrc: file.fallbackSrc });
				}, index * 100);
			});

			// 处理 CSS 文件
			existingStylesheets.forEach(link => {
				if (link.href && link.href.includes('/css/')) {
					const newHref = link.href
						.replace(versionRegex, `/${buildEnv}/${newVersion}/`)
						.replace(hashRegex, `.${newHash}.$1`);

					appendLink(newHref, { fallbackHref: link.href });
				}
			});

			// 处理 favicon
			const favicon = document.querySelector('link[rel="icon"]');
			if (favicon) {
				const newFaviconHref = favicon.href
					.replace(versionRegex, `/${buildEnv}/${newVersion}/`);
				favicon.href = newFaviconHref;
			}
		}
	}

	// 这里处理通过nacos回滚的情况（未验证）
	// fetch("/index/fe/nacos/getConfig")
	// 	.then(function (response) {
	// 		if (!response.ok) {
	// 			throw new Error("Fetch Request failed");
	// 		}
	// 		return response.json();
	// 	})
	// 	.then(function (data) {
	// 		console.log("Fetch Response:", data);
	// 		const { version, hash } = JSON.parse(data);
	// 		if (version && hash) {
	// 			createLastedAssets(version, hash)
	// 		} else {
	// 			createLastedAssets()
	// 		}
	// 	})
	// 	.catch(function (error) {
	// 		console.error("Fetch Request failed:", error);
	// 		createLastedAssets()
	// 	});
</script>

<!-- 拼多多安全sdk -->
<script src="https://commfile.pddpic.com/galerie-go/open_sdk/pc.202102201613.js" type="text/javascript"></script>

<!-- 淘宝安全sdk -->
<script src="https://g.alicdn.com/ali-data-safe/securesdk_v3/0.0.2/sec-min.js"></script>
<script src="https://g.alicdn.com/sd/eco-opensecenv/2.0.3/js/axios_min.js"></script>
<script src="https://g.alicdn.com/code/npm/@ali/cro-securesdk/0.0.5/secToken.js"></script>

<script type="text/javascript" src="https://g.alicdn.com/sj/securesdk/0.0.3/securesdk_v2.js" id="J_secure_sdk_v2"
	data-appkey="********"></script>

<script>
	// 抖音sdk太慢 等onload之后才加载
	window.onload = () => {
		console.log("onload");
		// 创建 script 元素
		var script = document.createElement("script");
		script.src =
			"https://lf3-open-web-sdk.bytetos.com/obj/open/sdk_v2.js";

		// 将 script 元素添加到文档的头部或者身体部分
		document.body.appendChild(script);
		script.onload = () => {
			BYTES_OPEN_int({
				accountId: "cda03627-f7e0-4a6e-a4e4-11654b2a438b",
				appKey: "7038561283238528519",
				sensUrls: [
					"/*/batchDecryData",
				],
			});
		};
	};
</script>

</html>