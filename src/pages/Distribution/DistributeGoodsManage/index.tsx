import cs from 'classnames';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useLocation } from "react-router-dom";
import { Button, Form, Popover, Input, Tooltip, InputNumber, Modal, Checkbox, Select, Radio, Switch } from 'antd';
import { observer } from "mobx-react";
import { useForm } from 'antd/es/form/Form';
import { DownOutlined, ExclamationCircleOutlined, FormOutlined, UpOutlined, CheckOutlined } from '@ant-design/icons';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import NormalLayout from '@/components-biz/layouts/NormalLayout';
import SearchTable from '@/components/SearchTable';
import { FormItemConfig } from '@/components/SearchTable/FormWidthRow';
import {
	batchCloseSkuRemindApi,
	ItemScmConfigGetScmConfigApi,
	ItemScmConfigSaveOrUpdateConfigApi,
	getBalancePriceSetNumApi,
	batchUpdateExaminePriceApi,
	ItemScmQueryScmItemInfoListPageApi
} from '@/apis/distribution';
import { SearchTableRefProps } from "@/components/SearchTable/SearchTable";

import s from './index.module.scss';
import sendPoint from '@/utils/pointTrack/sendPoint';
import Pointer from '@/utils/pointTrack/constants';
import { DistributorSelect } from '@/components-biz/Distribution';
import {
	BatchUpdateSysItemByPlatformApi,
	ItemAsyncGetProgressApi,
	ItemSysItemChangeItemRelationApi,
	ItemSysItemDeleteItemRelationWithBatchApi,
	ItemSysItemSaveItemRelationApi,
	UpdateSysItemByPlatformApi,
	batchDeleteBySkuIdsApi
} from '@/apis/warehouse/system';
import { PLAT_JD, PLAT_SCMHAND, PLAT_XHS, PLAT_YZ } from '@/constants';
import { getPlatformDetailLink } from '@/pages/AfterSale/TradeList/utils';
import { getFormulaPrice, getImageNode, nameFactory } from './utils';
import PlatformIcon from '@/pages/Warehouse/StockSync/HandStockSync/components/PlatformIcon';
import userStore from '@/stores/user';
import {
	BindRelationDTO,
	ItemRelationPlatformSystem,
	ItemSysItemSaveItemRelationRequest,
	OperateTypeEnum,
	RelationSystemItemSku,
	UserViewValEnum
} from '@/types/schemas/warehouse/system';
import ProductListModal from '@/components-biz/Product/List/Model';
import message from '@/components/message';
import { Obj } from '@/pages/Trade/interface';
import BatchEditModal, { addPosMap, getNewContent, BatchTypeEnum, AddContentType, CalPriceParams, PriceBatchType } from './components/BatchEditModal';
import SysSkuLogs from './components/SysSkuLogs';
import ProgressModal from '@/components-biz/ProgressModal';
import distributionStore from '@/stores/distribution';
import { SkuUpdateItem } from '@/types/schemas/distribution';
import Icon from '@/components/Icon';
import { weightUnit } from '@/pages/Index/Settings/System/constants';
import { accMul } from '@/utils/util';
import ImportGoodsFromDistribution from './components/ImportGoodsFromDistribution';
import WeightInput from '@/components/Input/InputNumber/WeightInput';
import { FieldsPermissionEnum } from '@/utils/permissionCheck/fieldsPermissionCheck';
import { isSourceScm } from '@/components-biz/ShopListSelect/shopListUtils';
import { fetchWithPaginationOptimization } from '@/pages/Warehouse/System/Relation/components/PlatformTable/utils/paginationOptimization';

const Option = Select.Option;

let global_newFormVal;
let global_isFirst;
let global_isFirstSale;
let global_curSearch;
let global_editMap = {};

const maxPrice = 99999999.99;
const maxWeight = 999999;

enum ColumnName {
	"商品/规格",
	"商家编码/规格编码",
	"结算价",
	"分销商结算价",
	"供应商结算价",
	"关联本地货品",
	"简称",
	"规格别名",
	"日志",
	"重量",
	成本价,
	吊牌价,
	市场,
	档口,
	供应商,
}
const supplierRoutes = ['/distribution/DistributeGoodsManage']; // 供应商的路由
const DistributorRoutes = ['/distribution/SettlementSetting']; // 分销商的路由

const 相同规格编码自动关联 = "AUTO_MATCH_SKU_OUTER_ID";

const defaultPriceNum = { notSetNum: 0, setNum: 0 };
const MyDistributorPage = () => {
	const { pathname, state: locationState }: any = useLocation();
	const [dataSource, setDataSource] = useState<any[]>([]);
	const [dataFormSource, setDataFormSource] = useState<Obj>();
	const [batchEditModalInfo, setBatchEditModalInfo] = useState<Obj>({});
	const [curRow, setCurRow] = useState<ItemRelationPlatformSystem>();
	const [isAllCollapse, setIsAllCollapse] = useState(false);
	const [priceNum, setPriceNum] = useState(defaultPriceNum);
	const [priceNum2, setPriceNum2] = useState(defaultPriceNum);
	const [recordLogModalData, setRecordLogModalData] = useState<ItemRelationPlatformSystem>();
	const [form] = useForm();
	const [editAutoLinkForm] = useForm();
	const [autoMatchSkuOuterId, setAutoMatchSkuOuterId] = useState(0);
	const [jumpObj, setJumpObj]: any = useState({});
	const ref = useRef<SearchTableRefProps>();
	const [loopObj, setLoopObj] = useState<Obj>({
		asyncCode: '',
		isStart: false,
	});
	const { isSupplierAccount, isDistributorAccount, isFreeSupplierAccount } = userStore;
	const isSupplier = supplierRoutes.includes(pathname) || isFreeSupplierAccount; // 能明确的供应商身份的是免费供应商版
	const isDistributor = DistributorRoutes.includes(pathname) || isDistributorAccount; // 能明确的分销商身份的是免费分销商版


	const { distributorList = [], supplierList = [] } = distributionStore;
	const [isKg, setIsKg] = useState(false);
	// const isKg = userStore?.userSetting?.weightUnit == weightUnit.显示kg;
	useEffect(() => {
		// 我是供应商
		if (isSupplier && distributorList?.length) {
			// 如果是别的页面传参跳转的
			if (locationState?.searchId) {
				form.setFieldsValue({
					saleUserIdList: [locationState?.searchId],
				});
				setJumpObj({
					numIid: locationState?.numIid || '',
					skuId: locationState?.skuId || '',
				});
				global_isFirstSale = true; // state 只能本次路由有效，所以也要存贮下状态
				setTimeout(() => {
					ref?.current?.submit();
				}, 100);
			} else if (isSupplier && !global_isFirstSale) {
				let val = distributorList?.[0]?.saleUserId;
				let storageVal = localStorage.getItem('DistributeGoodsManageSearch_saleUserId');
				if (storageVal) {
					val = storageVal;
					localStorage.setItem('DistributeGoodsManageSearch_saleUserId', '');
				}
				form.setFieldsValue({ saleUserIdList: [val] });
				global_isFirstSale = true;
				setTimeout(() => {
					ref?.current?.submit();
				}, 100);
			}
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [distributorList, isSupplier, locationState]);

	useEffect(() => {
		// 我是分销商
		if (isDistributor && supplierList?.length) {
			// 如果是别的页面传参跳转的
			if (locationState?.searchId2) {
				form.setFieldsValue({
					supplierUserIdList: [locationState?.searchId2],
				});
				setJumpObj({
					numIid: locationState?.numIid2 || '',
					skuId: locationState?.skuId2 || '',
				});
				global_isFirst = true;
				setTimeout(() => {
					ref?.current?.submit();
				}, 100);
			} else if (isDistributor && !global_isFirst) {
				let val = supplierList?.[0]?.supplierUserId;
				let storageVal = localStorage.getItem('DistributeGoodsManageSearch_supplierUserId');
				if (storageVal) {
					val = storageVal;
					localStorage.setItem('DistributeGoodsManageSearch_supplierUserId', '');
				}
				form.setFieldsValue({ supplierUserIdList: [val] });
				global_isFirst = true;
				setTimeout(() => {
					ref?.current?.submit();
				}, 100);
			}
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [supplierList, isDistributor, locationState]);

	useEffect(() => {
		ItemScmConfigGetScmConfigApi({ itemScmConfigEnum: 相同规格编码自动关联 }).then((res) => {
			if (res?.itemScmConfigEnum === 相同规格编码自动关联) {
				setAutoMatchSkuOuterId(Number(res?.values || 0));
			}
		});
	}, []);


	useEffect(() => {
		if (isDistributor) {
			sendPoint(Pointer.分销商角色_结算价设置_页面展现_展现次数);
		} else {
			sendPoint(userStore.isShowZeroStockVersion ? Pointer['供应商_代发商品管理_页面展现（零库存版）_展现'] : Pointer['供应商_代发商品管理_页面展现（库存版）_展现']);
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [userStore.isShowZeroStockVersion, isDistributor]);

	const fetchSystemList = (info) => {
		if (isDistributor) {
			sendPoint(Pointer.分销商角色_结算价设置_查询_点击次数);
		} else {
			sendPoint(userStore.isShowZeroStockVersion
				? Pointer['供应商_代发商品管理_页面展现（零库存版）_查询_点击']
				: Pointer['供应商_代发商品管理_查询_点击']);
		}
		let search: any = {
			pageNo: info.pageNo,
			pageSize: info.pageSize,
			saleUserIdList: [],
			userViewVal: UserViewValEnum.供应商,
			blurSearch: true
		};
		FormFieldList.forEach((i: { name: string }) => {
			search[i.name] = info[i.name];
		});
		if (!search?.saleUserIdList?.length || !search?.saleUserIdList?.[0]) {
			return;
		}
		// 只查询第一次跳转时候带的参数
		if (jumpObj?.numIid || jumpObj?.skuId) {
			search.numIid = jumpObj?.numIid || '';
			search.skuId = jumpObj?.skuId || '';
		}
		global_curSearch = search;
		return fetchWithPaginationOptimization(search, info.pageSize);
	};
	const fetchDataWhenDistributor = (info) => {
		let search: any = {
			pageNo: info.pageNo,
			pageSize: info.pageSize,
			supplierUserIdList: [],
		};
		FormFieldList.forEach((i: { name: string }) => {
			search[i.name] = info[i.name];
		});
		if (!search?.supplierUserIdList?.length || !search?.supplierUserIdList?.[0]) {
			return;
		}
		// 只查询第一次跳转时候带的参数
		if (jumpObj?.numIid || jumpObj?.skuId) {
			search.numIid = jumpObj?.numIid || '';
			search.skuId = jumpObj?.skuId || '';
		}
		global_curSearch = search;

		return ItemScmQueryScmItemInfoListPageApi(search);
	};
	const getSetNum = async () => {
		const params = {
			saleUserIdList: global_curSearch?.saleUserIdList,
			supplierUserIdList: global_curSearch?.supplierUserIdList,
			numIids: jumpObj?.numIid || '',
			skuId: jumpObj?.skuId || '',
		};
		setJumpObj({});
		try {
			const res: any = await getBalancePriceSetNumApi(params);

			if (isSupplier) {
				setPriceNum2(res);
			} else {
				setPriceNum(res);
			}
		} catch (error) {
			if (isSupplier) {
				setPriceNum2(defaultPriceNum);
			} else {
				setPriceNum(defaultPriceNum);
			}
		}

	};
	const responseAdapter = (data): any => {
		if (!data?.pageNo) {
			return;
		}
		const tableList: ItemRelationPlatformSystem[] = [];
		const formData = {};
		data?.list.forEach((item, i) => {
			const goodsItem = {
				isGoods: true,
				...item,
				rowKey: `key_${i}`,
			};
			tableList.push(goodsItem);
			item.platformItemSkuList.forEach((platformItem, platformItemIndex) => {
				const skuItem = {
					...item,
					relationSystemItemList: platformItem.relationSystemItemList,
					platformItem,
					platformItemIndex,
					rowKey: `key_${i}_${platformItemIndex}`,
					fatherRowKey: `key_${i}`,
					balancePrice: platformItem.balancePrice,
					balancePriceSale: platformItem.balancePriceSale,
				};
				if (platformItem.balancePrice == 'NaN') {
					platformItem.balancePrice = '';
				}
				if (platformItem.balancePriceSale == 'NaN') {
					platformItem.balancePriceSale = '';
				}
				tableList.push(skuItem);
				formData[nameFactory(skuItem.rowKey, BatchTypeEnum.结算价)] = platformItem.balancePrice || '';
				if (isDistributor) {
					formData[nameFactory(skuItem.rowKey, BatchTypeEnum.结算价)] = platformItem.balancePriceSale || '';
				}

				// 零库存版+供应商
				if (userStore.isShowZeroStockVersion && isSupplier) {
					formData[nameFactory(skuItem.rowKey, BatchTypeEnum.重量)] = platformItem.relationSystemItemList?.[0]?.weight || '';
				}

				formData[nameFactory(skuItem.rowKey, BatchTypeEnum.简称)] = platformItem.relationSystemItemList?.[0]?.sysItemAlias || '';
				formData[nameFactory(skuItem.rowKey, BatchTypeEnum.别名)] = platformItem.relationSystemItemList?.[0]?.sysSkuAlias || '';
				formData[nameFactory(skuItem.rowKey, BatchTypeEnum.供应商)] = platformItem.relationSystemItemList?.[0]?.supplierName || '';
				formData[nameFactory(skuItem.rowKey, BatchTypeEnum.市场)] = platformItem.relationSystemItemList?.[0]?.market || '';
				formData[nameFactory(skuItem.rowKey, BatchTypeEnum.档口)] = platformItem.relationSystemItemList?.[0]?.stall || '';
				formData[nameFactory(skuItem.rowKey, BatchTypeEnum.成本价)] = platformItem.relationSystemItemList?.[0]?.costPrice || '';
				formData[nameFactory(skuItem.rowKey, BatchTypeEnum.吊牌价)] = platformItem.relationSystemItemList?.[0]?.tagPrice || '';
			});
		});
		setDataSource(tableList);
		getSetNum();
		setDataFormSource(formData);
		global_editMap = formData;
		setTimeout(() => {
			form.setFieldsValue(formData);
		}, 0);
		return {
			total: data?.total,
			list: tableList
		};
	};

	const changeCollapse = (rowKey) => {
		setDataSource(pre => {
			const newData = [...pre];
			const dataIndex = newData.findIndex(i => i.rowKey == rowKey);
			if (newData?.[dataIndex]?.isGoods) {
				newData[dataIndex].isCollapse = !newData[dataIndex].isCollapse;
			}
			return newData;
		});
	};

	const changeAllCollapse = (dataIndex) => {
		const newIsAllCollapse = !isAllCollapse;
		setIsAllCollapse(newIsAllCollapse);
		setDataSource(pre => {
			const newData = [...pre];
			newData.forEach(row => {
				if (row.isGoods) {
					row.isCollapse = newIsAllCollapse;
				}
			});
			return newData;
		});
	};

	const SettlePriceButton = (props) => {
		const { value, onChange, priceNum } = props;

		const handleChange = (e) => {
			let _val: boolean | string = false;
			if (e === 0) {
				_val = value === false ? "" : false;
			} else if (e === 1) {
				_val = value === true ? "" : true;
			}
			onChange?.(_val);
			ref?.current?.submit();
		};
		return (
			<div className="r-flex" style={{ width: "100%" }}>
				<div className={cs(s["refund-container"], "r-pointer", "r-mr-8", s.afterSaleTopTabItem, value === false ? s.checkedTabItem : '')} onClick={() => { handleChange(0); }}>
					{value === false ? <CheckOutlined className="r-mr-4 r-c-error r-bold" /> : null}
					<span className="r-bold">未设置结算价<span className="r-c-error r-m-lr-4 r-bold">{priceNum.notSetNum > 0 ? priceNum.notSetNum : 0}</span></span>
				</div>
				<div className={cs(s["refund-container"], "r-pointer", s.afterSaleTopTabItem, value === true ? s.checkedTabItem : '')} onClick={() => { handleChange(1); }}>
					{value === true ? <CheckOutlined className="r-mr-4 r-c-error r-bold" /> : null}
					<span className="r-bold">已设置结算价<span className="r-c-error r-m-lr-4 r-bold" >{priceNum.setNum > 0 ? priceNum.setNum : 0}</span></span>
				</div>
			</div>
		);
	};


	let FormFieldList: FormItemConfig[] = [
		{
			label: (
				<span className="r-bold">{isSupplier ? "请选择分销商" : "请选择供应商"}</span>
			),
			name: isSupplier ? 'saleUserIdList' : 'supplierUserIdList',
			style: { marginBottom: 8, alignItems: "center" },
			children: <DistributorSelect style={{ width: '280px' }} isSingle roleType={isSupplier ? 'supplier' : 'sale'} />
		},
		{
			label: "",
			name: 'existBalancePrice',
			style: { marginBottom: 8, alignItems: "center" },
			children: <SettlePriceButton priceNum={isSupplier ? priceNum2 : priceNum} />
		},
		{
			name: 'oneRowAndNoContent',
			className: 'r-mb-8',
			oneRowAndNoContent: true,
		},
		{
			name: 'numIid',
			label: '',
			className: 'r-mb-8',
			children: <Input placeholder="商品ID" />,
		},
		{
			name: 'skuId',
			label: '',
			className: 'r-mb-8',
			children: <Input placeholder="skuID" />,
		},
		{
			name: 'title',
			label: '',
			className: 'r-mb-8',
			children: <Input placeholder="商品名称" />,
		},
		{
			name: 'skuName',
			label: '',
			className: 'r-mb-8',
			children: <Input placeholder="规格" />,
		},
		{
			name: 'outerId',
			label: '',
			className: 'r-mb-8',
			children: <Input placeholder="商家编码" />
			
			
		},
		{
			name: 'skuOuterId',
			label: '',
			className: 'r-mb-8',
			children: <Input placeholder="规格编码" />
			
		}
	];
	const StockSearchItems = [
		{
			name: 'sysSkuOuterId',
			label: '',
			className: 'r-mb-8',
			children: <Input placeholder="货品规格编码" />,
		},
		{
			label: "",
			name: 'relationSysItem',
			style: { marginBottom: 8, alignItems: "center" },
			children: (
				<Select className={cs('r-w-full')} placeholder="是否关联货品" size="small" style={{ width: 160 }} allowClear>
					<Option value="" key={0}>全部</Option>
					<Option value key={2}>已关联</Option>
					<Option value={false} key={1}>未关联</Option>
				</Select>)
		},
		{
			name: 'sysSkuOuterId',
			label: '',
			className: 'r-mb-8',
			children: <Input placeholder="货品规格编码" />,
		},
	];

	const zeroStockVersionSearchItems = [
		{
			name: 'sysItemAlias',
			label: '',
			className: 'r-mb-8',
			children: <Input placeholder="简称" />,
		},
		{
			name: 'skuContent',
			label: '',
			className: 'r-mb-8',
			children: <Input placeholder="别名" />,
		},
		{
			name: 'market',
			label: '',
			className: 'r-mb-8',
			children: <Input placeholder="市场" />,
		},
		{
			name: 'stall',
			label: '',
			className: 'r-mb-8',
			children: <Input placeholder="档口" />,
		},
		{
			name: 'supplierName',
			label: '',
			className: 'r-mb-8',
			children: <Input placeholder="供应商" />,
		},
		{
			name: 'existItemAlias',
			children: (
				<Select className={cs('r-w-full')} placeholder="是否设置简称" size="small" style={{ width: 160 }}>
					<Option value={null} key={0}>是否设置简称</Option>
					<Option value={0} key={1}>已设置简称</Option>
					<Option value={1} key={2}>未设置简称</Option>
				</Select>)
		},
		{
			name: 'existSkuAlias',
			children: (
				<Select className={cs('r-w-full')} placeholder="是否设置规格别名" size="small" style={{ width: 160 }}>
					<Option value={null} key={0}>是否设置规格别名</Option>
					<Option value={0} key={1}>已设置规格别名</Option>
					<Option value={1} key={2}>未设置规格别名</Option>
				</Select>)
		},
		{
			name: 'existWeight',
			children: (
				<Select className={cs('r-w-full')} placeholder="是否设置重量" size="small" style={{ width: 160 }}>
					<Option value={null} key={0}>是否设置重量</Option>
					<Option value={0} key={1}>已设置重量</Option>
					<Option value={1} key={2}>未设置重量</Option>
				</Select>)
		},
		{
			name: 'existMarket',
			children: (
				<Select className={cs('r-w-full')} placeholder="是否设置市场" size="small" style={{ width: 140 }}>
					<Option value={null} key={0}>是否设置市场</Option>
					<Option value={0} key={1}>已设置市场</Option>
					<Option value={1} key={2}>未设置市场</Option>
				</Select>)
		},
		{
			name: 'existStall',
			children: (
				<Select className={cs('r-w-full')} placeholder="是否设置档口" size="small" style={{ width: 140 }}>
					<Option value={null} key={0}>是否设置档口</Option>
					<Option value={0} key={1}>已设置档口</Option>
					<Option value={1} key={2}>未设置档口</Option>
				</Select>)
		},
		{
			name: 'existSupplier',
			children: (
				<Select className={cs('r-w-full')} placeholder="是否设置供应商" size="small" style={{ width: 140 }}>
					<Option value={null} key={0}>是否设置供应商</Option>
					<Option value={0} key={1}>已设置供应商</Option>
					<Option value={1} key={2}>未设置供应商</Option>
				</Select>)
		},
	];

	if (!userStore?.isShowZeroStockVersion) {
		FormFieldList.push(...StockSearchItems);
	} else if (pathname === "/distribution/DistributeGoodsManage") {
		FormFieldList.push(...zeroStockVersionSearchItems);
	}

	const afterSelectRows = async (selectRows, record: ItemRelationPlatformSystem) => {
		if (!selectRows.length) {
			message.error("请选择快递助手货品");
			return;
		}
		const { numIid: platformNumIid, platformItem } = record;
		const { skuId: platformSkuId, platform, sellerId } = platformItem;
		// 货品信息
		const { sysItemId, sysSkuId } = selectRows?.[0]?.sysSkuList?.[0];
		const commonParams = {
			sysItemId,
			sysSkuId,
			platformSkuId,
			platformNumIid,
			platform,
			sellerId,
		};
		const linkedItemNum = platformItem?.relationSystemItemList?.filter(j => (!j.isCheck))?.length;

		if (linkedItemNum && platformItem?.relationSystemItemList?.length) {
			const relationSysItem = platformItem?.relationSystemItemList?.[0];
			const params: any = {
				...commonParams,
				beforeChangeSysItemId: relationSysItem.sysItemId,
				beforeChangeSysSkuId: relationSysItem.sysItemId,
			};
			await ItemSysItemChangeItemRelationApi(params);
		} else {
			// 平台商品信息
			const params: ItemSysItemSaveItemRelationRequest = [{
				...commonParams
			}];
			await ItemSysItemSaveItemRelationApi(params);
		}
		ref.current.refresh();
		return true;

	};

	// 保存重量
	const onSaveWeight = async (e, record: ItemRelationPlatformSystem) => {
		if (e.key == 'Enter') {
			e.preventDefault();
		}
		// 延迟执行，非数字类型parser需要时间
		setTimeout(async () => {
			const { platformItem, numIid, isGoods } = record;
			const sysItem = platformItem.relationSystemItemList?.[0];
			const formName = nameFactory(record.rowKey, BatchTypeEnum.重量);

			let newVal = form.getFieldValue(formName); // e.target.value;
			let oldVal = sysItem.weight ?? '';
			// parser过，应该执行不到
			if (newVal > maxWeight) {
				message.error(isKg ? `重量不能超过${maxWeight / 1000}kg` : `重量不能超过${maxWeight}g`);
				setTimeout(() => {
					form.setFieldsValue({ [formName]: '' });
				}, 0);
				return;
			}
			if (["", null, undefined].includes(newVal)) {
				return;
			}
			if (Number(newVal) == Number(oldVal)) {
				return;
			}

			// Skip if the new value is invalid or unchanged
			if (["", null, undefined].includes(newVal) || Number(newVal) === Number(oldVal)) return;

			// Construct bindRelation object
			const bindRelation: BindRelationDTO = {
				numIid,
				skuId: isGoods ? '' : platformItem.skuId,
				operateType: isGoods ? OperateTypeEnum.绑定 : sysItem?.sysItemId ? OperateTypeEnum.绑定 : OperateTypeEnum.生成,
				weight: newVal,
				stall: sysItem?.stall,
				market: sysItem?.market,
				supplierName: sysItem?.supplierName,
			};

			// Add specific fields for goods
			if (isGoods) {
				Object.assign(bindRelation, {
					itemWeight: newVal,
				});
				// Remove irrelevant fields
				['weight', 'stall', 'market', 'supplierName'].forEach(key => delete bindRelation[key]);
			}

			if (isGoods) {
				bindRelation.skuId = sysItem.platformItemSkuList[0].skuId
			}


			const params = [{
				userViewVal: UserViewValEnum.供应商,
				saleUserId: form.getFieldValue('saleUserIdList')?.[0],
				supplierUserId: form.getFieldValue('supplierUserIdList')?.[0],
				platformType: isGoods ? sysItem.platform : platformItem.platform,
				sellerId: isGoods ? sysItem.sellerId : platformItem.sellerId,
				bindRelations: isGoods ? sysItem.platformItemSkuList.map(i => ({
					...bindRelation, skuId: i.skuId
				})) : [bindRelation],
			}];
			await UpdateSysItemByPlatformApi(params);
			message.success('修改成功');
		}, 100);

	};

	const onSavePrice = async (e, record: ItemRelationPlatformSystem) => {
		if (e.key == 'Enter') {
			e.preventDefault();
		}
		if (isDistributor) {
			sendPoint(Pointer.分销商角色_结算价设置_设置结算价_点击次数);
		} else {
			sendPoint(userStore.isShowZeroStockVersion
				? Pointer['供应商_代发商品管理_页面展现（零库存版）_点击-单个/批量设置结算价_点击']
				: Pointer['供应商_代发商品管理_点击-单个/批量设置结算价_点击']);
		}
		const { platformItem } = record;
		const formName = nameFactory(record.rowKey, BatchTypeEnum.结算价);
		let newVal = form.getFieldValue(formName) ?? '';
		if (newVal != '') {
			newVal = parseFloat(newVal).toFixed(2);
		}
		if (newVal > maxPrice) {
			message.error(`结算价不能超过${maxPrice}`);
			form.setFieldsValue({ [formName]: '' });
			return;
		}
		let oldVal = platformItem.balancePrice ?? '';
		if (isDistributor) {
			oldVal = platformItem.balancePriceSale ?? '';
		}
		if (["", null, undefined].includes(newVal)) {
			return;
		}
		if (!["", null, undefined].includes(oldVal) && Number(newVal) == Number(oldVal)) {
			return;
		}
		const skuUpdateItem: SkuUpdateItem = {
			numIid: record.numIid,
			skuId: platformItem.skuId,
			sellerId: platformItem.sellerId,
			platform: platformItem.platform,
			examinePrice: newVal,
			oldExaminePrice: oldVal,
		};

		try {
			await batchUpdateExaminePriceApi({
				balancePriceEnum:isDistributor?'SALE_BALANCE_PRICE':'SUPPLIER_BALANCE_PRICE',
				skuUpdateList: [skuUpdateItem],
				saleUserId: isDistributor?(record.saleUserId|| userStore?.userInfo?.userId):form.getFieldValue('saleUserIdList')?.[0],
				supplierUserId: form.getFieldValue('supplierUserIdList')?.[0]
			});
			message.success('设置结算价成功');
			getSetNum();
			setDataSource(pre => {
				let index = pre.findIndex(item => item.rowKey == record.rowKey);
				if (isSupplier) {
					pre[index].platformItem.balancePrice = newVal;
				} else {
					pre[index].platformItem.balancePriceSale = newVal;
				}
				return [...pre];
			});
		} catch (error) {
			console.log('error: ', error);
		}

	};

	const onFormInputBlur = async (e, type: BatchTypeEnum, record) => {
		if (e.key == 'Enter') {
			e.preventDefault();
		}
		setTimeout(async() => {
			const newVal = e.target.value;
			const { platformItem, numIid, isGoods } = record;
			const sysItem = platformItem.relationSystemItemList?.[0];
			const bindRelation: BindRelationDTO = {
				numIid,
				skuId: isGoods ? '' : platformItem.skuId,
				operateType: isGoods ? OperateTypeEnum.绑定 : sysItem?.sysItemId ? OperateTypeEnum.绑定 : OperateTypeEnum.生成,
				stall: sysItem?.stall,
				market: sysItem?.market,
				supplierName: sysItem?.supplierName,
			};
			const formName = nameFactory(record.rowKey, type);
			const prevVal = global_editMap[formName] ?? '';
			// Early return if value hasn't changed
			if (prevVal === newVal) return;
			// Map type-specific logic to bindRelation properties
			const typeMapping = {
				[BatchTypeEnum.简称]: () => (bindRelation.sysItemAlias = newVal),
				[BatchTypeEnum.别名]: () => (bindRelation.sysSkuAlias = newVal),
				[BatchTypeEnum.供应商]: () => {
					if (isGoods) bindRelation.itemSupplierName = newVal;
					else bindRelation.supplierName = newVal;
				},
				[BatchTypeEnum.档口]: () => {
					if (isGoods) bindRelation.itemStall = newVal;
					else bindRelation.stall = newVal;
				},
				[BatchTypeEnum.市场]: () => {
					if (isGoods) bindRelation.itemMarket = newVal;
					else bindRelation.market = newVal;
				},
				[BatchTypeEnum.吊牌价]: () => (bindRelation.tagPrice = newVal),
				[BatchTypeEnum.成本价]: () => {
					if (isGoods) bindRelation.itemCostPrice = newVal;
					else bindRelation.costPrice = newVal;
				},
			};
			if (typeMapping[type]) typeMapping[type]();
			if (isGoods) {
				bindRelation.skuId = sysItem.platformItemSkuList[0].skuId
			}

			const params:any = [{
				userViewVal: UserViewValEnum.供应商,
				saleUserId: form.getFieldValue('saleUserIdList')?.[0],
				supplierUserId: form.getFieldValue('supplierUserIdList')?.[0],
				platformType: isGoods ? sysItem.platform : platformItem.platform,
				sellerId: isGoods ? sysItem.sellerId : platformItem.sellerId,
				bindRelations: isGoods ? sysItem.platformItemSkuList.map(i => ({
					...bindRelation, skuId: i.skuId
				})) : [bindRelation],
			}];
			await UpdateSysItemByPlatformApi(params);
			global_editMap[formName] = newVal;
			message.success('修改成功');
		},100)
	};



	const EditIcon = ({ type }) => (
		<FormOutlined
			onClick={() => {
				if (selectedRows.length == 0) {
					return message.warn('需先勾选商品');
				}
				if (type == BatchTypeEnum.批量结算价) {
					sendPoint(userStore.isShowZeroStockVersion ? Pointer.无库存版_表头的_批量设置按钮 : Pointer.库存版_表头的_批量设置按钮);
				}
				setBatchEditModalInfo({ visible: true, type });
			}}
			className={s.titleIcon}
		/>
	);

	// group选择
	const onCheckedGroup = (e: CheckboxChangeEvent, record: ItemRelationPlatformSystem) => {
		setDataSource(prev => {
			prev.forEach(item => {
				if (item.fatherRowKey == record.rowKey && !item.checkDisabled) {
					item.isChecked = e.target.checked;
				}
			});
			return [...prev];
		});
	};
	// 单选
	const onCheckedItem = (e: CheckboxChangeEvent, record: ItemRelationPlatformSystem) => {
		setDataSource(prev => {
			let prevIndex = prev.findIndex(d => d.rowKey === record.rowKey);
			if (prevIndex > -1) {
				prev[prevIndex].isChecked = e.target.checked;
			}
			return [...prev];
		});
	};
	// 全选
	const onCheckAllChange = (e: CheckboxChangeEvent) => {
		setDataSource(prev => {
			prev.forEach(item => {
				if (!item.checkDisabled && !item.isGoods) {
					item.isChecked = e.target.checked;
				}
			});
			return [...prev];
		});
	};

	const checkedGroup = useMemo(() => {
		let filterList = dataSource.filter(item => !item.isGoods);
		const tmp = { num: 0, checked: false, disabled: false, disNum: 0, indeterminate: false };
		if (filterList.length == 0) return { group_all: tmp };
		const map = {
			'group_all': { ...tmp, total: filterList.length }
		};
		filterList.forEach(item => {
			if (!map[item.fatherRowKey]) {
				map[item.fatherRowKey] = {
					...tmp,
					total: item.platformItemSkuList.length
				};
			}

			if (item.checkDisabled) {
				map[item.fatherRowKey].disNum += 1;
				map['group_all'].disNum += 1;
			}
			if (item.isChecked) {
				map[item.fatherRowKey].num++;
				map['group_all'].num++;
			}
		});
		for (let key in map) {
			const { disNum, total, num } = map[key];
			if (disNum == total) {
				map[key].disabled = true;
			}
			if (num == total || (num > 0 && num + disNum == total)) {
				map[key].checked = true;
			}
			if (num > 0 && num + disNum < total) {
				map[key].indeterminate = true;
			}
		}

		return map;
	}, [dataSource]);

	const selectedRows = useMemo(() => {
		return dataSource.filter(item => !item.isGoods && item.isChecked);
	}, [dataSource]);

	// 关闭提醒
	const confirmTip = async (record) => {
		const { platformItem, numIid } = record;
		const params = {
			saleUserId: form.getFieldValue('saleUserIdList')?.[0],
			supplierUserId: userStore?.userInfo?.userId,
			skuList: [{
				skuId: platformItem.skuId,
				numIid,
				sellerId: platformItem.sellerId,
				platform: platformItem.platform
			}]
		};
		await batchCloseSkuRemindApi(params);
		setDataSource(pre => {
			const index = pre.findIndex(item => item.rowKey == record.rowKey);
			if (index >= 0) {
				pre[index].platformItem.modification = false;
			}
			return [...pre];
		});
		// ref?.current?.refresh();
	};

	const inputWeightProps = isKg ? {
		formatter: (value: any) => (value === '' ? value : +(value / 1000).toFixed(3)),
		parser: (value: any) => (value === '' ? value : accMul(value, 1000)),
	} : {
		precision: 0
	};

	// 表头
	const columnsMap = {
		[ColumnName['商品/规格']]: {
			title: (
				<span className="r-flex">
					<Checkbox
						className="r-mr-5  r-as-c"
						checked={checkedGroup.group_all.checked}
						disabled={checkedGroup.group_all.disabled}
						indeterminate={checkedGroup.group_all.indeterminate}
						onChange={onCheckAllChange}
					/>

					商品/规格

					<span
						className={s.collapseAllText}
						onClick={changeAllCollapse}
					>
						全部收起/展开
						{isAllCollapse ? <DownOutlined /> : <UpOutlined />}
					</span>
				</span>
			),
			width: 460,
			dataIndex: 'numIid',
			render(value, record, index) {

				const { platform, platformItem, isGoods } = record;
				let id = record.numIid;
				if ([PLAT_XHS, PLAT_JD].includes(platform)) {
					id = record.platformItem?.skuId ?? record.platformItemSkuList?.[0].skuId;
				}
				if ([PLAT_YZ].includes(platform)) {
					id = record.platformItem?.skuUuid ?? record.platformItemSkuList?.[0].skuUuid;
				}
				const goodsDetailUrl = getPlatformDetailLink(platform, id);

				// 商品
				if (isGoods) {


					return (
						<div className="r-flex ">
							<Checkbox
								className="r-mr-5 r-as-c"
								disabled={checkedGroup[record.rowKey]?.disabled}
								onChange={e => onCheckedGroup(e, record)}
								checked={checkedGroup[record.rowKey]?.checked}
								indeterminate={checkedGroup[record.rowKey]?.indeterminate}
							/>

							{getImageNode(record.itemPicUrl, goodsDetailUrl)}
							<div className="r-flex-1 r-flex r-fd-c r-jc-sb">
								<div >{record.title}</div>
								<div className="r-c-999" >
									ID: {record.numIid}
									<span className={s.collapseText} onClick={() => changeCollapse(record.rowKey)}>
										{record.isCollapse
											? <span>显示规格<DownOutlined /></span>
											: <span>收起规格<UpOutlined /></span>}
									</span>
								</div>
							</div>
							<div className="r-flex r-ml-10" style={ { width: '140px' } }>
								<PlatformIcon platform={record?.platform} />{isSupplier ? !userStore.hasFieldsPermission(FieldsPermissionEnum.供应商店铺信息) && isSourceScm(record)? '****' : record?.sellerNick : record.sellerNick }
							</div>
						</div>
					);
				}
				// 规格
				if (platformItem) {

					const modifiedNotice = (
						<Popover
							color="#fff"
							trigger="hover"
							content={(
								<span>
									我知道了，
									<span className="r-click" onClick={() => confirmTip(record)}>关闭提醒</span>
								</span>
							)}
						>
							<span className={s.skuNotice}>平台规格名称有改动</span>
						</Popover>
					);
					return (
						<div className="r-flex" style={{ paddingLeft: '40px' }}>
							<Checkbox
								className="r-mr-5 r-as-c"
								disabled={record.checkDisabled}
								onChange={e => onCheckedItem(e, record)}
								checked={record.isChecked}
							/>
							{getImageNode(platformItem.picUrl, goodsDetailUrl)}
							<div className="r-flex-1 r-flex r-fd-c r-jc-sb">
								<div>
									{platformItem.skuName}
									{platformItem.modification ? modifiedNotice : null}

								</div>

								<div className="r-c-999">skuID: {platformItem.skuId}</div>
							</div>
						</div>
					);
				}

			},
		},
		[ColumnName['商家编码/规格编码']]: {
			title: '商家编码/规格编码',
			width: 130,
			dataIndex: 'saleName',
			render(value, record, index) {
				if (record.isGoods) {
					return record.outerId || '无';
				}
				return record.platformItem?.skuOuterId || '无';
			},
		},
		[ColumnName.分销商结算价]: {
			title: (
				<div className="r-flex r-ai-c">
					分销商结算价
					<Tooltip title="分销商发起结算时，出账单会以此列价格为准。若对此价格有疑义，请联系分销商进行调整">
						<span className="r-pointer r-c-999 r-ml-4">
							<Icon type="wenhao-xian" size={15} />
						</span>

					</Tooltip>
				</div>
			),
			width: 140,
			dataIndex: 'balancePriceSale',
			render(value, record) {
				if (!record.fatherRowKey) {
					return;
				}
				if (value) {
					return Number(value).toFixed(2);
				}
				return "对方未设置";
			}
		},
		[ColumnName.供应商结算价]: {
			title: (
				<div className="r-flex r-ai-c">
					供应商结算价
					<Tooltip title="供应商发起结算时，出账单会以此列价格为准。若对此价格有疑义，请联系供应商进行调整">
						<span className="r-pointer r-c-999 r-ml-4">
							<Icon type="wenhao-xian" size={15} />
						</span>

					</Tooltip>
				</div>
			),
			width: 140,
			dataIndex: 'balancePrice',
			render(value, record) {
				if (!record.fatherRowKey) {
					return;
				}
				if (value) {
					return Number(value).toFixed(2);
				}
				return "对方未设置";
			}
		},
		[ColumnName.结算价]: {
			title: (
				<div className="r-flex r-ai-c">
					结算价<EditIcon type={BatchTypeEnum.批量结算价} />
					<Tooltip title="我方发起结算时，会以此列价格进行出账">
						<span className="r-pointer r-c-999 r-ml-4 r-mt-4">
							<Icon type="wenhao-xian" size={14} />
						</span>

					</Tooltip>
				</div>
			),
			width: 150,
			dataIndex: 'price',
			render(value, record) {
				if (record.isGoods) {
					return (
						<div
							className="r-click"
							onClick={() => {
								setCurRow(record);
								setBatchEditModalInfo({ visible: true, type: BatchTypeEnum.结算价 });
								if (isDistributor) {
									sendPoint(Pointer.分销商角色_结算价设置_批量设置结算价_点击次数);
								} else {
									sendPoint(userStore.isShowZeroStockVersion
										? Pointer['供应商_代发商品管理_页面展现（零库存版）_点击-单个/批量设置结算价_点击']
										: Pointer['供应商_代发商品管理_点击-单个/批量设置结算价_点击']);
								}
							}}
						>
							批量设置结算价
						</div>
					);
				}
				const name = nameFactory(record.rowKey, BatchTypeEnum.结算价);
				return (
					<Form.Item
						shouldUpdate
						name={name}
						className={cs('r-mb-0', 'r-mt-2')}
					>
						<InputNumber
							placeholder="结算价"
							precision={2}
							min={0}
							// max={ 999999.99 }
							style={{ width: '100%' }}
							onBlur={(e) => onSavePrice(e, record)}
							onPressEnter={(e) => onSavePrice(e, record)}
						/>
					</Form.Item>
				);
			}
		},
		[ColumnName.成本价]: {
			title: (
				<div className="r-flex r-ai-c">
					成本价<EditIcon type={BatchTypeEnum.成本价} />
				</div>
			),
			width: 150,
			dataIndex: 'costPrice',
			render(value, record) {
				if (record.isGoods) {
					return '--';
				}
				const name = nameFactory(record.rowKey, BatchTypeEnum.成本价);
				return (
					<Form.Item
						shouldUpdate
						name={name}
						className={cs('r-mb-0', 'r-mt-2')}
					>
						<InputNumber
							placeholder="成本价"
							precision={2}
							min={0}
							// max={ 999999.99 }
							style={{ width: '100%' }}
							onBlur={(e) => onFormInputBlur(e, BatchTypeEnum.成本价, record)}
							onPressEnter={(e) => onFormInputBlur(e, BatchTypeEnum.成本价, record)}
						/>
					</Form.Item>
				);
			}
		},
		[ColumnName.吊牌价]: {
			title: (
				<div className="r-flex r-ai-c">
					吊牌价<EditIcon type={BatchTypeEnum.吊牌价} />
					<Tooltip title="我方发起结算时，会以此列价格进行出账">
						<span className="r-pointer r-c-999 r-ml-4 r-mt-4">
							<Icon type="wenhao-xian" size={14} />
						</span>

					</Tooltip>
				</div>
			),
			width: 150,
			dataIndex: 'tagPrice',
			render(value, record) {
				if (record.isGoods) {
					return '--';
				}
				const name = nameFactory(record.rowKey, BatchTypeEnum.吊牌价);
				return (
					<Form.Item
						shouldUpdate
						name={name}
						className={cs('r-mb-0', 'r-mt-2')}
					>
						<InputNumber
							placeholder="吊牌价"
							precision={2}
							min={0}
							// max={ 999999.99 }
							style={{ width: '100%' }}
							onBlur={(e) => onFormInputBlur(e, BatchTypeEnum.吊牌价, record)}
							onPressEnter={(e) => onFormInputBlur(e, BatchTypeEnum.吊牌价, record)}
						/>
					</Form.Item>
				);
			}
		},
		[ColumnName.市场]: {
			title: <>市场<EditIcon type={BatchTypeEnum.市场} /></>,
			width: 150,
			dataIndex: 'market',
			render: (text, record) => {
				if (record.isGoods) {
					return '--';
				}
				const name = nameFactory(record.rowKey, BatchTypeEnum.市场);
				return (
					<Form.Item
						shouldUpdate
						name={name}
						className={cs('r-mb-0', 'r-mt-2')}
					>
						<Input
							onBlur={(e) => onFormInputBlur(e, BatchTypeEnum.市场, record)}
							onPressEnter={(e) => onFormInputBlur(e, BatchTypeEnum.市场, record)}
							placeholder="请输入市场"
							maxLength={60}
							style={{ width: '100%' }}
						/>
					</Form.Item>
				);
			}
		},
		[ColumnName.档口]: {
			title: <>档口<EditIcon type={BatchTypeEnum.档口} /></>,
			width: 150,
			dataIndex: 'stall',
			render: (text, record) => {
				if (record.isGoods) {
					return '--';
				}
				const name = nameFactory(record.rowKey, BatchTypeEnum.档口);
				return (
					<Form.Item
						shouldUpdate
						name={name}
						className={cs('r-mb-0', 'r-mt-2')}
					>
						<Input
							onBlur={(e) => onFormInputBlur(e, BatchTypeEnum.档口, record)}
							onPressEnter={(e) => onFormInputBlur(e, BatchTypeEnum.档口, record)}
							placeholder="请输入档口"
							maxLength={60}
							style={{ width: '100%' }}
						/>
					</Form.Item>
				);
			}
		},
		[ColumnName.供应商]: {
			title: <>供应商<EditIcon type={BatchTypeEnum.供应商} /></>,
			width: 150,
			dataIndex: 'supplierName',
			render: (text, record) => {
				if (record.isGoods) {
					return '--';
				}
				const name = nameFactory(record.rowKey, BatchTypeEnum.供应商);
				return (
					<Form.Item
						shouldUpdate
						name={name}
						className={cs('r-mb-0', 'r-mt-2')}
					>
						<Input
							onBlur={(e) => onFormInputBlur(e, BatchTypeEnum.供应商, record)}
							onPressEnter={(e) => onFormInputBlur(e, BatchTypeEnum.供应商, record)}
							placeholder="请输入供应商"
							maxLength={60}
							style={{ width: '100%' }}
						/>
					</Form.Item>
				);
			}
		},
		[ColumnName.简称]: {
			title: <>简称<span hidden={isDistributor}><EditIcon type={BatchTypeEnum.简称} /></span></>,
			width: 150,
			dataIndex: 'sysItemAlias',
			render: (text, record) => {
				if (isDistributor) {
					if (record.isGoods) {
						return record?.platformItemSkuList?.[0]?.itemAlias;
					} else {
						return '--';
					}
				}
				if (record.isGoods) {
					return '--';
				}
				const name = nameFactory(record.rowKey, BatchTypeEnum.简称);
				return (
					<Tooltip title={dataFormSource?.[name]} trigger="hover">
						<Form.Item
							shouldUpdate
							name={name}
							className={cs('r-mb-0', 'r-mt-0')}
						>
							<Input
								onBlur={(e) => onFormInputBlur(e, BatchTypeEnum.简称, record)}
								onPressEnter={(e) => onFormInputBlur(e, BatchTypeEnum.简称, record)}
								placeholder="请输入简称"
								maxLength={60}
								style={{ width: '100%' }}
							/>
						</Form.Item>
					</Tooltip>
				);
			}
		},
		[ColumnName.规格别名]: {
			title: <>规格别名<span hidden={isDistributor}><EditIcon type={BatchTypeEnum.别名} /></span></>,
			width: 150,
			dataIndex: 'sysSkuAlias',
			render: (text, record) => {
				if (record.isGoods) {
					return '--';
				}
				if (isDistributor) {
					return record?.platformItem?.skuAlias;
				}
				const name = nameFactory(record.rowKey, BatchTypeEnum.别名);
				return (
					<Tooltip title={dataFormSource?.[name]} trigger="hover">
						<Form.Item
							shouldUpdate
							name={name}
							className={cs('r-mb-0', 'r-mt-0')}
						>
							<Input
								onBlur={(e) => onFormInputBlur(e, BatchTypeEnum.别名, record)}
								onPressEnter={(e) => onFormInputBlur(e, BatchTypeEnum.别名, record)}
								placeholder="请输入规格别名"
								maxLength={60}
								style={{ width: '100%' }}
							/>
						</Form.Item>
					</Tooltip>
				);
			}
		},
		[ColumnName.日志]: {
			title: userStore.isShowZeroStockVersion ? '操作' : '',
			fixed: userStore.isShowZeroStockVersion ? 'right' : '',
			width: 70,
			dataIndex: 'rowKey',
			render(value, record, index) {
				if (record.isGoods) return '';

				// 如果全部是库存版且真实绑定的商品就禁止删除
				const disabled = !userStore.isShowZeroStockVersion && record?.platformItem?.relationSystemItemList?.length && record?.platformItem?.relationSystemItemList?.every(j => !j.isCheck);
				return (
					<>
						<Button
							style={{ margin: 0, padding: 0, fontSize: 12, marginRight: 8 }}
							type='link'
							className="r-click r-mr-8"
							onClick={() => {
								if (isDistributor) {
									sendPoint(Pointer.分销商角色_结算价设置_日志_点击次数);
								} else {
									sendPoint(userStore.isShowZeroStockVersion
										? Pointer['供应商_代发商品管理_页面展现（零库存版）_点击-日志_点击']
										: Pointer['供应商_代发商品管理_点击-日志_点击']);
								}
								setRecordLogModalData(record);
							}}
						>日志</Button>
						{
							supplierRoutes.includes(pathname)
								? <Tooltip title={disabled ? "已绑定关联关系，请解绑后删除" : null}>
									<Button
										type='link'
										style={{ margin: 0, padding: 0, fontSize: 12 }}
										className="r-click"
										disabled={disabled}
										onClick={() => {
											handleBatchDelete([record], true)
										}}
									>删除</Button>
								</Tooltip>
								: null}
					</>
				);
			},
		},
		[ColumnName.关联本地货品]: {
			title: '关联本地货品',
			dataIndex: 'status',
			width: 350,
			fixed: 'right',

			render: (value, record, index) => {
				const { platformItem } = record;
				const totalLinkedItemNum = record.platformItemSkuList?.filter(i => i.relationSystemItemList?.filter(j => (!j.isCheck))?.length).length;
				if (record.isGoods) {
					return `已关联${totalLinkedItemNum}个货品`;
				}

				const linkedItemNum = platformItem?.relationSystemItemList?.filter(j => (!j.isCheck))?.length;
				// console.log('record', record, index, record.platformItemSkuList);
				const openProductNode = () => {
					return (
						<ProductListModal
							type="radio"
							getContainer={() => document.body}
							beforeOk={(selectRows) => {
								return afterSelectRows(selectRows, record);
							}}
						>
							{linkedItemNum && platformItem?.relationSystemItemList?.length ? '更换' : '添加'}
						</ProductListModal>
					);
				};
				if (platformItem.relationSystemItemList?.length && linkedItemNum) {
					const sysItem: RelationSystemItemSku = platformItem.relationSystemItemList?.[0];
					return (
						<div className="r-flex r-jc-sb">
							<div className="r-flex r-mr-10">
								{getImageNode(sysItem.picUrl)}
								<div className="r-flex r-fd-c r-jc-sb">
									<div>货品规格编码：{sysItem.skuOuterId}</div>
									<div>简称：{sysItem.sysItemAlias}；规格名称：{sysItem.sysSkuName}</div>
								</div>
							</div>
							<div className="r-click r-flex r-ai-c r-mr-10" style={{ wordBreak: 'keep-all' }}>
								{openProductNode()}
								<span
									className="r-ml-10"
									onClick={() => {
										Modal.confirm({
											title: '解除提示',
											content: '确定解除该代发商品与快递助手货品的关联关系吗？',
											okText: '确认解除',
											onOk: () => {
												ItemSysItemDeleteItemRelationWithBatchApi(
													{
														reqDeleteItemRelationDTOs: [
															{
																sysItemId: sysItem.sysItemId,
																sysSkuId: sysItem.sysSkuId,
																platformSkuId: platformItem.skuId,
																platformNumIid: record.numIid,
																platform: platformItem.platform,
																sellerId: platformItem.sellerId
															}
														]
													}
												).then(res => {
													ref.current.refresh();
												});
											}
										});
									}}
								>解绑
								</span>
							</div>
						</div>
					);
				}
				return (
					<div className="r-flex r-jc-sb r-ai-c">
						<div className="r-c-999">未关联</div>
						<div className="r-click r-mr-10">{openProductNode()}</div>
					</div>
				);
			}
		},
		[ColumnName.重量]: {
			title: (
				<div className="r-flex r-ai-c">
					重量({isKg ? 'kg' : 'g'})<EditIcon type={BatchTypeEnum.重量} />
				</div>
			),
			width: 150,
			dataIndex: 'weight',
			render(value, record) {
				if (record.isGoods) {
					return '--';
				}
				const name = nameFactory(record.rowKey, BatchTypeEnum.重量);
				return (
					<Form.Item
						shouldUpdate
						name={name}
						className={cs('r-mb-0', 'r-mt-2')}
					>
						<WeightInput isKg={isKg} max={999999} width={'100%'} placeholder="请输入重量" onBlur={(e) => onSaveWeight(e, record)}
							onPressEnter={(e) => onSaveWeight(e, record)}/>
					</Form.Item>
				);
			}
		},
	};

	let columnKeys = [];
	if (userStore.isShowZeroStockVersion) {
		columnKeys = [
			ColumnName['商品/规格'],
			ColumnName['商家编码/规格编码'],
			ColumnName.简称,
			ColumnName.规格别名,
			ColumnName.结算价,
			isSupplier ? ColumnName.分销商结算价 : ColumnName.供应商结算价,
			DistributorRoutes.includes(pathname) ? false : ColumnName.成本价,
			DistributorRoutes.includes(pathname) ? false : ColumnName.吊牌价,
			DistributorRoutes.includes(pathname) ? false : ColumnName.市场,
			DistributorRoutes.includes(pathname) ? false : ColumnName.档口,
			DistributorRoutes.includes(pathname) ? false : ColumnName.供应商,
			ColumnName.日志
		].filter(e => e !== false);
	} else if (isDistributor) {
		columnKeys = [
			ColumnName['商品/规格'],
			ColumnName['商家编码/规格编码'],
			ColumnName.简称,
			ColumnName.规格别名,
			ColumnName.结算价,
			isSupplier ? ColumnName.分销商结算价 : ColumnName.供应商结算价,
			ColumnName.日志
		];
	} else {
		columnKeys = [
			ColumnName['商品/规格'],
			ColumnName['商家编码/规格编码'],
			ColumnName.结算价,
			isSupplier ? ColumnName.分销商结算价 : ColumnName.供应商结算价,
			ColumnName.日志,
			ColumnName.关联本地货品
		];
	}

	// 零库存版供应商
	if (userStore.isShowZeroStockVersion && isSupplier) {
		columnKeys.splice(4, 0, ColumnName.重量);
	}

	const columns = columnKeys.map(key => columnsMap[key]);


	// 批量设置结算价
	const handleBatchPrice = async (val: AddContentType, type: BatchTypeEnum) => {
		const { priceCalType } = val as CalPriceParams;
		let { addContent = '' } = val;
		if (addContent && Number(addContent) >= 0) {
			addContent = parseFloat(addContent).toFixed(2);
		}
		const newFormVal = { ...dataFormSource, ...form.getFieldsValue() };
		const skuUpdateList = [];
		const newDataItem = {}; //  dataSource数据更新，用于前后数据日志对比
		let operateList = selectedRows;
		if (type == BatchTypeEnum.结算价) {
			operateList = curRow.platformItemSkuList.map((platformItem, platformIndex) => ({
				...curRow,
				platformItem,
				rowKey: `${curRow.rowKey}_${platformIndex}`
			}));
		}
		operateList.forEach(row => {
			const platformItem = row.platformItem;
			const formName = nameFactory(`${row.rowKey}`, BatchTypeEnum.结算价);
			const oldVal = form.getFieldValue(formName) ?? '';
			if (priceCalType == PriceBatchType.按公式修改) {
				addContent = getFormulaPrice(val as CalPriceParams, platformItem?.relationSystemItemList?.[0]);
			}
			newFormVal[formName] = addContent;
			newDataItem[`${row.rowKey}`] = addContent;
			skuUpdateList.push({
				numIid: row.numIid,
				skuId: platformItem.skuId,
				sellerId: platformItem.sellerId,
				platform: platformItem.platform,
				examinePrice: addContent,
				oldExaminePrice: oldVal,
			});
		});
		await batchUpdateExaminePriceApi({
			skuUpdateList,
			balancePriceEnum:isDistributor?'SALE_BALANCE_PRICE':'SUPPLIER_BALANCE_PRICE',
			saleUserId: isDistributor? (curRow?.saleUserId || userStore?.userInfo?.userId):form.getFieldValue('saleUserIdList')?.[0],
			supplierUserId: form.getFieldValue('supplierUserIdList')?.[0]
		});
		message.success('批量设置结算价成功');
		form.setFieldsValue(newFormVal);
		setDataFormSource(pre => ({ ...pre, ...newFormVal }));
		setDataSource(pre => {
			pre.forEach((item) => {
				if (newDataItem[item.rowKey]) {
					if (isSupplier) {
						item.platformItem.balancePrice = newDataItem[item.rowKey];
					} else {
						item.platformItem.balancePriceSale = newDataItem[item.rowKey];
					}
				}
			});

			return [...pre];
		});
		getSetNum();
	};

	// 批量编辑简称、别名
	const handleBatchName = async (val: AddContentType, type: BatchTypeEnum) => {
		const { addContent, addType = addPosMap.覆盖 } = val;
		const newFormVal = { ...dataFormSource, ...form.getFieldsValue() };
		const rows = selectedRows;
		const params = [];
		rows.forEach((row: ItemRelationPlatformSystem, skuIndex) => {
			const formName = nameFactory(row.rowKey, type);
			const oldVal = newFormVal[formName] || '';
			let newVal = getNewContent(addContent, { ...row }, type) || '';
			if (addType == addPosMap.现有前) {
				newVal += oldVal;
			}
			if (addType == addPosMap.现有后) {
				newVal = oldVal + newVal;
			}
			newFormVal[formName] = newVal.toString().substring(0, 60);

			// 参数
			const sysItem = row.platformItem.relationSystemItemList?.[0];

			const bindRelation: BindRelationDTO = {
				numIid: row.numIid,
				skuId: row.platformItem.skuId,
				operateType: sysItem?.sysItemId ? OperateTypeEnum.绑定 : OperateTypeEnum.生成,
				userViewVal: UserViewValEnum.供应商,
				saleUserId: form.getFieldValue('saleUserIdList')?.[0],
				supplierUserId: form.getFieldValue('supplierUserIdList')?.[0],
				sellerId: row.platformItem.sellerId,
				platformType: row.platformItem.platform,
				stall: sysItem?.stall,
				market: sysItem?.market,
				supplierName: sysItem?.supplierName,
			};
			if (type == BatchTypeEnum.简称) {
				bindRelation.sysItemAlias = newFormVal[formName];
			}
			if (type == BatchTypeEnum.别名) {
				bindRelation.sysSkuAlias = newFormVal[formName];
			}
			if (type == BatchTypeEnum.市场) {
				bindRelation.market = newFormVal[formName];
			}
			if (type == BatchTypeEnum.档口) {
				bindRelation.stall = newFormVal[formName];
			}
			if (type == BatchTypeEnum.供应商) {
				bindRelation.supplierName = newFormVal[formName];
			}
			if (type == BatchTypeEnum.吊牌价) {
				bindRelation.tagPrice = newFormVal[formName];
			}
			if (type == BatchTypeEnum.成本价) {
				bindRelation.costPrice = newFormVal[formName];
			}
			params.push({ ...bindRelation });
		});
		try {
			await BatchUpdateSysItemByPlatformApi(params);
			message.success("修改成功");
			ref?.current?.submit();
		} catch (error) {
			message.error("修改失败：", error.message || "'");
		}
		global_newFormVal = newFormVal;
	};

	// 批量编辑重量，用的简称、别名一样的接口
	const handleBatchWeight = async (val: AddContentType, type: BatchTypeEnum) => {
		const params = [];
		selectedRows.forEach((row: ItemRelationPlatformSystem, skuIndex) => {
			// 参数
			const sysItem = row.platformItem.relationSystemItemList?.[0];
			const bindRelation: BindRelationDTO = {
				numIid: row.numIid,
				skuId: row.platformItem.skuId,
				operateType: sysItem?.sysItemId ? OperateTypeEnum.绑定 : OperateTypeEnum.生成,
				userViewVal: UserViewValEnum.供应商,
				saleUserId: form.getFieldValue('saleUserIdList')?.[0],
				supplierUserId: form.getFieldValue('supplierUserIdList')?.[0],
				sellerId: row.platformItem.sellerId,
				platformType: row.platformItem.platform,
				weight: val?.weight,
				stall: sysItem.stall,
				market: sysItem.market,
				supplierName: sysItem.supplierName,
			};
			params.push({ ...bindRelation });
		});
		try {
			await BatchUpdateSysItemByPlatformApi(params);
			message.success("修改成功");
			ref?.current?.submit();
		} catch (error) {
			message.error("修改失败：", error.message || "'");
		}
	};

	const onBatchEditOK = async (val: AddContentType, type: BatchTypeEnum) => {
		if (type == BatchTypeEnum.结算价 || type == BatchTypeEnum.批量结算价) {
			await handleBatchPrice(val, type);
		} else if (type == BatchTypeEnum.重量) {
			await handleBatchWeight(val, type);
		} else if (type == BatchTypeEnum.成本价) {
			await handleBatchName(val, type);
		} else if (type == BatchTypeEnum.吊牌价) {
			await handleBatchName(val, type);
		} else {
			await handleBatchName(val, type);
		}
	};

	const handleChangeAutoLink = () => {
		editAutoLinkForm.setFieldsValue({ autoMatchSkuOuterId });
		Modal.confirm({
			title: '相同规格编码自动关联',
			icon: null,
			width: 450,
			centered: true,
			content: (
				<>
					<Form className="r-mt-18" form={editAutoLinkForm}>
						<Form.Item label="" name="autoMatchSkuOuterId" >
							<Radio.Group className="r-flex r-fd-c">
								<Radio className="r-m-tb-12" type="radio" value={0} >关闭自动关联</Radio>
								<Radio type="radio" value={1}>开启自动关联</Radio>
							</Radio.Group>
						</Form.Item>
						<p className="r-c-666 r-pl-12">开启后，新增代发商品/规格时，若规格编码与本地货品规格编码相同时，自动建立关联关系</p>
					</Form>
				</>
			),
			async onOk() {
				try {
					const params = editAutoLinkForm.getFieldsValue();
					const res = await ItemScmConfigSaveOrUpdateConfigApi({ itemScmConfigEnum: 相同规格编码自动关联, values: params?.autoMatchSkuOuterId });
					setAutoMatchSkuOuterId(params?.autoMatchSkuOuterId);
					return Promise.resolve();
				} catch (error) {
					return Promise.reject();
				}

			},
			onCancel() {

			}
		});
	};

	const handleBatchDelete = (rows, isSingle = false) => {
		let skuIdList = [];
		let hasRelationSystemItem = false;
		rows.forEach(item => {
			if (!userStore.isShowZeroStockVersion && item?.platformItem?.relationSystemItemList?.length && item?.platformItem?.relationSystemItemList?.filter(j => (!j.isCheck))?.length > 0) {
				hasRelationSystemItem = true;
			} else {
				skuIdList.push({
					numIid: item.numIid,
					skuId: item?.platformItem?.skuId,
					sellerId:item?.sellerId
				})
			}
		});
		if (!skuIdList.length && hasRelationSystemItem) {
			message.warning(
				{
					content: (
						<div className='r-flex r-ai-c'>
							<ExclamationCircleOutlined />
							<div className='r-ml-4'>
								<p className='r-ta-l r-c-333'>请选择勾选符合条件的商品</p>
								<p className='r-c-error r-ta-l'>注：已绑定的商品需解绑后才允许删除</p>
							</div>
						</div>
					),
					icon: <></>,
					className: s["DistributeGoodsManageWarning"],
				}
			);
			return;
		} else if (!skuIdList.length) {
			message.warning("请选择商品");
			return;
		}
		Modal.confirm({
			centered: true,
			title: '提示',
			icon: <ExclamationCircleOutlined />,
			content: <>
				确定删除这{skuIdList.length}种平台商品吗？<br />
				删除仅对系统内生效，不删除平台商品，如需维护此商品关联关系可重新下载该商品<br />
				{
					!isSingle ? <p className='r-c-error'>注：已绑定关联关系的商品，请解绑后删除</p> : null
				}
			</>,
			okText: '确认',
			cancelText: '取消',
			onOk: async () => {
				await batchDeleteBySkuIdsApi({ skuDTOList: skuIdList });
				message.success('删除成功')
				ref.current.refresh();
			}
		});
	}

	const getExpandContext = () => {
		return (
			<div className="r-flex r-jc-sb">
				<div className="r-flex">
					{isSupplier && <ImportGoodsFromDistribution size='middle' data={global_curSearch} />}
					{isSupplier && <Button size='middle' className="r-flex r-jc-sb r-ai-c r-ml-12" onClick={() => { handleChangeAutoLink(); }}>相同规格编码自动关联 <Switch defaultChecked checked={autoMatchSkuOuterId == 1} className="r-ml-4" /></Button>}
					{DistributorRoutes.includes(pathname) ? null : <Button className='r-ml-12' size='middle' onClick={() => handleBatchDelete(selectedRows)}>批量删除</Button>}
				</div>

				<div className="r-c-999 r-ml-12">说明：仅显示90天有订单的商品/规格</div>
			</div>
		);
	};

	const onFieldsChange = (changedValues, allValues) => {
		setDataFormSource(pre => ({ ...pre, ...allValues }));
		if (
			'saleUserIdList' in changedValues || "supplierUserIdList" in changedValues
		) {
			ref?.current?.submit();
		}
	};

	const dataSourceWithCollapse = useMemo(() => {
		const newDataSource = [];
		let isCollapse = false;
		dataSource.forEach((row, ix) => {
			if (row.isGoods) {
				newDataSource.push(row);
				isCollapse = row.isCollapse;
			} else if (!isCollapse) {
				newDataSource.push(row);
			}
		});

		return newDataSource;
	}, [dataSource]);


	useEffect(() => {
		userStore.getUserSetting().then(res => {
			setIsKg(res?.weightUnit == weightUnit.显示kg);
		});
	}, []);

	return (
		<NormalLayout className={cs('r-w-full',s.noDividerTable)} >
			<SearchTable
				ref={ref}
				pageSizeId={isSupplier ? "mySupplierTable" : "myDistributorTable"}
				form={form}
				fetchData={isSupplier ? fetchSystemList : fetchDataWhenDistributor}
				responseAdapter={responseAdapter}
				searchBtnText="查询"
				resetBtnText="重置"
				showSearch
				rowFormConfig={{
					defaultParams: {
						saleUserIdList: [distributorList?.[0]?.saleUserId],
						supplierUserIdList: [supplierList?.[0]?.supplierUserId]
					},
					formList: FormFieldList,
					size: 'small',
					colProps: {
					}
				}}
				baseTableConfig={{
					onFieldsChange,
					dataSource: dataSourceWithCollapse,
					rowKey: 'rowKey',
					columns,
					pagination: false,
					cachePgination: true,
					expandContext: getExpandContext(),
					expandContextStyle: {
						paddingBottom:0
					}
					// showSelectedCount: getPageExtraContent()
				}}
			/>
			<BatchEditModal
				visible={batchEditModalInfo.visible}
				type={batchEditModalInfo.type}
				onCancel={() => setBatchEditModalInfo({})}
				onOk={onBatchEditOK}
			/>
			<Modal
				centered
				visible={!!recordLogModalData}
				footer={false}
				onCancel={() => { setRecordLogModalData(null); }}
				width={1000}
				destroyOnClose
				maskClosable={false}
			>
				<SysSkuLogs
					data={recordLogModalData}
					saleUserId={form.getFieldValue('saleUserIdList')?.[0]}
					supplierUserId={form.getFieldValue('supplierUserIdList')?.[0]}
				/>
			</Modal>
			<ProgressModal
				loopObj={{
					...loopObj,
					api: ItemAsyncGetProgressApi
				}}
				modalProps={{
					title: '进度查询'
				}}
				onClose={() => {
					setLoopObj(prev => ({
						...prev,
						isStart: false,
					}));
				}}
				onSuccess={(res) => {
					setLoopObj(prev => ({
						...prev,
						isStart: false,
					}));
					form.setFieldsValue(global_newFormVal);
					setDataFormSource(pre => ({ ...pre, ...global_newFormVal }));
					global_editMap = { ...global_editMap, ...global_newFormVal };
				}}
			/>
		</NormalLayout>
	);
};

export default observer(MyDistributorPage);
