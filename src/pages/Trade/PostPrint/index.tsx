/*
 * @Author: 唐荣洋 <EMAIL>
 * @Date: 2025-08-11 16:29:32
 * @Description:
 */
import React, { useEffect } from "react";
import { observer } from "mobx-react";
import { Spin } from "antd";
import Search from "./components/Search";
import styles from "./index.module.scss";
import CardContent from "./components/CardContent";
import TradeDetailCard from "./components/TradeDetailCard";
import AlertMsg from "./components/AlertMsg";
import { tradeStore } from "@/stores";
import LineAudio from "./components/LineAudio";
import ContentSetting from "./components/ContentSetting";
import videoMonitorStore from "@/stores/trade/videoMonitor";
import { local } from "@/libs/db";
import userStore from "@/stores/user";

const SELECTED_SHOOTING_DEVICES_CACHE_KEY = 'SELECTED_SHOOTING_DEVICES_POST_PRINT';

const PostPrint = () => {
	const { postPrintStore: {
		isLoading,
		tip,
		resetStore,
		getSettingConfig,
		settingConfig,
		postPrintList,
		getContentSettingConfig,
		contentSettingModalVisible,
		videoMonitorState,
		selectedShootingDevices,
		setSelectedShootingDevices,
		nvrDevices,
		setNvrDevices
	} } = tradeStore;
	const { 
		connectWs,
		checkKdzsPrintComponent,
		disconnectWs,
		videoMonitor
	} = videoMonitorStore;
	const { userInfo } = userStore;

	useEffect(() => {
		const init = async() => {
			await getSettingConfig();
			await getContentSettingConfig();
			
			// 校验并更新缓存的设备信息
			await validateAndUpdateCachedDevices();
			
			// 在设置完成后检查并连接控件
			if (videoMonitorState && videoMonitor) {
				await handleReconnect();
			}
		};
		
		init();
		
		return () => {
			resetStore();
			disconnectWs();
		};
	}, [videoMonitor, userInfo?.userId]); // 移除videoMonitorState依赖

	// 保留原有的useEffect用于响应设置变化
	useEffect(() => {
		if (videoMonitorState && videoMonitor) {
			handleReconnect();
		} else {
			disconnectWs();
		}
	}, [videoMonitorState, videoMonitor]);


	// 校验并更新缓存的设备信息
	const validateAndUpdateCachedDevices = async() => {
		try {
		// 获取缓存的设备信息
			const cachedDevices = local.getByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY) || [];
		
			if (cachedDevices.length === 0) {
				return;
			}

			// 获取最新的设备列表
			const currentDeviceList = await videoMonitorStore.getDeviceCameraList();
			// 过滤掉禁用的设备
			const enabledDevices = currentDeviceList.filter(item => item.status === 1);

			// 校验缓存中的设备是否还存在
			const validDevices = cachedDevices.filter((cachedDevice: any) => enabledDevices.find((device: any) => device.id === cachedDevice.id));

			// 校验是否属于同一个NVR
			if (validDevices.length > 0) {
				const nvrIds = validDevices.map((device: any) => device.deviceId);
				const uniqueNvrIds = Array.from(new Set(nvrIds));
				if (uniqueNvrIds.length > 1) {
				// 只保留第一个NVR下的设备
					const firstNvrId = nvrIds[0];
					const sameNvrDevices = validDevices.filter((device: any) => device.deviceId === firstNvrId);
					local.setByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY, sameNvrDevices);
					setSelectedShootingDevices(sameNvrDevices);
				} else {
					local.setByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY, validDevices);
					setSelectedShootingDevices(validDevices);
				}
			} else {
				local.setByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY, []);
				setSelectedShootingDevices([]);
			}

			// 获取nvr设备列表
			const nvrDevices = await videoMonitorStore.getDeviceList();
			setNvrDevices(nvrDevices);
		} catch (error) {
			console.error('校验缓存设备失败:', error);
			// 清除无效缓存
			local.removeByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY);
			setSelectedShootingDevices([]);
			setNvrDevices([]);
		}
	};

	const handleReconnect = async() => {
		console.log('重新连接');
		
		await checkKdzsPrintComponent();
		connectWs();
	};


	const getTradeDetailCard = () => {
		const tradeDetailList = [];
		postPrintList[0]?.trades.forEach(trade => {
			tradeDetailList.push(
				<div key={ trade.tid } style={ { marginBottom: '16px' } }>
					<TradeDetailCard pack={ postPrintList[0] } trade={ trade } />
				</div>
			);
		});
		return tradeDetailList;
	};

	return (
		<Spin spinning={ isLoading } tip={ tip }>
			{contentSettingModalVisible && <ContentSetting />}
			<div className={ styles['main-container'] }>
				<div>
					<Search />
				</div>
				{
					settingConfig.scanType !== "PT_TID" && <CardContent />
				}
				<div className="r-flex-1">
					<div className={ styles["alert-container"] }>
						<AlertMsg />
					</div>
					<div className={ styles["table-container"] }>
						{getTradeDetailCard()}
					</div>
					<div className={ styles["audio-container"] }>
						<LineAudio />
					</div>
				</div>
			</div>
		</Spin>

	);
};
export default observer(PostPrint);
