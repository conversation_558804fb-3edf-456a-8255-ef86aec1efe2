import { makeAutoObservable, runInAction } from "mobx";
import { message, notification } from "antd";
import { 
	IndexMonitoringQueryDeviceCameraApi, 
	IndexMonitoringQueryMonitoringDeviceApi,
	IndexMonitoringGetMonitoringDeviceBrandApi
} from '@/apis/setting/monitoringDevice';
import PrintCenter from '@/print/index';
import userStore from "@/stores/user";
import { customLogPost } from "@/pages/Trade/components/ChangeAndAddGoods/util";
import { getToken } from "@/utils/token";
import { tokens } from "@/utils/request";

// 发送消息的接口
interface SendMessageData {
	api: string;
	data?: any;
}

// 接收消息的接口
interface ReceiveMessageData {
	api: string;
	result?: number;
	msg?: string;
	data?: any;
}

// 消息回调接口
interface MessageCallback {
	api: string;
	callback: (response: ReceiveMessageData) => void;
	timestamp: number;
	timeout?: any;
}

export class VideoMonitor {
	constructor() {
		makeAutoObservable(this);
	}

	isIpcModel: boolean = true; // 是否是IPC模式

	isKdzsPrintComponent: boolean = false; // 是否安装了快递助手ERP聚合控件

	// WebSocket相关 - 改为public，方便外部访问
	webSocket: WebSocket | null = null;

	reconnectAttempts: number = 0; // 重连次数

	reconnectTimer: any = null; // 重连定时器

	isManualClose: boolean = false; // 是否手动关闭

	// 添加可观察的连接状态
	connected: boolean = false;

	// 消息回调管理 - 改为public
	messageCallbacks: Map<string, MessageCallback> = new Map();

	ipcDeviceList: any[] = [];

	nvrDeviceList: any[] = [];

	brandList: any[] = [];

	isKdzsPrintComponentChecked: boolean = false;


	get videoMonitor() {
		const { userInfo } = userStore;
		const { whiteListSetting } = userInfo || {};
		const whiteListSettingObj = JSON.parse(whiteListSetting || '{}');
		const { videoMonitor } = whiteListSettingObj || {}; // 视频监控开关
		return videoMonitor;
	}

	// 是否连接成功 - 使用可观察的connected状态
	get isSocketConnected() {
		return this.connected && this.webSocket?.readyState === WebSocket.OPEN;
	}

	setIsIpcModel = (isIpc: boolean = true) => {
		this.isIpcModel = isIpc;
	}

	// 检查快递助手ERP聚合控件
	checkKdzsPrintComponent = async() => {
		if (this.isKdzsPrintComponentChecked) {
			return this.isKdzsPrintComponent;
		}
		try {
			// 开始轮询检查版本号
			const startTime = Date.now();
			const timeout = 5000; // 5秒超时
			const pollInterval = 100; // 每100毫秒检查一次
			
			console.log('开始轮询检查打印控件版本号...');
			
			while (Date.now() - startTime < timeout) {
				// 检查是否有版本号
				if ((window as any)?.comp?.Print?.Data?.kdzsPrintVersion) {
					console.log('检测到打印控件版本号:', (window as any).comp.Print.Data.kdzsPrintVersion);
					runInAction(() => {
						this.isKdzsPrintComponent = true;
						this.isKdzsPrintComponentChecked = true;
					});
					return true;
				}
				
				// 等待一段时间后再次检查
				// eslint-disable-next-line no-await-in-loop
				await new Promise(resolve => setTimeout(resolve, pollInterval));
			}
			
			// 超时后仍没有版本号，返回false
			console.log('轮询超时，未检测到打印控件版本号');
			runInAction(() => {
				this.isKdzsPrintComponent = false;
				this.isKdzsPrintComponentChecked = true;
			});
			return false;
		} catch (error) {
			console.log('检查打印控件失败:', error);
			runInAction(() => {
				this.isKdzsPrintComponent = false;
				this.isKdzsPrintComponentChecked = true;
			});
			return false;
		}
	}

	// 检查快递助手ERP聚合控件, 未安装弹出弹框
	checkKdzsPrintComponentStatus = async() => {
		await PrintCenter.checkKdzsPrintComponent(true);
		runInAction(() => {
			this.isKdzsPrintComponent = true;
		});
		return true;
	}

	connectWs = () => {
		// 如果已经有连接或者没有安装控件，则不连接
		if (this.isSocketConnected || !this.isKdzsPrintComponent) {
			console.log(this.isSocketConnected, this.isKdzsPrintComponent, 'WebSocket已连接或控件未安装，跳过连接');
			return;
		}

		// 先断开现有连接，确保清理干净
		this.disconnectWs();

		try {
			console.log('ERP聚合控件开始连接: ws://127.0.0.1:55555');
			
			// 重置手动关闭标志，允许自动重连
			this.isManualClose = false;
			
			this.webSocket = new WebSocket("ws://127.0.0.1:55555");

			// 连接成功事件
			this.webSocket.onopen = () => {
				console.log('ERP聚合控件连接成功');
				
				// 使用runInAction更新可观察状态
				runInAction(() => {
					this.connected = true;
				});

				notification.success({
					message: "系统提示",
					description: "ERP聚合控件连接成功",
					duration: 3
				});

				// 重置重连计数
				this.reconnectAttempts = 0;
			};

			// 接收消息事件
			this.webSocket.onmessage = (event) => {
				this.handleMessage(event?.data);
			};

			// 连接断开事件
			this.webSocket.onclose = (event) => {
				console.log('ERP聚合控件连接关闭:', event.code, event.reason);
				
				// 使用runInAction更新可观察状态
				runInAction(() => {
					this.connected = false;
				});
				
				// 清空回调映射表
				this.clearMessageCallbacks();

				// 自动重连 - 只有在非手动关闭的情况下才重连
				if (!this.isManualClose && this.reconnectAttempts < 4) {
					this.scheduleReconnect();
				} else if (this.isManualClose) {
					console.log('手动关闭连接，不进行自动重连');
				}
			};

			// 连接错误事件
			this.webSocket.onerror = (error) => {
				console.log('ERP聚合控件连接失败:', error);
				
				// 使用runInAction更新可观察状态
				runInAction(() => {
					this.connected = false;
				});

				notification.warning({
					message: "系统提示",
					description: "ERP聚合控件连接失败",
					duration: 3
				});
			};

		} catch (error) {
			console.error('创建WebSocket连接失败:', error);
			
			// 使用runInAction更新可观察状态
			runInAction(() => {
				this.connected = false;
			});

			notification.error({
				message: "系统提示",
				description: "ERP聚合控件连接失败",
				duration: 3
			});
		}
	}

	// 处理接收到的消息 - 改为public
	handleMessage = (data: string) => {
		try {
			const messageData: ReceiveMessageData = JSON.parse(data);
			console.log('ERP聚合控件收到消息:', messageData);
			// 检查是否有对应的回调
			if (messageData.api && this.messageCallbacks.has(messageData.api)) {
				const callbackInfo = this.messageCallbacks.get(messageData.api)!;
				callbackInfo?.timeout && clearTimeout(callbackInfo.timeout);
				this.messageCallbacks.delete(messageData.api);
				callbackInfo.callback(messageData);
			} else {
				// 没有回调，根据api类型处理消息
				this.handleMessageByApi(messageData);
			}
		} catch (error) {
			// 如果不是JSON格式，按原始数据处理
			console.log('ERP聚合控件收到原始消息:', data);
		}
	}

	// 根据api类型处理消息 - 改为public
	handleMessageByApi = (messageData: ReceiveMessageData) => {
		const { api, result, msg, data } = messageData;
		
		// 根据不同的api类型处理消息
	
	}

	// 断开连接
	disconnectWs = () => {
		console.log('开始断开WebSocket连接');
		
		// 设置手动关闭标志，防止自动重连
		this.isManualClose = true;
		
		// 清理重连定时器
		if (this.reconnectTimer) {
			clearTimeout(this.reconnectTimer);
			this.reconnectTimer = null;
		}

		// 关闭WebSocket连接
		if (this.webSocket) {
			// 先移除事件监听器，避免重复触发
			this.webSocket.onopen = null;
			this.webSocket.onmessage = null;
			this.webSocket.onclose = null;
			this.webSocket.onerror = null;
			
			// 关闭连接
			this.webSocket.close();
			this.webSocket = null;
		}

		// 使用runInAction更新可观察状态
		runInAction(() => {
			this.connected = false;
		});

		// 清空消息回调
		this.clearMessageCallbacks();
		
		console.log('ERP聚合控件已断开连接');
	}

	// 发送消息 - 支持可选回调
	sendMessage = (api: string, data: any = {}, callback?: (response: ReceiveMessageData) => void) => {
		if (!this.isSocketConnected) {
			console.log('WebSocket未连接，无法发送消息');
			if (callback) {
				callback({ api, result: -1, msg: '聚合控件未连接', data: {} });
			}
			return;
		}

		try {
			// 构造发送的消息
			const messageToSend: SendMessageData = {
				api,
				data
			};

			// 如果有回调，保存到映射表中
			if (callback) {
				let timeout: any = null;

				// 有些没有response的接口，不设置超时
				// if (!['start_nvr_preview', 'close_nvr_preview', 'start_record', 'stop_record', 'start_nvr_record', 'stop_nvr_record']?.includes(api)) {
				// 	timeout = setTimeout(() => {
				// 		// 超时处理
				// 		if (this.messageCallbacks.has(api)) {
				// 			this.messageCallbacks.delete(api);
				// 			callback({ api, result: -1, msg: '请求超时', data: {} });
				// 		}
				// 	}, 10000); // 10秒超时
				// }

				this.messageCallbacks.set(api, {
					api,
					callback,
					timestamp: Date.now(),
					timeout
				});
			}

			// 发送消息
			this.webSocket!.send(JSON.stringify(messageToSend));
			console.log('ERP聚合控件发送消息:', messageToSend);

		} catch (error) {
			console.error('发送消息失败:', error);
			if (callback) {
				callback({ api, result: -1, msg: '发送消息失败', data: {} });
			}
		}
	}

	// 清空消息回调
	clearMessageCallbacks = () => {
		this.messageCallbacks.forEach((callbackInfo) => {
			callbackInfo?.timeout && clearTimeout(callbackInfo.timeout);
		});
		this.messageCallbacks.clear();
	}

	// 安排重连
	private scheduleReconnect = () => {
		if (this.reconnectTimer) {
			clearTimeout(this.reconnectTimer);
		}

		this.reconnectAttempts++;
		const delay = 3000 * Math.pow(2, this.reconnectAttempts - 1); // 指数退避

		console.log(`ERP聚合控件 ${this.reconnectAttempts}/4 次重连，延迟 ${delay}ms`);

		this.reconnectTimer = setTimeout(() => {
			this.connectWs();
		}, delay);
	}

	setIpcDeviceList = (list: any[]) => {
		this.ipcDeviceList = list;
	}

	// 获取摄像头设备列表
	getDeviceCameraList = async() => {
		if (this.ipcDeviceList.length) {
			return this.ipcDeviceList;
		}
		try {
			const res = await IndexMonitoringQueryDeviceCameraApi({});
			this.ipcDeviceList = res;
			return res;
		} catch (error) {
			console.error('获取拍摄设备列表失败:', error);
			return [];
		}
	}

	setNvrDeviceList = (list: any[]) => {
		this.nvrDeviceList = list;
	}

	// 获取设备列表
	getDeviceList = async() => {
		if (this.nvrDeviceList.length) {
			return this.nvrDeviceList;
		}
		try {
			const res = await IndexMonitoringQueryMonitoringDeviceApi({});
			this.nvrDeviceList = res;
			return res;
		} catch (error) {
			console.error('获取设备列表失败:', error);
			return [];
		}
	}

	getBrandList = async() => {
		if (this.brandList.length) {
			return this.brandList;
		}
		try {
			const res = await IndexMonitoringGetMonitoringDeviceBrandApi({});
			this.brandList = res;
			return res;
		} catch (error) {
			console.error('获取设备品牌列表失败:', error);
			return [];
		}
	}

	// 添加监控开启方法
	startVideoMonitor = (data, operateType) => {
		const { 
			outSid, orderList, 
			isIpcModel, isVideoMonitor,
			nvrDevices, selectedShootingDevices,
			texts, isKd, isDd
		} = data;
		console.log('orderListorderListorderListorderList', orderList, isKd, outSid);
		if (isVideoMonitor) {
			const companyId = userStore.userInfo?.userId;
			const operateUser = userStore.userInfo?.subUserName || userStore.userInfo?.userName;
			const operateUserId = userStore.userInfo?.subUserId || userStore.userInfo?.userId;
			customLogPost('视频监控录制', { 
				isIpcModel, 
				searchValue: outSid, 
				orderList 
			});

			if (isIpcModel) {
				const device = nvrDevices?.find(d => d.id == selectedShootingDevices[0]?.deviceId);
				if (!device) {
					return;
				}

				this.sendMessage("start_nvr_record", {
					outSid,
					cancel: -1,
					fake_sid: 0,
					host: window.location.hostname,
					token: getToken(),
					nvr_device: {
						device_ip: device?.deviceIp,
						device_username: device?.deviceAccountName,
						device_password: device?.deviceAccountPassword,
						device_channel: Number(selectedShootingDevices[0]?.cameraChannel),
						device_channel_list: selectedShootingDevices?.map(item => Number(item.cameraChannel)),
					},
					extra_params: {
						erpVideoIds: selectedShootingDevices?.map(item => Number(item.deviceId)) || [],
						erpCameraIds: selectedShootingDevices?.map(item => Number(item.id)) || [],
						autoToOSS: false, // 是否自动上传到oss
						operateType, 
						monitorType: 1, // 1 ipc 2 usb
						outSid, // 扫描查询的值
						exNumber: isKd ? outSid : '', // 快递单号
						companyId, // 用户id
						operateUser, // 操作人
						operateUserId, // 操作人id
						channel: Number(selectedShootingDevices[0]?.cameraChannel),
						channelList: selectedShootingDevices?.map(item => Number(item.cameraChannel)), // 同一个录像设备
						platformOrderNumber: isDd ? outSid : '', // 平台订单号
						// searchType, // 查询类型
					}
				}, (res) => {
					if (res?.msg) {
						message.error('视频录制失败：' + res.msg);
						customLogPost('视频录制失败ipc', { outSid, msg: res.msg });
					}
				});
			} else {
				// 普通模式，发送start_record消息
				this.sendMessage("start_record", {
					outSid, // 扫描查询的项，不固定，可能为售后单号，也可能为快递单号等等
					cancel: -1, // 有正在录制中的视频
					water_mark_texts: texts, // 自定义水印文字
					water_maker_type: 1, // 水印类型 0 默认 1 自定义
					hide_water_mark: 1, // 是否要隐藏客户端默认添加地运单号水印 0 不隐藏 1 隐藏运单号水印
					fake_sid: 0, // 是否是假单号 0 否 1 是
					host: window.location.hostname,
					token: getToken(),
					extra_params: {
						erpVideoIds: [],
						erpCameraIds: [],
						autoToOSS: false, // 是否自动上传到oss
						operateType, // 1 售后扫描快递单
						monitorType: 2, // 1 ipc 2 usb
						outSid, // 扫描查询的值
						exNumber: isKd ? outSid : '', // 快递单号
						companyId, // 用户id
						operateUser, // 操作人
						operateUserId, // 操作人id
						channel: '',
						channelList: [], // 同一个录像设备
						platformOrderNumber: isDd ? outSid : '', // 平台订单号
					}
				}, (res) => {
					if (res?.msg) {
						message.error('视频录制失败：' + res.msg);
						customLogPost('视频录制失败ipc', { outSid, msg: res.msg });
					}
				});
			}
		}
	};

clearVideoMonitor = (isVideoMonitor, isIpcModel) => {
	// 新增：收货流程结束，发送停止视频监控消息
	console.log('%c [ isVideoMonitor ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', isVideoMonitor, isIpcModel);
	if (isVideoMonitor) {
		if (isIpcModel) {
		// IPC模式，发送stop_nvr_record消息
			this.sendMessage("stop_nvr_record", {
				cancel: 0, // 0 ，正常保存。  cancel 1，不保存丢失视频记录
			}, (res) => {
				if (res?.msg) {
					message.error(res.msg);
				}
			});
		} else {
		// 普通模式，发送stop_record消息
			this.sendMessage("stop_record", {}, (res) => {
				if (res?.msg) {
					message.error(res.msg);
				}
			});
		}
	}
}

showErrorMsg = (value) => {
	if (value && !this.videoMonitor) {
		message.warning('视频监控需增购后使用，具体增购费用请联系销售增购后使用');
		return true;
	}
	return false;
}

}
const videoMonitorStore = new VideoMonitor();
export default videoMonitorStore;
