import React, { useEffect, useState, useMemo } from 'react';
import { CheckCircleFilled, CloseCircleFilled, SoundOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import { observer } from 'mobx-react';
import styles from './index.module.scss';
import { playAudio } from '@/pages/Trade/ScanPrint/utils';
import voiceSendSuccess from '@/assets/mp3/发货成功.mp3';
import voiceHandleFail from '@/assets/mp3/处理失败.mp3';
import userStore from '@/stores/user';
import { local } from '@/libs/db';
import videoMonitorStore from '@/stores/trade/videoMonitor';
import SelectShootingDeviceModal from '@/components-biz/SelectShootingDeviceModal';
import { tradeStore } from '@/stores';

const SELECTED_SHOOTING_DEVICES_CACHE_KEY = 'SELECTED_SHOOTING_DEVICES_POST_PRINT';

const LineAudio = () => {
	const { userInfo } = userStore;
	const { whiteListSetting } = userInfo || {};
	const [isShowChooseModel, setIsShowChooseModel] = useState(false); // 是否显示选择拍摄设备
	const { postPrintStore: { 
		videoMonitorState,
		setVideoMonitorState,
		selectedShootingDevices,
		setSelectedShootingDevices,
		nvrDevices,
		setNvrDevices
	} } = tradeStore;
	const { 
		checkKdzsPrintComponent,
		connectWs,
		disconnectWs,
		videoMonitor,
	} = videoMonitorStore;
	const [ishow, setIsShow] = useState(false); // 新增状态

	// 监听状态变化，更新 ishow
	useEffect(() => {
		console.log(videoMonitor, videoMonitorState, 'videoMonitor, videoMonitorState');
		// 确保 userInfo 已经加载完成
		if (userInfo?.userId && whiteListSetting) {
			const newIshow = videoMonitor == 1 && videoMonitorState;
			setIsShow(newIshow);
		}
	}, [videoMonitor, videoMonitorState, userInfo?.userId, whiteListSetting]);

	// 处理选择拍摄设备确认
	const handleSelectShootingDeviceConfirm = (selectedDevices: any[]) => {
		// 缓存选中的设备
		local.setByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY, selectedDevices);
		setSelectedShootingDevices(selectedDevices);
		setIsShowChooseModel(false);
	};

	// 处理选择拍摄设备取消
	const handleSelectShootingDeviceCancel = () => {
		setIsShowChooseModel(false);
	};

	// 获取选中的设备名称显示文本
	const getSelectedDevicesText = () => {
		if (selectedShootingDevices.length === 0) {
			return "请选择拍摄设备";
		} else {
			return selectedShootingDevices.map(item => item.cameraName).join(",");
		}
	};
	return (
		<div>
			<div className={ styles["sound-warning-container"] }>
				<SoundOutlined />
				<span className="text">请提前打开声音，以保证能够听到提示音</span>
				<div className="success-info" onClick={ () => playAudio(voiceSendSuccess) }>
					<CheckCircleFilled />
					<span className="r-ml-4">成功提示音</span>
				</div>
				<div className="fail-info" onClick={ () => playAudio(voiceHandleFail) }>
					<CloseCircleFilled />
					<span className="r-ml-4">失败提示音</span>
				</div>
				{
					ishow ? (
						<Button 
							type="primary" 
							className="r-ml-16" 
							style={ { backgroundColor: "#1890ff", borderColor: "#1890ff" } }
							onClick={ () => {
								setIsShowChooseModel(true);
							} }
						>
							{getSelectedDevicesText()}
						</Button>
					) : null
				}
				{/* 选择拍摄设备弹框 */}
				<SelectShootingDeviceModal
					visible={ isShowChooseModel }
					onCancel={ handleSelectShootingDeviceCancel }
					onConfirm={ handleSelectShootingDeviceConfirm }
					defaultSelectedDevices={ selectedShootingDevices }
				/>
			</div>
		</div>
	);
};

export default observer(LineAudio);