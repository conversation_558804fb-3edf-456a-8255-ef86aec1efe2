import React, { useCallback, useEffect, useState } from "react";
import { observer } from "mobx-react";
import { Alert, Button, Input, Layout, Spin } from "antd";
import cs from "classnames";
import { CheckCircleFilled, CloseCircleFilled, SoundOutlined } from "@ant-design/icons";
import TradeDetailCard from "./components/TradeDetailCard";
import styles from "./index.module.scss";
import voiceSendSuccess from '@/assets/mp3/发货成功.mp3';
import voiceHandleFail from '@/assets/mp3/处理失败.mp3';
import { playAudio } from "../ScanPrint/utils";
import TheoreticalWeightCom from "./components/TheoreticalWeightCom";
import { local } from "@/libs/db";
import AdvancedSetting from "./components/AdvancedSetting";
import Header from "./components/Header";
import userStore from "@/stores/user";
import packWeightStore from "@/stores/trade/packWeight";
import videoMonitorStore from "@/stores/trade/videoMonitor";
import SelectShootingDeviceModal from "@/components-biz/SelectShootingDeviceModal";

const SELECTED_SHOOTING_DEVICES_CACHE_KEY = 'SELECTED_SHOOTING_DEVICES_PACK_WEIGHT';

const PackWeight = () => {
	const {
		tradeList, showLoading,
		videoMonitorState,
		setVideoMonitorState,
		selectedShootingDevices,
		setSelectedShootingDevices,
		nvrDevices,
		setNvrDevices,
		getAdvancedSettingConfig
	} = packWeightStore;
	const { 
		checkKdzsPrintComponent,
		connectWs,
		disconnectWs,
		videoMonitor
	} = videoMonitorStore;
	const { userInfo } = userStore;
	const [isShowChooseModel, setIsShowChooseModel] = useState(false); // 是否显示选择拍摄设备

	const [ishow, setIsShow] = useState(false); // 新增状态
	const handleReconnect = async() => {
		console.log('重新连接');
		await validateAndUpdateCachedDevices();
		await checkKdzsPrintComponent();
		connectWs();
	};
	const handleConfigInfo = async() => {
		const configInfo = await getAdvancedSettingConfig().catch();
		console.log('configInfo: ', configInfo);
		setVideoMonitorState(!!configInfo?.videoMonitor);
		if (videoMonitorState && videoMonitor) {
			handleReconnect(); // 初始化视频监控相关
		} 
	};
	// PackWeight/index.tsx
	useEffect(() => {
		const init = async() => {
			userStore.getUserSetting();
			await handleConfigInfo();
		};
		init();
		return () => {
			disconnectWs();
		};
	}, [videoMonitor, userInfo?.userId]);
	useEffect(() => {
		if (videoMonitorState && videoMonitor) {
			handleReconnect(); // 初始化视频监控相关
		} else {
			disconnectWs(); // 断开ERP聚合控件连接
		}
	}, [videoMonitorState, videoMonitor]);
	// 校验并更新缓存的设备信息
	const validateAndUpdateCachedDevices = async() => {
		try {
		// 获取缓存的设备信息
			const cachedDevices = local.getByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY) || [];
		
			if (cachedDevices.length === 0) {
				return;
			}

			// 获取最新的设备列表
			const currentDeviceList = await videoMonitorStore.getDeviceCameraList();
			// 过滤掉禁用的设备
			const enabledDevices = currentDeviceList.filter(item => item.status === 1);

			// 校验缓存中的设备是否还存在
			const validDevices = cachedDevices.filter((cachedDevice: any) => enabledDevices.find((device: any) => device.id === cachedDevice.id));

			// 校验是否属于同一个NVR
			if (validDevices.length > 0) {
				const nvrIds = validDevices.map((device: any) => device.deviceId);
				const uniqueNvrIds = Array.from(new Set(nvrIds));
				if (uniqueNvrIds.length > 1) {
				// 只保留第一个NVR下的设备
					const firstNvrId = nvrIds[0];
					const sameNvrDevices = validDevices.filter((device: any) => device.deviceId === firstNvrId);
					local.setByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY, sameNvrDevices);
					setSelectedShootingDevices(sameNvrDevices);
				} else {
					local.setByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY, validDevices);
					setSelectedShootingDevices(validDevices);
				}
			} else {
				local.setByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY, []);
				setSelectedShootingDevices([]);
			}

			// 获取nvr设备列表
			const nvrDevices = await videoMonitorStore.getDeviceList();
			setNvrDevices(nvrDevices);
		} catch (error) {
			console.error('校验缓存设备失败:', error);
			// 清除无效缓存
			local.removeByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY);
			setSelectedShootingDevices([]);
			setNvrDevices([]);
		}
	};

	   // 监听状态变化，更新 ishow
	useEffect(() => {
		console.log(videoMonitor, videoMonitorState, 'videoMonitor, videoMonitorState');
		// 确保 userInfo 已经加载完成
		if (userInfo?.userId) {
			const newIshow = videoMonitor && videoMonitorState;
			setIsShow(newIshow);
		}
	}, [videoMonitor, videoMonitorState, userInfo?.userId]);
	// 处理选择拍摄设备确认
	const handleSelectShootingDeviceConfirm = (selectedDevices: any[]) => {
		// 缓存选中的设备
		local.setByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY, selectedDevices);
		setSelectedShootingDevices(selectedDevices);
		setIsShowChooseModel(false);
	};

	// 处理选择拍摄设备取消
	const handleSelectShootingDeviceCancel = () => {
		setIsShowChooseModel(false);
	};

	// 获取选中的设备名称显示文本
	const getSelectedDevicesText = () => {
		if (selectedShootingDevices.length === 0) {
			return "请选择拍摄设备";
		} else {
			return selectedShootingDevices.map(item => item.cameraName).join(",");
		}
	};
	const getTradeDetailCard = useCallback(() => {
		const tradeDetailList = [];
		tradeList.forEach(pack => {
			tradeDetailList.push(
				<div key={ pack.togetherId }>
					<TradeDetailCard pack={ pack } />
				</div>
			);
		});
		return tradeDetailList;
	}, [tradeList]);


	return (
		<div style={ { minHeight: "100%" } }>
			<Layout className="kdzs-section">
				<Header />
			</Layout>
			<div className={ styles['trade-wrap'] }>
				<div>
					<Spin spinning={ showLoading }>
						<div className={ styles['trade-item-wrap'] }>
							{getTradeDetailCard()}
						</div>

						<div className={ styles["sound-warning-container"] }>
							<SoundOutlined />
							<span className="text">请提前打开声音，以保证能够听到提示音</span>
							<div className="success-info" onClick={ () => playAudio(voiceSendSuccess) }>
								<CheckCircleFilled />
								<span className="r-ml-4">成功提示音</span>
							</div>
							<div className="fail-info" onClick={ () => playAudio(voiceHandleFail) }>
								<CloseCircleFilled />
								<span className="r-ml-4">失败提示音</span>
							</div>
							{
								ishow ? (
									<Button 
										type="primary" 
										className="r-ml-16" 
										style={ { backgroundColor: "#1890ff", borderColor: "#1890ff" } }
										onClick={ () => {
											setIsShowChooseModel(true);
										} }
									>
										{getSelectedDevicesText()}
									</Button>
								) : null
							}
							{/* 选择拍摄设备弹框 */}
							<SelectShootingDeviceModal
								visible={ isShowChooseModel }
								onCancel={ handleSelectShootingDeviceCancel }
								onConfirm={ handleSelectShootingDeviceConfirm }
								defaultSelectedDevices={ selectedShootingDevices }
							/>
						</div>
					</Spin>

				</div>
				<div className="r-ml-8 r-mt-0">
					<TheoreticalWeightCom />
				</div>
			</div>

			<AdvancedSetting />
		</div>
	);

};

export default observer(PackWeight);
