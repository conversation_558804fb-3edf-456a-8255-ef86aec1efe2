import React, { useCallback, useEffect, useRef, useState, ReactNode, useMemo } from 'react';
import { Button, Checkbox, Image, Popover, Select, Modal, Space, Tooltip, Radio, Form, InputNumber, Dropdown, Menu, Switch, Popconfirm, Drawer } from 'antd';
import { ExclamationCircleOutlined, FormOutlined, SettingOutlined, CheckCircleFilled, CloseCircleFilled, DownOutlined, SettingOutlined as SettingIcon, QuestionCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { useForm } from 'antd/es/form/Form';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import { ColumnsType } from 'antd/lib/table';
import cs from 'classnames';
import { observer } from "mobx-react";
import _ from 'lodash';
import { useHistory } from 'react-router-dom';
import dayjs from 'dayjs';
import Icon from '@/components/Icon';
import ColumnSettingDrawer from './components/ColumnSettingDrawer';
import s from './index.module.scss';
import SearchTable from '@/components/SearchTableVirtual';
import { SearchTableRefProps } from '@/components/SearchTable/SearchTable';
import { FormItemConfig } from '@/components/SearchTable/FormWidthRow';
import FooterBar from '@/components/FooterBar';
import ShopMultiSelect from '@/components-biz/ShopListSelect/shopMultiSelect';
import NormalLayout from '@/components-biz/layouts/NormalLayout';
import BatchImportModal from "./components/BatchImportModal";
import { clearParams } from '@/utils/stringHelper';
import xianshi from '@/assets/image/icon_限时@2x.png';
import {
	UpdateSysItemByPlatformApi,
	BatchUpdateSysItemByPlatformApi,
	batchDeleteBySkuIdsApi,
	ItemScmSaleBatchUpdateDropShippingItemFlagApi,
	ItemTakeGoodsLabelBindLabelPushPlatformItemApi,
	ItemTakeGoodsLabelUnBindLabelPushPlatformItemApi,
	ItemTakeGoodsLabelChangeBindLabelPushPlatformItemApi
} from '@/apis/warehouse/system';
import { TradeDictQueryDictApi, TradeDictInsertDictApi } from '@/apis/trade/search';
import {
	ItemSysItemListOfItemRelationPlatformItemViewRequest,
	ItemSysItemListOfItemRelationPlatformItemViewResponse,
	ItemScmSaleBatchUpdateDropShippingItemFlagRequest,
	ItemItemBatchUpdateSysItemByPlatformRequest
} from '@/types/schemas/warehouse/system';
import { DEFAULT_IMG, PLAT_JD, PLAT_XHS, USER_TYPE, AUTH_USER_TYPE, PLAT_YZ, PLAT_FXG, PLAT_PDD } from '@/constants';
import SyncWaresModal from "@/pages/Warehouse/StockSync/HandStockSync/components/SyncWaresModal";
import Input from '@/components/Input/InputSearch';
import Pointer from "@/utils/pointTrack/constants";
import sendPoint from "@/utils/pointTrack/sendPoint";
import PlatformIcon from '@/pages/Warehouse/StockSync/HandStockSync/components/PlatformIcon';
import SameSkuTable from '@/pages/Warehouse/System/Archives/components/ImportPlatItem/components/RelationTable/components/SameSku';
import message from '@/components/message';
import { Obj } from '@/pages/Trade/interface';
import BatchEditModal, { addPosMap, getAddRuleContent, getNewContent, nameMap } from './components/BatchEditModal';
import { getPlatformDetailLink } from '@/pages/AfterSale/TradeList/utils';
import { autoSearch, weightUnit } from '@/pages/Index/Settings/System/constants';
import { getPlatAndShops, isSourceScm } from '@/components-biz/ShopListSelect/shopListUtils';
import { IBatchEditModalInfo, PrintType } from '../Archives';
import PrintCenter from '@/print/index';
import { GoodsPrintType, UserViewValEnum } from '../../constants';
import { accMul } from '@/utils/util';
import WaresInfo from '@/components-biz/WaresInfo';
import BatchSetGoodsModal, { BatchSetGoodsShipType, IBatchSetGoodsOnOkParams } from './components/BatchSetGoodsModal';
import PlatformGoodsLogModal, { PlatformGoodsLogModalProps } from './components/PlatformGoodsLogModal';
import { ActivityGiftLimit, batchPublish, GetUserActivityInfo, IndexPlatformShopGetPlatformShopsApi, SettingSaveSystemSettingApi } from '@/apis/user';
import distributionStore from '@/stores/distribution';
import { downloadCenter } from '@/pages/Index/DownloadCenter/utils';
import { ModulesFunctionEnum } from '@/types/schemas/setting/download';
import { tradeStore } from '@/stores';
import { belongStartAndEnd, compareNum, compareRowId } from '../../utils';
import { distributionType } from '@/pages/Distribution/utils';
import FieldsPermissionCheck, { FieldsPermissionEnum } from '@/utils/permissionCheck/fieldsPermissionCheck';
import UserStore from '@/stores/user';
import BindYPListModal from './components/BindYPList/Model';
import { labelPushPlatformEnum } from '@/constants/labelPush';
import scanPrintStore from '@/stores/trade/scanPrint';
import GroupEditorModal from '@/components-biz/Product/Group/GroupModal';
import groupStore from '@/stores/warehouse/Group';
import { batchEditFuncName } from '../Archives/constant';
import { local } from '@/libs/db';
import { EnumItemCustomAttribute } from '@/pages/Index/Settings/components/CustomerConfigSettingModal/contant';
import AutoBindModal from '../../StockSync/HandStockSync/components/AutoBindModal';
import useGetState from '@/utils/hooks/useGetState';
import { BQ_PRINT_TYPE } from '@/pages/Trade/constants';
import WeightInput from '@/components/Input/InputNumber/WeightInput';
import KdzsDateRangePicker1 from "@/components/DateRangeComp/kdzsRangePicker1";
import { fetchWithPaginationOptimization } from '@/pages/Warehouse/System/Relation/components/PlatformTable/utils/paginationOptimization';
import LimitInfoBar from '@/components/LimitInfoBar';
import BatchMoveModal from './components/BatchMoveModal';
import { choiceItemMigrationplatform, itemMigrationplatform } from '../../components/ConfigDrawer/interface';
import LowVersionControlModal from '@/components-biz/LowVersionControlModal';
import { PageNameControlEnum, VersionEnum } from '@/components-biz/LowVersionControlModal/constant';
import { getImageThumbnail } from '@/utils/img.scale';
import WelfareGiftModal from './components/WelfareGiftModal';
import InputMulti from '@/components/Input/InputMulti';


interface BindProp {
	numIid: string,
	skuId: string,
	operateType: string,
	sysItemAlias: string,
	mapKey?: string,
	sysItemId: string,
	sysSkuId: string,
	sysSkuAlias: string,
	weight: string,
	costPrice: string,
	tagPrice: string,
	classifyId:string,
	market: string,
	supplierName: string,
	stall: string
}

interface IParam {
	distribItemVos: {
	  	// 宝贝id
		itemId?: string,
		// 源平台
		sourcePlatform?: string,
		// 目标平台
		targetPlatform?: string,
		// 源店铺id
		targetSellerId?: string,
		// 目标店铺id
		sourceSellerId?: string,
		// 目标店铺名称
		targetSellerNick?: string,
		categoryIds?: string,
		categoryNames?: string,
	}[]
}

type TableListData = ItemSysItemListOfItemRelationPlatformItemViewResponse['data']['list'];

function determineFilters(filter) {
	let useBracketsFilter = false;
	let useCommaFilter = false;
	let useParenthesisFilter = false;

	// 如果 filter 数组为空，返回两个 false，不启用任何过滤
	if (!filter || filter.length === 0) {
		return { useBracketsFilter, useCommaFilter, useParenthesisFilter };
	}

	if (filter.includes('1')) {
		useBracketsFilter = true; // 如果 filter 中包含 '1'，启用括号过滤
	}
	if (filter.includes('2')) {
		useCommaFilter = true; // 如果 filter 中包含 '2'，启用逗号过滤
	}
	if (filter.includes('3')) {
		useParenthesisFilter = true; // 如果 filter 中包含 '3'，启用括号过滤
	}

	return { useBracketsFilter, useCommaFilter, useParenthesisFilter };
}

const settingArr = [{
	label: '小图',
	value: '0',
}, {
	label: '中图',
	value: '1',
}, {
	label: '大图',
	value: '2',
}];

function filterText(text, filter) {
	// 通过工具函数获取过滤规则
	const { useBracketsFilter, useCommaFilter, useParenthesisFilter } = determineFilters(filter);

	// 如果过滤规则都为 false，直接返回原文本
	if (!useBracketsFilter && !useCommaFilter && !useParenthesisFilter) {
		return text;
	}

	// 过滤中英文逗号的正则
	const commaRegex = /[,，]/g;

	// 递归过滤嵌套的【】
	function removeBrackets(content) {
		const bracketsRegex = /【[^【】]*】/g; // 匹配最内层的完整括号对
		while (bracketsRegex.test(content)) {
			content = content.replace(bracketsRegex, ''); // 逐层移除最内层的括号对
		}
		return content;
	}

	function removeParenthesis(content) {
		// 处理嵌套括号：从最内层开始，逐层移除
		let hasChanged = true;
		while (hasChanged) {
			hasChanged = false;
			// 匹配最内层的括号对（不包含其他括号）
			const innerParenthesisRegex = /[（(]([^（）()]*)[）)]/g;
			content = content.replace(innerParenthesisRegex, (match, innerContent) => {
				// 如果内部内容不包含任何括号，则删除整个括号对
				if (!/[（(）)]/.test(innerContent)) {
					hasChanged = true;
					return '';
				}
				return match;
			});
		}
		return content;
	}

	// 过滤【】及其中内容
	if (useBracketsFilter) {
		text = removeBrackets(text);
	}

	// 过滤中英文逗号
	if (useCommaFilter) {
		text = text.replace(commaRegex, '');
	}

	// 过滤（）及其中内容
	if (useParenthesisFilter) {
		text = removeParenthesis(text);
	}

	return text;
}

const { Option } = Select;
const { confirm } = Modal;

const getList = (selectedRows: any[], isShowSku: boolean) => (isShowSku ? selectedRows : selectedRows.reduce((arr, v) => arr.concat(v.platformItemSkuList.map(i => ({ ...v, ...i }))), []));

const nameFactory = (id, key) => {
	return id + '_' + key;
};

const sendTypePoint = (type) => {
	const typePointMap = {
		'sysItemAlias': '商品_平台商品管理_批量简称图标_点击',
		'sysSkuAlias': '商品_平台商品管理_批量规格别名图标_点击',
		'weight': '商品_平台商品管理_批量重量图标_点击',
		'costPrice': '商品_平台商品管理_批量成本价图标_点击',
		'market': '商品_平台商品管理_批量市场图标_点击',
		'stall': '商品_平台商品管理_批量档口图标_点击',
		'supplierName': '商品_平台商品管理_批量供应商图标_点击',
	};
	const name = typePointMap[type];
	name && sendPoint(Pointer[name]);
};

let CUR_CHANGED_LIST = [];

const onCellRender = (row: any, index: number) => {
	return {
		style: row.colSpan ? {} : { borderTop: 0 },
		rowSpan: 1,
	};
};

const distributorFilterFormFields = ['existWeight', 'existCostPrice', 'market', 'stall', 'supplierName', 'existMarket', 'existStall', 'existSupplier', 'skuNameList', 'itemSkuCreateTime'];
// 常规库存版不需要的查询条件
const distributorFilterFormFields1 = ['existWeight', 'existCostPrice', 'market', 'stall', 'supplierName', 'existMarket', 'existStall', 'existSupplier', 'sysItemAlias', 'skuContent', 'existSkuAlias', 'existItemAlias', "classifyId", "existClassify", 'itemSkuCreateTime'];

type formIdType = 'numIid' | 'rowId';

const PlatformGoodsItemColConfigKey = 'PLAT_ITEM_COLUMN_ITEM_VIEW_CONFIG';
const PlatformGoodsSkuColConfigKey = 'PLAT_ITEM_COLUMN_SKU_VIEW_CONFIG';

/**
 * 商家平台视角
 * @param props
 * @returns
 */
const PlaftformGoods: React.FC = props => {
	const [form] = useForm();
	const tableRef = useRef<SearchTableRefProps>();
	const [syncWaresModalVisible, setSyncWaresModalVisible] = useState(false);
	const [autoBindModalVisible, setAutoBindModalVisible] = useState(false);
	const [batchSetGoodsVisible, setBatchSetGoodsVisible] = useState(false);
	const [batchSetDropShippingGoodsLoading, setBatchSetDropShippingGoodsLoading] = useState(false);
	const [dataSource, setDataSource] = useState<any[]>([]);
	const [dataFormSource, setDataFormSource] = useState<Obj>();
	const [originData, setOriginData] = useState<Obj>();
	const [isKg, setIsKg] = useState(false);
	const [importModalVisible, setImportModalVisible] = useState<boolean>(false);
	const [downloadExcelModalVisible, setDownloadExcelModalVisible] = useState(false); // 导出Excel
	const [exportType, setExportType] = useState(1); // 导出Excel类型
	const [curSearchParams, setCurSearchParams] = useState({});
	const [showVersionModal, setShowVersionModal] = useState(false);
	const [sameSku, setSameSku] = useState({
		sameSkuVisible: false,
		sameSkuDataSource: [],
		resolve: () => { }
	});
	const [batchMoveModalVisible, setBatchMoveModalVisible] = useState(false);

	const {
		platformAuthInfo,
		getLabelPushInfoList,
		setOnOKBatchMoveModalLoading,
	} = scanPrintStore;
	useEffect(() => {
		getLabelPushInfoList();
		getSkuPicPreviewConfig();
	}, []);
	const [exportLoading, setExportLoading] = useState(false);
	const history = useHistory();
	const [loadingMap, setLoadingMap] = useState<Obj>({});
	const [batchEditModalInfo, setBatchEditModalInfo] = useState<IBatchEditModalInfo>({ visible: false, type: "" });
	const [platformGoodsLogInfo, setPlatformGoodsLogInfo] = useState<PlatformGoodsLogModalProps['info']>({
		show: false,
		type: 'item',
		info: {},
	});

	const [isShowSupplier, setIsShowSupplier] = useState(false);
	const [isShowSku, setIsShowSku] = useState(false);
	const [isShowSkuPic, setIsShowSkuPic] = useState(false);
	const [isHideDelete, setIsHideDelete] = useState(false);
	const [skuPicPreviewConfig, setSkuPicPreviewConfig] = useState("1");
	const [skuPicGoodsConfig, setSkuPicGoodsConfig] = useState("1");
	const [tradeWaitSendFlag, setTradeWaitSendFlag, getTradeWaitSendFlag] = useGetState(false);
	const [dropShippingAutoPush, setDropShippingAutoPush] = useState(false);
	const [autoPushLoading, setAutoPushLoading] = useState(false);
	const { isDistributorAccount, hasCostPricePermission, isSupplierAccount, isFreeSupplierAccount, isShowZeroStockVersion, getAuthUserType, userInfo } = UserStore;
	const { supplierList = [] } = distributionStore;
	const [oldSelectObj, setOldSelectObj] = useState({ index: "", checked: false });
	const [oldSelectGroupObj, setOldSelectGroupObj] = useState({ index: -1, checked: false });
	const [optRow, setOptRow] = useState<{[k:string]:any}>();
	const [optRowBindSupplierId, setOptRowBindSupplierId] = useState();
	const [welfareGiftModalVisible, setWelfareGiftModalVisible] = useState(false);
	const [tradeFindType, setTradeFindType] = useState(String(local.get("platFormGoodsTradeFindType") ?? "1"));
	const { groupList } = groupStore;
	const isSupplierUserAndStock = isSupplierAccount && !isShowZeroStockVersion; // 常规版的库存版
	const isSupplierUserAndZeroStock = isSupplierAccount && isShowZeroStockVersion; // 常规版的零库存版
	// console.log('%c [ 版本：免费分销商、常规库存、常规零库存 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', isDistributorAccount, isSupplierUserAndStock, isSupplierUserAndZeroStock);
	const isSupplierStock = [AUTH_USER_TYPE.常规版库存版, AUTH_USER_TYPE.默认常规版库存版].includes(getAuthUserType);
	const isSupplierZeroStock = [AUTH_USER_TYPE.常规版零库存版, AUTH_USER_TYPE.默认常规版零库存版].includes(getAuthUserType);
	// 在组件内部添加状态
	const [submitSuccessVisible, setSubmitSuccessVisible] = useState(false);
	const [submittedCount, setSubmittedCount] = useState(0);

	// 列配置相关state
	const [colSettingVisible, setColSettingVisible] = useState(false);
	const [pageColumnConfig, setPageColumnConfig] = useState({ userConfig: [], defaultConfig: [] });
	const [colConfigLoading, setColConfigLoading] = useState(false);
	const [pageColumnAllConfig, setPageColumnAllConfig] = useState({
		item: { userConfig: [], defaultConfig: [] },
		sku: { userConfig: [], defaultConfig: [] }
	});
	const [userActivityInfo, setUserActivityInfo] = useState<any>(null);
	const [hasActivityEligibility, setHasActivityEligibility] = useState(false);
	useEffect(() => {
		if (isDistributorAccount) {
			sendPoint(Pointer.分销商_平台商品管理_页面展现_展现);
		}

	}, [isDistributorAccount]);

	useEffect(() => {
		setPageColumnConfig(pageColumnAllConfig[isShowSku ? 'sku' : 'item']);
	}, [pageColumnAllConfig, isShowSku]);

	useEffect(() => {
		PrintCenter.getBqTempList(BQ_PRINT_TYPE.吊牌);
	}, []);

	const afterSyncWaresOK = useCallback(() => {
		tableRef.current.refresh();
	}, []);

	const groupRefresh = () => {
		groupStore.getGroupList(true);
	};

	useEffect(() => { groupRefresh(); }, []);

	useEffect(() => {
		tradeStore.tradeListStore.keyDown();
		return () => {
			tradeStore.tradeListStore.cancelKeyDown();
		};
	}, []);

	const onCloseDownloadExcelModal = () => {
		setDownloadExcelModalVisible(false);
	};

	const tradeFindTypeChange = (e) => {
		e.stopPropagation();
		e.preventDefault();
		local.set("platFormGoodsTradeFindType", e.target.value);
		setTradeFindType(e.target.value);
	};

	// 导出excel文档
	const onDownloadExcel = async() => {
		let params = { ...curSearchParams, exportType, isZeroItemExport: true };
		if (exportType === 1) {
			// 导出已勾选
			if (selectedRows.length === 0) {
				message.warning('当前未勾选任何货品！');
				return;
			}
			const list = getList(selectedRows, isShowSku);
			params['skuIds'] = list.map(i => i.skuId);
			params['numIids'] = Array.from(new Set(list.map(i => i.numIid)));
		}
		params['hideDeleteSku'] = isHideDelete;
		params['tradeWaitSendFlag'] = getTradeWaitSendFlag();
		setExportLoading(true);
		await downloadCenter({
			requestParams: params,
			fileName: '平台商品',
			module: ModulesFunctionEnum.货品与商品关系_0库存版
		});
		setExportLoading(false);
		onCloseDownloadExcelModal();
		sendPoint(exportType === 1 ? Pointer.商品_平台商品管理_导出已勾选_成功导出 : Pointer.商品_平台商品管理_导出查询结果_成功导出);
		// runExportSkuApi(params);
	};

	const onChangeExportType = (e:any) => {
		setExportType(e.target.value);
		const localUserId = UserStore.userInfo.subUserId || UserStore.userInfo.userId;
		if (localUserId) {
			localStorage.setItem(`ExportType_PlatformGoods_${localUserId}`, e.target.value);
		}
	};

	useEffect(() => {
		const localUserId = UserStore.userInfo.subUserId || UserStore.userInfo.userId;
		if (localUserId) {
			if (downloadExcelModalVisible) {
				const num = localStorage.getItem(`ExportType_PlatformGoods_${localUserId}`);
				num && setExportType(Number(num));
			}
		}
	}, [downloadExcelModalVisible]);

	useEffect(() => {
		UserStore.getUserSetting().then(res => {
			setIsKg(res?.weightUnit == weightUnit.显示kg);
		});

		UserStore.getSystemSetting().then(res => {
			setDropShippingAutoPush(res?.dropShippingFlag || 0);
		});

		setIsShowSupplier(local.getByUserId("warehouse.platformGoods.isShowSupplier") || false);
		setIsShowSku(local.getByUserId("warehouse.platformGoods.isShowSku") || false);
		setIsShowSkuPic(local.getByUserId("warehouse.platformGoods.isShowSkuPic") || false);
		setIsHideDelete(local.getByUserId("warehouse.platformGoods.isHideDelete") || false);

		// 获取预览图展示配置
		getSkuPicPreviewConfig();
		getSkuPicGoodsConfig();
	}, []);

	// 获取预览图展示配置
	const getSkuPicGoodsConfig = async() => {
		let configValue = "1";
		try {
			let res = await TradeDictQueryDictApi({ userDictEnum: 'PLAT_ITEM_PAGE_COLUMN_PIC_CONFIG' });
			console.log('预览图配置API返回:', res);
			if (res?.value) {
				try {
					const parsedValue = JSON.parse(res.value);
					configValue = String(parsedValue.value);
				} catch (e) {
					console.log('Failed to parse config', e);
				}
			}
		} catch (error) {
			console.log(error, 'error');
		}
		console.log('设置预览图配置:', configValue, typeof configValue);
		setSkuPicGoodsConfig(configValue);
	};

	// 获取预览图展示配置
	const getSkuPicPreviewConfig = async() => {
		let configValue = "1";
		try {
			let res = await TradeDictQueryDictApi({ userDictEnum: 'PLAT_ITEM_PAGE_COLUMN_PIC_PREVIEW_CONFIG' });
			console.log('预览图配置API返回:', res);
			if (res?.value) {
				try {
					const parsedValue = JSON.parse(res.value);
					configValue = String(parsedValue.value);
				} catch (e) {
					console.log('Failed to parse config', e);
				}
			}
		} catch (error) {
			console.log(error, 'error');
		}
		console.log('设置预览图配置:', configValue, typeof configValue);
		setSkuPicPreviewConfig(configValue);
	};

	// 处理预览图展示配置变化
	const onSkuPicGoodsConfigChange = async(value: string) => {
		try {
			const config = { value };
			console.log('设置预览图配置为:', config, typeof value);

			const res = await TradeDictInsertDictApi({
				userDictEnum: 'PLAT_ITEM_PAGE_COLUMN_PIC_CONFIG',
				value: JSON.stringify(config)
			}).then(() => {
				// 直接设置字符串值
				setSkuPicGoodsConfig(value);
			});
			console.log('保存预览图配置响应:', res);
		} catch (error) {
			console.log(error, 'error');
		}
	};

	// 处理预览图展示配置变化
	const onSkuPicPreviewConfigChange = async(value: string) => {
		try {
			const config = { value };
			console.log('设置预览图配置为:', config, typeof value);

			const res = await TradeDictInsertDictApi({
				userDictEnum: 'PLAT_ITEM_PAGE_COLUMN_PIC_PREVIEW_CONFIG',
				value: JSON.stringify(config)
			}).then(() => {
				// 直接设置字符串值
				setSkuPicPreviewConfig(value);
			});
			console.log('保存预览图配置响应:', res);
		} catch (error) {
			console.log(error, 'error');
		}
	};

	// 表单元素
	const FormFieldList: FormItemConfig[] = [
		{
			name: "platformInfo",
			children: (
				<ShopMultiSelect
					isSendPoint
					style={ { width: '159px' } }
				/>
			),
		},
		{
			name: 'approveStatus',
			children: (
				<Select className={ cs('r-w-full') } placeholder="销售状态" size="small" style={ { width: 160 } }>
					<Option value={ null } key={ 0 }>销售状态</Option>
					<Option value="onsale" key={ 1 }><span className="r-c-error">出售中</span></Option>
					<Option value="instock" key={ 2 }>已下架</Option>
					<Option value="delete" key={ 3 }>已删除</Option>
				</Select>)
		},
		{
			name: "title",
			children: <Input placeholder="商品名称" style={ { width: 160 } } />,
		},
		{
			name: 'skuNameList',
			children: (
				<Tooltip title="支持输入多个关键词，使用空格隔开，空格表示并且">
					<Form.Item name="skuNameList" style={ { margin: 0 } }><Input placeholder="规格名称" style={ { width: 160 } } /></Form.Item>
				</Tooltip>
			),
		},
		{
			name: "numIids",
			children: <InputMulti maxInputNum={ 500 } placeholder="商品ID" numErrorMsg="单次查询最多筛选500个请重新输入" size="small" />
		},
		{
			name: "sysItemAlias",
			children: <Input placeholder="简称" style={ { width: 160 } } />,
		},
		{
			name: "outerId",
			children: (
				<Tooltip title="默认模糊搜索，如需精确搜索请在字段开头加上@@">
					<Form.Item name="outerId" style={ { margin: 0 } }><Input placeholder="商家编码" style={ { width: 160 } } /></Form.Item>
				</Tooltip>
			) 
		},
		{
			name: "skuOuterId",
			children: (
				<Tooltip title="默认模糊搜索，如需精确搜索请在字段开头加上@@">
					<Form.Item name="skuOuterId" style={ { margin: 0 } }><Input placeholder="规格编码" style={ { width: 160 } } /></Form.Item>
				</Tooltip>
			)
		},
		{
			name: 'skuContent',
			children: <Input placeholder="规格名称/规格别名" />
		}, {
			name: 'existSkuAlias',
			children: (
				<Select className={ cs('r-w-full') } placeholder="是否设置规格别名" size="small" style={ { width: 160 } }>
					<Option value={ null } key={ 0 }>是否设置规格别名</Option>
					<Option value={ 0 } key={ 1 }>已设置规格别名</Option>
					<Option value={ 1 } key={ 2 }>未设置规格别名</Option>
				</Select>)
		},
		{
			name: 'existItemAlias',
			children: (
				<Select className={ cs('r-w-full') } placeholder="是否设置简称" size="small" style={ { width: 160 } }>
					<Option value={ null } key={ 0 }>是否设置简称</Option>
					<Option value={ 0 } key={ 1 }>已设置简称</Option>
					<Option value={ 1 } key={ 2 }>未设置简称</Option>
				</Select>)
		},
		{
			name: 'existWeight',
			children: (
				<Select className={ cs('r-w-full') } placeholder="是否设置重量" size="small" style={ { width: 160 } }>
					<Option value={ null } key={ 0 }>是否设置重量</Option>
					<Option value={ 0 } key={ 1 }>已设置重量</Option>
					<Option value={ 1 } key={ 2 }>未设置重量</Option>
				</Select>)
		},
		{
			name: 'existCostPrice',
			children: (
				hasCostPricePermission ? (
					<Select className={ cs('r-w-full') } placeholder="是否设置成本价" size="small" style={ { width: 160 } }>
						<Option value={ null } key={ 0 }>是否设置成本价</Option>
						<Option value={ 0 } key={ 1 }>已设置成本价</Option>
						<Option value={ 1 } key={ 2 }>未设置成本价</Option>
					</Select>
				) : (
					<FieldsPermissionCheck fieldsPermission={ FieldsPermissionEnum.成本价 }>
						<Select disabled={ !hasCostPricePermission } className={ cs('r-w-full') } placeholder="是否设置成本价" size="small" style={ { width: 160 } }>
							<Option value={ null } key={ 0 }>是否设置成本价</Option>
							<Option value={ 0 } key={ 1 }>已设置成本价</Option>
							<Option value={ 1 } key={ 2 }>未设置成本价</Option>
						</Select>
					</FieldsPermissionCheck>
				)
			)
		},
		{
			name: "market",
			children: <Input placeholder="市场" style={ { width: 160 } } />,
		},
		{
			name: "stall",
			children: <Input placeholder="档口" style={ { width: 160 } } />,
		},
		{
			name: "supplierName",
			children: <Input placeholder="供应商名称" style={ { width: 160 } } />,
		},
		{
			name: 'existMarket',
			children: (
				<Select className={ cs('r-w-full') } placeholder="是否设置市场" size="small" style={ { width: 160 } }>
					<Option value={ null } key={ 0 }>是否设置市场</Option>
					<Option value={ 0 } key={ 1 }>已设置市场</Option>
					<Option value={ 1 } key={ 2 }>未设置市场</Option>
				</Select>)
		},
		{
			name: 'existStall',
			children: (
				<Select className={ cs('r-w-full') } placeholder="是否设置档口" size="small" style={ { width: 160 } }>
					<Option value={ null } key={ 0 }>是否设置档口</Option>
					<Option value={ 0 } key={ 1 }>已设置档口</Option>
					<Option value={ 1 } key={ 2 }>未设置档口</Option>
				</Select>)
		},
		{
			name: 'existSupplier',
			children: (
				<Select className={ cs('r-w-full') } placeholder="是否设置供应商" size="small" style={ { width: 160 } }>
					<Option value={ null } key={ 0 }>是否设置供应商</Option>
					<Option value={ 0 } key={ 1 }>已设置供应商</Option>
					<Option value={ 1 } key={ 2 }>未设置供应商</Option>
				</Select>)
		},
		{
			name: 'bindLabelPushPlatform',
			children: (
				<Select className={ cs('r-w-full') } placeholder="是否绑定四季星座网商品" size="small" style={ { width: 160 } }>
					<Option value={ null } key={ 0 }>是否绑定四季星座网商品</Option>
					<Option value key={ 1 }>已绑定四季星座网商品</Option>
					<Option value={ false } key={ 2 }>未绑定四季星座网商品</Option>
				</Select>)
		},
		{
			name: 'classifyId',
			children: (
				<Select className={ cs('r-w-full') } placeholder="选择商品分类" size="small" style={ { width: 160 } }>
					<Option value={ null } key={ 0 }>选择商品分类</Option>
					{groupList?.filter(f => f.classifyId != '-1')?.map(s => <Option value={ s.classifyId } key={ s.classifyId }>{ s.classifyName}</Option>)}
				</Select>)
		},
		{
			name: 'existClassify',
			children: (
				<Select className={ cs('r-w-full') } placeholder="是否设置分类" size="small" style={ { width: 160 } }>
					<Option value={ null } key={ 0 }>是否设置分类</Option>
					<Option value key={ 1 }>已设置商品分类</Option>
					<Option value={ false } key={ 2 }>未设置商品分类</Option>
				</Select>)
		},
		{
			name: 'itemSkuCreateTime',
			children: (
				<KdzsDateRangePicker1 placeholder={ ['上新时间起', '上新时间止'] } allowClear style={ { width: 160 } } />
			)
		}
	];


	const distributorFormFieldList: FormItemConfig[] = (() => {
		let tempList = [];
		if (isSupplierUserAndStock) { // 常规库存版
			tempList = FormFieldList.filter(item => !distributorFilterFormFields1.includes(item.name + ''));
		} else { // 分销商
			tempList = FormFieldList.filter(item => !distributorFilterFormFields.includes(item.name + ''));
		}
		tempList.push({
			name: 'selectDropShippingItemFlag',
			children: (
				<Select className={ cs('r-w-full') } placeholder="有无代发商品" size="small" style={ { width: 160 } }>
					<Option value={ null } key={ 0 }>有无代发商品</Option>
					<Option value key={ 1 }>有代发</Option>
					<Option value={ false } key={ 2 }>无代发</Option>
				</Select>)
		});

		return tempList;
	})();

	// group选择
	const onCheckedGroup = (e:CheckboxChangeEvent, record: any) => {
		const { isShiftDown } = tradeStore.tradeListStore;
		if (!isShiftDown) {
			let checked = false;
			setDataSource(prev => {
				prev.forEach(item => {
					if (item.groupId == record.groupId) {
						item.isChecked = e.target.checked;
					}
					checked = e.target.checked;
				});
				return [...prev];
			});
			setOldSelectGroupObj({
				index: record.groupId,
				checked
			});
		} else {
			dealGroupShift(record);
		}

	};
	// 单选
	const onCheckedItem = (e:CheckboxChangeEvent, record: any) => {
		const { isShiftDown } = tradeStore.tradeListStore;
		if (!isShiftDown) {
			let checked = false;
			setDataSource(prev => {
				let prevIndex = prev.findIndex(d => d.rowId === record.rowId);
				if (prevIndex > -1) {
					prev[prevIndex].isChecked = e.target.checked;
					checked = e.target.checked;
				}
				return [...prev];
			});
			setOldSelectObj({
				index: record.rowId,
				checked
			});
		} else {
			dealShift(record);
		}

	};
	// 全选
	const onCheckAllChange = (e:CheckboxChangeEvent) => {
		setDataSource(prev => {
			prev.forEach(item => {
				if (!item.checkDisabled) {
					item.isChecked = e.target.checked;
				}
			});
			return [...prev];
		});
	};

	const dealShift = (record) => {
		const { rowId } = record;
		if (oldSelectObj.index) {
			const { index, checked } = oldSelectObj;
			const [startRowId, endRowId] = compareRowId(rowId, index);

			setDataSource(prev => {
				prev.forEach((item) => {
					if (belongStartAndEnd({ startRowId, endRowId, compareRowId: item.rowId })) {
						item.isChecked = checked;
					}
				});
				return [...prev];
			});
		}
	};

	const dealGroupShift = (record) => {
		const { groupId } = record;
		if (oldSelectGroupObj.index > -1) {
			const { index, checked } = oldSelectGroupObj;
			const { start, end } = compareNum(groupId, index);
			setDataSource(prev => {
				prev.forEach((item) => {
					if (item.groupId >= start && item.groupId <= end) {
						item.isChecked = checked;
					}
				});
				return [...prev];
			});
		}
	};

	const checkedGroup = useMemo(() => {
		let filterList = dataSource.filter(item => !item.isCollapse);
		const tmp = { num: 0, checked: false, disabled: false, disNum: 0, indeterminate: false };
		if (filterList.length == 0) return { group_all: tmp };
		const map = {
			'group_all': { ...tmp, total: filterList.length }
		};
		filterList.forEach(item => {
			if (!map[item.groupId]) map[item.groupId] = { ...tmp, total: item.colSpan };

			if (item.checkDisabled) {
				map[item.groupId].disNum += 1;
				map['group_all'].disNum += 1;
			}
			if (item.isChecked) {
				map[item.groupId].num++;
				map['group_all'].num++;
			}
		});
		for (let key in map) {
			const { disNum, total, num } = map[key];
			if (disNum == total) {
				map[key].disabled = true;
			}
			if (num == total || (num > 0 && num + disNum == total)) {
				map[key].checked = true;
			}
			if (num > 0 && num + disNum < total) {
				map[key].indeterminate = true;
			}
		}
		return map;
	}, [dataSource]);

	const selectedRows = useMemo(() => {
		return dataSource.filter(item => !item.isCollapse && item.isChecked);
	}, [dataSource]);

	const onCheckShowSku = () => {
		const nextVal = !isShowSku;
		setIsShowSku(nextVal);
		if (!nextVal) {
			setIsShowSkuPic(false);
		}
		local.setByUserId('warehouse.platformGoods.isShowSku', nextVal);
		if (nextVal) return responseAdapterSku(originData);
		else return responseAdapterOut(originData);
	};

	const onCheckShowSkuPic = () => {
		const nextVal = !isShowSkuPic;
		local.setByUserId('warehouse.platformGoods.isShowSkuPic', nextVal);
		setIsShowSkuPic(nextVal);
	};

	const onCheckShowSupplier = () => {
		const nextVal = !isShowSupplier;
		local.setByUserId('warehouse.platformGoods.isShowSupplier', nextVal);
		setIsShowSupplier(nextVal);
	};

	const onCheckShowDelete = () => {
		const nextVal = !isHideDelete;
		local.setByUserId('warehouse.platformGoods.isHideDelete', nextVal);
		setIsHideDelete(nextVal);
	};

	const onChangeTradeWaitSendFlag = (e) => {
		const { checked } = e.target;
		if (checked) sendPoint(Pointer.零库存版_勾选有待发货订单_查询);
		setTradeWaitSendFlag((prev) => checked);
		tableRef.current.refresh();
	};

	const backFillData = useCallback(() => {
		const map = {};
		CUR_CHANGED_LIST.forEach(i => {
			map[i.numIid] = i;
		});
		const formValue = { ...dataFormSource, ...form.getFieldsValue() };
		setOriginData(pre => {
			pre.list.forEach((i: { numIid: string | number; platformItemSkuList: any[]; }) => {
				if (map[i.numIid]) {
					// if (!isShowSku) {
					// 	['costPrice', 'weight', 'supplierName', 'stall', 'market'].forEach(key => {
					// 		if (key === 'costPrice') {
					// 			i['itemCostPrice'] = formValue[nameFactory(i.numIid, key)];
					// 		}
					// 		if (key === 'weight') {
					// 			i['itemWeight'] = formValue[nameFactory(i.numIid, key)];
					// 		}
					// 		if (key === 'supplierName') {
					// 			i['itemSupplierName'] = formValue[nameFactory(i.numIid, key)];
					// 		}
					// 		if (key === 'stall') {
					// 			i['itemStall'] = formValue[nameFactory(i.numIid, key)];
					// 		}
					// 		if (key === 'market') {
					// 			i['itemMarket'] = formValue[nameFactory(i.numIid, key)];
					// 		}
					// 	});
					// }
					i.platformItemSkuList?.forEach((pItem, xIndex) => {
						const fillbackArr = ['costPrice', 'weight', 'sysItemAlias'];
						if (isShowSku) fillbackArr.push('sysSkuAlias', 'tagPrice', 'classifyId');
						if (isShowSupplier) fillbackArr.push('supplierName', 'stall', 'market');
						fillbackArr.forEach(key => {
							try {
								let factoryId = isShowSku ? map[i.numIid].platformItemSkuList[xIndex].rowId : i.numIid;
								if (key == 'sysItemAlias') factoryId = i.numIid;
								if (!pItem!.relationSystemItemList[0]) pItem!.relationSystemItemList[0] = {};
								const nextVal = formValue[nameFactory(factoryId, key)];
								if (!isShowSku && ['costPrice', 'weight', 'supplierName', 'stall', 'market'].includes(key)) return;
								if (isShowSku || nextVal !== '' || key == 'sysItemAlias') pItem!.relationSystemItemList[0][key] = nextVal;
							} catch (error) {
								console.log(error);
							}

						});

					});

				}
			});
			return { ...pre };
		});
	}, [form, isShowSku, isShowSupplier, dataFormSource]);

	function escapeRegExp(string) {
		return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // $& 表示整个匹配的字符串
	}

	const onBatchEditOK = (val: { addContent: any; addType: any; addRule: any;}, type: any) => {
		const { addContent, addType = addPosMap.覆盖, addRule = null } = val;
		const newFormVal = { ...dataFormSource, ...form.getFieldsValue() };
		const rows = getList(selectedRows, isShowSku);
		let _updateFlag = '';
		rows.forEach((j: { [x: string]: any; skuId?: any; }, skuIndex) => {
			let factoryId = isShowSku ? j.rowId : j.numIid;
			if (type == 'sysItemAlias') factoryId = j.numIid;
			const oldVal = newFormVal[nameFactory(factoryId, type)] || '';
			let newVal = getNewContent(addContent, { ...j }, type) || '';
			if ([nameMap.别名, nameMap.简称].includes(type)) {
				if (type === nameMap.简称) {
					if (isShowSku) {
						if (_updateFlag === j.relationSystemItem.sysItemId) return;
						_updateFlag = j.relationSystemItem.sysItemId;
					}
				}
				if (type === nameMap.别名) {
					const { filter } = val;
					newVal = filterText(newVal, filter);
				}
				const { prefix = '', suffix = '', replaceKeyword = '', replaceWith = '', deleteKeyword = '' } = val;
				if (type === nameMap.简称 && skuIndex > 0 && j.numIid === rows[skuIndex - 1].numIid) {
					newVal = oldVal;
				} else if (val.addType === 2) {
					newVal = prefix + oldVal + suffix;
				} else if (val.addType === 3) {
					const regex = new RegExp(escapeRegExp(replaceKeyword), 'gi');
					newVal = oldVal.replace(regex, replaceWith);
				} else if (val.addType === 4) {
					const regex = new RegExp(escapeRegExp(deleteKeyword), 'gi');
					newVal = oldVal.replace(regex, '');
				}
			} else {
				if (addRule) {
					newVal = getAddRuleContent(addRule, { ...j });
				}
				if (addType == addPosMap.现有前) {
					newVal += oldVal;
				}
				if (addType == addPosMap.现有后) {
					newVal = oldVal + newVal;
				}
			}
			console.log(rows, newVal, '----------------');
			let subStrNum;
			if (['sysSkuAlias', 'sysItemAlias'].includes(type)) subStrNum = 60;
			if (['market', 'stall', 'supplierName'].includes(type)) subStrNum = 64;
			if (type === batchEditFuncName.批量设置自定义属性) {
				newFormVal[nameFactory(factoryId, addType)] = subStrNum ? newVal.substring(0, subStrNum) : newVal;
			} else {
				newFormVal[nameFactory(factoryId, type)] = subStrNum ? newVal.substring(0, subStrNum) : newVal;
			}

		});
		form.setFieldsValue(newFormVal);
		setDataFormSource(pre => ({ ...pre, ...newFormVal }));
	};

	const batchSaveContent = async(selectedList?: any[]) => {
		const formValue = { ...dataFormSource, ...form.getFieldsValue() };
		let isBatch = false;
		if (!selectedList) {
			selectedList = selectedRows;
			isBatch = true;
		}
		CUR_CHANGED_LIST = selectedList;

		const loadingName = isBatch ? 'all' : selectedList[0].rowId;
		setLoadingMap(pre => ({ ...pre, [`${loadingName}Loading`]: true }));

		let list = getList(selectedList, isShowSku);
		const map: {
			[k: string]: ItemItemBatchUpdateSysItemByPlatformRequest[number],
		} = {};
		const otherBindObj: BindProp = {} as BindProp;
		const bindedObj = {};

		list.forEach(j => {
			const factoryId = isShowSku ? j.rowId : j.numIid;
			const sysItem = j.relationSystemItemList[0];
			const oldSysItemAlias = sysItem?.sysItemAlias || "";
			const newSysItemAlias = formValue[nameFactory(j.numIid, 'sysItemAlias')];
			let bindRelation:any = {
				numIid: j.numIid,
				skuId: j.skuId,
				operateType: 'CREATE',
				sysItemAlias: formValue[nameFactory(j.numIid, 'sysItemAlias')],
				sysSkuAlias: formValue[nameFactory(factoryId, 'sysSkuAlias')],
				weight: formValue[nameFactory(factoryId, 'weight')],
				costPrice: formValue[nameFactory(factoryId, 'costPrice')],
				tagPrice: formValue[nameFactory(factoryId, 'tagPrice')],
				classifyId: formValue[nameFactory(factoryId, 'classifyId')],
				market: formValue[nameFactory(factoryId, 'market')],
				supplierName: formValue[nameFactory(factoryId, 'supplierName')],
				stall: formValue[nameFactory(factoryId, 'stall')],
				sellerId: j.sellerId,
				platformType: j.platform,
				userViewVal: UserStore.userInfo.userType
			};
			if (UserStore.userInfo.userType === USER_TYPE.免费分销商) {
				bindRelation.saleUserId = UserStore.userInfo.userId;
			}
			// 自定义属性
			const itemCustomAttributeList = UserStore?.systemSetting?.itemCustomAttributeDTOList;
			if (UserStore?.systemSetting?.itemCustomAttribute === autoSearch.开启 && itemCustomAttributeList?.length) {
				bindRelation.customAttributesList = itemCustomAttributeList.map((item) => {
					return {
						key: item.key,
						name: item.name,
						value: formValue[nameFactory(factoryId, item.key)]
					};
				});
				bindRelation.customAttributes = JSON.stringify(bindRelation.customAttributesList);
			 }
			// 修改过简称的 需要将其余规格也带上
			if (isShowSku && oldSysItemAlias !== newSysItemAlias) {
				const platformItemSkuList = j.platformItemSkuList;
				platformItemSkuList.forEach(item => {
					const key = `${j.numIid}_${item.skuId}`;
					const _sysItem = item.relationSystemItemList[0] || {};
					const { sysSkuAlias = "", weight = "", costPrice = "", tagPrice = "", classifyId = "", market = "", supplierName = "", stall = "", sysItemId, sysSkuId } = _sysItem;
					if (j.skuId !== item.skuId && !otherBindObj?.[key]) {
						otherBindObj[`${j.numIid}_${item.skuId}`] = {
							numIid: j.numIid,
							skuId: item.skuId,
							operateType: _sysItem ? 'BIND' : 'CREATE',
							sysItemAlias: formValue[nameFactory(j.numIid, 'sysItemAlias')],
							mapKey: `${j.platform}-${j.sellerId}`,
							sysItemId,
							sysSkuId,
							sysSkuAlias,
							weight,
							costPrice,
							tagPrice,
							classifyId,
							market,
							supplierName,
							stall,
							sellerId: j.sellerId,
							platformType: j.platform,
						};
					}
				});
			}

			['weight', 'costPrice'].forEach(i => {
				if (bindRelation[i] == null) bindRelation[i] = '';
			});
			// if (!isShowSku) {
			// 	// // 非规格维度，为空的内容不做设值处理。
			// 	// ['sysSkuAlias', 'weight', 'costPrice', 'market', 'supplierName', 'stall', 'tagPrice', 'classifyId'].forEach((key) => {
			// 	// 	if (bindRelation[key] === '') {
			// 	// 		bindRelation[key] = sysItem?.[key];
			// 	// 	}
			// 	// });
			// 	bindRelation['itemWeight'] = bindRelation['weight'] ?? '';
			// 	bindRelation['itemCostPrice'] = bindRelation['costPrice'] ?? '';
			// 	bindRelation['itemSupplierName'] = bindRelation['supplierName'] ?? '';
			// 	bindRelation['itemMarket'] = bindRelation['market'] ?? '';
			// 	bindRelation['itemStall'] = bindRelation['stall'] ?? '';

			// 	['weight', 'costPrice', 'market', 'supplierName', 'stall'].forEach(i => {
			// 		bindRelation[i] = sysItem[i];
			// 	});

			// }


			// 档口、市场、供应商可以置空
			if (!isShowSku) {
				// 非规格维度，为空的内容不做设值处理。
				['sysSkuAlias', 'weight', 'costPrice', 'tagPrice', 'classifyId'].forEach((key) => {
					if (bindRelation[key] === '') {
						bindRelation[key] = sysItem?.[key];
					}
				});
			}

			if (sysItem) {
				bindRelation = {
					...bindRelation,
					sysItemId: sysItem.sysItemId,
					sysSkuId: sysItem.sysSkuId,
					operateType: 'BIND'
				};
			}
			const mapKey = `${j.platform}-${j.sellerId}`;
			if (!map[mapKey]) {
				map[mapKey] = {
					sellerId: j.sellerId,
					platformType: j.platform,
					bindRelations: []
				};
			}
			bindedObj[`${j.numIid}_${j.skuId}`] = bindRelation;
			map[mapKey]?.bindRelations?.push(bindRelation);
		});


		Object.keys(otherBindObj).forEach((key) => {
			const item = otherBindObj[key];
			const { mapKey, numIid, skuId } = item;
			let _obj = { ...item };
			delete _obj.mapKey;
			if (!bindedObj[`${numIid}_${skuId}`]) { map[mapKey]?.bindRelations?.push(_obj); }
		});

		if (!hasCostPricePermission) {
			Object.values(map).forEach(item => {
				item?.bindRelations.forEach(bindItem => {
					delete bindItem.costPrice;
				});
			});
		}

		try {
			if (isBatch) {
				const params = [];
				Object.values(map).forEach((item) => {
					params.push(...item.bindRelations);
				});
				setLoadingMap(pre => ({ ...pre, all: true }));
				params[0].sysItemLogSourceEnum = 'ITEM_RECORD_EDIT';
				await BatchUpdateSysItemByPlatformApi(params);
				handleLoading('success');
			} else {
				let params = Object.values(map);
				params[0].sysItemLogSourceEnum = 'ITEM_RECORD_EDIT';
				await UpdateSysItemByPlatformApi(params);
				setLoadingMap(pre => ({ ...pre, [`${loadingName}Loading`]: false, [`${loadingName}Result`]: 'success' }));
				backFillData();
			}
		} catch (error) {
			if (isBatch) {
				handleLoading('fail');
			} else {
				setLoadingMap(pre => ({ ...pre, [`${loadingName}Loading`]: false, [`${loadingName}Result`]: 'fail' }));
			}

		}
	};


	const batchDeleteSku = (selectedList: any[]) => {
		let rows = isShowSku ? selectedList : selectedList.reduce((arr, v) => arr.concat(v.platformItemSkuList.map(i => ({ ...v, ...i }))), []);
		let skuIdList = rows.map((i:any) => ({
			numIid: i.numIid,
			skuId: ['tb', 'ali'].includes(i.platform) && i.skuId == i.numIid ? '0' : i.skuId,
			sellerId: i.sellerId
		}));
		if (!skuIdList.length) {
			message.warning('请先选择规格');
			return;
		}
		confirm({
			centered: true,
			title: '批量删除商品/规格',
			icon: <ExclamationCircleOutlined />,
			content: (
				<div>
					<div>确定删除选中的<span style={ { color: '#f00' } }>{skuIdList?.length}</span>个平台商品规格吗？</div>
					<div className="r-fs-12 r-mt-8 r-c-666">注意：删除仅对系统内生效，不影响平台商品数据；如勾选商品下所有规格，则直接删除对应商品及其全部规格数据，否则只删除对应商品规格；删除后，会更新待发货订单上的商品信息</div>
				</div>
			),
			okText: '确认',
			cancelText: '取消',
			onOk: async() => {
				await batchDeleteBySkuIdsApi({ skuDTOList: skuIdList });
				message.success('删除成功');
				tableRef.current.refresh();
			}
		});
	};

	const handleLoading = (result) => {
		setLoadingMap(pre => ({ ...pre, all: false, allResult: result }));
		// 保存成功，切换显示规格时，回显保存数据
		if (result == 'success') {
			backFillData();
		}
	};

	const showDropShippingSku = () => {
		setIsShowSku(true);
		onCheckShowSku();
	};

	const showLog = (row) => {
		sendPoint(Pointer["分销商_平台商品管理_点击-日志_点击"]);
		setPlatformGoodsLogInfo({
			show: true,
			info: row,
			type: isShowSku ? 'sku' : 'item'
		});
	};

	const onBindSupplier = (row) => {
		setBatchSetGoodsVisible(true);
		setOptRow(row);
		setOptRowBindSupplierId(row.dropShippingSupplierUserId);
	};

	// /** 合并单元格 */
	// const getCellProps = (t, r, i) => {
	// 	if (!r?.colSpan) {
	// 		if (r?.platformItemSkuList?.length > 2) {
	// 			let isLast = r?.platformItemSkuList.at(-1)?.rowId === r?.rowId;
	// 			return {
	// 				colSpan: 5,
	// 				style: isLast ? {} : {
	// 					borderBottom: 'none'
	// 				}
	// 			};
	// 		} else {
	// 			return { colSpan: 5 };
	// 		}
	// 	} else if (isShowSku) {
	// 		return {
	// 			style: {
	// 				borderBottom: 'none',
	// 				borderRight: 'none'
	// 			}
	// 		};
	// 	}
	// };

	const getColumns = useMemo(() => {
		// 原有的列定义逻辑保持不变
		const EditIcon = ({ type, attrType = {} }) => (
			<FormOutlined
				onClick={ () => {
					sendTypePoint(type);
					if (selectedRows.length == 0) return message.warn('需先勾选商品');
					setBatchEditModalInfo({ visible: true, type, ...attrType });
				} }
				className={ s.titleIcon }
			/>
		);

		const inputWeightProps = isKg ? {
			formatter: (value: any) => (value === '' ? value : +(value / 1000).toFixed(3)),
			parser: (value: any) => (value === '' ? value : accMul(value, 1000)),
		} : {
			precision: 0
		};

		const FormatSkuColFn = (tmpCol: { title?: string | JSX.Element; align?: string; width?: number; render: any; dataIndex?: string; }, otherColProperties = {}) => {
			return {
				...tmpCol,
				...otherColProperties,
				onCell: onCellRender,
				render: (text: any, row: TableListData[number]) => {
					if (!row.colSpan) return null;
					return tmpCol.render(text, row);
				},
			};
		};

		const handleUnBindGoods = async(row) => {
			try {
				const unBindInfoList = [
					{
						numIid: row.numIid,
						platformNumIid: row.labelPushPlatformNumIid,
						skuId: row.skuId,
						platformSkuId: row.labelPushPlatformSkuId,
					}
				];
				await ItemTakeGoodsLabelUnBindLabelPushPlatformItemApi({ unBindInfoList, labelPushPlatform: labelPushPlatformEnum.四季星座网 });
				message.success("解绑成功");
				setDataSource(prev => {
					let prevIndex = prev.findIndex(d => d.rowId === row.rowId);
					if (prevIndex > -1) {
						prev[prevIndex].bindLabelPushPlatform = false;
					}
					return [...prev];
				});
			} catch (error) {
				console.log('handleUnBindGoods error:', error);
			}
		};

		const handleBindGoods = (list, row, stall) => {
			try {
				const _item = list[0];
				const bindInfo = {
					numIid: row.numIid,
					skuId: row.skuId,
					sellerId: row.sellerId,
					sellerNick: row.sellerNick,
					platform: row.platform,
					platformShopId: stall?.shopId,
					platformShopName: stall?.shopName,
					platformSiteId: stall?.siteId,
					platformSiteName: stall?.siteName,
					platformMarketName: stall?.marketName,
					platformMarketId: stall?.marketId,
					platformItemNo: _item.itemNo,
					platformItemPicUrl: _item.picUrl,
					platformItemTitle: _item.title,
					platformNumIid: _item.numIid,
					platformSize: _item.sysSkuList[0]?.size,
					platformColor: _item.sysSkuList[0]?.color,
					platformSkuId: _item.sysSkuList[0]?.skuId,
					platformSkuName: _item.sysSkuList[0]?.skuName,
					platformSkuPicUrl: _item.sysSkuList[0]?.skuPicUrl,
					platformSkuOuterId: _item.sysSkuList[0]?.skuOuterId,
				};
				if (row.bindLabelPushPlatform) {
					return ItemTakeGoodsLabelChangeBindLabelPushPlatformItemApi({
						changeBindInfoList: [
							{
								numIid: row.numIid,
								changeBeforePlatformNumIid: row.labelPushPlatformNumIid,
								skuId: row.skuId,
								changeBeforePlatformSkuId: row.labelPushPlatformSkuId,
								bindInfo,
							}
						],
						labelPushPlatform: labelPushPlatformEnum.四季星座网
					});
				} else {
					return ItemTakeGoodsLabelBindLabelPushPlatformItemApi({ bindInfoList: [bindInfo], labelPushPlatform: labelPushPlatformEnum.四季星座网 });
				}
			} catch (error) {
				console.log('handleBindGoods error: ', error);
			}
		};

		const handleBindGoodsOk = (list, row, stall) => {
			const _item = list[0];
			setDataSource(prev => {
				let prevIndex = prev.findIndex(d => d.rowId === row.rowId);
				if (prevIndex > -1) {
					prev[prevIndex].labelPushPlatformItemTitle = _item.title;
					prev[prevIndex].labelPushPlatformNumIid = _item.numIid;
					prev[prevIndex].labelPushPlatformSkuId = _item.sysSkuList[0]?.skuId;
					prev[prevIndex].labelPushPlatformSkuName = _item.sysSkuList[0]?.skuName;
					prev[prevIndex].labelPushPlatformSkuPicUrl = _item.sysSkuList[0]?.skuPicUrl;
					prev[prevIndex].bindLabelPushPlatform = true;
				}
				return [...prev];
			});
		};

		const checkCol = {
			title: (
				<Checkbox
					checked={ checkedGroup.group_all.checked }
					disabled={ checkedGroup.group_all.disabled }
					indeterminate={ checkedGroup.group_all.indeterminate }
					onChange={ onCheckAllChange }
				/>
			),
			align: "center",
			width: 30,
			render: (text, row) => {
				const { colSpan } = row;
				const checkNode = (
					<Checkbox
						disabled={ checkedGroup[row.groupId]?.disabled }
						onChange={ (e) => onCheckedGroup(e, row) }
						checked={ checkedGroup[row.groupId]?.checked }
						indeterminate={ checkedGroup[row.groupId]?.indeterminate }
					/>
				);
				return <>{colSpan && checkNode}</>;
			},
		};

		const tmpPlatCol = {
			title: '平台/店铺',
			width: 120,
			dataIndex: 'platformShop',
			render: (text, row: TableListData[number]) => {
				const platstyle = row.colSpan > 1 && isShowSku ? { position: 'absolute', top: '8px' } : {};
				return (
					<div className="r-flex" >
						<PlatformIcon platform={ row?.platform } />
						{row?.sellerNick}
					</div>
				);
			},
		};

		const goodsContent = (
			<div className="r-pd-2" style={ { display: 'flex' } }>
				<div className="r-mb-2">预览图展示：</div>
				<Radio.Group
					value={ skuPicGoodsConfig }
					onChange={ (e) => onSkuPicGoodsConfigChange(e.target.value) }
				>
					<div style={ { display: 'flex', flexDirection: 'row' } }>
						{settingArr.map(item => (
							<Radio key={ item.value } value={ item.value } style={ { marginRight: '8px' } }>{item.label}</Radio>
						))}
					</div>
				</Radio.Group>
			</div>
		);

		const tmpGoodsCol = {
			title: (
				<div className="r-flex r-ai-c">
					平台商品
					<Popover placement="bottom" title={ null } content={ goodsContent } trigger="click">
						<Icon className="r-ml-5 r-c-gray" type="guigeshezhi" size={ 16 } />
					</Popover>
				</div>
			),
			width: 250,
			dataIndex: "title",
			render: (text, row: TableListData[number]) => {
				const { platform, numIid, skuId, skuUuid } = row;
				const img = row?.itemPicUrl || row?.picUrl || DEFAULT_IMG;
				let id = numIid;
				if ([PLAT_XHS, PLAT_JD].includes(platform)) {
					id = skuId;
				}
				if ([PLAT_YZ].includes(platform)) {
					id = skuUuid;
				}
				const getLinkHref = getPlatformDetailLink(platform, id);
				const platstyle = row.colSpan > 1 && isShowSku ? {} : {};
				// 根据配置设置图片大小
				const imgSizePopover = {
					'0': 300, // 小图
					'1': 500, // 中图
					'2': 800 // 大图
				}[skuPicGoodsConfig] || 500;

				const popContent: ReactNode = (
					<Image
						width={ imgSizePopover }
						height={ imgSizePopover }
						src={ img }
						fallback={ DEFAULT_IMG }
						preview={ false }
					/>
				);
				return (
					<div className={ cs("r-flex", s['cell-gap']) } style={ platstyle }>
						<Popover placement="right" content={ popContent }>
							<a
								href={ !UserStore.hasFieldsPermission(FieldsPermissionEnum.供应商店铺信息) && isSourceScm(row as any) ? undefined : getLinkHref || "javascript:void(0)" }
								target={ getLinkHref ? "_blank" : "_self" }
								rel="noopener noreferrer"
								className={ cs("r-flex", "r-ai-c") }
							>
								<Image
									width={ 48 }
									height={ 48 }
									src={ getImageThumbnail({
										noScale: false,
										url: img || DEFAULT_IMG,
										width: 48,
										height: 48
									}) }
									fallback={ DEFAULT_IMG }
									preview={ false }
								/>
							</a>
						</Popover>

						<div className={ cs(s.platTitle, "r-flex-1") }>
							<div title={ text } className={ s.lineMax2 }>
								{text}
							</div>
							<div
								className={ cs(s.lineMax1, "r-mt-4") }
								title={ row.numIid }
							>
								商品ID: {row.numIid}
							</div>
						</div>
					</div>
				);
			},
		};

		const tmpOuterIdCol = {
			title: "商家编码",
			width: 150,
			dataIndex: 'outerId',
			render: (text, row: TableListData[number]) => {
				const { outerId } = row;
				return (
					<div className={ s.lineMax1 } title={ outerId }>
						{outerId}
					</div>
				);
			}
		};

		const tmpSysItemAliasCol = {
			title: <>简称<EditIcon type="sysItemAlias" /></>,
			sortSet: { name: '简称' },
			width: 150,
			dataIndex: 'sysItemAlias',
			render: (text, row: TableListData[number]) => {
				const name = nameFactory(row.numIid, 'sysItemAlias');
				return (
					<Tooltip title={ dataFormSource?.[name] } trigger="hover">
						<Form.Item
							shouldUpdate
							name={ name }
							className={ cs('r-mb-0', 'r-mt-0') }
						>
							<Input
								maxLength={ 60 }
								style={ { width: '100%' } }
							/>
						</Form.Item>
					</Tooltip>
				);
			}
		};

		const tmpWeightCol = (key: formIdType) => ({
			title: <>重量({isKg ? 'kg' : 'g'})<EditIcon type="weight" /></>,
			width: 150,
			dataIndex: 'weight',
			render: (text, row: TableListData[number]) => {
				return (
					<Form.Item
						shouldUpdate
						name={ nameFactory(row[key], 'weight') }
						className={ cs('r-mb-0', 'r-mt-0') }
					>
						<WeightInput isKg={ isKg } width="100%" height={ 24 } />
					</Form.Item>
				);
			}
		});

		const tmpCostPriceCol = (key: formIdType) => ({
			title: (
				<FieldsPermissionCheck fieldsPermission={ FieldsPermissionEnum.成本价 }>
					成本价(元)
					<EditIcon type="costPrice" />
				</FieldsPermissionCheck>
			),
			width: 150,
			dataIndex: 'costPrice',
			render: (text, row: TableListData[number]) => {
				return (
					<FieldsPermissionCheck fieldsPermission={ FieldsPermissionEnum.成本价 }>
						<Form.Item
							shouldUpdate
							name={ nameFactory(row[key], 'costPrice') }
							className={ cs('r-mb-0', 'r-mt-0') }
						>
							<InputNumber
								disabled={ !hasCostPricePermission }
								min={ 0 }
								max={ 999999.9999 }
								precision={ 4 }
								formatter={ value => {
									const strValue = String(value || '');
									return strValue.includes('.') ? strValue.replace(/\.?0+$/, '') : strValue;
								} }
								style={ { width: '100%' } }
							/>
						</Form.Item>
					</FieldsPermissionCheck>
				);
			}
		});

		const tmpTagPriceCol = (key: formIdType) => ({
			title: <>吊牌价(元)<EditIcon type="tagPrice" /></>,
			width: 150,
			dataIndex: 'tagPrice',
			render: (text, row: TableListData[number]) => {
				return (
					<Form.Item
						shouldUpdate
						name={ nameFactory(row[key], 'tagPrice') }
						className={ cs('r-mb-0', 'r-mt-0') }
					>
						<InputNumber
							min={ 0 }
							max={ 999999.99 }
							precision={ 2 }
							style={ { width: '100%' } }
						/>
					</Form.Item>
				);
			}
		});

		// sku分类
		const tmpSkuClassifyIdCol = (key: formIdType) => ({
			title: <>商品分类<EditIcon type="classifyId" /></>,
			width: 150,
			dataIndex: 'classifyId',
			render: (text, row: TableListData[number]) => {
				return (
					<Form.Item
						shouldUpdate
						name={ nameFactory(row[key], 'classifyId') }
						className={ cs('r-mb-0', 'r-mt-0') }
					>
						<Select >
							{groupList?.map(s => <Option value={ s.classifyId } key={ s.classifyId }>{ s.classifyName}</Option>)}
						</Select>
					</Form.Item>
				);
			}
		});

		const tmpBindYpGoodsCol = (key: formIdType) => ({
			title: <>绑定四季星座网商品</>,
			width: 300,
			dataIndex: 'bindYp',
			render: (text, row: TableListData[number]) => {
				return (
					<Form.Item
						shouldUpdate
						name={ nameFactory(row[key], 'bindYp') }
						className={ cs('r-mb-0', 'r-mt-0') }
					>
						<div className="r-flex">
							{row.bindLabelPushPlatform ? (
								<div className="r-c-333 r-fs-12 r-flex" style={ { width: 200 } }>
									<WaresInfo imgUrl={ row.labelPushPlatformSkuPicUrl } />
									{row.labelPushPlatformItemTitle}
								</div>
							) : null}
							<BindYPListModal
								type="radio"
								showSave
								row={ row }
								beforeOk={ handleBindGoods }
								onOk={ handleBindGoodsOk }
							><Button className="r-fs-12" type="link" >{row.bindLabelPushPlatform ? "更换" : "绑定商品"}</Button>
							</BindYPListModal>
							{
								row.bindLabelPushPlatform ? (
									<>
										<Popconfirm
											title={ (
												<>
													<div>您正在解绑四季星座网商品关联关系</div>
													<div className="r-c-error r-mt-4">解绑后您将无法推送商品标签至四季星座网</div>
												</>
											) }
											onConfirm={ () => { handleUnBindGoods(row); } }
											okText="解绑"
											cancelText="取消"
										>
											<Button type="link" className="r-fs-12">解绑</Button>
										</Popconfirm>
									</>
								) : null
							}
						</div>

					</Form.Item>
				);
			}
		});

		const tmpMarketCol = (key: formIdType) => ({
			title: <>市场<EditIcon type="market" /></>,
			width: 150,
			dataIndex: 'market',
			render: (text, row: TableListData[number]) => {
				const name = nameFactory(row[key], 'market');
				return (
					<Tooltip title={ dataFormSource?.[name] } trigger="hover">
						<Form.Item
							shouldUpdate
							name={ name }
							className={ cs('r-mb-0', 'r-mt-0') }
						>
							<Input
								maxLength={ 64 }
								style={ { width: '100%' } }
							/>
						</Form.Item>
					</Tooltip>
				);
			}
		});

		const tmpStallCol = (key: formIdType) => ({
			title: <>档口<EditIcon type="stall" /></>,
			width: 150,
			dataIndex: 'stall',
			render: (text, row: TableListData[number]) => {
				const name = nameFactory(row[key], 'stall');
				return (
					<Tooltip title={ dataFormSource?.[name] } trigger="hover">
						<Form.Item
							shouldUpdate
							name={ name }
							className={ cs('r-mb-0', 'r-mt-0') }
						>
							<Input
								maxLength={ 64 }
								style={ { width: '100%' } }
							/>
						</Form.Item>
					</Tooltip>
				);
			}
		});

		const tmpSupplierNameCol = (key: formIdType) => ({
			title: <>供应商<EditIcon type="supplierName" /></>,
			width: 150,
			dataIndex: 'supplierName',
			render: (text, row: TableListData[number]) => {
				const name = nameFactory(row[key], 'supplierName');
				return (
					<Tooltip title={ dataFormSource?.[name] } trigger="hover">
						<Form.Item
							shouldUpdate
							name={ name }
							className={ cs('r-mb-0', 'r-mt-0') }
						>
							<Input
								maxLength={ 64 }
								style={ { width: '100%' } }
							/>
						</Form.Item>
					</Tooltip>
				);
			}
		});

		const tmpItemCustomAttrCol = (key: formIdType, attrObj) => ({
			title: (
				<div style={ { width: "calc(100% - 16px)", overflow: "hidden", position: "relative", textOverflow: "ellipsis", paddingRight: 16 } }>
					{attrObj.name}
					<span style={ { position: "absolute", right: 0, top: 0 } }>
						<EditIcon type={ batchEditFuncName.批量设置自定义属性 } attrType={ { attrName: attrObj.name, attrKey: attrObj.key } } />
					</span>

				</div>
			),
			width: 150,
			id: 'customAttr',
			dataIndex: attrObj.key,
			render: (text, row: TableListData[number]) => {
				const name = nameFactory(row[key], attrObj.key);
				return (
					<Tooltip title={ dataFormSource?.[name] } trigger="hover">
						<Form.Item
							shouldUpdate
							name={ name }
							className={ cs('r-mb-0', 'r-mt-0') }
						>
							<Input
								maxLength={ 64 }
								style={ { width: '100%' } }
							/>
						</Form.Item>
					</Tooltip>
				);
			}
		});

		const tmpOperateCol = {
			title: '操作',
			fixed: 'right',
			width: 100,
			align: "center",
			render: (text, row: TableListData[number]) => {
				return loadingMap[`${row.rowId}Loading`] ? <div className={ s.deletePlatGoods }>保存中...</div>
					: (
						<div className="r-flex r-jc-c">
							<div
								data-point={ Pointer.商品_平台商品管理_保存_点击 }
								className={ s.deletePlatGoods }
								onClick={ () => batchSaveContent([row]) }
							>
								保存
								{loadingMap[`${row.rowId}Result`] == 'success' && <span style={ { color: '#52C41A' } }>(成功)</span>}
								{loadingMap[`${row.rowId}Result`] == 'fail' && <span style={ { color: '#FF4D4F' } }>(失败)</span>}
							</div>
							<div className={ cs([s.log, 'r-ml-12']) } onClick={ () => { showLog(row); } }>日志</div>
						</div>
					);
			}
		};

		const content = (
			<div className="r-pd-2" style={ { display: 'flex' } }>
				<div className="r-mb-2">预览图展示：</div>
				<Radio.Group
					value={ skuPicPreviewConfig }
					onChange={ (e) => onSkuPicPreviewConfigChange(e.target.value) }
				>
					<div style={ { display: 'flex', flexDirection: 'row' } }>
						{settingArr.map(item => (
							<Radio key={ item.value } value={ item.value } style={ { marginRight: '8px' } }>{item.label}</Radio>
						))}
					</div>
				</Radio.Group>
			</div>
		);

		const skuPicItem = {
			title: (
				<div className="r-flex r-ai-c">
					规格图
					<Popover placement="bottom" title={ null } content={ content } trigger="click">
						<Icon className="r-ml-5 r-c-gray" type="guigeshezhi" size={ 16 } />
					</Popover>
				</div>
			),
			dataIndex: 'picUrl',
			width: 70,
			render: (text, row: TableListData[number]) => {
				return (
					<div className="r-flex">
						<WaresInfo imgUrl={ row.picUrl } previewPicSize={ Number(skuPicPreviewConfig) } />
					</div>
				);
			}
		};

		const dropShippingGoodsItem = {
			title: '设为代发商品',
			dataIndex: 'dropShippingGoods',
			width: 120,
			render: (text, row: TableListData[number]) => {
				let isDropShippingFlag = row?.platformItemSkuList.some(item => item.dropShippingFlag);
				return (
					<div className="r-flex">
						{isDropShippingFlag ? (
							<div className="r-flex r-ai-c r-pointer" onClick={ showDropShippingSku }>
								<span className="r-c-warning r-mr-4 r-fs-14">有代发</span>
								<span className="r-as-c r-c-gray">(显示规格查看)</span>
							</div>
						) : '无代发' }
					</div>
				);
			}
		};

		// 列配置逻辑
		if (isShowZeroStockVersion && pageColumnConfig?.userConfig?.length > 0) {
			// 先定义所有列，然后根据配置返回
			const EditIcon = ({ type, attrType = {} }) => (
				<FormOutlined
					onClick={ () => {
						sendTypePoint(type);
						if (selectedRows.length == 0) return message.warn('需先勾选商品');
						setBatchEditModalInfo({ visible: true, type, ...attrType });
					} }
					className={ s.titleIcon }
				/>
			);

			const inputWeightProps = isKg ? {
				formatter: (value: any) => (value === '' ? value : +(value / 1000).toFixed(3)),
				parser: (value: any) => (value === '' ? value : accMul(value, 1000)),
			} : {
				precision: 0
			};

			const skuPicContent = (
				<div className="r-pd-2" style={ { display: 'flex' } }>
					<div className="r-mb-2">预览图展示：</div>
					<Radio.Group
						value={ skuPicPreviewConfig }
						onChange={ (e) => onSkuPicPreviewConfigChange(e.target.value) }
					>
						<div style={ { display: 'flex', flexDirection: 'row' } }>
							{settingArr.map(item => (
								<Radio key={ item.value } value={ item.value } style={ { marginRight: '8px' } }>{item.label}</Radio>
							))}
						</div>
					</Radio.Group>
				</div>
			);

			const skuPicColumn = {
				title: (
					<div className="r-flex r-ai-c">
						规格图
						<Popover placement="bottom" title={ null } content={ skuPicContent } trigger="click">
							<Icon className="r-ml-5 r-c-gray" type="guigeshezhi" size={ 16 } />
						</Popover>
					</div>
				),
				dataIndex: 'picUrl',
				width: 70,
				render: (text, row: TableListData[number]) => {
					return (
						<div className="r-flex">
							<WaresInfo imgUrl={ row.picUrl } previewPicSize={ Number(skuPicPreviewConfig) } />
						</div>
					);
				},
				sortSet: { collection: 1 }
			};

			// 根据是否显示规格来构建不同的列映射
			if (isShowSku) {
				// 显示规格场景下的列映射
				const skuColumnsMap = {
					checkCol,
					indexCol: {
						title: "",
						align: "center",
						width: 40,
						render: (text, row: TableListData[number]) => {
							if (!row.colSpan) return null;
							return <>{row.groupIndex}</>;
						},
					},
					platformSellerNick: FormatSkuColFn(tmpPlatCol),
					title: FormatSkuColFn(tmpGoodsCol),
					outerId: FormatSkuColFn(tmpOuterIdCol),
					sysItemAlias: FormatSkuColFn(tmpSysItemAliasCol),
					checkColSku: {
						id: "checkColSku",
						title: "",
						width: 40,
						className: "table-left-border",
						render: (text, row: TableListData[number]) => {
							return (
								<div className="r-flex r-ai-c">
									<Checkbox
										className="r-mr-10"
										disabled={ row.checkDisabled }
										onChange={ (e) => onCheckedItem(e, row) }
										checked={ row.isChecked }
									/>
								</div>
							);
						},
					},
					picUrl: skuPicColumn,
					skuName: {
						title: "规格名称",
						sortSet: { collection: 1 },
						width: 200,
						dataIndex: "skuName",
						render: (text, row: TableListData[number]) => {
							let val = text || "无规格";
							if (row.platformItemSkuList.length == 1 && row.skuId == "0") val = "无规格";
							return (
								<div className="r-flex r-ai-c">
									<div className={ s.lineMax1 } title={ val }>
										{row.enableStatus === 0 ? (
											<span className="r-c-error">
												(平台已删){" "}
											</span>
										) : (
											""
										)}
										{val}
									</div>
								</div>
							);
						},
					},
					skuOuterId: {
						title: "规格编码",
						sortSet: { collection: 1 },
						width: 150,
						dataIndex: "skuOuterId",
						render: (text, row: TableListData[number]) => {
							const { skuOuterId } = row;
							return (
								<div className={ s.lineMax1 } title={ skuOuterId }>
									{skuOuterId}
								</div>
							);
						},
					},
					sysSkuAlias: {
						title: (
							<>
								规格别名
								<EditIcon type="sysSkuAlias" />
							</>
						),
						sortSet: { collection: 1, name: "规格别名" },
						width: 150,
						dataIndex: "sysSkuAlias",
						render: (text, row: TableListData[number]) => {
							const name = nameFactory(row.rowId, "sysSkuAlias");
							return (
								<Tooltip title={ dataFormSource?.[name] } trigger="hover">
									<Form.Item
										shouldUpdate
										name={ name }
										className={ cs("r-mb-0", "r-mt-0") }
									>
										<Input
											maxLength={ 60 }
											style={ { width: "100%" } }
										/>
									</Form.Item>
								</Tooltip>
							);
						},
					},
					weight: { ...tmpWeightCol("rowId"), sortSet: { collection: 1, name: "重量" } },
					costPrice: { ...tmpCostPriceCol("rowId"), sortSet: { collection: 1, name: "成本价" } },
					tagPrice: { ...tmpTagPriceCol("rowId"), sortSet: { collection: 1, name: "吊牌价" } },
					classifyId: { ...tmpSkuClassifyIdCol("rowId"), sortSet: { collection: 1, name: "商品分类" } },
					market: { ...tmpMarketCol("rowId"), sortSet: { collection: 1, name: "市场" } },
					stall: { ...tmpStallCol("rowId"), sortSet: { collection: 1, name: "档口" } },
					supplierName: { ...tmpSupplierNameCol("rowId"), sortSet: { collection: 1, name: "供应商" } },
					bindYp: { ...tmpBindYpGoodsCol("rowId"), sortSet: { collection: 1, name: "绑定四季星座网商品" } },
					dropShippingFlag: isDistributorAccount || isSupplierUserAndZeroStock || isSupplierUserAndStock
						? {
							title: '设为代发商品',
							dataIndex: 'dropShippingGoods',
							width: 120,
							render: (text, row: TableListData[number]) => {
								return (
									<div className="k-c-primary r-pointer" onClick={ () => onBindSupplier(row) }>
										<span className="r-pointer" >绑定</span>
										<span>&nbsp;/&nbsp;</span>
										<span className="r-pointer">换绑供应商</span>
									</div>
								);
							},
							sortSet: { collection: 1 }
						}
						: null,
					dropShippingSupplierUserId: isDistributorAccount || isSupplierUserAndZeroStock || isSupplierUserAndStock
						? {
							title: '代发供应商',
							dataIndex: 'supplierInfo',
							width: 120,
							render: (text, row: TableListData[number]) => {
								const { supplierList = [] } = distributionStore;
								const { dropShippingFlag, dropShippingSupplierUserId } = row;
								if (dropShippingFlag) {
									const supplierInfo = supplierList.find(i => i.supplierUserId == dropShippingSupplierUserId) || {};
									return (
										<div>{supplierInfo.supplierName}（{supplierInfo.supplierMobile}）</div>
									);
								}
								return '';
							},
							sortSet: { collection: 1 }
						}
						: null,
					operate: tmpOperateCol,
				};

				// 添加自定义属性列
				const itemCustomAttributeList = UserStore?.systemSetting?.itemCustomAttributeDTOList?.filter((item) => (item?.name));
				if (UserStore?.systemSetting?.itemCustomAttribute === autoSearch.开启 && itemCustomAttributeList?.length) {
					itemCustomAttributeList.forEach((item) => {
						skuColumnsMap[item.key] = {
							...tmpItemCustomAttrCol('rowId', item),
							sortSet: { collection: 1, name: item.name }
						};
					});
				}

				let returnColumns = pageColumnConfig.userConfig
					.filter(col => (col.ischecked || (["market", "stall", "supplierName"].includes(col.key) && isShowSupplier)) && skuColumnsMap[col.key])
					.map(col => ({
						...skuColumnsMap[col.key],
						key: col.key,
					}));
				returnColumns.unshift(checkCol, {
					title: "",
					align: "center",
					width: 40,
					render: (text, row: TableListData[number]) => {
						if (!row.colSpan) return null;
						return <>{row.groupIndex}</>;
					},
				});
				returnColumns.splice(pageColumnConfig.userConfig.filter(i => i.collection == 0 && i.ischecked).length + 2, 0, {
					id: "checkColSku",
					title: "",
					width: 40,
					className: "table-left-border",
					render: (text, row: TableListData[number]) => {
						return (
							<div className="r-flex r-ai-c">
								<Checkbox
									className="r-mr-10"
									disabled={ row.checkDisabled }
									onChange={ (e) => onCheckedItem(e, row) }
									checked={ row.isChecked }
								/>
							</div>
						);
					},
				});
				returnColumns.push({ ...tmpBindYpGoodsCol("rowId"), sortSet: { collection: 1, name: "绑定四季星座网商品" } }, tmpOperateCol);

				// 应用其他过滤条件
				const { platformAuthInfo } = scanPrintStore;
				if (!isShowSupplier) returnColumns = returnColumns.filter(i => !['market', 'stall', 'supplierName'].includes(i.dataIndex));
				if (!platformAuthInfo?.[labelPushPlatformEnum.四季星座网]?.platformNick) returnColumns = returnColumns.filter(i => !['bindYp'].includes(i.dataIndex));
				if (isDistributorAccount || isSupplierUserAndStock) returnColumns = returnColumns.filter(i => !['weight', 'costPrice', 'tagPrice'].includes(i.dataIndex));
				if (isSupplierUserAndStock) {
					returnColumns = returnColumns.filter(i => !['sysItemAlias', 'sysSkuAlias', 'classifyId'].includes(i.dataIndex)); // 常规库存版不显示商品分类
					returnColumns = returnColumns.filter(i => i?.id !== 'customAttr'); // 常规库存版不显示自定义属性
				}
				// 添加单元格属性处理
				let collab = isShowSku
					? returnColumns.findIndex(i => i.id === "checkColSku")
					: 4;
				console.log(collab, "collab");
				returnColumns.forEach((col, idx) => {
					col['getCellProps'] = (_, r) => {
						const isSkuListLong = r?.platformItemSkuList?.length >= 2;
						const isLastRow = isSkuListLong && r?.platformItemSkuList[r?.platformItemSkuList?.length - 1]?.rowId === r?.rowId;
						if (idx < collab && isSkuListLong && isShowSku) {
							return {
								style: isLastRow ? {} : { borderBottom: 'none' }
							};
						}
					};
				});
				return returnColumns;
			} else {
				// 商品维度场景下的列映射
				const columnsMap = {
					checkCol,
					indexCol: {
						title: '',
						align: 'center',
						width: 40,
						render: (text, row: TableListData[number], index) => <>{index + 1}</>,
					},
					platformSellerNick: tmpPlatCol,
					title: tmpGoodsCol,
					outerId: tmpOuterIdCol,
					sysItemAlias: tmpSysItemAliasCol,
					weight: tmpWeightCol('numIid'),
					costPrice: tmpCostPriceCol('numIid'),
					market: tmpMarketCol('numIid'),
					stall: tmpStallCol('numIid'),
					supplierName: tmpSupplierNameCol('numIid'),
					dropShippingFlag: dropShippingGoodsItem,
					operate: tmpOperateCol,
				};

				let returnColumns = pageColumnConfig.userConfig
					.filter(col => (col.ischecked || (["market", "stall", "supplierName"].includes(col.key) && isShowSupplier)) && columnsMap[col.key])
					.map(col => ({
						...columnsMap[col.key],
						key: col.key,
					}));
				returnColumns.unshift(checkCol, {
					title: '',
					align: 'center',
					width: 40,
					render: (text, row: TableListData[number], index) => <>{index + 1}</>,
				});
				returnColumns.push(tmpOperateCol);
				// 应用其他过滤条件
				const { platformAuthInfo } = scanPrintStore;
				if (!isShowSupplier) returnColumns = returnColumns.filter(i => !['market', 'stall', 'supplierName'].includes(i.dataIndex));
				if (!platformAuthInfo?.[labelPushPlatformEnum.四季星座网]?.platformNick) returnColumns = returnColumns.filter(i => !['bindYp'].includes(i.dataIndex));
				if (isDistributorAccount || isSupplierUserAndStock) returnColumns = returnColumns.filter(i => !['weight', 'costPrice', 'tagPrice'].includes(i.dataIndex));
				if (isSupplierUserAndStock) {
					returnColumns = returnColumns.filter(i => !['sysItemAlias', 'sysSkuAlias', 'classifyId'].includes(i.dataIndex)); // 常规库存版不显示商品分类
					returnColumns = returnColumns.filter(i => i?.id !== 'customAttr'); // 常规库存版不显示自定义属性
				}

				// 添加单元格属性处理
				let collab = isShowSku
					? returnColumns.findIndex(i => i.id === "checkColSku")
					: 4;
				console.log(collab, "collab");
				returnColumns.forEach((col, idx) => {
					col['getCellProps'] = (_, r) => {
						const isSkuListLong = r?.platformItemSkuList?.length >= 2;
						const isLastRow = isSkuListLong && r?.platformItemSkuList[r?.platformItemSkuList?.length - 1]?.rowId === r?.rowId;
						if (idx < collab && isSkuListLong && isShowSku) {
							return {
								style: isLastRow ? {} : { borderBottom: 'none' }
							};
						}
					};
				});
				console.log('returnColumns', returnColumns);
				return returnColumns;
			}
		}

		// 表格列定义，与AntdTable使用一致
		const columnsSku = [
			checkCol,
			{
				title: "",
				align: "center",
				width: 40,
				render: (text, row: TableListData[number]) => {
					if (!row.colSpan) return null;
					return <>{row.groupIndex}</>;
				},
			},
			FormatSkuColFn(tmpPlatCol),
			FormatSkuColFn(tmpGoodsCol),
			FormatSkuColFn(tmpOuterIdCol),
			FormatSkuColFn(tmpSysItemAliasCol),
			{
				id: "checkColSku",
				title: "",
				width: 40,
				className: "table-left-border",
				render: (text, row: TableListData[number]) => {
					return (
						<div className="r-flex r-ai-c">
							<Checkbox
								className="r-mr-10"
								disabled={ row.checkDisabled }
								onChange={ (e) => onCheckedItem(e, row) }
								checked={ row.isChecked }
							/>
						</div>
					);
				},
			},
			{
				title: "规格名称",
				sortSet: { collection: 1 },
				width: 200,
				dataIndex: "skuName",
				render: (text, row: TableListData[number]) => {
					let val = text || "无规格";
					if (row.platformItemSkuList.length == 1 && row.skuId == "0") val = "无规格";
					return (
						<div className="r-flex r-ai-c">
							<div className={ s.lineMax1 } title={ val }>
								{row.enableStatus === 0 ? (
									<span className="r-c-error">
										(平台已删){" "}
									</span>
								) : (
									""
								)}
								{val}
							</div>
						</div>
					);
				},
			},

			{
				title: "规格编码",
				sortSet: { collection: 1 },
				width: 150,
				dataIndex: "skuOuterId",
				render: (text, row: TableListData[number]) => {
					const { skuOuterId } = row;
					return (
						<div className={ s.lineMax1 } title={ skuOuterId }>
							{skuOuterId}
						</div>
					);
				},
			},
			{
				title: (
					<>
						规格别名
						<EditIcon type="sysSkuAlias" />
					</>
				),
				sortSet: { collection: 1, name: "规格别名" },
				width: 150,
				dataIndex: "sysSkuAlias",
				render: (text, row: TableListData[number]) => {
					const name = nameFactory(row.rowId, "sysSkuAlias");
					return (
						<Tooltip title={ dataFormSource?.[name] } trigger="hover">
							<Form.Item
								shouldUpdate
								name={ name }
								className={ cs("r-mb-0", "r-mt-0") }
							>
								<Input
									maxLength={ 60 }
									style={ { width: "100%" } }
								/>
							</Form.Item>
						</Tooltip>
					);
				},
			},
			{
				...tmpWeightCol("rowId"),
				sortSet: { collection: 1, name: "重量" },
			},
			{
				...tmpCostPriceCol("rowId"),
				sortSet: { collection: 1, name: "成本价" },
			},
			{
				...tmpTagPriceCol("rowId"),
				sortSet: { collection: 1, name: "吊牌价" },
			},
			{
				...tmpSkuClassifyIdCol("rowId"),
				sortSet: { collection: 1, name: "商品分类" },
			},
			{
				...tmpMarketCol("rowId"),
				sortSet: { collection: 1, name: "市场" },
			},
			{
				...tmpStallCol("rowId"),
				sortSet: { collection: 1, name: "档口" },
			},
			{
				...tmpSupplierNameCol("rowId"),
				sortSet: { collection: 1, name: "供应商" },
			},
			{
				...tmpBindYpGoodsCol("rowId"),
				sortSet: { collection: 1, name: "绑定四季星座网商品" },
			},
			isDistributorAccount
			|| isSupplierUserAndZeroStock
			|| isSupplierUserAndStock
				? {
					title: '设为代发商品',
					dataIndex: 'dropShippingGoods',
					width: 120,
					render: (text, row: TableListData[number]) => {
						return (
							<div className="k-c-primary r-pointer" onClick={ () => onBindSupplier(row) }>
								<span className="r-pointer" >绑定</span>
								<span>&nbsp;/&nbsp;</span>
								<span className="r-pointer">换绑供应商</span>
							</div>
						);
					},
					sortSet: { collection: 1 }
				}
				: null, // 设为代发商品
			isDistributorAccount
			|| isSupplierUserAndZeroStock
			|| isSupplierUserAndStock
				? {
					title: '代发供应商',
					dataIndex: 'supplierInfo',
					width: 120,
					render: (text, row: TableListData[number]) => {
						const { supplierList = [] } = distributionStore;
						const { dropShippingFlag, dropShippingSupplierUserId } = row;
						if (dropShippingFlag) {
							const supplierInfo = supplierList.find(i => i.supplierUserId == dropShippingSupplierUserId) || {};
							return (
								<div>{supplierInfo.supplierName}（{supplierInfo.supplierMobile}）</div>
							);
						}
						return '';
					},
					sortSet: { collection: 1 }
				}
				: null, // 代发供应商
			tmpOperateCol
		];

		const itemCustomAttributeList = UserStore?.systemSetting?.itemCustomAttributeDTOList?.filter((item) => (item?.name));
		if (UserStore?.systemSetting?.itemCustomAttribute === autoSearch.开启 && itemCustomAttributeList?.length) {
			const _list = itemCustomAttributeList.map((item) => {
				return {
					...tmpItemCustomAttrCol('rowId', item), // 自定义属性
					sortSet: { collection: 1, name: item.name }
				};
			});
			let index = columnsSku.findIndex(item => item && (item as any)?.dataIndex == 'sysSkuAlias'); // 改为获取index
			columnsSku.splice(index + 1, 0, ..._list);
		}

		const columns = [
			checkCol,
			{
				title: '',
				align: 'center',
				width: 40,
				render: (text, row: TableListData[number], index) => {
					return <>{index + 1}</>;
				}
			},
			tmpPlatCol,
			tmpGoodsCol,
			tmpOuterIdCol,
			tmpSysItemAliasCol,
			{ ...tmpWeightCol('numIid'), sortSet: { name: '重量' } },
			{ ...tmpCostPriceCol('numIid'), sortSet: { name: '成本价' } },
			{ ...tmpMarketCol('numIid'), sortSet: { name: '市场' } },
			{ ...tmpStallCol('numIid'), sortSet: { name: '档口' } },
			{ ...tmpSupplierNameCol('numIid'), sortSet: { name: '供应商' } },
			(isDistributorAccount || isSupplierUserAndZeroStock || isSupplierUserAndStock) ? dropShippingGoodsItem : null, // 设为代发商品
			tmpOperateCol
		];


		let returnColumns = [];
		const {
			platformAuthInfo
		} = scanPrintStore;
		if (isShowSku) {
			returnColumns = [...columnsSku].filter(i => i);
			let index = columnsSku.findIndex(item => item && (item as any)?.id == 'checkColSku'); // 改为获取index
			if (isShowSkuPic) returnColumns.splice(index + 1, 0, { ...skuPicItem, sortSet: { collection: 1 } });
		} else {
			returnColumns = columns.filter(i => i);
		}
		if (!isShowSupplier) returnColumns = returnColumns.filter(i => !['market', 'stall', 'supplierName'].includes(i.dataIndex));
		if (!platformAuthInfo?.[labelPushPlatformEnum.四季星座网]?.platformNick) returnColumns = returnColumns.filter(i => !['bindYp'].includes(i.dataIndex));
		if (isDistributorAccount || isSupplierUserAndStock) returnColumns = returnColumns.filter(i => !['weight', 'costPrice', 'tagPrice'].includes(i.dataIndex));
		if (isSupplierUserAndStock) {
			returnColumns = returnColumns.filter(i => !['sysItemAlias', 'sysSkuAlias', 'classifyId'].includes(i.dataIndex)); // 常规库存版不显示商品分类
			returnColumns = returnColumns.filter(i => i?.id !== 'customAttr'); // 常规库存版不显示自定义属性
		}

		let collab = isShowSku
			? returnColumns.findIndex(i => i.id === "checkColSku")
			: 4;

		returnColumns.forEach((col, idx) => {
			col['getCellProps'] = (_, r) => {
				const isSkuListLong = r?.platformItemSkuList?.length >= 2;
				const isLastRow = isSkuListLong && r?.platformItemSkuList[r?.platformItemSkuList?.length - 1]?.rowId === r?.rowId;
				if (idx < collab && isSkuListLong && isShowSku) {
					return {
						style: isLastRow ? {} : { borderBottom: 'none' }
					};
				}
			};
		});
		return returnColumns as ColumnsType;
	// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [skuPicGoodsConfig, skuPicPreviewConfig, dataFormSource, isKg, groupList, isShowSku, isShowSkuPic, isShowSupplier, loadingMap, selectedRows, isShowZeroStockVersion, pageColumnConfig]);

	// 接口查询、查询参数重装
	const fetchSystemList = async(info: ItemSysItemListOfItemRelationPlatformItemViewRequest) => {
		if (isDistributorAccount) {
			sendPoint(Pointer.分销商_平台商品管理_查询_点击);
		}
		for (let key in info) {
			if (typeof info[key] === 'string') {
				info[key] = info[key].trim();
			}
		}
		const { approveStatus,
			pageNo, pageSize,
			platformInfo,
			outerId, sysItemAlias,
			numIids, title, skuNameList,
			skuContent, existItemAlias,
			existWeight, existCostPrice,
			existMarket, existSupplier, existStall,
			stall, market, supplierName,
			skuOuterId, existSkuAlias, selectDropShippingItemFlag,
			bindLabelPushPlatform, classifyId, existClassify,
			itemSkuCreateTime	 } = info;
		const { shopId, platform } = await getPlatAndShops(platformInfo);

		let itemSkuCreateStartTime;
		let itemSkuCreateEndTime;
		// 处理日期范围参数
		if (itemSkuCreateTime && itemSkuCreateTime.length) {
			itemSkuCreateStartTime = itemSkuCreateTime[0]?.format('YYYY-MM-DD HH:mm:ss');
			itemSkuCreateEndTime = itemSkuCreateTime[1]?.format('YYYY-MM-DD HH:mm:ss');
		  }
		const _search: ItemSysItemListOfItemRelationPlatformItemViewRequest = {
			platformList: platform,
			sellerIdList: shopId,
			itemSkuCreateStartTime,
			itemSkuCreateEndTime,
			pageNo,
			pageSize,
			itemType: 1,
			approveStatus,
			title,
			numIids: numIids?.length ? (numIids as any)?.split(',') : '',
			skuNameList: skuNameList?.trim() ? skuNameList?.split(" ") : '', // 规格名称
			sysItemAlias,
			outerId,
			skuOuterId,
			skuContent,
			existItemAlias,
			existWeight,
			existCostPrice,
			existMarket,
			existStall,
			existSupplier,
			stall,
			market,
			supplierName,
			existSkuAlias,
			selectDropShippingItemFlag,
			bindLabelPushPlatform,
			classifyId,
			existClassify,
			tradeWaitSendFlag: getTradeWaitSendFlag(),
			tradeFindType,
		};
		if (isDistributorAccount) {
			_search.userViewVal = UserViewValEnum.分销商视角;
		}
		console.log('numIids', numIids);

		const search = clearParams(_search, true);
		setLoadingMap({});
		setCurSearchParams(search);

		return fetchWithPaginationOptimization(search, info.pageSize).then(res => {
			setOriginData(res);
			return res;
		});
	};

	// 基于平台商品拆分维度
	const responseAdapter = (data: ItemSysItemListOfItemRelationPlatformItemViewResponse["data"]) => {
		if (isShowSku) return responseAdapterSku(data);
		else return responseAdapterOut(data);
	};

	const responseAdapterOut = (data: ItemSysItemListOfItemRelationPlatformItemViewResponse["data"]) => {
		const formData = {};
		const tableList: any = [];

		data?.list?.forEach((item, i: any) => {
			let obj = {
				groupId: i,
				rowId: item.numIid,
				colSpan: 1,
				...item,
				...item.platformItemSkuList[0],
				...item.extraAttrDTO
			};
			// const keyMappings = {
			// 	supplierName: 'itemSupplierName',
			// 	stall: 'itemStall',
			// 	market: 'itemMarket',
			// 	costPrice: 'itemCostPrice',
			// 	weight: 'itemWeight'
			// };
			// ['supplierName', 'stall', 'market', 'costPrice', 'tagPrice', 'classifyId', 'weight', 'sysSkuAlias', 'sysItemAlias'].forEach(key => {
			// 	const name = nameFactory(obj.numIid, key);

			// 	if (key === 'sysItemAlias') {
			// 		formData[name] = obj.platformItemSkuList[0]?.relationSystemItemList[0]?.sysItemAlias || '';
			// 	} else if (['tagPrice', 'classifyId', 'sysSkuAlias'].includes(key)) {
			// 		const firstVal = obj.platformItemSkuList[0]?.relationSystemItemList[0]?.[key];
			// 		formData[name] = obj.platformItemSkuList.every(i => i.relationSystemItemList[0]?.[key] === firstVal) ? firstVal : '';
			// 	} else {
			// 		formData[name] = obj[keyMappings[key]];
			// 	}
			// });
			['supplierName', 'stall', 'market', 'costPrice', 'tagPrice', 'classifyId', 'weight', 'sysSkuAlias', 'sysItemAlias'].forEach(key => {
				const name = nameFactory(obj.numIid, key);
				if (key === 'sysItemAlias') {
					formData[name] = obj.platformItemSkuList[0]?.relationSystemItemList[0]?.sysItemAlias || '';
				} else {
					const firstVal = obj.platformItemSkuList[0]?.relationSystemItemList[0]?.[key];
					formData[name] = obj.platformItemSkuList.every(i => i.relationSystemItemList[0]?.[key] == firstVal) ? firstVal : '';
				}
			});

			tableList.push(obj);
		});
		console.log('tableList,outer--------》', tableList);
		setDataSource(tableList);
		setDataFormSource(formData);
		setTimeout(() => {
			form.setFieldsValue(formData);
		}, 0);
		return {
			list: tableList,
			total: data.total
		};
	};


	const responseAdapterSku = (data: ItemSysItemListOfItemRelationPlatformItemViewResponse["data"]) => {
		const formData = {};
		const tableList: any = [];
		data.list.forEach((item, i: any) => {
			let itemLen = item.platformItemSkuList.length;
			item.platformItemSkuList.forEach((platformItem: any, index: number) => {
				platformItem.rowId = `${i}_${index}_${itemLen}`;
				const customAttributesList = platformItem.relationSystemItemList[0]?.customAttributesList || [];
				const customAttributes = {};
				if (UserStore?.systemSetting?.itemCustomAttribute === autoSearch.开启) {
					customAttributesList?.forEach(item => {
						customAttributes[item.key] = item.value || "";
					});
				}
				const obj = {
					groupId: i,
					groupIndex: i + 1,
					...(index === 0 ? { colSpan: itemLen } : {}),
					...item,
					...platformItem,
					relationSystemItem: platformItem.relationSystemItemList[0] || {},
					...item.extraAttrDTO
				};

				if (index == 0) {
					let name = nameFactory(obj.numIid, 'sysItemAlias');
					formData[name] = obj.relationSystemItem['sysItemAlias'] || '';
				}
				['supplierName', 'stall', 'market', 'costPrice', 'tagPrice', 'classifyId', 'weight', 'sysSkuAlias'].forEach(key => {
					let name = nameFactory(obj.rowId, key);
					formData[name] = obj.relationSystemItem[key] || '';
				});
				Object.keys(customAttributes)?.forEach(key => {
					let name = nameFactory(obj.rowId, key);
					formData[name] = customAttributes[key] || '';
				});
				// 展开规格的点击判断
				tableList.push(obj);
			});
		});
		console.log('tableList, sku', tableList);
		setDataSource(tableList);
		setDataFormSource(formData);
		// setLoadingMap({});
		setTimeout(() => {
			form.setFieldsValue(formData);
		}, 0);
		return {
			list: tableList,
			total: data.total
		};
	};

	const onFieldsChange = (changedValues, allValues) => {
		console.log('%c [ changedValues ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', changedValues);
		setDataFormSource(pre => ({ ...pre, ...allValues }));
	};

	const additionalFormNode = (
		<Space>
			<Checkbox data-point={ Pointer.商品_平台商品管理_显示规格_点击 } checked={ isShowSku } onChange={ () => onCheckShowSku() } className="r-ml-30 r-wb-ka">显示规格</Checkbox>
			{/* <Checkbox disabled={ !isShowSku } checked={ isShowSkuPic } onChange={ () => onCheckShowSkuPic() } className="r-ml-12 r-wb-ka">显示规格图片</Checkbox> */}
			{(!isDistributorAccount && !isSupplierUserAndStock) ? (
				<Checkbox data-point={ Pointer.商品_平台商品管理_显示档口供应商_点击 } checked={ isShowSupplier } onChange={ () => onCheckShowSupplier() } className="r-ml-12 r-wb-ka">
					<span style={ { whiteSpace: 'nowrap' } }>显示档口/供应商</span>
				</Checkbox>
			) : ''}
			<Checkbox checked={ isHideDelete } onChange={ () => onCheckShowDelete() } className="r-ml-12 r-wb-ka">
				<span style={ { whiteSpace: 'nowrap' } }>
					隐藏已删除规格
				</span>
			</Checkbox>
			{(!isDistributorAccount && !isFreeSupplierAccount) ? (
				<Checkbox checked={ getTradeWaitSendFlag() } onChange={ (e) => onChangeTradeWaitSendFlag(e) } style={ { position: "relative" } }>
					<Tooltip title="即根据最近24小时内的待发货订单进行查询，可能存在些许误差">
						<span style={ { whiteSpace: 'nowrap' } }>有待发货订单</span>
						{/* <span className={ s.menuLinkLimitIcon } >new</span> */}
						{/* <Icon size={ 30 } type="new" style={ { position: "absolute", top: "-12px" } } /> */}
					</Tooltip>
					<Popover
						placement="bottom"
						content={ (
							<Radio.Group
								value={ tradeFindType }
								onChange={ (e) => tradeFindTypeChange(e) }
							>
								<Space direction="vertical">
									<Radio value="1">查询结果精确到规格</Radio>
									<Radio value="2">查询结果精确到商品</Radio>
								</Space>
							</Radio.Group>
						) }
					>
						<SettingOutlined className="r-c-999 r-ml-8" onClick={ (e) => e.preventDefault() } />
					</Popover>
				</Checkbox>
			) : ''}

		</Space>
	);

	const emptyNode = (
		<div>
			<img style={ { width: '147px' } } src="https://img.alicdn.com/imgextra/i3/69942425/O1CN01XalmOh1Tmh4RNp7Gp_!!69942425.png" alt="" />
			<p className="r-mb-40 r-mt-14 r-fs-14" style={ { color: 'rgb(255, 77, 79)' } }>暂无数据</p>
		</div>
	);

	// 当前条件刷新
	const systemRefresh = useCallback(() => {
		tableRef?.current?.submit?.();
	}, []);

	// 货品打印设置
	const handleOpenPrintMainPanel = () => {
		console.log('handleOpenPrintMainPanel');
		PrintCenter.showTemplateMain({ printType: GoodsPrintType.吊牌 });
	};

	const handlePrint = (type) => {
		let selectArr = [];
		const newFormVal = { ...dataFormSource };
		dataSource.forEach((item) => {
			const { outerId = "", title = "", platform = "", sellerNick = "",
				skuOuterId = "", skuName = ""
			} = item;
			const factoryId = isShowSku ? item.rowId : item.numIid;
			const customAttributesList = [];
			Object.keys(EnumItemCustomAttribute).forEach((key) => {
				// customAttrObj = newFormVal[nameFactory(factoryId, key)] || "";
				customAttributesList.push({ key, value: newFormVal[nameFactory(factoryId, key)] || "" });
			});
			// 规格维度选中
			if (item.isChecked && isShowSku) {
				selectArr.push({
					sysItemAlias: newFormVal[nameFactory(item.numIid, 'sysItemAlias')] || "",
					sysSkuAlias: newFormVal[nameFactory(factoryId, 'sysSkuAlias')] || "",
					weight: newFormVal[nameFactory(factoryId, 'weight')] || "",
					costPrice: newFormVal[nameFactory(factoryId, 'costPrice')] || "",
					tagPrice: newFormVal[nameFactory(factoryId, 'tagPrice')] || "",
					classifyId: newFormVal[nameFactory(factoryId, 'classifyId')] || "",
					market: newFormVal[nameFactory(factoryId, 'market')] || "",
					supplierName: newFormVal[nameFactory(factoryId, 'supplierName')] || "",
					stall: newFormVal[nameFactory(factoryId, 'stall')] || "",
					skuName,
					title,
					platform,
					sellerNick,
					skuOuterId,
					outerId,
					customAttributesList,
				});
			}
			// 商品维度选中
			if (item.isChecked && !isShowSku) {
				item.platformItemSkuList.forEach(sku => {
					const { skuName = "", skuId = "", relationSystemItemList = [], skuOuterId = "" } = sku;
					const { sysItemAlias = "", sysSkuAlias = "", } = relationSystemItemList[0] || {};
					selectArr.push({
						sysItemAlias: newFormVal[nameFactory(item.numIid, 'sysItemAlias')] || "",
						sysSkuAlias,
						weight: newFormVal[nameFactory(factoryId, 'weight')] || "",
						costPrice: newFormVal[nameFactory(factoryId, 'costPrice')] || "",
						tagPrice: newFormVal[nameFactory(factoryId, 'tagPrice')] || "",
						classifyId: newFormVal[nameFactory(factoryId, 'classifyId')] || "",
						market: newFormVal[nameFactory(factoryId, 'market')] || "",
						supplierName: newFormVal[nameFactory(factoryId, 'supplierName')] || "",
						stall: newFormVal[nameFactory(factoryId, 'stall')] || "",
						skuName,
						skuId,
						title,
						outerId,
						sellerNick,
						platform,
						skuOuterId,
						customAttributesList,
					});
				});
			}
		});
		if (!selectArr.length) {
			message.warning("请选择要打印的商品或规格");
			return;
		}
		PrintCenter.batchPrintGoodsBarCodeOrTag({
			orderList: selectArr,
			printType: type
		});
		console.log('selectArr::', selectArr);
	};


	const printMenu = (
		<Menu>
			<Menu.Item
				key={ PrintType.吊牌 }
				onClick={ () => { handlePrint(PrintType.吊牌); } }
			>
				打印商品吊牌
			</Menu.Item>
			<Menu.Item
				key={ PrintType.条码 }
				onClick={ () => { handlePrint(PrintType.条码); } }
			>
				打印商品条码
			</Menu.Item>
		</Menu>
	);

	{
		(isDistributorAccount || isSupplierUserAndZeroStock) && (
			<div className="r-mr-8">
				<GroupEditorModal title="商品分类设置">
					<Button size="small">商品分类设置</Button>
				</GroupEditorModal>
			</div>
		);
	}
	const setUpMenu = (
		<Menu>
			<Menu.Item
				key={ PrintType.货品打印设置 }
				onClick={ () => handleOpenPrintMainPanel() }
			>
				{/* 货品打印设置 */}
				<div
					data-point={ Pointer.货品打印设置 }
				>商品打印设置
				</div>
			</Menu.Item>
			{
				(isDistributorAccount || isSupplierUserAndZeroStock) && (
					<Menu.Item
						key={ PrintType.商品分类设置 }
						// onClick={ () => { setAutoBindModalVisible(true); } }
					>
						<GroupEditorModal title="商品分类设置">
							商品分类设置
						</GroupEditorModal>
					</Menu.Item>
				)
			}
			{
				platformAuthInfo?.[labelPushPlatformEnum.四季星座网]?.platformNick && (
					<Menu.Item
						key={ PrintType.四季星座 }
						onClick={ () => { setAutoBindModalVisible(true); } }
					>
						自动绑定四季星座网商品
					</Menu.Item>
				)
			}
		</Menu>
	);

	const autoPushOnChange = (e: boolean) => {
		setAutoPushLoading(true);
		let dropShippingFlag = e ? 1 : 0;
		dropShippingAutoPush ? sendPoint(Pointer["分销商_平台商品管理_代发商品自动推单-开启_点击"]) : sendPoint(Pointer["分销商_平台商品管理_代发商品自动推单-关闭_点击"]);
		SettingSaveSystemSettingApi({
			dropShippingFlag,
		}).then(res => {
			if (res) {
				setDropShippingAutoPush(e);
				UserStore.setSystemSetting({ dropShippingFlag });
			} else {
				setDropShippingAutoPush(!e);
				message.warning('设置失败');
			}
		}).finally(() => {
			setAutoPushLoading(false);
		});
	};

	   // 检查用户是否有可用的目标店铺
	   const checkAvailableShops = async() => {
		try {
			const { userId } = await UserStore.getUserInfo();
			const { list = [] } = await IndexPlatformShopGetPlatformShopsApi({
				userId,
				refresh: 0,
			});
			
			const availableShops = list.filter(shop => itemMigrationplatform.includes(shop.platform) 
                && shop.status === 1);

			if (availableShops.length === 0) {
				message.warning("您没有可用的目标店铺，请先添加店铺");
				return false;
			}

			return true;
		} catch (error) {
			console.error('检查可用店铺失败:', error);
			return false;
		}
	};

	const onClickBanjia = async() => {
		sendPoint(Pointer['商品_平台商品管理_点击一键复制']);
		// 版本拦截：标配版/试用版用户(且没有最大额度)不能使用一键批量铺货功能
		if (userInfo?.level !== VersionEnum.高级版 && userInfo?.phItemNumLimit == 0) {
			try {
				const res = await GetUserActivityInfo({ activityType: 'GIFT_PH_LIMIT' });
				setHasActivityEligibility(res?.hasActivityEligibility);
				if (res?.hasActivityEligibility) {
					setUserActivityInfo(res?.giftActivityConfig);
				}
				if (!res?.userActivityGiftLimitInfo?.giftNum || res?.userActivityGiftLimitInfo?.giftNum == 0) {
					console.log(userInfo?.level, 'userInfo?.leveluserInfo?.leveluserInfo?.level');
					setShowVersionModal(true);
					return; // 这个return现在会正确退出整个函数
				}
			} catch (error) {
				console.error('获取用户活动信息失败:', error);
				return;
			}
		}
		if (!selectedRows.length) {
			message.info("请先选择规格");
			return;
		}

		const filterProducts = selectedRows.filter(row => {
			// 检查平台是否支持
			const isPlatformSupported = choiceItemMigrationplatform.includes(row.platform);

			// 检查规格条件：platformItemSkuList里面规格除了平台已删之外只剩下一个规格且这个规格skuId = '0'
			const validSkus = row.platformItemSkuList?.filter(sku => sku.enableStatus !== 0) || [];
			const hasValidSku = validSkus.length === 1 && validSkus[0].skuId === '0';

			return isPlatformSupported && !hasValidSku && row.approveStatus !== 'delete';
		});

		const selectedSetfilterProducts = _.uniqBy(filterProducts, item => `${item.numIid}_${item.sellerId}`);

		if (filterProducts?.length !== selectedRows.length) {
			Modal.confirm({
				title: '系统提示',
				width: 640,
				icon: <CloseCircleOutlined style={ { color: '#FF4D4F' } } />,
				content: (
					<div>
						<p>所选平台商品中部分数据暂不支持铺货操作，请不要勾选以下数据：</p>
						<p>1、非抖店、拼多多、1688、快手、视频号平台商品</p>
						<p>2、无规格商品</p>
						<p>3、平台已删商品</p>
						<div style={ { color: '#999', position: 'absolute', bottom: '28px', left: "32px", fontSize: '14px' } }>
							过滤异常后勾选（{selectedSetfilterProducts?.length}条）
						</div>
					</div>
				),
				okText: '跳过异常 操作正常数据',
				cancelText: '知道了',
				onOk: () => {
					const validRows = filterProducts;
					if (validRows.length === 0) {
						message.info("没有可操作的数据");
						return;
					}

					// 更新 dataSource，将过滤后的数据设置为选中状态
					setDataSource(prev => {
						return prev.map(item => {
							// 检查当前项是否在过滤后的数据中
							const isInFilteredProducts = validRows.some(validRow => validRow.rowId === item.rowId);
							return {
								...item,
								isChecked: isInFilteredProducts
							};
						});
					});
					// 执行检查并打开弹窗
					checkAvailableShops().then(hasAvailableShops => {
						if (hasAvailableShops) {
							setBatchMoveModalVisible(true);
						}
					});
				}
			});
			return;
		}

		// 执行检查并打开弹窗
		checkAvailableShops().then(hasAvailableShops => {
			if (hasAvailableShops) {
				// 打开批量搬家弹窗
				setBatchMoveModalVisible(true);
			}
		});
	};

	const expandContext = useMemo(() => (
		<div className="r-flex r-ai-c r-jc-sb">
			<div>
				<Button size="middle" data-point={ Pointer.商品_平台商品管理_下载平台商品_点击 } type="primary" className={ cs('r-mr-8') } onClick={ () => { setSyncWaresModalVisible(true); } }>下载平台商品</Button>
				<Button size="middle" data-point={ Pointer.商品_平台商品管理_点击导入 } onClick={ () => { setImportModalVisible(true); } } className={ cs('r-mr-8') } >Excel导入</Button>
				<Button size="middle" onClick={ () => { setDownloadExcelModalVisible(true); } } className={ cs('r-mr-8') } >导出</Button>
				<Dropdown
					overlay={ printMenu }
					placement="bottomLeft"
				>
					<Button size="middle" className="r-mr-8">
						商品打印<DownOutlined />
					</Button>
				</Dropdown>
				{
					isShowZeroStockVersion && (
						<Button
							className="r-mr-8"
							data-point={ Pointer.商品_平台商品管理_删除平台商品规格_点击 }
							type="default"
							size="middle"
							onClick={ () => batchDeleteSku(selectedRows) }
							loading={ loadingMap.all }
						>删除商品/规格
						</Button>
					)
				}
				{(isDistributorAccount
					|| isSupplierUserAndStock
					|| isSupplierUserAndZeroStock) && (
					<Button
						size="middle"
						className={ cs('r-mr-8') }
						loading={ batchSetDropShippingGoodsLoading }
						data-point={ Pointer["分销商_平台商品管理_点击-批量设为代发商品_点击"] }
						onClick={ () => {
							if (!selectedRows.length) {
								message.info("请先选择规格");
								return;
							}
							setBatchSetGoodsVisible(true);
						} }
					>批量绑定供应商
					</Button>
				)}
				<Button
					size="middle"
					type="primary"
					// className={ cs('r-mr-8') }
					// disabled={ selectedRows.length === 0 }
					onClick={ () => {
						onClickBanjia();
					} }
				>
					<div>
						一键批量铺货
						<Tooltip title="源商品目前仅支持抖店、拼多多、1688、快手、视频号平台链接" overlayStyle={ { minWidth: '280px' } }>
							<QuestionCircleOutlined style={ { marginLeft: '4px' } } />
						</Tooltip>
						<span className={ s.newIcon }>new</span>
					</div>
				</Button>
			</div>
			<div style={ { display: 'flex', position: 'absolute', right: 17 } }>
				<div className="r-flex r-ai-c" style={ { marginRight: 10 } }>
					{(isDistributorAccount || isSupplierUserAndStock || isSupplierUserAndZeroStock) && (
						<Button className="r-flex r-ai-c r-ml-12" size="middle">
							<span>代发商品自动推单</span>
							<Switch
								checked={ !!dropShippingAutoPush }
								loading={ autoPushLoading }
								onChange={ autoPushOnChange }
								size="small"
								checkedChildren="开"
								unCheckedChildren="关"
								className="r-ml-16"
							/>
						</Button>
					)}
				</div>
				{
					(isDistributorAccount || isSupplierUserAndZeroStock) ?	(
						<Dropdown
							overlay={ setUpMenu }
							placement="bottomLeft"
						>
							<Button className="r-mr-8" size="middle">
								<SettingOutlined />商品设置<DownOutlined />
							</Button>
						</Dropdown>
					) :	(
						<Button
							size="middle"
							className="r-pointer"
							data-point={ Pointer.货品打印设置 }
							onClick={ () => handleOpenPrintMainPanel() }
						><SettingOutlined />商品打印设置
						</Button>
					)
				}
				{
					isShowZeroStockVersion && (
						<Button
							size="middle"
							icon={ <SettingOutlined /> }
							onClick={ () => setColSettingVisible(true) }
						>
							列配置
						</Button>
					)
				}
			</div>
		</div>
	), [selectedRows, isShowZeroStockVersion]);

	const batchSetGoodsDropShippingFlag = (data: IBatchSetGoodsOnOkParams) => {
		let skuUpdateList: ItemScmSaleBatchUpdateDropShippingItemFlagRequest["skuUpdateList"] = [];
		if (optRow) {
			skuUpdateList.push({
				numIid: optRow.numIid,
				skuId: optRow.skuId,
				sellerId: optRow.sellerId,
				platform: optRow.platform,
				dropShippingFlag: data.shipType,
				oldDropShippingFlag: optRow.dropShippingFlag,
				supplierUserId: data.shipType === BatchSetGoodsShipType.供应商发货 ? data.supplierInfo?.supplierUserId : null,
				sysSkuId: optRow.relationSystemItemList?.[0]?.sysSkuId,
				sysItemId: optRow.relationSystemItemList?.[0]?.sysItemId,
			});
		} else if (!selectedRows.length) {
			message.info('请先选择规格');
			return;
		} else {
			selectedRows.forEach(row => {
				if (isShowSku) {
					skuUpdateList.push({
						numIid: row.numIid,
						skuId: row.skuId,
						sellerId: row.sellerId,
						platform: row.platform,
						dropShippingFlag: data.shipType,
						oldDropShippingFlag: row.dropShippingFlag,
						supplierUserId: data.shipType === BatchSetGoodsShipType.供应商发货 ? data.supplierInfo?.supplierUserId : null,
						sysSkuId: row.relationSystemItemList?.[0]?.sysSkuId,
						sysItemId: row.relationSystemItemList?.[0]?.sysItemId,
					});
				} else {
					row.platformItemSkuList.forEach(sku => {
						skuUpdateList.push({
							numIid: row.numIid,
							skuId: sku.skuId,
							sellerId: sku.sellerId,
							platform: sku.platform,
							dropShippingFlag: data.shipType,
							oldDropShippingFlag: sku.dropShippingFlag,
							supplierUserId: data.shipType === BatchSetGoodsShipType.供应商发货 ? data.supplierInfo?.supplierUserId : null,
							sysSkuId: sku.relationSystemItemList?.[0]?.sysSkuId,
							sysItemId: sku.relationSystemItemList?.[0]?.sysItemId,
						});
					});
				}
			});
		}
		setBatchSetDropShippingGoodsLoading(true);
		ItemScmSaleBatchUpdateDropShippingItemFlagApi({
			skuUpdateList,
		}).then(() => {
			systemRefresh();
		}).finally(() => {
			setBatchSetDropShippingGoodsLoading(false);
		});
	};

	const singleSetGoodsDropShippingFlag = (shipType: IBatchSetGoodsOnOkParams['shipType'], row: typeof dataSource[number]) => {
		let skuUpdateList: ItemScmSaleBatchUpdateDropShippingItemFlagRequest["skuUpdateList"] = [{
			numIid: row.numIid,
			skuId: row.skuId,
			sellerId: row.sellerId,
			platform: row.platform,
			dropShippingFlag: shipType,
			oldDropShippingFlag: row.dropShippingFlag,
			supplierUserId: shipType === BatchSetGoodsShipType.供应商发货 ? supplierList?.filter(i => i.status === distributionType.合作中)?.[0].supplierUserId : null,
			sysSkuId: row.relationSystemItemList?.[0]?.sysSkuId,
			sysItemId: row.relationSystemItemList?.[0]?.sysItemId,
		}];
		setDataSource(prev => {
			let idx = prev.findIndex(i => i.rowId == row.rowId);
			if (idx > -1) {
				prev[idx].dropShippingLoading = true;
			}
			return [...prev];
		});
		ItemScmSaleBatchUpdateDropShippingItemFlagApi({
			skuUpdateList,
		}).then(() => {
			setDataSource(prev => updateDataSource(prev, row.rowId, { dropShippingFlag: shipType }));
		}).finally(() => {
			setDataSource(prev => updateDataSource(prev, row.rowId, { dropShippingLoading: false }));
		});
	};

	const updateDataSource = (prev, rowId, updates) => {
		const idx = prev.findIndex(i => i.rowId === rowId);
		if (idx > -1) {
			prev[idx] = { ...prev[idx], ...updates };
		}
		return [...prev];
	};
	const _FormFieldList = useMemo(() => {
		let list = (isDistributorAccount || isSupplierUserAndStock) ? distributorFormFieldList : FormFieldList;
		if (!platformAuthInfo?.[labelPushPlatformEnum.四季星座网]?.platformNick) {
			list = list.filter(i => !['bindLabelPushPlatform'].includes(i.name as string));
		}
		return [...list];
	}, [isDistributorAccount, isSupplierUserAndStock, platformAuthInfo, groupList]);

	const onOKBatchMoveModal = async(selectedShops) => {
		try {
			setOnOKBatchMoveModalLoading(true);
			// 这里添加搬家逻辑
			console.log(selectedRows, selectedShops, '*************');
			const param: IParam = {
				distribItemVos: []
			};
			// 修改前：只根据 numIid 去重
			// const uniqueRows = _.uniqBy(selectedRows, 'numIid');
			// 修改后：根据 numIid 和 sellerId 组合去重
			const uniqueRows = _.uniqBy(selectedRows, item => `${item.numIid}_${item.sellerId}`);

			// 循环处理选中的商品和目标店铺
			uniqueRows.forEach(row => {
			// 获取源平台和源店铺信息
				const sourcePlatform = row.platform;
				const sourceSellerId = row.sellerId;

				// 循环处理目标店铺
				selectedShops.forEach(shop => {
				// 获取目标平台和目标店铺信息
					const targetPlatform = shop.platform;
					const targetSellerId = shop.sellerId;
					const targetSellerNick = shop.sellerNick;
					const categoryIds = shop.categoryIds;
					const categoryNames = shop.categoryNames;
					// 添加到distribItemVos数组
					param.distribItemVos.push({
						itemId: row.numIid,
						sourcePlatform,
						targetPlatform,
						sourceSellerId,
						targetSellerId,
						targetSellerNick,
						categoryIds,
						categoryNames
					});
				});
			});

			console.log('构建的搬家参数:', param);

			// 调用搬家API
			if (param.distribItemVos.length > 0) {
				// 将数据分成多批，每批20个商品
				const BATCH_SIZE = 20;
				const MAX_CONCURRENT = 3; // 最大并发数
				const batches = [];

				for (let i = 0; i < param.distribItemVos.length; i += BATCH_SIZE) {
					batches.push({
						distribItemVos: param.distribItemVos.slice(i, i + BATCH_SIZE)
					});
				}

				// 分组执行，每组最多3个并发
				const results = [];
				for (let i = 0; i < batches.length; i += MAX_CONCURRENT) {
					const currentBatch = batches.slice(i, i + MAX_CONCURRENT);
					const batchPromises = currentBatch.map(batchParam => batchPublish(batchParam));
					// 等待当前组的所有请求完成
					const batchResults = await Promise.all(batchPromises);
					results.push(...batchResults);
				}

				message.success('批量铺货任务已提交');
				setBatchMoveModalVisible(false);
				setSubmittedCount(selectedRows.length);
				setSubmitSuccessVisible(true);
			}
		} catch {
			console.log('er');
		} finally {
			setOnOKBatchMoveModalLoading(false);
		}

	};
	const batchMoveModalSelectedCount = () => {
		const selectedSetRows = _.uniqBy(selectedRows, item => `${item.numIid}_${item.sellerId}`);
		return selectedSetRows.length;
	};
	// 获取列配置
	useEffect(() => {
		if (isShowZeroStockVersion) {
			setColConfigLoading(true);
			TradeDictQueryDictApi({ userDictEnum: PlatformGoodsItemColConfigKey }).then(res => {
				let userConfig = [];
				let defaultConfig = [];
				try {
					userConfig = JSON.parse(res.value || '[]');
				} catch (e) {
					console.log('解析用户配置失败:', e);
				}
				try {
					defaultConfig = JSON.parse(res.platItemColumnItemViewConfigDefaultValue || '[]');
				} catch (e) {
					console.log('解析默认配置失败:', e);
				}
				setPageColumnAllConfig(prev => ({ ...prev, item: { userConfig, defaultConfig } }));
			}).finally(() => setColConfigLoading(false));
			TradeDictQueryDictApi({ userDictEnum: PlatformGoodsSkuColConfigKey }).then(res => {
				let userConfig = [];
				let defaultConfig = [];
				try {
					userConfig = JSON.parse(res.value || '[]');
				} catch (e) {
					console.log('解析用户配置失败:', e);
				}
				try {
					defaultConfig = JSON.parse(res.platItemColumnSkuViewConfigDefaultValue || '[]');
				} catch (e) {
					console.log('解析默认配置失败:', e);
				}
				setPageColumnAllConfig(prev => ({ ...prev, sku: { userConfig, defaultConfig } }));
			}).finally(() => setColConfigLoading(false));
		}
	}, [isShowZeroStockVersion]);

	// 列配置保存
	const handleColConfigSave = async(type: string, config: any[]) => {
		setColConfigLoading(true);
		try {
			if (type === 'reset') {
				await TradeDictInsertDictApi({ userDictEnum: isShowSku ? PlatformGoodsSkuColConfigKey : PlatformGoodsItemColConfigKey, value: JSON.stringify(pageColumnConfig.defaultConfig) });
				setPageColumnConfig(prev => ({ ...prev, userConfig: pageColumnConfig.defaultConfig }));
			} else {
				await TradeDictInsertDictApi({ userDictEnum: isShowSku ? PlatformGoodsSkuColConfigKey : PlatformGoodsItemColConfigKey, value: JSON.stringify(config) });
				setPageColumnConfig(prev => ({ ...prev, userConfig: config }));
			}
			message.success('列配置已保存');
		} finally {
			setColConfigLoading(false);
		}
	};

	return (
		<NormalLayout className={ cs(s.platformGoods, "r-bg-white") }>
			<SearchTable<
				ItemSysItemListOfItemRelationPlatformItemViewResponse["data"]["list"][0]
			>
				pageSizeId="platformGoods"
				ref={ tableRef }
				form={ form }
				fetchData={ fetchSystemList }
				responseAdapter={ responseAdapter }
				searchBtnPoint={ Pointer["商品_平台商品管理_查询_点击"] }
				additionalFormNode={ additionalFormNode }
				searchBtnText="查询"
				resetBtnText="重置"
				showSearch
				rowFormConfig={ {
					defaultParams: { approveStatus: "onsale" },
					formList: _FormFieldList,
					colProps: {
						// span: 3
					},
					size: "small",
				} }
				paginationStyle={ { bottom: '70px' } }
				hidePagination={ getTradeWaitSendFlag() }
				baseTableConfig={ {
					useVirtual: { horizontal: true, vertical: 'auto', header: false },
					// 传入一个对象可以分别指定 横向/纵向/表头 是否开启虚拟滚动
					noGap: true,
					innerTableStyle: {
						paddingTop: 0
					},
					rowKey: "rowId",
					// groupId: 'rowId',
					locale: {
						emptyText: emptyNode,
					},
					onFieldsChange,
					isStickyHeader: true,
					expandContext,
					cachePgination: true,
					pagination: {
						pageSizeOptions: [10, 20, 50, 100, 200, 500],
					},
					columns: getColumns,
					dataSource: isHideDelete
						? dataSource.filter((i) => i.enableStatus !== 0)
						: dataSource,
					scrollExtraHeight: 70,
					stickyTop: 152,
					headerColSet: {
						resizeId: `platformGoods_width_${UserStore?.userInfo?.userId}`,
					},
					expandable: {
						expandIcon: () => <></>,
					},
					// headerColSetId: `platformGoods_${isShowSku ? 'sku1' : 'sku0'}_${isShowSupplier ? 'supplier1' : 'supplier0'}_${isShowSkuPic ? 'skuPic1' : 'skuPic0'}_${UserStore?.userInfo?.userId}`
				} }
			/>
			<LimitInfoBar
				style={ {
					position: 'fixed',
					bottom: '78px',
					left: '24px',
					zIndex: 99,
				} }
			/>
			<FooterBar style={ { zIndex: 99 } }>
				<Button
					className="r-ml-10 footer-bar"
					data-point={
						Pointer.商品_平台商品管理_保存当前页所有修改_点击
					}
					type="primary"
					disabled={ !selectedRows.length }
					onClick={ () => batchSaveContent() }
					loading={ loadingMap.all }
				>
					保存当前页所有修改
				</Button>
				{loadingMap.allResult == "success" && (
					<span style={ { color: "#52C41A", position: "absolute" } }>
						<CheckCircleFilled style={ { margin: "0 5px 0 10px" } } />
						保存成功
					</span>
				)}
				{loadingMap.allResult == "fail" && (
					<span style={ { color: "#FF4D4F", position: "absolute" } }>
						<CloseCircleFilled style={ { margin: "0 5px 0 10px" } } />
						保存失败
					</span>
				)}
				{/* <Button
				{loadingMap.allResult == 'success' && <span style={ { color: '#52C41A' } }><CheckCircleFilled style={ { margin: '0 5px 0 10px' } } />保存成功</span>}
				{loadingMap.allResult == 'fail' && <span style={ { color: '#FF4D4F' } }><CloseCircleFilled style={ { margin: '0 5px 0 10px' } } />保存失败</span>} */}
				{/* {
					isShowZeroStockVersion && (
						<Button
							className="r-ml-10"
							data-point={ Pointer.商品_平台商品管理_删除平台商品规格_点击 }
							type="primary"
							disabled={ !selectedRows.length }
							onClick={ () => batchDeleteSku(selectedRows) }
							loading={ loadingMap.all }
						>删除平台商品/规格
						</Button>
					)
				} */}
			</FooterBar>
			{/* 相同sku选择 */}
			<SameSkuTable
				dataSource={ sameSku.sameSkuDataSource }
				visible={ sameSku.sameSkuVisible }
				cancel={ () => {
					setSameSku({ ...sameSku, sameSkuVisible: false });
				} }
				onOk={ sameSku.resolve }
			/>
			<SyncWaresModal
				visible={ syncWaresModalVisible }
				isDownload
				afterOK={ afterSyncWaresOK }
				onCancel={ () => {
					setSyncWaresModalVisible(false);
				} }
			/>
			<AutoBindModal
				visible={ autoBindModalVisible }
				isDownload
				afterOK={ afterSyncWaresOK }
				onCancel={ () => {
					setAutoBindModalVisible(false);
				} }
			/>
			{/* 保存当前页面所有更改的loading */}
			<Modal
				wrapClassName={ s.platformBatchSaveLoading }
				visible={ loadingMap.all }
				maskClosable={ false }
				getContainer={ document.body }
				footer={ null }
				bodyStyle={ { display: "none" } }
				destroyOnClose
			/>
			{/* 导入货品弹窗 */}
			<BatchImportModal
				visible={ importModalVisible }
				onOkAfter={ systemRefresh }
				onClose={ () => {
					setImportModalVisible(false);
				} }
			/>
			{/* 导出Excel */}
			<Modal
				centered
				visible={ downloadExcelModalVisible }
				title="导出平台商品"
				okText="导出"
				onOk={ onDownloadExcel }
				onCancel={ onCloseDownloadExcelModal }
				confirmLoading={ exportLoading }
				destroyOnClose
				maskClosable={ false }
			>
				<div>
					导出类型：
					<Radio.Group
						value={ exportType }
						onChange={ onChangeExportType }
					>
						<Radio value={ 1 }>导出勾选商品</Radio>
						<Radio value={ 2 }>导出当前查询结果</Radio>
					</Radio.Group>
				</div>
			</Modal>
			<BatchEditModal
				visible={ batchEditModalInfo.visible }
				type={ batchEditModalInfo.type }
				onCancel={ () => setBatchEditModalInfo({ visible: false, type: "" }) }
				batchEditModalInfo={ batchEditModalInfo }
				onOk={ onBatchEditOK }
			/>

			{/* 批量设置代发商品 */}
			{batchSetGoodsVisible && (
				<BatchSetGoodsModal
					isBatch={ !optRow }
					supplierId={ optRowBindSupplierId }
					onOk={ batchSetGoodsDropShippingFlag }
					selectedRows={ selectedRows }
					onCancel={ () => {
						setOptRow(null);
						setOptRowBindSupplierId(undefined);
						setBatchSetGoodsVisible(false);
					} }
				/>
			)}

			{/* 日志 */}
			{platformGoodsLogInfo.show && (
				<PlatformGoodsLogModal
					info={ platformGoodsLogInfo }
					handleCancel={ () => {
						setPlatformGoodsLogInfo((prev) => ({
							...prev,
							show: false,
						}));
					} }
				/>
			)}
			<BatchMoveModal
				visible={ batchMoveModalVisible }
				onCancel={ () => setBatchMoveModalVisible(false) }
				onOk={ (selectedShops) => {
					onOKBatchMoveModal(selectedShops);
				} }
				selectedCount={ batchMoveModalSelectedCount() }
			/>

			<Modal
				title="提交成功"
				visible={ submitSuccessVisible }
				onCancel={ () => setSubmitSuccessVisible(false) }
				footer={ [
					<Button key="close" onClick={ () => setSubmitSuccessVisible(false) }>
						关闭
					</Button>,
					<Button
						key="check"
						type="primary"
						onClick={ () => {
							// 跳转到商品搬家日志页面并重新请求
							history.push({ pathname: '/warehouse/migrationLog', state: { reload: true } });
							setSubmitSuccessVisible(false);
						} }
					>
						查看复制进度
					</Button>
				] }
				width={ 500 }
				centered
			>
				<div style={ { fontSize: '14px' } }>
					<p>已提交 <span style={ { color: '#FF4D4F', fontWeight: 'bold' } }>{batchMoveModalSelectedCount()}</span> 个商品铺货。系统将在后台上传商品至目标店铺。</p>
					<p style={ { color: '#FF4D4F', marginTop: '20px' } }>关闭弹窗或退出软件不影响进度。</p>
				</div>
			</Modal>

			{/* 列配置弹窗 */}
			<ColumnSettingDrawer
				isShowSku={ isShowSku }
				onCheckShowSku={ onCheckShowSku }
				visible={ colSettingVisible }
				onClose={ () => setColSettingVisible(false) }
				onSaveSort={ handleColConfigSave }
				colSortList={ pageColumnConfig.userConfig }
			/>
			{showVersionModal && (
				<LowVersionControlModal
					closable
					onCancel={ () => { 
						setShowVersionModal(false); 
						if (hasActivityEligibility) {
							setWelfareGiftModalVisible(true); 
						}
					} }
					pageName={ PageNameControlEnum.搬家铺货 }
				/>
			)}
			{/* 限时福利赠送弹窗 */}
			<WelfareGiftModal
				visible={ welfareGiftModalVisible }
				onCancel={ () => setWelfareGiftModalVisible(false) }
				onClaim={ async() => {
					// 这里添加领取逻辑
					console.log('立即领取');
					const data = {
						activityType: 'GIFT_PH_LIMIT',	
					};
					const res = await ActivityGiftLimit(data);
					if (res) {
						message.success('领取成功，赶紧试用吧');
					} 
				} }
				title="限时福利赠送"
				userType="活动新用户"
				giftCount={ userActivityInfo?.giftAmount }
				validityDays={ userActivityInfo?.validDay }
			/>
		</NormalLayout>
	);
};
export default observer(PlaftformGoods);
