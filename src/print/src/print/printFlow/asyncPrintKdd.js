import * as printItem from '../printItem';
import model from './../../common/model.ts';
import { previewConfig, previewConfig_plat } from '../config';
import { printWithEncryptPackages, printFhdEncrypt } from './crypto';
import { platformConfig ,isJDNewWjTpl} from './platformConfig';
import { get, post } from './../../common/http';
import urlHelper from '../../common/urlhelp.js';
import { printWithEncryptOrder, printEncryptDYNick } from './printEncrypt'
import axios from 'axios';
import PrintQueue from './printQueue'
import {ORDER_REFLUX_TYPE,platByKddTypeFnc} from '../../common/constant'

class PrintKdd {

  constructor(hook) {
    const {
      afterCheckComponentHook,
      afterChooseTempHook,
      afterChooseFjrHook,
      afterGetWaybillCodeHook,
      afterSavePrintMarkHook,
      afterSavePrintLogHook,
      afterBranchChangeHook,
      httpWhiteListHook,
      afterContinuePrintHook,
      notifyPrintHook,
      afterGetElecErrorHook,
      afterAutoAddPrintNum,
      afterShipFirstGetWaybillCodeHook
    } = hook || {};
    afterCheckComponentHook && (this.afterCheckComponentHook = afterCheckComponentHook);
    afterChooseTempHook && (this.afterChooseTempHook = afterChooseTempHook);
    afterChooseFjrHook && (this.afterChooseFjrHook = afterChooseFjrHook);
    afterGetWaybillCodeHook && (this.afterGetWaybillCodeHook = afterGetWaybillCodeHook);
    afterShipFirstGetWaybillCodeHook && (this.afterShipFirstGetWaybillCodeHook = afterShipFirstGetWaybillCodeHook);
    afterSavePrintMarkHook && (this.afterSavePrintMarkHook = afterSavePrintMarkHook);
    afterSavePrintLogHook && (this.afterSavePrintLogHook = afterSavePrintLogHook);
    afterBranchChangeHook && (this.afterBranchChangeHook = afterBranchChangeHook);
    httpWhiteListHook && (this.httpWhiteListHook = httpWhiteListHook);//把用户加入 http 白名单的钩子（加入白名单后，用户下次进入系统就直接是 http 环境了）
    afterContinuePrintHook && (this.afterContinuePrintHook = afterContinuePrintHook);
    notifyPrintHook && (this.notifyPrintHook = notifyPrintHook);
    afterGetElecErrorHook && (this.afterGetElecErrorHook = afterGetElecErrorHook);
    afterAutoAddPrintNum && (this.afterAutoAddPrintNum = afterAutoAddPrintNum);
    this.isMerge = false;
    this.printing = false; //是否正在打印
    this.waybillCodeQueue = new PrintQueue();
    this.progress = null; //进度
    this.templateInfoObj = {}; //模板信息
    this.templateDetailInfoObj = {}; //模板详情
    this.wdSet = {};
    // this.taskNum = 0;
    this.currentTask = 0;
    this.getWaybillCodeEnd = false;
    this.originalOrderList = null;
    this.fjrInfo = null;
    this.fhdFjrInfo = null;
    this.choosedPrinter = null;
    this.othersSet = {};
    this.printStyle = null;
    this.allErrorMsgList = [];
    this.allSuccessList = [];
    this.documentNoInfo = {
      allDocumentCount: 0,
      startDocumentIdx: 0
    };
    this.isOverPrint = false;
    this.printNumInfo = null;
    this.printBatch = null;
    this.printNums = null;
    this.timer = window.timer;
    this.printTraceId = ''
  }



  /**
   * 开始打印
   * @param {Object} data
   * 模板信息，可以不传
   * 订单数据
   * 用户信息（考虑初始化的时候传，这时候就可以不传了）
   * 打印
   */
  async print(data) {
    console.info('批打快递单 传给打印中心的数据', data);
    console.log(this);
    this.timer.start('打印前置处理')
    const userInfo = comp.Print.Data.userInfo;
    const printType = 'kdd';
    const setting = window.erpData?.advancedSetting?.groupPrintSetJsonString
    // 回流订单是否使用菜鸟打印还是平台打印
    const isUseCainiao = window.erpData?.advancedSetting?.refluxOrderPrintSet == '1'
    let {
      templateInfo,
      orderList,
      printOp,
      // ,shopNewPrintNum
      fjrInfoMap,
      fhdFjrInfoMap,
      printStyle,
      platformId, // 业务方传来的平台id
      firstSendMode,
      isMerge,
      ...othersSet
    } = data;
    this.printStyle = printStyle
    this.isMerge = isMerge
    this.printTraceId = printItem.generatePrintInfo({printSource:'async',isMerge:isMerge})
	  printItem.uploadPrintLog('async', {
		  orderList: orderList,
		  fjrInfoMap: fjrInfoMap,
		  printTraceId: this.printTraceId,
		  templateInfo: templateInfo,
	  })
    const platByKddType = platByKddTypeFnc({templateInfo,setting,groupInfo:isMerge ? templateInfo : null})

    let printPlatform = null
    const needUseTemp = []
    orderList.forEach(o => {
      // 给订单加上匹配的模板类型
      let kddType = null
      if (isMerge) {
        if (o.hlPlatformType && !['hl-other','hl-tx'].includes(o.hlPlatformType) && (o.hlEncryptOrder || !isUseCainiao)) {
          let hlType = o.hlPlatformType.replace('hl-', '')
          if(['sph'].includes(hlType)){ // 视频号小红书聚合模板组暂未接入平台面单
            kddType = platByKddType[o.platform]
          } else {
            kddType = ORDER_REFLUX_TYPE[hlType]
			// 新版小红书兼容，新版旧版模板都可匹配
			if(hlType === 'xhs') {
				let xhsTemp = templateInfo.userTemplateList.find(it=>it.expressType === kddType)
				kddType = !!xhsTemp ? 13 : 16
			}
          }
        } else {
			const isDyEncodeHand = o.platform === 'fxg' && o.source === 'HAND' && o.tradeEncodeType == 1
			// 抖音明文手工单需要改写一下绑定，跟手工单保持一致`
			isDyEncodeHand ? (kddType = platByKddType['hand']) : (kddType = platByKddType[o.platform])
        }
      }else{
        kddType = templateInfo.KddType
      }


      o.kddType = kddType
      // 判断是否已有该类型模板
      if (!needUseTemp.includes(kddType)) {
        needUseTemp.push(kddType)
      }
    })
    // TODO 校验控件提前
    let installResult = null
    let lodopInstallResult = null
    if (isMerge) {
      installResult = await printItem.checkPrintComponentByKdzs();
      this.afterCheckComponentHook(installResult);
      if (!installResult.isSuccess) {
        return;
      }
    }
    // // 抖音交替打印需要lodop校验
    // if (!isMerge && templateInfo.KddType == '8' && isConcatFhd) {
    //   lodopInstallResult = printItem.checkLodopProgress();
    // }
    let allInstallResult = null
    for (let i = 0; i < needUseTemp.length; i++) {
      allInstallResult = await printItem.checkPrintComponentByMerge(needUseTemp[i])
      if (!allInstallResult.isSuccess) {
        this.timer.stop('打印前置处理', {
          type: 'batchKdd',
          desc: '未安装控件',
          printTraceId: this.printTraceId
        })
        return
      }
    }

    // TODO 优先处理好模板

    if (isMerge) {
      const printTemplateInfo = comp.Print.Data.kddTemplates.find(o => o.id == templateInfo.id)
      const newTemplateInfo = Object.assign({}, templateInfo, printTemplateInfo)
      newTemplateInfo.userTemplateList = needUseTemp.map(o => {
        let tempInfo = templateInfo.userTemplateList.find(it => it.expressType === o)
        return tempInfo
      })
      const tempArr = newTemplateInfo.userTemplateList
      for (let i = 0; i < tempArr.length; i++) {
        this.templateDetailInfoObj[tempArr[i].expressType] = await printItem.getTempDetailInfo({
          printType,
          templateId: tempArr[i].userTemplateId,
        });
        this.templateInfoObj[tempArr[i].expressType] = this.templateDetailInfoObj[[tempArr[i].expressType]].ModeListShow
        this.templateInfoObj[tempArr[i].expressType].styleId = this.templateDetailInfoObj[[tempArr[i].expressType]].ModeList.StyleId
      }
    } else {
      printPlatform = templateInfo.KddType
      this.templateInfoObj[printPlatform] = templateInfo
      this.templateDetailInfoObj[printPlatform] = await printItem.getTempDetailInfo({
        printType,
        templateId: templateInfo.Mode_ListShowId,
      });
    }
    console.info('获取模板详情', this.templateDetailInfoObj);

    // 获取传入的其他配置
    let {
      canUseNewWaybillCodeSet, //可以使用新单号
      canChooseWaybillCodeNumSet, // 可以选择单号数量
      choosePrinterNotiSet, // 可以弹出选择打印机
      displayPrintNumSet, // 可以选择打印机打印份数
      // openPrintNotifySet, // 出纸进度
    } = othersSet;
    this.data = {
      printOp,
      printType,
    };
    this.originalOrderList = [...orderList]; // 源数据
    comp.Print.Data.fjrInfoMap = fjrInfoMap;
    comp.Print.Data.platformId = platformId || '147317';

    let isConcatFhd = false;
    // 判断是否需要发货单快递单交替打印
    if (!isMerge && this.templateDetailInfoObj[templateInfo.KddType]?.ModeTempPrintcfg.IsConcatFhd) {
       // 判断是否存在可交替打印的发货单
       const fhdTemplateList = new comp.Print().getFhdTemplateList(false);
       let fhdId = this.templateDetailInfoObj[templateInfo.KddType]?.ModeListShow?.rotationUserTemplateId
       let fhdIsDelete = fhdTemplateList.ModeListShows.find(o=>o.Mode_ListShowId == fhdId)
       if(!fhdId || !fhdIsDelete){
           // 未绑定发货单或者发货单已被删除
           Tatami.showFail('交替打印未绑定正确的热敏发货单')
       }else{
          const fhdTemplateList = new comp.Print().getFhdTemplateList(false);
          comp.Print.Data.hasNoRM_common = !(fhdTemplateList.ModeListShows || []).some(item => item.Exid == 666);
          comp.Print.Data.hasNoRM_yilian = !(fhdTemplateList.ModeListShows || []).some(item => item.Exid == 670);
          // const isYilianTemp = comp.base.isYilianTemp(templateInfo.styleId);
          const isYilianTemp = this.templateDetailInfoObj[templateInfo.KddType]?.ModeList?.WidthPaper === 760;
          const tempType = isYilianTemp ? 'yilian' : 'common';
          // 1.本模板是一联模板，并且一联热敏发货单存在
          // 2.本模板是普通模板，并且普通热敏发货单存在（目前非一联模板就是普通模板，2019.9）
          if ((isYilianTemp && !comp.Print.Data.hasNoRM_yilian) || (!isYilianTemp && !comp.Print.Data.hasNoRM_common)) {
            isConcatFhd = true;
            new comp.Print().setIsConcatFhd(tempType, true);
        }
      }
    }

    // if (!isMerge && printPlatform == '8' && isConcatFhd) {
    //   lodopInstallResult = printItem.checkLodopProgress();
    // }
    // 第二次检查一下模板能否打印 是否有退款
    // erp平台检查能否打印
    await printItem.checkCanPrint({
      printType,
      orderList,
      printOp,
    });

    const bindFjrId = (isMerge ? templateInfo.bindFjrId : this.templateDetailInfoObj[printPlatform].ModeListShow.bindFjrId) || ''
    // 获取本次打印使用的的发件人
    const { fjrInfo, modifyDefaultFjr } = await printItem.chooseFjr(fjrInfoMap, printType, bindFjrId, data);
    modifyDefaultFjr.length && this.afterChooseFjrHook(modifyDefaultFjr);
    printItem.setTradeFjr({ orderList, fjrInfo, printType });

    let fhdFjrInfo;
    if (isConcatFhd) {
      ({ fjrInfo: fhdFjrInfo } = await printItem.chooseFjr(fhdFjrInfoMap, 'fhd',null,{orderList}));
    }

    // 检查一下发件人省市区（大头笔）有没有
    await printItem.checkFjrAddress(fjrInfo);

    /**
     * 获取打印时必要的数据信息
     * 例如极兔（龙邦）模板需要获取商品信息
     * @param templateInfo
     * @returns {Object}
     */

    const otherPrintInfo = {}
    for (let k in this.templateDetailInfoObj) {
      otherPrintInfo[k] = await printItem.getOtherPrintInfo(this.templateDetailInfoObj[k]);
    }
    console.assert(otherPrintInfo, 'otherPrintInfo');


    /** 获取网点信息
     * @param {object} templateInfo
     */
    let errTip = '' //网点错误提示
    let errWd = [] //错误网点模板类型
    for (let k = 0; k < needUseTemp.length; k++) {
      this.wdSet[needUseTemp[k]] = await printItem.getYunBranchSet(this.templateDetailInfoObj[needUseTemp[k]]?.ModeListShow).catch(err => {
        errTip += this.templateInfoObj[needUseTemp[k]].ExcodeName + '：' + err + '<br/>'
        errWd.push(needUseTemp[k])
      });
    }

    if (!!errTip) {
      await new Promise((resolve) => {
        const modelConfig = {
          type: 'confirm',
          content: errTip + '<br/><br/>是否过滤模板类型订单继续打印？',
          width: 426,
          height: 200,
          okName: '是，继续打印',
          cancelName: '取消',
          okCb: () => {
            //过滤打印数据
            let successOrder = []
            let errorOrder = []

            orderList.forEach((item) => {
              if (errWd.includes(item.kddType)) {
                errorOrder.push(item)
              } else {
                successOrder.push(item)
              }

            })
            const errorOrderIdList = errorOrder.map(item => item.togetherId);
            orderList = [...successOrder];
            this.afterContinuePrintHook(errorOrderIdList)
            if (!orderList.length) {
              Tatami.showNoti('请选择订单重新打印');
              return false
            }
            resolve();
          },
          cancelCb: () => {
            return false;
          },
        };
        model(modelConfig);
      });
    }
    console.assert(this.wdSet, '未获取到网点信息');
	  orderList = await printEncryptDYNick({ orderList, templateDetailInfo: this.templateDetailInfoObj[8] });

    // if (printPlatform === 8 && !!data.fhdDecodeSwitch && isConcatFhd) { //
    //   orderList = await printFhdEncrypt(orderList, data.fhdDecodeSwitch);
    // } else {
    orderList = await printWithEncryptPackages(orderList, templateInfo);
    // }
     orderList = await printWithEncryptOrder({ orderList, afterContinuePrintHook: this.afterContinuePrintHook ,needUseTemp})
    // eslint-disable-next-line require-atomic-updates
    this.timer.stop('打印前置处理', {
      type: 'asyncPrint',
      printTraceId: this.printTraceId
    })
    /**
     * 快递单号获取逻辑,重打提示框，多单号选择框，子母件选择框
     * @param {Object} orderList
     * @param {Object} templateInfo
     * @param {Object} templateDetailInfo
     * @returns {boolean} isMustUseNewWaybillCode 是否必须用新单号
     * @returns {number} waybillCodeCount 申请单号的总数量
     * @returns {Array} oldWaybillCodeList 要使用的旧单号
     * @returns {number} sfZDQuantity 子母件数量
     */
    // this.timer.start('选择打印信息')
    const {
      waybillCodeCount,
      sonElecNoCount,
      oldYdNos,
      isMustUseNewWaybillCode,
      isZiMuJian,
      printNums,
      choosedPrinter,
      goodsDescription,
      packagingDescription,
      weight
    } = await this._getInfoAboutWaybillCodeNums({
      orderList,
      templateInfo,
      isMerge,
      canUseNewWaybillCodeSet, //可以使用新单号
      canChooseWaybillCodeNumSet, // 可以选择单号数量
      choosePrinterNotiSet, // 可以弹出选择打印机
      displayPrintNumSet,
    });

    // const infoAboutWaybillCodeNums = {
    //   // 单号数控
    //   waybillCodeCount: 2,
    //   // 子母件数量
    //   sonElecNoCount: '',
    //   // 旧单号列表
    //   oldYdNos: null,
    //   // 是否必须使用新单号
    //   isMustUseNewWaybillCode: false,
    //   // 包装方式
    //   goodsDescription: '',
    //   // 货品描述
    //   packagingDescription: '',
    //   // 子母件是否
    //   isZiMuJian: false,
    //   // 选择打印机
    //   choosedPrinter: '',
    //   // 打印份数
    //   printNums: 1
    // }

    Tatami.pub('otherPorint', '边申请边打印')

    // 发件人信息
    this.timer.start('获取打印信息')

    this.fjrInfo = fjrInfo
    this.fhdFjrInfo = fhdFjrInfo
    this.choosedPrinter = choosedPrinter
    this.documentNoInfo.allDocumentCount = orderList.length * waybillCodeCount * printNums
    this.printNumInfo = {
      waybillCodeCount,
      printNums
    }


    //获取打印批次号与打印序号
    this.printBatch = await printItem.getPrintBatch()
    let printNumRes = await printItem.getPrintNum({
      count: this.documentNoInfo.allDocumentCount,
      printType: 'kdd'
    })
    this.printNums = parseInt(printNumRes?.printNum || 0)
    this.timer.stop('获取打印信息', {
      type: 'asyncPrint',
      desc: `订单数量：${orderList.length};单号数量：${waybillCodeCount};打印份数：${printNums}`,
      printTraceId: this.printTraceId
    })
    comp.Print.setPrintCopies(printNums);
    this.timer.start('申请单号并打印')
    //分类拆分订单
    const orderListArr = this.getOrderListArr({ orderList, isMerge })
    // this.taskNum = orderListArr.length
    // 开启进度弹窗
    this.progress = new printItem.progress(orderList.length * waybillCodeCount)
    Tatami.pub('otherPorint', '边申请边打印进度展示')
    for (let [index, item] of new Map(orderListArr.map((it, idx) => [idx, it]))) {
      let { successList, errorList, errorMsgList } = await printItem.getWaybillCodeByAsync({
        orderList: item.orderList,
        temp: this.templateInfoObj[item.kddType],
        elecNoCount: waybillCodeCount, // 单号数量
        sfZDQuantity: sonElecNoCount, // 子母件生成个数(不仅仅是顺丰，只是后端字段名是这个而已)
        sonElecNoCount: sonElecNoCount, //子单号数量（菜鸟快运子母件）
        oldYdNos: oldYdNos, //要使用的旧单号
        printOp, // [操作来源  0：单打 1：批打 2：预发货 3：自动预发货 4：手工订单]
        wdSet: this.wdSet[item.kddType], // [网点地址选择对象]
        isUseOrderFjr: false, //是否使用订单里面设置的发件人信息：发件人信息在每一笔订单数据中，无需传递kddFjr 或者 fhdFjr
        fjrInfo, // 发件人信息，兼容单店铺和多店铺
        isNewEleNo: isMustUseNewWaybillCode, //是否必须使用新单号
        loginUserId: userInfo.userId,//登陆用户userId
        isRepeat: false,// [针对安能盲区添加的安能盲区订单重试的措施 true:代表安能盲区重试]
        modeInfo: this.templateDetailInfoObj[item.kddType],
        hasWWChat: false,//[true/false:是否需要联系旺旺提示]
        goodsDescription: otherPrintInfo.goodsDescription || goodsDescription, //物品描述，获取单号时需要
        afterGetWaybillCodeHook: this.afterGetWaybillCodeHook,//获取单号后执行的钩子函数
        packagingDescription: packagingDescription, //物品描述，获取单号时需要
        afterContinuePrintHook: this.afterContinuePrintHook,
        isZiMuJian:  isZiMuJian,
        weight,
        afterGetElecErrorHook: this.afterGetElecErrorHook,
        processBatch: this.printTraceId
      })
      if (successList.length) {
        console.log(successList, errorList, errorMsgList, 'getWaybillCodeByAsync');
        const ydnumCount = waybillCodeCount || (sfZDQuantity + 1) || 0;
        const checkResult = printItem.checkAutoAddPrintNum({
          orderList: successList,
          othersSet,
          modeInfo: this.templateInfoObj[item.kddType],
          ydnumCount,
        });
        successList = checkResult.orderList;
        if (checkResult.isAutoAdd) {
          this.afterAutoAddPrintNum && this.afterAutoAddPrintNum(successList, ydnumCount || 0);
        }

        // 多单号处理逻辑
        // eslint-disable-next-line require-atomic-updates
        let resList = await printItem.moreWaybillCodeProcess({
          orderList: successList,
          sfZDQuantity: sonElecNoCount,
          waybillCodeCount: waybillCodeCount,
          temp: this.templateInfoObj[item.kddType],
          isZiMuJian: false//  isZiMuJian,
        });
        this.waybillCodeQueue.enquequ({
          orderList: resList,
          kddType: item.kddType
        })
        console.log(resList, this.waybillCodeQueue.size(), 'getWaybillCodeByAsync');
        //成功展示的是快递单份数
        this.allSuccessList.push(...resList)
      }
      this.allErrorMsgList.push(...errorMsgList)
      // if(this.allErrorMsgList.length === orderList.length ){
      //   this.progress.remove()
      //   comp.Print.Data.isPrintNow = false
      //   await printItem.printEndResult({
      //     allErrorMsgList:this.allErrorMsgList,
      //     allSuccessList:this.allSuccessList
      //    })
      // }

      if (index === orderListArr.length - 1) {
        this.getWaybillCodeEnd = true
      }
      this.assemblyPrintData()
    }

  }
  // 申请单号并打印
  async assemblyPrintData() {
    if (this.getWaybillCodeEnd && this.waybillCodeQueue.isEmpty() && !this.isOverPrint) {
      this.isOverPrint = true
      this.getPrintEndResult()
    }
    if (this.printing || this.waybillCodeQueue.isEmpty()) return
    this.printing = true
    console.log(this.printing, this.waybillCodeQueue.size(), 'aaaa');
    const printWaybillCodeData = this.waybillCodeQueue.dequequ()
    let { kddType, orderList } = printWaybillCodeData
    // let isPrintEnd = this.getWaybillCodeEnd && this.waybillCodeQueue.isEmpty()  // 是否最后一个打印任务
    console.log(kddType, orderList, '当前打印的数据');
    const wdSet = this.wdSet[kddType]
    const templateInfo = this.templateInfoObj[kddType]
    const templateDetailInfo = this.templateDetailInfoObj[kddType]
    /*
    * 这里还能放一个检查是否发货内容打印到另一张上的逻辑
    * 行为仍然是修改 orderList 的内容
    * 暂时只有淘宝需要这个逻辑
    */
  //  序号叠加

    //获取打印序号，把打印序号信息放入 orderList 中
    // eslint-disable-next-line require-atomic-updates
    let prinrNumResult = await printItem.asyncDealWithPrintNum({
      orderList,
      printType: 'kdd',
    });
    orderList = prinrNumResult.orderList
    this.printNums = prinrNumResult.printNum
    orderList = await printItem.asyncDealWithPrintBatch({
      orderList,
      printNumRes: this.printBatch,
      startIndex: this.documentNoInfo.startDocumentIdx,
      count: this.documentNoInfo.allDocumentCount
    });
    // 判断打印内容是否需要打印在另一张纸上
    // 对于「需要在第二张纸上打印发货内容的 order 中」加上 isNeedBreakPage:true
    const checkedBreakPageResult = printItem.checkedBreakPage({
      orderList,
      modeInfo: this.templateDetailInfoObj[kddType],
      printStyle: this.printStyle,
    });
    orderList = checkedBreakPageResult.orderList;
    const extraPrintContentPageNum = checkedBreakPageResult.extraPrintContentPageNum;


    //打印进度弹窗
    // const progress = new printItem.getPrintProgress({
    //   showType: 'mark',
    //   totalCount: orderList.length,
    // });


    //打印标记
    // 如果某个订单获取单号报错了，则对应的订单不进行标记
    let originalOrderList = this.originalOrderList.reduce((acc, item) => {
      if (orderList.find(order => order.togetherId === item.togetherId)) {
        acc.push(item);
      }
      return acc;
    }, []);
    let markPrintResults = await printItem.markPrintStatus({
      orderList: originalOrderList, //此处需要原始的 orderList 数据
      printType: 'kdd',
      templateInfo,
      printer: this.choosedPrinter,
      wdSet,
      printCopies: 1,
    });
    if (!markPrintResults.isSuccess) {
      Tatami.showFail('订单标记打印失败，请重试，或联系在线客服');
      return
    }

    //完成「打印标记」后的钩子
    this.afterSavePrintMarkHook({ markTradeList: orderList });

    // progress.showType('progress');
    //组装打印数据
    const printDatas = await printItem.getFormatPrintData({
      orderList,
      templateInfo,
      kddFjrMap: this.fjrInfo,
      printType: 'kdd',
      modeInfo: templateDetailInfo,
      isGetFhdFjr: false,
      fhdFjrMap: this.fhdFjrInfo,
    });

    console.assert(printDatas, '组装数据');

    // 打印步骤
    const params_doPrint = {
      orderList,
      printDatas,
      choosedPrinter: this.choosedPrinter,
      templateInfo,
      templateDetailInfo,
      othersSet: this.othersSet,
      wdSet,
      printNums: 1,
      kddType,
      // isPrintEnd
    };

    this._doPrint(params_doPrint);
  }
  // 打印 && 底单逻辑
  _doPrint({
    orderList,
    printDatas,
    kddType,
    choosedPrinter,
    templateInfo,
    templateDetailInfo,
    othersSet,
    wdSet,
    printNums,
    // isPrintEnd
  }) {
    const self = this;
    console.log('打印 && 底单逻辑----');
    const printParams = {
      printDatas: printDatas,
      tempId: templateInfo.Mode_ListShowId,
      ptype: 'kdd',
      printBoxIsShow: 0,
      selectedPrinter: choosedPrinter,
      templateSet: templateDetailInfo,
      showPrinting: othersSet.showPrinting, // 显示出纸进度 暂时只针对快递单开放，开关设置走高级设置
      wdSet: wdSet,
      documentNoInfo: Object.assign({},this.documentNoInfo)
    };
    console.info(printParams);
    //打印回调methods获取
    const methodsCb = {
      printStartFunc: function (task) {
        // documentNoInfo.startDocumentIdx += printData.data.length * printNums
        const printCb = () => {
          self.printing = false
          self.assemblyPrintData()
        }
        if (self.isMerge) p.asyncListenPrinterDocument({ task, choosedPrinter, kddType, progress: self.progress, printCb })
      },
      printSynFunc: function (curCount) {
        // progress.updateProgress(curCount);
        if (!self.isMerge) {
          self.printing = false
          self.assemblyPrintData()
        }
      },
      printFailCb: async function (e) {
        console.log(e, '打印错误信息');
        // if (isPrintEnd) {
        //   self.progress.remove()
        //   comp.Print.Data.isPrintNow = false
        //   await printItem.printEndResult({
        //     allErrorMsgList:self.allErrorMsgList,
        //     allSuccessList:self.allSuccessList
        //   })
        // }
      },
      printOkFunc: async function () {
        self.progress.updateProgress(printDatas.length)
        //底单日志存储
        // progress.showType('log');
        const results = await printItem.saveKddLog({
          orderList,
          templateInfo,
          printOp: 1, //
          templateDetailInfo,
          printNums,
          processBatch: self.printTraceId
        });
        if (!!templateDetailInfo.ModeTempPrintcfg.IsConcatFhd) {
		  const tempId = templateDetailInfo?.ModeListShow?.rotationUserTemplateId || '';// await printItem.getDefaultTempId('fhd');
          templateInfo = await printItem.getTempById(tempId, 'fhd');
          await printItem.saveFhdLog({
            orderList,
            templateInfo,
          });
        }
        console.log('saveKddLog 方法的返回值', results);
        // if (isPrintEnd) {
        //   self.getPrintEndResult()
        // }

        //TODO 底单补偿。
        // progress.remove();
        console.log(printParams, 'printParams');
        // const isYilianTemp = comp.base.isYilianTemp(templateInfo.styleId);
        const isYilianTemp = printParams?.templateSet?.ModeList?.WidthPaper === 760;
        const tempType = isYilianTemp ? 'yilian' : 'common';
        new comp.Print().setIsConcatFhd(tempType, false);
      },
      /**
       *
       * @param {object} data  // 打印控件返回的内容
       * @param {string} compType //是哪种控件返回的提示
       */
      notifyPrintCb: function (data, compType) {
        console.log('--notifyPrintCb---');
        console.info(data);
        console.info(compType);
        self.notifyPrintHook(data, compType);
      },
    };

    const p = new comp.Print();

    p.printTemplate(printParams, methodsCb);
    self.documentNoInfo.startDocumentIdx += orderList.length

  }

  // 封装：通过单号选择框获取单号的相关信息
  async _getInfoAboutWaybillCodeNums({
    orderList,
    templateInfo,
    isMerge,
    canUseNewWaybillCodeSet, //可以使用新单号
    canChooseWaybillCodeNumSet, // 可以选择单号数量
    choosePrinterNotiSet, // 可以弹出选择打印机
    displayPrintNumSet,
  }) {
    let that = new comp.Print.FN();
    // 有没有打印过快递单
    const hasPrinted = await printItem.checkOrderHasPrint(orderList, 'kdd');
	let isincludesJDNew = isJDNewWjTpl(templateInfo)
	if(isincludesJDNew) canChooseWaybillCodeNumSet =false
    // 判断本次打印是否是子母件
    let zmjType = 1;
    let isZiMuJian = false;
    // 判断本次打印能不能用原单号打印
    let canUseOldNo = false;
    let oldWaybillCodeList = []
    zmjType = await printItem.checkIsZiMuJianNew({ modeInfo: templateInfo, isMerge });
    isZiMuJian = zmjType != 1
    canUseOldNo = zmjType == 1 && orderList.length == 1
    if (canUseOldNo) {
      oldWaybillCodeList = (orderList[0]?.sidNos || []).filter((item) => {
        return item !== '打印后生成'
      })
    }

    // 打印后发货开关
    let isOpenShip = window.erpData.userSetting.remindShip
    // 打印后发货设置钩子
    const funcHook = (window.printAPI.compHookObj || {}).updateUserSettingHook
    // 打印机列表
    let defaultPrinter = null
    const { dPrinter, printers } = await printItem.getPrinterList();
    const printersMap = Tatami.localcache.get('__printersMap__') || {};
    //取融合打印的打印机缓存
    //融合打印
    let id = null
    let tempData = null
    if(isMerge){
        id =  `group${templateInfo?.id}`;
        tempData = {
          exCode:templateInfo.exCode,
          exCodeName:templateInfo.groupName,
        }
    }else{
        id = templateInfo?.Mode_ListShowId;
        tempData = {
          exCode:templateInfo.ExCode,
          exCodeName:templateInfo.ExcodeName,
          kddType:templateInfo.KddType
        }
    }

    defaultPrinter = printersMap[id] || ((printers || []).includes(templateInfo.defaultPrinter) ? templateInfo.defaultPrinter : dPrinter) || that.getDefaultPrinter()
    // 单号数量和使用新/旧单号信息

    /**
     * 判断是否弹出已打印弹窗并执行弹框；如果已打印，允许用户选择使用原单号还是新单号打印，新单号可以选择新单号个数
     * 会返回一个对象，包含三个值：
     * isMustUseNewWaybillCode 是否是用新单号 Boolean
     * oldWaybillCodeList 旧单号列表 Array 缺省为空数组
     * waybillCodeCount 本次需要的单号数量 Number
    //  */
    // chooseWaybillInfos = await printItem.surePrintAgain({
    //   printType,
    //   templateInfo,
    //   hasPrinted,
    //   canUseOldNo,
    //   canUseNewWaybillCodeSet,
    //   canChooseWaybillCodeNumSet,
    //   orderList,
    //   isZiMuJian,
    //   firstSendMode
    // }) || chooseWaybillInfos;

    // // 如果订单没打印过，并且高级设置中允许选择多个单号 || 打印的订单数量超过两个  => 会有多单号选择框
    // // 子母件不会有多单号选择框
    // // 关于「是否是用新单号」如果有重打提示框，则由重打提示框获取该值，如果未弹出重打提示框，本逻辑会把「是否是用新单号」设置为 true
    // chooseWaybillInfos = await printItem.chooseWaybillCodeNum({
    //   hasPrinted,
    //   canChooseWaybillCodeNumSet,
    //   templateInfo,
    //   isZiMuJian,
    //   orderListLength: orderList.length,
    //   isMustUseNewWaybillCode: chooseWaybillInfos.isMustUseNewWaybillCode,//来自重打提示框的「是否申请新单号」
    // }) || chooseWaybillInfos;

    // // 获取子母件单号数量等相关信息，菜鸟子母件和非菜鸟子母件
    // let zmjRelativeInfos = {};
    // if (isZiMuJian) {
    //   zmjRelativeInfos = await printItem.getZmjRelativeInfos(templateInfo, chooseWaybillInfos);
    // }

    // // 抖音顺丰非字母件需要传 sfZDQuantity: undefined 用于后端区分
    // let dyExtraParams = {};
    // if (templateInfo.KddType === 8 && templateInfo.ExCode === 'SF' && !isZiMuJian) {
    //   dyExtraParams = {
    //     sfZDQuantity: undefined,
    //   };
    // }
    // 选择打印机流程
    let infoAboutWaybillCodeNums = await printItem.choosePrintSet({
      tempData,
      orderList,
      zmjType,
      canChooseWaybillCodeNumSet,
      displayPrintNumSet,
      canUseOldNo,
      hasPrinted,
      oldWaybillCodeList,
      funcHook,
      isOpenShip,
      printers,
      defaultPrinter,
      localcachePrintId: id,
	  isincludesJDNew
    });
    // return {
    //   isMustUseNewWaybillCode: chooseWaybillInfos.isMustUseNewWaybillCode,
    //   waybillCodeCount: chooseWaybillInfos.waybillCodeCount,
    //   oldWaybillCodeList: chooseWaybillInfos.oldWaybillCodeList,
    //   isZiMuJian,
    //   ...zmjRelativeInfos,
    //   // sfZDQuantity:zmjRelativeInfos.sfZDQuantity,
    //   ...dyExtraParams,
    // };
    return {
      ...infoAboutWaybillCodeNums,
      isZiMuJian: zmjType != '1'
    }
  }

  getOrderListArr({ orderList, isMerge }) {
    let orderListArr = []
    let currentType = null
    let currentArr = []
    if (isMerge) {
      orderList.map((item, idx) => {
        if ((currentType !== item.kddType || currentArr.length == 8) && idx !== 0) {
          orderListArr.push({
            kddType: currentType,
            orderList: currentArr
          })
          currentType = item.kddType
          currentArr = []
          currentArr.push(item)
        } else {
          currentType = item.kddType
          currentArr.push(item)
        }
        if (idx === orderList.length - 1) {
          orderListArr.push({
            kddType: currentType,
            orderList: currentArr
          })
        }
      })
    } else {
      orderList.map((item, idx) => {
        currentArr.push(item)
        if (idx % 8 === 7 || idx === orderList.length - 1) {
          orderListArr.push({
            kddType: item.kddType,
            orderList: currentArr
          })
          currentArr = []
        }
      })
    }
    return orderListArr
  }
  async getPrintEndResult() {
    let that = this
    this.progress.remove()
    comp.Print.Data.isPrintNow = false
    this.timer.stop('申请单号并打印', {
      type: 'asyncPrint',
      desc: `成功申请单号数量：${this.allSuccessList.length};失败数量：${this.allErrorMsgList.length}`,
      printTraceId: this.printTraceId
    })
    await printItem.printEndResult({
      allErrorMsgList: this.allErrorMsgList,
      allSuccessList: this.allSuccessList,
      printNumInfo: this.printNumInfo,
      cancalCb: () => {
        that.afterSavePrintLogHook();
        // 清除打印来源信息
        window.printSourceInfo = null
      }
    })
  }
  // --------------钩子函数的初始化--------------------
  /**
   * 检测控件安装结果后执行的钩子函数
   * type: 'cainiao'|'lodop'
   * isSuccess: true|false
   */
  afterCheckComponentHook() {
    console.log('afterCheckComponentHook', arguments);
  }

  //选择模板后执行的钩子函数
  afterChooseTempHook() {
    console.log('afterChooseTempHook', arguments);
  }

  //选择发件人后执行的钩子函数
  afterChooseFjrHook() {
    console.log('afterChooseFjrHook', arguments);
  }

  notifyPrintHook() {
    console.log('afterChooseFjrHook', arguments);
  }

  afterGetWaybillCodeHook() {
    console.log('afterGetWaybillCodeHook', arguments);
  }
  afterShipFirstGetWaybillCodeHook() {
    console.log('afterShipFirstGetWaybillCodeHook', arguments);
  }

  afterSavePrintMarkHook() {
    console.log('afterSavePrintMarkHook', arguments);
  }

  //保存打印日志后的钩子函数
  afterSavePrintLogHook() {
    console.log('afterSavePrintLogHook', arguments);
  }

  afterBranchChangeHook() {
    console.log('afterBranchChangeHook', arguments);
  }

  httpWhiteListHook() {
    console.log('httpWhiteListHook', arguments);
  }
  // 获取单号有失败的订单时的回调
  afterGetElecErrorHook() {
    console.log('afterGetElecErrorHook', arguments);
  }

  // 备注多单号完成后的回调
  afterAutoAddPrintNum(dealedOrderList, waybillCodeCount) {
    // 打印时自动追加单号到备注
    if (advancedSet.isAutoAddPrintNum) {
      const memos = [];
      if (waybillCodeCount > 1 || (waybillCodeCount === 1 && !advancedSet.isNotAddPrintNumOnlyOne)) {
        dealedOrderList.forEach(order => {
          memos.push({
            togetherId: order.togetherId,
            tid: order.tids[0],
            flag: order.sellerFlag || '',
            memo: order.sellerMome || order.allYdNos.join(','),
            userId: order.userId,
          });
        });
      }
      // 调用保存备注接口
      this.getCtrl('com.printSetting', 'singleModifyMemo', memos, false, true);
    }
  }

  // 获取单号有失败的订单时的回调
  afterContinuePrintHook() {
    console.log('afterContinuePrintHook', arguments);
  }

  // --------------钩子函数的初始化--------------------
}
export default PrintKdd;
