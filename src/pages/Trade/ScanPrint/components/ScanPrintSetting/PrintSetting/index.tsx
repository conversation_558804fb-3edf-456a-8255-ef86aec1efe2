import { Button, Checkbox, Input, Popover, Tooltip } from "antd";
import React, { useEffect, useMemo, useState } from "react";
import { useHistory } from 'react-router-dom';
import Icon from "@/components/Icon";
import VideoMonitorControl from "@/pages/AfterSale/ScanRegister/components/AftersaleScan/ScanSetModal/components/VideoMonitorControl";
import scanPrintStore from "@/stores/trade/scanPrint";


const PrintSetting = (props: any) => {
	let { setting, handleInputChange } = props;
	const history = useHistory();
	const {
		videoMonitorState,
	} = scanPrintStore;
	return (
		<div style={ { borderBottom: '1px dotted #bbb' } }>
			<div className="itemBox" style={ { borderBottom: '0px' } }>
				<div className="leftTittle">打印设置:</div>
				<div className="rightChoose">
				
					<div className="item">
						<div className="r-flex">
							<div>
								<Checkbox
									checked={ setting.isPrintTag === 1 }
									onChange={ (e) => handleInputChange(e, 'isPrintTag') }
								>
									打印快递单同时打印商品吊牌
								</Checkbox>
								<Popover
									content={ (
										<div>
											<p>1. 打印吊牌时需要安装Lodop控件，<a href="https://www.lodop.net/download/CLodop_Setup_for_Win64NT_6.609EN.zip" target="_blank" rel="noreferrer">点击下载</a>；</p>
											<p>2. 组合货品默认打印子货品的吊牌；</p>
											<p>3. 打印的吊牌的打印机需要单独设置；</p>
											<p>4. 打印快递单同时打印吊牌会影响快递单打印速度；</p>
										</div>
									) }
								>
									<span>
										<Icon type="wenhao-xian" className="r-c-999 r-pointer" />
									</span>
								</Popover>
								<span className="kdzs-link-text r-fs-14 r-ml-6" onClick={ () => { history.push('/settings/system?introName=printTagSet'); } }>吊牌设置</span>
							</div>
						</div>
					</div>
				
					<div className="item r-mt-8">
						<div className="r-flex">
							<div>
								<Checkbox
									checked={ setting.spaceKeyPrint === 1 }
									onChange={ (e) => handleInputChange(e, 'spaceKeyPrint') }
								>
									支持空格手动打印操作
								</Checkbox>
							</div>
						</div>
					
					</div>
			
				</div>
			</div>
			<VideoMonitorControl
				videoMonitorState={ videoMonitorState }
				onVideoMonitorChange={ (e) => handleInputChange(e, 'videoMonitor') }
				name="扫描打印视频监控："
			/>
		</div>
	);
};

export default PrintSetting;
