import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Button, Popover, Spin, Popconfirm, Modal, message, Collapse, Form, InputNumber } from "antd";
import { QuestionCircleOutlined, DeleteOutlined, FormOutlined, SaveOutlined, CloseCircleOutlined, CaretRightOutlined } from '@ant-design/icons';
import { observer } from "mobx-react-lite";
import { useRequest } from "ahooks";
import { cloneDeep, debounce, isEmpty, template } from "lodash";
import dayjs from "dayjs";
import memoFn from '@/libs/memorizeFn';
import TempTable from './components/Table';
import TemplateModal from "./components/TemplateModal";
import styles from './index.module.scss';
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import EmptyData from '@/assets/image/trade/scanPrintEmpty.png';
import { ProfitLogisticsCost<PERSON>uery<PERSON>pi, ProfitLogisticsTemplateDeleteApi, ProfitLogisticsCostInsertApi } from "@/apis/aftersale/costSetting";
// import { mockData } from "./mockData";
import { ProfitLogisticsCostQueryResponse } from "@/types/aftersale/costSetting";
import AddrCitySelect from "@/components-biz/AddrSelect/AddrCitySelect";
import PostFeeCalcModal from "./components/PostFeeCalcModal";
import OrderPostFeeCalcModal from "./components/OrderPostFeeCalcModal";

const ExpressTemplate = () => {
	const [templateModalVisible, setTemplateModalVisible] = useState(false);
	const [optType, setOptType] = useState("");
	const [editingTemplateIdList, setEditingTemplateIdList] = useState([]); // 正在编辑的模板
	const [savingTemplateIdList, setSavingTemplateIdList] = useState([]); // 正在保存的模板
	const [deletingTemplateIdList, setDeletingTemplateIdList] = useState([]); // 正在删除的模板
	const [disabledExpressCodeList, setDisabledExpressCodeList] = useState([]);
	const [editTemplateData, setEditTemplateData] = useState([]);
	const [expressDataSource, setExpressDataSource] = useState([]);
	const [templateList, setTemplateList] = useState({});
	const [expressSelectedTemplateListObj, setExpressSelectedTemplateListObj] = useState({});
	// const [showVirtualTable, setShowVirtualTable] = useState(false);
	const [postFeeCalcForm] = Form.useForm();
	// 修改状态定义
	const [showVirtualTableMap, setShowVirtualTableMap] = useState<Record<string, boolean>>({});
	const refObj = useRef<{ [key: string]: HTMLDivElement }>({}); 
	const { run: queryList, loading: queryLoadig } = useRequest(ProfitLogisticsCostQueryApi, {
		manual: true,
		onSuccess: (res: ProfitLogisticsCostQueryResponse["data"]) => {
			// setShowVirtualTable(false);
			// let _showVirtualTable = false;
			// res = mockData as any;
			// console.log(res, 'res');
			if (res?.templateList) {
				try {
					for (const key in res.templateList) {
						let count = 0;
						if (Object.prototype.hasOwnProperty.call(res.templateList, key)) {
							const item = res.templateList[key] as ProfitLogisticsCostQueryResponse["data"][];
							item.forEach(templateItem => {
								templateItem.detailList.forEach(detailItem => {
									const freightList = JSON.parse(detailItem.freightList);
									count += freightList.length ?? 0;
								});
							});
						}
						
						// 修改判断逻辑 ：之前只要有一个大于100的就全部显示虚拟表格，现在单独判断
						// 测试环境方便验证，线上为100，测试环境改为10
						if (count > 100) {
							setShowVirtualTableMap(prev => ({
								...prev,
								[key]: true
							}));
						}
					}
				} catch (error) {
					console.log(error);
				}
				setTemplateList(res.templateList);
			}
		},
		onError: () => {
			
		},

	});
	const popoverContent = (
		<div>
			<ol>
				<li>
					您只要设置好快递模板即可，系统将根据实际发货快递进行计算，未能匹配配送地区的，将按全国地区默认计算。
				</li>
				<li>自动合并物流单号一致的订单，均摊运费</li>
			</ol>
		</div>
	);
	const onOk = () => {
		setTemplateModalVisible(false);
		queryList({});
	};

	const onCancel = () => {
		setTemplateModalVisible(false);
	};

	const onAdd = () => {
		setTemplateModalVisible(true);
		setOptType("add");
		sendPoint(Pointer.设置_运费模板设置_新建);
	};

	const confirmDelete = useCallback((template) => {
		let _deletingTemplateIdList = cloneDeep(deletingTemplateIdList);
		_deletingTemplateIdList.push(template.templateId);
		setDeletingTemplateIdList(_deletingTemplateIdList);
		ProfitLogisticsTemplateDeleteApi({ templateId: template.templateId }).then(() => {
			message.success("模板已删除！");
			queryList({});
		}).finally(() => {
			_deletingTemplateIdList = _deletingTemplateIdList.filter(i => i !== template.templateId);
			setDeletingTemplateIdList(_deletingTemplateIdList);
		});
	}, [deletingTemplateIdList, queryList]);

	const onDelete = (template) => {
		sendPoint(Pointer.设置_运费模板设置_删除);
		Modal.confirm({
			title: '确定删除？',
			content: (
				<div>
					删除后可能会导致无法计算包裹的运费
				</div>
			),
			onOk: () => confirmDelete(template)
		});
	};

	const onEdit = (template) => {
		setOptType("edit");
		setEditTemplateData([template]);
		// setTemplateModalVisible(true);
		const _editingTemplateIdList = cloneDeep(editingTemplateIdList);
		_editingTemplateIdList.push(template.templateId);
		setEditingTemplateIdList(_editingTemplateIdList);
		sendPoint(Pointer.设置_运费模板设置_编辑);
	};

	// 创建防抖版本的保存函数
	const onSave = useMemo(() => {
		return debounce((template) => {
			const id = template.templateId;
			const logisticsId = template.logisticsId;
			const _savingTemplateIdList = cloneDeep(savingTemplateIdList);
			refObj?.current?.[id]?.getTemplateData()?.then(({
				templateData,
				needDeleteDetailIds,
				needDeletePrintTemplateIds,
			}) => {
				/**
				 * 如果选择了一个快递公司，并且没有选择快递模板，保存的时候需要去校验当前是否已经存在这样一条规则
				 */

				// 当前模板的详细信息
				const curExpressInfo = templateData[0];
				// 当前快递公司的所有模板
				const expressTemplateConfigInfo = templateList[curExpressInfo.logisticsId];
				// 当前模板是不是没有选择快递模板
				const noSelectedTemp = !curExpressInfo.printTemplateList || !curExpressInfo.printTemplateList.length;
				// 找出一开始未选择快递模板的那条规则
				const noSelectedTempRuleInfo = expressTemplateConfigInfo.filter(i => (!i.printTemplateList || isEmpty(i.printTemplateList)))[0];
				// 当前编辑的是不是一开始未选择快递模板的那条规则
				const curEditRuleIsOriginalEmptyTemplate = noSelectedTempRuleInfo?.templateId == curExpressInfo.templateId;
				if (expressTemplateConfigInfo && noSelectedTemp && !curEditRuleIsOriginalEmptyTemplate) {
					const isEmptyTemplate = expressTemplateConfigInfo.some(i => (!i.printTemplateList || isEmpty(i.printTemplateList)));
					 if (isEmptyTemplate) {
						Modal.confirm({
							title: "系统提示",
							content: `【${getExpressInfoByExCode(curExpressInfo.logisticsId)?.exName}】已存在未设置快递模版的运费规则，请选择模版后再保存`,
							okText: "我知道了",
							cancelButtonProps: { hidden: true }
						 });
						 return;
					 }
				}
				_savingTemplateIdList.push(id);
				setSavingTemplateIdList(_savingTemplateIdList);
				const params = {
					integrationTemplate: JSON.stringify(templateData),
					needDeleteDetailIds: needDeleteDetailIds.join(','),
					needDeletePrintTemplateIds: needDeletePrintTemplateIds.join(','),
					id
				};
				ProfitLogisticsCostInsertApi(params).then(() => {
					const _templateList = cloneDeep(templateList);
					const _editingTemplateIdList = cloneDeep(editingTemplateIdList).filter(i => i !== template.templateId);
					// 保存成功以后，页面必须重新刷新，不然二次保存快递模板的时候，因为没有id给后端，接口会报错
					queryList({});
					setTemplateList(_templateList);
					setEditingTemplateIdList(_editingTemplateIdList);
				}).finally(() => {
					const _savingTemplateIdList = savingTemplateIdList.filter(i => i !== id);
					setSavingTemplateIdList(cloneDeep(_savingTemplateIdList));
				});
			});
		}, 300); // 300ms 防抖延迟
	}, [savingTemplateIdList, templateList, editingTemplateIdList]);

	const onCancelSave = (template) => {
		const _editingTemplateIdList = cloneDeep(editingTemplateIdList).filter(i => i !== template.templateId);
		setEditingTemplateIdList(_editingTemplateIdList);
	};

	const getTableLoadinng = useCallback((template) => {
		if (deletingTemplateIdList.includes(template.templateId)) {
			return {
				loading: {
					spinning: true,
					tip: "正在删除..."
				}
			};
		}
		if (savingTemplateIdList.includes(template.templateId)) {
			return {
				loading: {
					spinning: true,
					tip: "正在保存..."
				}
			};
		}
		
		return {};

	}, [deletingTemplateIdList, savingTemplateIdList]);
	// 通过快递公司code拿到快递公司信息
	const getExpressInfoByExCode = (exCode) => {
		const expressInfo = expressDataSource.find(express => express.exCode == exCode);
		return expressInfo;
	};

	const renderTemplateList = useMemo(() => {
		const tempTableList = [];
		// 先根据修改时间排序
		// const _templateList = templateList.sort((last, next) => {
		// 	if (dayjs(last.modified).isAfter(dayjs(next.modified))) {
		// 		return -1;
		// 	}
		// 	return 1;
		// });
		for (const key in templateList) {
			const curExpressInfo = getExpressInfoByExCode(key);
			const templateSortList = templateList[key]?.sort((last, next) => {
				if (dayjs(last.modified).isAfter(dayjs(next.modified))) {
					return -1;
				}
				return 1;
			});
			const curExpressTemplateList = [];
			templateSortList.forEach(template => {
				const id = template.templateId;
				curExpressTemplateList.push(
					<div className={ styles["template-container"] }>
						<div className="header">
							<div>
								<span>最后编辑时间：</span>
								<span>{dayjs(template.modified).format("YYYY-MM-DD HH:mm:ss")}</span>
							</div>
							<div className="opt">
								<div className={ styles.save } hidden={ !editingTemplateIdList.includes(id) }>
									<Button
										type="primary"
										size="small"
										loading={ savingTemplateIdList.includes(id) }
										icon={ <SaveOutlined /> }
										onClick={ () => onSave(template) }
									>
										确认保存
									</Button>
								</div>
								<div className={ styles.cancel } hidden={ !editingTemplateIdList.includes(id) }>
									<Button
										size="small"
										icon={ <CloseCircleOutlined /> }
										onClick={ () => onCancelSave(template) } 
									>
										取消保存
									</Button>
								</div>
								<PostFeeCalcModal templateId={ id } />
								<div hidden={ editingTemplateIdList.includes(id) } className={ styles.edit } onClick={ () => onEdit(template) }>
									<FormOutlined />&nbsp;编辑
								</div>
								<div hidden={ editingTemplateIdList.includes(id) }>
									<span className={ styles.delete } onClick={ () => onDelete(template) }><DeleteOutlined />&nbsp;删除</span>
								</div>
							</div>
						</div>
						<div className="body">
							<TempTable
								showVirtualTable={ showVirtualTableMap[key] }
								expressSelectedTemplateListObj={ expressSelectedTemplateListObj }
								type={ editingTemplateIdList.includes(template.templateId) ? "edit" : "read" }
								data={ [template] }
								disabledExpressCodeList={ disabledExpressCodeList }
								cRef={ ref => refObj.current[template.templateId] = ref }
								tableKey={ key }
								{
									...getTableLoadinng(template)
								}
							/>
						</div>
					</div>
				);
			});
			tempTableList.push(
				<Collapse
					ghost
					key={ key }
					className={ styles.collapse }
					expandIcon={ ({ isActive }) => {
						return (
							<div className="r-flex r-ai-c" style={ { display: "flex", color: isActive ? "#1890ff" : "#000" } }>
								<CaretRightOutlined size={ 20 } className="r-fs-20" rotate={ isActive ? 90 : 0 } />
								<span className="r-bold r-fs-18 r-ml-6">{curExpressInfo?.exName}</span>
								{/* <span className="r-ml-6 kdzs-link-text">{isActive ? "收起" : "展开"}</span> */}
							</div>
						);
					} }
				>
					<Collapse.Panel
						header={ null }
						key="1"
					>
						<div>{curExpressTemplateList}</div>
					</Collapse.Panel>
				</Collapse>
			);
		}

		
		if (tempTableList.length) {
			return tempTableList;
		}
		return (
			<div className={ styles["empty-container"] }>
				<div>
					<img src={ EmptyData } alt="empty" />
					<div className="desc">暂无运费模板</div>
				</div>
				
			</div>
		);
		
	}, [templateList, onDelete, deletingTemplateIdList, editingTemplateIdList, disabledExpressCodeList, getTableLoadinng]);

	// 找出每个快递公司所选择的快递模板列表
	const handleSelectedTemplateList = () => {
		const expressSelectedTemplateListObj = {};
		for (const key in templateList) {
			const freightTemplateList = templateList[key];
			const selectedTemplateList = [];
			freightTemplateList.forEach(freightTemplate => {
				freightTemplate.printTemplateList.forEach(template => {
					selectedTemplateList.push(template.printTemplateId);
				});
			});
			expressSelectedTemplateListObj[key] = selectedTemplateList;
		}
		setExpressSelectedTemplateListObj(expressSelectedTemplateListObj);

	};
	
	// 找出已选择的快递公司
	const handleSelectedExpressList = () => {
		setDisabledExpressCodeList(Object.keys(templateList));
	};

	useEffect(() => {
		// handleSelectedExpressList();
		handleSelectedTemplateList();

	}, [templateList]);

	useEffect(() => {
		queryList({});
		memoFn.getExpressList().then(res => {
			setExpressDataSource(res || []);
		});
	}, []);
	return (
		<div>
			{/* header */}
			<div className={ styles.header } >
				<div className="r-flex r-ai-c">
					<Button type="primary" onClick={ () => onAdd() }>新建运费模板</Button>
					<div className="r-ml-6">
						<span>模板说明</span>
						<Popover
							overlayClassName={ styles["popover-overlay"] }
							placement="right"
							content={
								popoverContent
							}
						>
							<QuestionCircleOutlined className="r-ml-4" />
						</Popover>
					</div>
				</div>
				<div><OrderPostFeeCalcModal /></div>
				
			</div>
			{/* content */}
			<div className={ styles.content } >
				<Spin spinning={ queryLoadig } tip={ queryLoadig ? "正在加载..." : "" }>
					{ renderTemplateList }
				</Spin>
			</div>
			{
				templateModalVisible && (
					<TemplateModal
						templateListObj={ templateList }
						type={ optType }
						onOk={ onOk }
						onCancel={ onCancel }
						data={ editTemplateData }
						expressSelectedTemplateListObj={ expressSelectedTemplateListObj }
						disabledExpressCodeList={ disabledExpressCodeList }
					/>
				)
			}
			
		</div>
	);
};
export default observer(ExpressTemplate);