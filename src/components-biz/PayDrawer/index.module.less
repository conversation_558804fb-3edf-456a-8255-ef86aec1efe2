.payDrawer {
	:global {
		.ant-drawer-body {
			padding       : 24px;
			display       : flex;
			flex-direction: column;
			overflow-y    : auto;
		}
	}

	.stepsContainer {
		padding      : 24px 198px;
		background   : #f5f5f5;
		margin-bottom: 24px;

		:global {
			.ant-steps-item-process>.ant-steps-item-container>.ant-steps-item-tail::after {
				background: rgba(0, 0, 0, 0.15);
			}

			.ant-steps-item-finish>.ant-steps-item-container>.ant-steps-item-tail::after {
				background-color: #52c41a;
			}

			.ant-steps-item-finish .ant-steps-item-icon>.ant-steps-icon {
				color: #52c41a
			}

			.ant-steps-item-process>.ant-steps-item-container>.ant-steps-item-icon {
				background-color: #52c41a;
			}

			.ant-steps-item-finish .ant-steps-item-icon,
			.ant-steps-item-process .ant-steps-item-icon {
				border-color: #52c41a;
			}
		}

	}

	.content {
		position: relative;
		flex    : 1;

		.contentSlide {
			position: absolute;
			top     : 0;
			left    : 0;
			width   : 100%;
			height  : 100%;

			&.slideIn {
				animation: slideInRight 0.3s ease-out forwards;
			}

			&.slideOut {
				animation: slideOutLeft 0.3s ease-out forwards;
			}
		}
	}

	.orderInfo {
		.orderTable {
			margin-bottom: 24px;

			.table {
				width          : 100%;
				border-collapse: collapse;
				border         : 1px solid #f0f0f0;

				th,
				td {
					padding      : 8px;
					text-align   : left;
					border-bottom: 1px solid #f0f0f0;
					border-left  : 1px solid #f0f0f0;
				}

				th {
					background : #fafafa;
					font-weight: 500;
					color      : #262626;
				}

				td {
					color: #595959;
				}
			}
		}

		.agreement {
			margin-bottom: 24px;

			.checkbox {
				margin-right: 8px;
			}

			.link {
				color          : #1890ff;
				text-decoration: none;
				padding        : 0;

				&:hover {
					text-decoration: underline;
				}
			}
		}

		.footer {
			display    : flex;
			align-items: center;
			position   : absolute;
			bottom     : -22px;
			left       : 0;
			right      : 0;
			padding    : 0 24px;
			background : #FFFFFF;
			box-shadow : inset 0px 1px 0px 0px #F0F0F0;
			height     : 52px;
			line-height: 52px;
			margin     : 0 -24px;

			.totalPrice {
				font-size   : 16px;
				margin-right: 16px;

				.label {
					color: #666;
				}

				.price {
					color      : #ff4d4f;
					font-size  : 18px;
					font-weight: 500;
					margin-left: 8px;
				}
			}
		}
	}

	.paymentInfo {
		text-align: center;

		.info {
			text-align   : left;
			margin-bottom: 32px;

			.infoItem {
				margin-bottom: 16px;
				font-size    : 14px;
				color        : #666;

				.label {
					display: inline-block;
					width  : 80px;
					color  : #262626;
				}
			}
		}

		.qrcodeWrap {
			align-items: center;
			background : #f5f5f5;
			display    : flex;
			width      : 100%;
			padding    : 64px;
		}

		.qrCode {

			width       : 166px;
			height      : 166px;
			margin-right: 60px;

		}

		.completeBtn {
			background   : #ff7a00;
			border       : none;
			color        : white;
			padding      : 8px 24px;
			border-radius: 4px;
			cursor       : pointer;
			font-size    : 14px;

			&:hover {
				background: #e66a00;
			}
		}
	}

	.successContent {
		text-align: center;
		padding   : 60px 0;

		.successIcon {
			width          : 80px;
			height         : 80px;
			border-radius  : 50%;
			background     : #52c41a;
			color          : white;
			display        : flex;
			align-items    : center;
			justify-content: center;
			margin         : 0 auto 24px;
			font-size      : 40px;
		}

		.successTitle {
			font-size    : 20px;
			color        : #262626;
			margin-bottom: 24px;
			font-weight  : 500;
		}

		.backBtn {
			background   : transparent;
			border       : 1px solid #ff7a00;
			color        : #ff7a00;
			padding      : 8px 24px;
			border-radius: 4px;
			cursor       : pointer;
			font-size    : 14px;

			&:hover {
				background: #fff7e6;
			}
		}
	}
}

.overView {
	.icon {
		width       : 260px;
		height      : 190px;
		margin-right: 24px;
	}

	.title {
		font-size     : 20px;
		font-weight   : 500;
		line-height   : 30px;
		letter-spacing: 0px;
		color         : rgba(0, 0, 0, 0.85);
	}

	.subTitle {
		font-size     : 14px;
		font-weight   : normal;
		line-height   : 22px;
		letter-spacing: 0px;
		color         : rgba(0, 0, 0, 0.65);
		margin-top    : 8px;
	}

	.priceWrap {
		height        : 62px;
		line-height   : 62px;
		display       : flex;
		flex-direction: row;
		background    : #FAFAFA;
		margin-top    : 16px;
		color         : rgba(0, 0, 0, 0.85);
		font-size     : 14px;
		padding-left  : 16px;

		.price {
			font-weight : 500;
			color       : #FF0000;
			margin-right: 4px;
			margin-left : 16px;
		}

		.priceValue {
			font-size  : 20px;
			font-weight: 600;
			color      : #FF0000;
		}
	}

	.detailIcon {
		width: 100%;
	}

	.versionWrap {
		display    : flex;
		align-items: center;
		font-size  : 14px;
		margin-top : 16px;
		margin-left: 16px;

		.versionTitle {
			color       : rgba(0, 0, 0, 0.65);
			margin-right: 16px;
		}

		.versionActive,
		.version {
			padding      : 5px 12px;
			border       : 1px solid rgba(0, 0, 0, 0.15);
			color        : rgba(0, 0, 0, 0.85);
			margin-right : 8px;
			cursor       : pointer;
			transition   : all 0.3s ease-in-out;
			border-radius: 4px;
		}

		.versionActive {
			border  : 1px solid #FD8204;
			position: relative;
			color   : #FD8204;

			&::after {
				content            : ' ';
				background-image   : url('../../assets/image/pay/yes.png');
				background-size    : 100% 100%;
				background-repeat  : no-repeat;
				background-position: center;
				width              : 16px;
				height             : 16px;
				position           : absolute;
				bottom             : 0;
				right              : 0;
				z-index            : 1;
			}

		}
	}

}

@keyframes slideInRight {
	from {
		transform: translateX(100%);
		opacity  : 0;
	}

	to {
		transform: translateX(0);
		opacity  : 1;
	}
}

@keyframes slideOutLeft {
	from {
		transform: translateX(0);
		opacity  : 1;
	}

	to {
		transform: translateX(-100%);
		opacity  : 0;
	}
}