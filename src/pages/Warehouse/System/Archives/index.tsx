import React, {
	useCallback,
	useEffect,
	useMemo,
	useRef,
	useState,
} from "react";
import {
	Button,
	Checkbox,
	Tooltip,
	Dropdown,
	Menu,
	Modal,
	Select,
	Radio,
	Switch,
	Popover,
	Drawer,
} from "antd";
import { useHistory } from 'react-router-dom';
import { useForm } from "antd/es/form/Form";
import { Resizable } from "react-resizable";
import dayjs, { Dayjs } from "dayjs";
import cs from "classnames";
import {
	CaretRightOutlined,
	DownOutlined,
	DownloadOutlined,
	FileExcelOutlined,
	SettingOutlined,
	UpOutlined,
} from "@ant-design/icons";
import _, { cloneDeep, isEqual, debounce } from "lodash";
import { useLocation } from "react-router-dom";
import { observer } from "mobx-react";
import { toJS } from "mobx";
import { CheckboxChangeEvent } from "antd/lib/checkbox";
import { useStores } from "@/stores/tool";
import SearchTable from "@/components/SearchTableVirtual";
import { SearchTableRefProps } from "@/components/SearchTable/SearchTable";
import { FormItemConfig } from "@/components/SearchTable/FormWidthRow";
import useGetState from '@/utils/hooks/useGetState';
import PrintCenter from "@/print/index";
import {
	TradeDictInsertDictApi,
	TradeDictQueryDictApi,
} from "@/apis/trade/search";
import {
	ItemAsyncGetProgressApi,
	ItemEditSysItemAliasWithBatchApi,
	ItemSysItemEditSysItemAliasdApi,
	ItemSysItemSaveApi,
	ItemSysItemUpdateSysSkuInfoWithBatchApi,
	ItemSysSkuBatchChangeSyncStockFlagApi,
	ItemSysSkuEditSysSkuAliasdApi,
	SysItemGetSysItemListApi,
	SysItemGetSysSkuListApi,
} from "@/apis/warehouse/system";
import NormalLayout from "@/components-biz/layouts/NormalLayout";
import { BrandSelect } from "@/components-biz/Product/Brand";
import GroupPanel from "@/components-biz/Product/Group/GroupPanel";
import ProgressModal from "@/components-biz/ProgressModal";
import WaresInfo from "@/components-biz/WaresInfo";
import BrandEditorModal from "@/components-biz/Product/Brand/EditorModal";
import Collapse from "@/components/Collapse";
import KdzsDateRangePicker1 from "@/components/DateRangeComp/kdzsRangePicker1";
import Icon from "@/components/Icon";
import Input from "@/components/Input/InputSearch";
import InputMulti from "@/components/Input/InputMulti";
import message from "@/components/message";
import { local } from "@/libs/db";
import { downloadCenter } from "@/pages/Index/DownloadCenter/utils";
import {
	autoSearch,
	weightUnit,
} from "@/pages/Index/Settings/System/constants";
import { BQ_PRINT_TYPE } from "@/pages/Trade/constants";
import { spArr } from "@/pages/Trade/utils";
import EditText from "@/pages/Warehouse/System/Archives/components/EditText";
import { tradeStore } from "@/stores";
import userStore from "@/stores/user";
import warehouseStore from "@/stores/warehouse";
import brandStore from "@/stores/warehouse/Brand";
import groupStore, {
	ArchivesType,
	IColumnDictType,
	IQueryDictType,
} from "@/stores/warehouse/Group";
import { ModulesFunctionEnum } from "@/types/schemas/setting/download";
import { ItemClassify } from "@/types/schemas/warehouse/group";
import {
	ItemSysItemEditSysItemAliasRequest,
	ItemSysItemSaveRequest,
	ItemSysSkuEditSysSkuAliasRequest,
	SysItem,
	SysItemGetSysItemListRequest,
	SysItemGetSysItemListResponse,
} from "@/types/schemas/warehouse/system";

import s from "./index.module.scss";
import { AutoCalculate } from "@/utils/enum/productManage";
import CreateSystemModal from "./components/CreateSystem/modal";
import SysSkuLogs from "./components/SysSkuLogs/index";

import ImportPlatItemModal from "./components/ImportPlatItem/modal";
import CreateSystem from "./components/CreateSystem";
import EditSystemSingle from "./components/EditSystemSingle";
import sysDeleteHelper from "./components/SysDeleteHelper";
import UpdateArchives from "./components/UpdateArchives";
import { cloneSys } from "./components/CreateSystem/helper";
import Pointer from "@/utils/pointTrack/constants";
import BatchImportModal from "./components/BatchImportModal";
import CombinedModal from "./components/CombinedModal";
import SeeModal from "../CombinedGoods/components/seeGoodsModal";
import { avoidRepeatReq, escapeRegExp, isValidTableRowClick } from "@/utils/util";
import sendPoint from "@/utils/pointTrack/sendPoint";
import BatchUpdateSupplierModal from "./components/BatchUpdateSupplierModal";
import ImportSystemModal from "./components/ImportSystemModal";
import ColSortSetting from "./components/ColSortSetting";
import ColSortSettingDrawer from "./components/ColSortSettingDrawer";
import { batchEditButtons, batchEditFuncName,  defaultFormFieldList, ItemPropertyEnum, ItemPropertyOptions} from "./constant";
import BatchEditModal, {
	addPosMap,
	getNewContent,
	nameMap,
	eventBus,
} from "../PlatformGoods/components/BatchEditModal";
import { GoodsPrintType } from "../../constants";
import UpdateGroup from "./components/UpdateGroup";
import { belongStartAndEnd, compareRowId } from "../../utils";
import FieldsPermissionCheck, {
	FieldsPermissionCheckTypeEnum,
	FieldsPermissionEnum,
} from "@/utils/permissionCheck/fieldsPermissionCheck";
import CustomAttrRelationEditModal from "./components/CustomAttrRelationEditModal";
import FunctionPermissionCheck, {
	FunctionPermissionEnum,
} from "@/utils/permissionCheck/functionPermissionCheck";
import ImportCombinedSystemModal from "./components/ImportCombinedSystemModal";
import useExpand from '@/utils/hooks/useCacheExtand';
import BatchFillPlatform from './components/BatchFillPlatform';
import {formatWeightDisplay} from '@/components/Input/InputNumber/WeightInput';
import SearchSetting from './components/SearchSetting';
import StorageLocationModal from './components/StorageLocationModal';

const Option = Select.Option;

const ResizableTitle = (props) => {
	const {
		onResize,
		onResizeStop,
		onResizeStart,
		width,
		minWidth = 50,
		maxWidth = 300,
		dragsty,
		...restProps
	} = props;
	return props.title ? (
		<Resizable
			width={width}
			height={0}
			onResize={onResize}
			onResizeStop={onResizeStop}
			onResizeStart={onResizeStart}
			minConstraints={[minWidth, 0]}
			maxConstraints={[maxWidth, 0]}
			handle={<span className="react-resizable-handle" style={dragsty} />}
		>
			<th {...restProps} />
		</Resizable>
	) : (
		<th {...props} />
	);
};

function determineFilters(filter) {
	let useBracketsFilter = false;
	let useCommaFilter = false;
	let useParenthesisFilter = false;

	// 如果 filter 数组为空，返回两个 false，不启用任何过滤
	if (!filter || filter.length === 0) {
		return { useBracketsFilter, useCommaFilter };
	}

	if (filter.includes("1")) {
		useBracketsFilter = true; // 如果 filter 中包含 '1'，启用括号过滤
	}
	if (filter.includes("2")) {
		useCommaFilter = true; // 如果 filter 中包含 '2'，启用逗号过滤
	}
	if (filter.includes("3")) {
		useParenthesisFilter = true; // 如果 filter 中包含 '3'，启用括号过滤
	}

	return { useBracketsFilter, useCommaFilter, useParenthesisFilter };
}

function filterText(text, filter) {
	// 通过工具函数获取过滤规则
	const { useBracketsFilter, useCommaFilter, useParenthesisFilter } = determineFilters(filter);

	// 如果过滤规则都为 false，直接返回原文本
	if (!useBracketsFilter && !useCommaFilter && !useParenthesisFilter) {
		return text;
	}

	// 过滤中英文逗号的正则
	const commaRegex = /[,，]/g;

	// 递归过滤嵌套的【】
	function removeBrackets(content) {
		const bracketsRegex = /【[^【】]*】/g; // 匹配最内层的完整括号对
		while (bracketsRegex.test(content)) {
			content = content.replace(bracketsRegex, ""); // 逐层移除最内层的括号对
		}
		return content;
	}

	function removeParenthesis(content) {
		// 处理嵌套括号：从最内层开始，逐层移除
		let hasChanged = true;
		while (hasChanged) {
			hasChanged = false;
			// 匹配最内层的括号对（不包含其他括号）
			const innerParenthesisRegex = /[（(]([^（）()]*)[）)]/g;
			content = content.replace(innerParenthesisRegex, (match, innerContent) => {
				// 如果内部内容不包含任何括号，则删除整个括号对
				if (!/[（(）)]/.test(innerContent)) {
					hasChanged = true;
					return '';
				}
				return match;
			});
		}
		return content;
	}

	// 过滤【】及其中内容
	if (useBracketsFilter) {
		text = removeBrackets(text);
	}

	// 过滤中英文逗号
	if (useCommaFilter) {
		text = text.replace(commaRegex, "");
	}

	// 过滤（）及其中内容
	if (useParenthesisFilter) {
		text = removeParenthesis(text);
	}

	return text;
}

enum Idefault {
	single = "sysItemRecordColumnConfigDefaultValue",
	singleSku = "sysItemRecordSkuColumnConfigDefaultValue",
	combined = "combinationSysItemRecordColumnConfigDefaultValue",
}
enum QueryDefault {
	single = "sysItemRecordSelectColumnConfigDefaultValue", // 系统货品默认查询条件
	singleSku = "sysItemRecordSkuSelectColumnConfigDefaultValue",
}
export enum PrintType {
	吊牌 = "dpd",
	条码 = "tmd",
	四季星座 = "sjxz",
	商品分类设置 = "spfl",
	货品打印设置 = "hpdy",
	子货品吊牌 = "zhpdp",
	子货品条码 = "zhptm",
}
export enum ArchivesTypeEnum {
	组合 = "combined",
	单品 = "single",
	单品规格 = "singleSku",
}
enum stockSyncEnum {
	"批量关闭",
	"批量开启",
}

const relationItemOption = [
	{ label: "全部", value: null },
	{ label: "有关联", value: true },
	{ label: "未关联", value: false },
];

export enum 查询排序 {
	不排序 = "",
	升序 = "asc",
	降序 = "desc"
}

const MAX_SHOW_COUNT: number = 3;
const { confirm } = Modal;
// 表单元素定义 下拉框？日期？ 多选框？ 多选按钮？

const groupCellRender = (row: any, index: number) => {
	return {
		style: row.rowSpan ? {} : { borderTop: 0 },
	};
};

const shouldCellUpdate = (recode: any, prevRecord: any) => {
	return !isEqual(recode, prevRecord);
};

// 添加或使用已有的 ALLOW_EMPTY_FIELDS 常量
const ALLOW_EMPTY_FIELDS = ['warehouseSlotName'];

export interface IBatchEditModalInfo {
	visible: boolean;
	type: string;
	autoWeight?: boolean;
	autoCostPrice?: boolean;
	attrKey?: string;
	attrName?: string;
}

const ARCHIVES_ACTIVEKEY = "LOCAL_ARCHIVES_ACTIVEKEY";

// 表格列定义，与AntdTable使用一致
const SystemArchives: React.FC = observer((props) => {
	const [form] = useForm();
	const ref = useRef<SearchTableRefProps>();
	const [classifyId, setClassifyId] = useState<string>("");
	const [editSystem, setEditSystem] = useState<SysItem>(null);
	const [loading, setLoading] = useState(false);
	const [dataSource, setDataSource] = useState<SysItem[]>([]);
	const [selectedRows, setSelectedRows] = useState<number[]>([]);
	const [importModalVisible, setImportModalVisible] = useState<boolean>(false);
	const [widthMap, setWidthMap] = useState({});
	const location = useLocation(); //  location
	const store: typeof groupStore = useStores("GroupStore");
	const { refreshSupplierList } = warehouseStore;
	const { archivesType, columnTypeConfig } = store; // combined 组合货品档案
	const [seeModalSource, setSeeModalSource] = useState(null); // 查看组合详情
	const [seeModalShow, setSeeModalShow] = useState(false); // 查看组合详情
	const [recordModalSysSkuId, setRecordModalSysSkuId] = useState(null); // 日志弹窗
	const [showBatchUpdateSupplier, setShowBatchUpdateSupplier] =
		useState(false);
	const [showBatchUpdateClassifyId, setShowBatchUpdateClassifyId] =
		useState(false);
	const [downloadExcelModalVisible, setDownloadExcelModalVisible] =
		useState(false); // 导出Excel
	const [exportType, setExportType] = useState(1); // 导出Excel类型
	const [batchToggleStatus, setBatchToggleStatus] = useState(false);
	const [exportLoading, setExportLoading] = useState(false);
	const [oldSelectObj, setOldSelectObj] = useState({
		index: "",
		checked: false,
	});
	const [importSysItemVisible, setImportSysItemVisible] = useState(false);
	const [importCombinedSysItem, setImportCombinedSysItem] = useState(false);
	const [queryList, setQueryList] = useState([]);
	const [activeKey, setActiveKey] = useState(String(local.get(ARCHIVES_ACTIVEKEY) ?? "0"));
	const [relationItem, setRelationItem] = useState(null);
	const [skuPicConfig, setSkuPicConfig] = useState({});
	const [skuPicPreviewConfig, setSkuPicPreviewConfig] = useState({});
	const [defaultQueryConfig, setDefaultQueryConfig] = useState([]);
	const [pageColumnConfig, setPageColumnConfig] = useState([]);
	const [sortQueryObj, setSortQueryObj, getSortQueryObj] = useGetState({
		orderBy: "",
		orderType: "",
	});
	const [storageLocationVisible, setStorageLocationVisible] = useState(false); // 货位管理弹框

	const tableRowDataSourceRef = useRef(null);
	const [isKg, setIsKg] = useState(false);
	const [batchEditModalInfo, setBatchEditModalInfo] =
		useState<IBatchEditModalInfo>({ visible: false, type: "" });
	const [assistantTableModalObj, setAssistantTableModalObj] = useState({
		visible: false,
		selectedList: [],
	});
	const storeConfig = toJS(columnTypeConfig);
	const columnConfig = storeConfig[archivesType];
	const history = useHistory();

	const [extands, { add, set, remove, clear }] = useExpand();

	// 在组件内部添加状态管理
	const [barCodeRepeatFlag, setBarCodeRepeatFlag] = useState(false);

	useEffect(() => {
		tradeStore.tradeListStore.keyDown();
		return () => {
			tradeStore.tradeListStore.cancelKeyDown();
		};
	}, []);

	useEffect(() => {
		userStore.getUserSetting().then((res) => {
			setIsKg(res?.weightUnit == weightUnit.显示kg);
		});
		userStore.getSystemSetting();
	}, []);

	useEffect(() => {
		PrintCenter.getBqTempList(BQ_PRINT_TYPE.吊牌);
	}, []);

	useEffect(() => {
		getSkuPicConfig();
		getSkuPicPreviewConfig();
	}, []);

	// 货位管理保存回调
	const handleStorageLocationOk = (formData) => {
		setStorageLocationVisible(false);

		// 重新请求第一页
		ref.current?.refresh();
	};

	const changeActiveKey = (value) => {
		setSortQueryObj({
			orderBy: "",
			orderType: ""
		})
		setActiveKey(value);
		local.set(ARCHIVES_ACTIVEKEY, value)
		ref.current.submit();
	}
	const changeRelationItem = (value) => {
		setSortQueryObj({
			orderBy: "",
			orderType: ""
		})
		setRelationItem(value);
		ref.current.submit();
	}

	// 查询项
	let FormFieldList: FormItemConfig[] = [
		{
			label: "",
			shouldUpdate: false,
			colProps: {
				span: 24
			},
			children: (
				<div className="r-pb-16 r-mb-8 r-relative" style={ { borderBottom: "1px solid #eee" } }>
					<Radio.Group defaultValue={ activeKey } value={ activeKey } onChange={ (e) => changeActiveKey(e.target.value) }>
						<Radio.Button value="0">系统货品</Radio.Button>
						<Radio.Button value="1">系统规格</Radio.Button>
					</Radio.Group>
					<Radio.Group className="r-ml-16" defaultValue={ relationItem } value={ relationItem } onChange={ (e) => changeRelationItem(e.target.value) }>
						<Radio.Button value={null}>全部</Radio.Button>
						<Radio.Button value={false}>未关联</Radio.Button>
						<Radio.Button value={true}>已关联</Radio.Button>
					</Radio.Group>
				</div>
			),
		},
		{
			name: "supplierName",
			label: "",
			className: "r-mb-8",
			children: <Input placeholder='供应商多个以","分隔' />,
		},
		{
			name: "market",
			label: "",
			className: "r-mb-8",
			children: <Input placeholder='市场多个以","分隔' />,
		},
		{
			name: "stall",
			label: "",
			className: "r-mb-8",
			children: <Input placeholder='档口多个以","分隔' />,
		},
		{
			name: "brandIds",
			className: "r-mb-8",
			// colProps: {
			// 	span: 4
			// },
			children: (
				<BrandSelect
					showArrow
					filterOption={(input, option) =>
						option.children["toLowerCase"]().indexOf(
							input.toLowerCase()
						) >= 0
					}
					mode="multiple"
					maxTagCount={3}
					maxTagTextLength={3}
					allowClear
					size="small"
					style={{ width: 160 }}
				/>
			),
		},
		{
			name: "sysItemAlias",
			label: "",
			className: "r-mb-8",
			children: <Input placeholder="货品简称" />,
		},
		{
			name: "outerId",
			label: "",
			className: "r-mb-8",
			children: <InputMulti placeholder="货品规格编码" size="small" maxInputNum={ 500 } style={{ width: 160 }} />,
		},
		{
			name: "sysSkuName",
			label: "",
			className: "r-mb-8",
			children: <Input placeholder="规格/规格别名" />,
		},
		{
			name: "created",
			label: "",
			className: "r-mb-8",
			children: (
				<KdzsDateRangePicker1
					style={{ width: "159px" }}
					format="YYYY-MM-DD HH:mm"
					placeholder={["创建时间起", "创建时间止"]}
					className={cs("r-w-full")}
				/>
			),
			colProps: {
				// span: 5
			},
		},
		{
			name: "relationItem",
			label: "",
			className: "r-mb-8",
			children: (
				<Select
					allowClear
					size="small"
					style={{ width: 160 }}
					placeholder="是否关联平台商品"
					defaultValue={undefined}
					options={relationItemOption}
				/>
			),
		},
		{
			name: "existSysSkuAlias",
			label: "",
			className: "r-mb-8",
			children: (
				<Select
					className={cs("r-w-full")}
					placeholder="是否设置规格别名"
					size="small"
					style={{ width: 160 }}
					allowClear
				>
					<Option value="" key={0}>
						全部
					</Option>
					<Option value key={1}>
						已设置规格别名
					</Option>
					<Option value={false} key={2}>
						未设置规格别名
					</Option>
				</Select>
			),
		},
		{
			name: "existWeight",
			label: "",
			className: "r-mb-8",
			children: (
				<Select
					className={cs("r-w-full")}
					placeholder="是否设置重量"
					size="small"
					style={{ width: 160 }}
					allowClear
				>
					<Option value="" key={0}>
						全部
					</Option>
					<Option value key={1}>
						已设置重量
					</Option>
					<Option value={false} key={2}>
						未设置重量
					</Option>
				</Select>
			),
		},
		{
			name: "existCostPrice",
			label: "",
			className: "r-mb-8",
			children: (
				<Select
					className={cs("r-w-full")}
					placeholder="是否设置成本价"
					size="small"
					style={{ width: 160 }}
					allowClear
				>
					<Option value="" key={0}>
						全部
					</Option>
					<Option value key={1}>
						已设置成本价
					</Option>
					<Option value={false} key={2}>
						未设置成本价
					</Option>
				</Select>
			),
		},
		{
			name: "existSupplier",
			label: "",
			className: "r-mb-8",
			children: (
				<Select
					className={cs("r-w-full")}
					placeholder="是否设置供应商"
					size="small"
					style={{ width: 160 }}
					allowClear
				>
					<Option value="" key={0}>
						全部
					</Option>
					<Option value key={1}>
						已设置供应商
					</Option>
					<Option value={false} key={2}>
						未设置供应商
					</Option>
				</Select>
			),
		},
		{
			name: "existSyncStockFlag",
			label: "",
			className: "r-mb-8",
			children: (
				<Select
					className={cs("r-w-full")}
					placeholder="是否开启库存同步"
					size="small"
					style={{ width: 160 }}
					allowClear
				>
					<Option value="" key={0}>
						全部
					</Option>
					<Option value key={1}>
						已开启库存同步
					</Option>
					<Option value={false} key={2}>
						未开启库存同步
					</Option>
				</Select>
			),
		},
	]

	const FormField = useMemo(() => {
		if (archivesType === ArchivesTypeEnum.组合) {
			const FormFieldListGroup = [...FormFieldList];
			FormFieldListGroup.splice(FormFieldList.findIndex(i => i.name === "sysSkuName") + 1, 0, {
				name: "itemNo",
				label: "",
				className: "r-mb-8",
				children: <Input placeholder='货号' size="small" allowClear style={{ width: 160 }}/>,
			})
			FormFieldListGroup.splice(0, 1);
			return FormFieldListGroup;
		}

		if(archivesType == ArchivesTypeEnum.单品 || archivesType == ArchivesTypeEnum.单品规格){
			FormFieldList = [
				...FormFieldList,
				{
					name: "warehouseSlotName",
					label: "",
					className: "r-mb-8",
					children: <Input placeholder='货位' size="small" allowClear style={{ width: 160 }}/>,
				},
				{
					name: "existWarehouseSlot",
					label: "",
					className: "r-mb-8",
					children: (
						<Select
							className={cs("r-w-full")}
							placeholder="是否设置货位"
							size="small"
							style={{ width: 160 }}
							allowClear
						>
							<Option value="" key={0}>
								全部
							</Option>
							<Option value={true} key={1}>
								已设置货位
							</Option>
							<Option value={false} key={2}>
								未设置货位
							</Option>
						</Select>
					),
				},
				{
					name: "property",
					label: "",
					className: "r-mb-8",
					children: (
					  <Select
						allowClear
						size="small"
						style={{ width: 160 }}
						placeholder="货品属性"
						options={ItemPropertyOptions}
					  />
					),
				},
				{
					name: "itemNo",
					label: "",
					className: "r-mb-8",
					children: <Input placeholder='货号' size="small" allowClear style={{ width: 160 }}/>,
				},
			]
		}
		const fieldObj = {};
		FormFieldList.forEach(item => {
			fieldObj[item.name] = item;
		});
		let _list = [FormFieldList[0]];
		const checkedQuery = queryList?.filter(i => i.ischecked);
		checkedQuery.forEach(item => {
			if (fieldObj[item.key]) {
				_list.push(fieldObj[item.key]);
			}
		});
		return _list;
	}, [archivesType, activeKey, queryList, relationItem]);

	// 展开更多货品
	const toggleList = (row) => {
		dataSource.forEach((i, index) => {
			if (i.sysItemId === row.sysItemId) {
				extands.includes(row.sysItemId)
					? remove(row.sysItemId)
					: add(row.sysItemId);
				i.collapseShow = !i.collapseShow;
				if (i.collapseShow) {
					i.isItemShow = true;
				} else if (index < MAX_SHOW_COUNT) {
					i.isItemShow = true;
				} else {
					i.isItemShow = false;
				}
			}
		});
		setDataSource([...dataSource]);
	};

	const batchToggleList = () => {
		sendPoint(Pointer.商品_货品档案_批量展开收起);
		dataSource.forEach((i) => {
			i.collapseShow = !batchToggleStatus;
		});
		const extandKeys = new Set(
			dataSource
				.filter((i) => i?.colSpan > 2)
				.map((i) => String(i.sysItemId))
		);
		batchToggleStatus ? clear() : set(Array.from(extandKeys));
		setDataSource([...dataSource]);
		setBatchToggleStatus(!batchToggleStatus);
	};

	const onSeeModalShow = (row: any) => {
		setSeeModalSource(row);
		setSeeModalShow(true);
	};
	const onSeeModalHide = () => {
		setSeeModalSource(null);
		setSeeModalShow(false);
	};

	const showEditModal = (row: SysItem) => {
		setEditSystem({ ...row, sysSkuList: _.cloneDeep(row.allSysSkuList) });
	};

	// 获取列配置
	const getColumnList = (type: ArchivesType) => {
		// const dataJS = toJS(store.columnTypeConfig);
		// if (!dataJS[type]?.userConfig) {
		avoidRepeatReq(TradeDictQueryDictApi, {
			userDictEnum: IColumnDictType[type],
		}).then((res) => {
			try {
				if (!res[Idefault[type]]) res[Idefault[type]] = "[]";
				if (!res.value) res.value = res[Idefault[type]];
				const data = {
					userConfig: JSON.parse(res.value).filter(
						(item) => item && item.name
					),
					defaultConfig: JSON.parse(res[Idefault[type]]).filter(
						(item) => item && item.name
					),
				};
				let hasSyncStockFlag = false;
				if (type == "combined") {
					data.userConfig?.forEach((i) => {
						if (["combined", "sysSkuAlias"].includes(i.key)) {
							i.isedit = true;
						}
						if (i.key === "syncStockFlag") hasSyncStockFlag = true;
					});
				}
				// 现在库存同步只在默认中，如果用户配置列配置中没有库存同步，前端手动添加一个
				// 为什么要这样改？因为后端不改，觉得要提sql
				data.userConfig?.forEach((i) => {
					if (i.key === "syncStockFlag") hasSyncStockFlag = true;
				});
				if (!hasSyncStockFlag) {
					data.userConfig.push({
						ischecked: true,
						isedit: true,
						key: "syncStockFlag",
						collection: 1,
						name: "库存同步",
					});
				}

				// 在单品和单品规格模式下添加货位配置 (临时)
				// if (type === "single" || type === "singleSku") {
				// 	let hasStorageLocation = data.userConfig?.some(i=>i.key == "warehouseSlotName");
				// 	if(!hasStorageLocation){
				// 		data.userConfig.push({
				// 			ischecked: true,
				// 			isedit: true,
				// 			key: "warehouseSlotName",
				// 			collection: 1,
				// 			name: "货位",
				// 			index: data.userConfig.length
				// 		});
				// 	}
				// }

				// data.userConfig = data.userConfig.filter(d=>d.key !== "warehouseSlotName"); // 删除

				console.log('%c [ 列配置 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', data)
				store.setColumnConfig(type, data);
				setPageColumnConfig(data);
			} catch (error) {
				console.log(error);
			}
		});
		// }
	};

	useEffect(() => {
		brandStore.brandInit();
		refreshSupplierList();
	}, [refreshSupplierList]);

	useEffect(() => {
		let type: ArchivesType = "single";
		if (location.pathname.includes("warehouse/combinedArchives")) {
			// 组合货品档案
			type = "combined";
		} else if (activeKey === '1') {
			type = "singleSku"
		}
		store.setArchivesType(type);
		getColumnList(type); // 获取列配置
		if (type != ArchivesTypeEnum.组合) {
			getQueryConfig(type); // 获取查询项
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [location, activeKey]);

	const selectedList = useMemo(() => {
		return dataSource.filter((item) => !item.isCollapse && item.isChecked);
	}, [dataSource]);

	// 接口查询、查询参数重装
	const fetchSystemList = (data: SysItemGetSysItemListRequest) => {
		sendPoint(Pointer.商品_组合货品_查询);
		setLoading(true);
		let [startCreated = "", endCreated = ""] = data.created || [];
		startCreated = startCreated
			? dayjs(startCreated).format("YYYY-MM-DD HH:mm:00")
			: startCreated;
		endCreated = endCreated
			? dayjs(endCreated).format("YYYY-MM-DD HH:mm:59")
			: endCreated;
		const {
			sysItemAlias,
			outerId,
			sysSkuName,
			pageNo,
			pageSize,
			brandIds,
			supplierName,
			market,
			stall,
			relationItem: relationItemQuery,
			existSysSkuAlias,
			existWeight,
			existCostPrice,
			existSupplier,
			existSyncStockFlag,
			warehouseSlotName,    // 货位
    		existWarehouseSlot,  // 是否设置货位
			property,
			itemNo,
		} = data;
		let search: SysItemGetSysItemListRequest = {
			classifyId,
			sysSkuOuterIdList: outerId?.trim()?.split(","),
			brandIds,
			sysSkuName,
			sysItemAlias,
			startCreated,
			endCreated,
			pageNo,
			pageSize,
			supplierName: supplierName
				?.trim()
				.replace(new RegExp("，", "g"), ","),
			market: market?.trim().replace(new RegExp("，", "g"), ","),
			stall: stall?.trim().replace(new RegExp("，", "g"), ","),
			isCombination: 0,
			relationItem: relationItem ?? relationItemQuery,
			existSysSkuAlias,
			existWeight,
			existCostPrice,
			existSupplier,
			existSyncStockFlag,
			property,
			itemNo,
			// 添加条形码重复参数
			barCodeRepeatFlag: barCodeRepeatFlag,
			...getSortQueryObj(),
		};

		if(archivesType !== ArchivesTypeEnum.组合){
			search = {
				...search,
				warehouseSlotName: warehouseSlotName?.trim(), // 货位
				existWarehouseSlot, // 是否设置货位
			}
		}

		if (archivesType == ArchivesTypeEnum.组合) {
			search["isCombination"] = 1;
		} else if (archivesType == ArchivesTypeEnum.单品规格) {
			search.viewType = 2;
			return SysItemGetSysSkuListApi(search);
		} else {
			search.viewType = 1;
		}
		return SysItemGetSysItemListApi(search);
	};

	// 刷新分组信息
	const groupRefresh = () => {
		groupStore.getGroupList(true);
	};

	// 当前条件刷新
	const systemRefresh = useCallback(() => {
		groupRefresh();
		ref?.current?.refresh?.();
	}, []);

	// 重置条件刷新
	const systemResetRefresh = useCallback(() => {
		sendPoint(Pointer.商品_组合货品_确定添加为组合货品);
		groupRefresh();
		setBarCodeRepeatFlag(false); // 重置条形码重复标志
		ref?.current?.reset?.();
	}, []);

	// 删除单个sku
	const onDeleteSku = async (row: SysItem) => {
		const {
			sysItemId,
			sysSkuList: [{ sysSkuId }],
		} = row;
		await sysDeleteHelper.deleteSku(row, {
			permission_source: isSingle
				? FunctionPermissionEnum.货品删除
				: FunctionPermissionEnum.组合货品删除,
			sysItemId,
			sysSkuId: sysSkuId as number,
		});
		systemRefresh();
	};

	// clone当前货品
	const cloneSku = async (row: SysItem) => {
		const sku = { ...row, sysSkuList: _.cloneDeep(row.allSysSkuList) };
		const cloneItem = await cloneSys(sku);
		console.log(cloneItem);
		cloneItem.sysSkuList.forEach((i) => {
			i.sort = 0;
		});
		setEditSystem(cloneItem);
	};

	const tableRowDataSource = useMemo(() => {
		if (archivesType === ArchivesTypeEnum.单品规格) {
			return dataSource;
		}
		return dataSource.filter(
			(i) =>
				i.isCollapse ||
				i.collapseIndex < MAX_SHOW_COUNT ||
				i.collapseShow
		);
	}, [dataSource]);

	// 基于sku拆分维度
	const responseAdapter = (data: SysItemGetSysItemListResponse["data"]) => {
		let sysSkuList: SysItem[] = [];
		let tableRowDataSourceIndex = 0;
		setLoading(false);
		if (archivesType === ArchivesTypeEnum.单品规格) {
			data.list.forEach((sku, index) => {
				sku?.customAttributesList?.forEach((item) => {
					item.value && (sku[item.key] = item.value);
				});
				sysSkuList.push({
					rowId: `${sku.id}_${index}`,
					groupId: `groupId${index}`,
					...sku,
					sysSkuList: [sku],
					allSysSkuList: [sku],
					_updateTime: new Date(),
					syncStockFlag: sku.syncStockFlag,
					collapseIndex: index,
					colSpan: 1,
					rowSpan: 1
				});
				tableRowDataSourceIndex++;
			});
		} else {
			data.list.forEach((item, i) => {
				let itemLen = item.sysSkuList.length;
				item.sysSkuList.forEach((sku, index) => {
					sku?.customAttributesList?.forEach((item) => {
						item.value && (sku[item.key] = item.value);
					});

					sysSkuList.push({
						rowId: `${i}_${index}`,
						groupId: `groupId${i}`,
						collapseIndex: index,
						...(index === 0 ? { rowSpan: item.sysSkuList.length } : {}),
						...(index === 0 ? { colSpan: itemLen } : {}),
						...item,
						sysSkuList: [sku],
						allSysSkuList: item.sysSkuList,
						_updateTime: new Date(),
						syncStockFlag: sku.syncStockFlag,
						collapseShow:
							item.collapseShow ??
							tableRowDataSourceRef.current?.[tableRowDataSourceIndex]
								?.collapseShow,
						isItemShow: index < MAX_SHOW_COUNT,
					});
					tableRowDataSourceIndex++;
				});
				if (item.sysSkuList.length > MAX_SHOW_COUNT) {
					const tmp = { ...sysSkuList[sysSkuList.length - 1] };
					sysSkuList.push({
						isCollapse: true,
						rowId: tmp.rowId + i,
						groupId: tmp.groupId,
						sysItemId: tmp.sysItemId,
						collapseShow: tmp.collapseShow,
					});
				}
			});
			if (extands?.length) {
				sysSkuList.forEach((i) => {
					if (extands.includes(String(i.sysItemId))) {
						i.collapseShow = true;
					}
				});
			}
		}
		setDataSource(sysSkuList);
		setSelectedRows([]);
		return {
			...data,
			list: sysSkuList,
		};
	};

	const selectedRowKeys = useMemo(() => {
		return dataSource
			.filter((d) => !d.isCollapse && selectedRows.includes(d.sysItemId))
			.reduce(function (a: any, b: any) {
				return a.concat(b.sysSkuList);
			}, [])
			.map((d: SysItem) => d.sysSkuId);
	}, [selectedRows, dataSource]);

	const dealShift = (record) => {
		const { rowId } = record;
		if (oldSelectObj.index) {
			const { index, checked } = oldSelectObj;
			const [startRowId, endRowId] = compareRowId(rowId, index);

			setDataSource((prev) => {
				prev.forEach((item) => {
					if (
						belongStartAndEnd({
							startRowId,
							endRowId,
							compareRowId: item.rowId,
						}) &&
						item.isItemShow
					) {
						item.isChecked = checked;
					}
				});
				return [...prev];
			});
		}
	};
	// group选择
	const onCheckedGroup = (e: CheckboxChangeEvent, record: any) => {
		setDataSource((prev) => {
			prev.forEach((item) => {
				if (item.groupId == record.groupId) {
					item.isChecked = e.target.checked;
				}
			});
			return [...prev];
		});
	};
	// 单选
	const onCheckedItem = (
		e: CheckboxChangeEvent,
		record: any,
		turnOver: boolean = false
	) => {
		const { isShiftDown } = tradeStore.tradeListStore;
		if (!isShiftDown) {
			let checked = false;
			setDataSource((prev) => {
				let prevIndex = prev.findIndex((d) => d.rowId === record.rowId);
				if (prevIndex > -1) {
					if (turnOver) {
						checked = !prev[prevIndex].isChecked;
						prev[prevIndex].isChecked = checked;
					} else {
						checked = e.target.checked;
						prev[prevIndex].isChecked = checked;
					}
				}
				return [...prev];
			});
			setOldSelectObj({
				index: record.rowId,
				checked,
			});
		} else {
			dealShift(record);
		}
	};
	// 全选
	const onCheckAllChange = (e: CheckboxChangeEvent) => {
		setDataSource((prev) => {
			prev.forEach((item) => {
				item.isChecked = e.target.checked;
			});
			return [...prev];
		});
	};

	const checkedGroup = useMemo(() => {
		let filterList = dataSource.filter((item) => !item.isCollapse);

		const tmp = {
			num: 0,
			checked: false,
			disabled: false,
			disNum: 0,
			indeterminate: false,
		};
		if (filterList.length == 0) return { group_all: tmp };
		const map = {
			group_all: { ...tmp, total: filterList.length },
		};
		let _selectedRowKeys = [];
		filterList.forEach((item) => {
			if (!map[item.groupId])
				map[item.groupId] = { ...tmp, total: item.colSpan };

			if (item.checkDisabled) {
				map[item.groupId].disNum += 1;
				map["group_all"].disNum += 1;
			}
			if (item.isChecked) {
				map[item.groupId].num++;
				map["group_all"].num++;
				_selectedRowKeys.push(archivesType != ArchivesTypeEnum.组合 && activeKey == "1" ? item.sysSkuId : item.sysItemId);
			}
		});
		_selectedRowKeys = Array.from(new Set(_selectedRowKeys));
		setSelectedRows(_selectedRowKeys);
		for (let key in map) {
			const { disNum, total, num } = map[key];
			if (disNum == total) {
				map[key].disabled = true;
			}
			if (num == total || (num > 0 && num + disNum == total)) {
				map[key].checked = true;
			}
			if (num > 0 && num + disNum < total) {
				map[key].indeterminate = true;
			}
		}
		return map;
	}, [dataSource]);

	const sysItemIdSysSkuIdsMap = useMemo(() => {
		let map = {};
		selectedList.forEach((item) => {
			const { sysItemId, groupId, sysSkuList } = item;
			// 全选的情况
			if (checkedGroup?.[groupId]?.checked && !map[sysItemId]) {
				map[sysItemId] = [];
			} else if (checkedGroup?.[groupId]?.indeterminate) {
				// 半选
				if (map[sysItemId]) {
					map[sysItemId].push(sysSkuList?.[0].sysSkuId);
				} else {
					map[sysItemId] = [sysSkuList?.[0].sysSkuId];
				}
			}
		});
		return map;
	}, [dataSource, selectedList, checkedGroup]);

	const groupChange = (group: ItemClassify) => {
		setClassifyId(String(group.classifyId));
		ref?.current?.submit();
	};
	const editSysItemAlias = async (
		sysItemAlias: string,
		sysItemId: number
	) => {
		const params: ItemSysItemEditSysItemAliasRequest = {
			sysItemId,
			sysItemAlias,
		};
		await ItemSysItemEditSysItemAliasdApi(params);
		// 更新单个货品信息
		dataSource.forEach((d) => {
			if (d.sysItemId === sysItemId) {
				d.sysItemAlias = sysItemAlias;
			}
		});
		setDataSource([...dataSource]);
		message.success("保存成功，商品变更资料会在几分钟内同步至订单中");
	};

	const editSysSkuAlias = async (
		sysSkuAlias: string,
		sysSkuId: number,
		sysItemId: number
	) => {
		const params: ItemSysSkuEditSysSkuAliasRequest = {
			sysSkuId,
			sysSkuAlias,
		};
		await ItemSysSkuEditSysSkuAliasdApi(params);
		// 更新单个货品sku信息
		dataSource.forEach((d) => {
			if (d.sysItemId === sysItemId && d.sysSkuList) {
				d.sysSkuList.forEach((s) => {
					if (s.sysSkuId === sysSkuId) {
						s.sysSkuAlias = sysSkuAlias;
					}
				});
			}
		});
		setDataSource([...dataSource]);
		message.success("保存成功，商品变更资料会在几分钟内同步至订单中");
	};
	const onBatchDeleteSystemItem = async () => {
		const { arr, ignoreStock } = getUpdateSysSkuInfos();
		// await sysDeleteHelper.deleteSys({ sysItemIds: selectedRows, ignoreStock: hasStock });
		await sysDeleteHelper.deleteSys({
			sysSkuDeleteInfos: arr,
			ignoreStock,
		});
		systemRefresh();
	};

	const getUpdateSysSkuInfos = () => {
		let hasStock = false;
		let arr = [];

		let _sysItemIdSysSkuIdsMap = {};
		selectedList.forEach((selectItem) => {
			let { sysItemId } = selectItem;
			let sysSkuIds = [];
			selectItem.sysSkuList.forEach((item) => {
				if (Number(item.salableItemStock || 0) > 0) {
					hasStock = true;
				}
				sysSkuIds.push(item.sysSkuId);
				if (!_sysItemIdSysSkuIdsMap[sysItemId]) {
					_sysItemIdSysSkuIdsMap[sysItemId] = sysSkuIds;
				} else {
					_sysItemIdSysSkuIdsMap[sysItemId] =
						_sysItemIdSysSkuIdsMap[sysItemId].concat(sysSkuIds);
				}
			});
		});
		for (let item in _sysItemIdSysSkuIdsMap) {
			arr.push({
				sysItemId: item,
				sysSkuIds: _sysItemIdSysSkuIdsMap[item],
			});
		}
		return {
			arr,
			ignoreStock: hasStock,
			selectedList: _sysItemIdSysSkuIdsMap,
		};
	};

	const groupPanelHeight = useMemo(() => {
		const calHeight = document.body.clientHeight - 393 - 44;
		// return { height: 'calc(100% - 82px)', overflow: 'overlay', minHeight };
		return { maxHeight: calHeight, overflow: "overlay" };
	}, []);

	// 导出excel文档
	const onDownloadExcel = async () => {
		let res = ref?.current?.getSearchParams?.();
		let [startCreated, endCreated] = res.created || [];
		startCreated = startCreated
			? dayjs(startCreated).format("YYYY-MM-DD HH:mm:00")
			: startCreated;
		endCreated = endCreated
			? dayjs(endCreated).format("YYYY-MM-DD HH:mm:59")
			: endCreated;
		let {
			sysItemAlias,
			outerId,
			sysSkuName,
			brandIds,
			supplierName,
			stall,
			market,
			relationItem: relationItemQuery,
			existSysSkuAlias,
			existWeight,
			existCostPrice,
			existSupplier,
			existSyncStockFlag,
			warehouseSlotName,    // 货位
    		existWarehouseSlot,  // 是否设置货位
			property,
		} = res;
		supplierName = supplierName?.trim().replace(new RegExp("，", "g"), ",");
		market = market?.trim().replace(new RegExp("，", "g"), ",");
		stall = stall?.trim().replace(new RegExp("，", "g"), ",");
		let params:any = {
			sysItemAlias,
			sysSkuOuterIdList: outerId?.trim()?.split(","),
			sysSkuName,
			brandIds,
			startCreated,
			endCreated,
			supplierName,
			stall,
			market,
			exportType,
			relationItem: relationItem ?? relationItemQuery,
			existSysSkuAlias,
			existWeight,
			existCostPrice,
			existSupplier,
			existSyncStockFlag,
			property,
		};
		const isSingle = archivesType != ArchivesTypeEnum.组合;

		if(isSingle){
			params = {
				...params,
				warehouseSlotName: warehouseSlotName?.trim(), // 货位
				existWarehouseSlot, // 是否设置货位
			}
		}

		if (exportType === 1) {
			// 导出已勾选
			if (selectedRows.length === 0) {
				message.warning("当前未勾选任何货品！");
				return;
			}
			if (isSingle && activeKey == "1") {
				params["chooseSysSkuIdList"] = selectedRows;
			} else {
				params["chooseSysItemIdList"] = selectedRows;
			}
		}
		// 判断是单品还是组合
		const downloadCenterOtherParams = isSingle
			? { fileName: "本地货品导出", module: activeKey === "0" ? ModulesFunctionEnum.货品档案 : ModulesFunctionEnum.货品规格档案 }
			: {
				fileName: "组合货品档案",
				module: ModulesFunctionEnum.组合货品档案,
			};
		params.isCombination = isSingle ? 0 : 1;
		setExportLoading(true);
		await downloadCenter({
			permission_source: isSingle
				? FunctionPermissionEnum.导出货品资料
				: FunctionPermissionEnum.导出组合货品,
			requestParams: params,
			...downloadCenterOtherParams,
		} as any);
		setExportLoading(false);
		onCloseDownloadExcelModal();
	};

	const onChangeExportType = (e: any) => {
		sendPoint(Pointer.货品_导出Excel类型);
		setExportType(e.target.value);
	};

	const onCloseDownloadExcelModal = () => {
		setExportType(1);
		setDownloadExcelModalVisible(false);
	};
	const checkAllListIsAuto = (type) => {
		let autoCalc = true;
		selectedList.forEach((item) => {
			item.sysSkuList.forEach((sys) => {
				if (!sys[type]) {
					autoCalc = false;
				}
			});
		});
		return autoCalc;
	};
	const handleBatchEditClick = {
		[batchEditFuncName.批量设置简称]: () => {
			if (archivesType != ArchivesTypeEnum.组合)
				sendPoint(Pointer.商品_货品档案_批量修改_批量设置简称);
			else sendPoint(Pointer.商品_组合货品档案_批量修改_批量设置简称);
			setBatchEditModalInfo({
				visible: true,
				type: batchEditFuncName.批量设置简称,
			});
		},
		[batchEditFuncName.批量设置别名]: () => {
			if (archivesType != ArchivesTypeEnum.组合)
				sendPoint(Pointer.商品_货品档案_批量修改_批量设置别名);
			else sendPoint(Pointer.商品_组合货品档案_批量修改_批量设置别名);
			setBatchEditModalInfo({
				visible: true,
				type: batchEditFuncName.批量设置别名,
			});
		},
		[batchEditFuncName.批量设置售价]: () => {
			if (archivesType != ArchivesTypeEnum.组合)
				sendPoint(Pointer.商品_货品档案_批量修改_批量设置售价);
			else sendPoint(Pointer.商品_组合货品档案_批量修改_批量设置售价);
			setBatchEditModalInfo({
				visible: true,
				type: batchEditFuncName.批量设置售价,
			});
		},
		[batchEditFuncName.批量设置成本价]: () => {
			if (archivesType != ArchivesTypeEnum.组合) {
				sendPoint(Pointer.商品_货品档案_批量修改_批量设置成本价);
				setBatchEditModalInfo({
					visible: true,
					type: batchEditFuncName.批量设置成本价,
				});
			} else {
				sendPoint(Pointer.商品_组合货品档案_批量修改_批量设置成本价);
				const autoCostPrice = checkAllListIsAuto(
					AutoCalculate.自动计算成本
				);
				setBatchEditModalInfo({
					visible: true,
					type: batchEditFuncName.批量设置组合成本价,
					autoCostPrice,
				});
			}
		},
		[batchEditFuncName.批量设置货位]: () => {
			if (archivesType != ArchivesTypeEnum.组合) {
				sendPoint(Pointer.商品_货品档案_点击_批量设置货位);
				setBatchEditModalInfo({
					visible: true,
					type: batchEditFuncName.批量设置货位,
				});
			}
		},
		[batchEditFuncName.批量设置重量]: () => {
			if (archivesType != ArchivesTypeEnum.组合) {
				sendPoint(Pointer.商品_货品档案_批量修改_批量设置重量);
				setBatchEditModalInfo({
					visible: true,
					type: batchEditFuncName.批量设置重量,
				});
			} else {
				sendPoint(Pointer.商品_组合货品档案_批量修改_批量设置重量);
				const autoWeight = checkAllListIsAuto(
					AutoCalculate.自动计算重量
				);
				setBatchEditModalInfo({
					visible: true,
					type: batchEditFuncName.批量设置组合重量,
					autoWeight,
				});
			}
		},
		[batchEditFuncName.批量设置供应商]: () => {
			if (archivesType != ArchivesTypeEnum.组合)
				sendPoint(Pointer.商品_货品档案_批量修改_批量设置供应商);
			else sendPoint(Pointer.商品_组合货品档案_批量修改_批量设置供应商);
			setShowBatchUpdateSupplier((prev) => !prev);
		},
		[batchEditFuncName.批量删除]: () => {
			if (archivesType != ArchivesTypeEnum.组合)
				sendPoint(Pointer.商品_货品档案_批量修改_批量删除);
			else sendPoint(Pointer.商品_组合货品档案_批量修改_批量删除);
			onBatchDeleteSystemItem();
		},
		[batchEditFuncName.批量设置分类]: () => {
			if (archivesType != ArchivesTypeEnum.组合)
				sendPoint(Pointer.商品_货品档案_批量修改_批量设置分类);
			else sendPoint(Pointer.商品_组合货品档案_批量修改_批量设置分类);
			setShowBatchUpdateClassifyId(true);
		},
		[batchEditFuncName.批量设置自定义属性]: ({ attrKey, attrName }) => {
			console.log(
				"batchEditFuncName.批量设置自定义属性：",
				attrKey,
				attrName
			);
			setBatchEditModalInfo({
				visible: true,
				type: batchEditFuncName.批量设置自定义属性,
				attrKey,
				attrName,
			});
		},
		[batchEditFuncName.批量设置颜色]: ({ attrKey, attrName }) => {
			setBatchEditModalInfo({
				visible: true,
				type: batchEditFuncName.批量设置颜色,
				attrKey: batchEditFuncName.批量设置颜色,
				attrName: "颜色",
			});
		},
		[batchEditFuncName.批量设置尺码]: ({ attrKey, attrName }) => {
			setBatchEditModalInfo({
				visible: true,
				type: batchEditFuncName.批量设置尺码,
				attrKey: batchEditFuncName.批量设置尺码,
				attrName: "尺码",
			});
		},
		[batchEditFuncName.批量设置货品规格编码]: ({ attrKey, attrName }) => {
			setBatchEditModalInfo({
				visible: true,
				type: batchEditFuncName.批量设置货品规格编码,
				attrKey: batchEditFuncName.批量设置货品规格编码,
				attrName: "货品规格编码",
			});
		},
	};

	const handleFillPlatform = async (data) => {
		if (!data) return; // 数据为空时提前返回

		try {
			setLoading(true);
			console.log(data);

			const { sysItemIds } = data;
			const { list } = await fetchSystemList(form.getFieldsValue());

			// 筛选出符合条件的 SKU 列表，并展平
			const updatedSkuList = list
				.filter(
					(item) =>
						sysItemIds.includes(item.sysItemId) && item.sysSkuList
				)
				.flatMap((item) =>
					item.sysSkuList.filter((sku) =>
						data[item.sysItemId]?.includes(sku.sysSkuId)
					)
				);

			console.log(updatedSkuList);

			// 更新数据源，检查是否存在 sysSkuList
			const clonedDataSource = dataSource.map((item) => {
				if (!item.sysSkuList) {
					return item; // 如果没有 sysSkuList，直接返回原项
				}

				return {
					...item,
					sysSkuList: item.sysSkuList.map((sku) => {
						// 查找是否有更新的 SKU 信息
						const updatedSku = updatedSkuList.find(
							(updatedItem) =>
								updatedItem.sysSkuId === sku.sysSkuId
						);
						return updatedSku || sku; // 如果有更新则替换，否则保持不变
					}),
				};
			});

			console.log(clonedDataSource);
			setDataSource(clonedDataSource); // 更新数据源状态
		} catch (error) {
			console.error("Error while fetching and updating data:", error);
			message.error("加载数据时出错，请稍后重试");
		} finally {
			setLoading(false); // 无论成功与否，都切换 loading 状态
		}
	};

	const batchEditClick = (func, params) => {
		if (!selectedList.length) {
			message.warning("请先选择货品");
			return;
		}

		handleBatchEditClick[func](params);
	};
	const MoreMenu = useMemo(() => {
		const btns = [...batchEditButtons].filter(btn => {
			// 如果是批量设置货位按钮且是组合类型，则过滤掉
			if (btn.func === batchEditFuncName.批量设置货位 && archivesType === ArchivesTypeEnum.组合) {
				return false;
			}
			return true;
		});
		const itemCustomAttributeList =
			userStore?.systemSetting?.itemCustomAttributeDTOList?.filter(
				(item) => item?.name
			);
		// 只有开启
		if (
			userStore?.systemSetting?.itemCustomAttribute === autoSearch.开启 &&
			itemCustomAttributeList?.length
		) {
			const _list = itemCustomAttributeList.map((item) => ({
				name: `批量设置${item.name}`,
				func: batchEditFuncName.批量设置自定义属性,
				key: item.key,
				originName: item.name,
			}));
			btns.splice(6, 0, ..._list);
		}
		return (
			<Menu className="r-flex r-fd-c">
				<div className={s["operate-container"]}>
					{btns.map((operate) => {
						let dom = (
							<div
								key={operate.name}
								className={`operate-item r-pointer ${s["operateItem"]}`}
								onClick={() => {
									batchEditClick(operate.func, {
										attrKey: operate.key,
										attrName: operate.originName,
									});
								}}
							>
								{operate.name}
							</div>
						);
						if (operate.fieldsPermission) {
							return (
								<FieldsPermissionCheck
									fieldsPermission={
										FieldsPermissionEnum.成本价
									}
								>
									{dom}
								</FieldsPermissionCheck>
							);
						} else {
							return dom;
						}
					})}
				</div>
			</Menu>
		);
	}, [userStore?.systemSetting, dataSource]);

	// 货品打印设置
	const handleOpenPrintMainPanel = () => {
		console.log("handleOpenPrintMainPanel");
		PrintCenter.showTemplateMain({ printType: GoodsPrintType.吊牌 });
	};
	const getGroupObj = (list) => {
		let obj = {};
		list.forEach((item) => {
			const { classifyId, classifyName } = item;
			obj[classifyId] = classifyName;
		});
		return obj;
	};
	// 货品打印
	const handlePrint = (type) => {
		const { groupList } = groupStore;
		const groupObj = getGroupObj(groupList);
		let printList = [];
		if (!selectedList.length) {
			message.warning("请选择要打印的货品");
			return;
		}

		selectedList.forEach((item) => {
			const { classifyId = "", brandName = "" } = item;
			item.sysSkuList?.forEach((sku) => {
				const {
					storageAddrProvince = "",
					storageAddrCity = "",
					storageAddrDistrict = "",
					storageAddr = "",
					itemNo = "",
					id = "",
					barCode = "",
					skuOuterId = "",
					//  brandName = "",
					outerId = "",
				} = sku;
				// 货品分类
				sku.classifyText = groupObj[classifyId] || "";
				// 货品品牌名
				sku.brandName = brandName;
				// 货品编码
				sku.outerId = outerId;
				// 供应商地址
				sku.supplierAddress = `${storageAddrProvince}${storageAddrCity}${storageAddrDistrict}${storageAddr}`;
				// 货品条形码 货品条形码；（如没有配置货号，不生成条形码）
				sku.sysBarCode = barCode ? `${barCode}` : "";
				// 货号条形码 货号；（如没有配置货号，不生成条形码）
				sku.itemNoBarCode = itemNo ? `${itemNo}` : "";
				// 货品规格编码条形码 货品规格编码
				sku.sysSkuBarCode = `${skuOuterId}`;

				// 打印子货品吊牌和条码
				if (type === PrintType.子货品吊牌 || type === PrintType.子货品条码) {
					sku?.groupCombinationList?.forEach((row) => {
						const { 
							storageAddrProvince = "",
							storageAddrCity = "",
							storageAddrDistrict = "",
							storageAddr = "",
							barCode = "",
							itemNo = "",
							skuOuterId = "",
							outerId = "",
							groupProportionNum = 1,
							classifyId='', // 货品分类
							brandName='', // 货品品牌名
						} = row;
						// 货品分类
						row.classifyText = groupObj[classifyId] || "";
						// 货品品牌名
						row.brandName = brandName;
						// 货品编码
						row.outerId = outerId;
						// 供应商地址
						row.supplierAddress = `${storageAddrProvince}${storageAddrCity}${storageAddrDistrict}${storageAddr}`;
						// 货品条形码 货品条形码；（如没有配置货号，不生成条形码）
						row.sysBarCode = barCode ? `${barCode}` : "";
						// 货号条形码 货号；（如没有配置货号，不生成条形码）
						row.itemNoBarCode = itemNo ? `${itemNo}` : "";
						// 货品规格编码条形码 货品规格编码
						row.sysSkuBarCode = `${skuOuterId}`;

						row.num = groupProportionNum;
					});
				}

				printList.push(sku);
			});
		});
		
		PrintCenter.batchPrintGoodsBarCodeOrTag({
			orderList: printList,
			printType: type,
		});
		console.log("printList:", printList);
	};
	const batchStockAsync = (val) => {
		if (!selectedList.length) {
			message.warning("请先选择货品");
			return;
		}
		if (val) {
			sendPoint(Pointer.商品_货品档案_批量开启);
		} else {
			sendPoint(Pointer.商品_货品档案_批量关闭);
		}
		setLoading(true);
		let sysSkuInfos = [];
		selectedList.forEach((item) => {
			const { sysSkuList } = item;
			sysSkuInfos.push({
				sysSkuId: sysSkuList[0].sysSkuId,
				syncStockFlag: val,
			});
		});
		console.log("sysSkuInfos:", sysSkuInfos);
		ItemSysSkuBatchChangeSyncStockFlagApi({
			sysSkuInfos,
			permission_source: isSingle
				? FunctionPermissionEnum.货品库存同步
				: FunctionPermissionEnum.组合货品库存同步,
		})
			.then((res) => {
				console.log(res);
				message.success("批量设置成功");
				systemRefresh();
			})
			.finally(() => {
				setLoading(false);
			});
	};

	
	const printMenu = (
		<Menu>
			<Menu.Item
				key={PrintType.吊牌}
				onClick={() => {
					sendPoint(Pointer.打印货品吊牌);
					handlePrint(PrintType.吊牌);
				}}
			>
				打印货品吊牌
			</Menu.Item>
			<Menu.Item
				key={PrintType.条码}
				onClick={() => {
					sendPoint(Pointer.打印货品条码);
					handlePrint(PrintType.条码);
				}}
			>
				打印货品条码
			</Menu.Item>
			{
				archivesType == ArchivesTypeEnum.组合 && (
					<>
						<Menu.Item
							key={PrintType.子货品吊牌}
							onClick={() => {
								sendPoint(Pointer.打印子货品吊牌);
								handlePrint(PrintType.子货品吊牌);
							}}
						>
							打印子货品吊牌
						</Menu.Item>
						<Menu.Item
							key={PrintType.子货品条码}
							onClick={() => {
								sendPoint(Pointer.打印子货品条码);
								handlePrint(PrintType.子货品条码);
							}}
						>
							打印子货品条码
						</Menu.Item>
					</>
				)
			}
		</Menu>
	)

	const stockSyncMenu = (
		<Menu>
			<Menu.Item
				key={stockSyncEnum.批量开启}
				onClick={() => {
					batchStockAsync(stockSyncEnum.批量开启);
				}}
			>
				批量开启
			</Menu.Item>
			<Menu.Item
				key={stockSyncEnum.批量关闭}
				onClick={() => {
					batchStockAsync(stockSyncEnum.批量关闭);
				}}
			>
				批量关闭
			</Menu.Item>
		</Menu>
	);
	const isSingle = archivesType != ArchivesTypeEnum.组合;
	const getExpandContext = () => {
		return (
			<div className="r-flex r-ai-c r-jc-sb r-w-full">
				<div className="r-flex">
					{archivesType != ArchivesTypeEnum.组合 ? (
						<>
							<FunctionPermissionCheck
								functionPermission={
									FunctionPermissionEnum.手工创建
								}
							>
								<CreateSystemModal
									code={FunctionPermissionEnum.手工创建}
									onSave={systemResetRefresh}
								>
									<Button
										disabled={
											!userStore.hasFunctionPermission(
												FunctionPermissionEnum.手工创建
											)
										}
										type="primary"
										className={cs("r-mr-8")}
										data-point={
											Pointer.商品_普通货品档案_手工创建_弹窗_手工新建弹窗_展现
										}
									>
										手工创建
									</Button>
								</CreateSystemModal>
							</FunctionPermissionCheck>
							<FunctionPermissionCheck
								functionPermission={
									FunctionPermissionEnum.从线上店铺导入
								}
							>
								<ImportPlatItemModal
									code={FunctionPermissionEnum.从线上店铺导入}
									onDone={systemResetRefresh}
								>
									<Button
										disabled={
											!userStore.hasFunctionPermission(
												FunctionPermissionEnum.从线上店铺导入
											)
										}
										className={cs("r-mr-8")}
										data-point={
											Pointer.商品_普通货品档案_从线上店铺导入_弹窗_从线上店铺导入_展现
										}
									>
										从线上店铺导入
									</Button>
								</ImportPlatItemModal>
							</FunctionPermissionCheck>
							<FunctionPermissionCheck
								functionPermission={
									FunctionPermissionEnum.Excel导入货品
								}
							>
								<Button
									disabled={
										!userStore.hasFunctionPermission(
											FunctionPermissionEnum.Excel导入货品
										)
									}
									onClick={() => {
										setImportModalVisible(true);
									}}
									className={cs("r-mr-8")}
									data-point={
										Pointer.商品_普通货品_导入货匹资料_点击
									}
									icon={<DownloadOutlined />}
								>
									Excel导入货品
								</Button>
							</FunctionPermissionCheck>
							<FunctionPermissionCheck
								functionPermission={FunctionPermissionEnum.导出}
							>
								<Button
									disabled={
										!userStore.hasFunctionPermission(
											FunctionPermissionEnum.导出
										)
									}
									onClick={() =>
										setDownloadExcelModalVisible(true)
									}
									data-point={Pointer.货品_导出Excel}
									icon={<FileExcelOutlined />}
								>
									导出
								</Button>
							</FunctionPermissionCheck>
						</>
					) : (
						<>
							<FunctionPermissionCheck
								functionPermission={
									FunctionPermissionEnum.新建组合
								}
							>
								<CreateSystemModal
									code={FunctionPermissionEnum.新建组合}
									onSave={systemResetRefresh}
									archivesType={ArchivesTypeEnum.组合}
								>
									<Button
										disabled={
											!userStore.hasFunctionPermission(
												FunctionPermissionEnum.新建组合
											)
										}
										data-point={
											Pointer.商品_组合货品_新建组合货品
										}
										type="primary"
									>
										新建组合
									</Button>
								</CreateSystemModal>
							</FunctionPermissionCheck>
							<FunctionPermissionCheck
								functionPermission={
									FunctionPermissionEnum.将指定货品转为组合货品
								}
							>
								<CombinedModal
									code={
										FunctionPermissionEnum.将指定货品转为组合货品
									}
									onDone={systemResetRefresh}
								>
									<Button
										disabled={
											!userStore.hasFunctionPermission(
												FunctionPermissionEnum.将指定货品转为组合货品
											)
										}
										className={cs(
											"r-ml-8",
											s["custom-color"]
										)}
										data-point={
											Pointer.商品_组合货品_指定货品转为组合货品
										}
									>
										将指定货品转为组合货品
									</Button>
								</CombinedModal>
							</FunctionPermissionCheck>
							<Dropdown
								overlay={
									<Menu className="r-flex r-fd-c">
										<Menu.Item
											key="importCombinedSysItem"
											onClick={() => {
												setImportCombinedSysItem(true);
											}}
										>
											导入组合货品（推荐）
										</Menu.Item>
										<FunctionPermissionCheck
											functionPermission={
												FunctionPermissionEnum.导入组合货品
											}
										>
											<Menu.Item
												key="importOtherSysItem"
												disabled={
													!userStore.hasFunctionPermission(
														FunctionPermissionEnum.导入组合货品
													)
												}
												data-point={
													Pointer.商品_组合货品_导入组合货品
												}
												onClick={() => {
													sendPoint(
														Pointer.商品_组合货品_导入组合货品
													);
													setImportSysItemVisible(
														true
													);
												}}
											>
												导入其他资料
											</Menu.Item>
										</FunctionPermissionCheck>
									</Menu>
								}
								placement="bottomCenter"
								overlayClassName="operate-com-dropdown"
							>
								<Button size="middle" className="r-ml-8">
									导入组合货品
									<DownOutlined />
								</Button>
							</Dropdown>

							<FunctionPermissionCheck
								functionPermission={
									FunctionPermissionEnum.导出组合货品
								}
							>
								<Button
									disabled={
										!userStore.hasFunctionPermission(
											FunctionPermissionEnum.导出组合货品
										)
									}
									onClick={() =>
										setDownloadExcelModalVisible(true)
									}
									className={cs("r-ml-8")}
									data-point={
										Pointer.商品_组合货品档案_导出Excel
									}
									icon={<FileExcelOutlined />}
								>
									导出
								</Button>
							</FunctionPermissionCheck>

							{/* <FunctionPermissionCheck functionPermission={FunctionPermissionEnum.导入组合货品}>
									<ImportSystemModal code={FunctionPermissionEnum.导入组合货品}  onDone={ systemResetRefresh }>
										<Button  disabled={ !userStore.hasFunctionPermission(FunctionPermissionEnum.导入组合货品) }  data-point={ Pointer.商品_组合货品_导入组合货品 } className={ cs('r-ml-8') } >导入组合货品</Button>
									</ImportSystemModal>
								</FunctionPermissionCheck>
								<FunctionPermissionCheck functionPermission={FunctionPermissionEnum.导出组合货品}>
									<Button  disabled={ !userStore.hasFunctionPermission(FunctionPermissionEnum.导出组合货品) }  onClick={ () => setDownloadExcelModalVisible(true) } className={ cs('r-ml-8') } data-point={ Pointer.商品_组合货品档案_导出Excel }>导出Excel</Button>
								</FunctionPermissionCheck> */}
						</>
					)}
					<FunctionPermissionCheck
						functionPermission={
							isSingle
								? FunctionPermissionEnum.货品批量修改
								: FunctionPermissionEnum.组合批量修改
						}
					>
						<Dropdown
							disabled={
								!userStore.hasFunctionPermission(
									isSingle
										? FunctionPermissionEnum.货品批量修改
										: FunctionPermissionEnum.组合批量修改
								)
							}
							overlay={MoreMenu}
							placement="bottomCenter"
							overlayClassName="operate-com-dropdown"
						>
							<Button
								size="middle"
								className="r-ml-8"
								style={{ width: 110 }}
							>
								批量修改
								<DownOutlined />
							</Button>
						</Dropdown>
					</FunctionPermissionCheck>
					<FunctionPermissionCheck
						functionPermission={
							isSingle
								? FunctionPermissionEnum.批量填充平台信息
								: FunctionPermissionEnum.组合批量填充平台信息
						}
					>
						<BatchFillPlatform
							selectedList={selectedList}
							isSingle={isSingle}
							onSave={handleFillPlatform}
							noCostAuth={
								!userStore.hasFieldsPermission(
									FieldsPermissionEnum.成本价
								)
							}
							code={
								isSingle
									? FunctionPermissionEnum.批量填充平台信息
									: FunctionPermissionEnum.组合批量填充平台信息
							}
						>
							<Button
								disabled={
									!userStore.hasFunctionPermission(
										isSingle
											? FunctionPermissionEnum.批量填充平台信息
											: FunctionPermissionEnum.组合批量填充平台信息
									)
								}
								className="r-ml-8"
							>
								批量填充平台信息
							</Button>
						</BatchFillPlatform>
					</FunctionPermissionCheck>
					<FunctionPermissionCheck
						functionPermission={
							isSingle
								? FunctionPermissionEnum.货品库存同步
								: FunctionPermissionEnum.组合货品库存同步
						}
					>
						<Dropdown
							disabled={
								!userStore.hasFunctionPermission(
									isSingle
										? FunctionPermissionEnum.货品库存同步
										: FunctionPermissionEnum.组合货品库存同步
								)
							}
							overlay={stockSyncMenu}
							placement="bottomLeft"
						>
							<Button className="r-ml-8">
								库存同步
								<DownOutlined />
							</Button>
						</Dropdown>
					</FunctionPermissionCheck>
					<FunctionPermissionCheck
						functionPermission={
							isSingle
								? FunctionPermissionEnum.货品打印
								: FunctionPermissionEnum.组合货品打印
						}
					>
						<Dropdown
							disabled={
								!userStore.hasFunctionPermission(
									isSingle
										? FunctionPermissionEnum.货品打印
										: FunctionPermissionEnum.组合货品打印
								)
							}
							overlay={printMenu}
							placement="bottomLeft"
						>
							<Button className="r-ml-8">
								货品打印
								<DownOutlined />
							</Button>
						</Dropdown>
					</FunctionPermissionCheck>
					{/* <EnumSelect placeholder="库存同步" enum={ stockSyncEnum } className="r-ml-8" /> */}
				</div>

				<div className="r-flex r-ai-c r-jc-sb">
					{/* 货品打印设置 */}
					{
						archivesType != ArchivesTypeEnum.组合 ?
							<Dropdown
								disabled={
									!userStore.hasFunctionPermission(
										isSingle
											? FunctionPermissionEnum.货品打印
											: FunctionPermissionEnum.组合货品打印
									)
								}
								overlay={<Menu>
									<FunctionPermissionCheck
										functionPermission={
											isSingle
												? FunctionPermissionEnum.货品打印设置
												: FunctionPermissionEnum.组合货品打印设置
										}
									>
										<Menu.Item
											key={0}
											disabled={
												!userStore.hasFunctionPermission(
													isSingle
														? FunctionPermissionEnum.货品打印设置
														: FunctionPermissionEnum.组合货品打印设置
												)
											}
											className="r-pointer r-mr-12"
											data-point={Pointer.货品打印设置}
											onClick={() => handleOpenPrintMainPanel()}
											icon={<SettingOutlined />}
										>
											打印设置
										</Menu.Item>
									</FunctionPermissionCheck>
									<Menu.Item
										key={1}
										className="r-pointer r-mr-12"
										icon={<SettingOutlined />}
									>
										<BrandEditorModal>
											品牌设置
										</BrandEditorModal>
									</Menu.Item>
									<Menu.Item
										key={2}
										className="r-pointer r-mr-12"
										onClick={ () => { history.push('/settings/system?introName=goodsCustomSet'); } }
										icon={<SettingOutlined />}
									>
										自定义属性设置
									</Menu.Item>

									<FunctionPermissionCheck
										functionPermission={FunctionPermissionEnum.货位管理}
									>
										<Menu.Item
											key={3}
											disabled={!userStore.hasFunctionPermission(FunctionPermissionEnum.货位管理)}
											className="r-pointer r-mr-12"
											onClick={() => { setStorageLocationVisible(true); }}
											icon={<SettingOutlined />}
											data-point={Pointer.商品_货品档案_点击_货位管理}
										>
											货位管理
										</Menu.Item>
									</FunctionPermissionCheck>
								</Menu>}
								placement="bottomLeft"
								className="r-mr-8"
							>
								<Button className="r-ml-8">
									货品设置
									<DownOutlined />
								</Button>
							</Dropdown> : <FunctionPermissionCheck
								functionPermission={
									isSingle
										? FunctionPermissionEnum.货品打印设置
										: FunctionPermissionEnum.组合货品打印设置
								}
							>
								<Button
									disabled={
										!userStore.hasFunctionPermission(
											isSingle
												? FunctionPermissionEnum.货品打印设置
												: FunctionPermissionEnum.组合货品打印设置
										)
									}
									className="r-pointer r-mr-12"
									data-point={Pointer.货品打印设置}
									onClick={() => handleOpenPrintMainPanel()}
									icon={<SettingOutlined />}
								>
									打印设置
								</Button>
							</FunctionPermissionCheck>
					}
					{
						isSingle ? <ColSortSettingDrawer pageColumnConfig={pageColumnConfig} defaultList={ columnConfig } /> : <ColSortSetting />
					}
				</div>
			</div>
		);
	};

	const getTableExpandContext = () => {
		return (
			<>
				<div className={cs(s.left)} style={{ borderTop: "1px solid #ebebeb" }}>
					<Collapse
						expandIconPosition="right"
						activeKey="1"
						ghost
						expandIcon={({ isActive }) => (
							<CaretRightOutlined rotate={isActive ? 90 : 180} />
						)}
					>
						<Collapse.Panel
							header={
								<div className={cs(s.titleHeader)}>
									货品分类
								</div>
							}
							key="1"
						>
							<GroupPanel
								code={
									isSingle
										? FunctionPermissionEnum.货品修改增加分类
										: FunctionPermissionEnum.组合货品修改增加分类
								}
								onChange={groupChange}
								contentStyle={groupPanelHeight}
							/>
						</Collapse.Panel>
					</Collapse>
				</div>
			</>
		);
	};

	const [hoveredCell, setHoveredCell] = useState(null);
	const handleMouseEnter = (key, index) => {
		setHoveredCell({ key, index });
	};

	const handleMouseLeave = () => {
		setHoveredCell(null);
	};

	const handleSortQuery = (key) => {
		setSortQueryObj((prev) => {
			if (prev.orderBy === key) {
				if (prev.orderType === 查询排序.升序) {
					prev.orderType = 查询排序.降序;
				} else {
					prev.orderType = 查询排序.升序;
				}
				return { ...prev };
			} else {
				return {
					orderBy: key,
					orderType: 查询排序.升序
				};
			}

		});
		ref.current.submit();
	}

	const sortContent = useCallback((sortKey, title) => {
		const sysSort = ["sys_item_alias", "outer_id", "brand_id",'property'];
		return archivesType == ArchivesTypeEnum.单品规格 || (archivesType == ArchivesTypeEnum.单品 && sysSort.includes(sortKey)) ? <div className="r-pointer" onClick={ () => { handleSortQuery(sortKey); } }>
			{ title }
			<span style={ { cursor: 'pointer' } }>
				{sortQueryObj?.orderBy === sortKey
					? (sortQueryObj.orderType === 查询排序.升序
						? <Icon type="paixushang" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />
						: <Icon type="paixuxia" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />)
					: <Icon type="paixuxia" style={ { transform: 'translateY(2px)', color: "rgba(0, 0, 0, 0.45)" } } />}
			</span>
		</div> : title
	}, [sortQueryObj, isSingle, archivesType])

	const skuPicTitle = useCallback(() => {
		const settingArr = [{
			label: '小图',
			value: '0',
		}, {
			label: '中图',
			value: '1',
		}, {
			label: '大图',
			value: '2',
		}];
		const content = (
			<div className="r-pd-2">
				<div style={{display:'flex'}}>
				<div className="r-mb-8">缩略图展示：</div>
				<Radio.Group
					value={ skuPicConfig?.value || '1' }
					onChange={ (e) => onSkuPicConfigChange(e.target.value) }
				>
					<div style={{ display: 'flex', flexDirection: 'row' }}>
						{settingArr.map(item => (
							<Radio key={item.value} value={item.value} style={{ marginRight: '8px' }}>{item.label}</Radio>
						))}
					</div>
				</Radio.Group>
				</div>

				<div style={{display:'flex'}}>
				<div className="r-mb-2">预览图展示：</div>
				<Radio.Group
					value={ skuPicPreviewConfig?.value || '1' }
					onChange={ (e) => onSkuPicPreviewConfigChange(e.target.value) }
				>
					<div style={{ display: 'flex', flexDirection: 'row' }}>
						{settingArr.map(item => (
							<Radio key={item.value} value={item.value} style={{ marginRight: '8px' }}>{item.label}</Radio>
						))}
					</div>
				</Radio.Group>
				</div>
			</div>
		);
		const title = (
			<div className='r-flex r-ai-c'>
				规格图
				<Popover placement="bottom" title={ null } content={ content } trigger="click">
					<Icon className="r-ml-5 r-c-gray" type="guigeshezhi" size={ 16 } />
				</Popover>
			</div>
		);
		return title;
	}, [skuPicConfig, skuPicPreviewConfig])

	const columns = [
		{
			title: (
				<Checkbox
					checked={checkedGroup?.group_all.checked}
					indeterminate={
						!checkedGroup?.group_all.checked &&
						!!checkedGroup?.group_all.num
					}
					onChange={onCheckAllChange}
				/>
			),
			align: "center",
			dataIndex: "groupChecked",
			key: "groupChecked",
			width: 40,
			fixed: "left",
			className: cs(s.noline),
			minWith: 40,
			render: (text, row) => {
				const { colSpan } = row;
				const checkNode = (
					<Checkbox
						onChange={(e) => onCheckedGroup(e, row)}
						checked={checkedGroup?.[row.groupId]?.checked}
						indeterminate={
							!checkedGroup?.[row.groupId]?.checked &&
							!!checkedGroup?.[row.groupId]?.num
						}
					/>
				);
				return <>{colSpan && checkNode}</>;
			},
		},
		{
			title: "序号",
			dataIndex: "index",
			key: "index",
			align: "center",
			width: 50,
			fixed: "left",
			minWith: 50,
			render: (t: string, row: SysItem, index: number) => {
				if (row.isCollapse) return null;
				if (!row.rowSpan) return null;
				return +row.groupId.replace("groupId", "") + 1;
			},
		},
		{
			title: (
				sortContent("sys_item_alias", "简称", )
			),
			dataIndex: "sysItemAlias",
			width: 160,
			minWith: 160,
			className: cs("colorfont"),
			shouldCellUpdate,
			render: (t: string, row: SysItem, index: number) => {
				if (!row.rowSpan) return null;
				const val = row.sysItemAlias;
				return (
					<div
						className={cs("colorblack")}
						style={{ width: "100%", height: "100%" }}
						onMouseEnter={() =>
							handleMouseEnter("sysItemAlias", index)
						}
						onMouseLeave={handleMouseLeave}
					>
						<EditText
							UserStore={userStore}
							isSingle={isSingle}
							tipText="简称"
							text={val}
							onChange={(text: string) => {
								console.log(row)
								editSysItemAlias(text, row.sysItemId);
							}}
							showIconEdit
							hoveredCell={
								index === hoveredCell?.index &&
								"sysItemAlias" === hoveredCell?.key
							}
						/>
					</div>
				);
			},
			onCell: groupCellRender,
		},
		{
			title: (
				sortContent("outer_id", "货品编码")
			),
			dataIndex: "outerId",
			width: 100,
			minWith: 100,
			className: cs("colorfont"),
			shouldCellUpdate,
			render: (t: string, row: SysItem, index: number) => {
				if (!row.rowSpan) return null;
				const val = row.outerId;
				return (
					<div className="line-clamp-2" title={val}>
						{val}
					</div>
				);
			},
			onCell: groupCellRender,
		},
		{
			title: "货品名称",
			dataIndex: "sysItemName",
			width: 200,
			minWith: 200,
			className: cs("colorfont"),
			shouldCellUpdate,
			render: (t: string, row: SysItem, index: number) => {
				if (!row.rowSpan) return null;
				const val = row.sysItemName;
				return (
					<div className="line-clamp-2" title={val}>
						{val}
					</div>
				);
			},
			onCell: groupCellRender,
		},
		{
			title: (
				sortContent("brand_id", "品牌")
			),
			dataIndex: "brandName",
			width: 100,
			minWith: 100,
			className: cs("colorfont"),
			shouldCellUpdate,
			render: (t: string, row: SysItem, index: number) => {
				if (!row.rowSpan) return null;
				const val = row.brandName;
				return (
					<div className="line-clamp-2" title={val}>
						{val}
					</div>
				);
			},
			onCell: groupCellRender,
		},
		{
			title: (
				sortContent("property", "货品属性")
			),
			dataIndex: "property",
			width: 100,
			minWith: 100,
			className: cs("colorfont"),
			shouldCellUpdate,
			render: (t: string, row: SysItem, index: number) => {
				if (!row.rowSpan) return null;
				const option = ItemPropertyOptions.find(opt => opt.value === row.property);
				const val = option ? option.label : '';
				return (
				  <div className="line-clamp-2" title={val}>
					{val}
				  </div>
				);
			  },
			onCell: groupCellRender,
		},
		{
			title: "",
			dataIndex: "sysChecked",
			id: "sysChecked",
			width: 40,
			minWith: 40,
			className: cs(s.noline),
			render: (text, row) => {
				const { isCollapse } = row;
				const checkNode = (
					<Checkbox
						onChange={(e) => onCheckedItem(e, row)}
						checked={row.isChecked}
					/>
				);
				const clickItem = (
					<>
						<div
							style={{
								background: "#fff",
								margin: "-8px",
								paddingLeft: "8px",
								height: "calc(100% + 14px)",
								width: "calc(100% + 16px)",
								lineHeight: "calc(100% + 16px)",
							}}
							data-point={
								row?.collapseShow
									? Pointer["商品_货品档案_收起货品规格"]
									: Pointer["商品_货品档案_展开更多货品规格"]
							}
							className={cs(
								"r-ml-5 r-pointer r-c-warning",
								s["more-expand"]
							)}
							onClick={() => {
								toggleList(row);
							}}
						>
							{row.collapseShow ? "收起" : "展开更多"}规格{" "}
							{row.collapseShow ? (
								<UpOutlined />
							) : (
								<DownOutlined />
							)}
						</div>
					</>
				);
				return <>{isCollapse ? clickItem : checkNode}</>;
			},
		},
		{
			title: (
				<div>
					{ isSingle ? skuPicTitle() : "规格图" }
				</div>
			),
			dataIndex: "picUrl",
			width: 80,
			minWith: 80,
			className: cs(s.noborder, "colorfont"),
			shouldCellUpdate,
			render: (text, row: SysItem) => {
				if (row.isCollapse) return null;
				const sizeObj = {
					"0": 32,
					"1": 48,
					"2": 64,
				}
				return (
					<div className="r-flex">
						<WaresInfo imgUrl={row.sysSkuList[0].picUrl} imgSize={ sizeObj[skuPicConfig?.value] || 48 } previewPicSize={skuPicPreviewConfig?.value}/>
					</div>
				);
			},
		},
		{
			title: (
				sortContent("sys_sku_name", "规格名称")
			),
			dataIndex: "sysSkuName",
			width: 140,
			minWith: 140,
			className: cs(s.noborder, "colorfont"),
			shouldCellUpdate,
			render: (text, row: SysItem, index: number) => {
				if (row.isCollapse) return null;
				const { isCombination } = row?.sysSkuList?.[0] || {};
				const val = row.sysSkuList[0].sysSkuName;
				return (
					<>
						<div className="r-flex" style={{ height: "100%" }}>
							{isCombination == 1 ? (
								<>
									<Tooltip title="组合货品">
										<span className="r-warehouse-combined">
											组
										</span>
									</Tooltip>
									<Tooltip title={val}>
										<span className="line-clamp-2">{val}</span>
									</Tooltip>
								</>
							) : (
								<div
									title={val}
									className={cs("colorblack")}
									style={{ width: "100%", height: "100%" }}
									onMouseEnter={() =>
										handleMouseEnter("sysSkuName", index)
									}
									onMouseLeave={handleMouseLeave}
								>
									<EditText
										UserStore={userStore}
										isSingle={isSingle}
										tipText="规格名称"
										text={ val }
										onChange={(text: string) => {
											// editSysSkuAlias(
											// 	text,
											// 	row.sysSkuList[0].sysSkuId as number,
											// 	row.sysItemId
											// );
											handleSingleChange({row, sysSkuList: row.sysSkuList, field: 'sysSkuName', text })
										}}
										showIconEdit
										hoveredCell={
											index === hoveredCell?.index &&
											"sysSkuName" === hoveredCell?.key
										}
									/>
								</div>
							)}

						</div>
					</>
				);
			},
		},
		{
			title: (
				sortContent("sys_sku_alias", "规格别名")
			),
			dataIndex: "sysSkuAlias",
			width: 120,
			minWith: 120,
			className: cs(s.noborder, "colorfont"),
			shouldCellUpdate,
			render: (text, row: SysItem, index) => {
				if (row.isCollapse) return null;
				return (
					<div
						className={cs("colorblack")}
						style={{ width: "100%", height: "100%" }}
						onMouseEnter={() =>
							handleMouseEnter("sysSkuAlias", index)
						}
						onMouseLeave={handleMouseLeave}
					>
						<EditText
							UserStore={userStore}
							isSingle={isSingle}
							tipText="规格别名"
							canEmpty={true}
							text={row.sysSkuList[0].sysSkuAlias}
							onChange={(text: string) => {
								editSysSkuAlias(
									text,
									row.sysSkuList[0].sysSkuId as number,
									row.sysItemId
								);
							}}
							showIconEdit
							hoveredCell={
								index === hoveredCell?.index &&
								"sysSkuAlias" === hoveredCell?.key
							}
						/>
					</div>
				);
			},
		},
		{
			title: (
				sortContent("sku_outer_id", "货品规格编码")
			),
			dataIndex: "skuOuterId",
			width: 100,
			minWith: 100,
			className: cs(s.noborder, "colorfont"),
			shouldCellUpdate,
			render: (text, row: SysItem) => {
				if (row.isCollapse) return null;
				const val = row.sysSkuList[0].skuOuterId;
				return (
					<div className="line-clamp-2" title={val} style={
						{whiteSpace:'pre-wrap'}
					}>
						{val}
					</div>
				);
			},
		},
		{
			title: (
				sortContent("sys_color", "颜色及尺码")
			),
			dataIndex: "sysColorAndSize",
			width: 100,
			minWith: 100,
			className: cs(s.noborder, "colorfont"),
			shouldCellUpdate,
			render: (text, row: SysItem) => {
				if (row.isCollapse) return null;
				const title = `${row.sysSkuList[0]?.sysColor
						? row.sysSkuList[0]?.sysColor + ";"
						: ""
					} ${row.sysSkuList[0]?.sysSize || ""}`;
				return (
					<div className="line-clamp-2" title={title}>
						{title}
					</div>
				);
			},
		},
		{
			title: "组合",
			width: 230,
			minWith: 230,
			dataIndex: "combined",
			render: (txt: any, row: SysItem, index: number) => {
				if (row.isCollapse) return null;
				const { groupCombinationList = [], isCombination } =
					row?.sysSkuList?.[0] || {};
				return (
					isCombination == 1 && (
						<div>
							{groupCombinationList.map(
								(item: any, i: number) => {
									const showText = (
										<>
											<div>
												货品简称：
												{item.sysItemAlias || ""}
											</div>
											<div>
												规格名称：
												{item.sysSkuName || ""}
											</div>
											<div>
												货品规格编码：
												{item.skuOuterId || ""}
											</div>
											<div>
												数量：
												{item.groupProportionNum || 0}
											</div>
										</>
									);
									return (
										// eslint-disable-next-line react/no-array-index-key
										<div
											key={`${item.sysSkuId}_${item.skuOuterId}_${i}`}
											className={cs(
												"r-flex r-ai-c",
												i > 0 ? "r-mt-4" : ""
											)}
										>
											<WaresInfo
												imgUrl={item.picUrl}
												imgSize={28}
												linkDetailId={row.numIid}
												linkDetailPlatForm={
													row.platform
												}
											/>
											<Tooltip
												title={showText}
												trigger="hover"
												overlayStyle={{ maxWidth: 500 }}
											>
												<span
													className={cs(
														s.skuNumClick,
														"line-clamp-1"
													)}
													onClick={() =>
														onSeeModalShow(row)
													}
												>
													{[
														item.sysItemAlias,
														item.sysSkuName,
														item.skuOuterId,
													]
														.filter((i) => i)
														.join("/")}
													*
													{item.groupProportionNum ||
														0}
												</span>
											</Tooltip>
										</div>
									);
								}
							)}
						</div>
					)
				);
			},
		},
		{
			title: (
				sortContent("item_no", "货号")
			),
			dataIndex: "itemNo",
			width: 100,
			minWith: 100,
			className: cs(s.noborder, "colorfont"),
			shouldCellUpdate,
			render: (text, row: SysItem) => {
				if (row.isCollapse) return null;
				const val = row.sysSkuList[0].itemNo;
				return (
					<div className="line-clamp-2" title={val}>
						{val}
					</div>
				);
			},
		},
		{
			title: (
				sortContent("bar_code", "条形码")
			),
			dataIndex: "barCode",
			width: 100,
			minWith: 100,
			className: cs(s.noborder, "colorfont"),
			shouldCellUpdate,
			render: (text, row: SysItem) => {
				if (row.isCollapse) return null;
				const val = row.sysSkuList[0].barCode;
				return (
					<div className="line-clamp-2" title={val}>
						{val}
					</div>
				);
			},
		},
		{
			title: "库存同步",
			dataIndex: "syncStockFlag",
			width: 100,
			minWith: 100,
			className: cs(s.noborder, "colorfont"),
			key: "syncStockFlag",
			shouldCellUpdate,
			render: (text, row: SysItem, index) => {
				if (row.isCollapse) return null;
				const val = row.syncStockFlag;
				return (
					<FunctionPermissionCheck functionPermission={isSingle ? FunctionPermissionEnum.货品库存同步 : FunctionPermissionEnum.组合货品库存同步} >
						<Switch
						checkedChildren="开启"
						unCheckedChildren="关闭"
						checked={val == 1}
						loading={row.switchLoading}
						key={row.rowId}
						disabled={
							!userStore.hasFunctionPermission(
								isSingle
									? FunctionPermissionEnum.货品库存同步
									: FunctionPermissionEnum.组合货品库存同步
							)
						}
						onChange={(checked) => {
							handleChangeSyncStockFlag(checked, row, index);
						}}
					/>
					</FunctionPermissionCheck>
				);
			},
		},
		{
			title: (
				sortContent("weight", `重量(${isKg ? "k" : ""}g)`)
			),
			dataIndex: "weight",
			width: 120,
			minWith: 120,
			className: cs(s.noborder, "colorfont"),
			shouldCellUpdate,
			render: (text, row: SysItem, index) => {
				if (row.isCollapse) return null;
				const { isCombination } = row?.sysSkuList?.[0] || {};
				const weight = +row.sysSkuList[0].weight || 0;
				const autoWeight = row.sysSkuList[0].autoWeight;
				const displayedWeight = formatWeightDisplay(isKg,weight)
				return (
					<>
						{
							isCombination !== 1
								? <div
									className={cs("colorblack")}
									style={{ width: "100%", height: "100%" }}
									onMouseEnter={() =>
										handleMouseEnter("weight", index)
									}
									onMouseLeave={handleMouseLeave}
								>
									<EditText
										UserStore={userStore}
										isSingle={isSingle}
										tipText="重量"
										isNumber
										text={ String(displayedWeight) }
										onChange={(text: string) => {
											// editSysSkuAlias(
											// 	text,
											// 	row.sysSkuList[0].sysSkuId as number,
											// 	row.sysItemId
											// );
											handleSingleChange({row, sysSkuList: row.sysSkuList, field: 'weight', text })
										}}
										showIconEdit
										hoveredCell={
											index === hoveredCell?.index &&
											"weight" === hoveredCell?.key
										}
									/>
								</div> : <div className="line-clamp-2" title={String(displayedWeight)}>{displayedWeight}</div>
						}
						{autoWeight ? (
							<Tooltip title="系统根据单品比例自动计算">
								<Icon
									className="r-ml-2"
									size={14}
									type="zidongjisuan"
								/>{" "}
							</Tooltip>
						) : null}
					</>
				);
			},
		},
		{
			title: (
				sortContent("price", "售价(元)")
			),
			dataIndex: "price",
			width: 100,
			minWith: 100,
			className: cs(s.noborder, "colorfont"),
			shouldCellUpdate,
			render: (text, row: SysItem, index) => {
				if (row.isCollapse) return null;
				const { isCombination } = row?.sysSkuList?.[0] || {};
				const p = row.sysSkuList[0].price;
				const val = _.isNil(p) ? "0.00" : Number(p).toFixed(2);
				return (
					<>
						{
							isCombination !== 1
								? <div
								className={cs("colorblack")}
								style={{ width: "100%", height: "100%" }}
								onMouseEnter={() =>
									handleMouseEnter("price", index)
								}
								onMouseLeave={handleMouseLeave}
							>
								<EditText
									UserStore={userStore}
									isSingle={isSingle}
									tipText="售价"
									isNumber
									text={ val }
									onChange={(text: string) => {
										// editSysSkuAlias(
										// 	text,
										// 	row.sysSkuList[0].sysSkuId as number,
										// 	row.sysItemId
										// );
										handleSingleChange({row, sysSkuList: row.sysSkuList, field: 'price', text })
									}}
									showIconEdit
									hoveredCell={
										index === hoveredCell?.index &&
										"price" === hoveredCell?.key
									}
								/>
							</div> : <div className="line-clamp-2" title={val}>{val}</div>
						}
					</>
				);
			},
		},
		{
			title: (
				sortContent("tag_price", "吊牌价(元)")
			),
			dataIndex: "tagPrice",
			width: 100,
			minWith: 100,
			className: cs(s.noborder, "colorfont"),
			shouldCellUpdate,
			render: (text, row: SysItem, index) => {
				if (row.isCollapse) return null;
				const { isCombination } = row?.sysSkuList?.[0] || {};
				const p = row.sysSkuList[0].tagPrice;
				const val = _.isNil(p) ? "0.00" : Number(p).toFixed(2);
				return (
					<>
						{
							isCombination !== 1
								?	<div
								className={cs("colorblack")}
								style={{ width: "100%", height: "100%" }}
								onMouseEnter={() =>
									handleMouseEnter("tagPrice", index)
								}
								onMouseLeave={handleMouseLeave}
							>
								<EditText
									UserStore={userStore}
									isSingle={isSingle}
									tipText="吊牌价"
									text={val}
									isNumber
									onChange={(text: string) => {
										// editSysSkuAlias(
										// 	text,
										// 	row.sysSkuList[0].sysSkuId as number,
										// 	row.sysItemId
										// );
										handleSingleChange({row, sysSkuList: row.sysSkuList, field: 'tagPrice', text })
									}}
									showIconEdit
									hoveredCell={
										index === hoveredCell?.index &&
										"tagPrice" === hoveredCell?.key
									}
								/>
							</div> : <div className="line-clamp-2" title={val}>{val}</div>
						}
					</>
				);
			},
		},
		{
			title: (
				sortContent("cost_price", "成本价(元)")
			),
			dataIndex: "costPrice",
			width: 100,
			minWith: 100,
			className: cs(s.noborder, "colorfont"),
			shouldCellUpdate,
			render: (text, row: SysItem, index) => {
				if (row.isCollapse) return null;
				const { isCombination } = row?.sysSkuList?.[0] || {};
				const p = row.sysSkuList[0].costPrice;
				const autoCostPrice = row.sysSkuList[0].autoCostPrice;
				const val = _.isNil(p) ? "0.00" : p
				return (
					<FieldsPermissionCheck
						fieldsPermission={FieldsPermissionEnum.成本价}
						type={FieldsPermissionCheckTypeEnum.仅展示}
						noPermissionRender={<div>***</div>}
					>
						<>
							{
								isCombination !== 1
									?	<div
									className={cs("colorblack")}
									style={{ width: "100%", height: "100%" }}
									onMouseEnter={() =>
										handleMouseEnter("costPrice", index)
									}
									onMouseLeave={handleMouseLeave}
								>
									<EditText
										UserStore={userStore}
										isSingle={isSingle}
										tipText="成本价"
										isNumber
										text={ val }
										onChange={(text: string) => {
											// editSysSkuAlias(
											// 	text,
											// 	row.sysSkuList[0].sysSkuId as number,
											// 	row.sysItemId
											// );
											handleSingleChange({row, sysSkuList: row.sysSkuList, field: 'sysSkuCostPrice', text })
										}}
										showIconEdit
										hoveredCell={
											index === hoveredCell?.index &&
											"costPrice" === hoveredCell?.key
										}
									/>
								</div> : <div className="line-clamp-2" title={val}>{val}</div>
							}

							{autoCostPrice ? (
								<Tooltip title="系统根据单品比例自动计算">
									<Icon
										className="r-ml-2"
										size={14}
										type="zidongjisuan"
									/>{" "}
								</Tooltip>
							) : null}
						</>
					</FieldsPermissionCheck>
				);
			},
		},
		{
			title: (
				sortContent("supplier_id", "供应商")
			),
			dataIndex: "supplierName",
			width: 120,
			minWith: 120,
			className: cs(s.noborder, "colorfont"),
			shouldCellUpdate,
			render: (text, row: SysItem) => {
				if (row.isCollapse) return null;
				const val = row.sysSkuList[0].supplierName;
				return (
					<div className="line-clamp-2" title={val}>
						{val}
					</div>
				);
			},
		},
		{
			title: "市场",
			dataIndex: "market",
			width: 120,
			minWith: 120,
			className: cs(s.noborder, "colorfont"),
			shouldCellUpdate,
			render: (text, row: SysItem) => {
				if (row.isCollapse) return null;
				const val = row.sysSkuList[0].market;
				return (
					<div className="line-clamp-2" title={val}>
						{val}
					</div>
				);
			},
		},
		{
			title: "档口",
			dataIndex: "stall",
			width: 120,
			minWith: 120,
			className: cs(s.noborder, "colorfont"),
			shouldCellUpdate,
			render: (text, row: SysItem) => {
				if (row.isCollapse) return null;
				const val = row.sysSkuList[0].stall;
				return (
					<div className="line-clamp-2" title={val}>
						{val}
					</div>
				);
			},
		},
		{
			title: (
				sortContent("created", "创建时间")
			),
			dataIndex: "created",
			width: 140,
			minWith: 120,
			className: cs("colorfont"),
			render: (text, row: SysItem) => {
				if (row.isCollapse) return null;
				const val = row.sysSkuList[0].created;
				return (
					<div className="line-clamp-2" title={val}>
						{val}
					</div>
				);
			},
		},
		{
			title: (
				sortContent("modified", "更新时间")
			),
			dataIndex: "modified",
			width: 140,
			minWith: 120,
			className: cs("colorfont"),
			render: (text, row: SysItem) => {
				if (row.isCollapse) return null;
				const val = row.sysSkuList[0].modified;
				return (
					<div className="line-clamp-2" title={val}>
						{val}
					</div>
				);
			},
		},
		{
			title: "关联平台商品",
			dataIndex: "goodsView",
			width: 90,
			minWith: 90,
			className: cs(s.noborder, "colorfont"),
			shouldCellUpdate,
			render: (text, row: SysItem) => {
				if (row.isCollapse) return null;

				return (
					<UpdateArchives
						onChange={() => {
							ref?.current?.refresh();
						}}
						text={<div className="r-fc-1890FF r-pointer">{row.sysSkuList[0]?.relationItemSize}个</div>}
						sysItemId={row.sysItemId}
						sysSkuId={row.sysSkuList[0]?.sysSkuId as number}
					/>
				);
			},
		},
		{
			title: (
				sortContent("warehouse_slot_name", "货位")
			),
			dataIndex: "warehouseSlotName",
			width: 120,
			minWith: 120,
			className: cs(s.noborder, "colorfont"),
			shouldCellUpdate,
			render: (text, row: SysItem, index) => {
				if (row.isCollapse) return null;
				const val = row.sysSkuList[0]?.warehouseSlotName;
				return (
					<div
						className={cs("colorblack")}
						style={{ width: "100%", height: "100%" }}
						onMouseEnter={() =>
							handleMouseEnter("warehouseSlotName", index)
						}
						onMouseLeave={handleMouseLeave}
					>
						<EditText
							UserStore={userStore}
							isSingle={isSingle}
							tipText="货位"
							point={Pointer.商品_货品档案_列表_点击编辑货位}
							canEmpty={true}
							text={val}
							onChange={(text: string) => {
								handleSingleChange({
									row,
									sysSkuList: row.sysSkuList,
									field: 'warehouseSlotName',
									text
								});
							}}
							showIconEdit
							hoveredCell={
								index === hoveredCell?.index && "warehouseSlotName" === hoveredCell?.key
							}
							maxLength={32}
						/>
					</div>
				);
			},
		},
		{
			title: (
				<div className="r-flex r-jc-c">
					操作
					{
						archivesType != ArchivesTypeEnum.单品规格 && <Tooltip title="展开/收起所有货品规格">
							<div>
								<Icon
									onClick={batchToggleList}
									size={14}
									style={{ color: "#FD8204" }}
									type={batchToggleStatus ? "shouqi" : "zhankai"}
								/>
							</div>
						</Tooltip>
					}
				</div>
			),
			dataIndex: "operate",
			fixed: "right",
			className: cs(s.operateCell, s.cellLine),
			shouldCellUpdate,
			children: archivesType == ArchivesTypeEnum.单品规格 ? [
				{
					dataIndex: "Deletion&Logging",
					key: "Deletion&Logging",
					width: 120,
					render(_, row) {
						if (row.isCollapse) return null;
						const isDisabled1 = !userStore.hasFunctionPermission(
							isSingle
								? FunctionPermissionEnum.货品删除
								: FunctionPermissionEnum.组合货品删除
						);
						const isDisabled2 = !userStore.hasFunctionPermission(
							isSingle
								? FunctionPermissionEnum.货品编辑
								: FunctionPermissionEnum.组合货品编辑
						);
						const menu = (
							<Menu>
								<FunctionPermissionCheck
									functionPermission={
										isSingle
											? FunctionPermissionEnum.货品删除
											: FunctionPermissionEnum.组合货品删除
									}
								>
									<Menu.Item
										key="delete"
									>

											<div
												className={
													isDisabled1
														? "r-c-black45"
														: "r-fc-1890FF r-pointer"
												}
												onClick={() => {
													onDeleteSku(row);
												}}
												data-point={
													Pointer.商品_普通货品档案_删除
												}
											>
												删除
											</div>
									</Menu.Item>
								</FunctionPermissionCheck>
							</Menu>
						)
						const cellNode = (
							<>
								<FunctionPermissionCheck
									functionPermission={
										isSingle
											? FunctionPermissionEnum.货品编辑
											: FunctionPermissionEnum.组合货品编辑
									}
								>
									<div
										className={
											isDisabled2
												? "r-c-black45"
												: "r-fc-1890FF r-pointer"
										}
										onClick={() => {
											!isDisabled2 &&
												showEditModal({
													...row,
													permission_source: isSingle
														? FunctionPermissionEnum.货品编辑
														: FunctionPermissionEnum.组合货品编辑,
												});
										}}
										data-point={
											Pointer.商品_普通货品档案_编辑_弹窗_编辑普通货品弹窗_展现
										}
									>
										编辑
									</div>
								</FunctionPermissionCheck>
								<div
									className="r-fc-1890FF r-pointer"
									onClick={() =>
										setRecordModalSysSkuId(
											row.sysSkuList[0].sysSkuId
										)
									}
								>
									日志
								</div>
								<Dropdown overlay={menu} placement="topRight">
									<div className="r-c-primary">更多</div>
								</Dropdown>
							</>
						);
						return (
							<div
								className="r-flex"
								style={{
									width: "100%",
									height: "100%",
									margin: "-8px",
									position: "absolute",
								}}
							>
								<div
									className={s.cellBorder}
									style={{ borderRight: "1px solid #f0f0f0" }}
								>
									{cellNode}
								</div>
							</div>
						);
					},
				}
			] : [
				{
					dataIndex: "Deletion&Logging",
					key: "Deletion&Logging",
					width: 80,
					render(_, row) {
						if (row.isCollapse) return null;
						const isDisabled1 = !userStore.hasFunctionPermission(
							isSingle
								? FunctionPermissionEnum.货品删除
								: FunctionPermissionEnum.组合货品删除
						);
						const cellNode = (
							<>
								<FunctionPermissionCheck
									functionPermission={
										isSingle
											? FunctionPermissionEnum.货品删除
											: FunctionPermissionEnum.组合货品删除
									}
								>
									<div
										className={
											isDisabled1
												? "r-c-black45"
												: "r-fc-1890FF r-pointer"
										}
										onClick={() => {
											onDeleteSku(row);
										}}
										data-point={
											Pointer.商品_普通货品档案_删除
										}
									>
										删除
									</div>
								</FunctionPermissionCheck>
								<div
									className="r-fc-1890FF r-pointer"
									onClick={() =>
										setRecordModalSysSkuId(
											row.sysSkuList[0].sysSkuId
										)
									}
								>
									日志
								</div>
							</>
						);
						return (
							<div
								className="r-flex"
								style={{
									width: "100%",
									height: "100%",
									margin: "-8px",
									position: "absolute",
								}}
							>
								<div
									className={s.cellBorder}
									style={{ borderRight: "1px solid #f0f0f0" }}
								>
									{cellNode}
								</div>
							</div>
						);
					},
				},
				{
					dataIndex: "Edit&Clone",
					key: "Edit&Clone",
					width: 80,
					render(_, row) {
						if (row.isCollapse) return null;

						const isDisabled2 = !userStore.hasFunctionPermission(
							isSingle
								? FunctionPermissionEnum.货品编辑
								: FunctionPermissionEnum.组合货品编辑
						);
						const isDisabled3 = !userStore.hasFunctionPermission(
							isSingle
								? FunctionPermissionEnum.货品克隆
								: FunctionPermissionEnum.组合货品克隆
						);

						const groupNode = (
							<>
								<FunctionPermissionCheck
									functionPermission={
										isSingle
											? FunctionPermissionEnum.货品编辑
											: FunctionPermissionEnum.组合货品编辑
									}
								>
									<div
										className={
											isDisabled2
												? "r-c-black45"
												: "r-fc-1890FF r-pointer"
										}
										onClick={() => {
											!isDisabled2 &&
												showEditModal({
													...row,
													permission_source: isSingle
														? FunctionPermissionEnum.货品编辑
														: FunctionPermissionEnum.组合货品编辑,
												});
										}}
										data-point={
											Pointer.商品_普通货品档案_编辑_弹窗_编辑普通货品弹窗_展现
										}
									>
										编辑
									</div>
								</FunctionPermissionCheck>
								<FunctionPermissionCheck
									functionPermission={
										isSingle
											? FunctionPermissionEnum.货品克隆
											: FunctionPermissionEnum.组合货品克隆
									}
								>
									<div
										className={
											isDisabled3
												? "r-c-black45"
												: "r-fc-1890FF r-pointer"
										}
										onClick={() => {
											!isDisabled3 &&
												cloneSku({
													...row,
													permission_source: isSingle
														? FunctionPermissionEnum.货品编辑
														: FunctionPermissionEnum.组合货品编辑,
												});
										}}
										data-point={
											Pointer.商品_普通货品档案_克隆_弹窗_克隆普通货品弹窗_展现
										}
									>
										克隆
									</div>
								</FunctionPermissionCheck>
							</>
						);
						return (
							row?.colSpan && (
								<div
									className="r-flex"
									style={{
										width: "100%",
										margin: "-8px",
										position: "absolute",
									}}
								>
									<div className={s.cellBorder}>
										{groupNode}
									</div>
								</div>
							)
						);
					},
				},
			],
		},
	];

	const handleResizeStop = (widthKV) => {
		let userColumnConfig = [...columnConfig.userConfig];
		userColumnConfig.forEach((i) => {
			if (widthKV[i.key]) i.width = widthKV[i.key];
		});
		setWidthMap((pre) => {
			return { ...pre, ...widthKV };
		});
		TradeDictInsertDictApi({
			userDictEnum: IColumnDictType[archivesType],
			value: JSON.stringify(userColumnConfig),
		});
	};

	const getColumns = useMemo(() => {
		const columnMap: any = {};
		console.log(columnConfig?.userConfig)
		columns.forEach((i) => (columnMap[i.dataIndex] = i));
		const _item = columnMap.groupChecked;
		const newCols = [_item, columnMap.index];
		const list = columnConfig?.userConfig || [];
		const configWidthMap = {};
		let isPushSysCheck = false;
		// newCols.push(_item);
		configWidthMap[_item.key] = _item.width;
		list?.forEach((i) => {
			if (!isPushSysCheck && i.collection) {
				const _item = columnMap["sysChecked"];
				newCols.push(_item);
				configWidthMap[_item.key] = _item.width;
				isPushSysCheck = true;
			}
			if (i.ischecked && columnMap[i.key]) {
				if (i.colFixed) {
					newCols.push({...columnMap[i.key], fixed: "left"});
				} else {
					newCols.push(columnMap[i.key]);
				}
				configWidthMap[i.key] = i.width;
			} else if (i.ischecked && i.name) {
				newCols.push({
					title: i.name,
					dataIndex: i.key,
					width: 120,
					className: cs(s.noborder, "colorfont"),
					shouldCellUpdate,
					render: (text, row: SysItem) => {
						if (row.isCollapse) return null;
						const val = row.sysSkuList[0]?.[i.key];
						return (
							<div className="line-clamp-2" title={val} >
								{val}
							</div>
						);
					},
				});
				configWidthMap[i.key] = i.width || 120;
			}
		});
		newCols.push(columnMap.operate);
		let newCellCols = [...newCols].filter(Boolean).map((i) => {
			return {
				...i,
				width:
					widthMap[i.dataIndex] ||
					configWidthMap[i.dataIndex] ||
					i.width,
			};
		});

		// 合并单元格
		const checkIndex = newCellCols.findIndex(
			(col) => col.dataIndex === "sysChecked"
		);
		// 判断是否是最后一行
		const isLastRow = (r) =>
			r?.allSysSkuList?.length > 1 &&
			r?.collapseIndex !== r?.allSysSkuList?.length - 1;
		// 将合并属性添加到每列中
		newCellCols.forEach((col, idx) => {
			col.getCellProps = (_, r) => {
				if (r?.isCollapse) {
					if (idx === checkIndex) {
						return { colSpan: newCellCols.length - checkIndex }
					} else if (idx > checkIndex) {
						return { style: { display: "none" } };
					}
					return;
				}
				if (idx < checkIndex) {
					if (r?.collapseShow) {
						return { style: { borderBottom: "none" } };
					} else if (isLastRow(r)) {
						return { style: { borderBottom: "none" } };
					}
				}
			};
		});

		// 设置最后一个单元格的边框
		const firstCell = newCellCols[newCellCols?.length - 1]?.children?.[0];
		const lastCell =
			newCellCols[newCellCols?.length - 1]?.children?.slice(-1)[0];
		if (firstCell) {
			firstCell.getCellProps = (_, r) => {
				if (r.isCollapse) {
					return { style: { display: "none" } };
				} else {
					return { style: { borderRight: "none" } };
				}
			};
		}
		if (lastCell) {
			lastCell.getCellProps = (_, r) => {
				if (r?.collapseShow && !r?.isCollapse) {
					return { style: { borderBottom: "none" } };
				} else if (isLastRow(r)) {
					return { style: { borderBottom: "none" } };
				}
			};
		}
		if (archivesType === ArchivesTypeEnum.单品规格) {
			newCellCols = newCellCols.filter(item => item.dataIndex !== 'sysChecked');
		}
		console.log(newCellCols, "--------------");
		return newCellCols;
	}, [columns, columnConfig?.userConfig, archivesType, skuPicConfig]);

	// 移除
	console.log(dataSource, "dataSource");
	const removeSelectedSkus = (params) => {
		const {updateSysSkuInfos} = params ||{}
		const newData = []
		const removeItems = new Set()
		updateSysSkuInfos?.forEach((i:any) => {
			removeItems.add(i.sysItemId)
		})
		dataSource.forEach(item => {
			if (!removeItems.has(item.sysItemId) || !classifyId || item.classifyId == params.classifyId) {
				newData.push(item);
			}
		});
		setDataSource(newData);
	};
	const onBatchEditOK = (
		val: {
			addContent: any;
			addType: any;
			autoCostPrice?: boolean;
			autoWeight?: boolean;
			addRule?: string;
			isCustomAttr?: boolean;
			attrName?: string;
			prefix?: string;
			suffix?: string;
			replaceKeyword?: string;
			replaceWith?: string;
			deleteKeyword?: string;
		},
		type: any
	) => {
		const validateValue = (condition: boolean, errorMessage: string) => {
			if (condition) {
				message.error(errorMessage);
				return false;
			}
			return true;
		};
		// 校验 addContent
		if (
			typeof val.addContent !== "number" &&
			!(val.autoCostPrice || val.autoWeight || val.addRule) &&
			type !== batchEditFuncName.批量设置货品规格编码 // 添加这行来跳过货品编码的判断
			&& type !== batchEditFuncName.批量设置货位  // 添加这行来跳过货位
		) {
			if (
				type === batchEditFuncName.批量设置简称 ||
				type === batchEditFuncName.批量设置别名
			) {
				if (val.addType === 1) {
					if (!validateValue(!val.addContent?.trim(), "请输入值"))
						return;
				}
			} else if (!validateValue(!val.addContent?.trim(), "请输入值"))
				return;
		}

		// 批量设置简称的校验逻辑
		if (
			type === batchEditFuncName.批量设置简称 ||
			type === batchEditFuncName.批量设置货品规格编码 ||
			type === batchEditFuncName.批量设置别名
		) {
			switch (val.addType) {
				case 2: // 添加
					const { prefix, suffix } = val;
					if (
						!validateValue(
							!prefix?.trim() && !suffix?.trim(),
							"请输入要添加的值"
						)
					)
						return;
					break;
				case 3: // 替换
					const { replaceKeyword } = val;
					if (
						!validateValue(
							!replaceKeyword?.trim(),
							"请输入需要替换的关键词"
						)
					)
						return;
					break;
				case 4: // 删除
					const { deleteKeyword } = val;
					if (
						!validateValue(
							!deleteKeyword?.trim(),
							"请输入需要删除的关键词"
						)
					)
						return;
					break;
				default:
					break;
			}
		}
		setLoading(true);
		// 根据 type 执行不同的处理函数
		if (handleBatchEditWithSku[type]) {
			handleBatchEditWithSku[type](val, type);
		} else if (
			val.addRule &&
			[
				batchEditFuncName.批量设置颜色,
				batchEditFuncName.批量设置尺码,
				batchEditFuncName.批量设置自定义属性,
			].includes(type)
		) {
			setLoading(false);
			setAssistantTableModalObj({
				visible: true,
				selectedList,
				addRule: val.addRule,
				addType: val.addType,
				attrName: val.attrName,
				isCustomAttr: val.isCustomAttr,
			});
		} else {
			handleBatchEditFunc(val, type);
		}
	};
	const handleBatchEditFunc = (val, type) => {
		const editParam = {
			[nameMap.成本价]: "sysSkuCostPrice",
			[nameMap.重量]: "weight",
			[nameMap.售价]: "price",
			[nameMap.分类]: "classifyId",
			[nameMap.供应商]: "supplierId",
			[nameMap.组合成本价]: "sysSkuCostPrice",
			[nameMap.组合重量]: "weight",
			[nameMap.颜色]: "sysColor",
			[nameMap.尺码]: "sysSize",
			[nameMap.货位]: "warehouseSlotName",
		};
		// 是否自动计算
		const autoCalcName = {
			[nameMap.组合成本价]: "autoCostPrice",
			[nameMap.组合重量]: "autoWeight",
		};
		const { addContent, addType, market, stall, supplierName } = val;
		const { arr, selectedList } = getUpdateSysSkuInfos();

		let attrParams = {};
		if (type === batchEditFuncName.批量设置自定义属性) {
			attrParams = {
				customAttributesList: [
					{
						key: addType,
						value: addContent,
					},
				],
			};
		} else if (
			[
				batchEditFuncName.批量设置颜色,
				batchEditFuncName.批量设置尺码,
			].includes(type)
		) {
			attrParams = {
				[editParam[type]]: addContent,
			};
		} else {
			// 处理允许空值的字段
			let paramValue = addContent;
			if (paramValue === undefined && ALLOW_EMPTY_FIELDS.includes(editParam[type])) {
				paramValue = '';
			}
			attrParams = {
				[editParam[type]]: paramValue,
			};
		}
		const params = {
			...attrParams,
			updateSysSkuInfos: arr,
			permission_source: isSingle
				? FunctionPermissionEnum.货品批量修改
				: FunctionPermissionEnum.组合批量修改,
		};
		if (autoCalcName[type]) {
			params[autoCalcName[type]] = val[autoCalcName[type]] || 0;
		}

		console.log('%c [ params ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', params)
		ItemSysItemUpdateSysSkuInfoWithBatchApi(params)
			.then((res) => {
				message.success(
					"保存成功，商品变更资料会在几分钟内同步至订单中"
				);
				let _type = type;
				console.log(type, "typetypetype");
				if (_type === nameMap.售价) _type = "price";
				if (_type === nameMap.供应商) {
					updateSkuData({
						selectedList,
						changeObj: [
							{ key: "market", value: market },
							{ key: "stall", value: stall },
							{ key: "supplierName", value: supplierName },
							{ key: "supplierId", value: addContent },
						],
					});
					// 组合货品设置成本价和重量 更新列表
				} else if (
					[nameMap.组合成本价, nameMap.组合重量].includes(_type)
				) {
					ref?.current.refresh();
				} else if (_type === batchEditFuncName.批量设置自定义属性) {
					ref?.current.refresh();
				} else if (_type === nameMap.分类) {
					removeSelectedSkus(params);
				} else {
					updateSkuData({
						selectedList,
						changeObj: [{ key: _type, value: addContent }],
					});
				}
			})
			.finally(() => {
				setLoading(false);
			});
	};
	const updateSkuData = ({ selectedList, changeObj }) => {
		const changValues = ({ sysSkuItem, changeObj }) => {
			for (const changeItem of changeObj) {
				const { value, key } = changeItem;
				sysSkuItem = {
					...sysSkuItem,
					[key]: value,
				};
			}
			return sysSkuItem;
		};
		const _dataSource = _.cloneDeep(dataSource);
		_dataSource.forEach((d, i) => {
			const itemArr = selectedList[d.sysItemId];
			if (itemArr?.length && d.sysSkuList) {
				d.sysSkuList.forEach((s, index) => {
					if (itemArr.includes(s.sysSkuId)) {
						d.sysSkuList[index] = changValues({
							sysSkuItem: d.sysSkuList[index],
							changeObj,
						});
					}
				});
				d.allSysSkuList.forEach((s, index) => {
					if (itemArr.includes(s.sysSkuId)) {
						d.allSysSkuList[index] = changValues({
							sysSkuItem: d.allSysSkuList[index],
							changeObj,
						});
					}
				});
			}
		});
		setDataSource(_dataSource);
	};

	const handleBatchEditWithSku = {
		[nameMap.库存版简称]: (val, type) => {
			const { addContent, addType } = val;
			let selectObj = {};
			selectedList.forEach((item) => {
				const { sysItemAlias = "", sysItemId, sysSkuList } = item;
				let newVal = "";
				if (val.addType === 1) {
					newVal =
						getNewContent(
							addContent,
							{ ...item, ...sysSkuList[0] },
							type
						) || sysItemAlias;
				} else if (val.addType === 2) {
					const { prefix = "", suffix = "" } = val;
					newVal = prefix + sysItemAlias + suffix;
				} else if (val.addType === 3) {
					const { replaceKeyword, replaceWith = "" } = val;
					console.log(replaceKeyword, replaceWith,'0000000000');
					const regex = new RegExp(escapeRegExp(replaceKeyword), "gi");
					newVal = sysItemAlias.replace(regex, replaceWith);
				} else if (val.addType === 4) {
					const { deleteKeyword = "" } = val;
					const regex = new RegExp(escapeRegExp(deleteKeyword), "gi");
					newVal = sysItemAlias.replace(regex, "");
				}
				// let alias = "";
				// 如果传过来简称为空 将使用原先简称
				// if (addType === addPosMap.覆盖) alias = newVal || sysItemAlias;
				// if (addType === addPosMap.现有前) alias = newVal + sysItemAlias;
				// if (addType === addPosMap.现有后) alias = sysItemAlias + newVal;
				selectObj[sysItemId] = { sysItemId, sysItemAlias: newVal };
			});
			const sysItemAliasEditInfos = Object.values(selectObj);
			let splitArr: Array<any> = spArr(sysItemAliasEditInfos, 100);
			const promises = splitArr.map(function (item) {
				let params = { sysItemAliasEditInfos: item };
				return ItemEditSysItemAliasWithBatchApi(params);
			});
			Promise.all(promises)
				.then((res) => {
					message.success(
						"保存成功，商品变更资料会在几分钟内同步至订单中"
					);
					// systemRefresh();
					const _dataSource = _.cloneDeep(dataSource);
					_dataSource.forEach((d) => {
						const _obj = selectObj[d.sysItemId];
						if (_obj) {
							d.sysItemAlias = _obj.sysItemAlias;
						}
					});
					setDataSource(_dataSource);
				})
				.finally(() => {
					setLoading(false);
				});
		},
		[nameMap.库存版别名]: (val, type) => {
			const { addContent, addType } = val;
			let _updateSysSkuAliasInfos = [];
			const _updateSysSkuAliasObj = {};
			selectedList.forEach((item) => {
				const { sysItemAlias, sysItemId, sysSkuList } = item;
				let sysSkuIdSysSkuAliasMap = {};
				if (!_updateSysSkuAliasObj[sysItemId])
					_updateSysSkuAliasObj[sysItemId] = {};
				sysSkuList.forEach((skuItem) => {
					const { sysSkuAlias = "", sysSkuId } = skuItem;
					let newVal = "";
					if (val.addType === 1) {
						const { filter } = val;
						newVal = filterText(
							getNewContent(
								addContent,
								{ ...item, ...skuItem },
								type
							) || "",
							filter
						);
					} else if (val.addType === 2) {
						const { prefix = "", suffix = "" } = val;
						newVal = prefix + sysSkuAlias + suffix;
					} else if (val.addType === 3) {
						const { replaceKeyword, replaceWith = "" } = val;
						const regex = new RegExp(escapeRegExp(replaceKeyword), "gi");
						newVal = sysSkuAlias.replace(regex, replaceWith);
					} else if (val.addType === 4) {
						const { deleteKeyword = "" } = val;
						const regex = new RegExp(escapeRegExp(deleteKeyword), "gi");
						newVal = sysSkuAlias.replace(regex, "");
					}
					// let alias = "";
					// if (addType === addPosMap.覆盖) alias = newVal;
					// if (addType === addPosMap.现有前) alias = newVal + sysSkuAlias;
					// if (addType === addPosMap.现有后) alias = sysSkuAlias + newVal;
					sysSkuIdSysSkuAliasMap[sysSkuId] = newVal === "" ? "EMPTY" : newVal;
					_updateSysSkuAliasObj[sysItemId][sysSkuId] = newVal;
				});
				_updateSysSkuAliasInfos.push({
					sysItemId,
					sysSkuIdSysSkuAliasMap,
				});
			});

			let splitArr: Array<any> = spArr(_updateSysSkuAliasInfos, 100);
			const promises = splitArr.map(function (item) {
				let params = {
					updateSysSkuAliasInfos: item,
					permission_source: isSingle
						? FunctionPermissionEnum.货品批量修改
						: FunctionPermissionEnum.组合批量修改,
				};
				return ItemSysItemUpdateSysSkuInfoWithBatchApi(params);
			});
			Promise.all(promises)
				.then((res) => {
					message.success(
						"保存成功，商品变更资料会在几分钟内同步至订单中"
					);
					const _dataSource = _.cloneDeep(dataSource);
					_dataSource.forEach((d) => {
						const _obj = _updateSysSkuAliasObj[d.sysItemId];
						if (_obj && d.sysSkuList) {
							d.sysSkuList.forEach((s) => {
								if (_obj.hasOwnProperty(s.sysSkuId)) {
									const _alias = _obj[s.sysSkuId];
									s.sysSkuAlias = _alias;
								}
							});
						}
					});
					setDataSource(_dataSource);
					// systemRefresh();
				})
				.finally(() => {
					setLoading(false);
				});
		},
		[nameMap.货品规格编码]: (val, type) => {
			const _updateSysSkuOuterIdInfos = [];
			const _updateObj = {};
			selectedList.forEach((item) => {
				const { sysItemId, sysSkuList } = item;
				const _updateSysSkuOuterIdObj = {};
				if (!_updateObj[sysItemId]) _updateObj[sysItemId] = {};
				sysSkuList.forEach((skuItem) => {
					const { skuOuterId = "", sysSkuId } = skuItem;
					let newVal = "";
					if (val.addType === 2) {
						const { prefix = "", suffix = "" } = val;
						newVal = prefix + skuOuterId + suffix;
					} else if (val.addType === 3) {
						const { replaceKeyword, replaceWith = "" } = val;
						const regex = new RegExp(escapeRegExp(replaceKeyword), "gi");
						newVal = skuOuterId.replace(regex, replaceWith);
					} else if (val.addType === 4) {
						const { deleteKeyword = "" } = val;
						const regex = new RegExp(escapeRegExp(deleteKeyword), "gi");
						newVal = skuOuterId.replace(regex, "");
					}
					_updateSysSkuOuterIdObj["sysItemId"] = sysItemId;
					_updateSysSkuOuterIdObj["sysSkuId"] = sysSkuId;
					_updateSysSkuOuterIdObj["sysSkuOuterId"] = newVal;
					_updateObj[sysItemId][sysSkuId] = newVal;
					_updateObj[sysSkuId] = {};
					_updateObj[sysSkuId]["sysSkuOuterId"] = skuOuterId;
				});
				_updateSysSkuOuterIdInfos.push(_updateSysSkuOuterIdObj);
			});

			let splitArr: Array<any> = spArr(_updateSysSkuOuterIdInfos, 100);
			const promises = splitArr.map(function (item) {
				let params = {
					updateSysSkuOuterIdInfos: item,
					permission_source: isSingle
						? FunctionPermissionEnum.货品批量修改
						: FunctionPermissionEnum.组合批量修改,
				};
				return ItemSysItemUpdateSysSkuInfoWithBatchApi(params);
			});
			Promise.all(promises)
				.then((res) => {
					const errorArr = [];
					res[0].forEach((i) => {
						if (!i?.success) {
							const _sku = _updateObj[i.operationId];
							errorArr.push(_sku.sysSkuOuterId);
						}
					});
					if (errorArr.length) {
						return eventBus.emit(
							"handleColseOrErrorMessage",
							errorArr
						);
					} else {
						eventBus.emit("handleColseOrErrorMessage", errorArr);
					}
					message.success(
						"保存成功，商品变更资料会在几分钟内同步至订单中"
					);
					const _dataSource = _.cloneDeep(dataSource);
					_dataSource.forEach((d) => {
						const _obj = _updateObj[d.sysItemId];
						if (_obj && d.sysSkuList) {
							d.sysSkuList.forEach((s) => {
								const _skuOuterId = _obj[s.sysSkuId];
								if (_skuOuterId) {
									s.sysSkuOuterId = _skuOuterId;
									s.skuOuterId = _skuOuterId;
								}
							});
						}
					});
					setDataSource(_dataSource);
				})
				.finally(() => {
					setLoading(false);
				});
		},
	};
	// 库存同步单个开启关闭
	const handleChangeSyncStockFlag = (val, item, index) => {
		const {
			sysItemId,
			sysSkuList,
			sysItemAlias,
			syncStockFlag,
			classifyId,
			brandId,
		} = item;
		sendPoint(Pointer.商品_货品档案_开启关闭);
		const _syncStockFlag = val ? 1 : 0;
		sysSkuList[0] = {
			...sysSkuList[0],
			syncStockFlag: _syncStockFlag,
		};
		setDataSource((prevData) => {
			return prevData.map((i) => {
				if (i.rowId === item.rowId) {
					return {
						...i,
						switchLoading: true,
					};
				}
				return i;
			});
		});
		const {
			sysSkuId,
			skuOuterId,
			isCombination,
			sort,
			groupCombinationList,
		} = sysSkuList[0];
		const _sysSkuList: ItemSysItemSaveRequest["sysSkuList"] = [
			{
				sysSkuId,
				skuOuterId,
				isCombination,
				sort,
				syncStockFlag: _syncStockFlag,
				sysItemId,
			},
		];
		if (isCombination)
			_sysSkuList[0].groupCombinationList = groupCombinationList;
		const params: ItemSysItemSaveRequest = {
			sysItemId,
			sysItemAlias,
			sysSkuList: _sysSkuList,
			sysItemSaveType: archivesType == ArchivesTypeEnum.组合 ? 1 : 0,
			classifyId,
			brandId,
		};
		ItemSysItemSaveApi(params)
			.then(() => {
				message.success("操作成功");
				setDataSource((prevData) => {
					return prevData.map((i) => {
						if (i.rowId === item.rowId) {
							return {
								...i,
								syncStockFlag: _syncStockFlag,
								switchLoading: false,
							};
						}
						return i;
					});
				});
			})
			.finally(() => { });
	};

	const handleSingleChange = async({ row, sysSkuList, field, text }) => {
		let textFormat = text;
		if (field === "weight" && isKg) {
			textFormat = +text * 1000;
		} else if (["price", "tagPrice"].includes(field)) {
			textFormat = (+text).toFixed(2);
		} else if (["sysSkuCostPrice"].includes(field)) {
			textFormat = (+text).toFixed(4);
		} else if (ALLOW_EMPTY_FIELDS.includes(field) && textFormat === undefined) {
			// 对允许为空的字段进行特殊处理
			textFormat = '';
		}
		const params = {
			permission_source: FunctionPermissionEnum.货品批量修改,
			updateSysSkuInfos:[
				{
					sysItemId: row.sysItemId,
					sysSkuIds: [ sysSkuList[0].sysSkuId ]
				}
			]
		};
		params[field] = textFormat;
		await ItemSysItemUpdateSysSkuInfoWithBatchApi(params);
		// 更新单个货品sku信息
		dataSource.forEach((d) => {
			if (d.sysItemId === row.sysItemId && d.sysSkuList) {
				d.sysSkuList.forEach((s) => {
					if (s.sysSkuId === sysSkuList[0]?.sysSkuId) {
						if (["sysSkuCostPrice"].includes(field)) {
							s.costPrice = (+text).toFixed(4);
						} else {
							s[field] = textFormat;
					}}
				});
			}
		});
		setDataSource([...dataSource]);
		message.success("保存成功，商品变更资料会在几分钟内同步至订单中");
	}

	const handleCustomAttrRelationEditOK = (data) => {
		ref?.current.refresh();
		setAssistantTableModalObj({ visible: false, selectedList: [] });
	};
	const handleCustomAttrRelationEditCancel = () => {
		setAssistantTableModalObj({ visible: false, selectedList: [] });
	};

	const onSkuPicConfigChange = (value) => {
		TradeDictInsertDictApi({
			userDictEnum: "SYS_ITEM_PAGE_COLUMN_PIC_CONFIG",
			value: JSON.stringify({ value })
		}).then(() => {
			setSkuPicConfig({value});
		});
	}

	const onSkuPicPreviewConfigChange = (value) => {
		TradeDictInsertDictApi({
			userDictEnum: "SYS_ITEM_PAGE_COLUMN_PIC_PREVIEW_CONFIG",
			value: JSON.stringify({ value })
		}).then(() => {
			setSkuPicPreviewConfig({value});
		});
	}

	const getSkuPicConfig = async() => {
		let config = _.cloneDeep({
			value: "1"
		});
		try {
			let res = await TradeDictQueryDictApi({ userDictEnum: 'SYS_ITEM_PAGE_COLUMN_PIC_CONFIG' });
			config = JSON.parse(res?.value);
		} catch (error) {
			console.log(error, 'error');
		}
		setSkuPicConfig(config);
	};

	const getSkuPicPreviewConfig = async() => {
		let config = _.cloneDeep({
			value: "1"
		});
		try {
			let res = await TradeDictQueryDictApi({ userDictEnum: 'SYS_ITEM_PAGE_COLUMN_PIC_PREVIEW_CONFIG' });
			config = JSON.parse(res?.value);
		} catch (error) {
			console.log(error, 'error');
		}
		setSkuPicPreviewConfig(config);
	};

	// 获取系统货品、系统规格视角下的查询条件
	const getQueryConfig = async(type) => {
		let list = _.cloneDeep([]);
		let defaultList = _.cloneDeep([]);
		try {
			let res = await TradeDictQueryDictApi({ userDictEnum: IQueryDictType[type] });
			list = JSON.parse(res?.value);
			defaultList = JSON.parse(res?.[QueryDefault[type]]);

			// 后端没有改的情况下临时使用本地配置
			// // 创建本地默认配置的映射，用 key 作为唯一标识
			// const localConfigMap = new Map(
			// 	defaultFormFieldList.map(item => [item.key, item])
			// );

			// // 过滤和更新接口返回的配置（本地删除的过滤掉）
			// list = list.filter(item => localConfigMap.has(item.key))
			// 	.map(item => {
			// 		const localItem = localConfigMap.get(item.key);
			// 		return {
			// 			...item,
			// 			name: localItem.name, // 使用本地配置的 name
			// 		};
			// 	});

			// // 添加接口中没有但本地配置中有的项
			// const existingKeys = new Set(list.map(item => item.key));
			// const missingItems = defaultFormFieldList.filter(item => !existingKeys.has(item.key))
			// 	.map(item => ({
			// 		...item,
			// 		index: list.length + 1
			// 	}));

			// list = [...list, ...missingItems];
			// defaultList = [...defaultList, ...missingItems];

		} catch (error) {
			console.log(error, 'error');
		}

		console.log('%c [ 接口返回的查询项配置 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', defaultList, list)
		setDefaultQueryConfig([...defaultList]);
		setQueryList([...list]);
	};

	const handleQueryReset = () => {
		TradeDictInsertDictApi({
			userDictEnum: IQueryDictType[archivesType],
			value: JSON.stringify(defaultQueryConfig),
		}).then(() => {
			setQueryList(defaultQueryConfig);
		});
	};

	const handleQuerySortChange = (list) => {
		TradeDictInsertDictApi({
			userDictEnum: IQueryDictType[archivesType],
			value: JSON.stringify(list)
		}).then(() => {
			setQueryList([...list]);
		});
	};

	const additionalFormNode = useMemo(() => (
		<div className="r-ml-8">
		  {archivesType != ArchivesTypeEnum.组合 && (
			<SearchSetting
			  colSortList={queryList}
			  handleQueryReset={handleQueryReset}
			  handleQuerySortChange={handleQuerySortChange}
			/>
		  )}
		  <Checkbox
			className="r-ml-14"
			checked={barCodeRepeatFlag}
			onChange={(e) => setBarCodeRepeatFlag(e.target.checked)}
		  >
			<span style={{ whiteSpace: 'nowrap' }}>条形码重复</span>
		  </Checkbox>
		</div>
	  ), [archivesType, queryList, barCodeRepeatFlag, handleQueryReset, handleQuerySortChange]);

	return (
		<NormalLayout className={cs(s.systemContainer)}>
			<div className={cs(s.systemTable)}>
				<SearchTable<SysItem>
					pageSizeId="archivesTable"
					ref={ref}
					form={form}
					fetchData={fetchSystemList}
					responseAdapter={responseAdapter}
					searchBtnText="查询"
					searchBtnPoint={Pointer.商品_普通货品档案_查询}
					resetBtnText="重置"
					showSearch
					showSearchToggle
					additionalFormNode={additionalFormNode}
					onReset={() => {
						setBarCodeRepeatFlag(false);
					}}
					rowFormConfig={{
						formList: FormField,
						colProps: {},
					}}
					baseTableConfig={{
						noGap: true,
						stickyTop: 152,
						headerColSet: {
							useDrawer: true,
							resizeId: `Archives_width_${userStore?.userInfo?.userId}_${userStore?.userInfo?.subUserId}`,
							onResizeChange: handleResizeStop,
							initMap: widthMap,
						},
						isStickyHeader: true,
						dataSource: tableRowDataSource,
						rowKey: "rowId",
						groupId: "groupId",
						columns: getColumns,
						pagination: false,
						cachePgination: true,
						loading,
						expandContext: getExpandContext(),
						tableExpandContext: getTableExpandContext,
						innerTableStyle: cs(s.innerTable),
						scroll: {
							scrollToFirstRowOnChange: true,
						},
						components: {
							header: {
								cell: ResizableTitle,
							},
						},
						onRow: (record, index) => {
							return {
								onClick: debounce((e) => {
									if (isValidTableRowClick(e)) {
										// let _selectedRowKeys = [...selectedRows];
										// e.target.checked = !_selectedRowKeys.includes(record.sysItemId);
										onCheckedItem(e, record, true);
									}
								}, 200),
							};
						},
					}}
				/>
			</div>
			<Drawer
				className={ s["edit-goods-drawer"] }
				getContainer={document.body}
				visible={!!editSystem}
				bodyStyle={{
					overflowY: "auto",
					maxHeight: "calc(100vh - 60px)",
				}}
				title={`${editSystem?.sysItemId ? "编辑" : "新建"}${archivesType == ArchivesTypeEnum.组合 ? "组合" : "普通"
					}货品`}
				footer={false}
				onClose={() => {
					setEditSystem(null);
				}}
				width={1200}
				destroyOnClose
				maskClosable={false}
			>
				{
					archivesType === ArchivesTypeEnum.单品规格 ?
						<EditSystemSingle
							code={editSystem?.permission_source}
							data={editSystem}
							archivesType={archivesType}
							onCancel={() => {
								setEditSystem(null);
							}}
							onSave={systemRefresh}
						/> : <CreateSystem
							code={editSystem?.permission_source}
							data={editSystem}
							archivesType={archivesType}
							onCancel={() => {
								setEditSystem(null);
							}}
							onSave={systemRefresh}
						/>
				}
			</Drawer>
			<Modal
				centered
				visible={!!recordModalSysSkuId}
				footer={false}
				onCancel={() => {
					setRecordModalSysSkuId(null);
				}}
				width={1000}
				destroyOnClose
				maskClosable={false}
			>
				<SysSkuLogs sysSkuId={recordModalSysSkuId} />
			</Modal>
			{/* 导入货品弹窗 */}
			<BatchImportModal
				visible={importModalVisible}
				onOkAfter={systemRefresh}
				code={FunctionPermissionEnum.导入货品资料}
				onClose={() => {
					setImportModalVisible(false);
				}}
			/>
			{/* 查看组合货品信息 */}
			<SeeModal
				visible={seeModalShow}
				dataSource={seeModalSource}
				handleCancel={onSeeModalHide}
			/>

			{local.get(ItemAsyncGetProgressApi.name) && (
				<ProgressModal
					loopObj={{
						api: ItemAsyncGetProgressApi,
					}}
					modalProps={{
						title: "批量导入商品进度查询",
					}}
				/>
			)}

			{showBatchUpdateSupplier && (
				<BatchUpdateSupplierModal
					onCancel={() => setShowBatchUpdateSupplier(false)}
					onFinish={onBatchEditOK}
				/>
			)}
			{showBatchUpdateClassifyId && (
				<Modal
					centered
					title="批量修改分类"
					visible
					footer={false}
					zIndex={1008}
					onCancel={() => {
						setShowBatchUpdateClassifyId(false);
					}}
					// getContainer={ document.body }
					width={400}
					destroyOnClose
					maskClosable={false}
				>
					<UpdateGroup
						sysItemIdSysSkuIdsMap={sysItemIdSysSkuIdsMap}
						onFinish={onBatchEditOK}
						onCancel={() => {
							setShowBatchUpdateClassifyId(false);
						}}
					/>
				</Modal>
			)}

			{/* 导出Excel */}
			<Modal
				centered
				visible={downloadExcelModalVisible}
				title="导出Excel"
				okText="导出"
				onOk={onDownloadExcel}
				onCancel={onCloseDownloadExcelModal}
				confirmLoading={exportLoading}
				destroyOnClose
				maskClosable={false}
			>
				<div>
					导出类型：
					<Radio.Group
						value={exportType}
						onChange={onChangeExportType}
					>
						<Radio value={1}>导出已勾选</Radio>
						<Radio value={2}>导出查询结果</Radio>
					</Radio.Group>
				</div>
			</Modal>
			<BatchEditModal
				visible={batchEditModalInfo.visible}
				type={batchEditModalInfo.type}
				batchEditModalInfo={batchEditModalInfo}
				onCancel={() => setBatchEditModalInfo({})}
				onOk={onBatchEditOK}
				from="archives"
			/>
			<CustomAttrRelationEditModal
				modalData={assistantTableModalObj}
				onOk={handleCustomAttrRelationEditOK}
				onCancel={handleCustomAttrRelationEditCancel}
			/>
			<ImportSystemModal
				onDone={() => {
					systemResetRefresh();
					setImportSysItemVisible(false);
				}}
				handleCancel={() => {
					setImportSysItemVisible(false);
				}}
				modalVisible={importSysItemVisible}
			/>
			<ImportCombinedSystemModal
				onDone={() => {
					systemResetRefresh();
					setImportCombinedSysItem(false);
				}}
				handleCancel={() => {
					setImportCombinedSysItem(false);
				}}
				modalVisible={importCombinedSysItem}
			/>

			{/* 新增的货位管理弹框 */}
			<StorageLocationModal
				visible={storageLocationVisible}
				onCancel={() => setStorageLocationVisible(false)}
				onOk={handleStorageLocationOk}
			/>
		</NormalLayout>
	);
});
export default SystemArchives;
