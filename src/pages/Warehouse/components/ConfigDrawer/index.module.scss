.configDrawer {
  :global {
    .ant-drawer-header {
      padding: 8px 24px;
      border-bottom: 1px solid #f0f0f0;
    }
    
    .ant-drawer-body {
      padding: 0px;
      border-top: 8px solid #f0f2f5;
      border-bottom: 8px solid #f0f2f5;
      border-left: 8px solid #f0f2f5;
      border-right: 8px solid #f0f2f5;
    }
    
    .ant-drawer-footer {
      border-top: 1px solid #f0f0f0;
      padding: 10px 24px;
    }
  }
}

.drawerTitle {
  font-size: 16px;
  color: #333;
}

.closeIcon {
  cursor: pointer;
  color: rgba(0, 0, 0, 0.45);
  
  &:hover {
    color: rgba(0, 0, 0, 0.85);
  }
}

.tabsContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 1160px;
  
  /* 确保容器本身没有边框 */
  border-bottom: none;
}

.titleTabs {
  flex: 1;
  
  :global {
    .ant-tabs-nav {
      margin-bottom: 0;
      
      /* 移除导航底部边框 */
      &::before {
        display: none !important;
      }
    }
    
    .ant-tabs-tab {
      padding: 8px 0px;
      font-size: 14px;
    }
    
    .ant-tabs-tab-active {
      font-weight: bold;
    }
    
    /* 隐藏Tabs下方的线条 */
    .ant-tabs-ink-bar {
      display: none !important;
    }
    
    /* 隐藏Tabs导航区域的底部边框 */
    .ant-tabs-nav-wrap {
      border-bottom: none !important;
    }
    
    /* 隐藏Tabs内容区域的顶部边框 */
    .ant-tabs-content-holder {
      border-top: none !important;
    }
  }
}

.platformContent {
  padding: 0;
}

.platformTabs {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: #fff;
  
  :global {
    .ant-tabs-nav {
      margin: 0;
      padding-left: 16px;
      
      .ant-tabs-tab {
        padding: 12px 16px;
      }
    }
    
    .ant-tabs-content-holder {
      display: none; // 隐藏Tabs的内容区域，因为我们使用自定义内容
    }
  }
}

.platformForm {
  // padding: 16px 24px;
  overflow-y: auto;
  :global {
    .ant-form-item-label {
      width: 100px !important;
      flex: 0 0 100px !important;
    }
  }
}
.priceExample {
  color: #999;
  font-size: 12px;
  line-height: 1.5;
}
.formSection {
  padding: 24px 24px 12px 24px;
  border-top: 8px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
  
  :global {
    .ant-form-item {
      margin-bottom: 0px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
    
    .ant-form-item-label {
      height: 32px;
      line-height: 32px;
      
      > label {
        height: 32px;
        line-height: 32px;
      }
    }
    
    .ant-form-item-control {
      line-height: 32px;
    }
    
    .ant-radio-wrapper,
    .ant-checkbox-wrapper {
      line-height: 32px;
    }
  }
}

.radioItem {
  line-height: 32px;
  height: 32px;
  margin-bottom: 12px !important;
}

.checkboxItem {
  line-height: 32px;
  height: 32px;
  margin-bottom: 12px;
}

.sectionTitle {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
}

.platformSpecific {
  margin-top: 32px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.formInput {
  height: 32px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  padding: 4px 11px;
}

.formItemRow {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.formLabel {
  width: 100px;
  text-align: right;
  padding-right: 8px;
  color: #666;
}

.formTextarea {
  width: 100%;
  min-height: 80px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  padding: 4px 11px;
  resize: vertical;
}

.uploadArea {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 120px;
  background-color: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 2px;
  cursor: pointer;
  &:hover {
    border-color: #40a9ff;
  }
}

.checkboxGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
  
  label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
  }
}
.tabContent{
  padding: 24px;
}
.imageFormItem{
  margin-bottom: 12px !important;
}
.priceFormItem{
  margin-bottom: 12px !important;
}
.serviceFormItem{
  margin-bottom: 12px !important;
}
.serviceFormItemYel{
  font-family: PingFang SC;
  font-size: 12px;
  font-weight: 500;
  line-height: 20px;
  letter-spacing: normal;
  padding: 1px 4px;
  border-radius: 8px 0px 8px 0px;
  background: linear-gradient(270deg, #F9DF47 0%, #FEF07C 100%);
  color: #613400;
  margin-left: 2px;
}
.priceDecimalContainer {
  background-color: #f9f9f9;
  border-radius: 4px;
  padding: 8px;
  margin-left: 100px;
  width: calc(100% - 100px);
  margin-bottom: 16px;
}

.tiaozhuanItem {
  color: #1890FF;
  margin-left: 8px ;
  cursor: pointer;
}
.orderSettingBox {
  background-color: #f9f9f9;
  padding: 8px 16px;
  border-radius: 4px;
  .radioRow {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
  }
}