import { DownOutlined, ExclamationCircleOutlined, InfoCircleOutlined, QuestionCircleOutlined, RightOutlined } from '@ant-design/icons';
import { Button, Dropdown, Menu, Modal, Select, Tooltip, Popover, InputNumber } from 'antd';
import { useHistory, useLocation } from "react-router-dom";
import React, { useEffect, useMemo, useState } from 'react';
import { observer } from 'mobx-react';
import _ from "lodash";
import cs from "classnames";
import { runInAction } from 'mobx';
import event from '@/libs/event';
import s from '../../index.module.scss';
import c from './index.module.scss';
import { IAbnormalOrderManage, IPackage } from '../../interface';
import { ABNORMAL_MODAL_BUTTON_PROPS, choiceCheckList, IOptBtn, OccupyTradeOptType, optBtn, BQ_PRINT_TYPE } from '../../constants';
import PrintCenterAPI from '@/print/index';
import { tradeStore, editGoodsStore } from '@/stores';
import { IndexSettingGetSenderSettingResponseObj } from '@/types/schemas/user';
import { sendDelivery } from './utils';
import userStore from '@/stores/user';
import { PRINT_MAP, PLAT_HAND, PLAT_TB, PLAT_FXG, PLAT_KS, PLAT_ALI, PLAT_OTHER, GIFT_ALLOW_GENERATORTAG_PLATFORM, PLAT_TM } from '@/constants';
import { getShopStatusInfo } from "@/pages/Warehouse/StockSync/HandStockSync/components/SyncWaresModal";
import memoFn from "@/libs/memorizeFn";
import { formatKddFjrUtil } from '@/utils/print/formatFjrData';
import Pointer from '@/utils/pointTrack/constants';
import AbnormalManageModal from "../AbnormalManageModal";
import { getAbnormalInfo, handleAbnormalOrderChoice } from "../AbnormalManageModal/abnormalOrder";
import { newBtnAddToPrintSet, spArr, isPackCheckDisable } from '../../utils';
import { TradePrintCheckApi } from "@/apis/trade/search";
import { TradeHandTradeBatchDeleteApi } from "@/apis/trade/handOrder";
import { TakeGoodsLabelGenerateApi, ItemSmartExpressClearSmartExpressResultApi, ItemSmartExpressRecalculateSmartExpressApi, ItemSmartExpressManualSmartExpressApi, TradeBatchUpdateStorageApi } from "@/apis/trade/takeGoodsLabel";
import { ItemTakeGoodsLabelGenerateRequest } from "@/types/trade/takeGoodsLabel";
import UnderstockErrorMsgListModal, { GeneratorLabelType } from './components/UnderstockErrorMsgListModal';
import { alreadyGeneratorLabelModal, beforeGeneratorConfirmModal, generatorLabelFinishModal } from './components/generatorLabelUtil';
import sendPoint from '@/utils/pointTrack/sendPoint';
import message from "@/components/message";
import { PrintTypeEnum, TradeChoiceType, PendingStatus, IgnoreStatus, TradeStatus, RefundStatus, SendType } from '@/utils/enum/trade';
import { local } from '@/libs/db';
import { sleep } from '@/utils/util';
import GenerateLabelWarningModal from '@/components-biz/GenerateLabelWarningModal';
import { AbnormalType as TradeAbnormalType } from '@/components-biz/GenerateLabelWarningModal/constants';
import useGetState from '@/utils/hooks/useGetState';
import { AbnormalType } from './constants';
import { dealPlatAsHandPlat, isSourceHand, isSourceScm, transferOtherToHand } from '@/components-biz/ShopListSelect/shopListUtils';
import { pageLoading } from '@/components/PageLoading';
import { TradeTradeDetailGetApi } from '@/apis/trade';
import BatchUpdateTrade from './components/BatchUpdateTrade';
import { checkOrdersWithTemp, checkDeleteHandOrder } from './opt';
import BatchResend from './components/BatchResend';
import BatchAppendSend from './components/BatchAppendSend';
import BatchPrintBIC from './components/BatchPrintBIC';
import BatchSendBIC from './components/BatchSendBIC';
import BatchCreateWave from './components/BatchCreateWave';
import { AbnormalFromType, abnormalModalShow, checkAbnormalOrders, checkAbnormalType, dealCancelOrderChoice } from './checkAbnormalUtil';
import ProcessingModal from '@/components-biz/ProcessingModal';
import ResultModal from '@/components/ResultModal';
import SelectSmartExpressTemplate from '@/components-biz/SelectSmartExpressTemplate';
import bhdStore from '@/stores/report/Bhd';
import FunctionPermissionCheck, { FunctionPermissionEnum } from '@/utils/permissionCheck/functionPermissionCheck';
import { checkShopTemp, preCheckPrintLimit } from '@/utils/print/shopTemp';
import BatchSellerSend from './components/BatchSellerSend';
import SplitOrder from '../SplitOrder';
import SplitOrderClose from '../SplitOrder/close';
import InputSerialNumberModal, { filterOrders } from './components/InputSerialNumberModal';
import { StockStorageGetApi } from "@/apis/warehouse/entrepot";
import StorageResShowModal from './components/StorageResShowModal';
import BatchCancelYdNoModel from '@/components-biz/BatchCancelYdNoModel';
import { manualMergeTradeHelper } from '../ManualMergeTrade/helper';

export interface IBottomComProps {
	list: IPackage[],
	from?: string
}

export interface IFjrInfoMap {
	isUseCommon: boolean
	isUseMore: boolean
	isKddBindFjr: IndexSettingGetSenderSettingResponseObj[]
}

interface ITradeInfos {
	tid?: string;
	orderInfos?: {
		oid?: string;
		numIid?: string;
		skuId?: string;
		num?: number;
		sysItemId?: number;
		sysSkuId?: number;
		ydNo?: string;
		printStatus?: {
			[k: string]: any;
		};
		sellerId?: number;
		sellerNick?: string;
		platform?: string;
		payTime?: string;
		printTime?: string;
		skuOuterId?: string;
		title?: string;
		picUrl?: string;
		[k: string]: any;
	}[];
	[k: string]: any;
}


export interface UnderstockErrorMsgListModalData {
	show: boolean,
	params?: ItemTakeGoodsLabelGenerateRequest,
	understockErrorMsgList?: any[]
}

// TODO event触发方法会有缓存，临时存储值，待优化
let kddFjr: any = {};
let fhdFjr: any = {};

// 打印中心erp共享数据
// @ts-ignore
window.erpData = window.erpData || {};
// TODO 待优化

let labelGeneratorNum = 0;
let noLogisticsType: SendType;
let noLogisticsParams: { exName?: string, exNumber?: string };
const BottomCom = ((props: IBottomComProps) => {
	const { list, from = "" } = props;
	const [quickChoiceList, setQuickChoiceList] = useState([]);
	const [kddFjrInfoMap, setKddFjrInfoMap] = useState([]);
	const [fhdFjrInfoMap, setFhdFjrInfoMap] = useState([]);
	const history = useHistory();
	const {
		setIsShowBatchAddGoodsModal,
		setBatchAddGoodsPackage,
		setIsShowBatchChangeGoodsModal,
		setBatchChangeGoodsPackage,
	} = editGoodsStore;
	const {
		productSetting: {
			showGoodsSelect
		},
		setBatchSupplierSendPackage,
		setIsBatchSupplierSendModal,
		isMergePrint,
		fjrInfoMap,
		setModifySysMemoPackage,
		setIsShowBatchSysMemoModal,
		setModifyMemoPackage,
		setIsShowBatchModifyMemoModal,
		setModifyFlagPackage,
		setIsShowBatchModifyFlagModal,
		setIsShowBatchExportModal,
		setIsBatchCopyTradeInfoModal,
		setBatchCopyTradeInfoPackage,
		kddTempList,
		setSelectedTemp,
		selectedTemp,
		setSelectedTempGroup,
		selectedTempGroup,
		setting,
		tradeAdvanceSetObj,
		tradeListStore: {
			setList,
			checkedCount,
			handleChoiceChange,
			listCheckStatus,
			setTempAbnormalIgnoreKey,
			isBatchResend,
			setIsBatchResend,
			isBatchAppendSend,
			setIsBatchAppendSend,
			isBatchPrintBIC,
			setIsBatchPrintBIC,
			isBatchSendBIC,
			setIsBatchSendBIC,
			deleteBatchTradeSplice,
			setIsBatchCreateWave,
			isBatchCreateWave,
		},
		importExpress,
		goodsTagListStore: {
			getIsFirstSend
		},
		tradeOptStore: {
			updateTradeOccupied,
		},
	} = tradeStore;
	const { shopList } = userStore;
	const [understockErrorMsgListModalData, setIsShowUnderstockErrorMsgListModal] = useState<UnderstockErrorMsgListModalData>({ show: false, params: {} });
	const [abnormalManageModalVisible, setAbnormalManageModalVisible] = useState<boolean>(false);
	const [abnormalManageModalType, setAbnormalManageModalType] = useState<string>("PRINT");
	const [abnormalPrintType, setAbnormalPrintType] = useState<PrintTypeEnum>(PrintTypeEnum.快递单);
	const [orderAbnormalData, setOrderAbnormalData] = useState<object>({ PRINT: {}, SEND: {}, PRESEND: {}, WAVE: {}, });
	const [sendShipType, setSendShipType] = useState(false);
	const [showGenerateLabelWarningModal, setShowGenerateLabelWarningModal] = useState(false);
	const [labelAbnormalObj, setLabelAbnormalObj] = useState({});
	const [allowGenerateType, setAllowGenerateType, getAllowGenerateType] = useGetState({});
	const [processingModalVisible, setProcessingModalVisible] = useState(false);
	const [resultModalVisible, setResultModalVisible] = useState(false);
	const [resultModalTitle, setResultModalTitle] = useState('');
	const [resultModalSuccessList, setResultModalSuccessList] = useState([]);
	const [resultModalFailList, setResultModalFailList] = useState([]);
	const [processingModalPercent, setProcessingModalPercent] = useState(0);
	const [processingModalContent, setProcessingModalContent] = useState("");
	const [selectTemplateModalVisible, setSelectTemplateModalVisible] = useState(false);
	const [sellerSendModalVisible, setSellerSendModalVisible] = useState(false);
	const [inputSerialNumberModalVisible, setInputSerialNumberModalVisible] = useState({ visible: false, list: [] });
	const [activeInfo, setActiveInfo] = useState(null); // 当前激活的分组

	// 生成标签进度
	const [labelGenerateProgress, setLabelGenerateProgress] = useState({ show: false, num: 0, sum: 0 });
	const [userId, setUserId] = useState(null);
	const [isPreShip, setIsPreShip] = useState(false);
	const [preSendMode, setPreSendMode] = useState(false);
	const location = useLocation();
	const [isShowBatchUpdateTrade, setIsShowBatchUpdateTrade] = useState(false);
	const [inputSerialNumberAbnormalModal, setInputSerialNumberAbnormalModal] = useState({ visible: false, canCheckNum: 0, cancelChoiceOrders: [] });
	const [sendStorageModal, setSendStorageModal] = useState<{ visible: boolean, storageId: number, loading: boolean, storageTypeDesc?: string, type?: string, storageName?: string }>({ visible: false, storageId: null, loading: false, storageTypeDesc: null, type: null, storageName: null });
	const [storageInfoList, setStorageInfoList] = useState([]);
	const [storageResShowModal, setStorageResShowModal] = useState({ visible: false, resInfo: {} });

	const [callFrom, setCallFrom] = useState<any>('');

	// 回收单号弹窗
	const [cancelYdNovisible, setCancelYdNovisible] = useState(false);
	const [cancelYdNoData, setCancelYdNoData] = useState({});
	useEffect(() => {
		let newChoiceCheckList = [...choiceCheckList];

		if (!userStore.isStockAllocationVersion) {
			newChoiceCheckList = newChoiceCheckList.filter(i => i.type != TradeChoiceType.勾选有货的);
		} else {
			const aimIndex = newChoiceCheckList.findIndex(i => i.type == TradeChoiceType.勾选有货的);
			newChoiceCheckList[aimIndex].disabled = !showGoodsSelect;
		}

		// 在全选前面插入勾选前N个选项
		const allSelectIndex = newChoiceCheckList.findIndex(i => i.type === TradeChoiceType.全选);
		if (allSelectIndex !== -1) {
			newChoiceCheckList.splice(allSelectIndex, 0, {
				label: '勾选前',
				type: TradeChoiceType.勾选前N个,
				disabled: false,
			});
		}

		setQuickChoiceList(newChoiceCheckList);
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [userStore.hasWaveManagePermission, userStore.isStockAllocationVersion, showGoodsSelect]);


	useEffect(() => {
		userStore.getShopList();
		userStore.getUserInfo().then(async res => {
			const { userId } = res;
			setUserId(userId);
		});

		userStore.getSystemSetting();
		PrintCenterAPI.getBqTempList(BQ_PRINT_TYPE.吊牌);
	}, []);

	useEffect(() => {
		getFjrInfoById(PrintTypeEnum.发货单);
		getFjrInfoById(PrintTypeEnum.快递单);
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [userStore.senderSetting, userStore.invoicesSenderSetting]);

	useEffect(() => {
		event.on('printBatch.printAct', handlePrintAct);
		event.on('tradeList.send.abnormal', abnormalOrderManage);
		return () => {
			event.off('printBatch.printAct', handlePrintAct);
			event.off('tradeList.send.abnormal', abnormalOrderManage);
		};
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [list, selectedTemp, selectedTempGroup]);

	useEffect(() => {
		if (!storageInfoList?.length) {
			StockStorageGetApi({ checkStorageAuthorityFlag: true }).then((res) => {
				if (res && res.length > 0) {
					setStorageInfoList(res);
				}
			});
		}
	}, [sendStorageModal]);

	useEffect(() => {
		getIsFirstSend().then((res) => {
			if (res) setPreSendMode(res);
		});
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, []);

	const customLogPost = (type: string, dataType: string, data: any = {}) => {
		let title = '';
		switch (type) {
			case PrintTypeEnum.快递单:
				title = '订单打印';
				break;
			case PrintTypeEnum.爆款打印:
				title = '爆款打印';
				break;
			default:
				break;
		}
		if (title) {
			window.errorCollection?.customMessageUpload({
				type: `${title}【${dataType}】`,
				data
			});
		} else {
			window.errorCollection?.customMessageUpload({
				type: `${dataType}`,
				data
			});
		}
	};

	const moreChoiceOper = (type: TradeChoiceType) => {
		let choiceItem = choiceCheckList.find((item) => item.type === type);
		if (!choiceItem?.disabled) {
			handleChoiceChange(choiceItem);
		}
	};

	const selectAllPacks = (e: any) => {
		if (listCheckStatus === 0) {
			handleChoiceChange({
				type: TradeChoiceType.半选,
			});
		} else if (listCheckStatus === 1) {
			handleChoiceChange({
				type: TradeChoiceType.不选,
			});
		}
	};

	const handleHalfClick = (e: any) => {
		e.preventDefault();
		handleChoiceChange({
			type: TradeChoiceType.全选,
		});
	};

	/**
	 * 检查订单数据
	 * @param orderList
	 */
	const checkOrdersMust = (orderList: IPackage[], isOpenAbnormalCheck = false) => {
		return new Promise<IPackage[]>((resolve, reject) => {
			for (const item of orderList) {
				// 校验店铺是否异常
				let shop = shopList.find((s: any) => s.sellerId == item.sellerId);
				if (shop) {
					let { disable, statusText } = getShopStatusInfo(shop);
					if (disable) {
						Modal.warning({
							centered: true,
							title: '订单打印异常提醒',
							content: (
								<div className="r-c-warning">{shop.sellerNick}：{statusText}，请先处理店铺异常</div>
							),
							onOk: () => {
								history.push("/shops");
							},
							okText: '前往处理',
						});
						reject();

						window.errorCollection?.customMessageUpload({
							type: `订单打印异常提醒: 请先处理店铺异常`,
							data: {
								shop,
								statusText,
								item,
								isMergePrint
							}
						});
						return;
					}
				}

				if (!isOpenAbnormalCheck) {
					// * 校验订单内是否未选择
					let isNotCheckedOrder = true;
					item.trades?.forEach((trade) => {
						const checkOrders = trade.orders.filter((order) => order.isChecked);
						if (checkOrders.length) {
							isNotCheckedOrder = false;
						}
					});

					if (isNotCheckedOrder) {
						Modal.confirm({
							centered: true,
							title: '订单打印异常提醒',
							content: (
								<div style={ { maxHeight: 600, overflowY: 'auto' } }>
									<p className="r-c-warning" >以下订单不可打印，请勾选宝贝后再试</p>
									<p>{item.togetherId}</p>
								</div>
							),
							...ABNORMAL_MODAL_BUTTON_PROPS,
						});
						reject();

						window.errorCollection?.customMessageUpload({
							type: `订单打印异常提醒: 以下订单不可打印，请勾选宝贝后再试`,
							data: {
								item,
								isMergePrint
							}
						});
						return;
					}
				}
			}

			// todo
			// 只能使用同一平台去打印
			if (!isMergePrint) {
				let noHandList = orderList.filter(o => (o.platform !== PLAT_HAND && o.platform !== PLAT_OTHER));
				let platformFlag = noHandList.some(i => i.platform !== noHandList[0].platform);
				if (platformFlag) {
					Modal.warning({
						centered: true,
						title: '订单打印异常提醒',
						content: (
							<div className="r-c-warning">请选择相同平台的订单打印</div>
						),
						okText: '我知道了'
					});
					reject();
					window.errorCollection?.customMessageUpload({
						type: `订单打印异常提醒: 请选择相同平台的订单打印`,
						data: {
							noHandList,
							isMergePrint
						}
					});
					return;
				}
			}
			// 校验是否有挂起订单
			let hasPending = orderList.filter((item) => item.isPending);
			if (hasPending.length && !isOpenAbnormalCheck) {
				Modal.confirm({
					centered: true,
					title: '订单打印异常提醒',
					content: (
						<div style={ { maxHeight: 600, overflowY: 'auto' } }>
							<p className="r-c-warning" >以下订单不可打印，请取消挂起后再试</p>
							{hasPending.map(item => (
								<p key={ item.togetherId }>{item.togetherId}</p>
							))}
						</div>
					),
					...ABNORMAL_MODAL_BUTTON_PROPS,
				});
				reject();
				window.errorCollection?.customMessageUpload({
					type: `订单打印异常提醒: 以下订单不可打印，请取消挂起后再试`,
					data: {
						hasPending,
						isMergePrint
					}
				});
				return;
			}
			// 检查顺丰加运费选择的模板不是顺丰
			// const hasSfExpressOrder = orderList.filter((item) => item.hasSfExpressService);
			// const ExCode = isMergePrint ? selectedTempGroup.exCode : selectedTemp.ExCode;
			// if (hasSfExpressOrder?.length && ExCode !== 'SF' && !isOpenAbnormalCheck) {
			// 	Modal.confirm({
			// 		centered: true,
			// 		title: '订单打印异常提醒',
			// 		content: (
			// 			<div style={ { maxHeight: 600, overflowY: 'auto' } }>
			// 				<p className="r-c-warning" >以下订单不可打印，该订单买家申请加运费发顺丰订单，请使用顺丰物流打印发货。</p>
			// 				{hasSfExpressOrder.map(item => (
			// 					<p key={ item.togetherId }>{item.togetherId}</p>
			// 				))}
			// 			</div>
			// 		),
			// 		...ABNORMAL_MODAL_BUTTON_PROPS,
			// 	});
			// 	reject();
			// 	window.errorCollection?.customMessageUpload({
			// 		type: `订单打印异常提醒: 以下订单不可打印，该订单买家申请加运费发顺丰订单，请使用顺丰物流打印发货`,
			// 		data: {
			// 			hasSfExpressOrder,
			// 			ExCode,
			// 			isMergePrint
			// 		}
			// 	});
			// 	return;
			// }
			// 未选择订单时无法打印
			if (!orderList.length) {
				Modal.warning({
					centered: true,
					title: '订单打印异常提醒',
					content: (
						<div className="r-c-warning">请先选择订单</div>
					),
					okText: '我知道了',
					cancelText: '取消'
				});
				reject();
				window.errorCollection?.customMessageUpload({
					type: `订单打印异常提醒: 请先选择订单`,
					data: {
						isMergePrint
					}
				});
				return;
			}

			resolve(orderList);
		});
	};
	// 打印前校验
	const checkAbnormalAddress = async(printType: string, filterOrders: IPackage[]) => {
		return new Promise<IPackage[]>((resolve, reject) => {
			try {
				const func = async() => {
					// 将获得的列表进行校验
					const abnormalTradeInfoObj: any = await checkAbnormalOrders(filterOrders, undefined, printType);
					console.log('abnormalTradeInfoObj', abnormalTradeInfoObj);
					console.log('将获得的列表进行校验', abnormalTradeInfoObj, filterOrders);
					if (abnormalTradeInfoObj) {
						pageLoading.destroy();
						const modalRes = await abnormalModalShow(abnormalTradeInfoObj);
						dealCancelOrderChoice(abnormalTradeInfoObj, modalRes);
						// 跳过异常，继续打印
						if (modalRes) {
							// 取消勾选后检查是否还有订单
							let orderList = filterOrders?.filter(item => item.isChecked) || [];
							orderList = await checkOrdersMust(orderList);
							if (!orderList) {
								reject();
								window.errorCollection?.customMessageUpload({
									type: `打印前校验: 取消异常勾选后没有订单`,
									data: {
										filterOrders,
										printType
									}
								});
							} else {
								resolve(orderList);
							}
						} else {
							console.log('取消订单');
							window.errorCollection?.customMessageUpload({
								type: `打印前校验: 异常弹框，取消订单打印`,
								data: {
									filterOrders,
									printType
								}
							});
							reject();
						}
					} else {
						resolve(filterOrders);
					}
				};
				func();

			} catch (error) {
				console.log('checkAbnormalAddress error:', error);
				reject(error);
				window.errorCollection?.customMessageUpload({
					type: `打印前校验: checkAbnormalAddress error`,
					data: {
						error,
						printType
					}
				});
			}
		});
	};

	/**
	 * 检查订单异常
	 * @param orderList
	 * @param printType 具体打印类型
	 */
	const abnormalOrderManage = async(params: IAbnormalOrderManage, printType?: PrintTypeEnum,) => {
		const { pathname } = history.location;
		// 当前是否是爆款标签打印页面
		const isHotGoodsScanPrintPage = pathname === "/trade/hotGoodsScanPrint";
		// 当前是否是订单打印页面
		const isTradeIndexPrintPage = pathname === "/trade/index";

		if (isHotGoodsScanPrintPage && from !== "hotGoodsScanPrintPage") {
			return;
		}
		if (isTradeIndexPrintPage && from !== "") {
			return;
		}
		pageLoading.loading();

		noLogisticsType = params.noLogisticsType;

		noLogisticsParams = params.noLogisticsParams;
		const { orderList, type = AbnormalFromType.打印, isNoLogistics = false, isPreShip = false, togetherId = "", afterPrint = false, templateInfo, callFrom } = params;
		let { hasAbnormal, abnormalInfos } = await getAbnormalInfo({ orderList, type, isNoLogistics, isPreShip, afterPrint, templateInfo });
		const { extraSetting }: any = userStore.userSetting;

		// 获得改地址订单 通过接口获取其他异常信息
		const abnormalTradeInfoObj: any = await checkAbnormalOrders(orderList, type, printType);

		(() => {
			// 从异常接口处拿到的异常，加入到开启异常检测的弹窗显示中,
			// addGoodsNoSend是否存在待发货的新增商品, modifiedOnline线上改商品异常, scmOrderNotExist分销订单不存在，scmRelationAbnormal分销关系异常
			const otherAbnormalKey = [
				checkAbnormalType.供分销关系解绑,
				checkAbnormalType.分销店铺解绑,
				checkAbnormalType.分销订单发货,
				checkAbnormalType.分销订单退款,
				checkAbnormalType.订单状态异常,
				checkAbnormalType.未填写商品识别码,
				checkAbnormalType.承诺日达,
				checkAbnormalType.分销订单不存在,
				checkAbnormalType.分销关系异常,
				checkAbnormalType.线上改商品,
				checkAbnormalType.有待发货的系统商品,
				checkAbnormalType.存在已发货订单商品,
				checkAbnormalType.存在已打印订单商品,
				checkAbnormalType.换货手工单对应的售后单异常,
				checkAbnormalType.换货手工单对应的售后单异常中断
			];
			console.log('otherAbnormalKey', otherAbnormalKey, abnormalTradeInfoObj);

			otherAbnormalKey.forEach(key => {
				if (abnormalTradeInfoObj?.[key]) {
					let abnormalList = abnormalTradeInfoObj[key]?.split(',');
					abnormalInfos[type][key] = {
						togetherIds: abnormalList,
						num: abnormalList.length
					};
					hasAbnormal = true;
				}
			});
		})();
		if (abnormalTradeInfoObj && abnormalTradeInfoObj.address) {
			let abnormalList = abnormalTradeInfoObj.address.split(',');
			abnormalInfos[type].changeAddress = {
				togetherIds: abnormalList,
				num: abnormalList.length
			};
			hasAbnormal = true;
		}

		if (abnormalTradeInfoObj?.afterSale && extraSetting?.abnormalRefundCheckNew.printBeforeCheck) {
			let abnormalList = abnormalTradeInfoObj.afterSale.split(',');
			const saleType = extraSetting?.abnormalRefundCheckNew?.supportForceAction ? 'refund' : 'refundBreak';
			// 使用Set进行去重处理
			let togetherIds = [...new Set([...abnormalList, ...abnormalInfos[type][saleType].togetherIds])];
			abnormalInfos[type][saleType] = {
				togetherIds,
				num: togetherIds.length
			};
			hasAbnormal = true;
		}
		/** 低毛利润订单配置 */
		const { printBeforeCheck, sendBeforeCheck, waveBeforeCheck } = extraSetting?.abnormalLowProfitTradeConfigCheck || {};
		const isNeedCheckLowProfit = ((type == AbnormalFromType.打印 || type == AbnormalFromType.预发货) && printBeforeCheck) || ((type == AbnormalFromType.发货) && sendBeforeCheck) || ((type == AbnormalFromType.波次生成) && waveBeforeCheck);

		if (abnormalTradeInfoObj?.lowProfit && isNeedCheckLowProfit) {
			const lowProfitType = !extraSetting?.abnormalLowProfitTradeConfigCheck?.supportForceAction ? 'lowProfitBreak' : 'lowProfit';
			let togetherIds = [...new Set([...abnormalTradeInfoObj.lowProfit.split(','), ...abnormalInfos[type][lowProfitType].togetherIds])];
			abnormalInfos[type][lowProfitType] = {
				togetherIds,
				num: togetherIds.length
			};
			hasAbnormal = true;
		}
		console.log('abnormalTradeInfoObj', abnormalTradeInfoObj, abnormalInfos, extraSetting?.abnormalRefundCheckNe);

		if (hasAbnormal) {
			setAbnormalManageModalVisible(hasAbnormal);
			setAbnormalManageModalType(type); // 区分打印、发货、预发货、波次生成
			setAbnormalPrintType(printType); // 保存一份打印类型，区分是快递单 = 'kdd',发货单 = 'fhd',运单号 = 'ydh',爆款打印 = 'scanPrintKdd',
			setOrderAbnormalData(abnormalInfos);
			setSendShipType(isNoLogistics);
			setIsPreShip(isPreShip);
			setCallFrom(callFrom);
			// 处理异常订单的选中
			const { allTogetherIds, breakTogetherIds } = handleAbnormalOrderChoice(abnormalInfos, type);
			handleChoiceChange({ type: TradeChoiceType.取消勾选指定订单, cancelChoiceOrders: allTogetherIds });
			customLogPost(printType, '检查订单异常：处理异常订单的选中', {
				printType,
				allTogetherIds,
				abnormalInfos,
				type
			});
		}
		if (type === AbnormalFromType.发货 && !hasAbnormal) {

			sendDelivery({
				isSendAbnormal: true,
				isNoLogistics,
				isPreShip,
				togetherId,
				noLogisticsType,
				noLogisticsParams,
				callFrom,
			});
		}

		// 没有异常，把结果回调出去
		if (type === AbnormalFromType.波次生成 && !hasAbnormal) {
			let tempLate = isMergePrint ? selectedTempGroup : selectedTemp;
			event.emit('wave.abnormal.result', { isBreak: false, orderList, tempLate });
		}
		pageLoading.destroy();

		if (hasAbnormal) {
			customLogPost(printType, '检查订单异常：abnormalOrderManage', {
				printType,
				isHotGoodsScanPrintPage,
				isTradeIndexPrintPage,
				from,
				abnormalInfos,
				abnormalTradeInfoObj,
				type,
			});
		}

		return hasAbnormal;
	};

	// 关闭异常弹窗
	const onCloseAbnormalManageModal = () => {
		setAbnormalManageModalVisible(false);
	};

	/**
	 * 点击跳过异常 直接打印
	 * @param ignoreList 弹窗内已忽略异常的订单togetherId数组
	 */
	const onSubmitAbnormalOrders = (data?: [], hasRefundIds?: boolean) => {

		let orderList: IPackage[] = list?.filter(item => item.isChecked) || [];

		if (!orderList.length) {
			let warnText = '发货';
			switch (abnormalManageModalType) {
				case 'PRINT':
					warnText = '打印';
					break;
				case AbnormalFromType.波次生成:
					warnText = '生成波次';
					event.emit('wave.abnormal.result', { isBreak: true });
					break;

				default:
					break;
			}
			Modal.warning({
				centered: true,
				title: '系统提示',
				content: (
					<div className="r-c-warning">勾选订单均为异常订单，请核验订单后再操作{warnText}！</div>
				),
				okText: '我知道了',
				cancelText: '取消'
			});
			return;
		}
		if (abnormalManageModalType === 'PRINT') {
			let tempLate = isMergePrint ? selectedTempGroup : selectedTemp;

			onHandlePrint(abnormalPrintType, orderList, tempLate); // 传入具体打印类型
		} else if (abnormalManageModalType === 'PRESEND') {
			let tempLate = isMergePrint ? selectedTempGroup : selectedTemp;
			onHandlePrint(PrintTypeEnum.运单号, orderList, tempLate);
		} else if (abnormalManageModalType === 'SEND') {

			sendDelivery({
				isSendAbnormal: true,
				isNoLogistics: sendShipType,
				isPreShip,
				noLogisticsType,
				noLogisticsParams,
				// 是否有忽略退款中订单
				hasRefundIds,
				callFrom,
			});
		} else if (abnormalManageModalType === AbnormalFromType.波次生成) {
			let tempLate = isMergePrint ? selectedTempGroup : selectedTemp;
			event.emit('wave.abnormal.result', { isBreak: false, orderList, tempLate });
		}
	};
	/**
	 * 打印中心-订单数据收集
	 * @param printType
	 * @param isCheckAbnormal 用于处理选择快递模版后再次调用此方法时 重复检查订单异常
	 */
	const handlePrintAct = async(params: {
		printType?: PrintTypeEnum,
		singleOrder?: IPackage,
		templateInfo?: any,
		isCheckAbnormal?: boolean,
	}) => {
		// * 发货单无需校验
		const { isMergePrint } = tradeStore;
		const { printType, singleOrder, templateInfo, isCheckAbnormal = true } = params;

		let printTypeCheck = [PrintTypeEnum.快递单, PrintTypeEnum.运单号, PrintTypeEnum.爆款打印].includes(printType);
		try {
			let orderList: IPackage[] = [];
			if (singleOrder) {
				orderList = [singleOrder];
			} else {
				orderList = list?.filter(item => item.isChecked) || [];
			}
			if (orderList?.length === 0) {
				message.warning('请选择需要打印的订单');
				return;
			}
			let tempLate = null;
			tempLate = templateInfo || selectedTemp;
			if (printTypeCheck && isMergePrint && !templateInfo) {
				tempLate = selectedTempGroup;
			}
			let isChooseTemplate = isMergePrint ? !!tempLate.userTemplateList : !!tempLate.KddType;
			// 业务逻辑调整 未选模板先去选择模板,验货发货不需要校验，因为页面已经做了校验
			if (!isChooseTemplate && printTypeCheck) {
				const res = await PrintCenterAPI?.chooseTempalteDialog(isMergePrint, kddTempList, orderList);
				let temp;
				if (isMergePrint) {
					temp = kddTempList.find((i) => i.id === res.id);
					setSelectedTempGroup(temp);
				} else {
					temp = kddTempList.find((i) => i.Mode_ListShowId === res.Mode_ListShowId);
					setSelectedTemp(temp);
				}
				// * hack 可达
				await sleep(200);
				tempLate = temp;
			}
			// 检查订单模板是否匹配，并进行过滤
			if (printTypeCheck && ((isMergePrint && !!tempLate.userTemplateList) || !!tempLate.KddType)) {
				let filterOrdersWithTemp = await checkOrdersWithTemp(orderList, tempLate);
				if (!filterOrdersWithTemp) {
					customLogPost(printType, '检查订单模板是否匹配，并进行过滤: checkOrdersWithTemp error', {
						printType,
						isMergePrint,
						tempLate
					});
					return;
				}
				orderList = filterOrdersWithTemp as IPackage[]; // 跳过订单平台和模板不一致的订单继续打印
			}

			// 手动导入单号拦截
			if (printTypeCheck && +tempLate.Exid === -901) {
				message.warning('手动导入单号无法打印快递单，请调整后再试');
				customLogPost(printType, '手动导入单号无法打印快递单，请调整后再试', {
					printTypeCheck,
					printType,
					isMergePrint,
					tempLate
				});
				return;
			}

			let { userSetting }: any = userStore;
			const extraSetting = userSetting?.extraSetting;
			console.log('extraSettingextraSetting', extraSetting);

			let isOpenAbnormalCheck = printTypeCheck && userSetting?.openAbnormalCheck === 0;
			// 检查打印必要条件是否满足
			let filterOrders = await checkOrdersMust(orderList, isOpenAbnormalCheck);
			if (!filterOrders) {
				customLogPost(printType, '检查打印必要条件是否满足: checkOrdersMust error', {
					isOpenAbnormalCheck,
					printType,
					isMergePrint,
				});
				return;
			}

			orderList = filterOrders as IPackage[];

			const { tradeListStore: {
				exReachProgessObj: {
					completeCount,
					totalCount
				},
				updateExReachProgress,
				updatePrintActQueue,
			} } = tradeStore;

			if (totalCount > 0 && completeCount !== totalCount && userSetting?.openAbnormalCheck === 0 && userSetting?.abnormalExpressArriveCheck === 1) {
				updateExReachProgress({
					showProgressModal: true,
				});
				console.log('订单还未完全加载完成');
				pageLoading.loading();
				updatePrintActQueue([() => {
					handlePrintAct({ printType, singleOrder, templateInfo: tempLate, isCheckAbnormal });
				}]);
				customLogPost(printType, '订单还未完全加载完成', {
					totalCount,
					completeCount,
					userSetting,
					printType,
					singleOrder,
					isCheckAbnormal,
					isMergePrint,
					tempLate
				});
				return;
			}
			console.log('======continue======', isOpenAbnormalCheck, isCheckAbnormal, totalCount, completeCount);
			// 开启异常订单检测的 将走异常订单检测逻辑
			if (isOpenAbnormalCheck && isCheckAbnormal) {
				// 检查订单异常
				pageLoading.loading();
				let _abnormal: IAbnormalOrderManage = { orderList, templateInfo: tempLate };
				printType === PrintTypeEnum.运单号 && (_abnormal.type = AbnormalFromType.预发货);
				let isBreak = await abnormalOrderManage(_abnormal, printType);
				if (isBreak) {
					// 中断打印流程 处理异常
					pageLoading.destroy();
					return;
				}
			} else {
				// 地址异常校验
				pageLoading.loading();
				orderList = await checkAbnormalAddress(printType, orderList) || orderList;
			}
			pageLoading.destroy();
			onHandlePrint(printType, orderList, tempLate);
		} catch (error) {
			pageLoading.destroy();
			console.log("打印快递单error:", error);
			customLogPost(printType, '打印快递单校验异常 handlePrintAct error', {
				printType,
				isMergePrint,
				error
			});
		}

	};
	const onHandlePrint = (printType: string, orderList: IPackage[], tempLate: any) => {
		console.log("onHandlePrint orderList:::", orderList, printType);
		customLogPost(printType, '点击打印 handlePrintAct', {
			printType,
			togetherIds: orderList?.map(item => item.togetherId),
		});

		switch (printType) {
			case PrintTypeEnum.发货单:
				PrintCenterAPI.batchPrintFhd({
					fjrInfoMap: {
						fhdFjrMap: {
							"isUseCommon": false,
							"isUseMore": true,
							"senderInfo": fhdFjr
						},
					},
					orderList,
					temp: tempLate
				});
				break;
			case PrintTypeEnum.快递单:
				pageLoading.loading(false, 999);
				PrintCenterAPI.batchPrintKdd({
					fjrInfoMap: {
						kddFjrMap: {
							isUseCommon: false,
							isUseMore: true,
							isKddBindFjr: true,
							senderInfo: kddFjr
						},
						// 模板绑定发件人
						userIdList: orderList.map((i) => i.sellerId)
					},
					fhdFjrInfoMap: {
						fhdFjrMap: {
							"isUseCommon": false,
							"isUseMore": true,
							"senderInfo": fhdFjr
						},
					},
					orderList,
					temp: tempLate,
					printType,
					// pathname: location.pathname
				});
				break;
			case PrintTypeEnum.爆款打印:
				// 改为批打一样的方法
				PrintCenterAPI.batchPrintKdd({
					// 发件人信息
					scanFjrInfo: {
						isUseMore: false,
						isUseCommon: true,
						sender: local.get('hotGoodsScanPrintSearchSetting').senderInfo
					},
					fhdFjrInfoMap: {
						fhdFjrMap: {
							"isUseCommon": false,
							"isUseMore": true,
							"senderInfo": fhdFjr
						},
					},
					orderList, // 打印订单数据
					temp: tempLate,
					printType,
					choosedPrinter: local.get('hotGoodsScanPrintSearchSetting').printer, // 新增：打印机
					isMerge: isMergePrint, // 新增：融合打印
				});
				break;
			case PrintTypeEnum.运单号:
				PrintCenterAPI.batchPrintKdd({
					fjrInfoMap: {
						kddFjrMap: {
							isUseCommon: false,
							isUseMore: true,
							isKddBindFjr: true,
							senderInfo: kddFjr
						},
						// 模板绑定发件人
						userIdList: orderList.map((i) => i.sellerId)
					},
					fhdFjrInfoMap: {
						fhdFjrMap: {
							"isUseCommon": false,
							"isUseMore": true,
							"senderInfo": fhdFjr
						},
					},
					orderList,
					temp: tempLate,
					printType,
					firstSendMode: true
				});
				break;
			default:
				break;
		}
	};

	// 根据id获取发件人信息
	const getFjrInfoById = async(type: string) => {
		// console.log('getFjrInfoById');

		switch (type) {
			case PrintTypeEnum.发货单: {
				const { list: fhdFJRList } = await userStore.getInvoicesSenderSetting();
				setFhdFjrInfoMap(fhdFJRList);
				break;
			}

			case PrintTypeEnum.快递单: {
				const { list: kddFJRList } = await userStore.getSenderSetting();
				setKddFjrInfoMap(kddFJRList);
				break;
			}

			default:
				break;
		}
	};

	/**
	 * 格式化快递单发件人 用于打印中心
	 */
	// TODO 数据格式化方法抽离
	// TODO event消息缓存优化
	useEffect(() => {
		let senderInfo: any = [];
		let defaultFjr = fhdFjrInfoMap?.find((sItem) => sItem.isDefault);
		let defaultFjrData: any = [];
		if (defaultFjr && defaultFjr.enableStatus) {
			defaultFjrData = {
				"id": defaultFjr.id,
				"invoiceMemo": defaultFjr.tips,
				"invoiceOperator": defaultFjr.manager,
				"invoiceStore": defaultFjr.shopName,
				"invoiceTitle": defaultFjr.shippingTitle,
				"phone": defaultFjr.mobile,
				"address": defaultFjr?.senderAddress
			};
		}
		console.log(defaultFjr, 'defaultFjrdefaultFjr');
		shopList?.forEach((i) => {
			let list: any = [];
			let obj = {
				"address": "",
				"city": "",
				"district": "",
				"list": list,
				"taobaoNick": i.sellerNick,
				"userId": i.sellerId
			};
			let ins: any = {};
			// TODO 待优化逻辑
			// 使用默认发件人
			if (!i.invoicesSenderSetting.length) {
				// ins = fhdFjrInfoMap?.find((sItem) => sItem.isDefault);
				if (defaultFjr && defaultFjr.enableStatus) {
					list.push(defaultFjrData);
				}
			} else {
				i.invoicesSenderSetting?.forEach((sid) => {
					ins = fhdFjrInfoMap?.find((sItem) => sItem.id == sid);
					if (ins && ins.enableStatus) {
						list.push({
							"id": ins.id,
							"invoiceMemo": ins.tips,
							"invoiceOperator": ins.manager,
							"invoiceStore": ins.shopName,
							"invoiceTitle": ins.shippingTitle,
							"phone": ins.mobile,
							"address": ins?.senderAddress
						});
					}
				});
			}
			senderInfo.push(obj);
		});
		fhdFjr = [...senderInfo, {
			list: [defaultFjrData],
			"taobaoNick": '',
			"userId": userId // 手工单使用
		}];
		// console.log(fhdFjr, 'fhdFjr');
	}, [fhdFjrInfoMap, shopList, userId]);

	/**
	 * 格式化发货单发件人 用于打印中心
	 */
	useEffect(() => {
		let { senderInfo, allList } = formatKddFjrUtil({ shopList, kddFjrInfoMap });

		// taobaoNick 暂时不需要传
		// 模板绑定发件人
		fjrInfoMap.kdd = [
			{
				list: allList,
				"taobaoNick": '',
				"userId": ''
			}
		];
		// 模板绑定发件人
		// @ts-ignore
		window.erpData.allKddFjr = fjrInfoMap.kdd;
		// 模板绑定发件人需传入
		kddFjr = [...senderInfo, {
			list: allList,
			"taobaoNick": '',
			"userId": userId // 手工单使用
		}];
		// console.log(kddFjr, 'kddFjr');
	}, [fjrInfoMap, kddFjrInfoMap, shopList, userId]);

	useEffect(() => {
		window.erpData.allFhdFjr = fhdFjrInfoMap;
	}, [fhdFjrInfoMap]);


	const [selectFirstNValue, setSelectFirstNValue] = useState();

	const handleSelectFirstN = (value: number) => {
		handleChoiceChange({
			type: TradeChoiceType.勾选前N个,
			selectCount: value
		});
	};

	const menu = useMemo(() => {
		return (
			<Menu className="r-flex r-fd-c">
				<div className={ s["quick-choice-container"] }>
					{quickChoiceList.map((item) => (
						<div
							data-point={ item.point }
							key={ item.label }
							style={ { color: item.disabled ? '#999' : '' } }
							className={ `${s["quick-choice-item"]} quick-choice-item ${item.type === TradeChoiceType.勾选前N个 ? '' : 'r-pointer'}` }
							onClick={ item.type === TradeChoiceType.勾选前N个 ? undefined : () => { moreChoiceOper(item.type); } }
						>
							{item.type === TradeChoiceType.勾选前N个 ? (
								<div className="r-flex r-ai-c">
									<span className="r-mr-8">{item.label}</span>
									<InputNumber
										controls={ false }
										size="small"
										value={ selectFirstNValue }
										min={ 1 }
										max={ list.length }
										onChange={ (value) => setSelectFirstNValue(value) }
										onBlur={ (e) => {
											console.log(e.target.value, Number(e.target.value), 'onblur');
											const value = Number(e.target.value);
											if (!value) {
												handleSelectFirstN(0);
												return;
											}
											if (value < 1) {
												handleSelectFirstN(1);
											} else if (value > list.length) {
												handleSelectFirstN(list.length);
											} else if (value) {
												handleSelectFirstN(value);
											}
										} }
										onPressEnter={ (e: any) => {
											console.log(e.target.value, Number(e.target.value), 'onblur');
											const value = Number(e.target.value);
											if (!value) {
												handleSelectFirstN(0);
												return;
											}
											if (value < 1) {
												handleSelectFirstN(1);
											} else if (value > list.length) {
												handleSelectFirstN(list.length);
											} else if (value) {
												handleSelectFirstN(value);
											}
										} }
										style={ { width: '50px' } }
									/>
									个
								</div>
							) : (
								<>
									{item.label}
									{item.tip ? <Tooltip title={ item.tip } className="r-ml-5 r-mt-2" ><QuestionCircleOutlined /></Tooltip> : null}
								</>
							)}
						</div>
					))}
				</div>
			</Menu>
		);
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [quickChoiceList, selectFirstNValue, list?.length]);

	// 处理智选快递的操作结果（清空，重算）
	const handleSmartExpressOptRes = (tidList, resultObj): { errorList: any[], successList: any[] } => {
		const { result, errorMsg } = resultObj;
		const errorList = [];
		const successList = [];
		// 计算处理成功的订单
		// 如果有errorMsg，说明全部都是失败
		if (errorMsg) {
			tidList.forEach(tid => {
				errorList.push({
					tid,
					msg: errorMsg
				});
			});
		} else {
			result?.forEach(res => {
				if (res.success) {
					successList.push(res.tid);
				} else {
					errorList.push({
						tid: res.tid,
						msg: res.msg
					});
				}
			});
		}
		return { errorList, successList };
	};

	const onSelectedeTemplateOk = async(selectedTemplate) => {
		if (selectedTemplate) {
			setSelectTemplateModalVisible(false);
			const selctedPackList = list?.filter(item => item.isChecked) || [];
			setProcessingModalPercent(0);
			setProcessingModalVisible(true);
			setProcessingModalContent("正在批量修改快递");
			const tidList = [];
			selctedPackList.forEach(pack => {
				const tids = pack.togetherId.split('|');
				tidList.push(...tids);
			});
			const clearResult = await ItemSmartExpressManualSmartExpressApi({
				tidList,
				templateName: selectedTemplate.printTemplateName,
				templateId: selectedTemplate.printTemplateId
			}).catch((e) => ({
				errorMsg: "处理异常",
				result: [],
			}));
			if (clearResult.errorMsg) {
				message.error(clearResult.errorMsg);
				setProcessingModalPercent(100);
				setTimeout(() => {
					setProcessingModalVisible(false);
				}, 1000);
				return;
			}
			const { errorList, successList } = handleSmartExpressOptRes(tidList, clearResult);
			await updateTradeList(selctedPackList);
			setProcessingModalPercent(100);
			setTimeout(() => {
				setResultModalTitle("批量修改快递推荐结果");
				setResultModalVisible(true);
				setResultModalFailList(errorList);
				setResultModalSuccessList(successList);
				setProcessingModalVisible(false);
			}, 700);
		} else {
			message.error("请先选择快递模板");
		}
	};

	/**
	 * 检查订单列表中是否包含已生成波次的订单
	 * @param orderList 订单列表
	 * @param operationName 操作名称，用于生成警告信息
	 * @returns 如果包含已生成波次的订单，返回true；否则返回false
	 */
	const checkHasWaveOrders = (orderList: IPackage[], operationName: string): boolean => {
		const hasWaveOrders = orderList.some(pack => Array.isArray(pack.waveNoList) && pack.waveNoList.length > 0);
		if (hasWaveOrders) {
	  		message.warning(`已生成波次的订单不允许${operationName}`);
	  		return true;
		}
		return false;
	};


	const actionMap = {
		batchClose: (checkedList: IPackage[]) => {
			 // 检查是否有已生成波次的订单
			 if (checkHasWaveOrders(checkedList, '关闭')) return;

			SplitOrderClose({ checkedList });
		},
		batchSplit: (checkedList: IPackage[]) => {
			sendPoint(Pointer.批量拆单);
			// 检查是否有已生成波次的订单
			if (checkHasWaveOrders(checkedList, '拆单')) return;

			SplitOrder({
				checkedList,
				isBatch: true
			});
		},
		batchSplitBySubOrder: (checkedList: IPackage[]) => {
			// sendPoint(Pointer.批量拆单);
			// 检查是否有已生成波次的订单
			if (!checkedList?.length) {
				message.warning('请勾选合并订单');
				return;
			}
			if (checkHasWaveOrders(checkedList, '拆单')) return;
			manualMergeTradeHelper.batchSplitTrade(checkedList);
			// SplitOrderBySubOrder({
			// 	checkedList,
			// });
		},
		batchSysMemo: (checkedList: IPackage[]) => {
			setModifySysMemoPackage(checkedList);
			setIsShowBatchSysMemoModal(true);
		},
		sellerSend: (checkedList: IPackage[]) => {
			if (checkedList.some(pack => (pack.platform != PLAT_TB && pack.platform != PLAT_TM && pack.platform != PLAT_ALI) || pack.source == 'HAND')) {
				message.info('不可操作，仅支持淘宝、天猫、1688平台订单');
				return;
			}
			if (checkedList.every(item => item.isPending)) {
				message.warning('请选择需要发货的订单');
				return;
			}
			setSellerSendModalVisible(true);
		},
		buyerGet: (checkedList: IPackage[]) => {
			if (checkedList.some(pack => pack.platform != PLAT_ALI || pack.source == 'HAND')) {
				message.info('不可操作，仅支持1688平台订单');
				return;
			}
			if (checkedList.every(item => item.isPending)) {
				message.warning('请选择需要发货的订单');
				return;
			}
			sendDelivery({
				isSendAbnormal: false,
				isNoLogistics: true,
				noLogisticsType: SendType.买家自提,
			});
		},
		// 批量换商品
		batchChangeGoods: (checkedList: IPackage[]) => {
			if (userStore.isDistributorAccount) {
				message.info('暂不支持编辑');
				return;
			}
			// 其他在下一步过滤
			sendPoint(Pointer.订单_更多操作_批量换商品_点击);

			runInAction(() => {
				setIsShowBatchChangeGoodsModal(true);
				setBatchChangeGoodsPackage(checkedList, false);
			});
		},
		// 批量添加赠品
		batchAddGiftGoods: (checkedList: IPackage[]) => {
			if (userStore.isDistributorAccount) {
				message.info('暂不支持编辑');
				return;
			}
			// 其他在下一步过滤

			sendPoint(Pointer.订单_订单打印_订单操作_添加商品);

			runInAction(() => {
				setIsShowBatchAddGoodsModal(true);
				setBatchAddGoodsPackage(checkedList);
			});
		},
		// 指定供应商发货
		supplierSend: async(checkedList: IPackage[]) => {
			sendPoint(Pointer.订单打印_点击_指定供应商发货_点击次数);
			const hasList = checkedList?.flatMap(pack => pack.trades)?.some(trade => trade.orders.some(order => order.isChecked));
			if (!hasList) {
				message.info('请先选择');
				return;
			}
			if (checkedList.some(pack => pack?.serviceTagList?.includes('bic_order'))) {
				message.info('BIC质检订单不允许操作');
				return;
			}
			// 快团团指定供应商发货由后端拦截
			// if (checkedList.some(pack => pack.platform === PLAT_KTT)) {
			// 	message.info('快团团平台订单不允许操作');
			// 	return;
			// }

			// if (checkedList.some(pack => pack.platform === PLAT_TB && pack?.serviceTagList?.includes('presentOrder'))) {
			// 	message.info('礼物订单暂不支持分销推送');
			// 	return;
			// }
			setBatchSupplierSendPackage(checkedList);
			setIsBatchSupplierSendModal(true);
		},
		// 批量忽略异常-商品未绑定
		batchIgnoreAbnormal: async(checkedList: IPackage[]) => {
			// if (checkedList.some(isSourceScm)) {
			// 	message.info('存在分销推送订单，不支持批量忽略异常');
			// 	return;
			// }
			if (!checkedList.some(pack => pack.noGoodsLink)) {
				message.info('选中的订单没有异常订单');
				return;
			}
			Modal.confirm({
				centered: true,
				title: '系统提示',
				content: (
					<div>
						<p>确定要忽略订单商品关联关系异常吗？</p>
						<p>如果确认忽略，则订单打印发货无法扣减库存商品信息将以平台信息展示</p>
					</div>
				),
				onOk: () => tradeStore.tradeOptStore.batchIgnoreAbnormal(checkedList, '1'),
				okText: '忽略'
			});
		},
		// 批量忽略异常-系统赠品异常
		batchSysGiftAbnormal: async(checkedList: IPackage[]) => {
			// 这里和商品未绑定一样只判断合单层
			let hasAbnormal = checkedList?.some(pack => {
				return pack?.serviceTagList?.includes('sysGiftAbnormal');
			});

			if (!hasAbnormal) {
				message.info('选中的订单没有系统赠品异常订单');
				return;
			}
			Modal.confirm({
				centered: true,
				title: '系统提醒',
				content: (
					<div>
						<p>确定要忽略订单系统赠品异常吗？</p>
						<p>如果确认忽略，可能会导致赠品错发引起的资损问题！</p>
					</div>
				),
				onOk: () => tradeStore.tradeOptStore.batchIgnoreAbnormal(checkedList, '2'),
				okText: '确定'
			});
		},
		markEmpty: (checkedList: IPackage[]) => {
			sendPoint(Pointer.订单_订单打印_订单操作_标记为空单);
			 // 检查是否有已生成波次的订单
			 if (checkHasWaveOrders(checkedList, '标记为空单')) return;

			// 仅待付款、待发货才能空单标记或取消空单标记
			const isAllowMarkEmpty = checkedList.every(pack => pack.trades.every(trade => [TradeStatus.等待买家付款, TradeStatus.等待卖家发货].includes(trade.status as TradeStatus)));
			if (!isAllowMarkEmpty) {
				Modal.confirm({
					title: "提示",
					content: "勾选订单中包含【部分发货、已发货、交易成功、已关闭】状态订单，请取消勾选后重新操作",
					okText: "知道了",
					cancelButtonProps: { hidden: true }
				});
				return;
			}
			if (checkedList.every(item => [IgnoreStatus.自动标记, IgnoreStatus.手动标记].includes(item.trades[0]?.ignoreType))) {
				message.warning('选中订单已经是空包订单');
				return;
			}
			tradeStore.tradeOptStore.batchUpdateIgnore(checkedList, IgnoreStatus.手动标记);
		},
		cancelMarkEmpty: (checkedList: IPackage[]) => {
			sendPoint(Pointer.订单_订单打印_订单操作_取消空单标记);
			// 检查是否有已生成波次的订单
			if (checkHasWaveOrders(checkedList, '取消空单标记')) return;

			// 仅待付款、待发货才能空单标记或取消空单标记
			const isAllowMarkEmpty = checkedList.every(pack => pack.trades.every(trade => [TradeStatus.等待买家付款, TradeStatus.等待卖家发货].includes(trade.status as TradeStatus)));
			if (!isAllowMarkEmpty) {
				Modal.confirm({
					title: "提示",
					content: "勾选订单中包含【部分发货、已发货、交易成功、已关闭】状态订单，请取消勾选后重新操作",
					okText: "知道了",
					cancelButtonProps: { hidden: true }
				});
				return;
			}
			if (checkedList.every(item => [IgnoreStatus.手动取消, IgnoreStatus.非空单].includes(item.trades[0]?.ignoreType))) {
				message.warning('选中订单不是空包订单');
				return;
			}
			tradeStore.tradeOptStore.batchUpdateIgnore(checkedList, IgnoreStatus.手动取消);
		},
		batchModifyMemo: (checkedList: IPackage[]) => {
			if (checkedList.some(isSourceScm)) {
				message.info('存在分销推送订单，不支持批量备注');
				return;
			}
			setModifyMemoPackage(checkedList);
			setIsShowBatchModifyMemoModal(true);
		},
		batchPending: (checkedList: IPackage[]) => {
			 // 检查是否有已生成波次的订单
			 if (checkHasWaveOrders(checkedList, '挂起')) return;

			if (checkedList.every(item => item.isPending)) {
				message.warning('选中订单已是挂起状态');
				return;
			}
			tradeStore.tradeOptStore.handleTradePending(checkedList, PendingStatus.挂起);
		},
		batchCancelPending: (checkedList: IPackage[]) => {
			if (checkedList.every(item => !item.isPending)) {
				message.warning('选中订单不是挂起订单，无需取消挂起');
				return;
			}
			tradeStore.tradeOptStore.handleTradePending(checkedList, PendingStatus.取消挂起);
		},
		batchOrderSellerFlag: (checkedList: IPackage[]) => {
			 // 检查是否有已生成波次的订单
			 if (checkHasWaveOrders(checkedList, '修改本地标记')) return;
			setModifyFlagPackage(checkedList);
			setIsShowBatchModifyFlagModal(true);
		},
		batchExport: () => {
			setIsShowBatchExportModal(true);
		},
		noLogisticsSend: (checkedList: IPackage[]) => {
			sendPoint(Pointer.订单_无需物流发货);
			if (checkedList.every(item => item.isPending)) {
				message.warning('请选择需要发货的订单');
				return;
			}
			// console.log('checkedList', checkedList);
			let platformError: string[] = [];
			let orderStatusError: string[] = [];
			checkedList.forEach(pack => {
				let checkedOrder: any[] = [];
				pack.trades.forEach(trade => {
					checkedOrder = checkedOrder.concat(trade.orders.filter(order => order.isChecked));
					if (checkedOrder.filter(order => (order.refundStatus !== 'NOT_REFUND')
						|| (order.status === 'TRADE_FINISHED' || order.status === 'TRADE_CLOSED' || order.status === 'TRADE_CLOSED_BY_TAOBAO')).length) {
						orderStatusError.push(trade.tid);
					}
				});
				if (![PLAT_TB, PLAT_ALI].includes(pack.platform) && !isSourceHand(pack)) {
					platformError.push(pack.togetherId);
				}
			});
			if (platformError.length) {
				// 平台不对
				Modal.warning({
					title: '提示',
					content: <div>以下订单不支持无需物流发货，目前无需物流发货仅支持手工单、淘宝、1688订单！<br />{platformError.join(',')}</div>,
					okText: '确定'
				});
			} else if (orderStatusError.length) {
				// 订单状态变更
				Modal.warning({
					title: '提示',
					content: <div>以下订单状态发生变化，请刷新后再试<br />{orderStatusError.join(',')}</div>,
					okText: '确定'
				});
			} else {
				// sendDelivery('', false, true);
				sendDelivery({
					isSendAbnormal: false,
					isNoLogistics: true,
				});
			}
		},
		generatorTag: async(checkedList: IPackage[]) => {
			sendPoint(Pointer.订单_生成标签_生成标签);
			// if (checkedList.some(pack => pack.source == PLAT_SCM)) {
			// 	message.info('存在分销推送订单，不支持生成标签');
			// 	return;
			// }
			generatorLabelPrecheck();
		},
		preShip: (checkedList: IPackage[]) => {
			if (checkedList.some(pack => isSourceScm(pack))) {
				message.info('存在分销推送订单，不支持自动发货');
				return;
			}
			sendDelivery({
				isPreShip: true,
			});
		},
		getYdNos: (checkedList: IPackage[]) => {
			sendPoint(Pointer.订单_更多操作_获取运单号);

			// AbnormalManageModal 统一配置了
			// if (checkedList.some(pack => pack?.serviceTagList?.includes('gfcf'))) {
			// 	message.info('官方仓发订单不支持获取运单号');
			// 	return;
			// }
			handlePrintAct({ printType: PrintTypeEnum.运单号 });
		},
		batchOccupyTradeStock: (checkedList: IPackage[]) => {
			updateTradeOccupied(OccupyTradeOptType.占用, checkedList);
		},
		batchFreedTradeStock: (checkedList: IPackage[]) => {
			Modal.confirm({
				title: '您正在批量释放订单占用库存',
				content: <div className="r-c-error">订单库存释放后，库存会回到可用库存，您可选择手动占用库存</div>,
				onOk: () => {
					updateTradeOccupied(OccupyTradeOptType.释放, checkedList);
				}
			});
		},
		generatorReportBhd: (checkedList: IPackage[]) => {
			const tids = checkedList.flatMap(pack => pack.trades).map(trade => trade.tid)?.join(',');
			history.push('/report/bhd/BhdList');
			setTimeout(() => {
				bhdStore.setGenerateReportBhdTids(tids);
			}, 1000);
		},
		batchUpdateTrade: (checkedList: IPackage[]) => {
			sendPoint(Pointer.批量更新订单);
			  // 检查是否有已生成波次的订单
			  if (checkHasWaveOrders(checkedList, '更新')) return;

			setIsShowBatchUpdateTrade(true);
		},
		batchCopyTradeInfo: (checkedList: IPackage[]) => {
			setIsBatchCopyTradeInfoModal(true);
			setBatchCopyTradeInfoPackage(checkedList);
		},
		batchCancelYdNo: (checkedList: IPackage[]) => {
			// 批量单号回收需要选择模板并勾选订单，并且部分快递不允许回收，当前选中的订单无可回收的单号也拦截一下
			// 未选择模板
			if ((isMergePrint && !selectedTempGroup.id) || (!isMergePrint && !selectedTemp.Mode_ListShowId)) {
				message.warning('请选择模板');
				return;
			}
			let hasMoreYdnoNum = 0;
			let allYdnoNum = 0;
			// 选中订单没有任何单号
			let noYdNoList = [];
			let hasYdNoList = [];
			let cancelParams: any = [];
			checkedList.forEach(pack => {
				let sids = pack.sids.filter(sid => sid && sid.trim() && sid !== '打印后生成');
				console.log(sids);

				if (!sids.length) {
					noYdNoList.push(pack);
				} else {
					if (sids.length > 1) hasMoreYdnoNum++;
					allYdnoNum += sids.length;
					hasYdNoList.push(pack);
					let params = {
						taobaoId: pack.sellerId,
						tids: pack.tids[0] || '',
						ydNO: sids.join(','),
					};
					if (isMergePrint) {
						// 聚合打印需要找到匹配的模板
						let handPlatSetting = setting?.groupPrintSetJsonString?.orderMatchSetting || [];

						let orderTemp = selectedTempGroup?.userTemplateList?.find((o: any) => {
							if (dealPlatAsHandPlat(pack.platform, null, pack)) {
								let orderSettingType = handPlatSetting.find(o => o.platform === transferOtherToHand(pack.platform, pack));
								if (orderSettingType == '13') return [13, 16].includes(Number(o.expressType));
								return o.expressType == orderSettingType.bindControlType;
							}
							if (PRINT_MAP[pack.platform] === '13') return [13, 16].includes(Number(o.expressType));
							return o.expressType == PRINT_MAP[pack.platform];
						});
						cancelParams.push({
							...params,
							kdCode: orderTemp.exCode,
							exid: orderTemp.exId,
							kddType: orderTemp.expressType,
						});
					} else {
						cancelParams.push({
							...params,
							kdCode: selectedTemp.ExCode,
							exid: selectedTemp.Exid,
							kddType: selectedTemp.KddType,
						});
					}

				}
			});
			if (!hasYdNoList.length) {
				message.warning('当前勾选订单无可回收单号');
				return;
			}
			// 组装弹窗数据
			let cancelYdNoParamsData = {
				tempName: isMergePrint ? selectedTempGroup.groupName : selectedTemp.ExcodeName,
				tempId: isMergePrint ? selectedTempGroup.id : selectedTemp.Mode_ListShowId,
				allYdnoNum,
				hasMoreYdnoNum,
				checkedList,
				noYdNoList,
				hasYdNoList,
				cancelParams,
				isMergePrint,
			};
			setCancelYdNoData(cancelYdNoParamsData);
			setCancelYdNovisible(true);
		},
		batchResend: () => {
			sendPoint(Pointer.订单_订单操作_重新发货);
			setIsBatchResend(true);
		},
		// 追加包裹
		batchAppendSend: () => {
			console.log('%c [ 追加包裹 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', '追加包裹');
			sendPoint(Pointer.订单_订单操作_追加包裹);
			setIsBatchAppendSend(true);
		},
		clearExpress: async(checkedList: IPackage[]) => {
			sendPoint(Pointer.订单_订单操作_清空快递);
			const tidList = [];
			checkedList.forEach(pack => {
				const tids = pack.togetherId.split('|');
				tidList.push(...tids);
			});
			setProcessingModalPercent(0);
			setProcessingModalVisible(true);
			setProcessingModalContent("正在清空快递");
			const clearResult = await ItemSmartExpressClearSmartExpressResultApi({
				tidList
			}).catch((e) => ({
				errorMsg: "处理异常",
				result: [],
			}));
			if (clearResult.errorMsg) {
				message.error(clearResult.errorMsg);
				setProcessingModalPercent(100);
				setTimeout(() => {
					setProcessingModalVisible(false);
				}, 1000);
				return;
			}
			const { successList } = handleSmartExpressOptRes(tidList, clearResult);
			await updateTradeList(checkedList);
			setProcessingModalPercent(100);
			setTimeout(() => {
				setProcessingModalVisible(false);
				const content = (
					<div>
						成功清空<span className="r-bold" style={ { color: "#52c41a" } }> {successList.length} </span>笔订单
					</div>
				);
				Modal.confirm({
					title: "清空快递",
					centered: true,
					content,
					okText: "我知道了",
					onOk: () => {
						// event.emit(EVENT_BUS.REQUEST_AGAIN);
						return false;
					},
					cancelButtonProps: { hidden: true }
				});
			}, 700);
		},
		batchRecalcExpress: async(checkedList: IPackage[]) => {
			sendPoint(Pointer.订单_订单操作_批量重算快递);
			setProcessingModalPercent(0);
			setProcessingModalVisible(true);
			setProcessingModalContent("正在批量重算快递");
			const tidList = [];
			checkedList.forEach(pack => {
				const tids = pack.togetherId.split('|');
				tidList.push(...tids);
			});
			const clearResult = await ItemSmartExpressRecalculateSmartExpressApi({
				tidList
			}).catch((e) => ({
				errorMsg: "处理异常",
				result: [],
			}));
			if (clearResult.errorMsg) {
				message.error(clearResult.errorMsg);
				setProcessingModalPercent(100);
				setTimeout(() => {
					setProcessingModalVisible(false);
				}, 1000);
				return;
			}
			const { errorList, successList } = handleSmartExpressOptRes(tidList, clearResult);
			// await updateTradeList(checkedList);
			setProcessingModalPercent(100);
			setTimeout(() => {
				setResultModalTitle("批量重算快递");
				setResultModalVisible(true);
				setResultModalFailList(errorList);
				setResultModalSuccessList(successList);
				setProcessingModalVisible(false);
			}, 700);
		},
		assignExpress: async(checkedList: IPackage[]) => {
			sendPoint(Pointer.订单_订单操作_指定快递);
			setSelectTemplateModalVisible(true);
		},
		// 批量删除手工订单
		batchHandTradeDelete: async(checkedList: IPackage[]) => {
			await new Promise((resolve, reject) => {
				Modal.confirm({
					title: "提示",
					content: "请确认是否删除选中的手工单",
					okText: "确认",
					centered: true,
					onOk: () => {
						resolve(true);
					},
					onCancel: () => {
						reject();
					}
				});
			});
			const deleteList = await checkDeleteHandOrder(checkedList);
			const params = (deleteList as any[] || []).map(item => {
				return {
					tid: item.togetherId,
					sellerId: item.sellerId,
					platform: item.platform,
					source: item.source,
				};
			});
			console.log('%c [ 开始删除手工单 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', deleteList);
			const { data } = await TradeHandTradeBatchDeleteApi(params);
			let { failedList = [], successList = [] } = data;
			if (successList?.length > 0) {
				deleteBatchTradeSplice(successList);
			}
			setResultModalTitle("批量删除手工单结果");
			setResultModalVisible(true);
			setResultModalFailList(failedList?.map(item => {
				return {
					tid: item.tid,
					msg: item.errorMsg,
				};
			}));
			setResultModalSuccessList(data?.successList);
		},
		inputSerialNumber: async(checkedList: IPackage[]) => {
			handleInputSerialNumberBefore();
		},
		setSendStorage: () => {
			sendPoint(Pointer.订单_更多操作_点击设置发货仓库);
			setSendStorageModal({ visible: true, storageId: null, loading: false });
		},
		// 打印BIC订单码
		batchPrintBIC: (checkedList: IPackage[]) => {
			console.log('%c [ 打印BIC订单码 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', '');
			sendPoint(Pointer.订单_订单操作_打印订单码);
			setIsBatchPrintBIC(true);
		},
		// BIC订单发货
		batchSendBIC: (checkedList: IPackage[]) => {
			console.log('%c [ BIC订单发货 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', '');
			sendPoint(Pointer.订单_订单操作_BIC订单发货);
			setIsBatchSendBIC(true);
		},
		// 批量生成拣货波次
		batchCreateWave: (checkedList: IPackage[]) => {
			sendPoint(Pointer.订单_操作_生成波次_点击);
			console.log('%c [ 生成拣货波次 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', '');
			setIsBatchCreateWave(true);
		}
	};

	const handleInputSerialNumberBefore = async() => {
		const checkedList: IPackage[] = list?.filter(item => item.isChecked) || [];
		try {
			const _cancelTids = [];
			for (let item of checkedList) {
				let hasCancelTids = false;
				const { source } = item;
				for (let trade of item.trades) {
					for (let order of trade.orders) {
						const isDisabled = ![TradeStatus.等待卖家发货, TradeStatus.等待买家付款].includes(order.status as TradeStatus);
						const disabledPlat = !([PLAT_KS, PLAT_FXG, PLAT_TM, PLAT_TB].includes(order.platform) && source != "HAND");
						if (order?.isChecked && (isDisabled || disabledPlat)) {
							hasCancelTids = true;
						}
					}
				}
				if (hasCancelTids) {
					_cancelTids.push(item?.togetherId);
				}
			}
			if (_cancelTids?.length) {
				setInputSerialNumberAbnormalModal({ visible: true, canCheckNum: (checkedList?.length - _cancelTids?.length), cancelChoiceOrders: _cancelTids });
				return;
			}
			const _checkedList = filterOrders(_.cloneDeep(checkedList));
			if (!_checkedList?.length) {
				message.warning("订单未勾选任何商品");
				return;
			}
			setInputSerialNumberModalVisible({ visible: true, list: _checkedList });
		} catch (error) {
			console.log("handleInputSerialNumberBefore error:", error);

		}


	};

	// 生成标签前的前置校验2.0
	const generatorLabelPrecheck = async() => {
		const abnormalList = []; // 存在异常的订单
		const abnormalOidList = []; // 平铺所有存在异常的子订单，生成标签时，所有的异常子订单会全部取消勾选
		const selectedList: IPackage[] = list?.filter(item => item.isChecked) || [];
		const needValidType = [
			TradeAbnormalType.买家申请退款,
			TradeAbnormalType.交易成功,
			TradeAbnormalType.已关闭,
			TradeAbnormalType.已发货,
			TradeAbnormalType.系统赠品异常,
			TradeAbnormalType.订单正在执行自动策略,
			TradeAbnormalType.列表加载商品数据与数据库商品数据不一致
		];
		const pendingList = []; // 订单状态为挂起
		const generatedList = []; // 已生成标签的商品
		const abnormalObj = {};
		const allTradeGoodsNoChecked = {
			checked: true,
			list: [],
		};
		let abnormalArr: any[] = []; // 收集要请求接口判断异常的参数
		let tradeObjMap = new Map<string, any>();

		needValidType.forEach(type => abnormalObj[type] = {
			type,
			tradeList: [],
			abnormalOrderList: [],
			notAllow: [TradeAbnormalType.订单正在执行自动策略, TradeAbnormalType.列表加载商品数据与数据库商品数据不一致].includes(type), // 是否允许生成
			/**
			 *  abnormalOidList用于把异常的子订单以及子订单的对应的异常状态存储起来，
			 *  因为弹窗中勾选的时候，需要根据子订单的oid以及子订单的异常状态来判断需不需要勾选
			 */
		});
		// 主要用于将异常子订单的状态收集起来
		const handleOrderAbnormalList = (() => {
			const abnormalOrderObj = {};
			return (oid, abnormalType) => {
				if (!abnormalOrderObj[oid]) {
					abnormalOrderObj[oid] = {
						abnormalList: [abnormalType]
					};
				} else {
					abnormalOrderObj[oid].abnormalList.push(abnormalType);
				}
				return abnormalOrderObj;
			};
		})();

		const tradeInfoCheck = (item: any, abnormalKey: any) => {
			let _trade = tradeObjMap.get(item.tid);
			if (_trade) {
				const orderAbnormalList = handleOrderAbnormalList(item.oid, abnormalKey);
				abnormalList.push(_trade.togetherId);
				abnormalOidList.push(item.oid);
				abnormalObj[abnormalKey]?.abnormalOrderList?.push({
					oid: item.oid,
					abnormalList: orderAbnormalList?.[item.oid]?.abnormalList
				});
				abnormalObj[abnormalKey]?.tradeList?.push({
					..._trade,
					togetherId: _trade.togetherId
				});
			}
		};

		selectedList.forEach(tradeListItem => {
			tradeListItem.trades.forEach(trade => {

				// 收集要接口请求异常的参数
				const tradeCheckInfo = {
					sellerId: trade.sellerId,
					platform: trade.platform,
					source: trade.source,
					distributorUserId: tradeListItem.distributorUserId,
					tid: trade.tid,
					storageTime: trade.storageTime,
					ptTid: trade.ptTid,
					modified: trade.modified,
					oidList: trade?.orders?.map(o => o.oid) || [], // 系统赠品判断
					// oidList: [trade?.orders[0].oid], // test
				};
				abnormalArr.push(tradeCheckInfo);
				tradeObjMap.set(trade.tid, { ...trade, togetherId: tradeListItem.togetherId });

				trade.orders.forEach(order => {
					if (order.isChecked) {
						allTradeGoodsNoChecked.checked = false;
						// 判断子单商品是否含有已生成标签的商品
						// if (order.labelstatus === 1) {
						// 	generatedList.push(tradeListItem.togetherId);
						// }
						// 判断订单状态: 是否是已发货,且不是先发货模式
						const isSend = (order.status === TradeStatus.等待买家确认收货 && !order.firstSend) || order.status === TradeStatus.卖家部分发货;
						if (isSend) {
							const orderAbnormalList = handleOrderAbnormalList(order.oid, TradeAbnormalType.已发货);
							abnormalList.push(tradeListItem.togetherId);
							abnormalOidList.push(order.oid);
							abnormalObj[TradeAbnormalType.已发货]?.abnormalOrderList?.push({
								oid: order.oid,
								abnormalList: orderAbnormalList?.[order.oid]?.abnormalList
							});
							abnormalObj[TradeAbnormalType.已发货]?.tradeList?.push({
								...trade,
								togetherId: tradeListItem.togetherId
							});
						}
						// 判断订单状态:是不是交易成功
						const isSuccessed = order.status === TradeStatus.交易成功 || order.status === TradeStatus.买家已签收;
						if (isSuccessed) {
							const orderAbnormalList = handleOrderAbnormalList(order.oid, TradeAbnormalType.交易成功);
							abnormalList.push(tradeListItem.togetherId);
							abnormalOidList.push(order.oid);
							abnormalObj[TradeAbnormalType.交易成功]?.abnormalOrderList?.push({
								oid: order.oid,
								abnormalList: orderAbnormalList?.[order.oid]?.abnormalList
							});
							abnormalObj[TradeAbnormalType.交易成功]?.tradeList?.push({
								...trade,
								togetherId: tradeListItem.togetherId
							});
						}
						// 判断订单状态:是不是交易关闭
						const isClosed = order.status === TradeStatus.交易关闭 || order.status === TradeStatus.交易自动关闭;
						if (isClosed) {
							const orderAbnormalList = handleOrderAbnormalList(order.oid, TradeAbnormalType.已关闭);
							abnormalList.push(tradeListItem.togetherId);
							abnormalOidList.push(order.oid);
							abnormalObj[TradeAbnormalType.已关闭]?.abnormalOrderList?.push({
								oid: order.oid,
								abnormalList: orderAbnormalList?.[order.oid]?.abnormalList
							});
							abnormalObj[TradeAbnormalType.已关闭]?.tradeList?.push({
								...trade,
								togetherId: tradeListItem.togetherId
							});
						}
						// 判断退款状态:是不是有退款
						const hasRefund = RefundStatus.退款中 === order.refundStatus || RefundStatus.退款成功 === order.refundStatus;
						if (hasRefund) {
							const orderAbnormalList = handleOrderAbnormalList(order.oid, TradeAbnormalType.买家申请退款);
							abnormalList.push(tradeListItem.togetherId);
							abnormalOidList.push(order.oid);
							abnormalObj[TradeAbnormalType.买家申请退款]?.abnormalOrderList?.push({
								oid: order.oid,
								abnormalList: orderAbnormalList?.[order.oid]?.abnormalList
							});
							abnormalObj[TradeAbnormalType.买家申请退款]?.tradeList?.push({
								...trade,
								togetherId: tradeListItem.togetherId
							});
						}

						// 判断系统赠品异常
						const hasAbnormalSysGift = order?.isSysGift && order?.sysGiftStatus == 2;
						// const hasAbnormalSysGift = order?.isSysGift; // test
						if (hasAbnormalSysGift) {
							const orderAbnormalList = handleOrderAbnormalList(order.oid, TradeAbnormalType.系统赠品异常);
							abnormalList.push(tradeListItem.togetherId);
							abnormalOidList.push(order.oid);
							abnormalObj[TradeAbnormalType.系统赠品异常]?.abnormalOrderList?.push({
								oid: order.oid,
								abnormalList: orderAbnormalList?.[order.oid]?.abnormalList
							});
							abnormalObj[TradeAbnormalType.系统赠品异常]?.tradeList?.push({
								...trade,
								togetherId: tradeListItem.togetherId
							});
						}

					} else { // 如果订单没有勾选商品，先把收货人姓名存下来。等到假如所有的订单都没有勾选上商品，就进行提示
						allTradeGoodsNoChecked.list.push(trade.receiverNameMask);
					}
				});
			});
			// 判断订单是不是挂起状态
			if (tradeListItem.isPending) {
				pendingList.push(tradeListItem.togetherId);
			}
		});

		if (abnormalArr?.length) {
			// 每次请求 最多能查200个tid
			let splitArr: Array<any> = spArr(abnormalArr, 200);
			const promises = splitArr.map(function(item) {
				let params = {
					tidList: item.map((i) => i.tid),
					tradeCheckInfos: item,
				};
				return TradePrintCheckApi(params);
			});

			// 从接口获取一些异常
			try {
				const resArr = await Promise.all(promises);
				console.log('%c [ 从接口获取一些异常 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', resArr);
				resArr?.forEach((res) => {
					res?.data?.abnormalTradeInfoList?.forEach((item: any) => {
						if (item && item !== 'null') {
							if (item.type == AbnormalType.订单正在执行自动策略) {
								tradeInfoCheck(item, TradeAbnormalType.订单正在执行自动策略);
							} else if (item.type == AbnormalType.列表加载商品数据与数据库商品数据不一致) {
								tradeInfoCheck(item, TradeAbnormalType.列表加载商品数据与数据库商品数据不一致);
							}
						}
					});
				});
			} catch (error) {
				console.log('%c [ 接口获取异常失败 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', error);
			}
		}

		// 勾选订单中包含挂起订单
		if (pendingList.length > 0) {
			alreadyGeneratorLabelModal("勾选订单中包含挂起订单，请取消勾选后重新生成标签").then((res) => {
				handleChoiceChange({
					type: TradeChoiceType.取消勾选指定订单,
					cancelChoiceOrders: pendingList,
				});
				generatorLabelPrecheck();
			}).catch(() => { });
			return;
		}
		// 如果有已生成标签订单 弹出弹窗
		// if (generatedList.length > 0) {
		// 	alreadyGeneratorLabelModal("勾选订单中包含【已生成标签】商品，请取消勾选后重新生成标签").then((res) => {
		// 		handleChoiceChange({
		// 			type: TradeChoiceType.取消勾选指定订单,
		// 			cancelChoiceOrders: generatedList
		// 		});
		// 		generatorLabelPrecheck();
		// 	}).catch(() => {});
		// 	return;
		// }

		// 判断所有勾选的订单是不是都没有勾选商品
		if (allTradeGoodsNoChecked.checked) {
			const newReceiverNameMaskList = [...new Set(allTradeGoodsNoChecked.list)];
			const content = newReceiverNameMaskList.map((receiverNameMask) => (
				<p>{receiverNameMask}</p>
			));
			Modal.warning({
				centered: true,
				title: `以下订单未勾选要生成的宝贝`,
				content: (
					<div>
						<div>{content}</div>
						<div className="r-c-warning" >请勾选宝贝后再试</div>
					</div>
				),
				okText: '我知道了'
			});
			return;
		}

		console.log('%c [ 所有的异常拦截数据 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', abnormalObj);

		// 如果有需要校验的异常订单，就提示
		if (abnormalList.length > 0) {
			const newAbnormalList = [...new Set(abnormalList)];
			// 先把异常订单取消掉，让用户自己勾选
			handleChoiceChange({
				type: TradeChoiceType.取消勾选子订单,
				cancelChoiceOrders: newAbnormalList,
				oidList: abnormalOidList,
			});
			setLabelAbnormalObj(abnormalObj);
			setShowGenerateLabelWarningModal(true);
			return;
		} else {
			beforeGeneratorConfirmModal().then((res) => {
				generatorTag(selectedList);
			}).catch(() => { });
		}
	};

	// 当modal中的checkBox发生变化时
	const abnormalLabelCheckedOnChange = (dataItem) => {
		/**
		 * 勾选逻辑和取消勾选逻辑都比较复杂，以下是业务需求描述：
		 * 当勾选时，必须子订单的订单状态和售后状态同时命中才允许勾选
		 * 比如子订单的订单状态是已关闭，售后状态是退款成功，
		 * 这个时候，只有同时勾选了"已关闭"和"买家申请退款"，子订单才会被勾选，
		 * 取消勾选的逻辑亦是如此
		 */
		const { isChecked, tradeList, abnormalOrderList, allAbnormalData } = dataItem;
		const togetherIdList = [];
		const allCheckedAbnormalTypeList = []; // 获取所有勾选的异常类型
		tradeList.forEach(trade => {
			togetherIdList.push(trade.togetherId);
		});
		const newTogetherIdList = [...new Set(togetherIdList)];
		const allowCancelCheckedAbnormalOidList = []; // 允许勾选或取消勾选的oid列表
		for (let key in allAbnormalData) {
			const abnormalItem = allAbnormalData[key];
			if (abnormalItem.isChecked) {
				allCheckedAbnormalTypeList.push(abnormalItem.type);
			}
		}
		abnormalOrderList.forEach(order => {
			const allow = order.abnormalList.every(abnormalType => allCheckedAbnormalTypeList.includes(abnormalType));
			if (isChecked && allow) {
				// 勾选时，判断当前oid的每一项异常状态是不是都在已勾选的异常状态列表中，只有满足情况才允许勾选
				// const allow = oid.abnormalList.every(abnormalType => allCheckedAbnormalList.includes(abnormalType));
				allowCancelCheckedAbnormalOidList.push(order.oid);
			} else if (!isChecked && !allow) {
				// 取消勾选时，判断当前oid的每一项异常状态是不是都在已勾选的异常状态列表中，只有满足情况才允许取消勾选
				// const allow = oid.abnormalList.every(abnormalType => allCheckedAbnormalList.includes(abnormalType));
				allowCancelCheckedAbnormalOidList.push(order.oid);
			}
		});
		if (isChecked) {
			handleChoiceChange({
				type: TradeChoiceType.勾选子订单,
				oidList: allowCancelCheckedAbnormalOidList,
				cancelChoiceOrders: newTogetherIdList
			});
		} else {
			handleChoiceChange({
				type: TradeChoiceType.取消勾选子订单,
				oidList: allowCancelCheckedAbnormalOidList,
				cancelChoiceOrders: newTogetherIdList
			});
		}
	};

	const handleAllowGenerateType = (data) => {
		const allowObj = {
			allowGenerateAlreadySendTrade: false,
			allowGenerateRefundTrade: false,
			allowGenerateFinishedTrade: false,
			allowGenerateClosedTrade: false,
		};
		for (let key in data) {
			if (data[key].isChecked) {
				switch (key) {
					case TradeAbnormalType.已发货:
						allowObj.allowGenerateAlreadySendTrade = true;
						break;
					case TradeAbnormalType.买家申请退款:
						allowObj.allowGenerateRefundTrade = true;
						break;
					case TradeAbnormalType.交易成功:
						allowObj.allowGenerateFinishedTrade = true;
						break;
					case TradeAbnormalType.已关闭:
						allowObj.allowGenerateClosedTrade = true;
						break;
					default: break;
				}
			}
		}
		setAllowGenerateType(allowObj);
	};

	// 确认生成标签时
	const generateLabelWarningModalOnOk = (data) => {
		// 生成小标签时，告诉后端哪些勾选了
		const selectedList: IPackage[] = list?.filter(item => item.isChecked) || [];
		if (selectedList.length < 1) {
			message.error("未勾选操作订单，请勾选订单后再试！");
			setShowGenerateLabelWarningModal(false);
			return;
		}
		handleAllowGenerateType(data);
		beforeGeneratorConfirmModal().then((res) => {
			setShowGenerateLabelWarningModal(false);
			generatorTag(selectedList);
		}).catch(() => { });
	};

	// 生成标签前校验标签状态
	const checkTradeStatus = () => {
		let selectedList: IPackage[] = list?.filter(item => item.isChecked) || [];
		if (!selectedList || selectedList.length === 0) {
			message.info("请重新选择订单");
			return;
		}

		// 校验订单内是否未选择
		let isNotCheckedOrder: { value: boolean, text: string } = { value: true, text: "" };
		let alreadyGeneratorLabelSet: string[] = [];
		let pendingList: string[] = [];
		let notWaitSendList: string[] = [];
		for (let tradeListItem of selectedList) {
			for (let item of tradeListItem.trades) {
				for (let order of item.orders) {
					// 判断子订单发货状态  已发货 退款中 交易成功 已关闭 订单不可生成标签
					if (order.isChecked
						&& (
							['TRADE_CLOSED', 'TRADE_CLOSED_BY_TAOBAO', 'SELLER_CONSIGNED_PART', 'TRADE_BUYER_SIGNED', 'TRADE_FINISHED'].includes(order.status)
							|| ['REFUND_ING', 'REFUND_SUCCESSED'].includes(order.refundStatus)
							|| (order.status === 'WAIT_BUYER_CONFIRM_GOODS' && !order.firstSend)
						)) {
						// 已发货中 先发货的订单可以继续生成标签
						notWaitSendList.push(tradeListItem.togetherId);
					}
					// 子订单商品检查是否含有已生成商品标签订单
					if (order.isChecked && order.labelstatus === 1) {
						alreadyGeneratorLabelSet.push(tradeListItem.togetherId);
					}
					if (order.isChecked) isNotCheckedOrder.value = false;
				}
				if (isNotCheckedOrder.value) {
					isNotCheckedOrder.text = item.receiverNameMask;
				}
			}
			if (tradeListItem.isPending) {
				pendingList.push(tradeListItem.togetherId);
			}
		}
		// 勾选订单中包含【已发货、退款中、交易成功、已关闭】状态商品
		if (notWaitSendList.length > 0) {
			alreadyGeneratorLabelModal("勾选订单中包含【已发货、退款中、交易成功、已关闭】状态商品，请取消勾选后重新生成标签").then((res) => {
				handleChoiceChange({
					type: TradeChoiceType.取消勾选指定订单,
					cancelChoiceOrders: notWaitSendList
				});
				checkTradeStatus();
			}).catch(() => { });
			return;
		}
		// 勾选订单中包含挂起订单
		if (pendingList.length > 0) {
			alreadyGeneratorLabelModal("勾选订单中包含挂起订单，请取消勾选后重新生成标签").then((res) => {
				handleChoiceChange({
					type: TradeChoiceType.取消勾选指定订单,
					cancelChoiceOrders: pendingList
				});
				checkTradeStatus();
			}).catch(() => { });
			return;
		}
		// 如果有已生成标签订单 弹出弹窗
		if (alreadyGeneratorLabelSet.length > 0) {
			alreadyGeneratorLabelModal("勾选订单中包含【已生成标签】商品，请取消勾选后重新生成标签").then((res) => {
				handleChoiceChange({
					type: TradeChoiceType.取消勾选指定订单,
					cancelChoiceOrders: alreadyGeneratorLabelSet
				});
				checkTradeStatus();
			}).catch(() => { });
			return;
		}
		if (isNotCheckedOrder.value) {
			Modal.warning({
				centered: true,
				title: `[${isNotCheckedOrder.text}]未勾选要生成的宝贝`,
				content: (
					<div className="r-c-warning" >请勾选宝贝后再试</div>
				),
				okText: '我知道了'
			});
			return;
		}
		// 生成标签二次确认
		beforeGeneratorConfirmModal().then((res) => {
			generatorTag(selectedList);
		}).catch(() => { });

	};

	const generatorTag = (list: IPackage[], generateMarketLabelWhenUnderstock: boolean = false) => {
		let goodsLabelTradeInfoList: ItemTakeGoodsLabelGenerateRequest["goodsLabelTradeInfoList"] = [];
		let selectList: ItemTakeGoodsLabelGenerateRequest["goodsLabelTradeInfoList"] = [];
		let tradeInfos: ITradeInfos = { tid: "", orderInfos: [] };
		let generatorLabelLen = 0;
		const packList = [];
		const isOpenGift = !(userStore.systemSetting.platformGiftNotInvolvedTakeGoodsLabel == 1 && userStore.systemSetting.platformGift == 1);
		for (let tradeListItem of list) {
			for (let item of tradeListItem.trades) {
				tradeInfos.tid = item.tid;
				// 判断是 拆单还是合单发货  先找到所有选中商品 对比 是否与order中商品相等
				let selectOrder = item.orders.filter((oItem) => (oItem.isChecked));
				let split = selectOrder.length !== item.orders.length;
				tradeInfos.split = split;
				for (let order of selectOrder) {
					if (order.isChecked && (item.splitTradeType === 'GIFT' || (order.isGift && GIFT_ALLOW_GENERATORTAG_PLATFORM.includes(item.platform) && isOpenGift) || !order.isGift)) {
						generatorLabelLen += +order.num;
						tradeInfos.orderInfos.push({
							oid: order.oid,
							// numIid: order.numIid,
							// skuId: order.skuId,
							num: order.num,
							// sysItemId: order.systemNumIid,
							sysSkuId: order.systemSkuId,
							// ydNo: order.ydNo,
							// printStatus: order.printStatus,
							// sellerId: item.sellerId,
							// sellerNick: item.sellerNick,
							// platform: item.platform,
							// payTime: item.payTime,
							ignoreExcFlag: order.ignoreExcFlag ? "IGNORE_EXC" : "NOT_IGNORE_EXCE",
							// printTime: order.printTime,
							// skuOuterId: order.outerSkuId,
							// title: order.title,
							// picUrl: order.picPath,
							// skuName: order.skuPropertiesName
							isGift: order.isGift,
							alreadyAllotStockNum: order.alreadyAllotStockNum,
							combinationAllotStockNum: order.combinationAllotStockNum,
						});
					}
				}
				tradeInfos.orderInfos.length > 0 && selectList.push(tradeInfos);
				tradeInfos = { tid: "", orderInfos: [] };
			}
			packList.push(tradeListItem);
			goodsLabelTradeInfoList.push({ tradeInfos: selectList });
			selectList = [];
		}
		console.log('goodsLabelTradeInfoList::::', goodsLabelTradeInfoList);
		takeGoodsLabelGenerate({ goodsLabelTradeInfoList, generateMarketLabelWhenUnderstock, generatorLabelLen, packList });
	};
	// 生成标签
	const takeGoodsLabelGenerate = async(params: ItemTakeGoodsLabelGenerateRequest) => {
		let labelTimer = null;
		try {
			const { goodsLabelTradeInfoList, generateMarketLabelWhenUnderstock, forceGenerateSortLabelWhenUnderstock, generatorLabelLen, packList } = params;
			// 生成标签时的进度条 目前直接是前端用定时器做的假进度条
			setLabelGenerateProgress({ show: true, num: 0, sum: generatorLabelLen || 0 });
			labelTimer = setInterval(() => {
				let num = 0;
				setLabelGenerateProgress((prev) => {
					num = ++prev.num;
					return { show: true, num, sum: generatorLabelLen || 0 };
				});
				if (num > (generatorLabelLen - 1)) clearInterval(labelTimer);
			}, 500);
			try {
				let splitArr: Array<any> = spArr(goodsLabelTradeInfoList, 50);
				let resArr = [];
				const handler = async() => {
					if (splitArr.length) {
						let params = {
							goodsLabelTradeInfoList: splitArr.pop(),
							generateMarketLabelWhenUnderstock,
							forceGenerateSortLabelWhenUnderstock,
							skipAlreadyGenerateLabelTrade: true,
							...getAllowGenerateType()
						};
						let _res = await TakeGoodsLabelGenerateApi(params);
						resArr.push(_res);
						await handler();
					}
				};
				await handler();
				clearInterval(labelTimer);
				let alreadyGeneratorLabelSet: any = [];
				let understockErrorMsgList: any = [];
				let successTidOids: {} = {};
				let failCount: number = 0;
				// api 请求报错时统计订单数量
				let apiErrorCount: number = 0;
				resArr.forEach((item, index) => {
					if (item.alreadyGeneratorLabelSet) {
						alreadyGeneratorLabelSet = alreadyGeneratorLabelSet.concat(item.alreadyGeneratorLabelSet);
					}
					if (item.understockErrorMsgList) {
						understockErrorMsgList = understockErrorMsgList.concat(item.understockErrorMsgList);
					}
					if (item.failCount) {
						failCount += Number(item.failCount || 0);
					}
					if (item.successTidOids) {
						successTidOids = { ...successTidOids, ...item.successTidOids };
					}
				});
				setLabelGenerateProgress({ show: false, num: 0, sum: 0 });
				// 弹窗处理
				checkLabelGeneratorState({
					resArr,
					alreadyGeneratorLabelSet,
					understockErrorMsgList,
					failCount,
					params,
					apiErrorCount,
					successTidOids,
					generatorLabelLen,
					packList
				});
			} catch (error) {
				clearInterval(labelTimer);
				setLabelGenerateProgress({ show: false, num: 0, sum: 0 });
				console.log('err:::', error);
			}
		} catch (error) {
			clearInterval(labelTimer);
			setLabelGenerateProgress({ show: false, num: 0, sum: 0 });
			console.log('error:::', error);
		}
	};

	// 更新订单列表，该方法可以公用。只需要传入勾选的packList就行，
	const updateTradeList = async(packList) => {
		const tradeInfos = [];
		packList.forEach(pack => {
			tradeInfos.push({
				tids: pack.togetherId.split('|'),
				platform: pack.platform,
				sellerId: pack.sellerId
			});
		});
		const { data: { list } } = await TradeTradeDetailGetApi({ tradeInfos });
		if (list?.length) {
			const { tradeListStore: { handleUpdateList } } = tradeStore;
			handleUpdateList({
				type: "batchUpdateTrade",
				data: list,
				needCheck: true,
			});
		}
	};

	const checkLabelGeneratorState = async(
		{
			resArr,
			alreadyGeneratorLabelSet,
			understockErrorMsgList,
			successTidOids,
			failCount,
			params,
			apiErrorCount,
			generatorLabelLen,
			packList
		}: {
			resArr: any[],
			alreadyGeneratorLabelSet: [],
			understockErrorMsgList: [],
			successTidOids: {},
			failCount: number,
			params: ItemTakeGoodsLabelGenerateRequest,
			apiErrorCount: number,
			generatorLabelLen: number
			packList: [],
		}
	) => {
		// 成功生成小标签以后，如果"展示订单商品拿货标签囤货情况"开关开启，需要重新调用TradeTradeDetailGetApi接口，页面更新'囤货缺货'展示
		const res = await memoFn.getAdvancedSet();
		if (res?.showGoodsLabelStockpile) {
			updateTradeList(packList);
		}
		if (understockErrorMsgList.length > 0) {
			setIsShowUnderstockErrorMsgListModal({ show: true, params, understockErrorMsgList });
			return;
		}
		if (alreadyGeneratorLabelSet.length > 0) {
			alreadyGeneratorLabelModal("勾选订单中包含【已生成标签】商品，请取消勾选后重新生成标签").then((res) => {
				handleChoiceChange({
					type: TradeChoiceType.小标签生成_取消勾选指定订单,
					cancelChoiceOrders: alreadyGeneratorLabelSet
				});
				checkTradeStatus();
			}).catch(() => {

			});
			return;
		}
		let successCount = (generatorLabelLen - (failCount || 0) - apiErrorCount);
		successCount = successCount > 0 ? successCount : 0;
		const _failCount = failCount - 0 + apiErrorCount || 0;
		// 更新列表中成功生成标签标识
		handleChoiceChange({
			type: TradeChoiceType.更新小标签标识,
			successTidOids
		});
		// 生成标签结束后提示
		generatorLabelFinishModal({ successCount, failCount: _failCount, resArr }).then((res) => { }).catch(() => { });

	};

	const handleBatchOpt = (item: { name: string, id: string }) => {
		// 不需要勾选订单的放这里
		if (['batchImport', 'importExpress'].includes(item.id)) {
			actionMap?.[item.id]?.();
		} else {
			// 要勾选，但是不需要前置判断的放这里
			const notFirstCheck = ['batchExport', 'batchPrintBIC', 'batchSplitBySubOrder'];
			let packList = list?.filter(item => item.isChecked) || [];
			if (!packList.length && !notFirstCheck.includes(item.id)) {
				message.warning('请先选择订单');
				return;
			}

			// 分组基本上没有点击事件
			actionMap?.[item.id]?.(packList);
		}
	};


	const handleButtonList = (type: 'moreButton' | 'mainButton') => {
		/**
			 * 该按钮数据是后端存储时的结构，只包含了一些基本信息，前端还需要二次加工以完善按钮信息才可以使用，
			 * 比如哪些按钮是库存版显示的，哪些按钮需要做表格数据勾选校验，哪些按钮需要触发埋点
			 */
		let initBtn = optBtn;
		let needHandleBtn: IOptBtn[] = [];
		const advanceSetRes = tradeAdvanceSetObj;
		const { customPrintSetStatus, customPrintSetJsonString2 } = advanceSetRes;
		// 如果用户开启了开关，并且有自定义的按钮数据
		if (customPrintSetStatus === 2 && customPrintSetJsonString2) {
			initBtn = JSON.parse(customPrintSetJsonString2);

			// 需要子账号权限的功能点
			const needHandleFunctionPermissionMap = {
				'printKdd': FunctionPermissionEnum.打印快递单,
				'printFhd': FunctionPermissionEnum.打印发货单,
				'send': FunctionPermissionEnum.发货,
				'batchExport': FunctionPermissionEnum.批量导出,
				'batchImport': FunctionPermissionEnum.批量导入订单,
				'preShip': FunctionPermissionEnum.自动发货,
				'batchResend': FunctionPermissionEnum.重新发货,
				'batchCopyTradeInfo': FunctionPermissionEnum.批量复制,
			};

			for (let key in initBtn) {
				initBtn[key]?.forEach((i: IOptBtn) => {
					// * 针对自定义的按钮加上functionPermission字段
					if (needHandleFunctionPermissionMap[i.id]) {
						i.functionPermission = needHandleFunctionPermissionMap[i.id];
					}
					i?.children?.forEach(item => {
						if (needHandleFunctionPermissionMap[item.id]) {
							item.functionPermission = needHandleFunctionPermissionMap[item.id];
						}
					});
				});
			}
		}

		// 如果有新增的功能按钮，需加入
		newBtnAddToPrintSet(initBtn, optBtn, userStore);
		const { moreButton, mainButton } = initBtn;
		// 如果需要返回的是更多按钮
		if (type === 'moreButton') {
			needHandleBtn = moreButton;
		} else if (type === 'mainButton') {
			needHandleBtn = mainButton;
		}

		 // 过滤掉没有波次管理权限的按钮
		 needHandleBtn = needHandleBtn.filter(btn => {
			// 如果是波次管理按钮，检查用户是否有波次管理权限
			if (btn.id === 'batchCreateWave' && !userStore.hasWaveManagePermission) {
				return false;
			}
			return true;
		});

		// console.log('%c [ 批打吸底按钮 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', type == 'mainButton' ? '外面的' : '更多的', [...needHandleBtn]);
		needHandleBtn.forEach(btn => {
			// 重新对每个按钮进行处理
			if (btn?.children?.length) {
				btn?.children?.forEach(item => {
					handleBtn(item);
					if (item.onClick) item.onClick = _.debounce(item.onClick, 500, { leading: true, trailing: false });
				});
			} else {
				handleBtn(btn);
				if (btn.onClick) btn.onClick = _.debounce(btn.onClick, 500, { leading: true, trailing: false });
			}
		});

		// * todo 待优化
		function handleBtn(btn) {
			btn['onClick'] = () => handleBatchOpt(btn);
			switch (btn.id) {
				case 'printFhd': {
					btn['onClick'] = () => {
						sendPoint(Pointer.订单_订单打印_订单操作_打印发货单);
						handlePrintAct({ printType: PrintTypeEnum.发货单 });
					};
					btn['className'] = ["r-btn-blue"];
					break;
				}
				case 'printKdd': {
					btn['onClick'] = async() => {
						sendPoint(Pointer.订单_订单打印_订单操作_打印快递单);
						// 只有试用用户需要校验打印次数
						// if (userStore.userInfo.level == 0) {
						pageLoading.loading();
						await preCheckPrintLimit();
						pageLoading.destroy();
						// }
						await checkShopTemp(tradeStore.selectedTemp);
						handlePrintAct({ printType: PrintTypeEnum.快递单 });
					};
					btn['className'] = ["r-btn-green"];
					break;
				}
				case 'send': {
					btn['className'] = ["r-btn-primary"];
					btn['onClick'] = () => {
						sendPoint(Pointer.订单_订单打印_订单操作_发货);
						handleSend();
					};
					break;
				}
				default: break;
			}
		}
		return needHandleBtn;
	};

	// 触发对应分组的第一个按钮的点击事件
	const handleButtonClick = (item) => {
		item?.children?.[0]?.onClick?.();
	};

	const handleMouseEnter = (item) => {
		// console.log('%c [ 分组按钮 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', item);
		setActiveInfo(item);
	};

	const handleMouseLeave = () => {
		setActiveInfo(null);
	};

	// 平铺按钮
	const getMainButtonList = useMemo(() => {
		const buttonList = handleButtonList('mainButton');
		const theButton = [];
		buttonList.forEach((btn, index) => {
			// 按钮分组处理
			if (btn?.children?.length) {
				const getOptBtn = (item) => (
					<Tooltip title={ item.tooltip } placement="right" >
						<div
							key={ item.name }
							className={ cs(s["operate-item"], 'operate-item r-pointer', s.isChildren, { [s.active]: activeInfo?.children[0].id == item.id }) }
							onClick={ item.onClick }
						>
							{item.name}
						</div>
					</Tooltip>
				);
				const getOptBtnContent = (item) => {
					if (item.functionPermission) {
						return <FunctionPermissionCheck key={ item.id } functionPermission={ item.functionPermission }>{getOptBtn(item)}</FunctionPermissionCheck>;
					} else {
						return getOptBtn(item);
					}
				};
				const childrenMenu = (
					<div className={ s['operate-item-group'] }>
						{
							btn?.children?.map(item => {
								return getOptBtnContent(item);
							})
						}
					</div>
				);
				const groupButton = (
					<div className={ cs(s['operate-item-pop'], "r-flex r-ai-c r-mr-16") }>
						<Popover
							title=""
							placement="top"
							trigger="hover"
							arrowPointAtCenter
							content={ childrenMenu }
							getPopupContainer={ triggerNode => triggerNode?.parentElement }
						>

							<Button
								key={ btn.id }
								className={ cs(btn.className) }
								size="large"
								style={ { minWidth: 110 } }
								onClick={ () => handleButtonClick(btn) }
								onMouseEnter={ () => handleMouseEnter(btn) }
								onMouseLeave={ handleMouseLeave }
							>
								<div className={ cs("r-flex r-ai-c") }>
									{btn.name}
									<DownOutlined style={ { fontSize: '12px', marginLeft: 4 } } />
								</div>
							</Button>
						</Popover>
					</div>
				);

				theButton.push(groupButton);
			} else if (btn.name) {
				const btnDom = (
					<Button
						key={ btn.id }
						className={ cs("r-mr-16", btn.className) }
						size="large"
						style={ { minWidth: 110 } }
						onClick={ btn.onClick }
						disabled={ !userStore.hasFunctionPermission(btn.functionPermission) }
					>{btn.name}{userStore.isDistributorAccount && btn.id == 'printKdd' ? <span className={ c["btn-top-right-icon"] }>限免</span> : ''}
					</Button>
				);
				if (btn.functionPermission) {
					theButton.push(<FunctionPermissionCheck key={ index } functionPermission={ btn.functionPermission }>{btnDom}</FunctionPermissionCheck>);
				} else {
					theButton.push(btnDom);
				}
			}
		});
		return theButton;
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [tradeAdvanceSetObj, handleBatchOpt, handleButtonList, userStore.hasWaveManagePermission]);

	// 更多操作
	const MoreMenu = () => {
		const renderMoreBtn = (operate: IOptBtn) => {
			// 不是先发货模式没有获取单号
			if (operate.id === 'getYdNos' && !preSendMode) {
				return "";
			}

			const getOptBtn = (item, isChildren: boolean) => (
				<Tooltip title={ item.tooltip } placement="right" >
					<div
						key={ item.name }
						className={ cs(s["operate-item"], 'operate-item r-pointer', { [s.isChildren]: isChildren, [s.active]: activeInfo?.children[0].id == item.id }) }
						onClick={ item.onClick }
					>
						{item.name}
						{
							item.isNew && (
								<span className={ c['new-icon'] }>New</span>
							)
						}
					</div>
				</Tooltip>
			);

			const getOptBtnContent = (item, isChildren: boolean = false) => {
				if (item.functionPermission) {
					return (
						<FunctionPermissionCheck functionPermission={ item.functionPermission }>{getOptBtn(item, isChildren)}</FunctionPermissionCheck>
					);
				} else {
					return getOptBtn(item, isChildren);
				}
			};

			// 按钮分组处理
			if (operate?.children?.length) {
				const childrenMenu = (
					<div className={ s['operate-item-group'] }>
						{
							operate?.children?.map(item => {
								return getOptBtnContent(item, true);
							})
						}
					</div>
				);
				return (
					<Popover
						title=""
						placement="right"
						trigger="hover"
						arrowPointAtCenter
						content={ childrenMenu }
						getPopupContainer={ triggerNode => triggerNode }
					>
						<div
							key={ operate.name + '_group' }
							className={ cs(s["operate-item"], 'operate-item r-pointer', s['operate-item-pop']) }
							onClick={ () => handleButtonClick(operate) }
							onMouseEnter={ () => handleMouseEnter(operate) }
							onMouseLeave={ handleMouseLeave }
						>
							<div className="r-flex r-ai-c r-relative">
								{operate.name}
								<RightOutlined style={ { fontSize: '12px', color: 'rgba(0, 0, 0, 0.45)', marginLeft: 4 } } />
								{
									operate?.children?.some(item => item?.isNew) && (
										<span className={ c['new-group-icon'] }>New</span>
									)
								}
							</div>
						</div>
					</Popover>
				);
			} else {
				return getOptBtnContent(operate, false);
			}
		};

		const moreButtonList = handleButtonList('moreButton');

		return (
			<Menu className="r-flex r-fd-c">
				<div className={ s["operate-container-w"] }>
					<div className={ s["operate-container"] }>
						{moreButtonList.map(operate => renderMoreBtn(operate))}
					</div>
				</div>
			</Menu>
		);
	};

	const hanldeSetStorageOk = async() => {
		try {
			if (!sendStorageModal?.storageId) {
				message.error("请先选择仓库");
				return;
			}
			setSendStorageModal((prev) => { return { ...prev, loading: true }; });
			const tidList = [];
			list?.forEach((item) => {
				if (item.isChecked) {
					item?.trades?.forEach(trade => {
						tidList.push(trade.tid);
					});
				}
			});
			sendPoint(Pointer.订单_更多操作_点击设置发货仓库_确定);
			const res = await TradeBatchUpdateStorageApi({ tidList, storageId: sendStorageModal?.storageId });
			setStorageResShowModal({ visible: true, resInfo: res });
			const _succussList = res?.successList.map((item) => (item.tid));
			list?.forEach((pack) => {
				pack?.trades?.forEach((trade) => {
					if (_succussList?.includes(trade?.tid)) {
						trade.storageId = sendStorageModal?.storageId;
						trade.storageName = sendStorageModal?.storageName;
						trade.storageType = sendStorageModal?.type;
						trade.storageTypeDesc = sendStorageModal?.storageTypeDesc;
						pack.storageId = sendStorageModal?.storageId;
						pack.storageName = sendStorageModal?.storageName;
						pack.storageType = sendStorageModal?.type;
						pack.storageTypeDesc = sendStorageModal?.storageTypeDesc;
					}
				});
			});
			setList([...list]);
			setSendStorageModal((prev) => { return { ...prev, visible: false, loading: false }; });
		} catch (error) {
			console.log("error:::", error);
			setSendStorageModal((prev) => { return { ...prev, loading: false }; });
		}

	};

	const handleSend = () => {
		setTempAbnormalIgnoreKey([]);
		sendDelivery({});
	};
	const understockErrorMsgListModalBack = (flag: string) => {
		let params = { ...understockErrorMsgListModalData.params };
		if (flag === GeneratorLabelType.拿货) {
			params.generateMarketLabelWhenUnderstock = true;
			takeGoodsLabelGenerate(params);
		}
		if (flag === GeneratorLabelType.库存) {
			params.forceGenerateSortLabelWhenUnderstock = true;
			takeGoodsLabelGenerate(params);
		}
		setIsShowUnderstockErrorMsgListModal({ show: false, params: {} });
	};
	const handleSkipItem = () => {
		setList([]);
	};

	const onResultModalFinished = () => {
		setResultModalVisible(false);
		// event.emit(EVENT_BUS.REQUEST_AGAIN);
	};

	const handleChangeStorage = (e) => {
		const storageIdItem = storageInfoList.find(item => item.id == e);
		setSendStorageModal((prev) => { return { ...prev, storageId: e, storageName: storageIdItem?.storageName, type: storageIdItem?.type, storageTypeDesc: storageIdItem?.storageTypeDesc }; });
	};

	return (
		<div className={ cs(s['bottom-container'], { [s.showTransparent]: from !== "hotGoodsScanPrintPage" && setting?.printSetExpandDTO?.showTransparent }) }>
			<div className={ s.scrollLine } id="scrollLine" style={ { width: (setting.filterOrder === 2 ? 'calc(100% - 186px)' : '100%') } }>
				<div style={ { width: 0, height: 1 } } />
			</div>
			<div className={ s['bottom-opt-container'] }>
				{
					from === "hotGoodsScanPrintPage" ? (
						<>
							<Button
								className="r-mr-16"
								size="large"
								style={ { width: 110 } }
								onClick={ () => { handleSkipItem(); } }
							>跳过
							</Button>
							<Button
								data-point={ Pointer.档口_爆款标签_打印快递单 }
								className="r-mr-16 r-c-white r-btn-green"
								size="large"
								style={ { width: 110 } }
								onClick={ () => { handlePrintAct({ printType: PrintTypeEnum.爆款打印 }); } }
							>打印
							</Button>
							<Button
								data-point={ Pointer.档口_爆款标签_发货 }
								type="primary"
								className="r-mr-16"
								size="large"
								style={ { width: 110 } }
								onClick={ handleSend }
							>发货
							</Button>
						</>
					) : (
						<>
							{getMainButtonList}
							<div id="tradeBottomMoreMenus" style={ { display: 'flex' } }>
								<Dropdown
									overlay={ MoreMenu }
									placement="topCenter"
									getPopupContainer={ () => document.getElementById('tradeBottomMoreMenus') }
									trigger={ ["click"] }
									overlayClassName="operate-com-dropdown"
								>
									<Button size="large" style={ { width: 110 } }>更多操作<DownOutlined /></Button>
								</Dropdown>
							</div>
						</>
					)
				}

			</div>
			<div className={ s['bottom-check-container'] }>
				{listCheckStatus === 0.5 ? <span className="half-checked" onClick={ handleHalfClick } /> : (
					<input
						type="checkbox"
						checked={ listCheckStatus === 1 }
						onChange={ selectAllPacks }
					/>
				)}
				<div className="r-ml-8 r-mr-16">{checkedCount}单</div>
				<Dropdown
					overlay={ menu }
					placement="topLeft"
					// trigger={ ["click"] }
					overlayClassName="bottom-com-dropdown"
				>
					<Button>
						快速勾选<DownOutlined />
					</Button>
				</Dropdown>
			</div>

			{/* 异常处理弹窗 */}
			<AbnormalManageModal
				isPreShip={ isPreShip }
				visible={ abnormalManageModalVisible }
				modalType={ abnormalManageModalType }
				data={ orderAbnormalData }
				onClose={ onCloseAbnormalManageModal }
				onSubmitAfter={ onSubmitAbnormalOrders }
			/>
			{
				labelGenerateProgress.show && (
					<Modal
						centered
						visible
						footer={ null }
						width={ 300 }
						closable={ false }
					>
						<div className={ c['label-progress-title'] }>
							<InfoCircleOutlined className={ c['label-progress-icon'] } />
							<span className="r-ml-12 r-fw-500">提示</span>
						</div>
						<div className={ c['label-progress-ing'] }>生成标签中...<span className="r-ml-8">{labelGenerateProgress.num}/{labelGenerateProgress.sum}</span></div>
					</Modal>
				)
			}
			{
				understockErrorMsgListModalData.show
				&& <UnderstockErrorMsgListModal understockErrorMsgList={ (understockErrorMsgListModalData.understockErrorMsgList as any[]) || [] } onOk={ understockErrorMsgListModalBack } />
			}

			{
				showGenerateLabelWarningModal && (
					<GenerateLabelWarningModal
						data={ labelAbnormalObj }
						onCancel={ () => setShowGenerateLabelWarningModal(false) }
						onOk={ (data) => generateLabelWarningModalOnOk(data) }
						checkedOnChange={ (data) => abnormalLabelCheckedOnChange(data) }
					/>
				)
			}
			{isShowBatchUpdateTrade && <BatchUpdateTrade onCancel={ () => { setIsShowBatchUpdateTrade(false); } } onOk={ () => { setIsShowBatchUpdateTrade(false); } } />}

			{isBatchResend && <BatchResend />}

			{isBatchAppendSend && <BatchAppendSend />}

			{isBatchPrintBIC && <BatchPrintBIC />}

			{isBatchSendBIC && <BatchSendBIC />}

			{isBatchCreateWave && <BatchCreateWave />}

			{/* 给用户提示用的进程弹窗 */}
			{processingModalVisible && (
				<ProcessingModal
					percent={ processingModalPercent }
					content={ processingModalContent }
				/>
			)}
			{/* 处理结果展示 */}
			{
				resultModalVisible
				&& (
					<ResultModal
						successList={ resultModalSuccessList }
						failList={ resultModalFailList }
						title={ resultModalTitle }
						onCancel={ onResultModalFinished }
						onOk={ onResultModalFinished }

					/>
				)
			}
			{/* 选择指定快递 */}
			{
				selectTemplateModalVisible && (
					<SelectSmartExpressTemplate
						onCancel={ () => setSelectTemplateModalVisible(false) }
						onOk={ onSelectedeTemplateOk }
					/>
				)
			}

			<BatchSellerSend
				visible={ sellerSendModalVisible }
				onCancel={ () => setSellerSendModalVisible(false) }
				onOk={
					(val) => {
						sendDelivery({
							isSendAbnormal: false,
							isNoLogistics: true,
							noLogisticsType: SendType.卖家配送,
							noLogisticsParams: val
						});
					}
				}
			/>
			<BatchCancelYdNoModel
				visible={ cancelYdNovisible }
				setVisible={ setCancelYdNovisible }
				cancelYdNoData={ cancelYdNoData }
				showHandleCheck={ false }
			/>
			<InputSerialNumberModal visible={ inputSerialNumberModalVisible?.visible } list={ inputSerialNumberModalVisible?.list || [] } onCancel={ () => setInputSerialNumberModalVisible({ visible: false, list: [] }) } />
			<Modal
				visible={ inputSerialNumberAbnormalModal?.visible }
				centered
				footer={ (
					<div className={ cs('r-flex r-ai-c r-jc-sb r-mt-32') }>
						<div className="r-c-666">过滤异常后勾选（{inputSerialNumberAbnormalModal?.canCheckNum}单）</div>
						<div>
							<Button
								type="primary"
								onClick={ () => {
									handleChoiceChange({
										type: TradeChoiceType.取消勾选指定订单,
										cancelChoiceOrders: [...inputSerialNumberAbnormalModal.cancelChoiceOrders]
									});
									setInputSerialNumberAbnormalModal({ visible: false, canCheckNum: 0, cancelChoiceOrders: [] });
									handleInputSerialNumberBefore();
								} }
							>跳过异常 操作正常数据
							</Button>
							<Button className="r-ml-16" onClick={ () => { setInputSerialNumberAbnormalModal({ visible: false, canCheckNum: 0, cancelChoiceOrders: [] }); } }>取消录入</Button>
						</div>

					</div>
				) }
				width={ 500 }
				onCancel={ () => { setInputSerialNumberAbnormalModal({ visible: false, canCheckNum: 0, cancelChoiceOrders: [] }); } }
				zIndex={ 9998 }
			>
				<div className="r-flex r-pt-26">
					<ExclamationCircleOutlined className="r-c-primary r-mr-12" style={ { fontSize: 36, color: "#faad14" } } />
					<div className="r-ml-4">
						<p className="r-ta-l r-c-333">所选订单中部分订单不支持导入商品识别码，请不要选择以下订单：</p>
						<p className="r-c-error r-ta-l">1、非待付款、待发货的订单</p>
						<p className="r-c-error r-ta-l">2、非淘宝、天猫、抖音、快手订单</p>
					</div>
				</div>

			</Modal>
			<Modal
				visible={ sendStorageModal?.visible }
				centered
				title="设置发货仓库"
				confirmLoading={ sendStorageModal?.loading }
				onOk={ () => { hanldeSetStorageOk(); } }
				width={ 500 }
				onCancel={ () => { setSendStorageModal((prev) => { return { ...prev, visible: false }; }); } }
				zIndex={ 9998 }
			>
				<div style={ { color: "#f5821f", backgroundColor: "#fff6ed", padding: 12, marginBottom: 12 } }>温馨提示：更改仓库后请确保对应打单员（子账号）拥有该仓库权限</div>
				<Select placeholder="请选择仓库" allowClear maxTagCount={ 1 } style={ { width: "80%", marginBottom: 24 } } onChange={ handleChangeStorage }>
					{storageInfoList?.map((item) => {
						return (
							<Select.Option value={ item.id } disabled={ item.openStatus == 0 }>
								{item.storageName}（{item.storageTypeDesc}）
							</Select.Option>
						);
					})}
				</Select>
			</Modal>
			<StorageResShowModal visible={ storageResShowModal?.visible } resInfo={ storageResShowModal?.resInfo } onCancel={ () => { setStorageResShowModal({ visible: false, resInfo: {} }); } } />
		</div>
	);
});

export default observer(BottomCom);
