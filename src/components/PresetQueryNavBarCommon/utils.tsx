import React from 'react';
import { But<PERSON>, List } from 'antd';
import { toJS } from 'mobx';
import Empty from 'antd/es/empty';
import { isArray } from 'lodash';
import { SysMemoModeEnum, TradeAdvancedSearchGetListResponse, TradeSearchConditionConfig } from "@/types/trade/search/search";
import userStore from '@/stores/user';
import WarehouseStore from '@/stores/warehouse';
import { getTradePlatformLabel } from '@/pages/Trade/utils';
import { getMirrorEnum } from '@/utils';
import Pointer from '@/utils/pointTrack/constants';
import { 
	GOOD_STOCK_STATUS_OPTIONS, NEW_TIME_REMAINING_TYPE_OPTIONS, ORDER_PRINT_STATUS, ORDER_STATUS_OPTIONS, ORDER_TYPE_OPTIONS, REFUND_TYPE_OPTIONS, SELL_ATTR_OPTIONS,
	TRADE_EXCEPTION_INCLUDE_OPTIONS, TRADE_EXCEPTION_UNINCLUDE_OPTIONS, TRADE_LABEL_NORMAL_INCLUDE_OPTIONS, TRADE_LABEL_NORMAL_UNINCLUDE_OPTIONS, 
	TRADE_LABEL_OTHER_INCLUDE_OPTIONS, TRADE_LABEL_OTHER_UNINCLUDE_OPTIONS,
	TIME_TYPE_OPTIONS, LABEL_GENERATOR_STATUS
} from '@/pages/Trade/components/SearchContainer/constants';
import {
	BHD_ORDER_STATUS_OPTIONS,
	BHD_REFUND_STATUS_OPTIONS,
	BHD_STALL_STATUS_OPTIONS
} from '@/pages/Report/Bhd/BhdList/contants';
import { flagValueList } from '@/pages/Trade/components/SearchContainer/FlagAndMemoSelect/flagAndMemoSelect';
import { tradeStore } from '@/stores';
import { SystemMarkList } from '@/pages/Trade/components/SearchContainer/OrderMarkSelect';
import { itemInfoEnumValue, itemInfoQueryMap } from '@/pages/Trade/components/SearchContainer/itemInfoUtils';
import distributionStore from '@/stores/distribution';
import { getPrintNode, getValueByOldStatus } from '@/pages/Trade/components/SearchContainer/SearchCondition';
import { BizTypeEnum, GroupBizTypeEnum } from './constants';
import { 
	OrderSourceEnum,
	includeIngColorOrSizeEnum,
	getIncludeIngColorOrSize,
	getIsColorAndSizePrecise,
	renderRange,
	gramsToKilograms, 
	kilogramsToGrams, 
	renderKgRange,
	TRADE_LABEL_INCLUDE_ARR,
	TRADE_LABEL_NOT_INCLUDE_ARR,
	goodsIncludeStatusLabelMap
} from '@/pages/Trade/components/SearchContainer/utils';
import { 
	timeTypeOptions_aftersale,
	refundStatusListOptions_aftersale,
	reviewStatusOptions_aftersale,
	refundSystemStatusListOptions_aftersale,
	refundSystemTypeListOptions_aftersale,
	invoiceNoOptions_aftersale,
	tradeStatusOptions_aftersale,
	OfflineMemoEnum,
	isNoTradeMessOptions_aftersale,
	timeOutSearchOptions_aftersale,
	refundTagTypeOptions_aftersale,
	refundTagListOptions_aftersale,
	refundExceptionListOptions_aftersale,
	goodsStatusOptions,
	confirmItemStatusOptions_aftersale,
	handleReturnStatusOptions_aftersale,
	applyRefundFeeOptions_aftersale,
	exchangeRefundStatusListOptions_aftersale,
	scmRefundTypeOptions_aftersale,
	distributorIsPushOptions_aftersale,
	supplierIsConfirmOptions_aftersale
} from '@/pages/AfterSale/TradeList/constants';
import {
	ExpressStatusEnum,
	EXPRESS_STATUS_OPTIONS
} from '@/pages/AfterSale/constants';
import { 
	afterSaleTypeNames,
	afterSaleReason
} from '@/pages/AfterSale/TradeList/utils';
import { FunctionPermissionEnum } from '@/utils/permissionCheck/functionPermissionCheck';
import s from './index.module.scss';


// 是否包含时间范围选项
export const hasSelectTimeType = (type: BizTypeEnum) => {
	const arr = [BizTypeEnum.批打];
	return arr.includes(type);
};

// 预设条件管理权限
export const getPresetEditPermission = (type: BizTypeEnum) => {
	let allow = true;
	const { hasFunctionPermission } = userStore;
	switch (type) {
		case BizTypeEnum.批打:
			allow = hasFunctionPermission(FunctionPermissionEnum.预设条件管理);
			break;
		default:
			break;
	}
	return allow;
};

// 获取新增或者编辑分组的type
export const getBizType = (type: BizTypeEnum) => {
	let newType = '';
	switch (type) {
		case BizTypeEnum.批打:
			newType = GroupBizTypeEnum.订单查询分组;
			break;
		case BizTypeEnum.备货单查询:
			newType = GroupBizTypeEnum.备货单查询分组;
			break;
		case BizTypeEnum.备货单标签查询:
			newType = GroupBizTypeEnum.备货单标签查询分组;
			break;
		case BizTypeEnum.售后单查询:
			newType = GroupBizTypeEnum.售后单查询分组;
			break;
		case BizTypeEnum.分销商的售后单查询:
			newType = GroupBizTypeEnum.分销商的售后单查询分组;
			break;
		default:
			break;
	}
	return newType;
};

// 获取点位
export const getPointObj = (type: BizTypeEnum) => {
	let pointObj:{
		查询?: string;
		编辑?: string;
	} = {};
	switch (type) {
		case BizTypeEnum.批打:
			pointObj = {
				查询: Pointer.订单_订单打印_高级查询_查询,
				编辑: Pointer['点击-编辑预设查询条件'],
			};
			break;
		default:
			break;
	}
	return pointObj;
};

// 批打预设卡片
const getRuleTooltipTradeIndex = (rule, conditionSet, cb, data) => {
	let contentList: any[] = [];
	const { type, isShowKg } = data || {};
	try {
		const ruleContent = JSON.parse(rule?.ruleContent || '{}');
		
		conditionSet.forEach(conditionItem => {
			switch (conditionItem.condition) {
				case 'sysMemo':
					if (ruleContent?.sysMemoMode == SysMemoModeEnum.有线下备注) {
						let content = [<span className={ s["content-label"] }>有线下备注：</span>];
						content.push(
							<>{ruleContent?.sysMemoList?.join('、')}<br /></>
						);
						contentList.push(content);
					} else if (ruleContent?.sysMemoMode === SysMemoModeEnum.无线下备注) {
						let content = [<span className={ s["content-label"] }>无线下备注：</span>];
						contentList.push(content);
					}
					break;
				case 'timeType':
					if (ruleContent?.endTime) {
						let content = [<span className={ s["content-label"] }>截单时间：</span>];
						content.push(
							<>{`截止 ${ruleContent?.endTime} 的订单`}<br /></>
						);
						contentList.push(content);
					}
					if (ruleContent.payEndTime) {
						const timerArr = ruleContent.payEndTime.split(":");
						let content = [<span className={ s["content-label"] }>付款时间：</span>];
						content.push(
							<>{`超出付款时间${timerArr[0] == "undefined" ? "00" : timerArr[0]}时${timerArr[1] == "undefined" ? "00" : timerArr[1]}分${timerArr[2] == "undefined" ? "00" : timerArr[2]}秒的订单`}<br /></>
						);
						contentList.push(content);
					}
					break;
				
				case 'platform':
					if (ruleContent['platformInfo']?.plat_sellerIds?.length) {
						let content = [];
						const { shopList } = userStore;
						ruleContent['platformInfo'].plat_sellerIds.forEach((item, index) => {
							const [plat = '', sellerId = ''] = item.split('_');
							let shopItem = shopList?.find(i => i.sellerId === sellerId);
							if (shopItem) {
								content.push(
									<>
										{getTradePlatformLabel(plat)}
										<span>{shopItem.sellerNick}{index !== ruleContent['platformInfo'].plat_sellerIds.length - 1 ? '、' : ''}</span>
									</>
								);
							}
						});
						if (content.length) {
							content.unshift([<span className={ s["content-label"] }>平台店铺：</span>]);
							content.push(<br />);
							contentList.push(content);
						}
					}
					break;

				case 'orderSource':
					if (ruleContent?.orderSource) {
						let content = [<span className={ s["content-label"] }>订单来源：</span>];
						content.push(
							<>{getMirrorEnum(OrderSourceEnum)[ruleContent?.orderSource]}<br /></>
						);
						contentList.push(content);

						if (ruleContent?.distributorUserIds?.length) {
							let _content = [];
							const { distributorList = [] } = distributionStore;
							let arr = [];
							ruleContent?.distributorUserIds.forEach(id => {
								let item = distributorList.find(item => item.saleUserId === id);
								if (item) {
									arr.push(item.saleName);
								}
							});
							_content.push([<span className={ s["content-label"] }>分销商：</span>]);
							_content.push(arr.join('、'));
							_content.push(<br />);
							contentList.push(_content);
						}
					}
					break;

				case 'tradeType':
					if (ruleContent?.tradeType && ruleContent?.tradeType !== ORDER_TYPE_OPTIONS.default) {
						let content = [<span className={ s["content-label"] }>订单类型：</span>];
						content.push(
							<>{ORDER_TYPE_OPTIONS.options.find(i => i.key === ruleContent?.tradeType)?.value}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'status':
					if (ruleContent?.status) {
						let content = [<span className={ s["content-label"] }>订单状态：</span>];
						content.push(
							<>{ORDER_STATUS_OPTIONS.options.find(i => i.key === ruleContent?.status)?.value}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'printStatus':
					if (ruleContent?.printStatus || ruleContent?.allPrintStatus) {
						const printObj = ruleContent?.allPrintStatus || getValueByOldStatus(ruleContent?.printStatus);
						const arr = [printObj?.printStatus, printObj?.fhdPrintStatus || '', printObj?.labelPrintStatus || ''].join(',');
						let content = [<span className={ s["content-label"] }>打印状态：</span>];
						console.log('printObj', printObj, arr);
						content.push(<>{ getPrintNode(arr, printObj)}</>);
						contentList.push(content);
					}
					break;

				case 'refundStatus':
					if (ruleContent?.refundStatus) {
						if (Array.isArray(ruleContent?.refundStatus)) {
							ruleContent.refundStatus = ruleContent?.refundStatus?.length == 1 ? ruleContent?.refundStatus[0] : '';
						}
						let content = [<span className={ s["content-label"] }>退款状态：</span>];
						content.push(
							<>{REFUND_TYPE_OPTIONS.options.find(i => i.key === ruleContent?.refundStatus)?.value}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'goodStockStatus':
					if (ruleContent?.goodStockStatus && ruleContent?.goodStockStatus !== GOOD_STOCK_STATUS_OPTIONS.default) {
						let content = [<span className={ s["content-label"] }>订单缺货状态：</span>];
						content.push(
							<>{GOOD_STOCK_STATUS_OPTIONS.options.find(i => i.key === ruleContent?.goodStockStatus)?.value}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'tid':
					if (ruleContent?.tid) {
						let content = [<span className={ s["content-label"] }>订单编号：</span>];
						content.push(
							<>{ruleContent?.tid.split(',').join('、')}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'sid':
					if (ruleContent?.sid) {
						let content = [<span className={ s["content-label"] }>快递单号：</span>];
						content.push(
							<>{ruleContent?.sid.split(',').join('、')}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'itemInclude': {
					const { goodsIncludeStatus } = ruleContent;
					const key = Object.keys(itemInfoQueryMap).find(key => key == goodsIncludeStatus);
					const label = itemInfoQueryMap[key]?.label;
					if (label) {
						const { shortNameIncludingList = [] } = ruleContent;
						if (shortNameIncludingList.length) {
							let content = [<span className={ s["content-label"] }>{label}(包含)：</span>];
							content.push(
								<>{shortNameIncludingList.join(',')}<br /></>
							);
							contentList.push(content);
						}
					} else if (ruleContent?.goodsIncludeStatus == itemInfoEnumValue.商品包含 || ruleContent?.goodsIncludeStatus == itemInfoEnumValue.货品包含) {
						// * 不要改成includes 老版的goodsIncludeStatus是number类型
						const { goodsIncludeStatus } = ruleContent;
						let { shortNameIncludingList = [], skuIncludingList = [] } = ruleContent;
						let skuIncluding;
						let shortNameIncluding;
						const { itemSku } = ruleContent;
						if (itemSku?.length) {
							skuIncluding = itemSku[0].skuIncluding;
							shortNameIncluding = itemSku[0].shortNameIncluding;
						}
						if (skuIncluding && !skuIncludingList.length) skuIncludingList = [skuIncluding.trim()];
						if (shortNameIncluding && !shortNameIncludingList.length) shortNameIncludingList = [shortNameIncluding.trim()];
						const label = goodsIncludeStatusLabelMap[goodsIncludeStatus];
						if (shortNameIncludingList.length) {
							let content = [<span className={ s["content-label"] }>{label}(包含)：</span>];
							content.push(
								<>{shortNameIncludingList.join(',')}<br /></>
							);
							contentList.push(content);
						}

						if (skuIncludingList.length) {
							let content = [<span className={ s["content-label"] }>{goodsIncludeStatus == itemInfoEnumValue.商品包含 ? '' : '货品'}规格(包含)：</span>];
							content.push(
								<>{skuIncludingList.join(',')}<br /></>
							);
							contentList.push(content);
						}
					}
					break;
				}

				case 'itemNotInclude': {
					const { goodsNotIncludeStatus } = ruleContent;
					const key = Object.keys(itemInfoQueryMap).find(key => key == goodsNotIncludeStatus);
					const label = itemInfoQueryMap[key]?.label;
					if (label) {
						const { shortNameNotIncludingList = [] } = ruleContent;
						if (shortNameNotIncludingList.length) {
							let content = [<span className={ s["content-label"] }>{label}(不包含)：</span>];
							content.push(
								<>{shortNameNotIncludingList.join(',')}<br /></>
							);
							contentList.push(content);
						}
					} else if ([itemInfoEnumValue.商品不包含, itemInfoEnumValue.货品不包含].includes(goodsNotIncludeStatus)) {
						const { shortNameNotIncludingList = [], skuNotIncludingList = [] } = ruleContent;
						const label = goodsIncludeStatusLabelMap[goodsNotIncludeStatus];
						if (shortNameNotIncludingList.length) {
							let content = [<span className={ s["content-label"] }>{label}(不包含)：</span>];
							content.push(
								<>{shortNameNotIncludingList.join(',')}<br /></>
							);
							contentList.push(content);
						}

						if (skuNotIncludingList.length) {
							let content = [<span className={ s["content-label"] }>{goodsNotIncludeStatus === itemInfoEnumValue.商品不包含 ? '' : '货品'}规格(不包含)：</span>];
							content.push(
								<>{skuNotIncludingList.join(',')}<br /></>
							);
							contentList.push(content);
						}
					}
					break;
				}


				case 'colorSizeIncludingList':
					if ('includeIngColorOrSizeTwo' in ruleContent) {
						ruleContent.includeIngColorOrSize = getIncludeIngColorOrSize(ruleContent?.includeIngColorOrSizeTwo);
						ruleContent.isColorAndSizePrecise = getIsColorAndSizePrecise(ruleContent?.includeIngColorOrSizeTwo);
					} else {
						ruleContent.isColorAndSizePrecise = !ruleContent?.precise?.includes("isPrecise");
					}
					if (ruleContent?.colorIncludingList?.length) {
						let content = [<span className={ s["content-label"] }>颜色{ruleContent?.includeIngColorOrSize ? '包含' : '不包含'}({ruleContent?.isColorAndSizePrecise ? '模糊' : '精确'})：</span>];
						content.push(
							<>{ruleContent?.colorIncludingList.join(',')}<br /></>
						);
						contentList.push(content);
					}

					if (ruleContent?.sizeIncludingList?.length) {
						let content = [<span className={ s["content-label"] }>尺码{ruleContent?.includeIngColorOrSize ? '包含' : '不包含'}({ruleContent?.isColorAndSizePrecise ? '模糊' : '精确'})：</span>];
						content.push(
							<>{ruleContent?.sizeIncludingList.join(',')}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'flagValue':
					if (ruleContent?.flagValue) {
						if (ruleContent?.flagValue === '-1') {
							if (ruleContent?.buyerMessage) {
								let content = [<span className={ s["content-label"] }>留言：</span>];
								content.push(
									<>{ruleContent?.buyerMessage}<br /></>
								);
								contentList.push(content);
							}

							if (ruleContent?.sellerMemo || ruleContent?.sellerFlag?.length) {
								let content = [<span className={ s["content-label"] }>备注：</span>];
								let sellerFlagStr = ruleContent?.sellerFlag?.map(item => flagValueList.find(i => i.key === item)?.value)?.join(',');
								content.push(
									<>{ruleContent?.sellerMemo} {sellerFlagStr ? `[${sellerFlagStr}]` : ''}<br /></>
								);
								contentList.push(content);
							}
						} else {
							let content = [<span className={ s["content-label"] }>留言备注：</span>];
							let temp = flagValueList.find(i => i.key === ruleContent?.flagValue)?.value;
							if (!temp) {
								const flagList = tradeStore.flagList || [];
								temp = flagList?.find(i => i.selValue === ruleContent?.flagValue)?.comment || '自定义';
							}

							content.push(
								<>{temp}<br /></>
							);
							contentList.push(content);
						}
					} else {
						if (ruleContent?.sellerMemo || ruleContent?.sellerFlag?.length) {
							let content = [<span className={ s["content-label"] }>备注：</span>];
							let sellerFlagStr = ruleContent?.sellerFlag?.map(item => flagValueList.find(i => i.key === item)?.value)?.join(',');
							content.push(
								<>{ruleContent?.sellerMemo} {sellerFlagStr ? `[${sellerFlagStr}]` : ''}<br /></>
							);
							contentList.push(content);
						}
						if (ruleContent?.buyerMessage) {
							let content = [<span className={ s["content-label"] }>留言：</span>];
							content.push(
								<>{ruleContent?.buyerMessage}<br /></>
							);
							contentList.push(content);
						}
					}
					break;

				case 'bizMark':
					if (ruleContent?.bizMarkObj?.bizMark) {
						const { value } = ruleContent?.bizMarkObj;
						let content = [<span className={ s["content-label"] }>订单标记：</span>];
						let temp = SystemMarkList.find(i => i.value === value)?.name;
						if (!temp) {
							const markList = tradeStore.markList || [];
							temp = markList?.find(i => i.id === value)?.remark || '自定义';
						}

						content.push(
							<>{temp}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'buyerNick':
					if (ruleContent?.buyerNick) {
						let content = [<span className={ s["content-label"] }>买家昵称：</span>];
						content.push(
							<>{ruleContent?.buyerNick}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'areaContain':
					if (ruleContent?.addressInfo?.id) {
						let content = [<span className={ s["content-label"] }>省市区：</span>];
						content.push(
							<>{ruleContent?.addressInfo?.name}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'receiveName':
					if (ruleContent?.receiveName) {
						let content = [<span className={ s["content-label"] }>收件人：</span>];
						content.push(
							<>{ruleContent?.receiveName}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'mobile':
					if (ruleContent?.mobile) {
						let content = [<span className={ s["content-label"] }>手机号：</span>];
						content.push(
							<>{ruleContent?.mobile}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'residueSendTime':
					if (ruleContent?.customizeResidueSendTime) {
						let desc = NEW_TIME_REMAINING_TYPE_OPTIONS.options.find(item => item.key === ruleContent?.customizeResidueSendTime)?.value;

						if (!desc) {
							const [startTime, endTime] = ruleContent?.customizeResidueSendTime?.split('-');
							if (startTime === 'none') {
								desc = `剩余时间小于${endTime}小时`;
							} else if (endTime === 'none') {
								desc = `剩余时间大于${startTime}小时`;
							} else {
								desc = `剩余时间介于${startTime}-${endTime}小时之间`;
							}
						}

						let content = [<span className={ s["content-label"] }>剩余发货时间：</span>];
						content.push(
							<>{desc}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'exceptionFlag':
					if (ruleContent?.tradeExceptionList?.length) {
						let tempArr = [];
						let labelContent = '';
						ruleContent?.tradeExceptionList.forEach(key => {
							let item = TRADE_EXCEPTION_INCLUDE_OPTIONS.find(item => item.key === key);
							if (!item) {
								item = TRADE_EXCEPTION_UNINCLUDE_OPTIONS.find(item => item.key === key);
								labelContent = '订单异常(不包含)：';
							} else {
								labelContent = '订单异常(包含)：';
							}

							item.value && tempArr.push(item.value);
						});
						let content = [<span className={ s["content-label"] }>{labelContent}</span>];
						content.push(
							<>{tempArr.join(',')}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'quickQuery':
					if (ruleContent?.tradeLabelList?.length) {
						let tempArr = [];
						let labelContent = '';
						ruleContent?.tradeLabelList.forEach(key => {
							let item = TRADE_LABEL_INCLUDE_ARR.find(item => item.key === key);
							if (!item) {
								item = TRADE_LABEL_NOT_INCLUDE_ARR.find(item => item.key === key);
								labelContent = '订单标签(不包含)：';
							} else {
								labelContent = '订单标签(包含)：';
							}

							item.value && tempArr.push(item.value);
						});
						let content = [<span className={ s["content-label"] }>{labelContent}</span>];
						content.push(
							<>{tempArr.join(',')}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'goodsTotalNumRange':
					if (ruleContent?.goodsTotalNumRange) {
						const [min, max] = ruleContent?.goodsTotalNumRange;
						if (min || max) {
							let content = [<span className={ s["content-label"] }>商品数量：</span>];
							content.push(renderRange(min, max));
							contentList.push(content);
						}
					}
					break;

				case 'goodsTypeNumRange':
					if (ruleContent?.goodsTypeNumRange) {
						const [min, max] = ruleContent?.goodsTypeNumRange;
						if (min || max) {
							let content = [<span className={ s["content-label"] }>商品种类：</span>];
							content.push(renderRange(min, max));
							contentList.push(content);
						}
					}
					break;

				case 'paymentRange':
					if (ruleContent?.paymentRange) {
						const [min, max] = ruleContent?.paymentRange;
						if (min || max) {
							let content = [<span className={ s["content-label"] }>订单金额：</span>];
							content.push(renderRange(min, max));
							contentList.push(content);
						}
					}
					break;

				case 'weightRange':
					if (ruleContent?.weightRange) {
						const [min, max] = ruleContent?.weightRange;
						if (min || max) {
							let content = [<span className={ s["content-label"] }>订单重量：</span>];
							content.push(renderKgRange(min, max, isShowKg));
							contentList.push(content);
						}
					}
					break;

				case 'sellAttribute':
					if (ruleContent?.sellAttributeList?.length) {
						let sellerOptionMap = {};
						SELL_ATTR_OPTIONS.options.forEach(i => sellerOptionMap[i.key] = i.value);
						let content = [<span className={ s["content-label"] }>销售属性：</span>];
						content.push(
							<>{ruleContent?.sellAttributeList.map(i => sellerOptionMap[i]).join(',')}<br /></>
						);
						contentList.push(content);
						return;
					}
					if (ruleContent?.sellAttribute && ruleContent?.sellAttribute !== SELL_ATTR_OPTIONS.default) {
						let content = [<span className={ s["content-label"] }>销售属性：</span>];
						content.push(
							<>{SELL_ATTR_OPTIONS.options.find(i => i.key === ruleContent?.sellAttribute)?.value}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'supplierIncludingList':
					if (ruleContent?.supplierIncludingList?.length) {
						let content = [<span className={ s["content-label"] }>供应商：</span>];
						content.push(
							<>{ruleContent?.supplierIncludingList.join(',')}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'marketIncludingList':
					if (ruleContent?.marketIncludingList?.length) {
						let content = [<span className={ s["content-label"] }>市场：</span>];
						content.push(
							<>{ruleContent?.marketIncludingList.join(',')}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'dangKouIncludingList':
					if (ruleContent?.dangKouIncludingList?.length) {
						let content = [<span className={ s["content-label"] }>档口：</span>];
						content.push(
							<>{ruleContent?.dangKouIncludingList.join(',')}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'authorIncludingList':
					if (ruleContent?.authorIncludingList?.length) {
						let content = [<span className={ s["content-label"] }>达人名称/ID：</span>];
						content.push(
							<>{ruleContent?.authorIncludingList.join(',')}<br /></>
						);
						contentList.push(content);
					}
					break;
				default:
					break;
			}
		});

		if (ruleContent?.precise?.includes?.('isPrecise') || ruleContent?.precise === true) {
			contentList.push([<span className={ s["content-label"] }>宝贝精确查询：</span>, <>是</>]);
		}
	} catch (error) {
		console.error(error, '解析预设content报错');
	}
	return (
		<div className={ s["rule-tooltip-content"] } style={ { padding: '4px 8px' } } onClick={ e => e.stopPropagation() }>
			{contentList.length === 0 && (
				<Empty description="暂未设置查询条件" />
			)}
			{contentList.length !== 0 && (
				<List
					size="small"
					dataSource={ contentList.slice(0, 10) }
					renderItem={ item => {
						const [label = '', ...content] = item;
						return (
							<List.Item className="r-mb-4" style={ { padding: 4 } } key={ item.label }>
								{label}
								<div className="r-oneline">{content}</div>
							</List.Item>
						);
					} }
				/>
			)}
			{contentList.length > 10 && (
				<div className="r-w-full r-flex r-jc-c">
					<Button type="link" onClick={ cb }>查看完整条件</Button>
				</div>
			)}
		</div>
	);
};

// 备货单卡片
const getRuleTooltipBhdIndex = (rule, conditionSet, cb, data) => {
	let contentList: any[] = [];
	const { type, isShowKg } = data || {};
	try {
		const ruleContent = JSON.parse(rule?.ruleContent || '{}');
		
		conditionSet.forEach(conditionItem => {
			switch (conditionItem.condition) {
				// "平台&店铺"
				case 'platform':
					if (ruleContent['platformInfo']?.plat_sellerIds?.length) {
						let content = [];
						const { shopList } = userStore;
						ruleContent['platformInfo'].plat_sellerIds.forEach((item, index) => {
							const [plat = '', sellerId = ''] = item.split('_');
							let shopItem = shopList?.find(i => i.sellerId === sellerId);
							if (shopItem) {
								content.push(
									<>
										{getTradePlatformLabel(plat)}
										<span>{shopItem.sellerNick}{index !== ruleContent['platformInfo'].plat_sellerIds.length - 1 ? '、' : ''}</span>
									</>
								);
							}
						});
						if (content.length) {
							content.unshift([<span className={ s["content-label"] }>平台店铺：</span>]);
							content.push(<br />);
							contentList.push(content);
						}
					}
					break;
				
				// "时间范围" : 这里只显示时间选择类型
				case 'timeType':
					if (ruleContent?.timeType) {
						let content = [<span className={ s["content-label"] }>时间类型：</span>];
						let name = TIME_TYPE_OPTIONS.options?.find(i => i.key == ruleContent?.timeType)?.value;
						content.push(
							<>{name}<br /></>
						);
						contentList.push(content);
					}
					break;

				// "订单来源"
				case 'orderSource':
					if (ruleContent?.orderSource) {
						let content = [<span className={ s["content-label"] }>订单来源：</span>];
						content.push(
							<>{getMirrorEnum(OrderSourceEnum)[ruleContent?.orderSource]}<br /></>
						);
						contentList.push(content);

						if (ruleContent?.distributorUserIds?.length) {
							let _content = [];
							const { distributorList = [] } = distributionStore;
							let arr = [];
							ruleContent?.distributorUserIds.forEach(id => {
								let item = distributorList.find(item => item.saleUserId === id);
								if (item) {
									arr.push(item.saleName);
								}
							});
							_content.push([<span className={ s["content-label"] }>分销商：</span>]);
							_content.push(arr.join('、'));
							_content.push(<br />);
							contentList.push(_content);
						}
					}
					break;
				
					// "订单状态": 这里状态不一样，多个全部状态ALL_STATUS
				case 'status':
					if (ruleContent?.status) {
						let content = [<span className={ s["content-label"] }>订单状态：</span>];
						content.push(
							<>{BHD_ORDER_STATUS_OPTIONS.options.find(i => i.key === ruleContent?.status)?.value || '全部状态'}<br /></>
						);
						contentList.push(content);
					}
					break;
				
					// "打印状态": 这里和批打不一样
				case 'printStatus':
					if (ruleContent?.printStatus) {
						let content = [<span className={ s["content-label"] }>打印状态：</span>];
						content.push(
							<>{ORDER_PRINT_STATUS.options.find(i => i.key === ruleContent?.printStatus)?.value}<br /></>
						);
						contentList.push(content);
					}
					break;
	
					// "销售属性"
				case 'sellAttribute':
					if (ruleContent?.sellAttributeList?.length) {
						let sellerOptionMap = {};
						SELL_ATTR_OPTIONS.options.forEach(i => sellerOptionMap[i.key] = i.value);
						let content = [<span className={ s["content-label"] }>销售属性：</span>];
						content.push(
							<>{ruleContent?.sellAttributeList.map(i => sellerOptionMap[i]).join(',')}<br /></>
						);
						contentList.push(content);
						return;
					}
					break;

					// 缺货状态
				case 'goodStockStatus':
					if (ruleContent?.goodStockStatus && ruleContent?.goodStockStatus !== GOOD_STOCK_STATUS_OPTIONS.default) {
						let content = [<span className={ s["content-label"] }>订单缺货状态：</span>];
						content.push(
							<>{GOOD_STOCK_STATUS_OPTIONS.options.find(i => i.key === ruleContent?.goodStockStatus)?.value}<br /></>
						);
						contentList.push(content);
					}
					break;
				
					// "商品包含"
				case 'itemInclude': {
					const { goodsIncludeStatus } = ruleContent;
					const key = Object.keys(itemInfoQueryMap).find(key => key == goodsIncludeStatus);
					const label = itemInfoQueryMap[key]?.label;
					if (label) {
						const { shortNameIncludingList = [] } = ruleContent;
						if (shortNameIncludingList.length) {
							let content = [<span className={ s["content-label"] }>{label}(包含)：</span>];
							content.push(
								<>{shortNameIncludingList.join(',')}<br /></>
							);
							contentList.push(content);
						}
					} else if (ruleContent?.goodsIncludeStatus == itemInfoEnumValue.商品包含 || ruleContent?.goodsIncludeStatus == itemInfoEnumValue.货品包含) {
						// * 不要改成includes 老版的goodsIncludeStatus是number类型
						const { goodsIncludeStatus } = ruleContent;
						let { shortNameIncludingList = [], skuIncludingList = [] } = ruleContent;
						let skuIncluding;
						let shortNameIncluding;
						const { itemSku } = ruleContent;
						if (itemSku?.length) {
							skuIncluding = itemSku[0].skuIncluding;
							shortNameIncluding = itemSku[0].shortNameIncluding;
						}
						if (skuIncluding && !skuIncludingList.length) skuIncludingList = [skuIncluding.trim()];
						if (shortNameIncluding && !shortNameIncludingList.length) shortNameIncludingList = [shortNameIncluding.trim()];
						const label = goodsIncludeStatusLabelMap[goodsIncludeStatus];
						if (shortNameIncludingList.length) {
							let content = [<span className={ s["content-label"] }>{label}(包含)：</span>];
							content.push(
								<>{shortNameIncludingList.join(',')}<br /></>
							);
							contentList.push(content);
						}
	
						if (skuIncludingList.length) {
							let content = [<span className={ s["content-label"] }>{goodsIncludeStatus == itemInfoEnumValue.商品包含 ? '' : '货品'}规格(包含)：</span>];
							content.push(
								<>{skuIncludingList.join(',')}<br /></>
							);
							contentList.push(content);
						}
					}
					break;
				}
	
				// "商品不包含"
				case 'itemNotInclude': {
					const { goodsNotIncludeStatus } = ruleContent;
					const key = Object.keys(itemInfoQueryMap).find(key => key == goodsNotIncludeStatus);
					const label = itemInfoQueryMap[key]?.label;
					if (label) {
						const { shortNameNotIncludingList = [] } = ruleContent;
						if (shortNameNotIncludingList.length) {
							let content = [<span className={ s["content-label"] }>{label}(不包含)：</span>];
							content.push(
								<>{shortNameNotIncludingList.join(',')}<br /></>
							);
							contentList.push(content);
						}
					} else if ([itemInfoEnumValue.商品不包含, itemInfoEnumValue.货品不包含].includes(goodsNotIncludeStatus)) {
						const { shortNameNotIncludingList = [], skuNotIncludingList = [] } = ruleContent;
						const label = goodsIncludeStatusLabelMap[goodsNotIncludeStatus];
						if (shortNameNotIncludingList.length) {
							let content = [<span className={ s["content-label"] }>{label}(不包含)：</span>];
							content.push(
								<>{shortNameNotIncludingList.join(',')}<br /></>
							);
							contentList.push(content);
						}

						if (skuNotIncludingList.length) {
							let content = [<span className={ s["content-label"] }>{goodsNotIncludeStatus === itemInfoEnumValue.商品不包含 ? '' : '货品'}规格(不包含)：</span>];
							content.push(
								<>{skuNotIncludingList.join(',')}<br /></>
							);
							contentList.push(content);
						}
					}
					break;
				}
				
				// "留言备注" : 这里显示留言或备注，不兼容老数据
				case 'flagValue':
					if (ruleContent?.flagValue) {
						if (ruleContent?.flagValue === '-1') {
							if (ruleContent?.buyerMessageOrSellerMemo) {
								let content = [<span className={ s["content-label"] }>留言或备注：</span>];
								content.push(
									<>{ruleContent?.buyerMessageOrSellerMemo}<br /></>
								);
								contentList.push(content);
							}

							if (ruleContent?.sellerFlag?.length) {
								let content = [<span className={ s["content-label"] }>旗帜：</span>];
								let sellerFlagStr = ruleContent?.sellerFlag?.map(item => flagValueList.find(i => i.key === item)?.value)?.join(',');
								content.push(
									<>{sellerFlagStr ? `[${sellerFlagStr}]` : ''}<br /></>
								);
								contentList.push(content);
							}
						} else {
							let content = [<span className={ s["content-label"] }>留言备注：</span>];
							let temp = flagValueList.find(i => i.key === ruleContent?.flagValue)?.value;
							if (!temp) {
								const flagList = tradeStore.flagList || [];
								temp = flagList?.find(i => i.selValue === ruleContent?.flagValue)?.comment || '自定义';
							}

							content.push(
								<>{temp}<br /></>
							);
							contentList.push(content);
						}
					}
					break;
				

				// "退款状态": 单选
				case 'refundStatus':
					if (ruleContent?.refundStatus) {
						let content = [<span className={ s["content-label"] }>退款状态：</span>];
						content.push(
							<>{BHD_REFUND_STATUS_OPTIONS.options.find(i => i.key === ruleContent?.refundStatus)?.value}<br /></>
						);
						contentList.push(content);
					}
					break;

					// "省市区"
				case 'areaContain':
					if (ruleContent?.addressInfo?.id) {
						let content = [<span className={ s["content-label"] }>省市区：</span>];
						content.push(
							<>{ruleContent?.addressInfo?.name}<br /></>
						);
						contentList.push(content);
					}
					break;

					// "买家昵称"
				case 'buyerNick':
					if (ruleContent?.buyerNick) {
						let content = [<span className={ s["content-label"] }>买家昵称：</span>];
						content.push(
							<>{ruleContent?.buyerNick}<br /></>
						);
						contentList.push(content);
					}
					break;

					// "系统单号"
				case 'tid':
					if (ruleContent?.tid) {
						let content = [<span className={ s["content-label"] }>系统单号：</span>];
						content.push(
							<>{ruleContent?.tid.split(',').join('、')}<br /></>
						);
						contentList.push(content);
					}
					break;

					// "订单编号"
				case 'ptTid':
					if (ruleContent?.ptTid) {
						let content = [<span className={ s["content-label"] }>订单编号：</span>];
						content.push(
							<>{ruleContent?.ptTid.split(',').join('、')}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'sid':
					if (ruleContent?.sid) {
						let content = [<span className={ s["content-label"] }>快递单号：</span>];
						content.push(
							<>{ruleContent?.sid.split(',').join('、')}<br /></>
						);
						contentList.push(content);
					}
					break;

					// "供应商包含" : 供应商 市场 档口
				case 'supplierIncludingList':
					if (ruleContent?.supplierIncludingList?.length || ruleContent?.marketIncludingList?.length || ruleContent?.dangKouIncludingList?.length) {
						let content = [<span className={ s["content-label"] }>供应商包含：</span>];

						let arr = [];
						if (ruleContent?.supplierIncludingList?.length) {
							arr.push(`供应商：${ruleContent?.supplierIncludingList.join(',')}`);
						}
						if (ruleContent?.marketIncludingList?.length) {
							arr.push(`市场：${ruleContent?.marketIncludingList.join(',')}`);
						}
						if (ruleContent?.dangKouIncludingList?.length) {
							arr.push(`档口：${ruleContent?.dangKouIncludingList.join(',')}`);
						}
						content.push(<>{arr.join('、')}<br /></>);
						contentList.push(content);
					}
					break;

					// "供应商不包含" : 供应商 市场 档口
				case 'supplierNotIncludingList':
					if (ruleContent?.supplierNoIncludingList?.length || ruleContent?.marketNoIncludingList?.length || ruleContent?.dangKouNoIncludingList?.length) {
						let content = [<span className={ s["content-label"] }>供应商不包含：</span>];

						let arr = [];
						if (ruleContent?.supplierNoIncludingList?.length) {
							arr.push(`供应商：${ruleContent?.supplierNoIncludingList.join(',')}`);
						}
						if (ruleContent?.marketNoIncludingList?.length) {
							arr.push(`市场：${ruleContent?.marketNoIncludingList.join(',')}`);
						}
						if (ruleContent?.dangKouNoIncludingList?.length) {
							arr.push(`档口：${ruleContent?.dangKouNoIncludingList.join(',')}`);
						}
						content.push(<>{arr.join('、')}<br /></>);
						contentList.push(content);
					}
					break;

					// "剩余发货时间"
				case 'residueSendTime':
					if (ruleContent?.customizeResidueSendTime) {
						let desc = NEW_TIME_REMAINING_TYPE_OPTIONS.options.find(item => item.key === ruleContent?.customizeResidueSendTime)?.value;
	
						if (!desc) {
							const [startTime, endTime] = ruleContent?.customizeResidueSendTime?.split('-');
							if (startTime === 'none') {
								desc = `剩余时间小于${endTime}小时`;
							} else if (endTime === 'none') {
								desc = `剩余时间大于${startTime}小时`;
							} else {
								desc = `剩余时间介于${startTime}-${endTime}小时之间`;
							}
						}
	
						let content = [<span className={ s["content-label"] }>剩余发货时间：</span>];
						content.push(
							<>{desc}<br /></>
						);
						contentList.push(content);
					}
					break;
	
					// "订单标记"
				case 'bizMark':
					if (ruleContent?.bizMarkObj?.bizMark) {
						const { value } = ruleContent?.bizMarkObj;
						let content = [<span className={ s["content-label"] }>订单标记：</span>];
						let temp = SystemMarkList.find(i => i.value === value)?.name;
						if (!temp) {
							const markList = tradeStore.markList || [];
							temp = markList?.find(i => i.id === value)?.remark || '自定义';
						}
	
						content.push(
							<>{temp}<br /></>
						);
						contentList.push(content);
					}
					break;

					// "订单类型" : 这里不一样
				case 'tradeType':
					if (ruleContent?.tradeTypeTagList) {
						let content = [<span className={ s["content-label"] }>订单类型：</span>];
						content.push(
							<>{ORDER_TYPE_OPTIONS.options.find(i => i.key === ruleContent?.tradeTypeTagList)?.value}<br /></>
						);
						contentList.push(content);
					}
					break;

					// "订单标签"
				case 'quickQuery':
					if (ruleContent?.tradeLabelList?.length) {
						let tempArr = [];
						let labelContent = '';
						ruleContent?.tradeLabelList.forEach(key => {
							let item = TRADE_LABEL_INCLUDE_ARR.find(item => item.key === key);
							if (!item) {
								item = TRADE_LABEL_NOT_INCLUDE_ARR.find(item => item.key === key);
								labelContent = '订单标签(不包含)：';
							} else {
								labelContent = '订单标签(包含)：';
							}

							item.value && tempArr.push(item.value);
						});
						let content = [<span className={ s["content-label"] }>{labelContent}</span>];
						content.push(
							<>{tempArr.join(',')}<br /></>
						);
						contentList.push(content);
					}
					break;
				
				// "订单异常"
				case 'exceptionFlag':
					if (ruleContent?.tradeExceptionList?.length) {
						let tempArr = [];
						let labelContent = '';
						ruleContent?.tradeExceptionList.forEach(key => {
							let item = TRADE_EXCEPTION_INCLUDE_OPTIONS.find(item => item.key === key);
							if (!item) {
								item = TRADE_EXCEPTION_UNINCLUDE_OPTIONS.find(item => item.key === key);
								labelContent = '订单异常(不包含)：';
							} else {
								labelContent = '订单异常(包含)：';
							}
	
							item.value && tempArr.push(item.value);
						});
						let content = [<span className={ s["content-label"] }>{labelContent}</span>];
						content.push(
							<>{tempArr.join(',')}<br /></>
						);
						contentList.push(content);
					}
					break;
			
				
				case 'receiveName':
					if (ruleContent?.receiveName) {
						let content = [<span className={ s["content-label"] }>收件人：</span>];
						content.push(
							<>{ruleContent?.receiveName}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'mobile':
					if (ruleContent?.mobile) {
						let content = [<span className={ s["content-label"] }>手机号：</span>];
						content.push(
							<>{ruleContent?.mobile}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'goodsTotalNumRange':
					if (ruleContent?.goodsTotalNumRange) {
						const [min, max] = ruleContent?.goodsTotalNumRange;
						if (min || max) {
							let content = [<span className={ s["content-label"] }>商品数量：</span>];
							content.push(renderRange(min, max));
							contentList.push(content);
						}
					}
					break;

				case 'goodsTypeNumRange':
					if (ruleContent?.goodsTypeNumRange) {
						const [min, max] = ruleContent?.goodsTypeNumRange;
						if (min || max) {
							let content = [<span className={ s["content-label"] }>商品种类：</span>];
							content.push(renderRange(min, max));
							contentList.push(content);
						}
					}
					break;

				case 'paymentRange':
					if (ruleContent?.paymentRange) {
						const [min, max] = ruleContent?.paymentRange;
						if (min || max) {
							let content = [<span className={ s["content-label"] }>订单金额：</span>];
							content.push(renderRange(min, max));
							contentList.push(content);
						}
					}
					break;

				case 'weightRange':
					if (ruleContent?.weightRange) {
						const [min, max] = ruleContent?.weightRange;
						if (min || max) {
							let content = [<span className={ s["content-label"] }>订单重量：</span>];
							content.push(renderKgRange(min, max, isShowKg));
							contentList.push(content);
						}
					}
					break;

				case 'authorIncludingList':
					if (ruleContent?.authorIncludingList?.length) {
						let content = [<span className={ s["content-label"] }>达人名称/ID：</span>];
						content.push(
							<>{ruleContent?.authorIncludingList.join(',')}<br /></>
						);
						contentList.push(content);
					}
					break;

					// 发货仓库： 取仓库名称 WarehouseStore
				case 'storageIdList':
					if (ruleContent?.storageIdList?.length) {
						const { storageInfoList = [] } = WarehouseStore;
						let arr = [];
						ruleContent?.storageIdList?.forEach(id => {
							let item = storageInfoList?.find(item => item.id === id);
							if (item) {
								arr.push(item.storageName);
							}
						});

						let content = [<span className={ s["content-label"] }>发货仓库：</span>];
						content.push(
							<>{arr.join(',')}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'isSetAlias':
					if (ruleContent?.existItemAlias || ruleContent?.existItemAlias === 0) {
						let content = [<span className={ s["content-label"] }>是否设置简称：</span>];
						content.push(
							<>{ruleContent?.existItemAlias === 0 ? '是' : '否'}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'isSetSkuAlias':
					if (ruleContent?.existSkuAlias || ruleContent?.existSkuAlias === 0) {
						let content = [<span className={ s["content-label"] }>是否设置规格别名：</span>];
						content.push(
							<>{ruleContent?.existSkuAlias === 0 ? '是' : '否'}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'isSetWeight':
					if (ruleContent?.existWeight || ruleContent?.existWeight === 0) {
						let content = [<span className={ s["content-label"] }>是否设置重量：</span>];
						content.push(
							<>{ruleContent?.existWeight === 0 ? '是' : '否'}<br /></>
						);
						contentList.push(content);
					}
					break;

				case 'isSetCostPrice':
					if (ruleContent?.existCostPrice || ruleContent?.existCostPrice === 0) {
						let content = [<span className={ s["content-label"] }>是否设置成本价：</span>];
						content.push(
							<>{ruleContent?.existCostPrice === 0 ? '是' : '否'}<br /></>
						);
						contentList.push(content);
					}
					break;

				default:
					break;
			}
		});

		// 标签生成状态
		if (ruleContent?.labelGenerateStatus && ruleContent?.labelGenerateStatus !== LABEL_GENERATOR_STATUS.default) {
			let content = [<span className={ s["content-label"] }>标签状态：</span>];
			content.push(
				<>{LABEL_GENERATOR_STATUS.options.find(i => i.key === ruleContent?.labelGenerateStatus)?.value}<br /></>
			);
			contentList.push(content);
		}
		
		// 市场档口有无设置查询
		if (ruleContent?.hasMarketStall && ruleContent?.hasMarketStall !== BHD_STALL_STATUS_OPTIONS.default) {
			let content = [<span className={ s["content-label"] }>市场档口状态：</span>];
			content.push(
				<>{BHD_STALL_STATUS_OPTIONS.options.find(i => i.key === ruleContent?.hasMarketStall)?.value}<br /></>
			);
			contentList.push(content);
		}

		if (ruleContent?.includePending?.includes?.('includePending')) {
			contentList.push([<span className={ s["content-label"] }>包括挂起订单：</span>, <>是</>]);
		}
		if (ruleContent?.includePreShip?.includes?.('includePreShip')) {
			contentList.push([<span className={ s["content-label"] }>包括自动发货订单：</span>, <>是</>]);
		}
		if (ruleContent?.includeYdNoRecord?.includes?.('includeYdNoRecord')) {
			contentList.push([<span className={ s["content-label"] }>包括快递缺货登记：</span>, <>是</>]);
		}
		if (ruleContent?.includeArrivalOfGoodsAndStockpile) {
			contentList.push([<span className={ s["content-label"] }>包含已配货、已到货、已囤货商品：</span>, <>是</>]);
		}
		if (ruleContent?.firstSend) {
			contentList.push([<span className={ s["content-label"] }>本系统先发货订单：</span>, <>是</>]);
		}
		if (ruleContent?.waitSendOrFirstSend) {
			contentList.push([<span className={ s["content-label"] }>待发货+先发货订单：</span>, <>是</>]);
		}
	} catch (error) {
		console.error(error, '解析预设content报错');
	}
	return (
		<div className={ s["rule-tooltip-content"] } style={ { padding: '4px 8px' } } onClick={ e => e.stopPropagation() }>
			{contentList.length === 0 && (
				<Empty description="暂未设置查询条件" />
			)}
			{contentList.length !== 0 && (
				<List
					size="small"
					dataSource={ contentList.slice(0, 10) }
					renderItem={ item => {
						const [label = '', ...content] = item;
						return (
							<List.Item className="r-mb-4" style={ { padding: 4 } } key={ item.label }>
								<div className="r-flex-shrink">{label}</div>
								<div className="r-oneline">{content}</div>
							</List.Item>
						);
					} }
				/>
			)}
			{contentList.length > 10 && (
				<div className="r-w-full r-flex r-jc-c">
					<Button type="link" onClick={ cb }>查看完整条件</Button>
				</div>
			)}
		</div>
	);
};

// 售后订单卡片
const getRuleTooltipAftersaleIndex = (rule, conditionSet, cb, data) => {
	let contentList: any[] = [];
	const showMax = 10; // 显示数量
	const { type, isShowKg } = data || {};
	try {
		const ruleContent = JSON.parse(rule?.ruleContent || '{}');

		// console.log('%c [ ruleContent ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', ruleContent);
		
		conditionSet.forEach(conditionItem => {
			switch (conditionItem.condition) {
				// 时间类型
				case 'rangeTime':
					if (ruleContent?.timeType) {
						let content = [<span className={ s["content-label"] }>时间类型：</span>];
						let name = timeTypeOptions_aftersale?.find(i => i.value == ruleContent?.timeType)?.label;
						content.push(
							<>{name}<br /></>
						);
						contentList.push(content);
					}
					break;

					
				// "平台&店铺"
				case 'platformInfo':
					if (ruleContent['platformInfo']?.plat_sellerIds?.length) {
						let content = [];
						const { shopList } = userStore;
						ruleContent['platformInfo'].plat_sellerIds.forEach((item, index) => {
							const [plat = '', sellerId = ''] = item.split('_');
							let shopItem = shopList?.find(i => i.sellerId === sellerId);
							if (shopItem) {
								content.push(
									<>
										{getTradePlatformLabel(plat)}
										<span>{shopItem.sellerNick}{index !== ruleContent['platformInfo'].plat_sellerIds.length - 1 ? '、' : ''}</span>
									</>
								);
							}
						});
						if (content.length) {
							content.unshift([<span className={ s["content-label"] }>平台店铺：</span>]);
							content.push(<br />);
							contentList.push(content);
						}
					}
					break;
				
				// 售后类型
				case 'afterSaleTypeList':
					if (ruleContent?.afterSaleTypeList?.length) {
						let content = [<span className={ s["content-label"] }>售后类型：</span>];
						let optionMap = {};
						afterSaleTypeNames.forEach(i => optionMap[i.value] = i.name);
						content.push(
							<>{ruleContent?.afterSaleTypeList.map(i => optionMap[i]).join(',')}<br /></>
						);
						contentList.push(content);
						return;
					}
					break;
				
					// 平台退款状态
				case 'refundStatus':
					if (ruleContent?.refundStatusList?.length) {
						let content = [<span className={ s["content-label"] }>平台退款状态：</span>];

						const optionMap = Object.fromEntries(refundStatusListOptions_aftersale.map(i => [i.value, i.label]));
						
						content.push(
							<>{ruleContent?.refundStatusList.map(i => optionMap[i]).join(',')}<br /></>
						);
						contentList.push(content);
					}
					break;

					// 售后原因
				case 'refundReasonList':
					if (ruleContent?.refundReasonList?.length) {
						let content = [<span className={ s["content-label"] }>售后原因：</span>];

						const optionMap = Object.fromEntries(afterSaleReason.map(i => [i.value, i.name]));
						
						content.push(
							<>{ruleContent?.refundReasonList.map(i => optionMap[i]).join(',')}<br /></>
						);
						contentList.push(content);
					}
					break;

					// 审核状态
				case 'reviewStatus':
					if (ruleContent?.reviewStatus) {
						let content = [<span className={ s["content-label"] }>审核状态：</span>];
						let name = reviewStatusOptions_aftersale?.find(i => i.value == ruleContent?.reviewStatus)?.label;
						content.push(
							<>{name}<br /></>
						);
						contentList.push(content);
					}
					break;

					// 工单类型
				case 'refundSystemTypeList':
					if (ruleContent?.refundSystemTypeList?.length) {
						let content = [<span className={ s["content-label"] }>工单类型：</span>];

						const optionMap = Object.fromEntries(refundSystemTypeListOptions_aftersale.map(i => [i.value, i.label]));
						
						content.push(
							<>{ruleContent?.refundSystemTypeList.map(i => optionMap[i]).join(',')}<br /></>
						);
						contentList.push(content);
					}
					break;
					
					// 售后工单状态
				case 'refundSystemStatusList':
					if (ruleContent?.refundSystemStatusList?.length) {
						let content = [<span className={ s["content-label"] }>售后工单状态：</span>];

						const optionMap = Object.fromEntries(refundSystemStatusListOptions_aftersale.map(i => [i.value, i.label]));
						
						content.push(
							<>{ruleContent?.refundSystemStatusList.map(i => optionMap[i]).join(',')}<br /></>
						);
						contentList.push(content);
					}
					break;

					// 订单/售后单号
				case 'refundId':
					if (ruleContent?.refundOrPtTidList) {
						let content = [<span className={ s["content-label"] }>订单/售后单号：</span>];
						content.push(
							<>{ruleContent?.refundOrPtTidList}<br /></>
						);
						contentList.push(content);
					}
					break;

					// 发货/退货物流
				case 'invoiceNo':
					if (ruleContent?.logisticsSearchType && (ruleContent?.logisticsNoList || ruleContent?.logisticsCompanyName || ruleContent?.logisticsStatusEnum)) {
						let content = [<span className={ s["content-label"] }>发货/退货物流：</span>];
						let name = invoiceNoOptions_aftersale?.find(i => i.value == ruleContent?.logisticsSearchType)?.label;
						content.push(
							<>{name}<br /></>
						);
						contentList.push(content);
					}
					if (ruleContent?.logisticsNoList) {
						let content = [<span className={ s["content-label"] }>快递单号：</span>];
						content.push(
							<>{ruleContent?.logisticsNoList}<br /></>
						);
						contentList.push(content);
					}
					if (ruleContent?.logisticsCompanyName) {
						let content = [<span className={ s["content-label"] }>快递公司：</span>];
						content.push(
							<>{ruleContent?.logisticsCompanyName}<br /></>
						);
						contentList.push(content);
					}
					if (ruleContent?.logisticsStatusEnum?.length) {
						let content = [<span className={ s["content-label"] }>物流状态：</span>];
						const optionMap = Object.fromEntries(EXPRESS_STATUS_OPTIONS?.options.map(i => [i.value, i.label]));
						const logisticsStatusEnum = [];
						ruleContent?.logisticsStatusEnum?.forEach(i => {
							if (i === ExpressStatusEnum.无物流状态) {
								logisticsStatusEnum.push(ExpressStatusEnum.无快递单号, ExpressStatusEnum["无物流状态(有单号)"]);
							} else {
								logisticsStatusEnum.push(i);
							}
						});
						content.push(
							<>{logisticsStatusEnum.map(i => optionMap[i]).join(',')}<br /></>
						);

						contentList.push(content);
					}
					break;

					// 订单状态 改为多选
				case 'tradeStatus':
					if (ruleContent?.tradeStatusList?.length) {
						let content = [<span className={ s["content-label"] }>订单状态：</span>];
						const optionMap = Object.fromEntries(tradeStatusOptions_aftersale.map(i => [i.value, i.label]));
						
						content.push(
							<>{ruleContent?.tradeStatusList.map(i => optionMap[i]).join(',')}<br /></>
						);

						contentList.push(content);
					}
					break;

					// "留言备注" : 这里显示留言或备注，不兼容老数据
				case 'flagValue':
					if (ruleContent?.flagValue) {
						if (ruleContent?.flagValue === '-1') {
							if (ruleContent?.buyerMessageOrSellerMemo) {
								let content = [<span className={ s["content-label"] }>留言或备注：</span>];
								content.push(
									<>{ruleContent?.buyerMessageOrSellerMemo}<br /></>
								);
								contentList.push(content);
							}

							// 这里还是老的，不是留言或备注
							if (ruleContent?.sellerMemo) {
								let content = [<span className={ s["content-label"] }>备注：</span>];
								content.push(
									<>{ruleContent?.sellerMemo}<br /></>
								);
								contentList.push(content);
							}

							if (ruleContent?.sellerFlag?.length) {
								let content = [<span className={ s["content-label"] }>旗帜：</span>];
								let sellerFlagStr = ruleContent?.sellerFlag?.map(item => flagValueList.find(i => i.key === item)?.value)?.join(',');
								content.push(
									<>{sellerFlagStr ? `[${sellerFlagStr}]` : ''}<br /></>
								);
								contentList.push(content);
							}
						} else {
							let content = [<span className={ s["content-label"] }>留言备注：</span>];
							let temp = flagValueList.find(i => i.key === ruleContent?.flagValue)?.value;
							if (!temp) {
								const flagList = tradeStore.flagList || [];
								temp = flagList?.find(i => i.selValue === ruleContent?.flagValue)?.comment || '自定义';
							}

							content.push(
								<>{temp}<br /></>
							);
							contentList.push(content);
						}
					}
					break;

					// 线下备注
				case 'offlineMemo':
					if (ruleContent?.needFindLocalContent === '1') {
						let content = [<span className={ s["content-label"] }>有线下备注：</span>];
						content.push(
							<>{ruleContent?.localContent}<br /></>
						);
						contentList.push(content);
					} else if (ruleContent?.needFindLocalContent === '0') {
						let content = [<span className={ s["content-label"] }>无线下备注：</span>];
						contentList.push(content);
					}
					break;

					// "买家昵称"
				case 'buyerNick':
					if (ruleContent?.buyerNick) {
						let content = [<span className={ s["content-label"] }>买家昵称：</span>];
						content.push(
							<>{ruleContent?.buyerNick}<br /></>
						);
						contentList.push(content);
					}
					break;

					// 是否无主件
				case 'isNoTradeMess':
					if (ruleContent?.isNoTradeMess) {
						let content = [<span className={ s["content-label"] }>是否无主件：</span>];
						content.push(
							<>{isNoTradeMessOptions_aftersale.find(i => i.value === ruleContent?.isNoTradeMess)?.label}<br /></>
						);
						contentList.push(content);
					}
					break;

					// 即将超时
				case 'timeOutSearch':
					if (ruleContent?.timeOutSearch) {
						let content = [<span className={ s["content-label"] }>即将超时：</span>];
						content.push(
							<>{timeOutSearchOptions_aftersale.find(i => i.value === ruleContent?.timeOutSearch)?.label}<br /></>
						);
						contentList.push(content);
					}
					break;

					// 售后标签
				case 'refundTagList':
					if (ruleContent?.refundTagList?.length) {
						let content = [<span className={ s["content-label"] }>{refundTagTypeOptions_aftersale.find(i => i.value === ruleContent?.isQueryContainRefundTag)?.label || '售后标签(包含)'}：</span>];
						const optionMap = Object.fromEntries(refundTagListOptions_aftersale.map(i => [i.value, i.label]));
						
						content.push(
							<>{ruleContent?.refundTagList.map(i => optionMap[i]).join(',')}<br /></>
						);
						contentList.push(content);
					}
					break;

					// 售后异常
				case 'refundExceptionList':
					if (ruleContent?.refundExceptionList?.length) {
						let content = [<span className={ s["content-label"] }>售后异常：</span>];
						const optionMap = Object.fromEntries(refundExceptionListOptions_aftersale.map(i => [i.value, i.label]));
						
						content.push(
							<>{ruleContent?.refundExceptionList.map(i => optionMap[i]).join(',')}<br /></>
						);
						contentList.push(content);
					}
					break;

					// 货物状态 多选
				case 'goodsStatus':
					if (ruleContent?.newGoodsStatusList?.length) {
						let content = [<span className={ s["content-label"] }>货物状态：</span>];
						
						const optionMap = Object.fromEntries(
							goodsStatusOptions.flatMap(group => group.options.map(i => [i.value, i.label]))
						);
						content.push(
							<>{ruleContent?.newGoodsStatusList.map(i => optionMap[i]).join(',')}<br /></>
						);
						contentList.push(content);
					}
					break;


					// 收货状态
				case 'confirmItemStatus':
					if (ruleContent?.confirmItemStatusList?.length) {
						let content = [<span className={ s["content-label"] }>收货状态：</span>];
						const optionMap = Object.fromEntries(confirmItemStatusOptions_aftersale.map(i => [i.value, i.label]));
		
						content.push(
							<>{ruleContent?.confirmItemStatusList.map(i => optionMap[i]).join(',')}<br /></>
						);
						contentList.push(content);
					}
					break;

					// 入库状态
				case 'handleReturnStatus':
					if (ruleContent?.handleReturnStatusList?.length) {
						let content = [<span className={ s["content-label"] }>入库状态：</span>];
						const optionMap = Object.fromEntries(handleReturnStatusOptions_aftersale.map(i => [i.value, i.label]));
		
						content.push(
							<>{ruleContent?.handleReturnStatusList.map(i => optionMap[i]).join(',')}<br /></>
						);
						contentList.push(content);
					}
					break;

					// 申请售后金额
				case 'applyRefundFee':
					if (ruleContent?.applyRefundFeeType && (ruleContent?.minRefundFee || ruleContent?.maxRefundFee || ruleContent?.minRefundItemNum || ruleContent?.maxRefundItemNum)) {
						let content = [<span className={ s["content-label"] }>申请售后类型：</span>];
						content.push(
							<>{applyRefundFeeOptions_aftersale.find(i => i.value === ruleContent?.applyRefundFeeType)?.label}<br /></>
						);
						contentList.push(content);
					}
					if (ruleContent?.minRefundFee || ruleContent?.maxRefundFee) {
						let content = [<span className={ s["content-label"] }>{applyRefundFeeOptions_aftersale.find(i => i.value === ruleContent?.applyRefundFeeType)?.label || '申请售后金额'}范围：</span>];
						content.push(
							<>{ruleContent?.minRefundFee}-{ruleContent?.maxRefundFee}<br /></>
						);
						contentList.push(content);
					}
					if (ruleContent?.minRefundItemNum || ruleContent?.maxRefundItemNum) {
						let content = [<span className={ s["content-label"] }>{applyRefundFeeOptions_aftersale.find(i => i.value === ruleContent?.applyRefundFeeType)?.label || '申请售后数量'}范围：</span>];
						content.push(
							<>{ruleContent?.minRefundItemNum}-{ruleContent?.maxRefundItemNum}<br /></>
						);
						contentList.push(content);
					}
					break;


					// 注意：商品货品 "包含" 不包含 都在一起
				case 'sysItemInclude': 
					if (ruleContent?.goodsIncludeStatus == itemInfoEnumValue.商品包含 || ruleContent?.goodsIncludeStatus == itemInfoEnumValue.货品包含
						|| ruleContent?.goodsIncludeStatus == itemInfoEnumValue.商品不包含 || ruleContent?.goodsIncludeStatus == itemInfoEnumValue.货品不包含
					) {
						const showType = ruleContent?.goodsIncludeStatus == itemInfoEnumValue.商品包含 || ruleContent?.goodsIncludeStatus == itemInfoEnumValue.货品包含 ? '包含' : '不包含';
						const { goodsIncludeStatus } = ruleContent;
						let { shortNameIncludingList = [], skuIncludingList = [] } = ruleContent;
						let skuIncluding;
						let shortNameIncluding;
						const { itemSku } = ruleContent;
						if (itemSku?.length) {
							skuIncluding = itemSku[0].skuIncluding;
							shortNameIncluding = itemSku[0].shortNameIncluding;
						}
						if (skuIncluding && !skuIncludingList.length) skuIncludingList = [skuIncluding.trim()];
						if (shortNameIncluding && !shortNameIncludingList.length) shortNameIncludingList = [shortNameIncluding.trim()];
						const label = goodsIncludeStatusLabelMap[goodsIncludeStatus];
						if (shortNameIncludingList.length) {
							let content = [<span className={ s["content-label"] }>{label}({showType})：</span>];
							content.push(
								<>{shortNameIncludingList.join(',')}<br /></>
							);
							contentList.push(content);
						}
	
						if (skuIncludingList.length) {
							let content = [<span className={ s["content-label"] }>{goodsIncludeStatus == itemInfoEnumValue.商品包含 || goodsIncludeStatus == itemInfoEnumValue.商品不包含 ? '' : '货品'}规格({showType})：</span>];
							content.push(
								<>{skuIncludingList.join(',')}<br /></>
							);
							contentList.push(content);
						}
					}
					break;

					// 换货/补寄订单状态
				case 'exchangeRefundStatusList':
					if (ruleContent?.exchangeRefundStatusList?.length) {
						let content = [<span className={ s["content-label"] }>换货/补寄订单状态：</span>];
						const optionMap = Object.fromEntries(exchangeRefundStatusListOptions_aftersale.map(i => [i.value, i.label]));
		
						content.push(
							<>{ruleContent?.exchangeRefundStatusList.map(i => optionMap[i]).join(',')}<br /></>
						);
						contentList.push(content);
					}
					break;

					// 自发/代发售后单
				case 'scmRefundType':
					if (ruleContent?.scmRefundType) {
						let content = [<span className={ s["content-label"] }>自发/代发售后单：</span>];
						content.push(
							<>{scmRefundTypeOptions_aftersale.find(i => i.value === ruleContent?.scmRefundType)?.label}<br /></>
						);
						contentList.push(content);
					}
					break;

					// 是否提交供应商
				case 'distributorIsPush':
					if (ruleContent?.distributorIsPush !== undefined) {
						let content = [<span className={ s["content-label"] }>是否提交供应商：</span>];
						content.push(
							<>{distributorIsPushOptions_aftersale.find(i => i.value === ruleContent?.distributorIsPush)?.label}<br /></>
						);
						contentList.push(content);
					}
					break;
					// 供应商确认状态
				case 'supplierIsConfirm':
					if (ruleContent?.supplierIsConfirm !== undefined) {
						let content = [<span className={ s["content-label"] }>供应商确认状态：</span>];
						content.push(
							<>{supplierIsConfirmOptions_aftersale.find(i => i.value === ruleContent?.supplierIsConfirm)?.label}<br /></>
						);
						contentList.push(content);
					}
					break;

				// 选择供应商
				case 'scmSupplierUserIdList':
					if (ruleContent?.scmSupplierUserIdList?.length) {
						let _content = [];
						const { supplierList = [] } = distributionStore;
						console.log('%c [ supplierList ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', toJS(supplierList));
						let arr = [];
						ruleContent?.scmSupplierUserIdList.forEach(id => {
							let item = supplierList?.find(item => item.supplierUserId === id);
							if (item) {
								arr.push(item.supplierName);
							}
						});
						_content.push([<span className={ s["content-label"] }>供应商：</span>]);
						_content.push(arr.join('、'));
						_content.push(<br />);
						contentList.push(_content);
					}
					break;

				// 选择分销商 (分销商的售后单)
				case 'scmDistributorUserId':
					if (ruleContent?.scmDistributorUserId?.length) {
						let _content = [];
						const { distributorList = [] } = distributionStore;
						let arr = [];
						ruleContent?.scmDistributorUserId.forEach(id => {
							let item = distributorList?.find(item => item.saleUserId === id);
							if (item) {
								arr.push(item.saleName);
							}
						});
						_content.push([<span className={ s["content-label"] }>分销商：</span>]);
						_content.push(arr.join('、'));
						_content.push(<br />);
						contentList.push(_content);
					}
					break;
				

					// "供应商包含" : 供应商 市场 档口
				case 'supplierInclude':
					if (isArray(ruleContent?.supplierIncludingList) || isArray(ruleContent?.marketIncludingList) || isArray(ruleContent?.stallIncludingList)) {
						if (ruleContent?.supplierIncludingList?.length || ruleContent?.marketIncludingList?.length || ruleContent?.stallIncludingList?.length) {
							let content = [<span className={ s["content-label"] }>供应商包含：</span>];
	
							let arr = [];
							if (ruleContent?.supplierIncludingList?.length) {
								arr.push(`供应商：${ruleContent?.supplierIncludingList.join(',')}`);
							}
							if (ruleContent?.marketIncludingList?.length) {
								arr.push(`市场：${ruleContent?.marketIncludingList.join(',')}`);
							}
							if (ruleContent?.stallIncludingList?.length) {
								arr.push(`档口：${ruleContent?.stallIncludingList.join(',')}`);
							}
							content.push(<>{arr.join('、')}<br /></>);
							contentList.push(content);
						}
					} else if (ruleContent?.supplierIncludingList || ruleContent?.marketIncludingList || ruleContent?.stallIncludingList) {
						let content = [<span className={ s["content-label"] }>供应商包含：</span>];
						// 改为字符串
						let arr = [];
						if (ruleContent?.supplierIncludingList) {
							arr.push(`供应商：${ruleContent?.supplierIncludingList}`);
						}
						if (ruleContent?.marketIncludingList) {
							arr.push(`市场：${ruleContent?.marketIncludingList}`);
						}
						if (ruleContent?.stallIncludingList) {
							arr.push(`档口：${ruleContent?.stallIncludingList}`);
						}
						content.push(<>{arr.join('、')}<br /></>);
						contentList.push(content);
					}
					
					break;
				default:
					break;
			}
		});
	} catch (error) {
		console.error(error, '解析预设content报错');
	}
	return (
		<div className={ s["rule-tooltip-content"] } style={ { padding: '4px 8px' } } onClick={ e => e.stopPropagation() }>
			{contentList.length === 0 && (
				<Empty description="暂未设置查询条件" />
			)}
			{contentList.length !== 0 && (
				<List
					size="small"
					dataSource={ contentList.slice(0, showMax) }
					renderItem={ item => {
						const [label = '', ...content] = item;
						return (
							<List.Item className="r-mb-4" style={ { padding: 4 } } key={ item.label }>
								<div className="r-flex-shrink">{label}</div>
								<div className="r-oneline">{content}</div>
							</List.Item>
						);
					} }
				/>
			)}
			{contentList.length > showMax && (
				<div className="r-w-full r-flex r-jc-c">
					<Button type="link" onClick={ cb }>查看完整条件</Button>
				</div>
			)}
		</div>
	);
};

// 各页面预设卡片渲染
export const getRuleTooltip = (rule: TradeAdvancedSearchGetListResponse["data"][number], conditionSet: TradeSearchConditionConfig[], cb: any, data: any) => {
	let content: any = '';
	const { type } = data || {};
	switch (type) {
		case BizTypeEnum.批打:
			content = getRuleTooltipTradeIndex(rule, conditionSet, cb, data);
			break;
		case BizTypeEnum.备货单查询:
		case BizTypeEnum.备货单标签查询:
			content = getRuleTooltipBhdIndex(rule, conditionSet, cb, data);
			break;
		case BizTypeEnum.售后单查询:
		case BizTypeEnum.分销商的售后单查询:
			content = getRuleTooltipAftersaleIndex(rule, conditionSet, cb, data);
			break;
		default:
			break;
	}

	return content;
};