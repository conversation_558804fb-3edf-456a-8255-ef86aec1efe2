import { uniq } from "lodash";
import { platform } from "@/types/schemas/common";
import { IShopInfo } from "@/types/schemas/user";
import { PLAT_ALL_SCM, PLAT_DW, PLAT_HAND, PLAT_HAND_SHOP_NAME, PLAT_MAP, PLAT_OTHER, PLAT_SCM, PLAT_SCMHAND, PLAT_SPH, PLAT_XHS, PLAT_KTT, PLAT_FXG } from "@/constants";
import userStore from "@/stores/user";
import { IPackage } from "@/pages/Trade/interface";

/**
 * 店铺状态： 订购过期｜暂停｜正常
 */
type ShopStatus = 'orderExpired' | 'stop' | 'normal';
const EmptyValue = '——';

/**
 * 获取所有平台
 */
export async function getAllPlats(isHasHandPlat?: boolean, isHasEbillPlat: boolean = false) {
	let list = await userStore.getShopList({ isHasEbillPlat });
	let plats = [];
	list.forEach((item) => {
		if (item.platform !== PLAT_OTHER) {
			plats.push(item.platform);
		}
	});
	if (isHasHandPlat) plats.push(PLAT_OTHER);
	return uniq(plats);
}

// 列表中获取店铺名 手工单返回无店铺
export const getShopName = ({ plat, sellerNick }) => {
	let newPlat = plat?.toLocaleLowerCase() || '';
	if (newPlat === PLAT_HAND) {
		return PLAT_HAND_SHOP_NAME;
	}
	return sellerNick || EmptyValue;
};

// 列表中平台名称展示 plat 为 hand 或 other
export const getPlatName = (plat: platform) => {
	if (plat === PLAT_HAND) {
		plat = PLAT_OTHER;
	}
	return PLAT_MAP[plat] || EmptyValue;
};

// 按照手工平台逻辑处理该平台
export const dealPlatAsHandPlat = (plat: platform, filterArr: platform[] = [], pack: IPackage = null) => {
	console.log('%c [ 按照手工平台逻辑处理该平台 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', plat);
	if (plat == PLAT_FXG && pack?.trades?.[0]?.source == "HAND" && pack?.trades?.[0]?.tradeEncodeType != 2) {
		return true;
	}
	let handsPlat: platform[] = [PLAT_HAND, PLAT_DW, PLAT_OTHER, PLAT_KTT];
	if (filterArr?.length) {
		handsPlat = handsPlat.filter(plat => !filterArr.includes(plat));
	}
	return handsPlat.includes(plat);
};

// 手工单 包括其他平台和手工单 source 为 HAND
// 打印的时候，需要判断分销手工单也算是手工单
export const isSourceHand = (pack: IPackage | { source: string, platform: platform } = {}, isContainScmHand = false) => {
	const { source, platform } = pack;
	let isHand = source?.toLowerCase() === PLAT_HAND || (platform === PLAT_HAND && source != PLAT_SCMHAND);
	if (isContainScmHand) {
		return isHand || [PLAT_SCMHAND].includes(pack.source);
	}
	return isHand;
};

export const isSourceScm = (pack: IPackage | { source: string } = {}) => {
	return PLAT_ALL_SCM.includes(pack.source);
};

// 售后模块，是否来自分销商的售后单
export const isAfterSaleSourceScm = (pack: any = {}) => {
	return PLAT_ALL_SCM.includes(pack.afterSaleSource);
};

export const transferOtherToHand = (plat: platform, pack: IPackage = null) => {
	if (plat == PLAT_FXG && pack?.trades[0]?.source == "HAND" && pack?.trades[0]?.tradeEncodeType != 2) {
		return PLAT_HAND;
	}
	if (plat === PLAT_OTHER) return PLAT_HAND;
	return plat;
};

/**
 * 根据平台 获取所有店铺
 * plats: 平台ID 数组，默认获取所有平台店铺
 * shopStatusArr： 店铺状态数组， 默认获取所有状态店铺
 */
export async function getShopsByPlat({
	plats = [],
	shopStatusArr = [],
	hasHand = false,
	isHasEbillPlat = false,
}: {
	plats?: platform[],
	shopStatusArr?: ShopStatus[],
	hasHand?: boolean,
	isHasEbillPlat?: boolean
} = {}) {
	let list = await userStore.getShopList({ isHasEbillPlat });

	const shopList: IShopInfo[] = [];
	const otherShopList: IShopInfo[] = [];
	list.forEach(item => {
		if (!plats.length) {
			item.platform !== PLAT_OTHER ? shopList.push(item) : hasHand ? otherShopList.push(item) : "";
		} else if (plats.includes(item.platform)) {
			item.platform !== PLAT_OTHER ? shopList.push(item) : otherShopList.push(item);
		}
	});
	shopList.push(...otherShopList);
	if (hasHand && (!plats.length || plats.includes(PLAT_OTHER))) {
		// console.log(99,);
		shopList.push({
			platform: PLAT_HAND,
			sellerNick: PLAT_HAND_SHOP_NAME,
			sellerId: userStore?.userInfo?.userId,
			status: 1,
			sellerAbbreviation: PLAT_HAND_SHOP_NAME
		});
	}
	let res: IShopInfo[];
	if (!shopStatusArr.length) {
		res = shopList;
	} else {
		res = [];
		shopList.forEach(item => {
			let status: ShopStatus;
			if (item.status === 0) {
				status = 'stop';
			} else if (item.status === 1) {
				status = 'normal';
			} else if (item.status === 2) {
				status = 'orderExpired';
			}
			if (shopStatusArr.includes(status)) {
				res.push(item);
			}
		});
	}
	return res;
}

export async function getMultiShops({ plats, plat_sellerIds, hasHand = true, isHasEbillPlat = false }: { plats?: platform[], plat_sellerIds?: string[], hasHand?: boolean, isHasEbillPlat?: boolean }) {
	let multiShops: {
		platform: string,
		sellerId: string,
	}[] = [];
	if (plat_sellerIds?.length) {
		multiShops = plat_sellerIds.map(item => {
			const arr = item.split('_');
			return {
				platform: arr[0],
				sellerId: arr[1]
			};
		});
	} else if (plats?.length) {
		const shopList = await getShopsByPlat({
			plats,
			isHasEbillPlat,
			hasHand
		});
		multiShops = shopList.map(item => ({
			platform: item.platform,
			sellerId: `${item.sellerId}`
		}));
	} else {
		// 后端接口改造 当没有选择店铺时将传所有店铺
		const shopList = await getShopsByPlat({ hasHand, isHasEbillPlat });
		multiShops = shopList.map(item => ({
			platform: item.platform,
			sellerId: `${item.sellerId}`
		}));
	}
	return multiShops;
}


export const getAllShopMap = async({ hasHand = false, isHasEbillPlat = false }) => {
	const data = await getShopsByPlat({ hasHand, isHasEbillPlat });
	const map = {};
	data.forEach(item => {
		map[`${item.platform}_${item.sellerId}`] = item;
	});
	return map;
};

export const getPlatAndShops = async(shopInfo, hasHand = false) => {
	let shopId = [];
	let platform = [];
	if (!shopInfo?.plat_sellerIds?.length && !shopInfo?.plats?.length) {
		const shopList = await getShopsByPlat({ hasHand });
		shopList.forEach(item => {
			shopId.push(item.sellerId);
			platform.push(item.platform);
		});
	} else if (!shopInfo?.plat_sellerIds?.length && shopInfo?.plats?.length) {
		const shopList = await getShopsByPlat({
			plats: shopInfo?.plats || [],
			hasHand
		});
		shopList.forEach(item => {
			shopId.push(item.sellerId);
			platform.push(item.platform);
		});
	} else {
		shopInfo?.plat_sellerIds?.forEach((item: string) => {
			if (item?.indexOf('_') > -1) {
				const _arr = item.split('_');
				platform.push(_arr[0]);
				shopId.push(_arr[1]);
			}
		});
	}

	return {
		shopId,
		platform: Array.from(new Set(platform))
	};

};

/**
 * 检查平台是否应该被隐藏
 * @param platform 平台标识
 * @param hidePlatforms 需要隐藏的平台列表
 */
export const shouldHidePlatform = (platform: platform, hidePlatforms?: platform[]): boolean => {
	// 通用平台隐藏逻辑
	if (hidePlatforms?.length && hidePlatforms.includes(platform)) return true;
	return false;
};

/**
 * 检查店铺是否应该被隐藏
 * @param shop 店铺信息
 * @param hidePlatforms 需要隐藏的平台列表
 */
export const shouldHideShop = (shop: IShopInfo, hidePlatforms?: platform[]): boolean => {
	// 通用店铺隐藏逻辑
	if (hidePlatforms?.length && hidePlatforms.includes(shop.platform)) return true;
	return false;
};

/**
 * 获取多店铺信息（支持过滤和隐藏选项）
 * 基于getMultiShops方法，新增filterOptions和hidePlatforms参数支持平台过滤和隐藏
 * @param plats 平台ID数组
 * @param plat_sellerIds 店铺ID数组（格式：平台_卖家ID）
 * @param hasHand 是否包含手工单平台
 * @param isHasEbillPlat 是否包含电子面单平台
 * @param filterOptions 平台过滤选项，只获取指定平台的数据
 * @param hidePlatforms 需要隐藏的平台列表
 */
export async function getMultiShopsWithFilter({
	plats,
	plat_sellerIds,
	hasHand = true,
	isHasEbillPlat = false,
	filterOptions,
	hidePlatforms
}: {
	plats?: platform[],
	plat_sellerIds?: string[],
	hasHand?: boolean,
	isHasEbillPlat?: boolean,
	filterOptions?: platform[],
	hidePlatforms?: platform[]
}) {
	let multiShops: {
		platform: string,
		sellerId: string,
	}[] = [];

	if (plat_sellerIds?.length) {
		// 当有具体的店铺ID时，直接处理并应用过滤和隐藏逻辑
		multiShops = plat_sellerIds.map(item => {
			const arr = item.split('_');
			return {
				platform: arr[0],
				sellerId: arr[1]
			};
		}).filter(item => {
			const platform = item.platform as platform;
			// 应用过滤逻辑：如果有过滤选项，只保留过滤选项中的平台
			if (filterOptions?.length && !filterOptions.includes(platform)) {
				return false;
			}
			// 应用隐藏逻辑：排除需要隐藏的平台
			if (shouldHidePlatform(platform, hidePlatforms)) {
				return false;
			}
			return true;
		});
	} else if (plats?.length) {
		// 当有平台选择时，先应用过滤和隐藏逻辑
		let selectedPlats = [...plats];

		// 应用过滤逻辑：如果有过滤选项，需要与选中的平台取交集
		if (filterOptions?.length) {
			selectedPlats = selectedPlats.filter(plat => filterOptions.includes(plat));
		}

		// 应用隐藏逻辑：排除需要隐藏的平台
		if (hidePlatforms?.length) {
			selectedPlats = selectedPlats.filter(plat => !shouldHidePlatform(plat, hidePlatforms));
		}

		const shopList = await getShopsByPlat({
			plats: selectedPlats,
			isHasEbillPlat,
			hasHand
		});

		// 对店铺列表应用隐藏逻辑
		const filteredShopList = shopList.filter(shop => !shouldHideShop(shop, hidePlatforms));

		multiShops = filteredShopList.map(item => ({
			platform: item.platform,
			sellerId: `${item.sellerId}`
		}));
	} else {
		// 没有选择店铺时，获取所有店铺并应用过滤和隐藏逻辑
		let shopList;
		if (filterOptions?.length) {
			// 如果有过滤选项，先按过滤选项获取店铺
			const filteredPlats = hidePlatforms?.length
				? filterOptions.filter(plat => !shouldHidePlatform(plat, hidePlatforms))
				: filterOptions;

			shopList = await getShopsByPlat({
				plats: filteredPlats,
				hasHand,
				isHasEbillPlat
			});
		} else {
			// 没有过滤选项时，获取所有店铺
			shopList = await getShopsByPlat({ hasHand, isHasEbillPlat });
		}

		// 对店铺列表应用隐藏逻辑
		const filteredShopList = shopList.filter(shop => !shouldHideShop(shop, hidePlatforms));

		multiShops = filteredShopList.map(item => ({
			platform: item.platform,
			sellerId: `${item.sellerId}`
		}));
	}

	return multiShops;
}

/**
 * 获取平台和店铺信息（支持过滤和隐藏选项）
 * 基于getPlatAndShops方法，新增filterOptions和hidePlatforms参数支持平台过滤和隐藏
 * @param shopInfo 店铺信息对象，包含plats和plat_sellerIds
 * @param hasHand 是否包含手工单平台
 * @param filterOptions 平台过滤选项，只获取指定平台的数据
 * @param hidePlatforms 需要隐藏的平台列表
 */
export const getPlatAndShopsWithFilter = async(
	shopInfo: any,
	hasHand = false,
	filterOptions?: platform[],
	hidePlatforms?: platform[]
) => {
	let shopId = [];
	let platform = [];

	if (!shopInfo?.plat_sellerIds?.length && !shopInfo?.plats?.length) {
		// 当没有选择任何平台或店铺时，应用过滤和隐藏选项
		let shopList;
		if (filterOptions?.length) {
			// 如果有过滤选项，先按过滤选项获取店铺
			const filteredPlats = hidePlatforms?.length
				? filterOptions.filter(plat => !shouldHidePlatform(plat, hidePlatforms))
				: filterOptions;

			shopList = await getShopsByPlat({
				plats: filteredPlats,
				hasHand
			});
		} else {
			// 如果没有过滤选项，获取所有平台店铺
			shopList = await getShopsByPlat({ hasHand });
		}

		// 对店铺列表应用隐藏逻辑
		const filteredShopList = shopList.filter(shop => !shouldHideShop(shop, hidePlatforms));

		filteredShopList.forEach(item => {
			shopId.push(item.sellerId);
			platform.push(item.platform);
		});
	} else if (!shopInfo?.plat_sellerIds?.length && shopInfo?.plats?.length) {
		// 只选择了平台，没有选择具体店铺
		let selectedPlats = shopInfo?.plats || [];

		// 如果有过滤选项，需要与选中的平台取交集
		if (filterOptions?.length) {
			selectedPlats = selectedPlats.filter(plat => filterOptions.includes(plat));
		}

		// 应用隐藏逻辑：排除需要隐藏的平台
		if (hidePlatforms?.length) {
			selectedPlats = selectedPlats.filter(plat => !shouldHidePlatform(plat, hidePlatforms));
		}

		const shopList = await getShopsByPlat({
			plats: selectedPlats,
			hasHand
		});

		// 对店铺列表应用隐藏逻辑
		const filteredShopList = shopList.filter(shop => !shouldHideShop(shop, hidePlatforms));

		filteredShopList.forEach(item => {
			shopId.push(item.sellerId);
			platform.push(item.platform);
		});
	} else {
		// 选择了具体的店铺
		shopInfo?.plat_sellerIds?.forEach((item: string) => {
			if (item?.indexOf('_') > -1) {
				const _arr = item.split('_');
				const platId = _arr[0];

				// 如果有过滤选项，只处理过滤选项中的平台
				if (filterOptions?.length && !filterOptions.includes(platId as platform)) {
					return;
				}

				// 应用隐藏逻辑：排除需要隐藏的平台
				if (shouldHidePlatform(platId as platform, hidePlatforms)) {
					return;
				}

				platform.push(platId);
				shopId.push(_arr[1]);
			}
		});
	}

	return {
		shopId,
		platform: Array.from(new Set(platform))
	};
};
