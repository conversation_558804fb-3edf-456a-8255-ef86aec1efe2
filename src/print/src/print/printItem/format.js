import * as printItem from './index';
import { isSmallTag } from '../config';
import { getTplModeServiceValue, tryGet } from './utils';
// import { getTplModeServiceValue } from './utils';
import { attachExtraDataToPrintData } from './printData';
import { encryptAddress, encryptMobile, encryptName, isEncrypted } from '../utils/crypto';
import { platformConfig, platformByName } from '../printFlow/platformConfig';
import { isDYEncryptTpl, ThhTplNeedAuth } from '../utils/tpl';

function _encryptPhone(mobile) {
	mobile = mobile || '';
	if (mobile.length >= 8) {
		mobile = mobile.slice(0, mobile.length - 8) + '****' + mobile.slice(-4);
	} else {
		mobile = mobile.replace(/\S{4}$/, '****');
	}
	return mobile;
}

/**
 *  遍历所有的 Input 来生成打印数据，根据 「tagBelong」来确认要生成的打印数据
 *  @param {object} params
 *  @param {boolean} params.isSmallTag 是否是拿货小标签
 *  @param {object} params.obj 数据
 */
function setPrintDataFromInput(params) {
	const { obj: data, allInputsList } = params;
	const PrintDataObj = {};

	//tagBelong 1:属于走配置的数据框，直接由代码配置其对应的字段
	allInputsList.forEach(item => {
		if (item.tagBelong === '1') { //
			const thisKey = item.Key;
			PrintDataObj[thisKey] = data[thisKey];// 业务字段赋值个打印字段
		}
	});
	return PrintDataObj;
}

function formatPrintData(arg) {
	/**
	 * 是否是单打
	 */
	const isSinglePrint = arg.printOp === 0;
	//获取当前行明细的tr
	let mydate = new Date(),
		logoArr,
		obj = arg.obj,
		kddFjr = arg.kddFjr || {},
		fhdFjr,
		m = new comp.print.desc.PrintData(),
		_imgName, _iconIndex, _arrIcon,
		// encryItems = arg.encryItems || [],
		temp = arg.temp,
		kddType = (arg.temp || {}).KddType,
		_originUrl;

	let shopList = window?.erpData?.shopList || [];
	let currentSellerId = obj.sellerId;
	let currentSellerInfo = shopList.filter(item => item.sellerId == currentSellerId)[0] || {};
	const { allInputsList } = arg;
	const clientType = comp.Print.getClientType(arg.modeInfo, arg.printType);  // 控件类型
	const isFhd = arg.temp.Modeid === 'fhd';
	const isDecryptConsole = obj.fhdDecrypt; // 是否为需解密的平台
	// 加密平台打印发货单
	if (isFhd && platformConfig.crypto.isEncryptPackage(obj) && !isDecryptConsole) {
		// 手动解密打印，需要模拟加密格式，不允许明文打印发货单
		if (!isEncrypted(obj.s_mobile)) {
			obj.s_name = encryptName(obj.s_name || '');

			obj.s_mobile = encryptMobile(obj.s_mobile || '');
			obj.s_phone = encryptMobile(obj.s_phone || '');

			obj.s_address = encryptAddress(obj.s_address || '');
		}
	}

	const PrintDataObj = setPrintDataFromInput({ obj, allInputsList });

	m = Object.assign(m, PrintDataObj);

	// 抖音字段
	m.pslx = arg.pslx;
	m.is_cod = arg.is_cod;
	m.is_insure = arg.is_insure;
	// 抖音字段
	m.shop_title = obj.shopTitle || obj.seller_nick;
	m.shop_abbreviation = currentSellerInfo?.sellerAbbreviation;
	m.print_count = obj.print_count;    //打印计数：序号/当前批次运单号总数 !!!注意


	// 简称 或者 标题
	m.goods_title = tryGet(() => {
		// 仅取第一个
		const data = obj.tableDataArray[0];
		return data.tb_jc || data.tb_tit;

		// return obj.tableDataArray.map((k) => k.tb_jc || k.tb_tit).join(',');

		// 底单打印时，取 obj.goods_title
	}, obj.goods_title || '');

	//非打印信息的存储z
	m.userId = obj.userId;

	if (!kddFjr) { // 这里要处理下从扫描发货单来的数据

		kddFjr = printItem.getDefaultFjrInfo(arg.kddFjrMap, obj.userId2 || obj.userId); //发件人这里传从库 id
	}
	const mallType = window.sessionStorage.getItem('mallType');
	/*-----发件人字段-----*/
	if (arg.isUseOrderFjr) {
		m.f_name = obj.f_name || '';
		m.f_tel = obj.f_tel || '';
		m.f_addr = obj.f_addr || '';
		m.f_qm = obj.f_qm || '';
		m.f_zip = obj.f_zip || '';
	} else {
		//发件人名字
		m.f_name = kddFjr.name || '';
		// 发件人电话
		m.f_tel = kddFjr.phone || '';
		// 发件地址
		m.f_addr = `${kddFjr.province || ''}${kddFjr.city || ''}${kddFjr.district || ''}${kddFjr.address || ''}` || '';
		// 发件人签名
		m.f_qm = kddFjr.sign || '';
		// 发件邮编
		m.f_zip = kddFjr.postCode || '';
	}
	//发件日期  //  属性：(单选)当天 后一天，(多选)是否显示时分秒
	m.f_date = mydate.format('yyyy-MM-dd');
	// 发件旺旺  //  属性：(单选)[当前登陆为子旺旺时]只显示主旺旺 显示子旺旺 只显示:后边的部分
	m.f_ww = obj.seller_nick;
	// 卖家备注
	m.f_memo = obj.sellerMome || '';

	// 线下备注图片
	m.trade_remarks_image = obj.sysMemoPic || '';

	// TODO 买家昵称图片
	m.dy_nick = obj.base64Url;

	// 发货信息
	m.f_info = obj.print_content || '';
	if (temp.KddType == 14) {
		// 视频号换行需要做一下转义，不能直接/n
		m.f_info = m.f_info.replace(/\n/g, '<br/>')
	}
	// 数量
	m.count = obj.count || 0;
	//满就送
	m.mjs = obj.mjs || '';
	// 货款总计(买家付的)(单项产品实付+运费)(实付金额中有可能用积金宝等积份，所以货款总计有时会比实付总额高)
	m.hkzj = parseFloat(obj.hkzj || 0).toFixed(2);
	// 发票抬头
	m.invoice = obj.invoice || '';
	// 打印序号
	obj.printNum ? m.printid = Number(obj.printNum) : '';
	//打印计数
	m.print_count = obj.print_count || '';
	// 打印序号条形码
	obj.printNum ? m.txm_printid = Number(obj.printNum) : '';
	// 包裹总数 等于申请几个面单号 或者 1
	m.baoguoCount = obj.baoguoCount || 1;
	m.bgjs = obj.bgjs || '1/1';
	// 拼接后的手机串码
	m.sjcm = obj.identCodes;


	/*-----收件人字段-----*/
	// 收件人
	m.s_name = (obj.s_name || '').replace(/\n/g, '');

	if (/^(26|62|63)$/.test((arg.temp || {}).styleId) && !isSinglePrint) {   //SF丰密加密电话号码
		m.s_phone = _encryptPhone(obj.s_mobile);
		m.s_tel = _encryptPhone(obj.s_phone);
		m.f_tel = _encryptPhone(kddFjr.phone || obj.f_tel || '');

	} else {
		m.s_phone = obj.s_mobile || '';
		m.s_tel = obj.s_phone || '';
	}

	if (((arg.temp || {}).KddType == 1 || (arg.temp || {}).KddType == 2) && mallType == 'BBW') {  //贝贝网的网点，五联单打印手机号替换
		m.s_phone = obj.s_mobile_bbw || '';
		m.s_tel = obj.s_phone || '';
	}
	m.s_phone_encry = _encryptPhone(obj.s_mobile);
	m.s_tel_encry = _encryptPhone(obj.s_phone);

	// 收件省
	m.s_p = obj.s_province || '';
	// 收件市 // 一级市
	m.s_city = obj.s_city || '';
	// 收件区 // 县或区
	m.s_q = obj.s_county || '';
	// 收件地址 // 用户输入的部分，与省市区重复的部分在CS阶段过滤掉
	m.s_addr = obj.s_address || '';

	// pdd 平台优先取 s_address_origin
	const useOriginAddress = ['pdd'].includes(platformConfig.platform);

	let addressDetail = useOriginAddress ? (obj.s_address_origin || obj.s_address) : obj.s_address;

	addressDetail = getVirtualMobileAddress(addressDetail, obj.s_mobile, arg.temp, obj.isVirtualTel);     // 兼容网点虚拟号地址后拼接提示
	// 收件街道
	m.s_addrall = [obj.s_province, obj.s_city, obj.s_county, obj.s_town, addressDetail].filter(Boolean).join(' ');

	// 买家旺旺
	m.s_ww = obj.buyerNick || '';

	// 订单编号
	if (obj.tids) {
		m.s_tid = Array.isArray(obj.tids) ? obj.tids.join(',') : obj.tids;
		m.f_tid = obj.tids[0] || '';
	}
	m.pt_order_id = Array.isArray(obj.ptTids) ? obj.ptTids.join(',') : obj.ptTids;
	m.fh_first_order_id = m.pt_order_id?.split('|')[0];
	// m.noPrintInfo = { // 非打印信息存储
	//     dtbp: kddFjr.province || '',
	//     dtbc: kddFjr.city || '',
	//     dtbq: kddFjr.district || ''
	// }
	// 收件邮编
	m.s_zip = obj.s_zip || '';
	//下单时间
	m.xdate = obj.orderCreateTime || '';
	//付款时间
	m.fdate = obj.payTime || '';
	//打印日期
	m.pdate = new Date().format('yyyy-MM-dd');
	//打印时间
	m.ptime = new Date().format('yyyy-MM-dd hh:mm:ss');
	// 买家留言
	m.s_message = obj.buyerMome || '';
	// 总实付-订单实付-订单运费
	// let refundAmount = (obj.totalPayment || 0) - (obj.payAmount || 0) - (obj.hj_post_fee || 0)
	// 实付金额 // 属性：(单选)[是否显示"元"字] 显示/不显示
	m.ddje = parseFloat(obj.payAmount || 0).toFixed(2) + (obj.totalRefundPayment > 0 ? `(不含退款金额：${obj.totalRefundPayment})` : '');
	// 实收金额
	m.ssje = parseFloat(obj.receivedPayment || 0).toFixed(2);
	// 代收金额 // 属性：(单选)[是否显示"元"字] 显示/不显示
	m.dshk_black = m.dsje = obj.codPrice ? parseFloat(obj.codPrice).toFixed(2) : '0.00';
	// LC编号
	m.lc = obj.lc;
	// 包裹编号  (申请多面单号的序号)
	m.baoguoID = obj.baoguoID || 1;
	//目的地  市 区 组件有赋值
	//m.mdd = obj.jibaodi || '';
	// 个性集包码
	m.gx_jbm = obj.jibaoma || '';
	//集包目的地
	m.gx_mdd = obj.jibaodi || '';
	//大头笔
	m.mdd_dzmd = obj.datoubi || '';
	// routeCode
	m.routeCode = obj.routeCode || '';

	//快递单号 先赋值一份替代之前的fhdexnumber
	m.exnumber = obj.exnumber || '';
	if (obj.sidNos && obj.sidNos[0] && /^\w+$/.test(obj.sidNos[0])) {
		m.fhdexnumber = obj.sidNos[0];
	} else {
		m.fhdexnumber = obj.fhdexnumber || obj.exnumber;
	}
	// 集包条形码
	m.txm_jbm = obj.jibaoma || '';
	// 订单条形码(一个框如果出现这个选项，则不会显示其它的选项，本身是数据源) 框的前文字处标记条形码的规格：A\B\C\EAN128
	m.txm_tid = obj.s_tid || '';
	// 运单号条形码(一个框如果出现这个选项，则不会显示其它的选项，本身是数据源) 框的前文字处标记条形码的规格：A\B\C\EAN128
	m.txm_number = obj.txm_number || obj.exnumber || '';
	//运单号二维码
	m.ewm_number = obj.exnumber || '';
	//大头笔-运单号拼接  -----打印圆通到付
	m.ewm_bigben_number = (m.mdd_dzmd && m.txm_number) ? (m.mdd_dzmd + '-' + m.txm_number) : '';
	m.sxcp = obj.sxcp || '';
	m.fkfs = obj.pay_type || '';

	// 是否是分销
	obj.isfenxiao ? (m.isFx = obj.isfenxiao) : '';
	// 是否是货到付款
	obj.isCOD ? (m.isCod = obj.isCOD) : '';
	// 京东一联单代收货款标志
	m.cod = m.isCod ? 'COD' : '';
	// 合计_数量
	m.hj_sl = obj.count || 0;
	//合计邮费
	m.hj_yf = parseFloat(obj.hj_post_fee || 0).toFixed(2);

	// 运费：取当前打印包裹对应的订单运费总和
	m.s_fare = parseFloat(obj.hj_post_fee || 0).toFixed(2);

	//合计优惠
	m.hj_yh = parseFloat(obj.hj_youhui_fee || 0).toFixed(2);
	//合计实付
	m.hj_sf = m.ddje;
	// 保价金额
	m.bjje = obj.bjje ? obj.bjje : m.ddje; //手工订单的保价金额是导入的保价金额，淘宝订单保价金额是订单金额
	// 保价费用
	m.bjfy = '0.00'; //TODO 暂时说 这两个值未能获取到，后期看能否获取到
	// 收件单位(没有此项时用收件人姓名数据)
	m.sjdw = obj.s_name; //TODO
	// 体积
	m.tiji = ''; //TODO
	// 顺丰月结账号\卡号
	m.cutid = obj.cutid;
	m.cutid_encry = _encryptPhone(obj.cutid);

	// 全一客户编码
	m.customeid = obj.customeid || '';
	//始发网点
	m.sfwd = obj.sfwd || '';
	//始发网点 code
	m.dzmd_sf = obj.dzmd_sf || '';
	//到达网点
	m.ddwd = obj.ddwd || '';
	//SF定制二维码
	m.ewm_sfdz = obj.ewm_sfdz;
	m.fm_day_unit = obj.fm_day_unit;
	m.fm_day = obj.fm_day;
	m.fm_jgxx = obj.fm_jgxx;
	m.fm_cgzc = obj.fm_cgzc;

	// 顺丰云打印url
	obj.pdfUrlObj && (m.pdfUrl = obj.pdfUrlObj[obj.exnumber]);
	//圆通网点圆准达 时间
	m.yzd_time = obj.yzd_time;

	//顺丰快运
	m.sfky_day = obj.sfkyDay;
	m.water_mark = obj.watermark;
	m.psfs = obj.deliveryMode;
	m.cplx = obj.cplx || '';


	//  新增
	m.p_count_no_gifts = obj.noContainGiftsNum;
	m.offline_remarks = obj.sysMemo;
	m.wave_no = obj.waveNo;
	m.wave_packing_no = obj.boxNo;
	m.wave_package_no = obj.packageNo;
	// JD
	m.trans_type = obj.trans_type || '';
	//店铺平台名称
	m.shop_plat = platformByName[obj.platform] || ''

	// 龙邦（极兔速递) 运费
	m.freight = obj.freight;

	//SF 丰密面单icon数据处理
	if (obj.abFlag) {
		const PRINT_ICON_URL = 'https://static.kuaidizs.cn/resources/img/print/printIcon/';
		m.img_v_fmAb = PRINT_ICON_URL + obj.abFlag + '.png';
	}
	if (obj.printIcon) {
		_originUrl = (location.protocol || 'http:') + '//img.alicdn.com/imgextra';   //此处协议保存和XML协议一致，走闪打控件里面下载协议统一控制
		_arrIcon = obj.printIcon.split('');
		_iconIndex = 1;
		_imgName = ['/i1/69942425/O1CN011Tmguoyk7oV6rOn_!!69942425.png',
			'/i2/69942425/O1CN011TmguoleOjWaXrF_!!69942425.png',
			'/i4/69942425/O1CN011TmguoP8o00Izyl_!!69942425.png',
			'/i3/69942425/O1CN011TmgunRSfdoWHQ3_!!69942425.png',
			'/i4/69942425/O1CN011TmgulyO6uRIwuV_!!69942425.png',
			'/i3/69942425/O1CN011TmguoGMKl8gPZm_!!69942425.png'];   //'sf_heavy','sf_crab','sf_fresh','sf_fragile','sf_medicine','sf_z'
		_arrIcon.map(function (v, i) {
			if (v == 1 && _imgName[i]) {
				m['img_v_fm' + _iconIndex] = _originUrl + _imgName[i];
				_iconIndex++;
			}
		});
	}
	if (obj.newAbflag) {
		_originUrl = (location.protocol || 'http:') + '//img.alicdn.com/imgextra';   //此处协议保存和XML协议一致，走闪打控件里面下载协议统一控制
		const flagMap = {
			'A': '/i3/69942425/O1CN01dIPMrB1Tmh5bJ7gGl_!!69942425.png', //燃固
		};
		m.img_v_fmNewAb = _originUrl + (flagMap[obj.newAbflag] || '');
	}


	// 总重量
	obj.hj_weight ? (m.zhliang = (Math.round((obj.hj_weight / 1000) * 10000)) / 10000) : '';
	m.zhliang = obj.zhliang / 1000; // 转化为 kg
	m.total_weight = obj.totalWeight; // 订单总重量 pack
	m.urge_shipments = obj.urge; // 催发货标识 pack
	m.delivery_to_door = obj.isDeliveryOnDoorTis ? '送货上门' : ''
	obj.s_tid ? m.txm_tid = obj.s_tid : '';

	//分拣码
	m.fjm = obj.sortCode || '';

	//分销商总价
	m.fx_cgd_pay = obj.hj_fx || '';

	//子母件
	if (obj.isZiMuJian) {
		//字母件数量
		m.zmj_sum = obj.zmj_sum;
		//字母件子单号
		m.zmj_mnum = obj.sfZDydId || '';
	}
	// 顺丰云打印
	obj.pdfUrlObj && (m.pdfUrlObj = obj.pdfUrlObj);
	//菜鸟配 打印logo的路径
	if (obj.cnpKdCode) {
		logoArr = {
			'DISTRIBUTOR_11429905': 'yto',       //"圆通"
			'DISTRIBUTOR_12007118': 'zto',       //"中通" "浙江中通"
			'DISTRIBUTOR_11424992': 'htky',      //"百世"
			'DISTRIBUTOR_13150963': 'ems',       //EMS
			'DISTRIBUTOR_13150773': 'ems',        //EYB
		};
		m.logo = logoArr[obj.cnpKdCode] ? '/resources/img/print/ExImg/exlogo/' + logoArr[obj.cnpKdCode] + '.jpg' : '';
	}

	//JD面单新增字段
	m.sf_city = obj.sf_city;
	m.vender_id = obj.vender_id;
	//服务类型
	m.servicetype = obj.servicetype;

	//JD自营面单 新增字段
	m.txm_number_package = obj.packageYdId;    //单号条形码(包裹号)
	m.fhdexnumber_package = obj.packageYdId;    //快递单号(包裹号)

	m.printPdfData = obj.printPdfData; // 京东云模板打印数据
	m.geturl = obj.geturl;
	m.jdprintData = obj.jdprintData;
	m.wayBillNo = obj.wayBillNo;
	m.customUrl = obj.customUrl;
	// 京东众邮 新增字段
	m.zy_lqbm = obj.route;
	m.zy_sfwd = obj.zy_sfwd;
	m.zy_sfwdbm = obj.zy_sfwdbm;
	m.zy_mdwd = obj.zy_mdwd;
	m.zy_mdwdbm = obj.zy_mdwdbm;
	m.zy_ddsx = obj.zy_ddsx;

	m.route = obj.route;
	m.item_title = ((obj.orders || [])[0] || {}).title || '';
	m.txm_zd_number = m.zd_exnumber = obj.sfZDydId;

	//---------京广快运新增字段------------------//
	if (temp?.ExCode === 'SZKKE') { //京广快运
		m.jg_hwname = obj.goods_name;  //
		m.jg_fkfs = obj.payment_type;  //
		// m.jg_dshkje = obj.codPrice; //
		m.jg_yunfei = obj.postFee;  //
		m.spec_employee = obj.spec_employee;  //
		// m.jg_weight = obj.weight; //
		m.piece_number = obj.piece_number;//
		m.send_company = obj.send_company;//
		m.ac_company = obj.accete_company;
		m.big_name = obj.big_char_name;//
		m.big_code = obj.big_char_code;
		m.spec_site = obj.spec_site;
	} else if (temp.ExCode == 'FENGWANG') {
		m.fjm = obj.fw_sortCode;
		m.ewm_str = obj.ewm_str;
		m.advertising = obj.advertising || '';
	}

	m.water_mark = obj.watermark;
	m.freight = obj.freight;  //极兔运费
	//打印批次序号
	m.batch_index = obj.batch_index;
	m.print_batch = obj.print_batch;
	m.printFlag = obj.printFlag;

	//--------------打印发货单所需要的数据信息---------------//
	if (arg.isGetFhdFjr || arg.printType === 'fhd' || arg.printType === 'bhd') {
		//发货单扫码打印id
		m.fhd_number = m.fhd_txm_number = obj.fhd_txm_number || '';

		fhdFjr = arg.fhdFjr || {};
		m.o_shop = fhdFjr.invoiceStore || ''; //发货单的店铺名称
		m.o_title = fhdFjr.invoiceTitle || ''; //发货单标题
		m.o_info = fhdFjr.invoiceMemo || ''; //友情提示
		m.o_tel = fhdFjr.phone || ''; //联系电话
		m.o_name = fhdFjr.invoiceOperator || ''; //卖家姓名 经办人
		if (arg.printType === 'fhd') {        //单独打印发货单，发件地址取值发货单发件人设置的发件地址
			m.f_addr = fhdFjr.address || '';
		}
		//发货单表格
		m.table = '';
		m.table_title = ''; //发货单表格标题
		m.table_hj = ''; //发货单表格_合计
		m.tableDataArray = obj.tableDataArray;
		m.fhdRuleSortData = obj.fhdRuleSortData;
		//发货单相同商品合并 把相同商品挨在一起
		if (arg.sortType && m.tableDataArray && m.tableDataArray.length !== 0) {
			if (arg.sortType == 'sjbm') {
				m.tableDataArray.sort(function (a, b) {
					return (a.tb_sjbm_b || '').localeCompare(b.tb_sjbm_b || '');
				});
			} else if (arg.sortType == 'tit') {
				m.tableDataArray.sort(function (a, b) {
					return (a.tb_jc || a.tb_tit || '').localeCompare(b.tb_jc || b.tb_tit || '');
				});
			} else if (arg.sortType == 'ggsjbm') {
				m.tableDataArray.sort(function (a, b) {
					return (a.tb_sjbm_kh || '').localeCompare(b.tb_sjbm_kh || '');
				});
			}
		}
	} else if (platformConfig.kdd.canPrintTable(clientType)) {
		m.tableDataArray = obj.tableDataArray || [];
		//TODO 暂时去除合并逻辑
		// if (m.tableDataArray.length) {
		//     const cfMap = {}
		//     m.tableDataArray.forEach(item => {
		//         if (cfMap[item.tb_tit + item.tb_cpgg]) {
		//             const count = +cfMap[item.tb_tit + item.tb_cpgg].tb_count
		//             const newCount = Number(count) + Number(item?.tb_count || 0)
		//             cfMap[item.tb_tit + item.tb_cpgg] = { ...item, tb_count:newCount }
		//         } else {
		//             cfMap[item.tb_tit + item.tb_cpgg] = item
		//         }
		//     })
		//     m.tableDataArray = Object.keys(cfMap).map(key => {
		//         return cfMap[key]
		//     })
		// }
	}

	//2020.8.12 调整 ，拼多多电子面单涉及加密改造，自定义区域的字段需要使用订单详情接口返回的脱敏字段，而非加密后字段（获取单号需要加密字段）
	//「买家旺旺 s_ww 」，「收件人 s_name 」 ，「收件手机 s_phone 」，「收件地址 s_addrall 」
	if (kddType == 7) {
		// 买家旺旺
		m.s_ww = (obj.buyerNick_origin || obj.buyerNick || '').replace('\uFFFF', '');
		// 收件人
		m.s_name = obj.s_name_origin || obj.s_name || '';
		// 收件单位(没有此项时用收件人姓名数据)
		m.sjdw = obj.s_name_origin || obj.s_name;

		// 收件手机
		if ((arg.temp || {}).styleId == 26) {   //SF丰密加密电话号码
			m.s_phone = this.getPrivMethod('_encryptPhone', obj.s_mobile_origin || obj.s_mobile);
		} else {
			m.s_phone = obj.s_mobile_origin || obj.s_mobile || '';
		}

		// 收件地址 // 用户输入的部分，与省市区重复的部分在CS阶段过滤掉
		m.s_addr = obj.s_address_origin || obj.s_address || '';
		// m.shop_title = obj.shopTitle || obj.shopName; // 店铺标题
		// 解决pdd扫描打印
		m.s_addrall = (obj.s_province + obj.s_city + obj.s_county + obj.s_address_origin) || obj.s_address || '';
	}


	//erp 抖音电子面单特殊处理
	if (comp.Print.Data.platform === 'erp' && kddType == 8) {
		// 买家旺旺
		m.s_ww = (obj.buyerNick_origin || obj.buyerNick || '').replace('\uFFFF', '');
		// 收件人
		m.s_name = obj.s_name_origin || obj.s_name || '';
		// 收件单位(没有此项时用收件人姓名数据)
		m.sjdw = obj.s_name_origin || obj.s_name;

		// 收件手机
		m.s_phone = obj.s_mobile_origin || obj.s_mobile || '';

		// 收件地址 // 用户输入的部分，与省市区重复的部分在CS阶段过滤掉
		m.s_addr = obj.s_address_origin || obj.s_address || '';

		// 收件街道
		m.s_addrall = [obj.s_province, obj.s_city, obj.s_county, obj.s_town, m.s_addr].filter(Boolean).join(' ');
	}
	if (arg.printType === 'fhd') {
		let sidNosArr = Object.values(obj.sidNos || {}).filter(o => o !== '打印后生成') || []
		m.fhdexnumber = sidNosArr.join(',')
		m.txm_number = sidNosArr[0] || ''
		m.ewm_number = sidNosArr.join(',')
	}
	// 小红书与发货单的打印收件人需要特殊处理下，打印脱敏信息，不能打印密文串
	if ([13, 15].includes(kddType) || isFhd) {
		m.s_phone = obj.receiverMobileMask;
		m.s_tel = obj.receiverPhoneMask;
		m.s_name = obj.receiverNameMask;
		m.s_addr = obj.receiverAddressMask;
		m.s_addrall = [obj.s_province, obj.s_city, obj.s_county, obj.s_town, m.s_addr].filter(Boolean).join(' ');
	}
	m.isNeedBreakPage = obj.isNeedBreakPage;
	m.printContentArr = obj.printContentArr;
	m.pageNum = obj.pageNum;
	// 底单重打特殊字段取值
	if (arg.isReprint) {
		const ydAttr = JSON.parse(obj.ydAttr || '{}');
		if (ydAttr) {
			Object.assign(m, {
				position: ydAttr.position,  // 一段码
				position_no: ydAttr.position_no,   //二三段码
				tp_status: ydAttr.tp_status,    //四段码
				cus_area2: ydAttr.cus_area2,
				cus_area1: ydAttr.cus_area1,
				package_wdjc: ydAttr.package_wdjc,
				collection_value: ydAttr.collection_value,
				value: ydAttr.value,
			});
		}
	}

	// bic订单数据
	if (arg.isBIC) {
		m.fhdexnumber = obj.bicCode;
		m.txm_number = obj.bicCode;
		m.o_shop = obj.seller_nick;
	}

	try {
		// 自动添加额外字段
		attachExtraDataToPrintData({
			raw: m,
			electron: obj.electronData,
			tplDetail: arg.modeInfo,
		});
	} catch (error) {
		console.warn('attach extra data failed:', error);
	}

	return m;
}

/**
 * [_formatSmallFhdData 拿货单小标签数据转化]
 * @param  {[array]} orderList [收集的订单数据]
 * @return {[array]}           [格式化的数据]
 */
function _formatSmallFhdData(orderList, arg) {
	let plist = [],
		// _this = this,
		_len = arg.orderList.length,
		modeInfo = arg.modeInfo,
		sku, _m, m, ind, fjSids,
		_params = {
			printOp: arg.pirntOp,
			kddFjr: arg.kddFjr,
			fhdFjr: arg.fhdFjr,
			kddFjrMap: arg.kddFjrMap,
			fhdFjrMap: arg.fhdFjrMap,
			// isUseOrderFjr: arg.isUseOrderFjr,
			isGetFhdFjr: arg.isGetFhdFjr,
			sortType: arg.sortType,
			temp: arg.temp,
			printType: arg.printType,
			encryItems: arg.encryItems,
			isSmallTag: true,//发货单小标签
			allInputsList: arg.allInputsList,
		};
	orderList.map(function (v, i) {
		v.print_count = (i + 1) + '/' + _len;
		//拿货小标签加入普通字段
		if (!arg.isUseOrderFjr && arg.printType === 'kdd' && !arg.kddFjr) {
			_params.kddFjr = v.kddFjr || printItem.getDefaultFjrInfo(arg.kddFjrMap, v.userId2 || v.taobaoId);
		}
		if ((arg.isGetFhdFjr || arg.printType === 'fhd') && !arg.fhdFjr) {
			_params.fhdFjr = printItem.getDefaultFjrInfo(arg.fhdFjrMap, v.sellerId || v.taobaoId);
			if (arg.isUseOrderFjr) _params.kddFjr = {
				address: v.f_address || '',
				city: v.f_city || '',
				district: v.f_district || '',
				name: v.f_name || '',
				phone: v.f_mobile || '',
				postCode: "",
				province: v.f_province || ''
			}
		}
		_params.obj = v;
		m = formatPrintData(_params);
		if (v.count > 1) {
			m.is_multi = '多';
		} else {
			m.is_multi = '单';
		}
		if (v.tids && v.tids.length > 1) {
			m.txm_tid = '';
			m.s_tid = v.tids[0] + ',等' + v.tids.length + '个订单';
		}

		m.s_tid = m.txm_tid = v.tids[0];

		m.print_flag = v.isPrinted ? '▇' : ''; //是否是补打
		m.xdate = (v.orderCreateTime || '').split(' ')[0]; //下单时间
		m.print_time = new Date().format('MM-dd'); //打印时间
		m.goods_count = v.count;
		fjSids = v.fjSids ? v.fjSids.concat() : '';
		let currentIndex = 0;
		v.tableDataArray && v.tableDataArray.map((k) => {
			_m = Object.assign({}, m, true);
			// if (k.goods_color !== undefined) {      //外部处理
			//     _m.goods_color = k.goods_color;
			//     _m.goods_size = k.goods_size;
			// } else {
			//     sku = '';
			//     if (k.skuAll) {
			//         if (k.skuAll.indexOf(';') > -1) {
			//             sku = k.skuAll.split(';');
			//         } else if (k.skuAll.indexOf('，') > -1) {
			//             sku = k.skuAll.split('，');
			//         } else {
			//             sku = k.skuAll.split(';');
			//         }
			//     }
			//     sku && sku.map((item) => {
			//         if (item.indexOf('颜色') > -1) {
			//             _m.goods_color = item.split(':')[1] || item.split('：')[1] || '';
			//         }
			//         if (/尺寸|尺码|参考身高/.test(item)) {
			//             _m.goods_size = item.split(':')[1] || item.split('：')[1] || '';
			//         }
			//     });
			// }

			// 规格信息
			// 颜色尺码
			_m.goods_color = k.tb_color;
			_m.goods_size = k.tb_size;
			_m.specifications_name = k.tb_skuName;
			_m.specifications_name2 = k.tb_hpggmc;
			_m.specifications_code = k.tb_sjbm_kh;

			_m.gg_content = k.outerSkuId;
			_m.nh_goods_code = k.itemNo;
			_m.goods_content = k.tb_sjbm_b;
			_m.goods_jc = k.tb_jc;
			_m.goods_gg_shop_code = k.tb_sjbm_kh || ''; // 规格商家编码
			_m.fh_goods_name = k.tb_tit;
			_m.fh_seller_content = k.tb_sjbm_b;
			_m.fh_goods_jc = k.tb_bdhp_jc;
			_m.fh_specs_alias = k.tb_hpggbm;
			_m.fh_product_code = k.tb_hpsjbm;
			// 这里需要接入配置，是否是商品维度打印
			if (modeInfo?.ModeTempPrintcfg?.sameSkuMerge == 1) {
				_m.is_multi = k.tb_count > 1 ? '多' : '单';
				_m.fh_goods_count = k.tb_count;
				_m.goods_count = (v.tableDataArray || []).length;
				plist.push(_m);
			} else {
				for (ind = 0; ind < k.tb_count; ind++) {
					currentIndex++;
					const _m1 = Object.assign({}, _m, true);
					_m1.goods_count_xh = v.count + '-' + currentIndex; // 总数量-序号
					fjSids && (_m1.fhd_txm_number = _m1.fhd_number = _m1.fj_txm_number = _m1.fj_number = fjSids.shift());
					_m1.is_multi = '单';
					_m1.fh_goods_count = 1;
					plist.push(_m1);
				}
			}


		});
	});
	return plist;
}


async function getFormatPrintData(arg) {
	let plist = [],
		_isCloudFormat,
		_len,
		temp = arg.templateInfo || {},
		_kddFjr = arg.kddFjr,
		// _this = this,
		_infoFjr,
		_params
		, previewCycleCount = arg.previewCycleCount || 0
		, previewBatchNum = arg.previewBatchNum || 0
		, previewElecNoCount = arg.previewElecNoCount || 1;

	const kddType = temp.KddType;

	/**
	 * 是否是单打
	 */
	const isSinglePrint = arg.printOp === 0;

	const allInputsList = await printItem.getAllTemplateDefaultInputList(isSmallTag(temp) ? 2 : 1);// 获取所有的数据框
	console.info('allInputsList', allInputsList);
	_len = arg.previewOrderNum * previewElecNoCount || arg.orderList.length;
	_isCloudFormat = !arg.noAllowPrintElec && (comp.Print.Data.modeType && kddType == 3 || kddType == 7 || kddType == 9 || kddType == 13) && arg.printType == 'kdd';
	_params = {
		printOp: arg.printOp,
		kddFjr: arg.kddFjr,
		fhdFjr: arg.fhdFjr,
		kddFjrMap: arg.kddFjrMap,
		fhdFjrMap: arg.fhdFjrMap,
		isUseOrderFjr: arg.isUseOrderFjr,
		isGetFhdFjr: arg.isGetFhdFjr,
		sortType: arg.sortType,
		temp,
		modeInfo: arg.modeInfo,
		printType: arg.printType,
		encryItems: arg.encryItems,
		allInputsList,
		isBIC: arg.isBIC,
	};
	if (temp && temp.KddType == 2 && [89, 11027, 11028].includes(temp.Exid) && !arg.isReprint) { //韵达网点面单打印，特殊处理
		arg.orderList.map(v => plist.push(v.yundaPdfInfo));

		return plist;
	}
	if (temp.KddType == 14) {
		return arg.orderList.map(v => {
			_params.obj = v;
			v.custom = formatPrintData(_params);    // 自定义区域的字段
			return v;
		})
	}



	// 手工单使用订单发件人 手工单新增平台手工单与供分销手工单，platform不再是唯一评判标准
	// 手工单场景：platfrom = hand  source = hand 或者 SCMHAND
	// 待优化，isUseOrderFjr是因订单变化，不应该是全局参数
	if (comp.Print.Data.platform == 'erp' && arg.orderList.some((i) => (i.platform === 'hand' || ['HAND', 'SCMHAND'].includes(i.source)))) {
		arg.isUseOrderFjr = true;
	}
	if (isSmallTag(temp)) {
		arg.allInputsList = allInputsList;
		return _formatSmallFhdData(arg.orderList, { ...arg, temp });
	}
	if (kddType == 8 && arg.modeInfo) {
		_params.pslx = getTplModeServiceValue(arg.modeInfo, 'pslx');
		_params.is_cod = !!getTplModeServiceValue(arg.modeInfo, 'dshk');
		_params.is_insure = !!getTplModeServiceValue(arg.modeInfo, 'bjje');
	}

	// 抖音加密模板
	const isNewDyTpl = isDYEncryptTpl(kddType, temp.customTop) && !isSinglePrint;
	// 美团
	const isThhTpl = ThhTplNeedAuth(kddType) && !isSinglePrint;
	if (isThhTpl) {
		arg.orderList.map((v, i) => {
			/** satrt 使用订单数据里面的发件人信息 **/
			if (arg.isUseOrderFjr) {
				_infoFjr = {
					'address': {
						city: '',
						detail: v.f_address || '',
						province: '',
						town: '',
						district: '',
					},
					'mobile': v.f_mobile || '',
					'name': v.f_name || '',
					'phone': v.f_tel || '',
				};
			} else {
				//发件人处理
				if (!arg.kddFjr) {
					_kddFjr = _params.kddFjr = v.kddFjr || printItem.getDefaultFjrInfo(arg.kddFjrMap, v.userId2 || v.taobaoId);
				}
				if (arg.isGetFhdFjr && !arg.fhdFjr) {
					_params.fhdFjr = printItem.getDefaultFjrInfo(arg.fhdFjrMap, v.userId2 || v.taobaoId);
				}
				_infoFjr = {
					'address': {
						city: _kddFjr.city || '',
						detail: _kddFjr.address || '',
						province: _kddFjr.province || '',
						town: '',
						district: _kddFjr.district || '',
					},
					'mobile': _kddFjr.phone || '',
					'name': _kddFjr.name || '',
					'phone': '',
				};
			}
			/** end 使用订单数据里面的发件人信息 **/

			!v.cloudData.addData && (v.cloudData.addData = {});
			v.cloudData.addData.sender = _infoFjr;

			/** satrt 发货单快递单打在一张纸 **/
			if (!temp.IsConcatFhd && (arg.isSanLian || comp.base.getTempStyle('sanlian', temp.styleId))) {
				plist.push({
					documentID: v.exnumber,
					content: v.cloudData, //cloudData 电子面单 获取得到的数据
					isNeedBreakPage: v.isNeedBreakPage,

				});
			} else {
				v.print_count = (i + 1) + previewCycleCount * previewBatchNum + '/' + _len;
				_params.obj = v;
				//  处理打印数据的格式为控件要求的格式
				const thhData = printItem.transformToThhFormat(v);
				console.info(thhData);
				plist.push({
					documentID: v.exnumber,
					thhPrintType: 1,//打印流程类型 1为线下取号流程
					content: {
						data: thhData,
						templateURL: v.cloudData.template_url || '',
					}, //cloudData 电子面单 获取得到的数据
					custom: formatPrintData(_params),// 自定义区域的字段
					isNeedBreakPage: v.isNeedBreakPage,
				});
			}
			/** end 发货单快递单打在一张纸 **/
		});
		console.info(plist);
		return plist;
	}
	// 菜鸟官方模板打印数据拼装
	if (_isCloudFormat || isNewDyTpl || kddType == 15 || kddType == 16) {
		arg.orderList.map(async (v, i) => {
			if (arg.isUseOrderFjr && v.f_name) {      //使用订单数据里面的发件人信息
				_infoFjr = {
					'address': {
						city: v.f_city || '',
						detail: v.f_address || '',
						province: v.f_province || '',
						town: '',
						district: v.f_district || '',
					},
					'mobile': v.f_mobile || '',
					'name': v.f_name || '',
					'phone': v.f_tel || v.f_mobile || '',
				};
			} else {
				//发件人处理
				if (!arg.kddFjr) {
					_kddFjr = _params.kddFjr = Object.keys(v?.kddFjr || {}).length ? v.kddFjr : printItem.getDefaultFjrInfo(arg.kddFjrMap, v.userId2 || v.taobaoId);
				}
				if (arg.isGetFhdFjr && !arg.fhdFjr) {
					_params.fhdFjr = printItem.getDefaultFjrInfo(arg.fhdFjrMap, v.sellerId || v.taobaoId);
				}
				_infoFjr = {
					'address': {
						city: _kddFjr.city || '',
						detail: _kddFjr.address || '',
						province: _kddFjr.province || '',
						town: '',
						district: _kddFjr.district || '',
					},
					'mobile': _kddFjr.phone || '',
					'name': _kddFjr.name || '',
					'phone': '',
				};
			}

			if (temp.KddType == 7) {
				// 兼容
				if (!v.cloudData.waybillCode && (v.encrypted || v.cloudData.encryptedData || v.cloudData.data.encryptedData)) {
					$.extend(v.cloudData, v.cloudData.data);
					v.platformId && (v.cloudData.userid = v.platformId);
					!v.cloudData.addData && (v.cloudData.addData = {});
					v.cloudData.addData.sender = _infoFjr;
				} else {
					!v.cloudData.data && (v.cloudData.data = {});
					$.extend(v.cloudData.data, v.cloudData);
					v.cloudData.data.sender = _infoFjr;
					v.platformId && (v.cloudData.userid = v.platformId);
				}
				// 打印批次相关
				v.print_count = (i + 1) + previewCycleCount * previewBatchNum + '/' + _len;
				_params.obj = v;

				plist.push({
					documentID: String(v.exnumber),
					content: v.cloudData,//cloudData 电子面单 获取得到的数据
					custom: formatPrintData(_params),     //自定义区域的字段
					isNeedBreakPage: v.isNeedBreakPage,
				});
			} else if (temp.KddType == 9) { // 快手
				const ksInfoFjr = {
					address: {
						cityName: _infoFjr.address.city,
						countryCode: 'CHN',
						detailAddress: _infoFjr.address.detail,
						districtName: _infoFjr.address.district,
						provinceName: _infoFjr.address.province,
					},
					contact: {
						mobile: _infoFjr.mobile,
						name: _infoFjr.name,
					},
				};
				!v.cloudData.addData && (v.cloudData.addData = {});
				v.cloudData.addData.senderInfo = ksInfoFjr;

				if (!temp.IsConcatFhd && (arg.isSanLian || comp.base.getTempStyle('sanlian', temp.styleId))) {
					plist.push({
						documentID: v.exnumber,
						content: v.cloudData, //cloudData 电子面单 获取得到的数据
						isNeedBreakPage: v.isNeedBreakPage,

					});
				} else {
					v.print_count = (i + 1) + previewCycleCount * previewBatchNum + '/' + _len;
					_params.obj = v;

					let content = v.cloudData;
					if (isNewDyTpl) {
						const params = JSON.parse(v.cloudData.shop_Key || '{}');
						const paramsStr = Object.keys(params).map(key => `${key}=${params[key]}`).join('&');

						content = {
							templateURL: v.cloudData.template_url,
							params: encodeURI(paramsStr),
							encryptedData: v.cloudData.print_data,
							signature: v.cloudData.sign,
							addData: transformToDyFormat(v.cloudData.addData),
						};
					}
					plist.push({
						documentID: v.exnumber,
						waybillCode: v?.cloudData?.waybillCode,
						ksOrderFlag: true,
						content: content, //cloudData 电子面单 获取得到的数据
						custom: formatPrintData(_params), //自定义区域的字段
						isNeedBreakPage: v.isNeedBreakPage,
					});
				}

			} else {
				if (v.encrypted || v.cloudData.encryptedData || v.cloudData.shop_Key) {
					!v.cloudData.addData && (v.cloudData.addData = {});
					v.cloudData.addData.sender = _infoFjr;
				} else {
					Object.assign(v.cloudData.data.sender, _infoFjr);
				}
				if (!temp.IsConcatFhd && (arg.isSanLian || comp.base.getTempStyle('sanlian', temp.styleId) && temp.ExCode !== 'SF')) { // 顺丰菜鸟三联单需要打印自定义区域
					plist.push({
						documentID: v.exnumber,
						content: v.cloudData, //cloudData 电子面单 获取得到的数据
						isNeedBreakPage: v.isNeedBreakPage,
						printContentArr: v.printContentArr,
						pageNum: v.pageNum,
					});
				} else {
					v.print_count = (i + 1) + previewCycleCount * previewBatchNum + '/' + _len;
					_params.obj = v;

					let content = v.cloudData;

					if (isNewDyTpl) {
						const params = JSON.parse(v.cloudData.shop_Key || '{}');
						const paramsStr = Object.keys(params).map(key => `${key}=${params[key]}`).join('&');

						content = {
							templateURL: v.cloudData.template_url,
							params: encodeURI(paramsStr),
							encryptedData: v.cloudData.print_data,
							signature: v.cloudData.sign,
							addData: transformToDyFormat(v.cloudData.addData),
						};
					}

					plist.push({
						documentID: v.exnumber,
						content: content, //cloudData 电子面单 获取得到的数据
						custom: formatPrintData(_params),     //自定义区域的字段
						isNeedBreakPage: v.isNeedBreakPage,
						printContentArr: v.printContentArr,
						pageNum: v.pageNum,
					});
				}
			}
		});
		return plist;
	}
	// 得物模板打印数据
	if (kddType == 17) {
		arg.orderList.map(async (v, i) => {
			// if (arg.isUseOrderFjr && v.f_name) {      //使用订单数据里面的发件人信息
			//     _infoFjr = {
			//         'address': {
			//             city: v.f_city ||'',
			//             detail: v.f_address || '',
			//             province:v.f_province|| '',
			//             town: '',
			//             district: v.f_district|| '',
			//         },
			//         'mobile': v.f_mobile || '',
			//         'name': v.f_name || '',
			//         'phone': v.f_tel || v.f_mobile || '',
			//     };
			// } else {
			//     //发件人处理
			//     if (!arg.kddFjr) {
			//         _kddFjr = _params.kddFjr = Object.keys(v.kddFjr).length ? v.kddFjr : printItem.getDefaultFjrInfo(arg.kddFjrMap, v.userId2 || v.taobaoId);
			//     }
			//     if (arg.isGetFhdFjr && !arg.fhdFjr) {
			//         _params.fhdFjr = printItem.getDefaultFjrInfo(arg.fhdFjrMap, v.sellerId || v.taobaoId);
			//     }
			//     _infoFjr = {
			//         'address': {
			//             city: _kddFjr.city || '',
			//             detail: _kddFjr.address || '',
			//             province: _kddFjr.province || '',
			//             town: '',
			//             district: _kddFjr.district || '',
			//         },
			//         'mobile': _kddFjr.phone || '',
			//         'name': _kddFjr.name || '',
			//         'phone': '',
			//     };
			// }

			// if (v.encrypted || v.cloudData.encryptedData || v.cloudData.shop_Key) {
			//     !v.cloudData.addData && (v.cloudData.addData = {});
			//     v.cloudData.addData.sender = _infoFjr;
			// } else {
			//     Object.assign(v.cloudData.data.sender, _infoFjr);
			// }
			v.print_count = (i + 1) + previewCycleCount * previewBatchNum + '/' + _len;
			_params.obj = v;

			let content = v.cloudData;

			plist.push({
				documentID: v.exnumber,
				content: content, //cloudData 电子面单 获取得到的数据
				custom: formatPrintData(_params),     //自定义区域的字段
				isNeedBreakPage: v.isNeedBreakPage,
				printContentArr: v.printContentArr,
				pageNum: v.pageNum,
			});
		});
		return plist;
	}
	// 非菜鸟官方模板打印数据拼装
	arg.orderList.map((v, i) => {
		//发件人处理
		if (!arg.isUseOrderFjr && arg.printType === 'kdd' && !arg.kddFjr) {
			_params.kddFjr = v.kddFjr || printItem.getDefaultFjrInfo(arg.kddFjrMap, v.userId2 || v.taobaoId);
		}
		if (arg.isUseOrderFjr && arg.printType === 'kdd') {
			_params.kddFjr = v.kddFjr
		}
		if ((arg.isGetFhdFjr || arg.printType === 'fhd') && !arg.fhdFjr) {
			_params.fhdFjr = printItem.getDefaultFjrInfo(arg.fhdFjrMap, v.sellerId || v.taobaoId);
			if (arg.isUseOrderFjr) _params.kddFjr = {
				address: v.f_address || '',
				city: v.f_city || '',
				district: v.f_district || '',
				name: v.f_name || '',
				phone: v.f_mobile || '',
				postCode: "",
				province: v.f_province || ''
			}
		}
		v.print_count = (i + 1) + '/' + _len;
		if (temp.ExCode == 'DBKD') { //德邦保价处理  预览的时候无modeInfo
			let arr = [];
			arg.modeInfo && arg.modeInfo.ModeAdvancedServices && $.each(arg.modeInfo.ModeAdvancedServices, function (i, v) {
				if (v.serviceKey == 'bjfu') { //保价服务 //德邦
					arr = v.serviceValue.split('|');
				}
			});
			if (arr[0] == '声明价值保价') {
				v.bjje = arr[1];
			}
		}

		_params.obj = v;
		_params.isReprint = arg.isReprint;
		plist.push(formatPrintData(_params));
	});
	console.log('非菜鸟官方模板打印数据拼装');
	console.info(plist);
	return plist;

}


/**
 * 转换自定义发件人的数据格式符合抖音的格式
 * @param {*} addData
 * @returns
 */
function transformToDyFormat(addData) {
	const info = addData.sender;
	return {
		...addData,
		senderInfo: {
			address: {
				cityName: info.address.city,
				// countryCode: 'CHN',
				detailAddress: info.address.detail,
				districtName: info.address.district,
				provinceName: info.address.province,
			},
			contact: {
				mobile: info.mobile || info.phone,
				name: info.name,
			},
		},
	};
}


function getFormatXbqPrintData({ orderList, fhdNoArr, scanSetting, shopSignMap, startSerialNo }) {
	console.log('处理下小标签的打印数据');
	let total = orderList.length,
		pList = [],
		idx,
		printIdx = 0,
		serial = 0,
		sortCodeItem,
		pTime = new Date().format('MM-dd hh:mm:ss'),
		decryArr = [];


	orderList.forEach(v => {
		//打印市场档口分割标签页。
		v.marketStall && pList.push({
			isPrintDivider: true,
			dividerStr: v.marketStall,
		});
		let oneMarketPlist = [];
		v.order.forEach(v1 => {
			serial = 0;
			// let _skuObj = _this.getPrivMethod('_getColorAndSize',v1.skuPropertiesName);
			const _skuObj = _getColorAndSize(v1.skuPropertiesName);

			const _m = {
				print_time: pTime,   //打印时间
				// goods_color: _skuObj.goods_color || '',
				// goods_size: _skuObj.goods_size || '',
				// goods_sku: _skuObj.skuValue.join(';'), //过滤sku冒号前的说明文本
				goods_sku: v1.skuPropertiesName, // 商品规格
				goods_content: v1.outerIid || '', //商家编码
				goods_code: v1.outerIid || v1.shortTitle || v1.title || '',  // 商家编码 或 简称 或 标题
				goods_jc: v1.shortTitle,    // 宝贝简称 todo???
				gg_content: v1.skuOuterIdOrigin || v1.skuOuterId,  //规格商家编码
				sku_alias: v1.skuAlias,
			};
			v1.bhdTradeBeanList.forEach(v2 => {
				_m.txm_tid = _m.s_tid = v2.tid; // 订单编号
				// _m.is_multi = (v2.goodsNumOrigin || v2.goodsTotal) > 1 ? '多' : '单';  //多件需要判断整个订单的宝贝数量，不能判断查询的子订单的数量
				_m.is_multi = (!v1.duojian || ((v2.orders || []).length == 1 && v2.orders[0].nums == 1)) ? '单' : '多';
				_m.s_name = v2.receiverName; //收件人姓名
				_m.receiverSecret = v2.receiverSecret;
				_m.s_ww = v2.buyerNick;
				_m.f_memo = v2.sellerMemo || ''; // 买家备注
				_m.stock_sign = v2.stockStatus == 1 ? '屯' : ''; // 屯货标记
				_m.goods_count = (v2.goodsNumOrigin || v2.goodsTotal),  //总数量
					_m.shop_sign = scanSetting['storeLogo'] && scanSetting['storeLogo'] != '' ? scanSetting['storeLogo'] : shopSignMap[v2.taobaoId] || ''; //店铺标识
				_m.taobaoId = v2.taobaoId;
				_m.market = v.market || '';
				_m.stall = v.stall || '';
				_m.sf_price = v2.subPayment || 0; //实付单价

				v2.orders.forEach(item => {
					if (item.oid === v2.oid) {
						_m.ex_company = item.logisticsCompany || ''; // 快递公司
						_m.txm_waybill_number = item.invoiceNo || ''; //运单号
					}
				});
				if ($('input[data-search="showMemo"]').is(':checked')) {
					_m.remark = v2.sellerMemo || ''; // 备注
					_m.flag = v2.sellerFlag && sellerFlagMap[v2.sellerFlag] || ''; // 旗帜
					_m.leave_word = v2.buyerMessage || ''; // 留言
				}
				for (idx = 0; idx < v2.tradeGoodNum; idx++) {
					// for (idx = 0; idx < v2.goodsTotal; idx++) { // 淘宝的tradeGoodNum 和 goodsTotal 保持一致，拼多多和淘宝有区别。拼多多不一样
					sortCodeItem = (v2.sortCodeList || [])[idx] || {};
					const _m1 = $.extend({}, _m, true);
					//商品唯一码
					_m1.fj_number = sortCodeItem.sid;
					_m1.fhd_txm_number = _m1.fhd_number = _m1.fj_txm_number = _m1.fj_number;

					//商品分拣码生成
					_m1.fjm_code = sortCodeItem.sortCode || '';

					//商品唯一码生成时间 生成日期
					_m1.pdate = sortCodeItem.created ? new Date(sortCodeItem.created).format('MM-dd hh:mm:ss') : pTime;

					//商品唯一码生成时间 打印日期
					_m1.create_date = sortCodeItem.created ? new Date(sortCodeItem.created).format('MM-dd hh:mm:ss') : pTime;
					// 加急天数
					_m1.urgent_sign = sortCodeItem.urgentTime != 0 ? `缺${sortCodeItem.urgentTime}天` : '';
					_m1.goods_count_xh = v1.nums + '-' + (++serial);
					_m1.fdate = new Date(v2.payTime).format('yyyy-MM-dd hh:mm:ss');
					_m1.gg_content = v1.skuOuterId;
					printIdx++;
					// pList.push(_m1);
					oneMarketPlist.push(_m1);
				}
			});
		});
		//针对一个档口排序
		oneMarketPlist = _sortPrintLabel(oneMarketPlist, scanSetting);
		pList = pList.concat(oneMarketPlist);
	});

	//流水号赋值 避免排序后出现流水号顺序不正确问题
	pList = _setSerial(pList, startSerialNo);
	// 搜集解密信息
	pList.forEach(item => {
		decryArr.push({
			tid: item.s_tid,
			receiverSecret: item.receiverSecret,
		});
	});
	// 解密收件人
	// pList = await this.getPrivMethod('_getDecryPList', decryA)
	console.log('======最后的打印小标签的数据-----');
	console.log({ pList });
	return pList;
}
function getCustomDataItem(customAttributesList = []) {
	let systemSetting = window.erpData?.systemSetting || {};
	if (!systemSetting?.itemCustomAttribute) return {}
	let customObj = {}
	customAttributesList.forEach(o => {
		customObj[o.key] = o.value
	})
	return customObj
}
function getFormatBhdXbqPrintData({ orderList, startSerialNo, scanPrintSetting, printType, dpdPrintNumType, source, printStyle, isPrintSubGoods }) {
	let total = orderList.length,
		pList = [],
		pTime = new Date().format('MM-dd hh:mm:ss'),
		curStore = null,
		isGoodSmallTag = ['bhd', 'bkd'].includes(printType),
		printDividerObjType = 'stall';
	console.log('这是小标签数据组装开始');
	if (isGoodSmallTag) {
		// 如果是拿货或者爆款标签，需要获取档口标签规则设置
		let labelSort = ['stall', 'supplierName', 'market', 'custom']
		let labelSet = scanPrintSetting; //标签设置属性
		// 1、档口引导标签打印内容仅配置了一个情况下
		// 按对应的配置项来进行汇总排序；
		// 如果是只勾选自定义，默认取档口
		// 2、档口引导标签打印内容仅配置了多个情况下
		// 默认按「档口」->「供应商」->「市场」->自定义内容 顺序来配置，仅按照其中一项汇总排序
		// 3、如果只设置自定义或者为勾选，默认取档口
		let printDividerObj = labelSet?.scanSet?.stallContentConfig || {}
		let valuesArr = printDividerObj.values || []
		if (valuesArr.length) {
			printDividerObjType = labelSort.find(o => {
				return valuesArr.includes(o)
			})
			if (printDividerObjType === 'custom') printDividerObjType = 'stall';
		} else {
			printDividerObjType = 'stall'
		}

	}
	orderList.forEach(o => {

		if (printType === 'cgd') {
			let purchaseStatusObj = {
				0: '未入库',
				1: '部分入库',
				2: '全部入库',
			}
			//采购单数据单独处理一下
			let tableDataArray = o.purchaseItemDTOS?.map(it => {
				return {
					tb_hpjc: it.sysItemAlias || '',
					tb_hpggmc: it.sysSkuName || '',
					tb_hpggbm: it.skuOuterId || '',
					tb_sys_sku_alias: it.sysSkuAlias || '',
					tb_cgsl: it.itemCount || '',
					tb_cbj: it.sysItemCost || '',
					tb_tagprice: it.tagPrice,
					tb_sj: it.price,
					tb_yrksl: it.instockCount || '',
					tb_rksj: it.instockTime || '',
					tb_brandName: it.brandName || '',
					tb_img: (it.picUrl || '').replace('_.webp', ''),
					tb_tb_txm_txt: it.barCode || '',
				}
			}) || []
			let itemData = {
				print_time: pTime,
				cgd_name_goods: o.purchaseName || '', //'名称',
				cgd_no_goods: o.purchaseNo || '', //采购单号
				cgd_supplier_goods: o.supplierName || '', //'供应商',
				cgd_state_goods: purchaseStatusObj[o.purchaseStatus] || '', //'采购状态',
				cgd_total_goods: o.purchaseCount || '', //'采购总数量',
				cgd_money_goods: o.purchaseTotalAmount || '', //'采购总金额',
				cgd_freight_goods: o.carriage || '', //'采购运费',
				cgd_other_charges_go: o.otherCost || '', //'其它费用',
				cgd_founder_goods: o.createUser || '', //'创建人',
				cgd_order_time_goods: o.createTime || '', //'下单时间',
				cgd_notes_goods: o.memo || '', //'备注',
				cgd_no_txm_with_code: o.purchaseNo || '', //'采购单号',
				cgd_no_ewm: o.purchaseNo || '', //'采购单号',
				actual_quantity_in_s: o.instockCount,
				tableDataArray
			}
			pList.push(itemData)
		} else if (printType === 'rkd') {
			let instockStatusObj = {
				0: '未入库',
				1: '已入库',
			}
			// obj.rkd_founder_goods = "创建人";
			// obj.rkd_freight_goods  = "入库运费";
			// obj.rkd_money_goods  = "入库总金额";
			// obj.rkd_name_goods  = "名称";
			// obj.rkd_no_ewm  = "入库单号";
			// obj.rkd_no_goods  = "入库单号";
			// obj.rkd_no_txm_with_code  = "入库单号";
			// obj.rkd_notes_goods  = "备注";
			// obj.rkd_order_time_goods  = "下单时间";
			// obj.rkd_other_charges_go  = "其它费用";
			// obj.rkd_state_goods  = "入库状态";
			// obj.rkd_supplier_goods  = "供应商";
			// obj.rkd_total_goods  = "入库总数量";

			//入库单数据单独处理一下
			let tableDataArray = o.reachItemResponseItems?.map(it => {
				return {
					tb_hpjc: it.sysItemAlias || '',
					tb_hpggmc: it.sysSkuName || '',
					tb_hpggbm: it.skuOuterId || '',
					tb_sys_sku_alias: it.sysSkuAlias || '',
					tb_rksl: it.instockCount || '',
					tb_sj: it.price || '',
					tb_tagprice: it.tagPrice || '',
					tb_cbj: it.costPrice || '',
					tb_img: (it.picUrl || '').replace('_.webp', '')
				}
			}) || []
			let itemData = {
				print_time: pTime,
				rkd_founder_goods: o.createUser || '',
				rkd_freight_goods: o.carriage || '',
				rkd_money_goods: o.reachTotalAmount || '',
				// rkd_name_goods : o.createUser || '',
				rkd_no_ewm: o.reachNo || '',
				rkd_no_goods: o.reachNo || '',
				rkd_no_txm_with_code: o.reachNo || '',
				rkd_notes_goods: o.memo || '',
				rkd_order_time_goods: o.created || '',
				rkd_other_charges_go: o.otherCost || '',
				rkd_state_goods: instockStatusObj[o.instockStatus] || '',
				rkd_supplier_goods: o.supplierName || '',
				rkd_total_goods: o.reachTotal || '',
				tableDataArray
			}
			pList.push(itemData)
		} else if (printType === 'ckd') {
			// obj.ckd_rno_goods = "出库单号";
			// obj.ckd_state_goods = "出库状态";
			// obj.ckd_total_goods = "总数量";
			// obj.ckd_money_goods = "出库总金额";
			// obj.ckd_freight_goods = "出库运费";
			// obj.ckd_other_charges_go = "其它费用";
			// obj.ckd_founder_goods = "创建人";
			// obj.ckd_create_time_good = "创建时间";
			// obj.ckd_notes_goods = "备注";
			// obj.ckd_recipient_goods = "收件人";
			// obj.ckd_contact_details_ = "联系方式";
			// obj.ckd_no_txm_with_code = "出库单号";
			// obj.ckd_no_ewm = "出库单号"
			let outboundStatusObj = {
				0: '未入库',
				1: '部分出库',
				2: '全部出库',
				3: '关闭',
			}
			//出库单数据单独处理一下
			let tableDataArray = o.handOutboundOrderItemList?.map(it => {
				return {
					tb_hpjc: it.sysItemAlias || '',
					tb_hpggmc: it.sysSkuName || '',
					tb_hpggbm: it.skuOuterId || '',
					tb_sys_sku_alias: it.sysSkuAlias || '',
					tb_cksl: it.count || '',
					tb_sj: it.price || '',
					tb_tagprice: it.tagPrice || '',
					tb_cbj: it.costPrice || '',
					tb_ycksl: it.count || '',
					tb_img: (it.picUrl || '').replace('_.webp', ''),
					tb_tagprice_total: it.totalTagPrice || '',
					tb_cbj_total: it.totalCostPrice || '',
					tb_sj_total: it.totalPrice || '',
					tb_warehouseSlotName: it.warehouseSlotName || '',
					tb_tb_txm_txt: it.barCode || '',
				}
			}) || []
			let addressInfo = `${o.receiverProvince || ''} ${o.receiverCity || ''} ${o.receiverCounty || ''} ${o.receiverAddress || ''} `
			let itemData = {
				print_time: pTime,
				ckd_rno_goods: o.outboundNumber || '',
				ckd_state_goods: outboundStatusObj[o.stockStatus] || '',
				ckd_total_goods: o.totalSize || '',
				ckd_money_goods: o.totalPrice || '',
				ckd_freight_goods: o.carriage || '',
				ckd_other_charges_go: o.otherCost || '',
				ckd_founder_goods: o.createPersonName || '',
				ckd_create_time_good: o.created || '',
				ckd_notes_goods: o.memo || '',
				ckd_recipient_goods: o.addresseeName || '',
				ckd_contact_details_: o.mobile || '',
				ckd_no_txm_with_code: o.outboundNumber || '',
				ckd_no_ewm: o.outboundNumber || '',
				ckd_addrall: addressInfo,
				tableDataArray
			}
			pList.push(itemData)
		} else if (printType === 'thqd') {
			let tableDataArray = o.goodsInfo?.map(it => {
				return {
					tb_tit: it.title || '',
					tb_sjbm_b: it.outerId || '',
					tb_hpjc: it.sysItemAlias || '',
					tb_hpsjbm: it.sysSkuOuterId || '',
					tb_cbj: it.sysSkuCostPrice || '',
					tb_spyjje: (it.num || 0) * (it.sysSkuCostPrice || 0) || '',
					tb_img: it.picUrl || '',
					tb_cpgg: it.skuName || '', //  待定
					tb_hpggbm: it.sysSkuOuterId || '',
					tb_count: it.num || '',
					tb_weight: it.weight || '',
					tb_color: it.color || '',
					tb_size: it.tb_size | '',
					tb_market: it.market || '',
					tb_stall: it.stall || '',
					tb_supplier: it.supplierName || '',
					tb_spggbm: it.skuOuterId || '',
				}
			}) || []
			let itemData = {
				thqd_print_time: pTime,
				thqd_market: o.market || '',
				thqd_stall: o.stall || '',
				thqd_total_count: o.goodsNum || '',
				thqd_supplier: o.supplierName || '',
				thqd_total_amount: o.goodsPrice || '',
				thqd_print_num: o.printNum || '',
				tableDataArray,
				// 表格合计数据
				hj_thje: o.goodsPrice || '',
				hj_sl: o.goodsNum || ''
			}
			pList.push(itemData)
		} else if (printType === 'bic') {
			let tableDataArray = o.orders?.map(it => {
				return {
					tb_tit: `${it.title || ''}  ${it.skuPropertiesName || ''}`,
					tb_count: it.num || '',
				}
			}) || []

			let itemData = {
				bic_txm_with_code: o.orderCode || '',
				shop_name: o.sellerNick || '',
				tableDataArray,
			}
			pList.push(itemData)
		} else if (printType === 'zbxp') {
			// 获取商品设置的排序顺序
			const goodsInfoSorted = printStyle?.extJson?.goodsInfoSorted || '';
			const sortArr = goodsInfoSorted.split(',');
			let goodsInfo = ''
			let tableDataArray = o.tradeItemInfoList?.map(it => {
				sortArr.forEach((o, i) => {
					if (i < sortArr.length - 1) {
						if (it[o]) goodsInfo += it[o] + ' '
					} else {
						goodsInfo += (it[o] || '') + '\n'
					}
				})
				return {
					tb_tit: it.title || '',
					tb_sjbm: it.outerId || '',
					tb_spmc: it.itemTitle || '',
					tb_hpjc: it.sysItemAlias || '',
					tb_ggmc: it.skuName || '',
					tb_ggbm: it.skuOuterId || '',
					tb_hpggbm: it.sysSkuAlias || '',
					tb_bdhp_sjbm: it.sysOuterSkuId || '', //  待定
					tb_goodsNum: it.num || ''
				}
			}) || []
			// 店铺标识 :shopFlag
			// 系统单号  tid
			// 平台单号 ptTid
			// 付款时间 payTime
			// 买家留言 buyerMessage
			// 买家备注 sellerMemo
			// 店铺简称 sellerAbbreviation
			// 商品实付金额 payment
			let itemData = {
				zb_shop_sign: o.shopFlag,// "店铺标识",
				zb_tid: o.tid,//"系统单号",
				zb_pt_tid: o.ptTid,//"平台单号",
				zb_pay_date: o.payTime,//"付款时间",
				zb_buyer_message: o.buyerMessage,//"买家留言",
				zb_saler_remark: o.sellerMemo,//"卖家备注",
				zb_shop_abbreviation: o.sellerAbbreviation,//"店铺简称",
				zb_reciever_name: o.receiverNameMask,//"收件人",
				zb_reciever_mobile: o.receiverMobileMask || o.receiverPhoneMask || '',//"收件人手机号",
				zb_tel: o.receiverMobileMask,
				zb_amount_for_goods: o.payment,//"商品实付金额",
				zb_txm_pt_tid: o.ptTid,
				zb_p: o.receiverProvince,//"收件省",
				zb_city: o.receiverCity,//"收件市",
				zb_q: o.receiverCounty,//"收件区",
				zb_addr: o.receiverTown,//"收件街道",
				zb_addrall: o.receiverAddressMask,//"收件地址",
				zb_ww: o.buyerNick,//"买家旺旺",
				zb_zip: o.receiverZip,//"收件邮编"
				zb_tag_number: o.livePrintNum,
				zb_daren_name: (o.authorNameList || []).join(','),
				zb_daren_id: (o.authorIdList || []).join(','),
				dy_nick: o.base64Url,//"收件邮编"
				tableDataArray,
				// 表格合计数据
				hj_thje: o.totalFee || '',
				hj_sl: o.totalNum || '',
				shop_info: goodsInfo,
				shop_count: o.totalNum
			}
			pList.push(itemData)
		} else if (printType === 'bhbq') {
			// bhbq_product_jc = "货品简称";
			// bhbq_goods_name = "货品规格名称";
			// bhbq_goods_code = "货品规格编码";
			// bhbq_specs_alias = "货品别名";
			// bhbq_tm = "条形码";
			// bhbq_goods_no = "货号";
			// bhbq_position = "货位";
			// bhbq_market = "市场";
			// bhbq_store = "档口";
			// bhbq_supplier = "供应商";
			// bhbq_goods_number = "货品数量";
			// bhbq_specs_number = "规格数量";
			// bhbq_print_num = "打印序号";
			// bhbq_print_time = "打印时间"
			let itemData = {
				bhbq_product_jc: o.sysItemAlias,
				bhbq_goods_name: o.sysSkuName,
				bhbq_goods_code: o.sysSkuOuterId,
				bhbq_specs_alias: o.skuAlias,
				bhbq_tm: o.barCode,
				bhbq_goods_no: o.itemNo,
				bhbq_position: o.warehouseSlotName,
				bhbq_market: o.market,
				bhbq_store: o.stall,
				bhbq_supplier: o.supplierName,
				// bhbq_goods_number: o.count,
				bhbq_specs_number: o.count,
				bhbq_print_num: o.printNum,
				bhbq_print_time: pTime
			}
			pList.push(itemData)

		} else {
			if (scanPrintSetting?.printLabelWay == 1 && o[printDividerObjType] != curStore && isGoodSmallTag) {

				if (Object.keys(scanPrintSetting?.scanSet?.stallContentConfig || {}).length) {
					let stallContentConfig = scanPrintSetting?.scanSet?.stallContentConfig || {};
					let content = []
					stallContentConfig?.values.map(it => {
						it === 'custom' ? content.push(stallContentConfig.customContent) : content.push(o[it])
					})
					pList.push({
						isPrintDivider: true,
						dividerStr: content.join('-') || o[printDividerObjType] || '未设置档口',
					})
				} else {
					pList.push({
						isPrintDivider: true,
						dividerStr: o[printDividerObjType] || '未设置档口',
					})
				}
			}
			curStore = o[printDividerObjType]
			// "print_time": "打印时间",
			// "pdate": "生成时间",
			// "is_multi": "单件/多件",
			// "fj_number": "唯一码",
			// "goods_jc": "货品简称",
			// "sku_name": "规格名称",
			// "goods_code": "货品规格编码",
			// "goods_count": "总数量",
			// "supplier": "供应商",
			// "fj_txm_number": "唯一码条码",
			// "shop_sign": "店铺标识",
			// "stock_sign": "屯货标识",
			// "water_number": "流水号",
			// "fjm_code": "分拣码",
			// "s_tid": "订单编号",
			// "gg_content": "平台规格编码",
			// "goods_content": "平台商家编码",
			// "fj_ewm_number": "唯一码二维码",
			// "urgent_tag": "加急标识",
			// "tag_type": "标签类型",
			// "specs_alias": "规格别名",
			// "market": "市场",
			// "store": "档口"
			// "buyerMessage" : "买家留言"
			// "sellerMemo" : "卖家备注"
			// "nh_syssku_name": "货品规格名称"
			let _m = {
				print_time: pTime,   //打印时间
				pdate: o.gmtCreated,
				is_multi: o.packageGoodsNum > 1 ? '多' : '单',
				fj_number: o.id,
				goods_jc: o.sysItemAlias,
				sku_name: o.skuName,
				goods_code: o.sysSkuOuterId,
				goods_count: o.packageGoodsNum,
				supplier: o.supplierName,
				fj_txm_number: o.id,
				nh_txm_with_code: o.id,
				shop_sign: o.shopFlag || '',
				stock_sign: o.isStockPile ? '屯' : '',
				water_number: startSerialNo,
				fjm_code: o.sortCode || '',
				s_tid: o.tid || o.orderId,
				gg_content: o.skuOuterId,
				goods_content: o.outerId,
				fj_ewm_number: o.id,
				urgent_tag: o.faster ? `缺货${o.faster}天` : '',
				tag_type: o.labelType === 'TAKE' ? '拿货' : '拣货',
				specs_alias: o.sysSkuAlias,
				market: o.market,
				store: o.stall,
				labelStatus: o.labelStatus,
				s_message: o.buyerMessage || '',
				f_memo: o.sellerMemo || '',
				yd_no: o.ydNo,
				company_name: o.exName,
				shop_abbreviation: o.sellerAbbreviation || '',
				platform_goods_name: o.title || '',
				goods_no: o.sysItemNo || '',
				pay_days: o.payDate || '',
				fdate: o.payTime || '',
				cost_price: o.sysSkuCostPrice || '',
				nh_goods_size: o.size || '',
				nh_goods_color: o.color || '',
				nh_reciever_name: o.receiverNameMask || '',
				nh_reciever_mobile: o.receiverMobileMask || o.receiverPhoneMask || '',
				nh_tel: o.receiverMobileMask,
				nh_p: o.receiverProvince,
				nh_city: o.receiverCity,
				nh_q: o.receiverCounty,
				nh_addr: o.receiverTown,
				nh_addrall: o.receiverAddressMask,
				nh_ww: o.buyerNick,
				nh_zip: o.receiverZip,
				nh_sku_name: o.skuName,
				nh_syssku_name: o.sysSkuName,
				nh_dp_position: o.warehouseSlotName,
				// 货品规格条形码
				goods_specs_code: o.barCode || '',
				nh_specs_ewm_number: o.barCode || '',
				nh_specs_txm_number: o.barCode || '',
				// 货品规格编码条形码
				nh_cont_txm_number: o.sysSkuOuterId || '',
				nh_cont_ewm_number: o.sysSkuOuterId || '',
				//货号规格货号
				nh_no_txm_number: o.sysItemNo || '',
				nh_no_ewm_number: o.sysItemNo || '',
				amount_for_goods: o.payment || '',
				pt_order_id: o.ptTid || '',

			}
			if (printType === 'thd') {
				_m = {
					..._m,
					// 退货小标签
					title: o.title,
					txm_tid: o.tid || '',
					o_shop: o.sellerNick,
					product_jc: o.sysItemAlias,
					gg_content: o.outerSkuId || '',
					th_sku_name: o.skuName,
					seller_content: o.outerId || '',
					th_goods_code: o.sysOuterSkuId || o.sysSkuOuterId || '',
					th_goods_name: o.sysSkuName || '',
					th_number_txm: o.refundLabelId || '',
					th_number: o.refundLabelId || '',
					th_cost_price: o.costPrice || '',
					th_aftermarket_no: o.refundId || '',
					th_offline_emarks: o.localContent || '',
					th_buyers_message: o.buyerMessage || '',
					th_sellers_notes: o.sellerMemo || '',
					th_dp_position: o.warehouseSlotName || '',
				}
			}
			if (printType === 'bkd') {
				// bk_print_time = '打印时间';
				// bk_pdate = '生成时间';
				// bk_water_number = '流水号';
				// bk_txm = '爆款条形码';
				// bk_m = '爆款码';
				// bk_ewm = '爆款二维码';
				// bk_goods_count = '商品总数量';
				// bk_goods_title = '商品标题';
				// bk_seller_content = '商家编码';
				// bk_product_jc = '货品简称';
				// bk_product_code = '货品编码';
				// bk_sku_name = '规格名称';
				// bk_gg_content = '规格编码';
				// bk_goods_color = '商品颜色';
				// bk_goods_size = '商品尺码';
				// bk_goods_code = '货品规格编码';
				// bk_market = '市场';
				// bk_store = '档口';
				// bk_supplier = '供应商';

				_m = {
					bk_print_time: pTime,
					bk_pdate: o.gmtCreated,
					bk_water_number: startSerialNo,
					bk_m: o.hotSellId,
					bk_txm: o.hotSellId,
					bk_ewm: o.hotSellId,
					bk_goods_count: o.theSameHotSellIdItemNum,
					bk_txm_with_code: o.hotSellId,
					bk_goods_title: o.title,
					bk_seller_content: o.outerId,
					bk_product_jc: o.sysItemAlias,
					bk_product_code: o.sysOuterId,
					bk_sku_name: o.skuName,
					bk_gg_content: o.skuOuterId,
					bk_goods_color: o.color || '',
					bk_goods_size: o.size || '',
					bk_goods_code: o.sysSkuOuterId,
					bk_market: o.market,
					bk_store: o.stall,
					bk_supplier: o.supplierName,
					bk_shop_abbreviation: o.sellerAbbreviation,
					bk_specs_alias: o.sysSkuAlias,
					bk_goods_no: o.itemNo,
					bk_goods_size: o.size || '',
					bk_goods_color: o.color || '',
					bk_dp_position: o.warehouseSlotName,
				}
			}
			if (printType === 'dpd' || printType === 'tmd') {
				// 货品品牌名   brandName
				// 货品分类  classifyText （根据classifyId传过去对应货品分类名称）
				// 货品市场 market
				// 货品档口  stall
				// 货品简称 sysItemAlias
				// 货品编码   outerId
				// 货品规格 sysSkuName
				// 货品规格别名  sysSkuAlias
				// 货品规格编码 skuOuterId
				// 货号   itemNo
				// 条形码  barCode
				// 重量  weight
				// 售价  price
				// 吊牌价  tagPrice
				// 成本价 costPrice
				// 供应商 supplierName
				// 供应商地址  supplierAddress(我加的)
				// 组合/单品  isCombination
				// 货品条形码 sysBarCode
				// 货号条形码 itemNoBarCode
				// 货品规格编码条形码 sysSkuBarCode
				let customDataItem = getCustomDataItem(o.customAttributesList)
				if (isPrintSubGoods) {
					const groupCombinationList = o.groupCombinationList || []
					groupCombinationList.forEach(item => {
						_m = {
							dp_brand_name: item.brandName,
							dp_goods_classify: item.classifyText,
							dp_goods_market: item.market,
							dp_goods_store: item.stall,
							dp_product_jc: item.sysItemAlias,
							dp_product_code: item.outerId,
							dp_goods_sku: item.sysSkuName,
							dp_specs_alias: item.sysSkuAlias || item.skuAlias,
							dp_goods_code: item.skuOuterId,
							dp_goods_no: item.itemNo,
							dp_code: item.barCode,
							dp_zhliang: item.weight,
							dp_selling_price: item.price,
							dp_tag_price: item.tagPrice,
							dp_cost_price: item.costPrice,
							dp_supplier: item.supplierName,
							dp_supplier_address: item.supplierAddress,
							dp_is_multi: '单品',
							dp_hp_txm_with_code: item.sysBarCode,
							dp_no_txm_with_code: item.itemNoBarCode,
							dp_sku_txm_with_code: item.sysSkuBarCode,
							dp_no_ewm: item.itemNoBarCode,
							dp_code_ewm: item.sysSkuBarCode,
							dp_printid: item.printNum,
							dp_print_time: pTime,
							dp_goods_size: item.sysSize,
							dp_goods_color: item.sysColor,
							dp_position: item.warehouseSlotName,
							...customDataItem
						}
						for (let i = 0; i < item.num; i++) {
							pList.push(_m)
						}
					})
				} else {
					_m = {
					dp_brand_name: o.brandName,
					dp_goods_classify: o.classifyText,
					dp_goods_market: o.market,
					dp_goods_store: o.stall,
					dp_product_jc: o.sysItemAlias,
					dp_product_code: o.outerId,
					dp_goods_sku: o.sysSkuName,
					dp_specs_alias: o.sysSkuAlias || o.skuAlias,
					dp_goods_code: o.skuOuterId,
					dp_goods_no: o.itemNo,
					dp_code: o.barCode,
					dp_zhliang: o.weight,
					dp_selling_price: o.price,
					dp_tag_price: o.tagPrice,
					dp_cost_price: o.costPrice,
					dp_supplier: o.supplierName,
					dp_supplier_address: o.supplierAddress,
					dp_is_multi: o.isCombination ? '组合' : '单品',
					dp_hp_txm_with_code: o.sysBarCode,
					dp_no_txm_with_code: o.itemNoBarCode,
					dp_sku_txm_with_code: o.sysSkuBarCode,
					dp_no_ewm: o.itemNoBarCode,
					dp_code_ewm: o.sysSkuBarCode,
					dp_printid: o.printNum,
					dp_print_time: pTime,
					dp_goods_size: o.sysSize,
					dp_goods_color: o.sysColor,
					dp_position: o.warehouseSlotName,
					...customDataItem
				}
				}

				if (dpdPrintNumType == 1) {
					let keyObj = {
						sdck: 'count',
						cgrk: 'itemCount',
						dhrk: 'instockCount',
					}
					for (let i = 0; i < (o[keyObj[source]] || 0); i++) {
						pList.push(_m)
					}
				}
			} else if (printType === 'wdpd' || printType === 'wtmd') {
				//零库存版本
				// 供应商 supplierName
				// 商品市场 stall
				// 商品档口 market
				// 商品简称  sysItemAlias
				// 商家编码  outerId
				// 规格编码  skuOuterId
				// 规格名称 skuName
				// 规格别名  sysSkuAlias
				// 重量 weight
				// 吊牌价  tagPrice
				// 成本价 costPrice
				// 商品标题 title
				// 对应平台 platform
				// 对应店铺 sellerNick
				// 商品编码条形码 outerId
				// 规格编码条形码 skuOuterId
				// 商品编码二维码
				// 规格编码二维码

				let customDataItem = getCustomDataItem(o.customAttributesList)

				_m = {
					dp_merchandise_marke: o.market,//商品市场,
					dp_merchandise_store: o.stall,//商品档口,
					dp_abbreviation_jc: o.sysItemAlias,//商品简称,
					dp_merchant_code: o.outerId,//商家编码,
					dp_form_name_sku: o.skuName,//规格名称,
					dp_specs_alias: o.sysSkuAlias || o.skuAlias,//规格别名,
					dp_goods_code: o.skuOuterId,//规格编码,
					dp_zhliang: o.weight,//重量,
					dp_tag_price: o.tagPrice,//吊牌价,
					dp_cost_price: o.costPrice,//成本价,
					dp_title: o.title,//商品标题,
					dp_platform: o.platform,//对应平台,
					dp_shop: o.sellerNick,//对应店铺,
					dp_bm_txm_with_code: o.outerId,//商品编码条形码,
					dp_sku_txm_with_code: o.skuOuterId,//规格编码条形码,
					dp_bm_ewm: o.outerId,//商品编码二维码,
					dp_code_ewm: o.skuOuterId,//规格编码二维码,
					dp_printid: o.printNum,//打印序号,
					dp_print_time: pTime,//打印时间
					dp_position: o.warehouseSlotName,
					...customDataItem
				}
			}
			if (printType === 'zbd') {
				// 获取商品设置的排序顺序
				const goodsInfoSorted = printStyle?.extJson?.goodsInfoSorted || '';
				const sortArr = goodsInfoSorted.split(',');
				let goodsInfo = ''
				o.tradeItemInfoList?.forEach(it => {
					sortArr.forEach((o, i) => {
						if (i < sortArr.length - 1) {
							if (it[o]) goodsInfo += it[o] + ' '
						} else {
							goodsInfo += (it[o] || '') + '\n'
						}
					})
				})
				// 店铺标识 :shopFlag
				// 系统单号  tid
				// 平台单号 ptTid
				// 付款时间 payTime
				// 买家留言 buyerMessage
				// 买家备注 sellerMemo
				// 店铺简称 sellerAbbreviation
				// 商品实付金额 payment
				_m = {
					..._m,
					zb_shop_sign: o.shopFlag,// "店铺标识",
					zb_tid: o.tid,//"系统单号",
					zb_pt_tid: o.ptTid,//"平台单号",
					zb_pay_date: o.payTime,//"付款时间",
					zb_buyer_message: o.buyerMessage,//"买家留言",
					zb_saler_remark: o.sellerMemo,//"卖家备注",
					zb_shop_abbreviation: o.sellerAbbreviation,//"店铺简称",
					zb_reciever_name: o.receiverNameMask,//"收件人",
					zb_reciever_mobile: o.receiverMobileMask || o.receiverPhoneMask || '',//"收件人手机号",
					zb_tel: o.receiverMobileMask,
					zb_amount_for_goods: o.payment,//"商品实付金额",
					zb_txm_pt_tid: o.ptTid,
					zb_p: o.receiverProvince,//"收件省",
					zb_city: o.receiverCity,//"收件市",
					zb_q: o.receiverCounty,//"收件区",
					zb_addr: o.receiverTown,//"收件街道",
					zb_addrall: o.receiverAddressMask,//"收件地址",
					zb_ww: o.buyerNick,//"买家旺旺",
					zb_zip: o.receiverZip,//"收件邮编"
					dy_nick: o.base64Url,//"收件邮编"
					zb_tag_number: o.livePrintNum,
					zb_daren_name: (o.authorNameList || []).join(','),
					zb_daren_id: (o.authorIdList || []).join(','),
					shop_info: goodsInfo,
					shop_count: o.totalNum,
					zb_virtual_sign: o.isFakerTrade ? (o.fakerTradeFlag || '■') : '',
					zb_blacklist_id: o.isBlackList ? '黑' : '',
					zb_first_order_id: o.isNewCustomer ? '新' : '',
					zb_rep_order_sign: o.isRepeatTrade ? '★' : '',

				}
			}
			startSerialNo++
			if (dpdPrintNumType != '1' && !isPrintSubGoods) pList.push(_m)
		}
	})
	// console.log(pList);
	return pList

}
function getFormatConcatDpdPrintData({ orderList, printType, xbqPrintSet }) {
	let total = orderList.length,
		pList = [],
		pTime = new Date().format('MM-dd hh:mm:ss'),
		_m = null,
		printTagNumSwitch = xbqPrintSet.printTagNumSwitch == 1,
		// 是否打印赠品 isGift
		printTagGift = xbqPrintSet?.printTagGift === '2'
	orderList.forEach(o => {
		let printOrders = [...o.ordersArr]
		if (printTagGift) printOrders = printOrders.filter(it => (!it.isGift || it.splitTradeType === 'GIFT'))
		printOrders.forEach(order => {

			if (printItem.isZeroStockVersion()) {
				let customDataItem = getCustomDataItem(order.customAttributesList)

				//零库存版本
				// 供应商 supplierName
				// 商品市场 stall
				// 商品档口 market
				// 商品简称  sysItemAlias
				// 商家编码  outerId
				// 规格编码  skuOuterId
				// 规格名称 skuName
				// 规格别名  sysSkuAlias
				// 重量 weight
				// 吊牌价  tagPrice
				// 成本价 costPrice
				// 商品标题 title
				// 对应平台 platform
				// 对应店铺 sellerNick
				// 商品编码条形码 outerId
				// 规格编码条形码 skuOuterId
				// 商品编码二维码
				// 规格编码二维码
				_m = {
					dp_merchandise_marke: order.market,//商品市场,
					dp_merchandise_store: order.stall,//商品档口,
					dp_abbreviation_jc: order.titleShort,//商品简称,
					dp_merchant_code: order.outerId,//商家编码,
					dp_form_name_sku: order.skuName,//规格名称,
					dp_specs_alias: order.sysSkuAlias || order.skuAlias,//规格别名,
					dp_goods_code: order.outerSkuId,//规格编码,
					dp_zhliang: order.weight,//重量,
					dp_tag_price: order.tagPrice,//吊牌价,
					dp_cost_price: order.costPrice,//成本价,
					dp_title: order.title,//商品标题,
					dp_platform: order.platform,//对应平台,
					dp_shop: order.sellerNick,//对应店铺,
					dp_bm_txm_with_code: order.outerId,//商品编码条形码,
					dp_sku_txm_with_code: order.outerSkuId,//规格编码条形码,
					dp_bm_ewm: order.outerId,//商品编码二维码,
					dp_code_ewm: order.outerSkuId,//规格编码二维码,
					dp_printid: order.printNum,//打印序号,
					dp_print_time: pTime,//打印时间
					dp_goods_size: order.sysSize,
					dp_goods_color: order.sysColor,
					dp_position: order.warehouseSlotName,
					...customDataItem
				}
				if (!printTagNumSwitch) {
					let printGoodsNum = Number(order.num)
					for (let i = 0; i < printGoodsNum; i++) {
						pList.push(_m)
					}
				} else {
					pList.push(_m)
				}
			} else {
				// 货品品牌名   brandName
				// 货品分类  classifyText （根据classifyId传过去对应货品分类名称）
				// 货品市场 market
				// 货品档口  stall
				// 货品简称 sysItemAlias
				// 货品编码   outerId
				// 货品规格 sysSkuName
				// 货品规格别名  sysSkuAlias
				// 货品规格编码 sysOuterSkuId
				// 货号   itemNo
				// 条形码  barCode
				// 重量  weight
				// 售价  price
				// 吊牌价  tagPrice
				// 成本价 costPrice
				// 供应商 supplierName
				// 供应商地址  supplierAddress(我加的)
				// 组合/单品  isCombination
				// 货品条形码 sysBarCode
				// 货号条形码 itemNoBarCode
				// 货品规格编码条形码 sysSkuBarCode
				if (order?.groupRelationRecordList.length) {
					order.groupRelationRecordList.forEach(item => {
						let customDataItem = getCustomDataItem(item.customAttributesList)
						_m = {
							dp_brand_name: item.brandName,
							dp_goods_classify: item.classifyName,
							dp_goods_market: item.market,
							dp_goods_store: item.stall,
							dp_product_jc: item.sysItemAlias,
							dp_product_code: item.outerId,
							dp_goods_sku: item.sysSkuName,
							dp_specs_alias: item.sysSkuAlias || item.skuAlias,
							dp_goods_code: item.skuOuterId,
							dp_goods_no: item.itemNo,
							dp_code: item.barCode,
							dp_zhliang: item.weight,
							dp_selling_price: item.price,
							dp_tag_price: item.tagPrice,
							dp_cost_price: item.costPrice,
							dp_supplier: item.supplierName,
							dp_supplier_address: item.storageAddr,
							dp_is_multi: item.isCombination ? '组合' : '单品',
							dp_hp_txm_with_code: item.barCode || '',
							dp_no_txm_with_code: item.itemNo || '',
							dp_sku_txm_with_code: item.skuOuterId || '',
							dp_no_ewm: item.itemNo || '',
							dp_code_ewm: item.skuOuterId || '',
							dp_printid: item.printNum || '',
							dp_print_time: pTime,
							dp_goods_size: item.sysSize,
							dp_goods_color: item.sysColor,
							dp_position: item.warehouseSlotName,
							...customDataItem
						}
						// 子货品打印数量= 比例*组合货品数量
						if (!printTagNumSwitch) {
							let printGoodsNum = Number(order.num) * Number(item.groupProportionNum)
							for (let i = 0; i < printGoodsNum; i++) {
								pList.push(_m)
							}
						} else {
							pList.push(_m)
						}
					})
				} else {
					let customDataItem = getCustomDataItem(order.customAttributesList)
					_m = {
						dp_brand_name: order.brandName,
						dp_goods_classify: order.classifyName,
						dp_goods_market: order.market,
						dp_goods_store: order.stall,
						dp_product_jc: order.sysItemAlias,
						dp_product_code: order.sysOuterId,
						dp_goods_sku: order.sysSkuName,
						dp_specs_alias: order.sysSkuAlias || order.skuAlias,
						dp_goods_code: order.sysOuterSkuId,
						dp_goods_no: order.itemNo,
						dp_code: order.barCode || '',
						dp_zhliang: order.weight,
						dp_selling_price: order.price,
						dp_tag_price: order.tagPrice,
						dp_cost_price: order.costPrice || '',
						dp_supplier: order.supplierName,
						dp_supplier_address: order.storageAddr,
						dp_is_multi: order.isCombination ? '组合' : '单品',
						dp_hp_txm_with_code: order.barCode || '',
						dp_no_txm_with_code: order.itemNo || '',
						dp_sku_txm_with_code: order.sysOuterSkuId || '',
						dp_no_ewm: order.itemNo || '',
						dp_code_ewm: order.sysOuterSkuId || '',
						dp_printid: order.printNum || '',
						dp_print_time: pTime,
						dp_goods_size: order.sysSize,
						dp_goods_color: order.sysColor,
						dp_position: order.warehouseSlotName,
						...customDataItem
					}
					if (!printTagNumSwitch) {
						let printGoodsNum = Number(order.num)
						for (let i = 0; i < printGoodsNum; i++) {
							pList.push(_m)
						}
					} else {
						pList.push(_m)
					}
				}


			}
		})

	})
	return pList

}
// 根据sku获取宝贝颜色和尺码 这一块的逻辑有问题
function _getColorAndSize(sku) {
	let obj = {
		skuValue: [],
	},
		skuArr;
	if (sku) {
		if (sku.indexOf(';') > -1) {
			skuArr = sku.split(';');
		} else if (sku.indexOf('，') > -1) {
			skuArr = sku.split('，');
		} else {
			skuArr = sku.split(';');
		}
	}
	skuArr && $.each(skuArr, function () {
		const _skuValue = this.split(':')[1] || this.split('：')[1] || '';
		if (this.indexOf('颜色') > -1) {
			obj.goods_color = _skuValue;
		}
		if (/尺寸|参考身高/.test(this) || /码/g.test(this)) {
			obj.goods_size = _skuValue;
		}
		_skuValue && obj.skuValue.push(_skuValue);
	});

	return obj;
}

function _sortPrintLabel(list, scanSetting) {
	//针对一个档口排序
	// const rules = this.getCtrlData('rules') || {};
	const rules = scanSetting || {};
	const {
		labelPrintingSort,
	} = rules;
	console.log('=====排序前===');
	console.log({ rules });
	list.map((item) => {
		console.log(item.sku_alias || item.goods_jc, item.fdate);
	});
	let sortVal1, sortVal2;
	if (labelPrintingSort == 1) {
		list.sort((item1, item2) => {
			if (item1.is_multi === '多' && item2.is_multi === '单') {
				return -1;
			} else {
				return 1;
			}
		});
	} else if (labelPrintingSort == 2) {
		list.sort((item1, item2) => {
			if (item1.stock_sign === '屯' && item2.stock_sign === '') {
				return -1;
			} else {
				return 1;
			}
		});
	} else if (labelPrintingSort == 3 || labelPrintingSort == 5) {  // 商品规格(goods_sku)||商品编码排序(goods_content)
		list.sort((item1, item2) => {
			sortVal1 = item1.gg_content || item1.goods_content || '';
			sortVal2 = item2.gg_content || item2.goods_content || '';
			if (sortVal1 > sortVal2) {
				return 1;
			} else if (labelPrintingSort == 5 && sortVal1 == sortVal2 && item1.fdate > item2.fdate) {
				return 1;
			} else {
				return -1;
			}
		});
	} else if (labelPrintingSort == 4 || labelPrintingSort == 6) { // 商品规格别名(sku_alias)||商品简称排序
		list.sort((item1, item2) => {
			sortVal1 = item1.sku_alias || item1.goods_jc || '';
			sortVal2 = item2.sku_alias || item2.goods_jc || '';
			if (sortVal1 > sortVal2) {
				return 1;
			} else if (labelPrintingSort == 6 && sortVal1 == sortVal2 && item1.fdate > item2.fdate) {
				return 1;
			} else {
				return -1;
			}
		});
	} else if (labelPrintingSort == 7) { // 同订单排一起
		list.sort((item1, item2) => {
			if (item1.s_tid > item2.s_tid) {
				return 1;
			} else {
				return -1;
			}
		});
	}
	console.log('=====排序后===');
	list.map((item) => {
		console.log(item.sku_alias || item.goods_jc, item.fdate);
	});
	return list;
}

function _setSerial(list, startSerialNo) {
	list.map(item => {
		!item.isPrintDivider && (item.water_number = startSerialNo++);
	});
	return list;
}

async function _getDecryPList(decryArr, pList) {
	// 获取解密的收件人信息
	const data = await this.getCtrl('decryData').getMethod('getDecryDataAsync', decryArr);
	pList.map((item, index) => {
		item.s_name = data[index].decryReceiver;
	});
	return pList;
}


/**
  * 获取支持虚拟号功能的收件人地址
  * @param {string} address 收件人地址
  * @param {string} mobile  收件人手机号
  * @param {*} templateInfo  模板信息
  * @param {string & undefined} isVirtualTel 是否是虚拟号  'true' || 'false' || undefined
  * @returns {string} receiverAddress  兼容虚拟号提示的收件人地址
  */
function getVirtualMobileAddress(address, mobile, templateInfo = {}, isVirtualTel) {
	let receiverAddress = address || '';
	const isVirtualMobile = String(isVirtualTel) === 'true' && /^1\d{10}-\d{4}$/.test(mobile);     // 是否是虚拟号
	// 网点电子面单虚拟号后面需拼接配送提示
	if (isVirtualMobile && String(templateInfo.KddType) === '2') {
		const virtualMobileArr = mobile.split('-');
		receiverAddress = address + ` [配送请拨打${virtualMobileArr[0]}转${virtualMobileArr[1]}]`;
	}
	return receiverAddress;
}

export {
	getFormatPrintData,
	formatPrintData,
	getFormatXbqPrintData,
	getVirtualMobileAddress,
	getFormatBhdXbqPrintData,
	getFormatConcatDpdPrintData
};
