import { makeAutoObservable, toJS } from "mobx";
import React, { ReactNode } from "react";
import _, { isString } from "lodash";
import dayjs from "dayjs";
import s from '@/pages/Trade/ScanPrint/index.module.scss';
import { ItemTakeGoodsLabelConfigSelectResponse, ItemTakeGoodsLabelRuleConfigSelectResponse, ItemTakeGoodsLabelSelectTradeInfoByLabelIdResponse, NhbInfoObj } from "@/types/trade/takeGoodsLabel";
import { avoidRepeatReq } from "@/utils/util";
import {
	IndexGetLabelPushPlatformAuthInfoApi,
	ItemTakeGoodsLabelConfigEditApi,
	ItemTakeGoodsLabelConfigSelectApi,
	ItemTakeGoodsLabelRuleConfigSelectApi,
	ItemTakeGoodsLabelSelectTradeInfoByLabelIdApi
} from "@/apis/trade/takeGoodsLabel";
import { ItemItemUserConfigQueryConfigApi, ItemItemUserConfigUpdateConfigApi } from "@/apis/trade/search";
import { IPackage, IScanPrintFormSetting } from "@/pages/Trade/interface";
import { defaultScanConfig, defaultScanPrintShowListSet, salePropertyEnum, SET_CURRENT_SCAN_DATA_INFO, UPDATE_FORM_VALUES, VIEW_SIZE, ViewSize, scanPrint_default_column_config, scanPrint_column_config_key, PREVIEW_SIZE, PreviewSize } from "@/pages/Trade/ScanPrint/constants";
import voice多件未匹配到订单 from '@/assets/mp3/多件-未匹配到订单.mp3';
import voice单件未匹配到订单 from '@/assets/mp3/单件-未匹配到订单.mp3';
import voice重复扫描 from '@/assets/mp3/重复扫描.mp3';
import voice异常 from '@/assets/mp3/有异常.mp3';
import voice商品已备齐 from '@/assets/mp3/商品已备齐.mp3';
import voice合单 from '@/assets/mp3/合单.mp3';
import voice失败 from '@/assets/mp3/失败.mp3';
import voice扫描成功 from '@/assets/mp3/扫描.mp3';
import voiceSuccess from '@/assets/mp3/成功.mp3';
import { playNumberAudio, focusLabelInput, playAudio, scanPrintPlayAudio, textTransformAudioPlay } from "@/pages/Trade/ScanPrint/utils";
import { tradeStore } from "@/stores";
import userStore from "@/stores/user";
import { local } from "@/libs/db";
import { PLAT_HAND, PLAT_OTHER, PLAT_SPH, TEMPLATE_ZMJ } from "@/constants";
import event from '@/libs/event';
import { getPlatAndShops, getShopsByPlat } from "@/components-biz/ShopListSelect/shopListUtils";
import { platform } from "@/types/schemas/common";
import { templateType4PlatformTypeEnum } from "@/pages/Trade/constants";
import { TradeQueryBgTradeForScanApi } from "@/apis/report/kddLog";
import timer, { logTypeEnum } from "@/libs/timer";
import { labelPushPlatformEnum } from "@/constants/labelPush";
import { handlePackageList } from "@/pages/Trade/utils";

export interface IScanPrintTempInfo {
	takeGoodsLabelInfoId: string;
	scanInfoMsg: string | ReactNode;
	currentScanOrderInfo: ItemTakeGoodsLabelSelectTradeInfoByLabelIdResponse["data"]["packageList"][number]["takeGoodsLabelInfos"][number];
	scannedGoodsCount: number;
	goodsTotalCount: number;
	scanInfoClassName?: string;
	mixScanModeMsg?: string; // 开启多件混扫模式后，扫描打印分拣码
}

export interface IPlatformAuthInfo {[labelPushPlatformEnum.拿货宝]?:NhbInfoObj, [labelPushPlatformEnum.四季星座网]?:NhbInfoObj}
// playAudio多件混扫分拣码提示临时兼容方案 等待几百毫秒 需要等分拣码播报完 再进行了其他语音提示
// 后续建议语音播报单纯抽离出来优化 队列形式播报
const DELAY_TIME = 1000;

class ScanPrintStore {
	constructor() {
		makeAutoObservable(this);
	}
	
	viewSize: string = local.get(VIEW_SIZE) || ViewSize.小; // 扫描打印左侧信息栏字体大小

	previewSize: string = local.get(PREVIEW_SIZE) || PreviewSize.中; // 扫描打印预览图大小

	setViewSize = (val:string) => {
		this.viewSize = val;
		local.set(VIEW_SIZE, val);
	}

    setPreviewSize = (val:string) => {
    	this.previewSize = val;
    	local.set(PREVIEW_SIZE, val);
    }

	scanPrintShowListSet: any[] = defaultScanPrintShowListSet; // 扫描打印-显示设置

	setScanPrintShowListSet = (val:any[]) => {
		this.scanPrintShowListSet = val || defaultScanPrintShowListSet;
	}

    isShowScanPrintSettingModal: boolean = false;

	setIsShowScanPrintSettingModal = (visible: boolean) => {
		this.isShowScanPrintSettingModal = visible;
	}

	forceNoPrint: boolean = false;

	setForceNoPrint = (v) => {
		this.forceNoPrint = v;
	}

	scanPrintSetting: ItemTakeGoodsLabelConfigSelectResponse["data"] = null;

	setScanPrintSetting = (setting: ItemTakeGoodsLabelConfigSelectResponse["data"]) => {
		if (setting?.customizeSort && typeof (setting?.customizeSort) === "string") {
			setting.customizeSort = JSON.parse(setting.customizeSort);
		}
		console.log('setting111', setting);
		this.scanPrintSetting = setting;
		// this.setVideoMonitorState(!!setting?.subUserScanSet?.videoMonitor);
	}

	getScanPrintSetting = async(refresh = false) => {
		if (this.scanPrintSetting && !refresh) {
			return this.scanPrintSetting;
		}
		const res:ItemTakeGoodsLabelConfigSelectResponse["data"] = await avoidRepeatReq(ItemTakeGoodsLabelConfigSelectApi, {});
		if (res?.scanSet && isString(res.scanSet)) {
			res.scanSet = { ...defaultScanConfig, ...JSON.parse(res.scanSet as string) }; // 新加的默认配置也放进去
		} else if (!res?.scanSet) {
			res.scanSet = defaultScanConfig;
		}
		this.setVideoMonitorState(!!res?.subUserScanSet?.videoMonitor);
		if (res?.scanSet && res.scanSet?.autoSend === undefined) {
			res.scanSet.autoSend = (local.get('scanPrintSearchSetting')?.autoSend ? 1 : 0) || 0;
			await ItemTakeGoodsLabelConfigEditApi(res);
		}

		// scanChangeExId 转换成快递匹配的选项
		if (res.scanSet?.scanChangeExId === 1 && res.scanSet?.scanChangeExIdForFirstSend === undefined && res.scanSet?.scanChangeExIdForGetYd === undefined) {
			res.scanSet.scanChangeExIdForFirstSend = 1; // 先发货订单自动切换为上传至平台的单号的模版
			res.scanSet.scanChangeExIdForGetYd = 1; // 已申请单号订单（未先发货）自动切换为最新申请的单号的模版
		}

		if (res?.customizeSort && isString(res.customizeSort)) {
			res.customizeSort = JSON.parse(res.customizeSort);
		}
	
		// 显示设置配置
		if (res?.scanPrintShowSet && isString(res.scanPrintShowSet)) {
			let data = JSON.parse(res.scanPrintShowSet as string);
			let { list = [], size = ViewSize.小 } = data;
			// 本地配置新增的加上
			const noItemList = defaultScanPrintShowListSet.filter((item) => (list.findIndex(_listItem => (_listItem.condition == item.condition)) < 0));
			if (noItemList.length > 0) {
				list.push(...noItemList);
			}
			// 本地配置没有的去掉
			list = list.filter(objB => {  
				return defaultScanPrintShowListSet.some(objA => objA.condition === objB.condition);  
			});  
			this.setViewSize(size);
			this.scanPrintShowListSet = list;
		} else if (!res?.scanPrintShowSet) {
			let newScanPrintShowSet = { list: defaultScanPrintShowListSet, size: local.get(VIEW_SIZE) || ViewSize.小 };
			// res.scanPrintShowSet = newScanPrintShowSet; // 用单独字段
			this.setViewSize(newScanPrintShowSet.size);// 默认字体大小
			this.scanPrintShowListSet = newScanPrintShowSet.list; // 显示设置字段
		}
		this.scanPrintSetting = res;
		return res;
	}

	sortCodeSetting: ItemTakeGoodsLabelRuleConfigSelectResponse["data"] = null;

	setSortCodeSetting = (setting: ItemTakeGoodsLabelRuleConfigSelectResponse["data"]) => {
		this.sortCodeSetting = setting;
	}

	getSortCodeSetting = async(refresh = false) => {
		if (this.sortCodeSetting && !refresh) {
			return this.sortCodeSetting;
		}
		const res = await avoidRepeatReq(ItemTakeGoodsLabelRuleConfigSelectApi, {});
		this.sortCodeSetting = res;
		return res;
	}

	// * 扫描打印临时发件人、打印机等设置
	scanPrintFormSetting: IScanPrintFormSetting = null;

	setScanPrintFormSetting = (val: IScanPrintFormSetting) => {
		const { kddTempList, isMergePrint } = tradeStore;
		const { senderSetting } = userStore;
		if (val.templateInfo) {
			val.template = kddTempList.find(item => (isMergePrint ? item.id : item.Mode_ListShowId) === val.templateInfo);
		} else {
			val.templateInfo = local.get('scanPrintSearchSetting')?.templateInfo;
		}
		if (val.sender) {
			val.senderInfo = senderSetting.list.find(item => item.id === val.sender);
		} else {
			val.sender = local.get('scanPrintSearchSetting')?.sender;
		}
		val = {
			...local.get('scanPrintSearchSetting'),
			...val,
		};

		this.scanPrintFormSetting = val;

		local.set('scanPrintSearchSetting', val);
	}

	// * 查询时的参数
	searchScanPrintFormSetting: IScanPrintFormSetting = null;

	setSearchScanPrintFormSetting = (val: IScanPrintFormSetting) => {
		this.searchScanPrintFormSetting = val;
	}

	// 本次扫描打印订单信息
	scanPrintOrderInfo: IPackage = {};

	forceScanPrepOrderIds = new Set();

	updateScanPrintOrderInfo = (oid:string, labelId:string) => {
		if (this.scanPrintOrderInfo[oid]) {
			this.scanPrintOrderInfo[oid].add(labelId);
		} else {
			this.scanPrintOrderInfo[oid] = new Set([labelId]);
		}
	}

	updateForceScanPrepOrderIds = (oid: string) => {
		this.forceScanPrepOrderIds.add(oid);
	}

	// * 扫描打印临时存储的信息
	scanPrintTempInfo: IScanPrintTempInfo = {
		takeGoodsLabelInfoId: '',
		scanInfoMsg: '',
		currentScanOrderInfo: null,
		scannedGoodsCount: 0,
		goodsTotalCount: 0,
		scanInfoClassName: "",
		mixScanModeMsg: "",
	}

	// * 扫描出来的订单信息
	takeGoodsLabelInfos: ItemTakeGoodsLabelSelectTradeInfoByLabelIdResponse["data"]["packageList"] = null;

	// * 扫描出来的订单详情
	packageInfoVos: ItemTakeGoodsLabelSelectTradeInfoByLabelIdResponse["data"]["packageInfoVos"] = null;

	setTakeGoodsLabelInfos = (val: ItemTakeGoodsLabelSelectTradeInfoByLabelIdResponse["data"]["packageList"]) => {
		this.takeGoodsLabelInfos = val;
	}

	// 只有一笔订单时 是否提示成功
	playVoiceSuccess:string = null;

	setPlayVoiceSuccess = (val:string) => {
		this.playVoiceSuccess = val;
	}

	checkErrorOrder = (res) => {
		let result = [];
		res.forEach(pack => {
			pack?.trades?.forEach(trade => {
				if (trade?.serviceTagList?.includes('platformStockOut')) result.push('平台缺货');
				if (trade?.serviceTagList?.includes('pOrderUnfinished')) result.push('拼团未完成');
				if (trade?.serviceTagList?.includes('risk')) result.push('风控');
			});
		});
		
		return Array.from(new Set(result));
	}

	updateTakeGoodsLabelInfo = async(res: ItemTakeGoodsLabelSelectTradeInfoByLabelIdResponse["data"]) => {
		// 重置扫描文案：
		this.scanPrintTempInfo.mixScanModeMsg = "";
		const { platformGiftNotInvolvedTakeGoodsLabel, platformGift } = await userStore.getSystemSetting();
		const allShopInfo = await getShopsByPlat({ hasHand: true });
		const shopObj = {};
		if (allShopInfo?.length > 0) {
			allShopInfo.forEach(shopInfo => {
				shopObj[shopInfo.sellerId] = shopInfo;
			});
		}
		const { scanNumSetting = [], id, shopValue, platValue } = this.searchScanPrintFormSetting;
		// * 平台、店铺校验
		// console.log(shopValue, platValue);
		const { platform, sellerId } = res?.packageList?.[0]?.takeGoodsLabelInfos?.[0] || {};
		if (platValue.includes(PLAT_HAND) && platform === PLAT_HAND) {
			// next
		} else if (shopValue.length) {
			let shops = shopValue.map(shop => shop.split('_')[1]);
			if (!shops.includes(sellerId)) {
				playAudio(voice失败);
				this.scanPrintTempInfo.scanInfoMsg = <span className={ s['scan-info-content-error'] }>平台店铺不一致：失败未找到订单</span>;

				window.errorCollection?.customMessageUpload({
					type: '扫描打印【店铺不一致：失败未找到订单】', 
					data: {
						searchScanPrintFormSetting: this.searchScanPrintFormSetting,
					}
				});
				return;
			}
		} else if (platValue.length) {
			if (!platValue.includes(platform)) {
				playAudio(voice失败);
				this.scanPrintTempInfo.scanInfoMsg = <span className={ s['scan-info-content-error'] }>平台店铺不一致：失败未找到订单</span>;

				window.errorCollection?.customMessageUpload({
					type: '扫描打印【平台不一致：失败未找到订单】', 
					data: {
						searchScanPrintFormSetting: this.searchScanPrintFormSetting,
					}
				});
				return;
			}
		}
		// 异常订单校验
		const errorOrder = this.checkErrorOrder(res?.packageInfoVos);
		if (errorOrder?.length) {
			this.scanPrintTempInfo.scanInfoMsg = <span className={ s['scan-info-content-error'] }>失败：存在{errorOrder?.[0]}订单</span>;
			window.errorCollection?.customMessageUpload({ type: `扫描打印【异常订单】-${errorOrder.join(',')}` });
			playAudio(voice异常);
			return; 
		}
		// 扫描设置 -- 扫描标签不自动跳过
		if (this.scanPrintSetting.scanSet?.scanedNoAutoSkip && this.scanPrintTempInfo.takeGoodsLabelInfoId && res?.packageId !== this.scanPrintTempInfo.takeGoodsLabelInfoId) {
			playAudio(voice失败);
			this.scanPrintTempInfo.scanInfoMsg = <span className={ s['scan-info-content-error'] }>失败：当前订单未找到匹配的宝贝信息</span>;

			window.errorCollection?.customMessageUpload({
				type: '扫描打印【扫描标签不自动跳过；失败：当前订单未找到匹配的宝贝信息】', 
				data: {
					searchScanPrintFormSetting: this.searchScanPrintFormSetting,
				}
			});
			return;
		}

		// * 扫描选项 单件多件判断

		this.scanPrintTempInfo.scanInfoMsg = '';
		let isSingleGoods = scanNumSetting.includes(1);
		let isMultipleGoods = scanNumSetting.includes(2);
		if (!isSingleGoods && isMultipleGoods && res.saleProperty === salePropertyEnum.单件) {
			playAudio(voice多件未匹配到订单);
			this.scanPrintTempInfo.scanInfoMsg = <span className={ s['scan-info-content-error'] }>多件、没匹配到订单</span>;

			window.errorCollection?.customMessageUpload({
				type: '扫描打印【多件、没匹配到订单】', 
				data: {
					searchScanPrintFormSetting: this.searchScanPrintFormSetting,
				}
			});
			return;
		} else if (isSingleGoods && !isMultipleGoods && res.saleProperty != salePropertyEnum.单件) {
			playAudio(voice单件未匹配到订单);
			this.scanPrintTempInfo.scanInfoMsg = <span className={ s['scan-info-content-error'] }>单件、没匹配到订单</span>;

			window.errorCollection?.customMessageUpload({
				type: '扫描打印【单件、没匹配到订单】', 
				data: {
					searchScanPrintFormSetting: this.searchScanPrintFormSetting,
				}
			});
			return;
		}

		// * 扫描设置 已发货商品自动隐藏/交易成功订单隐藏
		let setting = await this.getScanPrintSetting();
		const scanSuccessVoiceClose = setting?.scanSet?.scanSuccessVoiceClose;
		if (setting?.scanSet?.hideSuccessTrade) {
			res.packageList.forEach(item => {
				if (item.tradeStatus === 'TRADE_FINISHED') {
					item.isHide = true;
				}
			});
		}
		if (setting?.scanSet?.hideShipGoods) {
			res.packageList.forEach(pack => {
				pack.takeGoodsLabelInfos.forEach(goods => {
					// 已发货自动隐藏 需要去掉先发货的商品 先发货 firstSend
					if (goods.orderStatus === 'WAIT_BUYER_CONFIRM_GOODS' && !goods.firstSend) {
						goods.isHide = true;
					}
				});
			});
		}
		const getKddTempList = () => {
			const {
				kddTempList,
			} = tradeStore;
			const { scanPrintFormSetting } = scanPrintStore;
			const platValue = scanPrintFormSetting?.platValue;
			if (platValue.length && !platValue.includes(PLAT_HAND) && !platValue.includes(PLAT_SPH) && !platValue.includes(PLAT_OTHER)) {
				let res = kddTempList?.filter(item => platValue.includes(templateType4PlatformTypeEnum[item.KddType] as platform));
				return res;
			}
			return kddTempList;
		};

		// 处理快递单模板信息
		const handleTemplateInfo = async(info) => {
			const { scanPrintSetting	} = scanPrintStore;
			const { scanChangeExIdForFirstSend, scanChangeExIdForGetYd, scanChangeExIdForNotGetYd } = scanPrintSetting?.scanSet || {};
			const allowChangeTemplate = scanChangeExIdForFirstSend || scanChangeExIdForGetYd || scanChangeExIdForNotGetYd; // 扫描时自动切换标签关联快递模版设置
			const { exId, exName } = info;
			const isFirstSend = exId || exName;
			// 非先发货
			if (!isFirstSend) {
				const { kddTempList, isMergePrint } = tradeStore;
				const { platform } = await getPlatAndShops({}, true);
				const params = {
					queryType: 1,
					startTime: dayjs().subtract(1, "w").startOf('d').format("YYYY-MM-DD HH:mm:ss"),
					endTime: dayjs().endOf('d').format("YYYY-MM-DD HH:mm:ss"),
					pageNo: 1,
					pageSize: 10,
					platformList: platform,
					sellerIdList: [info.sellerId],
					tidList: [info?.packInfo?.tid]
				};
				timer.start(`快销查询底单记录`);
				let bgTradeInfo = await TradeQueryBgTradeForScanApi(params).catch((e) => console.log(e));
				timer.stop(`快销查询底单记录`, {
					type: logTypeEnum['小标签'],
				});
				console.log('bgTradeInfo,快销查询底单记录', bgTradeInfo);
				// 有单号记录的
				if (bgTradeInfo?.data?.list?.length && scanChangeExIdForGetYd) {
				// 取第一个就行，后端默认返回的第一个就是最新的
					const lastTemplateInfo = bgTradeInfo?.data?.list[0];
					// lastTemplateInfo中没有Mode_ListShowId值，所以要从getKddTempList()中获取
					if (!isMergePrint) { // 如果未开启融合打印
						getKddTempList()?.forEach(item => {
							if (item.Exid == lastTemplateInfo.exId) {
								lastTemplateInfo["templateId"] = item.Mode_ListShowId;
								lastTemplateInfo["bindFjrId"] = item.bindFjrId;
							}
							return item.dictStatus !== TEMPLATE_ZMJ;
						});
					} else { // 如果开启融合打印
						kddTempList.forEach(template => {
							if (template.exCode == lastTemplateInfo.exCode) {
								template.userTemplateList.forEach(subTemplate => {
									if (subTemplate.exId == lastTemplateInfo.exId) {
										lastTemplateInfo["templateId"] = template.id;
										lastTemplateInfo["bindFjrId"] = template.bindFjrId;
									}
								});
							}
						});
					}
					// 如果模板没有被删除（lastTemplateInfo.templateId判断），且勾选已申请单号订单（未先发货）自动切换为最新申请的单号的模版，就回显，不然走以前老逻辑回显
					if (lastTemplateInfo.templateId) {
						console.log('%c [ 命中-底单记录 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', '命中底单记录');
						event.emit(UPDATE_FORM_VALUES, lastTemplateInfo);
						return lastTemplateInfo;
					}
				}
			}
		};

		let hasPlayedVoice = false;
		this.playVoiceSuccess = null;

		// 订单详情挪到前面来
		console.time('小标签查询-订单详情');
		let _list = await handlePackageList(res?.packageInfoVos);
		this.packageInfoVos = _list;
		console.timeEnd('小标签查询-订单详情');

		// 多件订单混扫备齐模式
		const { scanPrintSetting } = scanPrintStore;
		const mixScanMode = !!scanPrintSetting?.scanSet?.mixScanMode;
		const { alreadyScan, sortNumCode, sortTradeSn, sortOrderSn, packageList } = res;
		console.log('scanPrintSetting', scanPrintSetting);
		if (res?.packageId === this.scanPrintTempInfo.takeGoodsLabelInfoId) {
			// 再次扫描
			// * 更新备齐状态
			console.log('xxxx再次扫描');
			let noChange = false;
			// !! 再次扫描需要情况计数，下面会重新计数
			if (mixScanMode && !alreadyScan) {
				this.scanPrintTempInfo.scannedGoodsCount = 0;
			}
			res?.packageList.forEach((pack, packIndex) => {
				pack.takeGoodsLabelInfos.forEach(async(info, infoIndex) => {
					let temp = this.takeGoodsLabelInfos[packIndex].takeGoodsLabelInfos[infoIndex];
					info.isAbnormal = this.isAbnormal(info);
					info.preparedNum = temp.preparedNum || 0;
					info.preparedObj = temp.preparedObj || {};
					info.labelIds = temp.labelIds || [];
					info.manualPrepare = temp.manualPrepare;
					info.sellerAbbreviation = shopObj[info.sellerId]?.sellerAbbreviation;
					console.log('infoinfo', JSON.parse(JSON.stringify(info)));
					// currentScan代表当前扫描的商品
					if (info.currentScan) {
						// if (info.orderPrintStatus === 'ALREADY_PRINT') {
						// this.scanPrintTempInfo.scanInfoMsg = <span className={ s['scan-info-content-error'] }>重复打印——已在扫描打印页面打印过快递单，如需原单号重打，请前往底单记录中打印</span>;
						// }
						event.emit(SET_CURRENT_SCAN_DATA_INFO, info);
						const lastTemplateInfo = await handleTemplateInfo({ ...info, packInfo: pack });
						console.log('lastTemplateInfo', lastTemplateInfo, info);
						// 非先发货，但是已经有单号、且勾选的
						if (lastTemplateInfo) {
							if (lastTemplateInfo.orderIds?.includes(info?.oid)) {
								info.exId = lastTemplateInfo.exId;
								info.exName = lastTemplateInfo.exName;
								info.ydNo = lastTemplateInfo.exNumber;
							}
						} else {
							// 先发货的、先申请单号但是未勾选的
							event.emit('scanPrint.changeModeListShowId', { ...info, packInfo: pack });
						}
						if (info.preparedObj?.[id] && !mixScanMode) {
							this.scanPrintTempInfo.scanInfoMsg = '重复扫描订单';
							playAudio(voice重复扫描);
							console.log('xxxxxx222', 'voice重复扫描');
							hasPlayedVoice = true;
							noChange = true;
						} else if (info.manualPrepare) {
							this.scanPrintTempInfo.scanInfoMsg = '商品已备齐';
							playAudio(voice商品已备齐);
							hasPlayedVoice = true;
							noChange = true;
						} else if (!info.preparedObj?.[id]) {
							hasPlayedVoice = scanPrintPlayAudio({ info, pack, scanSuccessVoiceClose });
							info.preparedObj[id] = true;
							if (mixScanMode) {
								info.alreadyScanLabelSortCodeList?.forEach(s => {
									this.updateScanPrintOrderInfo(info.oid, s.labelId);
								});
							} else {
								this.updateScanPrintOrderInfo(info.oid, id);
							}
							info.labelIds.push(id);
							if (!info.isHide && !info.isAbnormal) {
								// 多件订单混扫备齐模式 preparedNum从返回值中获取
								if (mixScanMode) {
									info.preparedNum = Number(info?.alreadyScanLabelCount || 0);
									// 多件扫描待定处理， 表格底部总宝贝数量
									this.scanPrintTempInfo.scannedGoodsCount += Number(info?.alreadyScanLabelCount || 0);
									info.alreadyScanLabelSortCode = info?.alreadyScanLabelSortCodeList?.map(x => `${x.sortNumCode}${x.sortTradeSn}-${x.sortOrderSn}`).join(',');
									info.alreadyScan = alreadyScan; // 当前扫描的商品是否是意见扫描的影响 +1 的动画
								} else {
									console.log('xxxxxxx555', info);
									info.preparedNum++;
									this.scanPrintTempInfo.scannedGoodsCount++;
								}

							}
							this.scanPrintTempInfo.currentScanOrderInfo = info;
						}

						if (platformGift == 1 && platformGiftNotInvolvedTakeGoodsLabel == 1) {
							pack.takeGoodsLabelInfos.forEach(i => {
								if (i.isGift && !i.preparedNum) {
									this.scanPrintTempInfo.scannedGoodsCount += i.goodsNum;
									i.preparedNum = i.goodsNum;
								}
							});
						}

						if (!noChange) {
							this.takeGoodsLabelInfos = res?.packageList;
						}
					// 非当前扫描的商品 多件订单混扫备齐模式 也需要处理
					} else if (!info.isHide && !info.isAbnormal && mixScanMode) {
						// 多件订单混扫备齐模式 preparedNum从返回值中获取
						info.preparedNum = Number(info?.alreadyScanLabelCount || 0);
						info.alreadyScanLabelSortCode = info?.alreadyScanLabelSortCodeList?.map(x => `${x.sortNumCode}${x.sortTradeSn}-${x.sortOrderSn}`).join(',');
						// 多件扫描待定处理，表格底部总宝贝数量
						this.scanPrintTempInfo.scannedGoodsCount += Number(info?.alreadyScanLabelCount || 0);
					}
				});
			});
			// if (!noChange) {
			// 	this.takeGoodsLabelInfos = res?.packageList;
			// }
			if (!mixScanMode) {
				!scanSuccessVoiceClose && !hasPlayedVoice && playAudio(voice扫描成功);
				return;
			}
			// 先判断是否扫过
			if (alreadyScan) {
				this.scanPrintTempInfo.scanInfoMsg = "重复扫描订单";
				playAudio(voice重复扫描);
				console.log('xxxxxx333', 'voice重复扫描');
			}
			// 播报扫描分拣码情况
			// 没扫过则播报标签，2个条件则不播报  1.只有1个商品时候 或者 2.已经扫描过 这两个条件不播报
			const isNoAllowPlay = ((packageList && packageList.length === 1 && packageList[0]?.takeGoodsLabelInfos?.length === 1 && packageList[0]?.takeGoodsLabelInfos[0]?.goodsNum < 2)) || alreadyScan;
			const text = `${sortNumCode}${sortTradeSn}-${sortOrderSn}`;
			// 没命中不允许播报条件 则播报分拣码
			!isNoAllowPlay && textTransformAudioPlay(text, hasPlayedVoice ? DELAY_TIME : 0);
			this.scanPrintTempInfo.mixScanModeMsg = text;

		} else {
			// 初次扫描
			this.scanPrintTempInfo.takeGoodsLabelInfoId = res?.packageId;
			this.scanPrintTempInfo.scannedGoodsCount = 0;
			let totalCount = 0;
			let delayTime;
			res?.packageList.forEach(pack => {
				pack.takeGoodsLabelInfos.forEach(async info => {
					info.isAbnormal = this.isAbnormal(info);
					info.sellerAbbreviation = shopObj[info.sellerId]?.sellerAbbreviation;
					totalCount += (info.isHide ? 0 : info.goodsNum) || 0;
					// currentScan代表当前扫描的商品
					if (info.currentScan) {
						if (info.orderPrintStatus === 'ALREADY_PRINT') {
							// this.scanPrintTempInfo.scanInfoMsg = <span className={ s['scan-info-content-error'] }>重复打印——已在扫描打印页面打印过快递单，如需原单号重打，请前往底单记录中打印</span>;
						}
						event.emit(SET_CURRENT_SCAN_DATA_INFO, info);
						hasPlayedVoice = scanPrintPlayAudio({ info, pack, firstScan: true, scanSuccessVoiceClose });
						const lastTemplateInfo = await handleTemplateInfo({ ...info, packInfo: pack });
						console.log('lastTemplateInfolastTemplateInfo', lastTemplateInfo, info);
						if (lastTemplateInfo) {
							if (lastTemplateInfo.orderIds?.includes(info?.oid)) {
								info.exId = lastTemplateInfo.exId;
								info.exName = lastTemplateInfo.exName;
								info.ydNo = lastTemplateInfo.exNumber;
							}
						} else {
							event.emit('scanPrint.changeModeListShowId', { ...info, packInfo: pack });
						}
						if (mixScanMode) {
							info.alreadyScanLabelSortCodeList?.forEach(s => {
								this.updateScanPrintOrderInfo(info.oid, s.labelId);
							});
						} else {
							this.updateScanPrintOrderInfo(info.oid, id);
						}
						info.preparedObj = {
							[`${id}`]: true,
						};
						info.labelIds = [id];
						if (!info.isHide && !info.isAbnormal) {
							// 多件订单混扫备齐模式 preparedNum从返回值中获取
							if (mixScanMode) {
								info.preparedNum = Number(info?.alreadyScanLabelCount || 0);
								info.alreadyScanLabelSortCode = info?.alreadyScanLabelSortCodeList?.map(x => `${x.sortNumCode}${x.sortTradeSn}-${x.sortOrderSn}`).join(',');
								// 多件扫描待定处理， 表格底部总宝贝数量
								this.scanPrintTempInfo.scannedGoodsCount += Number(info?.alreadyScanLabelCount || 0);
								info.alreadyScan = alreadyScan; // 当前扫描的商品是否是意见扫描的影响 +1 的动画
							} else {
								info.preparedNum = info.isAbnormal ? 0 : 1;
								this.scanPrintTempInfo.scannedGoodsCount = info.isAbnormal ? 0 : 1;
							}

						}

						if (platformGift == 1 && platformGiftNotInvolvedTakeGoodsLabel == 1) {
							pack.takeGoodsLabelInfos.forEach(i => {
								if (i.isGift) {
									this.scanPrintTempInfo.scannedGoodsCount += i.goodsNum;
									i.preparedNum = i.goodsNum;
								}
							});
						}
						this.scanPrintTempInfo.currentScanOrderInfo = info;
						this.takeGoodsLabelInfos = res?.packageList;
					} else if (!info.isHide && !info.isAbnormal && mixScanMode) {
						// 多件订单混扫备齐模式 preparedNum从返回值中获取
						info.preparedNum = info?.alreadyScanLabelCount || 0;
						info.alreadyScanLabelSortCode = info?.alreadyScanLabelSortCodeList?.map(x => `${x.sortNumCode}${x.sortTradeSn}-${x.sortOrderSn}`).join(',');
						// 多件扫描待定处理，表格底部总宝贝数量
						this.scanPrintTempInfo.scannedGoodsCount += Number(info?.alreadyScanLabelCount || 0);
					}
				});
			});
			this.scanPrintTempInfo.goodsTotalCount = totalCount;
			const { alreadyScan, sortNumCode, sortTradeSn, sortOrderSn } = res;
			// 1.先判断是重复扫描
			if (alreadyScan && mixScanMode) {
				this.scanPrintTempInfo.scanInfoMsg = "重复扫描订单";
				// 调整：待发货提示音会盖过重复扫描声音
				setTimeout(() => {
					playAudio(voice重复扫描); 
					console.log('xxxxxx444', 'voice重复扫描');
				}, 0);
				
				delayTime = 1000;
			} else if (!scanSuccessVoiceClose) {
				playAudio(voice扫描成功);
			}

			const isMultiple = res?.packageList.length > 1 || res?.packageList?.[0]?.takeGoodsLabelInfos?.length > 1 || res?.packageList?.[0]?.takeGoodsLabelInfos[0]?.goodsNum > 1;
			// 混扫不进行合单、多件提示
			// 合单、多件同时存在时只播报合单
			if (!hasPlayedVoice && isMultiple && !mixScanMode) {
				console.log('xxxxxisMultiple', isMultiple);
				setTimeout(() => {
					if (res?.packageList.length > 1) {
						playAudio(voice合单);
					} else {
						playNumberAudio(setting?.scanSet, isMultiple, !!res?.packageList.length);
					}
				}, hasPlayedVoice ? delayTime : 500);
				hasPlayedVoice = true;
			}

			if (!mixScanMode) return;
			// 播报扫描分拣码情况
			// 没扫过则播报标签，2个条件则不播报  1.只有1个商品时候 或者 2.已经扫描过 这两个条件不播报
			const isNoAllowPlay = ((packageList && packageList.length === 1 && packageList[0]?.takeGoodsLabelInfos?.length === 1 && packageList[0]?.takeGoodsLabelInfos[0]?.goodsNum < 2)) || alreadyScan;
			const text = `${sortNumCode}${sortTradeSn}-${sortOrderSn}`;
			// !! (这里如果已经播报过，默认延迟播报分拣码) 没命中不允许播报条件 则播报分拣码
			!isNoAllowPlay && textTransformAudioPlay(text, hasPlayedVoice ? DELAY_TIME : delayTime);
			// 回显扫描分拣码
			this.scanPrintTempInfo.mixScanModeMsg = text;
			console.log('123123123', this.scanPrintTempInfo);
		}
	}

	isAbnormal = (temp) => {
		// return (
		// 	['REFUND_SUCCESSED'].includes(temp.refundStatus)
		// 	|| ['TRADE_CLOSED', 'TRADE_FINISHED'].includes(temp.orderStatus)
		// 	|| (temp.orderStatus === 'WAIT_BUYER_CONFIRM_GOODS' && !temp.firstSend)
		// 	|| temp.orderPrintStatus === 'ALREADY_PRINT'
		// );
		return false;
	}


	updateInfoStatus = async(infos?:{sids?:string, tidOidsObj?:any}) => {
		if (!this.searchScanPrintFormSetting) return;
		const { id } = this.searchScanPrintFormSetting;
		const tids:string[] = [...new Set(this.takeGoodsLabelInfos.map(item => item.tid))];
		// 根据唯一码查询订单信息
		const res = await ItemTakeGoodsLabelSelectTradeInfoByLabelIdApi({
			labelId: id,
			tids
		});
		console.log('updateInfoStatus', _.cloneDeep(this.takeGoodsLabelInfos), res);
		let _takeGoodsLabelInfos = _.cloneDeep(this.takeGoodsLabelInfos);
		_takeGoodsLabelInfos = _takeGoodsLabelInfos.map((pack, packIndex) => {
			// if (!res.packageList[packIndex]) return pack;

			pack.tradeStatus = res?.packageList?.[packIndex]?.tradeStatus;
			pack.isPending = res.packageList[packIndex].isPending;
			pack.takeGoodsLabelInfos = pack.takeGoodsLabelInfos.map((info, infoIndex) => {
				let temp = res.packageList[packIndex].takeGoodsLabelInfos[infoIndex];
				// info.isAbnormal = this.isAbnormal(temp);
				// !! 禁止添加？兼容
				if (temp.orderPrintStatus === 'ALREADY_PRINT' || temp.orderStatus === 'WAIT_BUYER_CONFIRM_GOODS') {
					info.isAbnormal = true;
					if (info.preparedNum > 0 || info.manualPrepare) {
						this.scanPrintTempInfo.scannedGoodsCount -= info.goodsNum;
					}
					info.preparedNum = 0;
					info.manualPrepare = false;
				}

				
				// 打印后回显，优先级最高，（不能根据isChecked判断是否勾选）
				if (infos?.sids && infos?.tidOidsObj?.[pack?.tid]?.find(item => item?.oid == info?.oid)) {
					info.ydNo = infos?.sids || ''; // 这个时间不一定会有单号记录
				} else if (temp?.ydNo) {
					info.ydNo = temp.ydNo; // 根据最新的结果重置下快递单号
				}

				// 更新先发货标识
				if (info?.firstSend) {
					info.firstSend = temp?.firstSend || false;
				}
				if (info.currentScan) {
					this.scanPrintTempInfo.currentScanOrderInfo = {
						...info,
						...temp,
					};
				}
				return {
					...info,
					...temp,
				};
			});
			return pack;
		});
		this.takeGoodsLabelInfos = _takeGoodsLabelInfos;
		focusLabelInput();
	}

	clearScanPrintInfo = () => {
		this.takeGoodsLabelInfos = null;
		this.packageInfoVos = null;
		this.searchScanPrintFormSetting = null;
		this.shipData = null;
		this.scanPrintTempInfo = {
			takeGoodsLabelInfoId: '',
			scanInfoMsg: '',
			currentScanOrderInfo: null,
			scannedGoodsCount: 0,
			goodsTotalCount: 0,
			mixScanModeMsg: '',
		};

		// * 扫描出来的订单信息
		this.scanPrintOrderInfo = {};
		this.forceScanPrepOrderIds = new Set();
	}

	shipData: Record<string, IPackage> = null;

	setShipData = (val: IPackage) => {
		if (!this.shipData) this.shipData = {};
		this.shipData[val.togetherId] = val;
	}

	// 打印机列表
	printersList: string[] = []

	setPrintersList = (list:string[]) => {
		this.printersList = list;
	}

	// 系统默认打印机
	defaultPrinter: string = ''

	setDefaultPrinter = (printer:string) => {
		this.defaultPrinter = printer;
	}

	skipMergePrintModal: boolean = local.get('scanPrint.skipMergePrintModal') || false;

	setSkipMergePrintModal = (val:boolean) => {
		this.skipMergePrintModal = val;
	}

	prevLabelId: string = "";

	setPrevLabelId = (v:string) => {
		this.prevLabelId = v;
	}

	platformAuthInfoList: NhbInfoObj[] = null;

	platformAuthInfo: IPlatformAuthInfo = null;

	onOKBatchMoveModalLoading:boolean = false;

	setOnOKBatchMoveModalLoading = (v:boolean) => {
		this.onOKBatchMoveModalLoading = v;
	}

	hasPlatformAuthInfo = () => {
		return this.platformAuthInfo && Object.keys(this.platformAuthInfo).length > 0;
	}

	getLabelPushPlatformByNickName = (nickName) => {
		const platAuthInfo = this.platformAuthInfoList?.find((item) => (item.platformNick == nickName));
		if (platAuthInfo) {
			return platAuthInfo?.labelPushPlatform;
		}
		return "";
	}

	getLabelPushInfoList = async(refresh = false) => {
		try {
			if (this.platformAuthInfoList && !refresh) {
				return this.platformAuthInfoList;
			}
			const res = await IndexGetLabelPushPlatformAuthInfoApi();
			const _platformAuthInfo = {};
			this.platformAuthInfo = null;
			if (res.platformAuthInfoList) {
				this.platformAuthInfoList = res.platformAuthInfoList;
				res.platformAuthInfoList.forEach((item) => {
					if (item.enable) {
						_platformAuthInfo[item.labelPushPlatform] = item;
					}
				});
				this.platformAuthInfo = _platformAuthInfo;
				return this.platformAuthInfoList;
			}
			return [];
		} catch (error) {
			console.log('getLabelPushInfoList error nhbAccount:::', error);
		}
	}

	// 扫描打印列配置
	columnConfig = {
		userConfig: [], // 用户保存的配置
		defaultConfig: scanPrint_default_column_config, // 默认配置
	}

	columnConfigToJs = () => {
		return toJS(this.columnConfig);
	}

	setColumnConfig = (config) => {
		this.columnConfig.userConfig = config;
	}
	
	// 获取扫描打印列配置
	getColumnList = async(refresh = false) => {
		if (this.columnConfig?.userConfig?.length > 0 && !refresh) {
			return this.columnConfig;
		}
		try {
			const res = await ItemItemUserConfigQueryConfigApi({ itemUserConfigBizEnum: scanPrint_column_config_key });
			const rule = res?.find((item) => (item?.biz === 'label_scan_column_config'));

			let values = JSON.parse((rule?.value && rule?.value !== 'null') ? rule?.value : "[]");

			// let values = [];

			// 把新加到本地的key放进去，剔除本地没有的，更新线上保存的信息
			let newValues = [];
			scanPrint_default_column_config.forEach(item => {
				let old = values.find(v => v.key === item.key);
				if (old) {
					newValues.push({
						...item,
						index: old.index,
						ischecked: old.ischecked,
						isfixed: old.isfixed,
					});
				} else {
					newValues.push(item);
				}
			});

			newValues.sort((a, b) => (a.index - b.index));
		
			this.columnConfig.userConfig = newValues;
			return this.columnConfig;
		} catch (error) {
			this.columnConfig.userConfig = scanPrint_default_column_config;
			return this.columnConfig;
		}
	}

	// 保存扫描打印列配置
	saveColumnList = async(data) => {
		try {
			const params = data || [];
			const res = await ItemItemUserConfigUpdateConfigApi({ value: JSON.stringify(params), itemUserConfigBizEnum: scanPrint_column_config_key });
			if (res) {
				const rule = res?.find((item) => (item?.biz === 'label_scan_column_config'));
				const value = JSON.parse((rule?.value && rule?.value !== 'null') ? rule?.value : "[]");
				console.log('%c [ 11111 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', rule, value);
				this.columnConfig.userConfig = value;
			}
		} catch (error) {
			console.log("error:", error);
		}
	}

	videoMonitorState = false;

	selectedShootingDevices = [];

	nvrDevices = [];

	setNvrDevices = (v) => {
		this.nvrDevices = v;
	}

	setSelectedShootingDevices = (v) => {
		this.selectedShootingDevices = v;
	}

	setVideoMonitorState = (v) => {
		this.videoMonitorState = v;
	}
}

const scanPrintStore = new ScanPrintStore();
export default scanPrintStore;
