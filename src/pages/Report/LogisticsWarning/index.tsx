import Layout from "antd/lib/layout/layout";
import React, { useState, useEffect, useMemo, useCallback } from "react";
import dayjs from "dayjs";
import { useHistory } from "react-router-dom";
import { Button, Tooltip, Switch, Typography, Dropdown, Menu, Modal, Radio, Alert } from "antd";
import { ColumnsType } from "antd/lib/table";
import { useRequest } from "ahooks";
import { CopyOutlined, DownOutlined } from "@ant-design/icons";
import message from '@/components/message';
import s from './index.module.scss';
import Pointer from '@/utils/pointTrack/constants';
import SearchContainer from "./components/SearchContainer";
import BaseTable from "@/components/SearchTable/BaseTable";
import { TradeLogisticsGetLogisticsListRequest, TradeLogisticsGetLogisticsListResponse, TradeLogisticsGetUserConfigResponse } from "@/types/schemas/report/logisticsWarning";
import { TradeLogisticsBatchUpdateDeal<PERSON><PERSON>us<PERSON><PERSON>, TradeLogisticsGetLogisticsList<PERSON>pi, TradeLogisticsGetUserConfigApi, TradeLogisticsManualSyncLogisticsInfoApi, TradeLogisticsQueryLogisticsSyncTimeApi, TradeLogisticsSaveUserConfigApi } from "@/apis/report/logisticsWarning";
import ShowLogistic from "../KddLog/components/ShowLogistic";
import { copyToPaste, getMirrorEnum, splitFxgTid, useQuery } from "@/utils";
import { ABNORMAL_STATUS, EVENT_BUS, EXCEPTION_STATUS, HANDLE_STATUS, LOGISTICS_TYPE, PACKAGE_STATUS, REFUND_STATUS, logisticsWarningSettingEnum } from "./contants";
import { getTradePlatformLabel } from "@/pages/Trade/utils";
import LowVersionControlModal, { lowVersionLock } from "@/components-biz/LowVersionControlModal";
import { PageNameControlEnum } from "@/components-biz/LowVersionControlModal/constant";
import { getMultiShops, getShopName } from "@/components-biz/ShopListSelect/shopListUtils";
import Overview from "./components/Overview";
import KdzsDateRangePicker1 from "@/components/DateRangeComp/kdzsRangePicker1";
import { downloadCenter } from "@/pages/Index/DownloadCenter/utils";
import { ModulesFunctionEnum } from "@/types/schemas/setting/download";
import { DatePickerKey, getCacheDateRange } from "@/components/DateRangeComp/kdzsRangePickerUtil";
import userStore from "@/stores/user";
import event from "@/libs/event";
import Copy from "@/components/Copy";
import { guideModal } from "@/components-biz/PayDrawer";
import { PAY_TYPE } from "@/components-biz/PayDrawer/interface";



type dataItemType = TradeLogisticsGetLogisticsListResponse['data']['list'][number];

const LogisticsWarning:React.FC<{}> = (props) => {
	getMirrorEnum(EXCEPTION_STATUS);
	getMirrorEnum(REFUND_STATUS);
	// getMirrorEnum(ABNORMAL_STATUS);
	const history = useHistory();
	const locationSearchParams = useQuery(history.location.search);
	const [selectedRowKeys, setSelectedRowKeys] = useState([]);
	const [batchHandling, setBatchHandling] = useState(false);
	const [exportLoading, setExportLoading] = useState(false);
	const [countDateValue, setCountDateValue] = useState(getCacheDateRange(DatePickerKey.logistics_warning) || [dayjs().subtract(1, 'M').startOf('day'), dayjs().endOf('day')]);
	const [dateValue, setDateValue] = useState(getCacheDateRange(DatePickerKey.logistics_warning) || [dayjs().subtract(1, 'M').startOf('day'), dayjs().endOf('day')]);
	// const [logisticsConfigModalVisible, setLogisticsConfigModalVisible] = useState(false);
	const [logisticsConfig, setLogisticsConfig] = useState<TradeLogisticsGetUserConfigResponse['data']>({});
	const [dataSource, setDataSource] = useState<dataItemType[]>([]);
	const [activeCardItemKey, setActiveCardItemKey] = useState("");
	const [lastUpdateDate, setLastUpdateDate] = useState("--");
	const [searchParams, setSearchParams] = useState<TradeLogisticsGetLogisticsListRequest>({
		sendTimeStart: dateValue[0]?.format("YYYY-MM-DD HH:mm:ss") || '',
		sendTimeEnd: dateValue[1]?.format("YYYY-MM-DD HH:mm:ss") || '',
		total: 0,
		pageSize: 10,
		pageNo: 1,
		id: '',
	});
	const [downloadExcelModalVisible, setDownloadExcelModalVisible] = useState(false); // 导出Excel
	const [exportType, setExportType] = useState(1); // 导出Excel类型
	const { runAsync: updateConfigApi, loading: updateLoading } = useRequest(TradeLogisticsSaveUserConfigApi, {
		manual: true,
	});

	const onChangeExportType = (e: any) => {
		setExportType(e.target.value);
	};

	const onCloseDownloadExcelModal = () => {
		setDownloadExcelModalVisible(false);
	};

	const setTheActiveCardItemKey = (v) => {
		setActiveCardItemKey(v);
	};


	useEffect(() => {
		const fn = async() => {
			if (await lowVersionLock(PageNameControlEnum.物流预警)) return;
			getLogisticsConfig();
			getUpdateDate();
		};
		fn();
		event.on(EVENT_BUS.SET_CARD_ITEM_KET, setTheActiveCardItemKey);
		return () => {
			event.off(EVENT_BUS.SET_CARD_ITEM_KET, setTheActiveCardItemKey);
		};
	}, []);

	useEffect(() => {
		const fn = async() => {
			if (locationSearchParams.type) {
				const multiShopS = await getMultiShops({});
				handleSearch({
					sendTimeStart: dayjs().subtract(1, 'M').startOf('day')?.format("YYYY-MM-DD HH:mm:ss") || '',
					sendTimeEnd: dayjs().endOf('day')?.format("YYYY-MM-DD HH:mm:ss") || '',
					multiShopS,
					pageNo: 1,
					pageSize: searchParams.pageSize,
					dealStatusList: [HANDLE_STATUS.待处理],
					logisticsType: locationSearchParams.type
				});
			}

		};

		fn();
	}, [locationSearchParams.type]);

	const getLogisticsConfig = () => {
		TradeLogisticsGetUserConfigApi({}).then(res => {
			setLogisticsConfig(res);
		});
	};

	const { runAsync: syncApi, loading } = useRequest(TradeLogisticsManualSyncLogisticsInfoApi, {
		manual: true,
	});

	const { runAsync: searchApi, loading: isSearch } = useRequest(TradeLogisticsGetLogisticsListApi, {
		manual: true,
	});

	const getUpdateDate = async() => {
		const { userId } = await userStore.getUserInfo();
		TradeLogisticsQueryLogisticsSyncTimeApi({
			cacheKey: `logistics_manual_sync_cache_key_${userId}`
		}).then(res => {
			if (res) {
				const dateFormat = dayjs(res).format("YYYY-MM-DD HH:mm:ss");
				setLastUpdateDate(dateFormat);
			}
		});
	};

	const syncLogisticsWarning = async() => {
		await syncApi({
			id: dataSource.map(item => item.id).join(',')
		});
		getLogisticsConfig();
		handleSearch({
			...searchParams,
			sendTimeStart: dateValue[0]?.format("YYYY-MM-DD HH:mm:ss") || '',
			sendTimeEnd: dateValue[1]?.format("YYYY-MM-DD HH:mm:ss") || '',
		});
	};

	const onSearch = (params: TradeLogisticsGetLogisticsListRequest, updateSearchParams = true) => {
		let _searchParams = {
			...params,
			sendTimeStart: dateValue[0]?.format("YYYY-MM-DD HH:mm:ss") || '',
			sendTimeEnd: dateValue[1]?.format("YYYY-MM-DD HH:mm:ss") || '',
			pageNo: 1,
			pageSize: searchParams.pageSize
		};
		setSearchParams(_searchParams);
		// if(updateSearchParams){
		// 	setSearchParams(_searchParams);
		// }

		handleSearch(_searchParams);
	};

	const handleSearch = (params: TradeLogisticsGetLogisticsListRequest = searchParams) => {
		searchApi(params).then(res => {
			setDataSource(res.list);
			setSearchParams(prev => ({
				...prev,
				total: params.pageNo === 1 ? res.total : prev.total,
			}));
		});
	};

	const handleSwitch = async() => {
		await updateConfigApi({
			onOff: logisticsConfig.onOff === 1 ? 0 : 1,
		});
		getLogisticsConfig();
	};

	const columns: ColumnsType<dataItemType> = [
		{
			title: '序号',
			width: 40,
			dataIndex: 'index',
			fixed: "left",
			render: (text, row, index: number) => {
				return (
					<div>{index + 1}</div>
				);
			}
		},
		{
			title: '平台/店铺',
			width: 120,
			render: (text, row, index: number) => {
				return (
					<div>{getTradePlatformLabel(row.shopType)} <span className="r-ml-4">{getShopName({ plat: row.shopType, sellerNick: row.shopName })}</span></div>
				);
			}
		},
		{
			title: '订单编号',
			width: 160,
			dataIndex: 'tid',
			render: (text, row, index) => {
				const copyTid = () => {
					copyToPaste(splitFxgTid(text));
				};
				return (
					<div>{text} <CopyOutlined onClick={ copyTid } /></div>
				);
			}
		},
		{
			title: '发货时间',
			width: 130,
			dataIndex: 'sendTime',
		},
		{
			title: '快递面单',
			width: 120,
			dataIndex: 'exCodeName',
		},
		{
			title: '快递单号',
			width: 140,
			dataIndex: 'ydNo',
			render: (text, row, index) => {
				const copyYdNo = () => {
					copyToPaste(text);
				};
				return (
					<div>
						<ShowLogistic ydNo={ row.ydNo }>{text}</ShowLogistic>
						<Copy copyData={ text } />
					</div>
				);
			}
		},
		{
			title: '省市区',
			width: 120,
			render: (text, row, index: number) => {
				return (
					<div className="r-flex r-fd-c">
						<div>{row.receiverProvince}</div>
						<div>{row.receiverCity}</div>
						<div>{row.receiverCounty}</div>
					</div>
				);
			}
		},
		{
			title: '包裹状态',
			width: 80,
			dataIndex: 'expressPackageStatus',
			render: (text, row, index) => (
				<div>{PACKAGE_STATUS.find(item => +item.value === +text)?.label || ''}</div>
			)
		},
		{
			title: '处理状态',
			width: 80,
			dataIndex: 'dealStatus',
			render: (text, row, index) => (
				<div>{HANDLE_STATUS[row.dealStatus]}</div>
			)
		}, {
			title: '异常状态',
			width: 80,
			dataIndex: 'subLogisticsStatus',
			render: (text, row, index) => (
				<div>{EXCEPTION_STATUS[row.subLogisticsStatus]}</div>
			)
		}, {
			title: '预警状态',
			width: 80,
			dataIndex: 'abnormalStatus',
			render: (text, row, index) => {
				let raw = '超过24小时';
				if (text === ABNORMAL_STATUS.异常件 && row.logisticsType === LOGISTICS_TYPE.超时未签收) {
					raw = '超过7天';
				} else if (text === ABNORMAL_STATUS.异常件 && row.logisticsType === LOGISTICS_TYPE.退回件) {
					raw = '';
				}
				return (
					<div className={ s[`abnormal-status-${text}`] }>
						{/* {EXCEPTION_TYPE.find(item => +item.value === +text)?.label || ''} */}
						{ABNORMAL_STATUS[text] || ""}
						{+text === ABNORMAL_STATUS.预警件 ? `(${LOGISTICS_TYPE[row.logisticsType]})` : ''}
						{+text === ABNORMAL_STATUS.异常件 ? `(${LOGISTICS_TYPE[row.logisticsType]}${raw})` : ''}
					</div>
				);
			}
		}, {
			title: '退款状态',
			width: 80,
			dataIndex: 'refundStatusType',
			render: (text, row, index) => (
				<div>{REFUND_STATUS[row.refundStatusType]}</div>
			)
		},
		{
			title: '物流同步时间',
			width: 140,
			dataIndex: 'lastSyncTime',
		},
		{
			title: '最新物流信息',
			width: 140,
			dataIndex: 'lastDesc',
			render: (text, row) => (
				<>
					<Typography.Paragraph ellipsis={ {
						expandable: true,
						rows: 4,
					} }
					>
						{text || '暂无物流信息'}
					</Typography.Paragraph>
					<div>{row.lastTime}</div>
				</>
			),
		},
		{
			title: '详情',
			width: 60,
			render: (text, row, index: number) => {
				return (
					<ShowLogistic data-point={ Pointer.物流预警_物流详情 } ydNo={ row.ydNo } exCode={ row.kdCode } />
				);
			}
		}
	];
	const batchHandle = (type) => {
		if (!selectedRowKeys.length) {
			message.error("请勾选需要处理的数据");
			return;
		}
		setBatchHandling(true);
		const params = selectedRowKeys.map(i => ({
			id: i,
			dealStatus: type
		}));
		TradeLogisticsBatchUpdateDealStatusApi(params).then(res => {
			message.success("批量处理成功！");
			setSelectedRowKeys([]);
			handleSearch(searchParams);
		}).finally(() => setBatchHandling(false));

	};
	// 批量处理
	const batchHandleMenuItems = useMemo(() => {
		const handleList = [{
			key: HANDLE_STATUS.待处理,
			label: "待处理",
			onClick: () => batchHandle(HANDLE_STATUS.待处理)
		}, {
			key: HANDLE_STATUS.处理中,
			label: "处理中",
			onClick: () => batchHandle(HANDLE_STATUS.处理中)
		}, {
			key: HANDLE_STATUS.已处理,
			label: "已处理",
			onClick: () => batchHandle(HANDLE_STATUS.已处理)
		}];
		const menuItems = handleList.map(i => (
			<Menu.Item
				onClick={ i.onClick }
				key={ i.key }
			>
				{i.label}
			</Menu.Item>
		));
		return menuItems;

	}, [selectedRowKeys]);

	// 导出数据

	const logisticsExport = async() => {
		try {
			const { pageNo, pageSize, total, ...otherParams } = searchParams;
			let params = {
				...otherParams,
				sendTimeStart: dateValue[0]?.format("YYYY-MM-DD HH:mm:ss") || '',
				sendTimeEnd: dateValue[1]?.format("YYYY-MM-DD HH:mm:ss") || '',
			};
			if (!searchParams.multiShopS) {
				const multiShops = await getMultiShops({});
				params.multiShopS = multiShops;
			}
			if (exportType === 1) {
				if (selectedRowKeys?.length === 0) {
					message.warning("当前未勾选任何数据!");
					setExportLoading(false);
					return;
				}
				params = {
					id: selectedRowKeys?.join(',')
				};
			}
			setExportLoading(true);
			await downloadCenter({
				requestParams: params,
				fileName: '物流预警',
				module: ModulesFunctionEnum.物流预警
			});
			setExportLoading(false);
			setDownloadExcelModalVisible(false);
		} catch (error) {
			setExportLoading(false);
			setDownloadExcelModalVisible(false);
		}
	};

	const onDateChange = (value) => {
		setDateValue(value);
	};
	const onDateSelectFinished = (value) => {
		setCountDateValue(value);
		const _searchParams = {
			...searchParams,
			sendTimeStart: value[0]?.format("YYYY-MM-DD HH:mm:ss") || '',
			sendTimeEnd: value[1]?.format("YYYY-MM-DD HH:mm:ss") || '',
		};
		handleSearch(_searchParams);
	};

	// 当点击卡片上的数据详情时

	const onClickDetail = async(typeInfo, dataItem) => {
		// 处理查询入参
		const [startDate, endDate] = dataItem.dateRange || [];
		const params = {
			pageSize: searchParams.pageSize,
			pageNo: searchParams.pageNo,
			logisticsYunStatusVal: typeInfo.key,
			dealStatusList: [HANDLE_STATUS.处理中, HANDLE_STATUS.待处理],
			multiShopS: await getMultiShops({}),
			lastTimeStart: endDate && dayjs().subtract(endDate, 'h').format("YYYY-MM-DD HH:mm:ss"),
			lastTimeEnd: startDate && dayjs().subtract(startDate, 'h').format("YYYY-MM-DD HH:mm:ss"),
		};

		// 如果是异常件
		if (typeInfo.key === logisticsWarningSettingEnum.异常包裹) {
			params["subLogisticsStatus"] = dataItem.searchKey;
			params["lastTimeStart"] = null;
			params["lastTimeEnd"] = null;
		}
		const activeCardItemKey = `${typeInfo.key}_${dataItem.key}`;
		setActiveCardItemKey(activeCardItemKey);
		onSearch(params);

	};

	const rowSelection = {
		selectedRowKeys,
		fixed: "left",
		onChange: (selectedRowKeys: React.Key[], selectedRows) => {
			// setSelectedRows(selectedRows);
			setSelectedRowKeys(selectedRowKeys);
		},
	};

	const handleCopyOk = useCallback((key, msg = '复制成功') => {
		if (!selectedRowKeys?.length) {
			message.warn('请先勾选');
			return;
		}
		const selected = dataSource.filter(i => selectedRowKeys.includes(i.id));
		const _keyValues = selected.map(i => i[key]).join('\n');
		copyToPaste(_keyValues, msg, true);
	}, [selectedRowKeys, dataSource]);

	return (
		<div>
			<Layout className="kdzs-section-small r-flex r-jc-sb r-fd-r">
				<div className="r-flex r-ai-c">
					<h2 className="r-mr-8">物流预警</h2>
					<Switch
						loading={ updateLoading }
						className="r-mr-8"
						checkedChildren="开启"
						unCheckedChildren="关闭"
						data-point={ Pointer.物流预警_开关 }
						checked={ !!logisticsConfig.onOff }
						onChange={ handleSwitch }
					/>
					<span>更新时间：{lastUpdateDate}</span>
					<Tooltip title="点击后可同步当前页包裹最新物流信息" overlayStyle={ { maxWidth: '500px' } }>
						<Button
							className="r-ml-8"
							type="link"
							data-point={ Pointer.物流预警_手动更新 }
							onClick={ syncLogisticsWarning }
							loading={ loading }
							disabled={ !logisticsConfig.onOff || !dataSource.length }
						>手动更新
						</Button>
					</Tooltip>
				</div>
				<div className="r-flex r-ai-c">
					<div>
						发货时间：
						<KdzsDateRangePicker1
							style={ { width: 175, height: 32, padding: '0px 12px' } }
							value={ dateValue }
							onChange={ onDateChange }
							datePickerKey={ DatePickerKey.logistics_warning }
							onFinished={ onDateSelectFinished }
							cacheQuickChoose
							useServeTime
						/>
					</div>
					{/* <div className="r-ml-8">剩余单量：<span className="r-c-primary">限时免费</span></div> */}
					<Alert
						style={ {
							height: '32px',
							lineHeight: '32px',
							padding: 10,
							marginLeft: '12px'
						} }
						message={ (
							<div className="r-flex r-ai-c">
								<Tooltip 
									placement="bottom" 
									title={ `当前物流单号上限为${logisticsConfig.logisticsOrderLimit || 0}，
									单号不足可单独购买增购包` }
									overlayStyle={ { maxWidth: '240px' } }
								>
									<span>单量使用：</span>
								</Tooltip>
								<span>
									{String(logisticsConfig.curCount || '0').replace(/\B(?=(\d{3})+(?!\d))/g, ',')}/
									{String(logisticsConfig.logisticsOrderLimit || '0').replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
								</span>
								<Button
									className="r-ml-8"
									type="link"
									onClick={ () => {
										guideModal({
											type: PAY_TYPE.物流预警单量,
											tradeLimitNum: Number(logisticsConfig.logisticsOrderLimit),
											totalConsumeNum: Number(logisticsConfig.curCount),
										});
									} }
								>充值单量
								</Button>
							</div>
						) }
						type="warning"
					/>
				</div>
			</Layout>
			<Layout className="kdzs-section-small" style={ { padding: 24 } }>
				<Overview
					activeCardItemKey={ activeCardItemKey }
					date={ countDateValue }
					logisticsConfig={ logisticsConfig }
					onClick={ onClickDetail }
				/>
			</Layout>
			<Layout className="kdzs-section-small">
				<SearchContainer
					onSearch={ (params) => {
						onSearch({ ...params, logisticsYunStatusVal: "" });
						setSelectedRowKeys([]);
						setActiveCardItemKey("");
					} }
					isSearch={ isSearch }
					logisticsConfig={ logisticsConfig }
					onReset={ () => {
						setSelectedRowKeys([]);
						setActiveCardItemKey("");
					} }
				/>
			</Layout>
			<Layout className="kdzs-section-small">
				<div style={ { width: '100%', display: 'flex', marginBottom: '16px', marginTop: '8px', justifyContent: 'space-between' } }>
					<div className="r-flex" style={ { gap: '12px' } }>
						<Dropdown
							overlay={ (<Menu> {batchHandleMenuItems} </Menu>
							) }
							placement="bottomLeft"
						>
							<Button loading={ batchHandling } type="primary">
								批量处理<DownOutlined />
							</Button>
						</Dropdown>
						<Button onClick={ () => handleCopyOk('ydNo', '复制快递单号成功') }>复制快递单号</Button>
						<Button onClick={ () => handleCopyOk('tid', '复制订单编号成功') }>复制订单编号</Button>
					</div>

					<div className="r-ta-r">
						<Button
							loading={ exportLoading }
							className="r-ml-6"
							onClick={ () => { setDownloadExcelModalVisible(true); } }
						>导出
						</Button>
						{/* <Button
							onClick={ () => { setLogisticsConfigModalVisible(true); } }
							className="r-ml-12"
							type="primary"
						>预警设置
						</Button> */}

					</div>
				</div>
				<BaseTable
					rowKey="id"
					scroll={ {
						y: 'calc(100vh - 400px)'
					} }
					columns={ columns }
					pagination={ {
						showTotal: total => `共${total}个包裹`,
						total: searchParams.total,
						pageSize: searchParams.pageSize,
						current: searchParams.pageNo,
						pageSizeOptions: ['10', '20', '50', '100', '200', '500'],
						onChange: (pageNo: number, pageSize: number) => {
							setSearchParams(prev => {
								let _searchParams = {
									...prev,
									sendTimeStart: dateValue[0]?.format("YYYY-MM-DD HH:mm:ss") || '',
									sendTimeEnd: dateValue[1]?.format("YYYY-MM-DD HH:mm:ss") || '',
									pageNo: pageSize === prev.pageSize ? pageNo : 1,
									pageSize,
								};
								handleSearch(_searchParams);
								setSelectedRowKeys([]);
								return _searchParams;
							});
						}
					} }
					rowSelection={ rowSelection }
					dataSource={ dataSource }
					size="small"
					innerTableStyle={ { padding: 0 } }
				/>
			</Layout>

			{/* {logisticsConfigModalVisible && (
				<LogisticsWarningSettingModal
					configData={ logisticsConfig }
					onCancel={ () => { setLogisticsConfigModalVisible(false); } }
					onOk={ () => { setLogisticsConfigModalVisible(false); getLogisticsConfig(); } }
				/>
			)} */}
			<LowVersionControlModal pageName={ PageNameControlEnum.物流预警 } />
			{/* 导出Excel */}
			<Modal
				centered
				visible={ downloadExcelModalVisible }
				title="导出Excel"
				okText="导出"
				onOk={ logisticsExport }
				onCancel={ onCloseDownloadExcelModal }
				confirmLoading={ exportLoading }
				destroyOnClose
				maskClosable={ false }
			>
				<div>
					导出类型：
					<Radio.Group
						value={ exportType }
						onChange={ onChangeExportType }
					>
						<Radio value={ 1 }>导出已勾选</Radio>
						<Radio value={ 2 }>导出查询结果</Radio>
					</Radio.Group>
				</div>
			</Modal>
		</div>
	);
};

export default LogisticsWarning;
