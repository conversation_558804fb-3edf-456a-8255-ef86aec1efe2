/*
 * @Author: 唐荣洋 <EMAIL>
 * @Date: 2025-08-27 10:54:44
 * @Description:
 */
import { WarningOutlined } from "@ant-design/icons";
import { Button, Modal } from "antd";
import React from "react";
import ReactDOM from "react-dom";
import Icon from "@/components/Icon";
import Flex from "@/components/Flex";
import showPayDrawer from "./showPayDrawer";
import { PAY_TYPE } from "./interface";
import { session } from "@/libs/db";

function showTips({ tradeLimitNum, totalConsumeNum, type }) {
	let insertDiv = document.createElement('div');
	const userInfo = session.get('erp_userInfo');
	console.log(tradeLimitNum, totalConsumeNum, tradeLimitNum, 'tradeLimitNum');
	const isLimit = tradeLimitNum > 0 && totalConsumeNum >= tradeLimitNum;
	insertDiv.id = 'limitDomId';
	document.body.appendChild(insertDiv);
	ReactDOM.render(
		<Modal
			title={ !isLimit ? "单量统计" : "单量不足提醒" }
			visible
			onCancel={ () => {
				document.body.removeChild(insertDiv);
			} }
			onOk={ () => {
				document.body.removeChild(insertDiv);
			} }
		>
			<div>
				<div className="r-flex r-ta-c r-pd-16" style={ { background: "#f0f0f0" } }>
					<div style={ { flex: 1 } }>
						<div className="r-c-999">当前账号订单数量上限</div>
						<div className="r-fs-24">{(+tradeLimitNum)?.toLocaleString()}</div>
					</div>
					<div style={ { flex: 1 } }>
						{
							!isLimit ? (
								<div>当前剩余</div>
							) : (
								<div style={ { color: "#f00" } }><WarningOutlined className="r-mr-4" style={ { color: "ff0" } } />已超出</div>
							)
						}
						<div className="r-fs-24">{
							!isLimit ? (
								<div>{(tradeLimitNum - totalConsumeNum)?.toLocaleString()}</div>
							) : (
								<div style={ { color: "#f00" } }>{(totalConsumeNum - tradeLimitNum)?.toLocaleString()}</div>
							)
						}
						</div>
					</div>
				</div>
				<div className="r-flex r-mt-16 r-pd-16" style={ { border: "1px solid #f0f0f0", alignItems: "center", justifyContent: 'space-between' } }>
					<Flex>
						<div className=" r-mr-16"><Icon type="lianxikefu" className="r-c-666" style={ { fontSize: "24px" } } /></div>
						<div>
							<div className="r-fs-16 r-fw-b r-c-black">联系销售</div>
							<div className="r-c-999 r-mt-8">如需提高更多单量上限，请联系销售或直接购买</div>
						</div>
					</Flex>
					{
						[1, 2, 3].includes(userInfo?.level) ? <Button type="link" style={ { padding: 0 } } onClick={ () => showPayDrawer({ type: type || PAY_TYPE.物流预警单量 }) }>前往订购</Button> : null
					}
				</div>
			</div>
		</Modal>,
		document.getElementById("limitDomId")
	);
}
showTips.data = ({ type, dataStorageMonth }) => {
	let insertDiv = document.createElement('div');
	insertDiv.id = 'limitDomId';
	const userInfo = session.get('erp_userInfo');
	document.body.appendChild(insertDiv);
	ReactDOM.render(
		<Modal
			title="存储时长统计"
			visible
			onCancel={ () => {
				document.body.removeChild(insertDiv);
			} }
			onOk={ () => {
				document.body.removeChild(insertDiv);
			} }
		>
			<div>
				<div className="r-flex r-ta-c r-pd-16" style={ { background: "#f0f0f0" } }>
					<Flex style={ { flex: 1, alignItems: 'center', justifyContent: 'space-around' } }>
						<div className="r-c-999">当前账号存储上限</div>
						<div className="r-fs-24">{ dataStorageMonth }个月</div>
					</Flex>
				</div>
				<div className="r-flex r-mt-16 r-pd-16" style={ { border: "1px solid #f0f0f0", alignItems: "center", justifyContent: 'space-between' } }>
					<Flex>
						<div className=" r-mr-16"><Icon type="lianxikefu" className="r-c-666" style={ { fontSize: "24px" } } /></div>
						<div>
							<div className="r-fs-16 r-fw-b r-c-black">联系销售</div>
							<div className="r-c-999 r-mt-8">如需提高更多单量上限，请联系销售或直接购买</div>
						</div>
					</Flex>
					{
						[2].includes(userInfo?.level) ? <Button type="link" style={ { padding: 0 } } onClick={ () => showPayDrawer({ type }) }>前往订购</Button> : null
					}
				</div>
			</div>
		</Modal>,
		document.getElementById("limitDomId")
	);
};
showTips.shop = ({ tradeLimitNum, totalConsumeNum, type }) => {
	let insertDiv = document.createElement('div');
	const isLimit = tradeLimitNum > 0 && totalConsumeNum >= tradeLimitNum;
	insertDiv.id = 'limitDomId';
	const userInfo = session.get('erp_userInfo');
	document.body.appendChild(insertDiv);
	ReactDOM.render(
		<Modal
			title="店铺统计"
			visible
			onCancel={ () => {
				document.body.removeChild(insertDiv);
			} }
			onOk={ () => {
				document.body.removeChild(insertDiv);
			} }
		>
			<div>
				<div className="r-flex r-ta-c r-pd-16" style={ { background: "#f0f0f0" } }>
					<div style={ { flex: 1 } }>
						<div className="r-c-999">当前账号店铺上限</div>
						<div className="r-fs-24">{(+tradeLimitNum)?.toLocaleString()}</div>
					</div>
					<div style={ { flex: 1 } }>
						{
							!isLimit ? (
								<div>当前剩余</div>
							) : (
								<div style={ { color: "#f00" } }><WarningOutlined className="r-mr-4" style={ { color: "ff0" } } />已超出</div>
							)
						}
						<div className="r-fs-24">{
							!isLimit ? (
								<div>{(tradeLimitNum - totalConsumeNum)?.toLocaleString()}</div>
							) : (
								<div style={ { color: "#f00" } }>{(totalConsumeNum - tradeLimitNum)?.toLocaleString()}</div>
							)
						}
						</div>
					</div>
				</div>
				<div className="r-flex r-mt-16 r-pd-16" style={ { border: "1px solid #f0f0f0", alignItems: "center", justifyContent: 'space-between' } }>
					<Flex>
						<div className=" r-mr-16"><Icon type="lianxikefu" className="r-c-666" style={ { fontSize: "24px" } } /></div>
						<div>
							<div className="r-fs-16 r-fw-b r-c-black">联系销售</div>
							<div className="r-c-999 r-mt-8">如需提高更多单量上限，请联系销售或直接购买</div>
						</div>
					</Flex>
					{
						[1, 3].includes(userInfo?.level) ? <Button type="link" style={ { padding: 0 } } onClick={ () => showPayDrawer({ type }) }>前往订购</Button> : null
					}
				</div>
			</div>
		</Modal>,
		document.getElementById("limitDomId")
	);
};

export default showTips;
