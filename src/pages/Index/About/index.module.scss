/* 顶部导航 */
.appContainer {
    min-height      : 1024px;
    background-color: white;
}

.navBar {
    height         : 80px;
    display        : flex;
    align-items    : center;
    justify-content: space-between;
    border-bottom  : 1px solid #e5e7eb;
    max-width      : 1200px;
    margin         : 0 auto;
}

.navLogoContainer {
    display    : flex;
    align-items: center;
}

.navLogo {
    height: 32px;
}

.navLinks {
    margin-left: 80px;
    display    : flex;
    gap        : 36px;
}

.navLink {
    font-size      : 20px;
    color          : #333;
    text-decoration: none;
    cursor         : pointer;
}

.navLinkActive {
    color: #FD8204;
}

.navLink:hover {
    color: #FD8204;
}

.navButtons {
    display: flex;
    gap    : 16px;
}

.loginButton {
    border-radius: 4px;
}

.trialButton {
    background-color: #FD8204;
    border-radius   : 4px;
    white-space     : nowrap;
}

/* Hero区域 */
.heroSection {
    position: relative;
    height  : 720px;
    overflow: hidden;
}

.heroBg {
    position           : absolute;
    top                : 0;
    right              : 0;
    bottom             : 0;
    left               : 0;
    // background-size: cover;
    background-position: center;
    background-image   : url(./assets/banner.png);
}

.heroBgItem {
    position: absolute;
    right   : 60px;
    bottom  : -60px;
}

.heroGradient {
    position     : absolute;
    top          : 0;
    right        : 0;
    bottom       : 0;
    left         : 0;
    // background: linear-gradient(to right, rgba(249, 115, 22, 0.9), transparent);
}

.heroContent {
    position   : relative;
    height     : 100%;
    width      : 100%;
    display    : flex;
    align-items: center;
    padding    : 0 32px;
    max-width  : 1200px;
    margin     : 0 auto;
}

.heroTextContainer {
    color: white;
}

.heroTitle {
    font-size    : 54px;
    margin-bottom: 20px;
    color        : #fff;
}

.heroDescription {
    font-size    : 24px;
    margin-bottom: 30px;
}

.heroPlans {
    gap: 20px;
}

.heroPlan {
    background-color: #fff;
    padding         : 16px;
    border-radius   : 8px;
    width           : 510px;
    margin-top      : 24px;
}

.heroPlan1 {
    background: #fff url("./assets/bg2.png") no-repeat 240px bottom;
}

.heroPlan2 {
    background: #fff url("./assets/bg1.png") no-repeat 240px bottom;
}

.heroPlanTitle {
    font-size    : 24px;
    margin-bottom: 10px;
    color        : #333;
}

.heroPlanDesc {
    font-size    : 16px;
    margin-bottom: 20px;
    color        : #666;
}

.heroPlanButton {}

/* 核心优势 */
.coreAdvantages {
    padding: 60px 0;
}

.coreAdvantagesContainer {
    display        : flex;
    justify-content: center;
    gap            : 10px;
    max-width      : 1200px;
    margin         : 0 auto;
}

.coreAdvantageItem {
    flex         : 1;
    padding      : 16px;
    border-radius: 8px;
    transition   : all 0.3s ease;
    display      : flex;
    cursor       : pointer;

    &:hover {
        transform : translateY(-10px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
}

.advantageIcon {
    font-size   : 36px;
    color       : #FD8204;
    margin-right: 10px;
}

.advantageTitle {
    font-size    : 20px;
    margin-bottom: 10px;
}

.advantageDesc {
    color: #6b7280;
}


// 更新动画样式
.painSolutions,
.supportedPlatforms,
.productAdvantages,
.featuredFeatures {
    opacity   : 0;
    transform : translateY(30px);
    transition: all 1s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    &.visible {
        opacity  : 1;
        transform: translateY(0);
    }
}

/* 痛点解决方案 */
.painSolutions {
    padding         : 60px 0;
    text-align      : center;
    background-color: #FFFAF5;
}

.painSolutionsContainer {
    max-width: 1200px;
    margin   : 0 auto;
}

.sectionTitle {
    font-size    : 36px;
    font-weight  : bold;
    margin-bottom: 80px;
    position     : relative;

    &::after {
        content         : "";
        position        : absolute;
        bottom          : -10px;
        left            : 50%;
        transform       : translateX(-50%);
        width           : 80px;
        height          : 4px;
        background-color: #FFA64C;
    }
}

.solutionGrid {
    display              : grid;
    grid-template-columns: repeat(2, 1fr);
    gap                  : 70px;
}

.solutionItem {
    background-color: #fff;
    padding         : 16px;
    border-radius   : 8px;
    text-align      : left;
    display         : flex;
    box-shadow      : 0px 2px 15px 0px rgba(253, 203, 158, 0.5);
    transition      : all 0.3s ease;
    height          : 150px;
    cursor          : pointer;

    &:hover {
        transform: translateY(-10px);
    }
}

.solutionTitle {
    font-size    : 20px;
    margin-bottom: 10px;
    display      : flex;
    align-items  : center;
}

.solutionTitle i {
    color: #FD8204;
}

.solutionDesc {
    color    : #6b7280;
    font-size: 16px;
}

/* 支持平台 */
.supportedPlatforms {
    padding   : 60px 0;
    text-align: center;
}

.supportedPlatformsContainer {
    max-width: 1200px;
    margin   : 0 auto;
}

.platformGrid {
    display        : flex;
    justify-content: center;
    flex-wrap      : wrap;
    gap            : 30px;
}

.platformItem {
    width        : 80px;
    height       : 80px;
    border-radius: 50%;
    overflow     : hidden;
    box-shadow   : 0 0 10px rgba(0, 0, 0, 0.1);
}

.platformLogo {
    width     : 100%;
    height    : 100%;
    object-fit: cover;
}

/* 产品优势 */
.productAdvantages {
    padding         : 60px 0;
    text-align      : center;
    background-color: #FFFAF5;
}

.productAdvantagesContainer {
    max-width: 1200px;
    margin   : 0 auto;
}

.productAdvantageGrid {
    display              : grid;
    grid-template-columns: repeat(3, 1fr);
    gap                  : 70px;
}

.productAdvantageItem {
    flex            : 1;
    background-color: #fff;
    padding         : 16px 48px;
    border-radius   : 8px;
    box-shadow      : 0px 2px 15px 0px rgba(253, 203, 158, 0.5);
    transition      : all 0.3s ease;
    height          : 200px;
    cursor          : pointer;

    &:hover {
        transform: translateY(-10px);
    }
}

.productAdvantageIcon {
    font-size    : 36px;
    color        : #FD8204;
    margin-bottom: 20px;
}

.productAdvantageTitle {
    font-size    : 24px;
    margin-bottom: 10px;
}

.productAdvantageDesc {
    color     : #6b7280;
    font-size : 18px;
    text-align: left;
}

/* 特色功能部分 */
.featuredFeatures {
    padding   : 60px 0;
    text-align: center;
}

.featuredFeaturesTitle {
    font-size    : 36px;
    font-weight  : bold;
    margin-bottom: 30px;
}

.featuredFeaturesGrid {
    display        : flex;
    justify-content: center;
    align-items    : center;
    gap            : 30px;
}

.featuredFeaturesImgContainer {
    flex     : 1;
    position : relative;
    max-width: 1200px;
    margin   : 0 auto;
    padding  : 80px 0;
}

.featuredFeaturesImg {
    border-radius: 10px;
    max-width    : 100%;
}

.featuredFeaturesInfo {
    flex      : 1;
    text-align: left;
    z-index   : 2;
}

.featuredFeatureItem {
    background-color: #fff;
    border          : 1px solid #ddd;
    border-radius   : 2px;
    position        : absolute;
    padding         : 16px;
    transition      : all 0.3s ease;
    cursor          : pointer;

    &:hover {
        transform: translateY(-10px);
    }
}

.featuredFeatureItem0 {
    width: 500px;
    right: 150px;
    top  : 0;
}

.featuredFeatureItem1 {
    width: 440px;
    left : 0;
    top  : 50px;
}

.featuredFeatureItem2 {
    width: 384px;
    right: 0;
    top  : 210px;
}

.featuredFeatureItem3 {
    width : 368px;
    left  : 40px;
    bottom: 100px;
}

.featuredFeatureItem4 {
    width : 592px;
    right : 0;
    bottom: 40px;
}

.featuredFeatureTitle {
    font-size    : 24px;
    font-weight  : medium;
    margin-bottom: 10px;
    display      : flex;
    align-items  : center;
}

.featuredFeatureTitleIcon {
    width           : 8px;
    height          : 8px;
    display         : block;
    background-color: #FD8204;
    border          : 2px solid #FCE0C8;
    border-radius   : 4px;
}

.featuredFeatureTitle i {
    color       : #FD8204;
    margin-right: 10px;
}

.featuredFeatureDesc {
    color: #6b7280;
}

.functions {
    max-width            : 1200px;
    margin               : 20px auto 60px;
    display              : grid;
    grid-template-columns: repeat(7, 1fr);
    gap                  : 20px;

    :global {
        .ant-btn {
            height    : 68px;
            transition: all 0.3s ease;
            cursor    : pointer;
            font-size : 20px;

            &:hover {
                transform: translateY(-10px);
            }
        }
    }
}

/* 底部导航部分 */
.footer {
    background: linear-gradient(180deg, #FFB573 0%, #FF9537 100%);
    color     : white;
    padding   : 60px 0;
}

.footerContainer {
    max-width : 1200px;
    margin    : 0 auto;
    padding   : 0 30px;
    text-align: center;
}

.footerColumns {
    display        : flex;
    justify-content: space-between;
    gap            : 30px;
    margin-bottom  : 30px;
}

.footerColumn {
    flex: 1;
}

.footerColumnTitle {
    font-size    : 18px;
    font-weight  : medium;
    margin-bottom: 15px;
}

.footerColumnList {
    list-style: none;
    padding   : 0;
}

.footerLink {
    color          : #9ca3af;
    text-decoration: none;
    transition     : color 0.3s ease;
}

.footerLink:hover {
    color: white;
}

.footerContact {
    color: #9ca3af;
}

.footerCopyright {
    text-align : center;
    color      : #9ca3af;
    border-top : 1px solid #374151;
    padding-top: 30px;
}

.footerButton1 {
    border       : 2px solid #fff;
    color        : #FD8204;
    background   : #fff;
    width        : 240px;
    height       : 74px;
    font-size    : 36px;
    font-weight  : bold;
    border-radius: 4px;
}

.footerButton2 {
    margin-left  : 64px;
    border       : 2px solid #fff;
    color        : #fff;
    background   : none;
    width        : 240px;
    height       : 74px;
    font-size    : 36px;
    font-weight  : bold;
    border-radius: 4px;
}