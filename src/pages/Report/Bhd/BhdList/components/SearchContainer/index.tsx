import { But<PERSON>, Checkbox, Form, Select, Tooltip, Popover } from "antd";
import React, { useEffect, useMemo, useState, useRef } from "react";
import dayjs from "dayjs";
import { observer } from "mobx-react";
import { useToggle } from "ahooks";
import cs from 'classnames';
import { PlusSquareOutlined } from "@ant-design/icons";
import message from "@/components/message";
import s from './index.module.scss';
import { LabelGenerateStatus, checkValueIsEmpty } from "@/pages/Trade/components/SearchContainer/SearchCondition";
import { BHD_ORDER_STATUS_OPTIONS, BHD_REFUND_STATUS_OPTIONS, BHD_STALL_STATUS_OPTIONS, BHD_TIME_TYPE_OPTIONS } from "../../contants";
import { SELL_ATTR_OPTIONS, GOOD_STOCK_STATUS_OPTIONS, LABEL_GENERATOR_STATUS } from "@/pages/Trade/components/SearchContainer/constants";
import { ReportQueryBhdItemListRequest } from "@/types/schemas/report/bhd/bhdList";
import { getMultiShops } from "@/components-biz/ShopListSelect/shopListUtils";
import { DatePickerKey, getCacheDateRange } from "@/components/DateRangeComp/kdzsRangePickerUtil";
import { isBeyondInput } from "@/pages/Trade/utils";
import bhdStore from "@/stores/report/Bhd";
import { tradeStore } from "@/stores";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import userStore from '@/stores/user';
import Icon from "@/components/Icon";
import event from '@/libs/event';
import SearchRuleSort, { FromPageEnum } from "@/pages/Trade/components/SearchContainer/SearchRuleSort";
import { TradeQueryGatherParams, TradeSearchConditionConfig } from "@/types/trade/search/search";
import SearchItem, { IformStatusObj } from "./SearchItem";
import { IndexGetUserQueryConditionSettingApi, IndexUpdateUserQueryConditionSettingApi } from "@/apis/report/bhd/bhdList";
import { supplierOptionsEnum, supplierOptionsNotContainerEnum } from "./contants";
import { getRange, isRange, getRangeKg } from "@/pages/Trade/components/SearchContainer";
import { handleGoodsInfoSearchParams } from '@/pages/Trade/components/SearchContainer/itemInfoUtils';
import { getConditionList, handleSupplierIsIncluding } from "./utils";
import ExpressRegister from "./components/expressRegister";
import Flex from "@/components/Flex";
import PresetQueryNavBarCommon from '@/components/PresetQueryNavBarCommon';
import { BizTypeEnum } from '@/components/PresetQueryNavBarCommon/constants';
import { adjustTimeRange } from '@/pages/Trade/components/SearchContainer/utils';



const { Option } = Select;

export enum QueryConditionEnum {
	备货单查询条件 = "BHD_QUERY_CONDITION",
	备货单标签查询条件 = "BHD_LABEL_QUERY_CONDITION"
}

interface BhdListSearchContainerProps{
	handleSearch: (params:ReportQueryBhdItemListRequest) => void;
	loading: boolean;
	isStallBhd: boolean;
}
const BhdListSearchContainer:React.FC<BhdListSearchContainerProps> = observer((props) => {
	const { handleSearch, loading, isStallBhd } = props;
	const { showType, exNumberRecord } = bhdStore;
	const {
		goodsTagListStore: {
			getIsFirstSend
		}
	} = tradeStore;
	const { isShowZeroStockVersion, isShowKg } = userStore;
	// 是否开启了先发货模式
	const [preSendMode, setPreSendMode] = useState(false);
	const [form] = Form.useForm();
	// 自定义查询条件
	const [searchSortVisible, setSearchSortVisible] = useState<boolean>(false);
	const [conditionSet, setConditionSet] = useState<TradeSearchConditionConfig[]>([]);
	const [formData, setFormData] = useState<TradeQueryGatherParams>({});
	const [showAll, { toggle: toggleShowAll, set: setToggleShowAll }] = useToggle();
	const [formStatusObj, setFormStatusObj] = useState<IformStatusObj>({});
	const [settingModalVisible, setSettingModalVisible] = useState(false);

	const presetRef = useRef(null); // 预设查询条件

	useEffect(() => {
		const tids = bhdStore.generateReportBhdTids;
		if (tids) {
			bhdStore.setGenerateReportBhdTids('');
			handleFinish({ tids });
		}
	// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [bhdStore.generateReportBhdTids]);

	const searchInitValue = useMemo(() => ({
		timeType: BHD_TIME_TYPE_OPTIONS.default,
		searchTime: getCacheDateRange(DatePickerKey.report_bhd_list) || [dayjs().subtract(1, 'M').startOf('day'), dayjs().endOf('day')],
		status: BHD_ORDER_STATUS_OPTIONS.default,
		goodStockStatus: GOOD_STOCK_STATUS_OPTIONS.default,
		sellAttributeList: SELL_ATTR_OPTIONS.default,
		flagValue: '',
		// orderSource: userStore.isSupplierAccount && !isStallBhd ? '' : 'platfromOrder',
		orderSource: "",
		refundStatus: BHD_REFUND_STATUS_OPTIONS.default,
		labelGenerateStatus: LABEL_GENERATOR_STATUS.default,
		hasMarketStall: BHD_STALL_STATUS_OPTIONS.default,
		goodsIncludeStatus: '1',
		goodsNotIncludeStatus: '0',
		bizMarkObj: {
			value: '',
			bizMark: '',
		},
		customizeResidueSendTime: "",
		supplierIsIncluding: supplierOptionsEnum.供应商包含,
		supplierNotIncluding: supplierOptionsNotContainerEnum.供应商不包含,
	}), [showType, isStallBhd, userStore.isSupplierAccount]);

	useEffect(() => {
		let type = QueryConditionEnum.备货单查询条件;
		if (window.location.hash.indexOf('stall') > -1) {
			type = QueryConditionEnum.备货单标签查询条件;
		}
		const handleFn = async() => {
			let res;
			try {
				res = await IndexGetUserQueryConditionSettingApi({ type });
			} catch (error) {
				console.log('error: ', error);
			}
			const newCondition = getConditionList(res);
			console.log('%c [ 自定义查询条件设置 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', newCondition);
			setConditionSet(newCondition);
		};
		handleFn();
	}, []);


	useEffect(() => {
		form.setFieldsValue(searchInitValue);
	// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [searchInitValue]);


	useEffect(() => {
		getIsFirstSend().then((res) => {
			if (res) setPreSendMode(res);
		});
	}, []);

	const verifySearchParams = (params:any, isShowKg:boolean): boolean => {
		const supplierMax = 20;
		const [startTime, endTime] = params.searchTime;
		if (isBeyondInput(params.goodsAliasOrIdStr, 50)) {
			message.error(`同时查询 货品简称/编码 的数量不能超过50个`);
			return false;
		}
		if (isBeyondInput(params.goodsSkuNameOrIdOrAliasStr, 50)) {
			message.error(`同时查询 货品规格名称/编码/别名 的数量不能超过50个`);
			return false;
		}
		if (isBeyondInput(params.shortNameIncluding, 50)) {
			message.error(`同时查询 商品名称/简称/商品编码/id 的数量不能超过50个`);
			return false;
		}
		if (isBeyondInput(params.skuIncluding, 50)) {
			message.error(`同时查询 规格名称/编码/别名 的数量不能超过50个`);
			return false;
		}
		if (!isRange(params.orderRange, [1, 99999])) {
			message.error('订单数量输入超过限制，请调整数值在1-99999之间');
			return false;
		}
		if (!isRange(params.goodsTotalNumRange, [1, 99999])) {
			message.error('商品数量输入超过限制，请调整数值在1-99999之间');
			return false;
		}
		if (!isRange(params.goodsTypeNumRange, [1, 99999])) {
			message.error('商品种类输入超过限制，请调整数值在1-99999之间');
			return false;
		}
		if (!startTime && !endTime) {
			message.error("查询时间不能为空");
			return false;
		}
		if (!startTime) {
			message.error("开始时间不能为空");
			return false;
		}
		if (!endTime) {
			message.error("结束时间不能为空");
			return false;
		}
		// 单独校验kg重量数值范围isShowKg
		const { weightRange } = params || {};
		if (isShowKg && !isRange(weightRange, [0.000001, 99])) {
			message.error('订单重量(单位Kg)输入超过限制，请调整数值在0.000001-99之间');
			return false;
		}
		if (!isShowKg && !isRange(weightRange, [0.001, 99999])) {
			message.error('订单重量(单位g)输入超过限制，请调整数值在0.001-99999之间');
			return false;
		}
		return true;
	};
	const handleSubmit = () => {
		setSettingModalVisible(false);
		form.setFieldsValue({ includeYdNoRecord: ['includeYdNoRecord'] });
		setFormData({ ...formData, includeYdNoRecord: ['includeYdNoRecord'] });
		form.submit();
	};
	const handleFinish = async(otherParams) => {
		// 这里带上所有的条件= 表单 + 未回显的预设
		const ruleContent = presetRef?.current?.getCurRuleContent || {};
		console.log('%c [ ruleContent ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', ruleContent);
		const v = { ...ruleContent, ...form.getFieldsValue() };
		if (!verifySearchParams(v, isShowKg)) {
			return;
		}

		let flagValue;
		let flagSelValue;
		if (v.flagValue?.includes('_')) {
			flagSelValue = v.flagValue;
		} else {
			flagValue = v.flagValue;
		}
		const { plats, plat_sellerIds } = v.platformInfo || {};
		let multiShops = await getMultiShops({ plats, plat_sellerIds, hasHand: true });
		const includePending = !!v.includePending?.includes('includePending');
		const params = {
			searchType: isStallBhd ? 1 : 0, // 0:表示备货单搜索, 1:表示备货单标签搜索
			multiShopS: multiShops || [],
			isPlatformEmptyQuery: !plats?.length && !plat_sellerIds?.length,
			startTime: v.searchTime ? v.searchTime[0].format("YYYY-MM-DD HH:mm:ss") : '',
			endTime: v.searchTime ? v.searchTime[1].format("YYYY-MM-DD HH:mm:ss") : '',
			timeType: v.timeType,
			status: v.status,
			goodStockStatus: v.goodStockStatus,
			ptTid: v.ptTid,
			tid: v.tid,
			sid: v.sid,
			buyerNick: v.buyerNick,
			buyerMessageOrSellerMemo: v.buyerMessageOrSellerMemo || '',
			sellAttributeList: v.sellAttributeList || [],
			includePending,
			includePreShip: !!v.includePreShip?.includes('includePreShip'),
			includeYdNoRecord: !!v.includeYdNoRecord?.includes('includeYdNoRecord'),
			noEpidemicArea: !!v.noEpidemicArea?.includes('noEpidemicArea'),
			refundStatus: v.refundStatus,
			hasMarketStall: v.hasMarketStall,
			orderSource: v.orderSource,
			queryFirstSend: v.firstSend,
			queryWaitSendOrFirstSend: v.waitSendOrFirstSend,
			includeArrivalOfGoodsAndStockpile: v.includeArrivalOfGoodsAndStockpile,
			// itemVision: showType === BhdShowType.平台商品,
			labelGenerateStatus: v.labelGenerateStatus,
			queryVision: showType,
			printStatus: checkValueIsEmpty(v.printStatus),
			// 留言备注查询
			flagValue,
			flagSelValue,
			buyerMessage: v.buyerMessage,
			sellerMemo: v.sellerMemo,
			sellerFlag: v.sellerFlag,
			// 省市区查询
			selValue: v.addressInfo?.selValue,
			areaJson: v.addressInfo?.areaJson,
			areaContain: v.addressInfo?.areaContain,
			customizeResidueSendTime: v.customizeResidueSendTime,
			// 订单标记
			bizMark: v.bizMarkObj?.bizMark || '',
			bizMarkContain: v.bizMarkObj?.bizMarkContain,
			// 订单类型
			tradeTypeTagList: v?.tradeTypeTagList ? [v.tradeTypeTagList] : [],
			// 订单标签
			tradeLabelList: v.tradeLabelList,
			// 订单异常
			tradeExceptionList: v.tradeExceptionList,
			// 商品种类
			goodsTypeNum: getRange(v.goodsTypeNumRange),
			// 订单金额
			payment: getRange(v.paymentRange),
			// 订单重量
			weightRange: getRangeKg(v.weightRange, isShowKg), // 根据用户重量isShowKg配置调整参数
			// 商品数量
			goodsTotalNum: getRange(v.goodsTotalNumRange),
			authorIncludingList: v.authorIncludingList?.map(i => i.trim()),
			distributorUserIds: v?.distributorUserIds,
			storageIdList: v?.storageIdList,
			existItemAlias: v?.existItemAlias, // 有无设置简称
			existSkuAlias: v?.existSkuAlias, // 有无设置规格别名
			existWeight: v?.existWeight, // 有无设置重量
			existCostPrice: v?.existCostPrice, // 有无设置成本价
		};
		handleGoodsInfoSearchParams(v, params);
		// 是否包含供应商市场档口参数
		handleSupplierIsIncluding(v, params);
		if (otherParams.tids) {
			params.tid = otherParams.tids;
		}
		bhdStore.setSearchParams({
			...bhdStore.searchParams,
			tradeStatus: v.status,
			queryFirstSend: v.firstSend,
			queryWaitSendOrFirstSend: v.waitSendOrFirstSend,
			includePending,
			includeArrivalOfGoodsAndStockpile: v.includeArrivalOfGoodsAndStockpile,
			// itemVision: showType,
		});
		handleSearch(params);
	};

	const onChangeFistSend = (e:any) => {
		const _checked = e.target.checked;
		setFormStatusObj((prev) => {
			return {
				...prev,
				firstSendSelect: _checked
			};
		});
		if (_checked) {
			sendPoint(Pointer.备货单_本系统先发货订单);
			form.setFieldsValue({
				status: "WAIT_SELLER_SEND_GOODS,WAIT_BUYER_CONFIRM_GOODS,SELLER_CONSIGNED_PART",
				sellAttributeList: SELL_ATTR_OPTIONS.default,
				waitSendOrFirstSend: false,
				refundStatus: BHD_REFUND_STATUS_OPTIONS.default,
			});
			setFormStatusObj((prev) => {
				return {
					...prev,
					waitSendOrFirstSend: false
				};
			});
		} else {
			form.setFieldsValue({
				status: BHD_ORDER_STATUS_OPTIONS.default,
			});
		}
		bhdStore.setSearchParams({
			...bhdStore.searchParams,
			queryFirstSend: _checked,
		});
	};

	const onChangeWaitSendOrFirstSend = (e) => {
		const checked = e.target.checked;
		setFormStatusObj((prev) => {
			return {
				...prev,
				waitSendOrFirstSend: checked
			};
		});
		if (checked) {
			sendPoint(Pointer["备货单_待发货+先发货订单"]);
			form.setFieldsValue({
				status: "WAIT_SELLER_SEND_GOODS,WAIT_BUYER_CONFIRM_GOODS,SELLER_CONSIGNED_PART",
				sellAttributeList: SELL_ATTR_OPTIONS.default,
				firstSend: false,
				refundStatus: BHD_REFUND_STATUS_OPTIONS.default,
			});
			setFormStatusObj((prev) => {
				return {
					...prev,
					firstSendSelect: false
				};
			});
		} else {
			form.setFieldsValue({
				status: BHD_ORDER_STATUS_OPTIONS.default,
			});
		}
	};
	// 更新用户查询条件
	const handleSearchRuleSortOK = (queryConditionDTOList: TradeSearchConditionConfig[]) => {
		console.log("handleSearchRuleSortOK queryConditionDTOList:", queryConditionDTOList);
		setConditionSet(queryConditionDTOList);
		const params = {
			type: isStallBhd ? QueryConditionEnum.备货单标签查询条件 : QueryConditionEnum.备货单查询条件,
			queryConditionList: queryConditionDTOList
		};
		IndexUpdateUserQueryConditionSettingApi(params).then((res) => {
			console.log('IndexUpdateUserQueryConditionSettingApi res:', res);
		});
		setSearchSortVisible(false);
	};
	const handleSetSearchRules = async() => {
		setSearchSortVisible(true);
	};
	const onFormValueChange = (changedValues:{}, allValues) => {
		console.log('%c [ changedValues ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', changedValues);
		// 如果留言备注更改了，清空备注或留言的输入框内容
		if ('flagValue' in changedValues) {
			form.setFieldsValue({
				buyerMessage: '',
				sellerMemo: ''
			});
		}
		setFormData(allValues);
	};
	const setVisible = (val) => {
		setSettingModalVisible(val);
	};

	// 保存预设查询条件
	const savePresetQueryCondition = () => {
		presetRef?.current?.savePreCondition();
	};

	// 表单重置后取消预设查询条件查询的选中
	const handleReset = () => {
		console.log('表单已重置');
		// 取消预设查询条件查询的选中
		presetRef?.current?.reset();
	};

	// 处理预设查询条件信息回填 isSubmit: 是否开启查询 v: ruleContent
	const handlePreConditionInfo = (data:any, isSubmit = true) => {
		const _formData = form?.getFieldsValue();
		let newData = { ...data };
		
		// 这里处理一些参数的转换
		// 且放弃对老的参数的兼容，如果改过查询条件将不能回显并查询
		if (data) {
			// 单独的转换
			// if (data.endTime) {
			// 	_formData.searchTime[1] = data.endTime;
			// } else if (data.payEndTime) {
			// 	const result = adjustTimeRange(new Date(), _formData.searchTime[0], _formData.searchTime[1], data.payEndTime);
			// 	_formData.timeType = "2";
			// 	if (result?.startTime && result?.endTime) {
			// 		_formData.searchTime[0] = dayjs(result.startTime);
			// 		_formData.searchTime[1] = dayjs(result.endTime);
			// 	}
			// }

			// newData.searchTime = _formData.searchTime;
			// newData.timeType = _formData.timeType;
		}

		handlePresetReset();

		// 这里不需要展开
		// setToggleShowAll(true);

		setTimeout(() => {
			setFormData(newData);
					
			form.setFieldsValue(newData);

			if (isSubmit) {
				setTimeout(() => {
					form.submit(); // handleFinish方法会合并当前激活的预设查询条件
				}, 200);
			}
		}, 100);
	};

	// 取消预设查询条件查询，初始化搜索栏
	const handlePresetReset = () => {
		form.resetFields();
	};

	return (
		<div>
			<Form
				form={ form }
				onFinish={ handleFinish }
				size="small"
				className=""
				initialValues={ searchInitValue }
				onValuesChange={ onFormValueChange }
				onReset={ handleReset } // 添加 onReset 事件监听
			>
				<div className={ cs("r-flex", "r-fw-w", "r-ai-fs", s.searchContainer) }>
					{/* 自定义查询条件 */}
					<SearchItem conditionSet={ conditionSet } formData={ formData } showAll={ showAll } formStatusObj={ formStatusObj } />

					{/* 只有备货单标签有 */}
					{
						isStallBhd ? (
							<Form.Item className={ s.condition1 } name="labelGenerateStatus" >
								<LabelGenerateStatus />
							</Form.Item>
						) : null
					}
					{
						isShowZeroStockVersion
							? (
								<Form.Item className={ s.condition1 } name="hasMarketStall" >
									<Select >
										{BHD_STALL_STATUS_OPTIONS.options.map(item => (
											<Option value={ item.key } key={ item.key } >{item.value}</Option>
										))}
									</Select>
								</Form.Item>
							) : null
					}

					<Form.Item name="includePending">
						<Checkbox.Group>
							<Checkbox value="includePending" >包括挂起订单</Checkbox>
						</Checkbox.Group>
					</Form.Item>

					<Form.Item name="includePreShip">
						<Checkbox.Group>
							<Checkbox value="includePreShip" >包括自动发货订单</Checkbox>
						</Checkbox.Group>
					</Form.Item>

					<Popover placement="top" title={ <div className={ s["popover-title"] }><span>适用场景：</span></div> } content={ <div style={ { width: "350px" } }>快销类客户，家里不备库存，卖多少采购多少。货品采购回仓库，打印快递单发货，商品未配齐快递单，可进行表格登记缺货，后一天继续下单采购。</div> }>
						<Flex alignItems="center">
							<Form.Item name="includeYdNoRecord">
								<Checkbox.Group>
									<Checkbox value="includeYdNoRecord" >包括</Checkbox>
								</Checkbox.Group>
							</Form.Item>
							<Flex alignItems="center" className="ant-row ant-form-item" style={ { cursor: 'pointer', marginLeft: '-12px' } } onClick={ (e) => { e.stopPropagation(); setSettingModalVisible(true); } }>
								(&nbsp;<span className={ s["exNumber"] }>快递缺货登记</span><PlusSquareOutlined className="r-c-warning" />&nbsp;)
							</Flex>
						</Flex>
					</Popover>

					{
						isStallBhd && (
							<Form.Item name="includeArrivalOfGoodsAndStockpile" valuePropName="checked" >
								<Checkbox>包含已配货、已到货、已囤货商品</Checkbox>
							</Form.Item>
						)
					}

					{
						isStallBhd && preSendMode && (
							<Form.Item name="firstSend" valuePropName="checked">
								<Checkbox onChange={ onChangeFistSend }>本系统先发货订单</Checkbox>
							</Form.Item>
						)
					}

					{
						isStallBhd && preSendMode && (
							<div className="r-flex">
								<Form.Item name="waitSendOrFirstSend" valuePropName="checked" style={ { marginRight: 0 } }>
									<Checkbox onChange={ onChangeWaitSendOrFirstSend }>待发货+先发货订单</Checkbox>
								</Form.Item>
								<Tooltip title="支持待发货与先发货订单同时生成拿货标签">
									<span style={ { color: "#ccc", paddingTop: 2 } }>
										<Icon type="wenhao-xian" size={ 15 } />
									</span>
								</Tooltip>
							</div>

						)
					}

					<Form.Item >
						<Button type="link" onClick={ () => { toggleShowAll(); } }>{showAll ? '默认查询条件' : '所有查询条件'}</Button>
						<Button type="link" onClick={ handleSetSearchRules }>自定义查询条件</Button>
					</Form.Item>

					<Form.Item >
						<Button type="primary" htmlType="submit" loading={ loading } className="kdzs-search-btn r-mr-8">查询</Button>
						<Button htmlType="reset" className="kdzs-search-btn">重置</Button>
					</Form.Item>

					<Form.Item >
						<Button className="r-ml-8" onClick={ () => savePresetQueryCondition() }>保存为预设条件</Button>
					</Form.Item>
				</div>
			</Form>

			{
				settingModalVisible && <ExpressRegister settingModalVisible={ settingModalVisible } setVisible={ setVisible } handleSubmit={ handleSubmit } />
			}

			{
				searchSortVisible && (
					<SearchRuleSort
						visible={ searchSortVisible }
						searchConditions={ conditionSet }
						handleOk={ handleSearchRuleSortOK }
						handleCancel={ () => { setSearchSortVisible(false); } }
						fromPage={ FromPageEnum.备货单 }
					/>
				)
			}

			{/* 预设查询条件 */}
			<PresetQueryNavBarCommon
				isSearching={ loading }
				handlePreConditionInfo={ handlePreConditionInfo }
				conditionSet={ conditionSet }
				handlePresetReset={ handlePresetReset }
				form={ form }
				ref={ presetRef }
				type={ isStallBhd ? BizTypeEnum.备货单标签查询 : BizTypeEnum.备货单查询 }
			/>
		</div>
	);
});

export default BhdListSearchContainer;
