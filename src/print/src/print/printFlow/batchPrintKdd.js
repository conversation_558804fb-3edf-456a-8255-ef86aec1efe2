import * as printItem from '../printItem';
import model from './../../common/model.ts';
import { previewConfig, previewConfig_plat } from '../config';
import { printWithEncryptPackages, printFhdEncrypt } from './crypto';
import { platformConfig,isJDNewWjTpl } from './platformConfig';
import { get, post } from './../../common/http';
import {deepClone} from '../../print/printItem/common';
import urlHelper from '../../common/urlhelp.js';
import { printWithEncryptOrder, printEncryptDYNick } from './printEncrypt'
import axios from 'axios';
import concatPrintDpd from './concatPrintDpd';

class PrintKdd {

    constructor(hook) {
        const {
            afterCheckComponentHook,
            afterChooseTempHook,
            afterChooseFjrHook,
            afterGetWaybillCodeHook,
            afterSavePrintMarkHook,
            afterSavePrintLogHook,
            afterBranch<PERSON><PERSON>e<PERSON><PERSON>,
            httpWhite<PERSON>ist<PERSON>ook,
            afterContinuePrintHook,
            notifyPrintHook,
            afterGetElecErrorHook,
            afterAutoAddPrintNum,
            afterShipFirstGetWaybillCodeHook
        } = hook || {};
        afterCheckComponentHook && (this.afterCheckComponentHook = afterCheckComponentHook);
        afterChooseTempHook && (this.afterChooseTempHook = afterChooseTempHook);
        afterChooseFjrHook && (this.afterChooseFjrHook = afterChooseFjrHook);
        afterGetWaybillCodeHook && (this.afterGetWaybillCodeHook = afterGetWaybillCodeHook);
        afterShipFirstGetWaybillCodeHook && (this.afterShipFirstGetWaybillCodeHook = afterShipFirstGetWaybillCodeHook);
        afterSavePrintMarkHook && (this.afterSavePrintMarkHook = afterSavePrintMarkHook);
        afterSavePrintLogHook && (this.afterSavePrintLogHook = afterSavePrintLogHook);
        afterBranchChangeHook && (this.afterBranchChangeHook = afterBranchChangeHook);
        httpWhiteListHook && (this.httpWhiteListHook = httpWhiteListHook);//把用户加入 http 白名单的钩子（加入白名单后，用户下次进入系统就直接是 http 环境了）
        afterContinuePrintHook && (this.afterContinuePrintHook = afterContinuePrintHook);
        notifyPrintHook && (this.notifyPrintHook = notifyPrintHook);
        afterGetElecErrorHook && (this.afterGetElecErrorHook = afterGetElecErrorHook);
        afterAutoAddPrintNum && (this.afterAutoAddPrintNum = afterAutoAddPrintNum);
        this.timer = window.timer
        this.printTraceId = ''
    }



    /**
     * 开始打印
     * @param {Object} data
     * 模板信息，可以不传
     * 订单数据
     * 用户信息（考虑初始化的时候传，这时候就可以不传了）
     * 打印
     */
    async print(data) {
        console.info('批打快递单 传给打印中心的数据', data);
        this.printTraceId = printItem.generatePrintInfo({printSource:'batch',isMerge:false})
        this.timer.start('打印前置处理')
        const userInfo = comp.Print.Data.userInfo;
		const advancedSetting = window.erpData?.advancedSetting
        let isShowMorePrinter = advancedSetting?.multiPrinterConfig == '1';
        const printType = 'kdd';
        let isConcatXbq = advancedSetting?.showGoodsTag == '2'
        let {
            templateInfo,
            orderList,
            printOp,
            // ,shopNewPrintNum
            fjrInfoMap,
            fhdFjrInfoMap,
            printStyle,
            platformId, // 业务方传来的平台id
            firstSendMode,
            scanFjrInfo,
            choosedPrinter:scanChoosedPrinter,
            ...othersSet
        } = data;
        this.data = {
            printOp,
            printType,
        };
		printItem.uploadPrintLog('single', {
			orderList: orderList,
			fjrInfoMap: fjrInfoMap,
			printTraceId: this.printTraceId,
			templateInfo: templateInfo,
		})
        const isKs = ['ksshop', 'ksshopg', 'ksshop-pre'].includes(comp.Print.Data.platform)
        if (isKs) {
            //非快手面单跳过字段(enableNonKsDzmd):默认0，当为1时，才显示弹框上的“本次继续打印”按钮
            const {
                ksDzmdConfig,
            } = othersSet
            comp.Print.Data.ksDzmdConfig = ksDzmdConfig;
        }
        let originalOrderList = [...orderList]
        let systemSetting = window?.erpData?.systemSetting || {}
        let xbqTempisInvalid = false
        let xbqTemplateDetailInfo = null
        let printTagSet = JSON.parse(systemSetting?.printTagSet || '{}');
        comp.Print.Data.printTagSet = printTagSet
        comp.Print.Data.fjrInfoMap = fjrInfoMap;
        comp.Print.Data.platformId = platformId || '147317';
        // 获取传入的其他配置
        let {
            canUseNewWaybillCodeSet, //可以使用新单号
            canChooseWaybillCodeNumSet, // 可以选择单号数量
            choosePrinterNotiSet, // 可以弹出选择打印机
            displayPrintNumSet, // 可以选择打印机打印份数
            // openPrintNotifySet, // 出纸进度
            showCombinationInfo, // 打印子货品
        } = othersSet;
        comp.Print.Data.showCombinationInfo = showCombinationInfo

        // 先发货默认一个单号
        if (firstSendMode) {
            canChooseWaybillCodeNumSet = false
        }
        // 如果没有传入模板信息，则弹出模板选择框
        if (!templateInfo || Object.keys(templateInfo).length === 0) {
            // eslint-disable-next-line require-atomic-updates
            templateInfo = await printItem.chooseTemplateDialog();
            if (!templateInfo) {
                return false;
            }
            // 提供出一个钩子，供外部选择是否要继续打印
            const result = this.afterChooseTempHook(templateInfo.Mode_ListShowId, templateInfo);
            if (result && result.isBreak === true) {
                return;
            }
        }
        // 检查吊牌交替打印模板是否存在
        if(isConcatXbq){
            if(printTagSet?.tagTemplateSwitch == '1'){
                let temps = comp.Print.Data.bqTemplateListdpd.ModeListShows.find((item) => {
                    return printTagSet?.tagTemplate.Mode_ListShowId === item.Mode_ListShowId
                })
                xbqTempisInvalid = !temps
                if(temps){
                    // 有效的话先获取一下小标签的模板详情，需要拿到绑定打印机
                    // 获取模板详情
                     xbqTemplateDetailInfo = await printItem.getTempDetailInfo({
                        printType:temps.Modeid,
                        templateId: temps.Mode_ListShowId,
                        isHideloading:true
                    });
                }
            }else{
                xbqTempisInvalid = true

            }

        }
        // TODO 临时限制，小红书即将强制性使用小红书平台模板，非小红书模板强行提醒

        console.info('模板已选中',templateInfo);
        //针对网点面单订单未解密临时处理过滤
        if(templateInfo.KddType == 2 && !(comp.Print.Data.showAutoDecode && advancedSetting?.printSetExpandDTO?.branchDzmdAutoDecode)){
        orderList =  await printItem.checkoutIsDecrypted({
                temp:templateInfo,
                orderList,
                afterContinuePrintHook:this.afterContinuePrintHook
            })
        }
		if(templateInfo.KddType == 17 || isJDNewWjTpl(templateInfo)){
			// 得物模板只能申请一个单号
            canChooseWaybillCodeNumSet = false
            canUseNewWaybillCodeSet = false
		}

        // 检查一下模板有没有禁止打印,有没有无需物流发货，有没有https下韵达网点打印
        await printItem.checkTemplate(templateInfo, this.httpWhiteListHook, data);
        console.info('模板是否可打印检查');
        // 获取模板详情
        const templateDetailInfo = await printItem.getTempDetailInfo({
            printType,
            templateId: templateInfo.Mode_ListShowId,
        });
        console.info('获取模板详情', templateDetailInfo);

        //代收货款只针对女装网和开放平台
        const codAndSourceFlag = ['nzw', 'openmalltest', 'openmall', 'open'].includes(comp.Print.Data.platform);
        /**
         * 订单过滤判断：2、选择cod订单与模板服务（代收货款templateDetailInfo.ModeLogisticsItems）不匹配:---start...
         */
        if (codAndSourceFlag) {
            //判断快递模板是否为代收货款
            //不是网点
            const isCod_other = templateDetailInfo?.ModeLogisticsItems?.some(item => {
                return item.serviceCode === 'SVC-COD';
            });
            // 顺丰-网点
            const isCod_sf = templateDetailInfo?.ModeAdvancedServices?.some(item => {
                return item.serviceKey === 'dshk';
            });
            // 圆通网点
            const isCod_yt = templateDetailInfo?.ModeCustomerTemplate?.Svctypecode === '代收货款';

            // 京东快递
            const isCod_jd = templateDetailInfo?.ModeServiceItems?.some(item => {
                return item.Dataname === 'dsje';
            });

            //快递模版不是代收货款模板 进入过滤判断
            if (!(isCod_other || isCod_sf || isCod_yt || isCod_jd)) {
                //过滤订单:筛选不是代收货款订单
                const notCodOrderList = orderList?.filter(item => {
                    return !item.isCOD;
                });
                //全部订单不匹配:所有订单为代收货款订单
                if (!notCodOrderList?.length) {
                    model({
                        type: 'confirm',
                        content: `存在代收货款订单，请使用含有代收货款模板打单发货！`,
                        width: 426,
                        height: 200,
                        okName: '确定',
                        cancelHide: true,
                    });
                    return;
                }
                //部分订单不匹配：部分订单为代收货款
                if (notCodOrderList?.length > 0 && notCodOrderList?.length < orderList?.length) {
                    await new Promise((resolve) => {
                        const modelConfig = {
                            type: 'confirm',
                            content: `存在代收货款订单，是否跳过代收货款订单继续打印？`,
                            width: 426,
                            height: 200,
                            okName: '是，继续打印',
                            cancelName: '取消',
                            okCb: () => {
                                //过滤打印数据
                                orderList = [...notCodOrderList];
                                resolve();
                            },
                            cancelCb: () => {
                                return false;
                            },
                        };
                        model(modelConfig);
                    });
                }
            }

            /**
             * 过滤2:选择cod订单与模板服务（代收货款templateDetailInfo.ModeLogisticsItems）不匹配---end
             */
        }

        /*start--可以放在 checkTemplate 方法里面写*/
        // 判断这 是否是抖音 放心购
        if (['fxg'].includes(platformConfig.platform)) {
            // 丰网速运下线提示
            if (templateDetailInfo.ModeCustomerTemplate.Svctypecode == '丰网速运' && templateDetailInfo.ModeList.Excode == 'SF') {
                model({
                    type: 'confirm',
                    content: '顺丰丰网速运服务已下线，请您重新创建丰网速运电子面单模板打单发货',
                    width: 426,
                    height: 200,
                    okName: '去创建',
                    okCb: async () => {
                        const that = new comp.Print.FN();
                        that.addKdd(function (msg) {
                            comp.print.resources.showkddMain(msg);
                        }, { exCode: 'FENGWANG' });
                    },
                });
                return;
            }
        }
        /* end --可以放在 checkTemplate 方法里面写*/
        console.assert(templateDetailInfo, `获取模板详情错误`);
        let isConcatFhd = false;
        // 判断是否需要发货单快递单交替打印
        if (templateInfo.KddType != '1' && templateDetailInfo.ModeTempPrintcfg.IsConcatFhd && !isShowMorePrinter) {
            // 判断是否存在可交替打印的发货单
            const fhdTemplateList = new comp.Print().getFhdTemplateList(false);
            let fhdId = templateDetailInfo.ModeListShow.rotationUserTemplateId
            let fhdIsDelete = fhdTemplateList.ModeListShows.find(o=>o.Mode_ListShowId == fhdId)
            if(!fhdId || !fhdIsDelete){
                // 未绑定发货单或者发货单已被删除
                Tatami.showFail('交替打印未绑定正确的热敏发货单')
            }else{
                comp.Print.Data.hasNoRM_common = !(fhdTemplateList.ModeListShows || []).some(item => item.Exid == 666);
                comp.Print.Data.hasNoRM_yilian = !(fhdTemplateList.ModeListShows || []).some(item => item.Exid == 670);
                // const isYilianTemp = comp.base.isYilianTemp(templateInfo.styleId);
                const isYilianTemp = templateDetailInfo?.ModeList?.WidthPaper === 760;
                const tempType = isYilianTemp ? 'yilian' : 'common';
                // 1.本模板是一联模板，并且一联热敏发货单存在
                // 2.本模板是普通模板，并且普通热敏发货单存在（目前非一联模板就是普通模板，2019.9）
                if ((isYilianTemp && !comp.Print.Data.hasNoRM_yilian) || (!isYilianTemp && !comp.Print.Data.hasNoRM_common)) {
                    isConcatFhd = true;
                    new comp.Print().setIsConcatFhd(tempType, true);
                }
            }

        }
        let installResult = null
        if (!firstSendMode) {
            installResult = await printItem.checkPrintComponent(templateInfo, printType);
            console.info(templateInfo);
            // 检查打印组件：用哪个，并检查准备好了没有
            console.info('控件检测结果', installResult);
            this.afterCheckComponentHook(installResult);
            if (!installResult.isSuccess) {
                this.timer.stop('打印前置处理',{
                    type:'batchKdd',
                    printTraceId:this.printTraceId
                })
                return;
            }
            // 根据模板设置使用的空间类型判断抖音或者菜鸟是否需要校验lodop
            let isHaveLodopSet = false
            if([3,8].includes(Number(templateInfo.KddType))){
                 isHaveLodopSet = templateInfo.rotationControlType === '1'
            }
            if ((isHaveLodopSet || [7,9,14].includes(Number(templateInfo.KddType))) && isConcatFhd || (isConcatXbq && !xbqTempisInvalid)) {
                installResult = printItem.checkLodopProgress();
                if (!installResult.isSuccess) {
                    this.timer.stop('打印前置处理',{
                        type:'batchKdd',
                        printTraceId:this.printTraceId
                    })
                    return;
                }
            }
        }
        const printClient = firstSendMode ? '' : installResult?.type;
        console.assert(printClient, '未获取打印要使用的客户端');

        // 第二次检查一下模板能否打印 是否有退款
        // erp平台检查能否打印
        await printItem.checkCanPrint({
            printType,
            templateInfo,
            orderList,
            printOp,
            firstSendMode
        });

        // 拼多多有绑定发件人的功能
        const bindFjrId = ['pdd', 'supplier', 'erp'].includes(comp.Print.Data.platform) ? templateDetailInfo.ModeListShow.bindFjrId : undefined;
        let fjrInfo = null
        if(!scanFjrInfo){
            const { fjrInfo:kddFjrInfo, modifyDefaultFjr } = await printItem.chooseFjr(fjrInfoMap, printType, bindFjrId, data);
            modifyDefaultFjr.length && this.afterChooseFjrHook(modifyDefaultFjr);
            fjrInfo = kddFjrInfo
        }else{
            fjrInfo = scanFjrInfo
        }
        // 获取本次打印使用的的发件人
        printItem.setTradeFjr({ orderList, fjrInfo, printType });

        let fhdFjrInfo;
        if (isConcatFhd) {
            ({ fjrInfo: fhdFjrInfo } = await printItem.chooseFjr(fhdFjrInfoMap, 'fhd',null,{orderList}));
        }

        // 检查一下发件人省市区（大头笔）有没有
        await printItem.checkFjrAddress(fjrInfo);

        // 获取大头笔，快递五连单需要
        // eslint-disable-next-line require-atomic-updates
        orderList = await printItem.getBigHead(
            templateInfo,
            orderList,
            printOp,
        );
        console.assert(Array.isArray(orderList) && orderList?.length > 0, 'orderList处理错误');

        /**
         * 获取打印时必要的数据信息
         * 例如极兔（龙邦）模板需要获取商品信息
         * @param templateInfo
         * @returns {Object}
         */

        const otherPrintInfo = await printItem.getOtherPrintInfo(templateDetailInfo);
        console.assert(otherPrintInfo, 'otherPrintInfo');
        this.timer.stop('打印前置处理',{
            type:'batchKdd',
            printTraceId:this.printTraceId
        })

        /**
         * 快递单号获取逻辑,重打提示框，多单号选择框，子母件选择框
         * @param {Object} orderList
         * @param {Object} templateInfo
         * @param {Object} templateDetailInfo
         * @returns {boolean} isMustUseNewWaybillCode 是否必须用新单号
         * @returns {number} waybillCodeCount 申请单号的总数量
         * @returns {Array} oldWaybillCodeList 要使用的旧单号
         * @returns {number} sfZDQuantity 子母件数量
         */
        const infoAboutWaybillCodeNums = await this._getInfoAboutWaybillCodeNums({
            orderList,
            templateInfo,
            templateDetailInfo,
            printType,
            canUseNewWaybillCodeSet,
            canChooseWaybillCodeNumSet,
            firstSendMode,

        });

        /** 获取网点信息
         * @param {object} templateInfo
         */

        this.timer.start('获取网点与解密')

        let wdSet  = {}
		if(templateInfo.KddType != 17){
			wdSet = await printItem.getYunBranchSet(templateInfo);
		}
		orderList = await printEncryptDYNick({ orderList, templateDetailInfo });
        console.assert(wdSet, '未获取到网点信息');
        console.log(data);

        if (templateInfo.KddType === 8 && !!data.fhdDecodeSwitch && isConcatFhd) { //
            orderList = await printFhdEncrypt(orderList, data.fhdDecodeSwitch);
        } else {
            orderList = await printWithEncryptPackages(orderList, templateInfo);
        }
        if(templateInfo.KddType == 13){
            let xhsorder = orderList.find(o =>{
                return  ['xhs'].includes(o.platform)
            })
            if(!xhsorder)  orderList = await printWithEncryptOrder({orderList,afterContinuePrintHook:this.afterContinuePrintHook},templateInfo)
        }else if(templateInfo.KddType == 14){
            let sphorder = orderList.find(o =>{
                return  ['sph'].includes(o.platform)
            })
            if(!sphorder)  orderList = await printWithEncryptOrder({orderList,afterContinuePrintHook:this.afterContinuePrintHook},templateInfo)
        }else{
            orderList = await printWithEncryptOrder({orderList,afterContinuePrintHook:this.afterContinuePrintHook,templateInfo})

        }
        this.timer.stop('获取网点与解密',{
            type:'batchKdd',
            printTraceId:this.printTraceId
        })
        if(!orderList.length) return
        if(firstSendMode){
            this.timer.start('先发货弹窗')
            await printItem._getWaybillConfirmDialog({
                isHasAutoShip: printOp == 1, //暂时写死不显示自动发货
                kdName: templateInfo.ExcodeName,
                orderNum: infoAboutWaybillCodeNums.waybillCodeCount,
                templateInfo,
                templateDetailInfo,
                isMerge: false
            })
            this.timer.stop('先发货弹窗',{
                type:'batchKdd',
                printTraceId:this.printTraceId
            })
        }
        //获取单号流程，获取后会把数据放入 orderList 中
        // eslint-disable-next-line require-atomic-updates
        this.timer.start('申请单号')
        orderList.forEach(order=>order.kddType = templateInfo.KddType)
		orderList = await printItem.getWaybillCodeNew({
            orderList,
            temp: templateInfo,
            elecNoCount: infoAboutWaybillCodeNums.waybillCodeCount, // 单号数量
            sfZDQuantity: infoAboutWaybillCodeNums.sonElecNoCount, // 子母件生成个数(不仅仅是顺丰，只是后端字段名是这个而已)
            sonElecNoCount: infoAboutWaybillCodeNums.sonElecNoCount, //子单号数量（菜鸟快运子母件）
            oldYdNos: infoAboutWaybillCodeNums.oldWaybillCodeList, //要使用的旧单号
            printOp, // [操作来源  0：单打 1：批打 2：预发货 3：自动预发货 4：手工订单]
            wdSet, // [网点地址选择对象]
            isUseOrderFjr: false, //是否使用订单里面设置的发件人信息：发件人信息在每一笔订单数据中，无需传递kddFjr 或者 fhdFjr
            fjrInfo, // 发件人信息，兼容单店铺和多店铺
            isNewEleNo: infoAboutWaybillCodeNums.isMustUseNewWaybillCode, //是否必须使用新单号
            loginUserId: userInfo.userId,//登陆用户userId
            isRepeat: false,// [针对安能盲区添加的安能盲区订单重试的措施 true:代表安能盲区重试]
            modeInfo: templateDetailInfo,
            weight:infoAboutWaybillCodeNums.weight,
            hasWWChat: false,//[true/false:是否需要联系旺旺提示]
            goodsDescription: otherPrintInfo.goodsDescription || infoAboutWaybillCodeNums.goodsDescription, //物品描述，获取单号时需要
            afterGetWaybillCodeHook: this.afterGetWaybillCodeHook,//获取单号后执行的钩子函数
            packagingDescription: infoAboutWaybillCodeNums.packagingDescription, //物品描述，获取单号时需要
            afterContinuePrintHook: this.afterContinuePrintHook,
            isZiMuJian: infoAboutWaybillCodeNums.isZiMuJian,
            afterGetElecErrorHook: this.afterGetElecErrorHook,
            processBatch:this.printTraceId,
            ordinary:true,//普通批打标识
			printSource: 'printSingle'
        });
        this.timer.stop('申请单号',{
            type:'batchKdd',
            printTraceId:this.printTraceId
        })
        // 拼多多备注多单号
        const REMARK_SINGLE_NUMBER_LIST = ['pdd', 'fxg', 'ksshop', 'ksshopg', 'ksshop-pre', 'ks', 'erp'];
        if (REMARK_SINGLE_NUMBER_LIST.includes(comp.Print.Data.platform)) {
            const ydnumCount = infoAboutWaybillCodeNums.waybillCodeCount || (infoAboutWaybillCodeNums.sfZDQuantity + 1) || 0;
            const checkResult = printItem.checkAutoAddPrintNum({
                orderList,
                othersSet,
                modeInfo: templateDetailInfo,
                ydnumCount,
            });
            orderList = checkResult.orderList;
            if (checkResult.isAutoAdd) {
                this.afterAutoAddPrintNum && this.afterAutoAddPrintNum(orderList, ydnumCount || 0);
            }
        }

        // 多单号处理逻辑
        // eslint-disable-next-line require-atomic-updates
        orderList = await printItem.moreWaybillCodeProcess({
            orderList,
            sfZDQuantity: infoAboutWaybillCodeNums.sonElecNoCount,
            waybillCodeCount: infoAboutWaybillCodeNums.waybillCodeCount,
            temp: templateInfo,
            isZiMuJian: infoAboutWaybillCodeNums.isZiMuJian,
        });
        if (firstSendMode) {
            this.afterShipFirstGetWaybillCodeHook(orderList, templateInfo)
            return
        }

        /*
        * 这里还能放一个检查是否发货内容打印到另一张上的逻辑
        * 行为仍然是修改 orderList 的内容
        * 暂时只有淘宝需要这个逻辑
        */

        // 本次打印是否向用户展示预览按钮
        const isHasPreview = previewConfig(printClient) && previewConfig_plat(); // 类型支持预览且平台支持预览


        // 选择打印机流程
        const { choosedPrinter, printNums, xbqChoosedPrinter, ...choosedPrinterSet } = await printItem.choosePrinter({
            orderList,
            closeChoosePrinterNoti: choosePrinterNotiSet && printOp == 1,
            defaultPrinter:  ((templateDetailInfo || {}).ModeTempPrintcfg || {}).DefaultPrinter,
            scanChoosedPrinter,
            printType,
            templateInfo,
            templateDetailInfo,
            displayPrintNum: displayPrintNumSet,
            orderNum: orderList.length,
            isHasAutoShip: printOp == 1, //暂时写死不显示自动发货
            shipAfterPrint: true,
			isHasPreview: true,
            printClient,
            printerList: installResult.printers,
            isConcatXbq,
            xbqTempisInvalid,
            isShowMorePrinter,
            xbqTemplateDetailInfo
        });
        if(!xbqChoosedPrinter) isConcatXbq =false
        console.assert(choosedPrinter, '未获取打印机');
        if (isConcatXbq && !xbqTempisInvalid) {
            new concatPrintDpd().print({
                choosedPrinter: xbqChoosedPrinter,
                orderList: originalOrderList,
                xbqTemplateDetailInfo,
                isShowProgress: true
            });
        }
        this.timer.stop('选择打印机',{
            type:'batchKdd'
        })

        // 本次行为是否是预览
        const isPreview = choosedPrinterSet.isPreview;

        //这里还能加一个 _scannerStart 流程（如果需要的话）

        // 打印预览部分：直到在预览的面单上点击「打印」按钮，才离开本逻辑
        if (isPreview) {
            const temporaryOrderList = printItem.deepClone(orderList);
            try {
                await printItem.previewTemplate({
                    isPreview,
                    printType,
                    kddFjrMap: fjrInfo,
                    orderList: temporaryOrderList,
                    total: printNums * orderList.length * (templateInfo.isConcatFhd ? 2 : 1),
                    printer: choosedPrinter,
                    templateInfo,
                    templateDetailInfo,
                    orderNum: orderList.length,
                    elecNoCount: infoAboutWaybillCodeNums.sfZDQuantity || infoAboutWaybillCodeNums.waybillCodeCount || 1,
                });

            } catch (err) {
                console.log(err);
            }
        }
        this.timer.start('打印标记')
        comp.Print.Data.isPrintNow = true
        //获取打印序号，把打印序号信息放入 orderList 中
        // eslint-disable-next-line require-atomic-updates
        orderList = await printItem.dealWithPrintNum({
            orderList,
            printType: 'kdd',
        });
        //拼多多获取打印批次
        // 整合平台获取打印批次 0818 放心购厂家代打没有批次功能 0901
        if (comp.Print.Data.platform !== 'fxgfds') {
            orderList = await printItem.dealWithPrintBatch({
                orderList,
            });
        }
        // 判断打印内容是否需要打印在另一张纸上
        // 对于「需要在第二张纸上打印发货内容的 order 中」加上 isNeedBreakPage:true
        const checkedBreakPageResult = printItem.checkedBreakPage({
            orderList,
            modeInfo: templateDetailInfo,
            printStyle,
        });
        orderList = checkedBreakPageResult.orderList;
        const extraPrintContentPageNum = checkedBreakPageResult.extraPrintContentPageNum;


        //打印进度弹窗
        let progress = null

        if (isConcatXbq && window.progress) {
            progress = window.progress
        } else {
            progress = new printItem.getPrintProgress({
                showType: 'mark',
                totalCount: orderList.length,
                isConcatXbq
            });
           if(isConcatXbq) window.progress = progress
        }
        const client = comp.Print.getClientType(templateDetailInfo);
        const isCloudPrint = client == 'pdd' || client === 'cainiao';

        //打印标记
        // 如果某个订单获取单号报错了，则对应的订单不进行标记
        originalOrderList = originalOrderList.reduce((acc, item) => {
            if (orderList.find(order => order.togetherId === item.togetherId)) {
                acc.push(item);
            }
            return acc;
        }, []);
        let markPrintResults = await printItem.markPrintStatus({
            orderList: originalOrderList, //此处需要原始的 orderList 数据
            printType: 'kdd',
            templateInfo,
            printer: choosedPrinter,
            isCloudPrint,
			isConcatFhd,
            // isCloudPrint,
            wdSet,
            printCopies: printNums,
        });
        if (!markPrintResults.isSuccess) {
            Tatami.showFail('订单标记打印失败，请重试，或联系在线客服');
            progress.remove()
            return
        }

        //完成「打印标记」后的钩子
		this.afterSavePrintMarkHook({ markTradeList: orderList, isConcatFhd });

        progress.showType('progress');
        if (isConcatXbq) {
            progress.init({
                type: 'kdd',
                totalCount: orderList.length
            })
        }
        this.timer.stop('打印标记',{
            type:'batchKdd',
            desc:`快递单数量：${orderList.length},打印批次号：${orderList[0].print_batch}`,
            printTraceId:this.printTraceId
        })
        // 美团需要鉴权
        orderList = await printItem.getAuthPrintDevice(templateInfo, orderList);
        //组装打印数据
        this.timer.start('任务发送')
        const printDatas = await printItem.getFormatPrintData({
            orderList,
            templateInfo,
            kddFjrMap: fjrInfo,
            printType,
            modeInfo: templateDetailInfo,
            isGetFhdFjr: isConcatFhd,
            fhdFjrMap: fhdFjrInfo,
        });

        console.assert(printDatas, '组装数据');

        // 打印步骤
        const params_doPrint = {
            orderList,
            printDatas,
            progress,
            printType,
            choosedPrinter,
            templateInfo,
            templateDetailInfo,
            othersSet,
            extraPrintContentPageNum,
            wdSet,
            printNums,
            isShowMorePrinter
        };

        this._doPrint(params_doPrint);
    }

    // 打印 && 底单逻辑
    _doPrint({
        orderList,
        printDatas,
        progress,
        printType,
        choosedPrinter,
        templateInfo,
        templateDetailInfo,
        othersSet,
        extraPrintContentPageNum,
        wdSet,
        printNums,
        isShowMorePrinter
    }) {
        const self = this;
        const p = new comp.Print();
        let isMorePrinter = true;
        let printedLength = 0;
        // 当多台打印机同步打印时，printOkFunc可能会间隔很短触发，导致 afterSavePrintLogHook 触发多次
        let isLock = false;
        console.log('打印 && 底单逻辑----');
        const printParams = {
            printDatas: printDatas,
            tempId: templateInfo.Mode_ListShowId,
            ptype: printType,
            printBoxIsShow: 0,
            selectedPrinter: choosedPrinter,
            templateSet: templateDetailInfo,
            showPrinting: othersSet.showPrinting, // 显示出纸进度 暂时只针对快递单开放，开关设置走高级设置
            wdSet: wdSet,
            speedPrint:true
        };
        console.info(printParams);
        //打印回调methods获取
        const methodsCb = {
            printSynFunc: function (curCount) {
                console.log(curCount,'多台打印机-进度');
                progress.updateProgress({ curCount,type:'kdd' });
            },
            printOkFunc: async function (printData) {
                self.timer.stop('任务发送',{
                    type:'batchKdd',
                    printTraceId:self.printTraceId
                })
                let markOrd = [];
                printedLength += printData.length;

                //底单日志存储
                progress.showType('log');
                 // 避免保存多份打印日志
                if(isShowMorePrinter){
                    markOrd = orderList;
                    // 拼多多多台打印机同步打印优化此功能
                    // 原来逻辑：未开启多台打印机需要保存底单（网点使用多台打印机打印没有存储打印日志）
                    // 新增逻辑：开启多台打印机后，拼多多面单和网点(clodop打印和菜鸟打印)需要保存底单
                    let markOrderList = orderList.filter((i)=>  {
                        return printData.find((si)=> {
                            // 避免出现undifind === undefined的情况
                            // 拼多多控件
                            if(si.documentID && i.exnumber){
                                return si.documentID == i.exnumber;
                            }
                            // 网点
                            if(si.documentID && i.txm_number){
                                return si.documentID == i.txm_number;
                            }
                            // 网点
                            if(si.txm_number && i.exnumber){
                                return si.txm_number == i.exnumber;
                            }
                            // 视频号
                            if(si.exnumber && i.exnumber){
                                return si.exnumber == i.exnumber;
                            }
                        });
                    });
                    await printItem.saveKddLog({
                        orderList:markOrderList,
                        templateInfo,
                        printOp: self.data.printOp,
                        templateDetailInfo,
                        printNums,
                        processBatch:self.printTraceId
                        });
                }else if( printedLength === printDatas.length || !isShowMorePrinter){
                    await printItem.saveKddLog({
                        orderList,
                        templateInfo,
                        printOp: self.data.printOp,
                        templateDetailInfo,
                        printNums,
                        processBatch:self.printTraceId
                        });
                }
                // const results = await printItem.saveKddLog({
                //     orderList,
                //     templateInfo,
                //     printOp: self.data.printOp, //
                //     templateDetailInfo,
                //     printNums,
                //     processBatch:self.printTraceId
                // });

                if( !isLock && (printedLength === printDatas.length || !isShowMorePrinter)){
                    isLock = true;
                    if (!!templateDetailInfo.ModeTempPrintcfg.IsConcatFhd) {
                        const tempId = templateDetailInfo?.ModeListShow?.rotationUserTemplateId || '';// await printItem.getDefaultTempId('fhd');
                        templateInfo = await printItem.getTempById(tempId, 'fhd');
                        await printItem.saveFhdLog({
                            orderList,
                            templateInfo,
                        });
                    }
                    self.afterSavePrintLogHook(othersSet, templateInfo);
                    progress.remove('kdd');
                }
                console.log('saveKddLog 方法的返回值');
                comp.Print.Data.isPrintNow = false
                // 清除打印来源信息
                window.printSourceInfo = null
                //TODO 底单补偿。
                console.log(printParams, 'printParams');
                // const isYilianTemp = comp.base.isYilianTemp(templateInfo.styleId);
                const isYilianTemp = printParams?.templateSet?.ModeList?.WidthPaper === 760;
                const tempType = isYilianTemp ? 'yilian' : 'common';
                new comp.Print().setIsConcatFhd(tempType, false);
            },
            /**
             *
             * @param {object} data  // 打印控件返回的内容
             * @param {string} compType //是哪种控件返回的提示
             */
            notifyPrintCb: function (data, compType) {
                console.log('--notifyPrintCb---');
                console.info(data);
                console.info(compType);
                self.notifyPrintHook(data, compType);
            },
        };
        // 开启多台打印机如果只选择了一台或者打印面单数量不超过10，当做普通单台打印机处理
        if(isShowMorePrinter && (choosedPrinter.length === 1 || printDatas.length <= (sessionStorage.getItem('morePrintNum') || 10))) {
            printParams.selectedPrinter = choosedPrinter[0]
            isMorePrinter = false
        }

        if(isShowMorePrinter && isMorePrinter){ // 开启设置，并且选择了多台打印机且订单数量大于10
            // 记录使用了多台打印机的人数及单数
            Tatami.pub('otherPorint', '多台打印机人数')
            new Array(printDatas.length || 0).fill('').forEach(()=>{
                Tatami.pub('otherPorint', '多台打印机单量')
            });
            const documentNoInfo = {
                allDocumentCount : printDatas.length,
                startDocumentIdx : 0
            }
            let orderSplitLeng = Math.ceil(printDatas.length/choosedPrinter.length)

            let deepPrintParams = deepClone(printParams);
            // 开启了多台打印机时，  交替打印要变为模式1.   模式2不支持多台打印机打印
            deepPrintParams.templateSet.ModeTempPrintcfg.concatFhdType = 1;
            // 直接等分分发，不考虑打印时机
            choosedPrinter.forEach((o,i) => {
                deepPrintParams.printDatas = printDatas.slice( i* orderSplitLeng, (i+1) * orderSplitLeng)
                deepPrintParams.selectedPrinter = o
                deepPrintParams.documentNoInfo = documentNoInfo
                //一股脑丢给打印取自由发挥
                // 打印的单数和打印机数量有时并不成正比，不能做到平均分配，导致前面的打印机分的订单可能比平均数多一些。
                // 特殊情况下，后面的打印机没有订单可分了，勾选了10台打印机，可能只有9台参与一起打印。
                if(deepPrintParams.printDatas.length>0){
                    p.printTemplate(deepClone(deepPrintParams), methodsCb);
                }
                documentNoInfo.startDocumentIdx += deepPrintParams.printDatas.length

            })

        }else{

            p.printTemplate(printParams, methodsCb);
        }
    }

    // 封装：通过单号选择框获取单号的相关信息
    async _getInfoAboutWaybillCodeNums({
        orderList,
        templateInfo,
        templateDetailInfo,
        printType,
        canUseNewWaybillCodeSet,
        canChooseWaybillCodeNumSet,
        firstSendMode
    }) {
        // 有没有打印过快递单
        const hasPrinted = await printItem.checkOrderHasPrint(orderList, 'kdd', firstSendMode);

        // 判断本次打印是否是子母件
        let isZiMuJian = false;
        // 判断本次打印能不能用原单号打印
        let canUseOldNo = false;

        isZiMuJian = await printItem.checkIsZiMuJian(templateDetailInfo);
        canUseOldNo = await printItem.checkIsUseOldNo({ templateInfo, isZiMuJian, orderLength: orderList.length });

        // 单号数量和使用新/旧单号信息
        let chooseWaybillInfos = {};


        /**
         * 判断是否弹出已打印弹窗并执行弹框；如果已打印，允许用户选择使用原单号还是新单号打印，新单号可以选择新单号个数
         * 会返回一个对象，包含三个值：
         * isMustUseNewWaybillCode 是否是用新单号 Boolean
         * oldWaybillCodeList 旧单号列表 Array 缺省为空数组
         * waybillCodeCount 本次需要的单号数量 Number
         */
        chooseWaybillInfos = await printItem.surePrintAgain({
            printType,
            templateInfo,
            hasPrinted,
            canUseOldNo,
            canUseNewWaybillCodeSet,
            canChooseWaybillCodeNumSet,
            orderList,
            isZiMuJian,
            firstSendMode
        }) || chooseWaybillInfos;

        // 如果订单没打印过，并且高级设置中允许选择多个单号 || 打印的订单数量超过两个  => 会有多单号选择框
        // 子母件不会有多单号选择框
        // 关于「是否是用新单号」如果有重打提示框，则由重打提示框获取该值，如果未弹出重打提示框，本逻辑会把「是否是用新单号」设置为 true
        chooseWaybillInfos = await printItem.chooseWaybillCodeNum({
            hasPrinted,
            canChooseWaybillCodeNumSet,
            templateInfo,
            isZiMuJian,
            orderListLength: orderList.length,
            isMustUseNewWaybillCode: chooseWaybillInfos.isMustUseNewWaybillCode,//来自重打提示框的「是否申请新单号」
        }) || chooseWaybillInfos;

        // 获取子母件单号数量等相关信息，菜鸟子母件和非菜鸟子母件
        let zmjRelativeInfos = {};
        if (isZiMuJian) {
            zmjRelativeInfos = await printItem.getZmjRelativeInfos(templateInfo, chooseWaybillInfos);
        }

        // 抖音顺丰非字母件需要传 sfZDQuantity: undefined 用于后端区分
        let dyExtraParams = {};
        if (templateInfo.KddType === 8 && templateInfo.ExCode === 'SF' && !isZiMuJian) {
            dyExtraParams = {
                sfZDQuantity: undefined,
            };
        }

        return {
            isMustUseNewWaybillCode: chooseWaybillInfos.isMustUseNewWaybillCode,
            waybillCodeCount: chooseWaybillInfos.waybillCodeCount,
            oldWaybillCodeList: chooseWaybillInfos.oldWaybillCodeList,
            isZiMuJian,
            ...zmjRelativeInfos,
            // sfZDQuantity:zmjRelativeInfos.sfZDQuantity,
            ...dyExtraParams,
        };
    }


    // --------------钩子函数的初始化--------------------
    /**
     * 检测控件安装结果后执行的钩子函数
     * type: 'cainiao'|'lodop'
     * isSuccess: true|false
     */
    afterCheckComponentHook() {
        console.log('afterCheckComponentHook', arguments);
    }

    //选择模板后执行的钩子函数
    afterChooseTempHook() {
        console.log('afterChooseTempHook', arguments);
    }

    //选择发件人后执行的钩子函数
    afterChooseFjrHook() {
        console.log('afterChooseFjrHook', arguments);
    }

    notifyPrintHook() {
        console.log('afterChooseFjrHook', arguments);
    }

    afterGetWaybillCodeHook() {
        console.log('afterGetWaybillCodeHook', arguments);
    }
    afterShipFirstGetWaybillCodeHook() {
        console.log('afterShipFirstGetWaybillCodeHook', arguments);
    }

    afterSavePrintMarkHook() {
        console.log('afterSavePrintMarkHook', arguments);
    }

    //保存打印日志后的钩子函数
    afterSavePrintLogHook() {
        console.log('afterSavePrintLogHook', arguments);
    }

    afterBranchChangeHook() {
        console.log('afterBranchChangeHook', arguments);
    }

    httpWhiteListHook() {
        console.log('httpWhiteListHook', arguments);
    }
    // 获取单号有失败的订单时的回调
    afterGetElecErrorHook() {
        console.log('afterGetElecErrorHook', arguments);
    }

    // 备注多单号完成后的回调
    afterAutoAddPrintNum(dealedOrderList, waybillCodeCount) {
        // 打印时自动追加单号到备注
        if (advancedSet.isAutoAddPrintNum) {
            const memos = [];
            if (waybillCodeCount > 1 || (waybillCodeCount === 1 && !advancedSet.isNotAddPrintNumOnlyOne)) {
                dealedOrderList.forEach(order => {
                    memos.push({
                        togetherId: order.togetherId,
                        tid: order.tids[0],
                        flag: order.sellerFlag || '',
                        memo: order.sellerMome || order.allYdNos.join(','),
                        userId: order.userId,
                    });
                });
            }
            // 调用保存备注接口
            this.getCtrl('com.printSetting', 'singleModifyMemo', memos, false, true);
        }
    }

    // 获取单号有失败的订单时的回调
    afterContinuePrintHook() {
        console.log('afterContinuePrintHook', arguments);
    }

    // --------------钩子函数的初始化--------------------
}
export default PrintKdd;
