import { computed, configure, makeAutoObservable, runInAction } from "mobx";
import { omitProps, Store } from "..";
import { IPackage } from "@/pages/Trade/interface";
import { TradePrintSetGetPrintSetApi } from "@/apis/trade";
import userStore from "@/stores/user";
import memoFn from "@/libs/memorizeFn";



const defaultPackList = [{
	isActive: true,
	togetherId: "togetherId",
	trades: [{
		demo: true,
		orders: []
	}]
}];

class PackWeightStore {
	constructor() {
		makeAutoObservable(this);
	}

	// 理论重量
	theoreticalWeight = 0

	setTheoreticalWeight = (val) => {
		this.theoreticalWeight = val;
	}

	// 显示高级设置
	advancedSettingModalVisible = false;

	setAdvancedSettingModalVisible = (v:boolean) => {
		this.advancedSettingModalVisible = v;
	}

	// 高级设置
	advancedSettingConfig = {
		isWeightOnly: false, // 仅称重不发货
		isWeightCheck: false, // 支持重量校验
		weightCheckType: "weightNum",
		widthUpNum: 0,
		widthDownNum: 0,
		widthUpRate: 0,
		widthDownRate: 0,
	};

	updateAdvancedSettingConfig = async(v) => {
		memoFn.updateAdvancedSet({
			weighingAndShippingConfig: JSON.stringify(v)
		});
		this.advancedSettingConfig = v;
	}

	getAdvancedSettingConfig = async() => {
		const config = await memoFn.getAdvancedSet();
		if (config?.weighingAndShippingConfig) {
			try {
				const packageWeighConfig = JSON.parse(config?.weighingAndShippingConfig);
				this.advancedSettingConfig = packageWeighConfig;
			} catch (e) {
				console.log(e);
			}
		}
		return this.advancedSettingConfig;
	}



	// 订单VO
	tradeList:IPackage[] = defaultPackList;

	setTradeList = (list:IPackage[]) => {
		this.tradeList = list;
	}

	// 页面整体loading

	showLoading = false;

	setShowLoading = (v:boolean) => {
		this.showLoading = v;
	}

	// 是否处于发货中

	sendPending = false;

	setSendPending= (v:boolean) => {
		this.sendPending = v;
	}

	initStore = () => {
		this.setTradeList(defaultPackList);
		this.setSendPending(false);
		this.setShowLoading(false);
		this.setTheoreticalWeight(0);
	}

	
	videoMonitorState = false;

	selectedShootingDevices = [];

	nvrDevices = [];

	setNvrDevices = (v) => {
		this.nvrDevices = v;
	}

	setSelectedShootingDevices = (v) => {
		this.selectedShootingDevices = v;
	}

	setVideoMonitorState = (v) => {
		this.videoMonitorState = v;
	}

}
const packWeightStore = new PackWeightStore();
export default packWeightStore;
