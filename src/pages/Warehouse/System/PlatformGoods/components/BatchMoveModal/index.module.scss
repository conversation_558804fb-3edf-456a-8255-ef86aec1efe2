.content {
  padding: 0 16px;
}

.sourceInfo {
  margin-bottom: 16px;
}

.sourceTitle {
  font-size: 14px;
  font-weight: normal;
}

.count {
  color: #FF4D4F;
  font-weight: bold;
}

.targetTitle {
  font-size: 14px;
  font-weight: normal;
  margin-bottom: 10px;
}

.shopListHeader {
  background-color: #f5f5f5;
  padding: 10px 16px;
}

.selectedCount {
  color: #52C41A;
  font-weight: bold;
}

.shopList {
  min-height: calc(100vh - 850px);
  overflow-y: auto;
  padding: 16px;
  border:1px solid #eee;
}

.shopItem {
  padding-bottom: 16px;
}

.platformIcon {
  font-size: 19px;
}

.settingBtn {
  margin-right: auto;
}

.shopName {
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  padding-top: 4px;
}

.limitModal {
  :global {
    .ant-modal-body {
      padding:12px 24px 24px 24px;
    }
  }
}

.limitInfoBox {
  background-color: #f5f5f5;
  padding: 16px 64px;
  margin-bottom: 16px;
  border-radius: 2px;
  position: relative;
}

.infoRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px 0 20px;
}

.infoItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.infoLabel {
  color: #666;
  margin-bottom: 8px;
  font-size: 14px;
}

.infoValue {
  font-weight: 500;
  font-size: 24px;
  color: #333;
}

.infoLabelExc {
  color: #666;
  margin-bottom: 8px;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.infoValueExc {
  font-weight: 500;
  font-size: 24px;
  color: #FF4D4F;
}

.tipBox {
  padding: 16px;
  border: 1px solid #f5f5f5;
  border-radius: 4px;
}

.tipItem {
  display: flex;
  align-items: center;
  gap: 12px;
}

.tipIcon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;

  border-radius: 4px;
}

.tipContent {
  flex: 1;
}

.tipTitle {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 4px;
  color: #333;
}

.tipDesc {
  font-size: 14px;
  color: #666;
}
