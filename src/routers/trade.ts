import { lazy } from 'react';
import { RoutePointType, RouteType } from '@/types/schemas/index';
import TradePage from '@/pages/Trade';
import PreShip from '@/pages/Trade/PreShip';
import GoodsTag from '@/pages/Trade/GoodsTag';
import ScanPrint from '@/pages/Trade/ScanPrint';
import LabelGoodsCheck from '@/pages/Trade/LabelGoodsCheck';
import LabelAccountCheck from '@/pages/Trade/LabelAccountCheck';
import HotGoodsScanPrint from '@/pages/Trade/HotGoodsScanPrint';
import ForeignManage from '@/pages/ForeignManage';
import Strategy from '@/pages/Trade/Strategy';

const tradeRoutes: RouteType[] = [
	{
		path: "/trade/goodsTag",
		exact: true,
		// component: GoodsTag,
		component: lazy(() => import(/* webpackChunkName: "GoodsTag" */ '@/pages/Trade/GoodsTag')),
	}, {
		path: "/trade/scanPrint",
		exact: true,
		// component: ScanPrint,
		component: lazy(() => import(/* webpackChunkName: "ScanPrint" */ '@/pages/Trade/ScanPrint')),
	}, {
		path: "/trade/refundLabelScanPrint",
		exact: true,
		// component: ScanPrint,
		component: lazy(() => import(/* webpackChunkName: "ScanPrint" */ '@/pages/Trade/RefundLabelScanPrint')),
	}, {
		path: "/trade/postPrint",
		exact: true,
		component: lazy(() => import(/* webpackChunkName: "postPrint" */ '@/pages/Trade/PostPrint')),
	}, {
		path: "/trade/hotGoodsScanPrint",
		exact: true,
		// component: HotGoodsScanPrint,
		component: lazy(() => import(/* webpackChunkName: "HotGoodsScanPrint" */ '@/pages/Trade/HotGoodsScanPrint')),
	}, {
		path: "/trade/labelGoodsCheck",
		exact: true,
		// component: LabelGoodsCheck
		component: lazy(() => import(/* webpackChunkName: "LabelGoodsCheck" */ '@/pages/Trade/LabelGoodsCheck')),
	}, {
		path: "/trade/labelAccountCheck",
		exact: true,
		// component: LabelAccountCheck
		component: lazy(() => import(/* webpackChunkName: "LabelAccountCheck" */ '@/pages/Trade/LabelAccountCheck')),
	}, {
		path: "/trade/foreignManage",
		exact: true,
		component: lazy(() => import(/* webpackChunkName: "ForeignManage" */ '@/pages/ForeignManage')),
	}, {
		path: "/trade/strategy",
		exact: true,
		component: lazy(() => import(/* webpackChunkName: "Strategy" */ '@/pages/Trade/Strategy')),
	}, {
		path: "/trade/exceptionWarn",
		exact: true,
		component: lazy(() => import(/* webpackChunkName: "exceptionWarn" */ '@/pages/ExceptionWarn')),
	}, {
		path: "/trade/:param?",
		exact: true,
		// component: TradePage,
		// component: lazy(() => import(/* webpackChunkName: "Trade" */ '@/pages/Trade')),
		component: lazy(() => import(/* webpackChunkName: "Order" */ '@/pages/Trade/Order')),
	}, {
		path: "/preShip",
		exact: true,
		// component: PreShip,
		component: lazy(() => import(/* webpackChunkName: "PreShip" */ '@/pages/Trade/PreShip')),
	}, {
		path: "/inspectSend",
		exact: true,
		// component: PreShip,
		component: lazy(() => import(/* webpackChunkName: "PreShip" */ '@/pages/Trade/InspectSend')),
	}, {
		path: "/packWeight",
		exact: true,
		component: lazy(() => import(/* webpackChunkName: "PackWeight" */ '@/pages/Trade/PackWeight')),
	}, {
		path: "/liveTag",
		exact: true,
		component: lazy(() => import(/* webpackChunkName: "LiveTag" */ '@/pages/Trade/LiveTag')),
	}, {
		path: "/livestreamerManage",
		exact: true,
		component: lazy(() => import(/* webpackChunkName: "LivestreamerManage" */ '@/pages/Trade/LivestreamerManage')),
	}, {
		path: "/liveLog",
		exact: true,
		component: lazy(() => import(/* webpackChunkName: "LiveLog" */ '@/pages/Trade/LiveLog')),
	}


];

export default tradeRoutes;
