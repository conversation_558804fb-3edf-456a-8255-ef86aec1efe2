import React, { useEffect, useMemo, useState } from "react";
import { useHistory } from 'react-router-dom';
import { Checkbox, Col, Form, Modal, Radio, Row, Select, Space, message, Divider, Popover, Button, Tooltip, notification } from "antd";
import { InfoCircleOutlined, QuestionCircleOutlined, QuestionCircleFilled, CheckCircleOutlined, CloseCircleOutlined } from "@ant-design/icons";
import { observer } from "mobx-react";
import {
	autoPrintInterceptTypeDisabledList,
	autoPrintInterceptTypeEnum,
	matchResultTypeEnum,
	scanTypeEnum,
	scanTypeOptions,
	tradeFilterEnum,
	tradeStatusEnum
} from "../../constants";
import userStore from "@/stores/user";
import { tradeStore } from "@/stores";
import { flagGroup } from "@/constants";
import s from './index.module.scss';
import Icon from "@/components/Icon";
import { createSocketHander } from "@/utils/websocket";
import img1 from '@/assets/image/trade/扫描打印_智选快递.png';
import { ItemSmartExpressGetListApi } from "@/apis/setting/smartExpress";
import VideoMonitorControl from "@/pages/AfterSale/ScanRegister/components/AftersaleScan/ScanSetModal/components/VideoMonitorControl";

interface Props {
	onOk:()=>void,
	onCancel:()=>void,
	customLogPost:(...args:any[])=>void,
}

const SettingModal = (props:Props) => {
	const { onOk, onCancel, customLogPost } = props;
	const history = useHistory();
	const [form] = Form.useForm();
	const [loading, setLoading] = useState(false);
	const { postPrintStore: { settingConfig, getSettingConfig, saveSettingConfig, packWsHandler, setPackWsHandler, videoMonitorState, setVideoMonitorState } } = tradeStore;
	const { isShowZeroStockVersion } = userStore;
	const [isShowMore, setIsShowMore] = useState(true);
	const [isRefundStatus, setIsRefundStatus] = useState(false);
	const [tradeLabelListFlags, setTradeLabelListFlags] = useState(false);
	const [isPrintItemTag, setIsPrintItemTag] = useState(false);

	const initData = () => {
		handleSettingConfig();
	};

	const handleSettingConfig = async() => {
		let settingConfig:{[k:string]:any} = await getSettingConfig();
		console.log(31, settingConfig);
		// 零库存版本如果后端返回被过滤掉的选项，直接选中商家编码
		if (isShowZeroStockVersion && !scanTypeOptionsMemo?.find(item => item.value == settingConfig?.scanType)) {
			settingConfig.scanType = 'OUTER_ID';
		}
		if (settingConfig.tradeLabelList?.includes(tradeFilterEnum.有旗帜)) {
			settingConfig.tradeLabelList = settingConfig.tradeLabelList.filter(item => item !== tradeFilterEnum.有旗帜);
			setTradeLabelListFlags(true);
		}
		form.setFieldsValue(settingConfig);
		setIsRefundStatus(settingConfig.isRefundStatus);
		setIsPrintItemTag(settingConfig.isPrintItemTag);
		setVideoMonitorState(settingConfig.videoMonitor === 1); // 设置视频监控状态
	};

	const _onOk = async() => {
		const value = form.getFieldsValue();
		if (tradeLabelListFlags) {
			value.tradeLabelList.push(tradeFilterEnum.有旗帜);
		}
		console.log(value);
		setLoading(true);
		const params = {
			...settingConfig,
			...value,
			isRefundStatus,
			isPrintItemTag,
			videoMonitor: videoMonitorState ? 1 : 0, // 保存视频监控状态
		};

		try {
			await saveSettingConfig(params);
			message.success("设置已保存");
			onOk && onOk();
		} catch (error) {
			console.log(error);
		}
		setLoading(false);
	};

	const _onCancel = () => {
		if (loading) {
			return;
		}
		onCancel && onCancel();
	};

	const scanTypeOptionsMemo = useMemo(() => {
		if (isShowZeroStockVersion) {
			return scanTypeOptions.filter(i => {
				return ![scanTypeEnum.SYS_OUTER_ID, scanTypeEnum.BAR_CODE, scanTypeEnum.ITEM_NO].includes(i.label);
			});
		}
		return scanTypeOptions;
	}, [isShowZeroStockVersion]);

	useEffect(() => {
		initData();
	// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [scanTypeOptionsMemo]);

	const childFormItemMap = {
		[autoPrintInterceptTypeEnum.含以下旗帜不自动打印]: (
			<Form.Item name="sellerFlagList" label="" wrapperCol={ { offset: 1 } }>
				<Checkbox.Group style={ { width: '410px' } }>
					{flagGroup.slice(1).map((i, index) => {
						return (
							<span style={ { marginBottom: 8 } } hidden={ index > 4 && isShowMore }>
								<Checkbox
									key={ i.value }
									value={ i.value }
								><Icon type="flag1" style={ { color: i.color } } />{ i.name }旗
								</Checkbox>
							</span>
						);
					})}
					<span className="r-click" onClick={ () => setIsShowMore(pre => !pre) }>{isShowMore ? '更多' : '收起'}</span>
				</Checkbox.Group>
			</Form.Item>
		),
	};

	const connectPack = () => {
		const socketHandler: any = {};
		createSocketHander(socketHandler, {
			// url: "ws://localhost:8080",
			url: "ws://127.0.0.1:8010/SmartPack/Examine",
			onOpen: () => {
				notification.open({
					message: "打包机连接成功",
					description: "打包机连接成功",
					icon: <CheckCircleOutlined />,
					style: {
						color: "#f00"
					}
				});
				customLogPost('打包机连接成功');
				setPackWsHandler(socketHandler);
				console.log(event, 'onOpen');
			},
			onMessage: (event) => {
				customLogPost('打包机消息', event);
				console.log(event, 'event');
			},
			onClose: () => {
				notification.open({
					message: "打包机连接关闭",
					description: "打包机连接关闭",
					icon: <CloseCircleOutlined />,
					style: {
						color: "#f00"
					}
				});
				customLogPost('打包机连接关闭');
				console.log('onClose');
			},
			onError: (event) => {
				notification.open({
					message: "打包机连接失败",
					description: "打包机连接失败",
					icon: <CloseCircleOutlined />,
					style: {
						color: "#f00"
					}
				});
				customLogPost('打包机连接失败');
				console.log(event, 'onError');
			}
		});
	};

	const expressMatchTypeListChange = async(e) => {
		if (e.target.checked) {
			ItemSmartExpressGetListApi({
				queryCondition: {
				}
			}).then(res => {
				if (!res?.smartExpressConfigVO?.openStatus) {
					Modal.confirm({
						title: '系统提醒',
						icon: <InfoCircleOutlined />,
						content: '智选快递功能未开启，请先开启智选快递',
						okText: "去开启",
						onOk: () => {
							history.push('/settings/smartExpress');
						}
					});
					form.setFieldsValue({
						expressMatchTypeList: form.getFieldValue('expressMatchTypeList').filter(item => item !== 'NO_SIDS_SMART_EXPRESS_TEMPLATE'),
					});
				}
			});
		}
	};

	return (
		<Modal
			visible
			title="功能设置"
			bodyStyle={ { overflowY: 'auto', maxHeight: 'calc(100vh - 230px)' } }
			// bodyStyle={ { minHeight: 200 ,height: 'calc(100vh - 200px)'} }
			okText="保存"
			okButtonProps={ { loading } }
			onOk={ _onOk }
			onCancel={ _onCancel }
			className={ s.modalStyle }
			width={ 620 }
		>
			<Form form={ form } labelCol={ { span: 5 } } >
				{/* <div className={ s.titleLine }>扫描设置</div>
				<Form.Item name="scanType" label="扫描条码">
					<Select getPopupContainer={ (e) => e.parentElement } options={ scanTypeOptionsMemo } style={ { width: '200px' } } />
				</Form.Item> */}
				<div className={ s.titleLine }>匹配订单设置</div>
				{/* <Form.Item name="sellAttributeTypeList" label="订单销售类型">
					<Checkbox.Group>
						<Checkbox value={ sellAttributeTypeEnum.单款单件 }>单款单件</Checkbox>
						<Checkbox value={ sellAttributeTypeEnum.单款多件 }>单款多件</Checkbox>
					</Checkbox.Group>
				</Form.Item> */}
				<Form.Item name="tradeStatusList" label="匹配订单">
					<Checkbox.Group>
						<Checkbox value={ tradeStatusEnum.待发货 }>待发货订单</Checkbox>
						<Checkbox value={ tradeStatusEnum.先发货订单 }>系统先发货订单</Checkbox>
					</Checkbox.Group>
				</Form.Item>
				<Form.Item label="订单过滤">
					<div className="r-flex">
						<Form.Item>
							<Checkbox checked={ isRefundStatus } onChange={ (e) => { setIsRefundStatus(e.target.checked); } }>有退款订单</Checkbox>
						</Form.Item>
						<Form.Item name="tradeLabelList" className="r-ml-8">
							<Checkbox.Group>
								<Checkbox value={ tradeFilterEnum.挂起 }>挂起订单</Checkbox>
							</Checkbox.Group>
						</Form.Item>
					</div>
					<div>
						<Form.Item name="">
							<Checkbox checked={ tradeLabelListFlags } onChange={ (e) => setTradeLabelListFlags(e.target.checked) }>有旗帜订单</Checkbox>
						</Form.Item>
						{
							tradeLabelListFlags && (
								<Form.Item name="tradeLabelSellerFlagList" label="" wrapperCol={ { offset: 1 } }>
									<Checkbox.Group style={ { width: '410px' } }>
										{flagGroup.slice(1, 6).map((i, index) => {
											return (
												<span style={ { marginBottom: 8 } }>
													<Checkbox
														key={ i.value }
														value={ i.value }
													><Icon type="flag1" style={ { color: i.color } } />{ i.name }旗
													</Checkbox>
												</span>
											);
										})}
									</Checkbox.Group>
								</Form.Item>
							)
						}
					</div>
				</Form.Item>
				<Form.Item name="matchResultType" label="匹配结果">
					<Radio.Group>
						<Radio value={ matchResultTypeEnum.付款最早订单 }>显示最早下单的一笔订单</Radio>
						<Radio value={ matchResultTypeEnum.剩余发货时间最少订单 }>显示剩余发货时间最少的一笔订单</Radio>
					</Radio.Group>
				</Form.Item>
				<Divider className="r-mt-10 r-mb-10" />
				<div className={ s.titleLine }>快递设置</div>
				<Form.Item label="快递匹配设置">
					<div>扫描后自动切换订单关联快递模板</div>
					<Form.Item name="expressMatchTypeList">
						<Checkbox.Group>
							<Checkbox value="FIRST_SEND_AUTO_TEMPLATE">匹配先发货订单后，快递单模板自动切换为订单发货模板</Checkbox>
							<Checkbox value="NO_SEND_HAS_SIDS_TEMPLATE" style={ { marginLeft: 0 } }>已申请单号订单（未先发货）自动切换为最新申请的单号的模板</Checkbox>
							<Checkbox value="NO_SIDS_SMART_EXPRESS_TEMPLATE" style={ { marginLeft: 0 } } onChange={ expressMatchTypeListChange }>未申请单号订单自动切换为智选快递推荐的模板
								<Popover content={ (
									<div>
										<p>同“订单打印”页面列表智选快递匹配功能</p>
										<p>
											<img src={ img1 } alt="" />
										</p>
									</div>
								) }
								><QuestionCircleOutlined className="r-ml-4 r-mr-4 r-c-999" />
								</Popover>
								<a className="r-c-primary" onClick={ () => { history.push('/settings/smartExpress'); } }>规则设置</a>
							</Checkbox>
						</Checkbox.Group>
					</Form.Item>
				</Form.Item>
				<Divider className="r-mt-10 r-mb-10" />
				<div className={ s.titleLine }>打印设置</div>
				<Form.Item name="isAutoPrint" label="匹配后自动打印">
					<Radio.Group>
						<Radio value>自动打印</Radio>
						<Radio value={ false }>不自动打印</Radio>
					</Radio.Group>
				</Form.Item>
				<Form.Item label="自动打印设置">
					<div className="r-mb-5">注意：未勾选时，订单将自动强制打印</div>
					<Form.Item name="autoPrintInterceptTypeList">
						<Checkbox.Group >
							<Space direction="vertical" style={ { display: 'flex' } } size={ 10 }>

								{Object.keys(autoPrintInterceptTypeEnum).map(i => {
									if (isShowZeroStockVersion && autoPrintInterceptTypeEnum[i] === autoPrintInterceptTypeEnum.含缺货商品的不自动打印) {
										return null;
									}
									const checkNode = (
										<Checkbox
											key={ i }
											value={ autoPrintInterceptTypeEnum[i] }
											disabled={ autoPrintInterceptTypeDisabledList.includes(autoPrintInterceptTypeEnum[i]) }
										>{ i } { i === '含已发货商品的不自动打印' ? <span className="r-c-gray">注：勾选时，先发货订单不进行拦截</span> : i === '含缺货商品的不自动打印' ? <span className="r-c-gray">注：未勾选时订单缺货将自动强制打印</span> : null }
										</Checkbox>
									);
									let childNode = childFormItemMap[autoPrintInterceptTypeEnum[i]];
									let newChildNode = null;
									if (childNode) {
										newChildNode = (
											<Form.Item
												noStyle
												shouldUpdate={ (prevValues, currentValues) => prevValues.autoPrintInterceptTypeList?.length !== currentValues.autoPrintInterceptTypeList?.length }
											>
												{
													({ getFieldValue }) => {
														return getFieldValue('autoPrintInterceptTypeList')?.includes(autoPrintInterceptTypeEnum[i]) ? childNode : null;
													}
												}
											</Form.Item>
										);
									}
									return <>{checkNode}{newChildNode}</>;
								})}
							</Space>

						</Checkbox.Group>
					</Form.Item>
				</Form.Item>
				<Form.Item label="吊牌打印" className="r-flex r-ai-c">
					<Checkbox checked={ isPrintItemTag } onChange={ (e) => setIsPrintItemTag(e.target.checked) }>
						打印快递单同时打印商品吊牌
					</Checkbox>
					<Popover
						content={ (
							<div>
								<p>打印吊牌时需要安装Lodop控件，<a href="https://www.lodop.net/download/CLodop_Setup_for_Win64NT_6.609EN.zip" target="_blank" rel="noreferrer">点击下载</a>；</p>
								<p>组合货品默认打印子货品的吊牌；</p>
								<p>打印的吊牌的打印机需要单独设置；</p>
								<p>打印快递单同时打印吊牌会影响快递单打印速度；</p>
							</div>
						) }
						style={ { width: 360 } }
					>
						<QuestionCircleFilled style={ { color: "#FD8204", fontSize: "14px" } } />
					</Popover>
					<Button type="link" onClick={ () => { history.push('/settings/system?introName=printTagSet'); } }>吊牌设置</Button>
				</Form.Item>
				<div className={ s.titleLine }>其他设置</div>
				<Form.Item name="" label="打包机异常检测状态" labelCol={ { span: 6 } }>
					<div className="r-flex r-ai-c">
						{ packWsHandler?.webSocket ? <span>已连接</span> : <span>未连接</span> }
						{
							!packWsHandler?.webSocket && (
								<>
									<Button
										className="r-ml-4"
										type="link"
										style={ { padding: 0, height: 'auto' } }
										onClick={ () => {
											connectPack();
										} }
									>重新连接
									</Button>
									<Tooltip title={
										<div>仅单件模式下可用，系统将自动把异常推送给打包机，完成打包机拦截，详见<a>打包机教程</a></div>
									}
									><QuestionCircleFilled className="r-ml-4" style={ { color: "#FD8204" } } />
									</Tooltip>
								</>
							)
						}
					</div>
				</Form.Item>
				<VideoMonitorControl
					videoMonitorState={ videoMonitorState }
					onVideoMonitorChange={ setVideoMonitorState }
					name="后置打印视频监控："
				/>
			</Form>
		</Modal>
	);
};

export default observer(SettingModal);
