import { PLAT_DW, PLAT_FXG, PLAT_HAND, PLAT_HAND_OLD, PLAT_JD, PLAT_KS, PLAT_OTHER, PLAT_PDD, PLAT_SCM, PLAT_SCMHAND, PLAT_SPH, PLAT_TB, PLAT_TM, PLAT_YZ, PLAT_KTT } from "@/constants";
import { TradeChoiceType } from "@/utils/enum/trade";
import Pointer from "@/utils/pointTrack/constants";
import { IChoiceCheckItem, ICopySettingItem } from "./interface";
import { FunctionPermissionEnum } from "@/utils/permissionCheck/functionPermissionCheck";
import { PageNameControlEnum } from "@/utils/permissionCheck/whiteListPermissionCheck";
import { constants } from "node:http2";

export const mockAdvancedSet = {
	autoTogether: true,
	bgTradeSellMemo: 0,
	blueDesc: "铂金会员",
	btnViewConfig:
		// eslint-disable-next-line max-len
		'{"useDefault":true,"config":{"outer":["orderPrintFhd","orderPrintKdd","executeShip"],"inner":["batchExportOrder","batchModifyLabel","addOtherPackage","batchDelFlag","batchCancelSend","pending","cancelPend","batchModifyMemo"]}}',
	buttionFooter: true,
	defaultSellerMemoFlag: 1,
	displayGenYdNum: true,
	displayGoods: true,
	displayMemo: true,
	expressOnOffTime: "2021-12-09 19:48:53",
	expressSwitch: 1,
	filterOrder: false,
	filterOrderSpecNick: 1,
	filterSelected: 1,
	genSidType: "asc",
	goodsSelectBox: 1,
	greenDesc: "普钻会员",
	hideAfterShip: false,
	isHidePendingTrade: 0,
	isAbnormalAddress: 0,
	isAutoAddPrintNum: 0,
	isAutoDelete: 1,
	isButtionFooter: true,
	isExpressMemoFilter: 0,
	isFhdMorefjr: 0,
	isFhdUnityfjr: 0,
	isHotStyle: 1,
	isKddUuityfjr: 0,
	isMergeFirst: 0,
	isMergeHint: 1,
	isMergePriority: 0,
	isNewYdNo: true,
	isNotAddPrintNumOnlyOne: 0,
	isNumIid: 1,
	isOnlyShowOnSale: 1,
	isPrintReverse: 1,
	isRelationJumpVisibility: 0,
	isSendImel: 1,
	isSendRemind: true,
	isTemplateSuspend: 0,
	isYdNumWarn: false,
	isYdPlain: true,
	ismKddMorefjr: 1,
	logisticsWarn: 1,
	mergeNumLimit: 3,
	mulShopMergeHint: 0,
	numIid: 0,
	openPrintNotify: 1,
	outerId: 0,
	pageSizeSet: 150,
	printByGoodSequence: 0,
	printNumReset: 1,
	printNumStyle: 1,
	purpleDesc: "普通会员",
	redDesc: "黑钻会员",
	remarkHighLight: 1,
	remindHour: "6",
	remindModifySet: 0,
	searchVersion: 1,
	sendAfterPrint: 0,
	sendRemind: true,
	shortName: 0,
	showFixed: 0,
	showFlagName: 1,
	showGood: 0,
	showNum: 1,
	skipAbnormal: 0,
	skuAlias: 0,
	skuId: 0,
	skuName: 0,
	sortRule: 1,
	templateMode: 2,
	title: 0,
	weChatRemind: 0,
	weChatRemindHour: "4",
	ydNum: 0,
	yellowDesc: "黄金会员",
	_lastModified: "2021/12/13 14:29:25",
};

export const choiceCheckList: IChoiceCheckItem[] = [{
	label: '勾选可达订单',
	point: Pointer.订单_订单打印_订单勾选_勾选可达订单,
	type: TradeChoiceType.勾选可达订单,
	disabled: false,
},
// {
// 	label: '勾选非疫情地区订单的',
// 	point: Pointer.订单_订单打印_订单勾选_勾选可达订单,
// 	type: TradeChoiceType.勾选非疫情订单,
// 	disabled: false,
// },
{
	label: '勾选未打印快递单的',
	point: Pointer.订单_订单打印_订单勾选_勾选未打印快递单,
	type: TradeChoiceType.勾选未打印快递单,
	disabled: false,
}, {
	label: '勾选未打印发货单的',
	point: Pointer.订单_订单打印_订单勾选_勾选未打印发货单,
	type: TradeChoiceType.勾选未打印发货单,
	disabled: false,
}, {
	label: '勾选有货的',
	point: Pointer.订单_订单打印_订单勾选_点击勾选有货的订单,
	type: TradeChoiceType.勾选有货的,
	tip: '需在订单列表-产品内容设置中开启【显示商品选择框】后生效',
	disabled: false,
}, {
	label: '全选',
	point: Pointer.订单_订单打印_订单勾选_全选,
	type: TradeChoiceType.全选,
	disabled: false,
}, {
	label: '反选',
	point: Pointer.订单_订单打印_订单勾选_反选,
	type: TradeChoiceType.反选,
	disabled: false,
}, {
	label: '不选',
	point: Pointer.订单_订单打印_订单勾选_不选,
	type: TradeChoiceType.不选,
	disabled: false,
}];

// 平台订单更多
export const tradeOperateList = [{
	name: '手写单号',
	action: 'inputSid',
	point:Pointer.订单_订单打印_订单更多操作_手写单号,
}, {
	name: '发货',
	action: 'sendTrade',
	functionPermission: FunctionPermissionEnum.发货,
}, {
	name: '换商品',
	action: 'changeGoods',
	point:Pointer.订单_订单打印_订单更多操作_换商品,
}, {
	name: '添加商品',
	action: 'addGiftGoods',
	point:Pointer.订单_订单打印_订单更多操作_添加赠品,
}, {
	name: '打印快递单',
	action: 'printKDD',
	functionPermission: FunctionPermissionEnum.打印快递单,
}, {
	name: '打印发货单',
	action: 'printFHD',
	functionPermission: FunctionPermissionEnum.打印发货单,
}, {
	name: '挂起',
	action: 'pending',
	point:Pointer.订单_订单打印_订单更多操作_挂起,
// }, {
// 	name: '占用订单库存',
// 	action: 'occupyTradeStock'
// }, {
// 	name: '释放订单库存',
// 	action: 'freedTradeStock'
}, {
	name: '订单操作日志',
	action: 'tradeOptLog',
}, {
	name: '创建手工单',
	action: 'createHandOrder',
	functionPermission: FunctionPermissionEnum.手工录入订单,
}, {
	name: '标记为空单',
	action: 'markEmpty',
	point:Pointer.订单_订单打印_订单更多操作_标记为空单,
}, {
	name: '取消空单标记',
	action: 'cancelMarkEmpty',
}, {
	name: '处理异常',
	action: 'dealAbnormal',
	point:Pointer.订单_订单打印_订单更多操作_处理异常,
}, {
	name: '拆分订单',
	action: 'splitOrder',
	point:Pointer.订单_订单打印_订单更多操作_拆分订单,
}, {
	name: '标记黑名单',
	action: 'addBlackList',
	point:Pointer.订单_订单打印_订单更多操作_标记为黑名单,
}, {
	name: '取消黑名单标记',
	action: 'removeBlackList',
}];

// 风控订单不能进行发货和打印快递单或发货单操作
export const riskControlTradeOperateList = [{
	name: '挂起',
	action: 'pending',
	point:Pointer.订单_订单打印_订单更多操作_挂起,
}, {
	name: '订单操作日志',
	action: 'tradeOptLog',
}, {
	name: '处理异常',
	action: 'dealAbnormal',
	point:Pointer.订单_订单打印_订单更多操作_处理异常,
}];

// 手工订单更多
export const handTradeOperateList = [{
	name: '编辑',
	action: 'handEdit',
}, {
	name: '换商品',
	action: 'changeGoods',
	point:Pointer.订单_订单打印_订单更多操作_换商品,
}, {
	name: '添加商品',
	action: 'addGiftGoods',
	point:Pointer.订单_订单打印_订单更多操作_添加赠品,
}, {
	name: '复制订单',
	action: 'handCopy',
}, {
	name: '删除订单',
	action: 'deleteOrder',
}, {
	name: '手写单号',
	action: 'inputSid',
	point:Pointer.订单_订单打印_订单更多操作_手写单号,
}, {
	name: '发货',
	action: 'sendTrade',
	functionPermission: FunctionPermissionEnum.发货,
}, {
	name: '打印快递单',
	action: 'printKDD',
	functionPermission: FunctionPermissionEnum.打印快递单,
}, {
	name: '打印发货单',
	action: 'printFHD',
	functionPermission: FunctionPermissionEnum.打印发货单,
}, {
	name: '挂起',
	action: 'pending',
	point:Pointer.订单_订单打印_订单更多操作_挂起,
// }, {
// 	name: '占用订单库存',
// 	action: 'occupyTradeStock'
// }, {
// 	name: '释放订单库存',
// 	action: 'freedTradeStock'
}, {
	name: '订单操作日志',
	action: 'tradeOptLog',
// }, {
// 	name: '订单本地标记',
// 	action: 'orderSellerFlag',
}, {
	name: '处理异常',
	action: 'dealAbnormal',
	point:Pointer.订单_订单打印_订单更多操作_处理异常,
}, {
	name: '创建手工单',
	action: 'createHandOrder',
	functionPermission: FunctionPermissionEnum.手工录入订单,
}, {
	name: '标记为空单',
	action: 'markEmpty',
	point:Pointer.订单_订单打印_订单更多操作_标记为空单,
}, {
	name: '取消空单标记',
	action: 'cancelMarkEmpty',
}, {
	name: '拆分订单',
	action: 'splitOrder',
	point:Pointer.订单_订单打印_订单更多操作_拆分订单,
}, {
	name: '标记黑名单',
	action: 'addBlackList',
	point:Pointer.订单_订单打印_订单更多操作_标记为黑名单,
}, {
	name: '取消黑名单标记',
	action: 'removeBlackList',
}];

// 挂起订单更多
export const pendingTradeOperateList = [{
	name: '取消挂起',
	action: 'cancelPending',
}, {
	name: '订单操作日志',
	action: 'tradeOptLog',
}, {
	name: '处理异常',
	action: 'dealAbnormal',
	point:Pointer.订单_订单打印_订单更多操作_处理异常,
}];

/**
 * 该按钮数据是存到后端去的，只包含了一些基本信息，前端还需要二次加工以完善按钮信息，
 * 比如哪些按钮是库存版显示的，哪些按钮需要做表格数据勾选校验，哪些按钮需要触发埋点
 */

export interface IOptBtn {
	name: string;
	id: string;
	useful?: boolean; // 没看到什么作用
	sort?: number;
	onClick?: any;
	functionPermission?: FunctionPermissionEnum,  // 权限限制，BottomCom组件也有补充配置needHandleFunctionPermissionMap
	groupName?: string; // 自定义按钮设置里面的分组的备注名称
	isNew?: boolean; // 显示new
	[k: string]: any;
}

// 高级设置自定义按钮、底部按钮默认配置
// children为分组菜单，子成员无需存配置，子成员的排序也走本地配置
export const optBtn = {
	mainButton: [
		{
			name: '打印快递单',
			id: 'printKdd',
			useful: true,
			functionPermission: FunctionPermissionEnum.打印快递单,
		},
		{
			name: '打印发货单',
			id: 'printFhd',
			useful: true,
			functionPermission: FunctionPermissionEnum.打印发货单,
		},
		{
			name: '发货',
			id: 'send',
			useful: true,
			functionPermission: FunctionPermissionEnum.发货,
		},

	],
	moreButton: [
		{
			name: '批量拆单',
			id: 'batchSplitTypes',
			groupName: '批量拆单/批量按子订单拆分',
			children: [
				{
					name: '批量拆单',
					id: 'batchSplit',
					useful: true,
					tooltip: '根据设置规则，对订单按商品种类、数量、重量、库存情况进行拆分'
				},
				{
					name: '批量按子订单拆分',
					id: 'batchSplitBySubOrder',
					useful: true,
					tooltip: '仅支持合并订单按原订单进行拆分'
				},
			]
		},
		{
			name: '换商品',
			id: 'batchChangeGoods',
			useful: true,
		},
		{
			name: '添加商品',
			id: 'batchAddGiftGoods',
			useful: true,
		},
		{
			name: '生成标签/备货单',
			id: 'generatorTagTypes',
			children: [
				{
					name: '生成标签',
					id: 'generatorTag',
				},
				{
					name: '生成备货单',
					id: 'generatorReportBhd',
					useful: true,
				},
			]
		},
		{
			name: '获取运单号',
			id: 'getYdNos',
			useful: true,
		},
		{
			id: 'batchCreateWave',
			name: '生成拣货波次',
			useful: true,
		},
		{
			name: '自动发货',
			id: 'preShip',
			useful: true,
			functionPermission: FunctionPermissionEnum.自动发货,
		},
		{
			name: '无需物流发货',
			groupName: '无需物流/买家自提/卖家配送',
			id: 'noLogisticsSendTypes',
			children: [
				{
					name: '无需物流发货',
					id: 'noLogisticsSend',
					useful: true,
				},
				{
					name: '买家自提',
					id: 'buyerGet',
					useful: true,
				},
				{
					name: '卖家配送',
					id: 'sellerSend',
					useful: true,
				},
			]
		},
		{
			name: '指定供应商发货',
			id: 'supplierSend',
			useful: true,
		},
		{
			name: '追加包裹/重新发货',
			id: 'batchResendTypes',
			children: [
				{
					name: '追加包裹',
					id: 'batchAppendSend',
				},
				{
					name: '重新发货',
					id: 'batchResend',
					useful: true,
					functionPermission: FunctionPermissionEnum.重新发货,
				},
			]
		},
		{
			name: '设置快递',
			groupName: '指定快递/系统智选快递/清空快递',
			id: 'assignExpressTypes',
			children: [
				{
					name: '指定快递模板',
					id: 'assignExpress',
					useful: true,
					tooltip: '可指定订单打印发货的快递模版，指定后可通过智选快递查询对应的订单（需开启智选快递功能）'
				},
				{
					name: '系统智选快递', // 原批量重算快递
					id: 'batchRecalcExpress',
					useful: true,
					tooltip: '系统将根据智选快递规则计重新计算最优快递模版'
				},
				{
					name: '清空快递',
					id: 'clearExpress',
					useful: true,
					tooltip: '清空指定或系统推荐的快递模版'
				},


			]
		},
		{
			name: '备注/旗帜',
			groupName: '批量备注/旗帜/线下备注',
			id: 'batchModifyMemoTypes',
			children: [
				{
					name: '批量备注/旗帜',
					id: 'batchModifyMemo',
					useful: true,
				},
				{
					name: '批量线下备注',
					id: 'batchSysMemo',
					useful: true,
				},
			]
		},
		{
			name: '挂起/标记',
			groupName: '挂起/本地标记/空单标记',
			id: 'batchPendingTypes',
			children: [
				{
					name: '批量挂起订单',
					id: 'batchPending',
					useful: true,
				},
				{
					name: '取消订单挂起',
					id: 'batchCancelPending',
					useful: true,
				},
				{
					name: '批量本地标记',
					id: 'batchOrderSellerFlag',
					useful: true,
				},
				{
					name: '标记为空单',
					id: 'markEmpty',
					useful: true,
				},
				{
					name: '取消空单标记',
					id: 'cancelMarkEmpty',
					useful: true,
				},
			]
		},
		{
			name: '批量复制',
			id: 'batchCopyTradeInfo',
			useful: true,
			functionPermission: FunctionPermissionEnum.批量复制,
		},
		{
			name: '批量回收单号',
			id: 'batchCancelYdNo',
			useful: true,
			functionPermission: FunctionPermissionEnum.批量回收单号,
		},
		{
			name: '更新/关闭订单',
			id: 'batchUpdateTradeTypes',
			children: [
				{
					name: '批量更新订单',
					id: 'batchUpdateTrade',
					useful: true,
					tooltip: '操作后，将使用在系统中维护的商品信息更新订单'
				},
				{
					name: '批量关闭订单',
					id: 'batchClose',
					useful: true,
				},
			]
		},
		{
			name: 'BIC订单处理',
			groupName: '打印订单码/BIC订单发货',
			id: 'batchPrintBICTypes',
			children: [
				{
					name: '打印订单码',
					id: 'batchPrintBIC',
				},
				{
					name: 'BIC订单发货',
					id: 'batchSendBIC',
				},
			]
		},
		{
			name: '批量忽略异常',
			groupName: '商品未绑定/系统赠品异常',
			id: 'batchIgnoreAbnormalTypes',
			children: [
				{
					name: '商品未绑定',
					id: 'batchIgnoreAbnormal',
				},
				{
					name: '系统赠品异常',
					id: 'batchSysGiftAbnormal',
				},
			]
		},
		{
			name: '批量导出',
			id: 'batchExport',
			functionPermission: FunctionPermissionEnum.批量导出,
		},
		{
			name: '录入商品识别码',
			id: 'inputSerialNumber',
			useful: true,
		},
		{
			name: '设置发货仓库',
			id: 'setSendStorage',
			useful: true,
		},
		{
			name: '批量删除手工单',
			id: 'batchHandTradeDelete',
			useful: true,
		},
	],
	hiddenButton: [],
};


export const ExpressReachIconMap = {
	// 不可用
	'-1': {
		icon: 'bukeda',
		title: '当前快递不可用',
		color: '#FF3831',
		svg: true
	},
	// 不可达
	'0': {
		icon: 'bukeda',
		title: '当前选择的快递公司可能无法到达！',
		color: '#FF3831',
		svg: true
	},
	// 可达
	'1': {
		icon: 'keda',
		title: '当前选择的快递公司可以到达！',
		color: '#31B62E',
	},
	// 超区
	'2': {
		icon: 'wenhao-mian',
		title: '此地址为部分区域派送，请检查地址是否规范，或与您合作的网点核对！',
		color: '#EC3F3F',
	},
	// 未知
	'3': {
		icon: 'buqueding',
		title: '不确定是否可达',
		color: '#479DE4',
	},
	// 自提
	'4': {
		icon: 'ziti1',
		title: '需要自提',
		color: '#78B3C1',
	},
	// 未配置
	'5': {
		icon: 'buqueding',
		title: '当前快递未配置可达信息',
		color: '#479DE4',
	},
	'6': {
		icon: '',
		title: '',
		color: '',
	}
};

export const paginationPointMap = {
	'30': Pointer.订单_订单打印_订单分页_30条,
	'50': Pointer.订单_订单打印_订单分页_50条,
	'100': Pointer.订单_订单打印_订单分页_100条,
	'200': Pointer.订单_订单打印_订单分页_200条,
	'500': Pointer.订单_订单打印_订单分页_500条,
	'1000': Pointer.订单_订单打印_订单分页_1000条,
};

export const defaultColWidth = {
	localMemo: 200,
	buyerNick: 150,
	orderNumber: 60,
	receiverAddress: 200,
	tradeLabel: 120,
	sellerNick: 120,
	source: 120,
	distributor: 120,
	serviceLabel: 120,
	expressnum: 200,
	buyerMessage: 100,
	packageOrders: 200,
	totalPayment: 100,
	payTime: 80,
	created: 80,
	tid: 80,
	ptTid: 80,
	tradeFrom: 80,
	totalWeight: 80,
	authorInfo: 80,
	storageName: 80,
	tradeIndexPrintContent: 200,
	waveNoList: 120, // 添加波次号列宽度
};

export const defaultCopySetting: ICopySettingItem[] = [
	{ name: '收件人', isChecked: true, key: 'receiverNameMask' },
	{ name: '收件人电话', isChecked: true, key: 'receiverMobileMask' },
	{ name: '收件地址', isChecked: true, key: 'receiverAddress' },
	{ name: '邮编', isChecked: true, key: 'receiverZip' },
	{ name: '快递信息', isChecked: true, key: 'expressInfo' },
	{ name: '留言备注', isChecked: false, key: 'messageAndMemo' },
	{ name: '产品内容', isChecked: false, key: 'productInfo' }
];

export const handOrderMatchPlatformMap = [
	{
		name: '菜鸟',
		index: 0,
		key: 'cn',
		id: 3
	},
	{
		name: '抖音(不可用)',
		index: 1,
		key: 'dy',
		id: 8,
		disabled: true

	},
	{
		name: '拼多多',
		key: 'pdd',
		index: 2,
		id: 7

	},
	{
		name: '快手',
		index: 3,
		key: 'ks',
		id: 9

	},
	{
		name: '京东',
		index: 4,
		key: 'jd',
		id: 5
	},
	{
		name: '小红书',
		index: 5,
		key: 'xhs',
		id: 13
	},
	{
		name: '视频号',
		index: 6,
		key: 'sph',
		id: 14
	},
	{
		name: '得物',
		index: 7,
		key: 'dw',
		id: 17
	}
];

export enum templateType4PlatformTypeEnumOrigin {
	'tb' = 3,
	'pdd' =	7,
	'fxg' =	8,
	'ksxd' = 9,
	'jd' =	5,
	'xhs' = 13, // 16也是小红书
	'sph' = 14,
	'yz' = 15,
	'dw' = 17,
}

export const templateType4PlatformTypeEnum = new Proxy(templateType4PlatformTypeEnumOrigin, {
  get(target, prop: string | symbol) {
    if (typeof prop === 'string' && !isNaN(Number(prop))) {
      const value = Number(prop);
      if (value === 16) {
        return 'xhs';
      }
    }
    return target[prop as keyof typeof templateType4PlatformTypeEnumOrigin];
  }
});
// 平台字体图标映射
export const LabelPlatformMap = {
	[PLAT_TB]: {
		className: 'r-trade-taobao',
		text: '淘',
	},
	[PLAT_PDD]: {
		className: 'r-trade-pdd',
		text: '拼',
	},
	[PLAT_FXG]: {
		className: 'r-trade-doudian',
		text: '抖',
	},
	[PLAT_TM]: {
		className: 'r-trade-tm',
		text: '天',
	},
	[PLAT_KS]: {
		className: 'r-trade-ksxd',
		text: '快',
	},
	[PLAT_JD]: {
		className: 'r-trade-ksxd',
		text: '京',
	},
	[PLAT_HAND]: {
		className: 'r-trade-hand',
		text: '其',
	},
	[PLAT_HAND_OLD]: {
		className: 'r-trade-hand',
		text: '手',
	},
	[PLAT_OTHER]: {
		className: 'r-trade-hand',
		text: '其',
	},
	[PLAT_SPH]: {
		className: 'r-trade-sph',
		text: '视',
	},
	'xhs': {
		className: 'r-trade-xhs',
		text: '红',
	},
	'c2m': {
		className: 'r-trade-c2m',
		text: '淘',
	},
	[PLAT_SCM]: {
		className: 'r-trade-scm',
		text: '分',
	},
	[PLAT_SCMHAND]: {
		className: 'r-trade-scm',
		text: '分',
	},
	[PLAT_YZ]: {
		className: 'r-trade-scm',
		text: '赞',
	},
	[PLAT_DW]: {
		className: 'r-trade-doudian',
		text: '得',
	},
	[PLAT_KTT]: {
		className: 'r-trade-ktt',
		text: '快',
	},
};

/**
 * 标签状态: 1:待配货 2:已配货 3:缺货 4:已屯货 5:已退货 6:已发货 7:已作废
 */
export enum TakeGoodsLabelStatusEnum {
	WAIT_SORT = "待配货",
	ALREADY_SORT = "已配货",
	STOCKOUT = "缺货",
	STOCKPILE = "已屯货",
	REFUND = "已退货",
	SEND = "已发货",
	EXPIRE = "已作废",
	ARRIVAL_OF_GOODS = "已到货",
	待配货 = "WAIT_SORT",
	已配货 = "ALREADY_SORT",
	缺货 = "STOCKOUT",
	已屯货 = "STOCKPILE",
	已退货 = "REFUND",
	已发货 = "SEND",
	已作废 = "EXPIRE",
	已到货 = "ARRIVAL_OF_GOODS",
}
/**
  * 拿货小标签类型 take 拿货 SORT 拣货
  */
export enum TakeGoodsLabelTypeEnum {
	TAKE = "市场",
	SORT = "仓库",
	市场 = "TAKE",
	仓库 = "SORT"
}

/**
  * 打印状态: 0:未打印 1:已打印,
  */
export enum TakeGoodsLabelPrintStatusEnum {
	"UN_PRINT" = "未打印",
	"ALREADY_PRINT" = "已打印",
	未打印 = "UN_PRINT",
	已打印 = "ALREADY_PRINT"
}

export const OccupyTradeOptType = {
	'占用': 1,
	'释放': 2,
};

// 忽略异常类型
export const IgnoreTypeEnum = {
	'商品绑定异常': '1',
	'系统赠品异常': '2',
};

export const OccupyErrorTypeEnum = {
	'订单状态发生改变': 'TRADE_STATUS_CHANGE_ERROR',
	'订单商品库存不足': 'TRADE_ITEM_STOCK_ERROR'
};

/**
 * 爆款打单-查询视角
 */
export enum TopPrintSearchPerspective {
	"itemPerspective" = 0, // 商品视角
	"sysPerspective" = 1, // 货品视角
}

/**
 * 爆款打单-极简模式
 */
export enum TopPrintSearchItemTopMode {
	"open" = 2,  // 极简模式
	"off" = 1, // 普通模式
}



/**
 * 爆款打单-查询模式
 */
export enum TopPrintSearchModel {
	"MODEL_SKU" = 'sku', // 按款
	"MODEL_GOOD" = 'item', // 按商品
}
/**
 * 爆款打单-配置项名称
 */
export enum TopPrintConfigItem {
	"平台商品名称" = 'title',
	"平台商品ID" = 'numIid',
	"平台商家编码" = 'outerId',
	"简称" = 'sysItemAlias',
	"货品商家编码" = 'sysOuterId',
	"平台规格名称" = 'skuName',
	"平台规格编码" = 'outerSkuId',
	"货品规格名称" = 'sysSkuName',
	"规格别名" = 'sysSkuAlias',
	"货品规格编码" = 'skuOuterId',
}

/**
 * 爆款打单-配置
 */

export const TopPrintConfig = {
	[TopPrintConfigItem.平台商品名称]: {
		key: "title",
		stockValue: "1",
		class: "itemInfo", // 属于商品信息展示
		zeroStockValue: "21",
		name: '平台商品名称',
	},
	[TopPrintConfigItem.平台商品ID]: {
		key: "numIid",
		stockValue: "2",
		class: "itemInfo", // 属于商品信息展示
		zeroStockValue: "22",
		name: '平台商品ID',
	},
	[TopPrintConfigItem.平台商家编码]: {
		key: "outerId",
		stockValue: "3",
		class: "itemInfo", // 属于商品信息展示
		zeroStockValue: "23",
		name: '平台商家编码',
	},
	[TopPrintConfigItem.简称]: {
		key: "sysItemAlias",
		stockValue: "11",
		class: "itemInfo", // 属于商品信息展示
		zeroStockValue: "24",
		name: '简称',
	},
	[TopPrintConfigItem.货品商家编码]: {
		key: "sysOuterId",
		stockValue: "12",
		class: "itemInfo", // 属于规格信息展示
		zeroStockValue: null,
		name: '货品商家编码',
	},
	[TopPrintConfigItem.平台规格名称]: {
		key: "skuName",
		stockValue: "1",
		class: "skuInfo", // 属于规格信息展示
		zeroStockValue: "21",
		name: '平台规格名称',
	},
	[TopPrintConfigItem.平台规格编码]: {
		key: "outerSkuId",
		stockValue: "2",
		zeroStockValue: "22",
		class: "skuInfo", // 属于规格信息展示
		name: '平台规格编码',
	},
	[TopPrintConfigItem.货品规格名称]: {
		key: "sysSkuName",
		stockValue: "11",
		zeroStockValue: null,
		class: "skuInfo", // 属于规格信息展示
		name: '货品规格名称',
	},
	[TopPrintConfigItem.规格别名]: {
		key: "sysSkuAlias",
		stockValue: "12",
		zeroStockValue: "23",
		class: "skuInfo", // 属于规格信息展示
		name: '规格别名',
	},
	[TopPrintConfigItem.货品规格编码]: {
		key: "skuOuterId",
		stockValue: "13",
		zeroStockValue: null,
		class: "skuInfo", // 属于规格信息展示
		name: '货品规格编码',
	},
};

export const WaybillNoSource = {
	"系统申请来源": "1",
	"导入发货": "2",
	"无需物流": "3",
};

// 兜底默认列配置
// eslint-disable-next-line max-len
export const DEFAULT_COLUMN_CONFIG = "[{\"index\":0,\"ischecked\":true,\"isedit\":true,\"key\":\"index\",\"name\":\"序号\"},{\"index\":1,\"ischecked\":true,\"isedit\":false,\"key\":\"buyerNick\",\"name\":\"昵称\",\"width\":150},{\"index\":2,\"ischecked\":true,\"isedit\":true,\"key\":\"orderNumber\",\"name\":\"宝贝数量\",\"width\":60},{\"index\":3,\"ischecked\":true,\"isedit\":false,\"key\":\"receiverAddress\",\"name\":\"收货信息\",\"width\":245},{\"index\":4,\"ischecked\":true,\"isedit\":true,\"key\":\"tradeLabel\",\"name\":\"订单标签\",\"width\":100},{\"index\":5,\"ischecked\":true,\"isedit\":false,\"key\":\"expressnum\",\"name\":\"快递单号\",\"width\":200},{\"index\":6,\"ischecked\":true,\"isedit\":true,\"key\":\"serviceLabel\",\"name\":\"订单异常\",\"width\":100},{\"index\":7,\"ischecked\":true,\"isedit\":true,\"key\":\"buyerMessage\",\"name\":\"留言备注\",\"width\":160},{\"index\":8,\"ischecked\":false,\"isedit\":true,\"key\":\"sysMemo\",\"name\":\"线下备注\",\"preKey\":\"buyerMessage\",\"width\":160},{\"index\":9,\"ischecked\":false,\"isedit\":true,\"key\":\"sysMemoPic\",\"name\":\"线下备注图片\",\"preKey\":\"sysMemo\",\"width\":160},{\"index\":10,\"ischecked\":true,\"isedit\":true,\"key\":\"packageOrders\",\"name\":\"产品内容\",\"width\":300},{\"index\":11,\"ischecked\":false,\"isedit\":true,\"key\":\"totalPayment\",\"name\":\"实付金额\",\"width\":120},{\"index\":12,\"ischecked\":false,\"isedit\":true,\"key\":\"totalReceivedPayment\",\"name\":\"实收金额\",\"width\":120},{\"index\":13,\"ischecked\":false,\"isedit\":true,\"key\":\"payTime\",\"name\":\"付款时间\",\"width\":150},{\"index\":14,\"ischecked\":false,\"isedit\":true,\"key\":\"created\",\"name\":\"下单时间\",\"width\":150},{\"index\":15,\"ischecked\":false,\"isedit\":true,\"key\":\"tid\",\"name\":\"系统单号\",\"width\":200},{\"index\":16,\"ischecked\":false,\"isedit\":true,\"key\":\"ptTid\",\"name\":\"订单编号\",\"preKey\":\"tid\",\"width\":200},{\"index\":17,\"ischecked\":false,\"isedit\":true,\"key\":\"tradeFrom\",\"name\":\"店铺名称\",\"width\":200},{\"index\":18,\"ischecked\":false,\"isedit\":true,\"key\":\"totalWeight\",\"name\":\"重量\",\"width\":100},{\"index\":19,\"ischecked\":true,\"isedit\":true,\"key\":\"source\",\"name\":\"订单来源\"},{\"index\":20,\"ischecked\":false,\"isedit\":true,\"key\":\"authorInfo\",\"name\":\"达人信息\",\"preKey\":\"source\",\"width\":150},{\"index\":21,\"ischecked\":false,\"isedit\":true,\"key\":\"distributor\",\"name\":\"分销商\"},{\"index\":22,\"ischecked\":false,\"isedit\":true,\"key\":\"tradeType\",\"name\":\"订单类型\"},{\"index\":23,\"ischecked\":false,\"isedit\":true,\"key\":\"storageName\",\"name\":\"发货仓库\"},{\"index\":24,\"ischecked\":false,\"isedit\":true,\"key\":\"tradeIndexPrintContent\",\"name\":\"发货内容\",\"width\":245},{\"index\":25,\"ischecked\":false,\"isedit\":true,\"key\":\"waveNoList\",\"name\":\"波次号\"},{\"index\":26,\"ischecked\":true,\"isedit\":false,\"key\":\"operate\",\"name\":\"操作\"}]";

export const ABNORMAL_MODAL_BUTTON_PROPS = {
	cancelText: '我知道了',
	cancelButtonProps: {
		type: 'primary' as const
	},
	okText: '开启异常检测',
	okButtonProps: {
		type: 'link' as const,
		// target: '_blank',
		href: '#/settings/system?introName=openAbnormalCheck'
	},
};

export const enum ConsolidateTypeEnum {
	香港集运 = 0,
	新疆集运 = 1,
	西藏中转 = 3,
	甘肃中转 = 14,
	内蒙古中转 = 15,
	宁夏中转 = 16,
	青海中转 = 17,
}

// 拼多多集运类型
export const ConsolidateType = {
	[ConsolidateTypeEnum['香港集运']]: {
		name: "香港集运",
		key: "consolidate_hk",
		course: "https://mms.pinduoduo.com/daxue/detail?courseId=5460"
	},
	[ConsolidateTypeEnum['新疆集运']]: {
		name: "新疆集运",
		key: "consolidate_xj",
		course: "https://mms.pinduoduo.com/daxue/detail?courseId=5435"
	},
	[ConsolidateTypeEnum['西藏中转']]: {
		name: "西藏中转",
		key: "consolidate_xz",
		course: "https://mms.pinduoduo.com/daxue/detail?courseId=6164"
	},
	[ConsolidateTypeEnum['甘肃中转']]: {
		name: "甘肃中转",
		key: "consolidate_gs",
		noTip: true,
	},
	[ConsolidateTypeEnum['内蒙古中转']]: {
		name: "内蒙古中转",
		key: "consolidate_nmg",
		noTip: true,
	},
	[ConsolidateTypeEnum['宁夏中转']]: {
		name: "宁夏中转",
		key: "consolidate_nx",
		noTip: true,
	},
	[ConsolidateTypeEnum['青海中转']]: {
		name: "青海中转",
		key: "consolidate_qh",
		noTip: true,
	},
};

export const filterConsolidateType = Object.values(ConsolidateType).reduce((a, b) => { a[b.key] = b.name; return a; }, {});

export const FxgConsolidateType = {
	'fxg_1': '新疆中转',
	'fxg_2': '西藏中转',
};

/**
 * 订单标签 serviceTagList
 * serviceTagList 是所有的标签都在一起，包含订单标签，订单异常标签，注意这里只处理订单标签，不然异常标签也显示到一起了
 * class trade-warn-filled 纯黄色背景 trade-error-filled纯红色背景 trade-gray-filled 灰色背景 trade-error-outlined 镂空红色边框
 * name 标签过滤名称
 * tagName 标签名称
 * tip Tooltip提示信息
 */
export const serviceTagTypeMap = {
	bic_order: {
		name: 'BIC质检订单',
		class: 'trade-error-outlined'
	},
	bicInspected: {
		name: 'BIC订单已送检',
		tagName: 'BIC已送检',
		class: 'trade-warn-filled', // 纯黄色背景
	},
	remote_derict: {
		name: '偏远直邮'
	},
	superLuckyBag: {
		name: '福袋'
	},
	replenishSendOrder: {
		name: '补寄订单'
	},
	appendSend: {
		name: '追加包裹',
		class: 'trade-warn-filled'
	},
	bondedOrder: {
		name: '保税订单',
	},
	presentOrder: {
		name: '礼物订单',
	},
	sysGift: {
		name: '含系统赠品',
		class: 'trade-error-outlined'
	},
	xsd_ptps: {
		name: '小时达:平台配送',
		tagName: '小时达(平台配送)',
		class: 'trade-error-outlined'
	},
	xsd_sjzps: {
		name: '小时达:商家自配送',
		tagName: '小时达(商家自配送)',
		class: 'trade-error-outlined'
	},
	aezy: {
		name: 'AE自营订单',
		class: 'trade-error-outlined',
		tip: '1688AE自营订单，仅支持平台发货'
	},
	gfcf: {
		name: '官方仓发',
		class: 'trade-error-outlined',
		tip: '此订单无需商家自行发货，请勿打单发货造成重复发货损失'
	},
	zt: {
		name: '自提',
		class: 'trade-error-outlined',
		tip: ''
	},
	tcps: {
		name: '同城配送',
		class: 'trade-error-outlined',
		tip: ''
	},
	wxwl: {
		name: '无需物流',
		class: 'trade-error-outlined',
		tip: ''
	},
};

/**
 * 订单异常标签 serviceTagList
 * serviceTagList 是所有的标签都在一起，包含订单标签，订单异常标签，注意这里只处理订单异常标签
 * class trade-warn-filled 纯黄色背景 trade-error-filled纯红色背景 trade-gray-filled 灰色背景 trade-error-outlined 镂空红色边框
 * name 标签过滤名称
 * tagName 标签名称
 * tip Tooltip提示
 */
export const serviceTagTypeFilterAbnormalMap = {
	sysGiftAbnormal: {
		name: '系统赠品异常',
		class: 'trade-error-filled',
		tip: '订单下存在子订单产生售后，请复核当前系统赠品是否无误'
	},
	abnormalOnlineRefunded: {
		name: '线上已退款',
		class: 'trade-error-filled',
	},
	bizWaveAbnormal: {
        name: '波次拣货异常',
        class: 'trade-error-filled',
        //tip: '订单已生成拣货波次，无法进行操作'
    },
	wxfh: {
		name: '无需发货',
		class: 'trade-error-filled',
		tip: ''
	},
	fhqbftsp: {
		name: '发货前部分退商品',
		class: 'trade-error-filled',
		tip: ''
	},
};

// 过滤的时候需要加到pack上，将key值进行处理
export const getServiceFilterKey = (a) => `service_tag_${a}`;

export const filterServiceTagType = Object.keys(serviceTagTypeMap)
	.reduce((a, b) => {
		a[getServiceFilterKey(b)] = serviceTagTypeMap?.[b]?.name;
		return a;
	}, {});

export const packInitialFieldsMap = [{
	field: 'isHideByPending',
	initialVal: false,
}, {
	field: 'isHideByDropShip',
	initialVal: false,
}, {
	field: 'isHideByRefund',
	initialVal: false,
}, {
	field: 'isHidePartShipTrade',
	initialVal: false,
}, {
	field: 'duoduoWholesale',
	initialVal: false,
}, {
	field: 'shipHold',
	initialVal: false,
}, {
	field: 'freeSF',
	initialVal: false,
}, {
	field: 'isMendian',
	initialVal: false,
}, {
	field: 'jdOrderShipType',
	initialVal: null,
}, {
	field: 'jdStoreOrderType',
	initialVal: null,
}, {
	field: 'jdjp',
	initialVal: false,
}, {
	field: 'jdjc',
	initialVal: false,
}, {
	field: 'jdyc',
	initialVal: false,
}, {
	field: 'occupiedStockStatus',
	initialVal: '',
}, {
	field: 'isChecked',
	initialVal: false,
}, {
	field: 'isPreSale',
	initialVal: false,
}, {
	field: 'isPartShipped',
	initialVal: false,
}, {
	field: 'isLimited',
	initialVal: false,
}];

export enum DEFAULT_SHOP_SELECT {
	默认不选择平台店铺 = 1,
	默认其他无店铺 = 2,
	继续选择上次创建时的平台店铺 = 3,
}

export enum TRADE_TYPE_ENUM {
	'pt' = "普通订单",
	'exchange_goods' = "换货订单",
	're_goods' = "补发订单",
	普通订单 = "pt",
	换货订单 = "exchange_goods",
	补发订单 = "re_goods",
}

export const PRINT_CONTENT_MAX_LENGTH = 10000;

export const PRINT_CONTENT_MAX_ROW = 1000

// 快递公司类型
export enum ExCompanyTypeEnum {
	快递 = 1,
	快运 = 2
}

export const EVENT_BUS = {
	// 批打页面用上一次的查询条件重新触发查询
	"REQUEST_AGAIN": "trade_request_list_again",
};

export const enum BQ_PRINT_TYPE {
	标签 = "bq",
	退货单 = "thd",
	吊牌 = "dpd",
	采购单 = "cgd",
	入库单 = "rkd",
	出库单 = "ckd",
	退货清单 = "thqd",
	直播标签 = "zbd",
	商品备货标签 = 'bhbq'
}

export enum PAY_DISPLAY_TYPE_ENUM {
	花呗分期 = "ANT_CREDIT_PAY",
	红包 = "COUPON_FEE",
	信用卡 = "CREDIT_CARD",
}

export const PAY_DISPLAY_TYPE_ICON_MAP = {
	[PAY_DISPLAY_TYPE_ENUM.花呗分期]: 'huabei',
	[PAY_DISPLAY_TYPE_ENUM.红包]: 'hongbao',
	[PAY_DISPLAY_TYPE_ENUM.信用卡]: 'xinyongka',
};

export const PAY_DISPLAY_TYPE_COLOR_MAP = {
	[PAY_DISPLAY_TYPE_ENUM.花呗分期]: '#4182FA',
	[PAY_DISPLAY_TYPE_ENUM.红包]: '#FF4D4F',
	[PAY_DISPLAY_TYPE_ENUM.信用卡]: '#FFAA00',
};
