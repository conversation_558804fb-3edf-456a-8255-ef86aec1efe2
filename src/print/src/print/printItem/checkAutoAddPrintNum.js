function checkAutoAddPrintNum({ orderList, othersSet, ydnumCount }) {
    const isAutoAddPrintNum = othersSet.isAutoAddPrintNum == 1; //0 为不自动追加单号到备注
    const isNotAddPrintNumOnlyOne = othersSet.isNotAddPrintNumOnlyOne == 0; //1 仅打印1个单号时不追加
	const setting = window?.erpData?.advancedSetting?.printSetExpandDTO || {}
	const addYdNoShipped = setting.addYdNoShipped == 2;
    // 当前模板是否有「卖家备注」 f_memo 这个输入框
    // let hasFINfoInput = false;
    // try{
    //     void function(){
    //         for(let i = 0; i < modeInfo.ModeInputs.length; i++) {
    //             const input = modeInfo.ModeInputs[i];
    //             for(let j = 0; j < input.proArray.length; j++) {
    //                 if(input.proArray[j].Dataname === 'f_memo') {
    //                     hasFINfoInput = true;
    //                     return;
    //                 }
    //             }
    //         }
    //     }();
    // } catch(e){
    //     console.error(e);
    // }
    let isAutoAdd = false;
    // 自动追加多单号

    if((isAutoAddPrintNum && isNotAddPrintNumOnlyOne) || (isAutoAddPrintNum && !isNotAddPrintNumOnlyOne && ydnumCount > 1)) {
        isAutoAdd = true;
    }

    if(isAutoAddPrintNum && isNotAddPrintNumOnlyOne){ // 自动追加多单号，仅打印1个单号时也追加 并且模板本身有「卖家备注」这个数据框
        const newOrderList = orderList.map(order => {
			if (addYdNoShipped && !(order.onlineShip || order.sendStatus === 'WAIT_BUYER_CONFIRM_GOODS' || order.firstSend)) return order
            order.isAutoAddYoId = true;
            order.sellerMome =  order.sellerMome.split(';').map(o =>{
                order.allYdNos && order.allYdNos.forEach(yid => {
                    if(o.indexOf(yid) === -1) {
                        o += (',' +yid); //备注追加所有获取的单号
                    }
                });
                return o
            }).join(';')


            return order;
        });
        return {
            orderList: newOrderList,
            isAutoAdd,
        };
    } else if(isAutoAddPrintNum && !isNotAddPrintNumOnlyOne) { // 自动追加多单号，仅打印1个单号时不追加 并且模板本身有「卖家备注」这个数据框
        const newOrderList = orderList.map(order => {
			if (addYdNoShipped && !(order.onlineShip || order.sendStatus === 'WAIT_BUYER_CONFIRM_GOODS' || order.firstSend)) return order
            if(order.allYdNos && order.allYdNos.length > 1) {
                order.isAutoAddYoId = true;
                    order.sellerMome =  order.sellerMome.split(';').map(o =>{
                        order.allYdNos.forEach(yid => {
                            if(o.indexOf(yid) === -1) {
                                o += (',' +yid); //备注追加所有获取的单号
                            }
                        });
                    return o
                }).join(';')
            }

            return order;
        });
        return {
            orderList: newOrderList,
            isAutoAdd,
        };
    } else{
        return {
            orderList,
            isAutoAdd,
        };
    }
}

export { checkAutoAddPrintNum };
