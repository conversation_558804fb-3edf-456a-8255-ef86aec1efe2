import { Button, Checkbox, Dropdown, Form, Input, InputNumber, Menu, Modal, Popover, Radio, Tooltip,Image } from "antd";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { observer } from "mobx-react";
import { DownOutlined, QuestionCircleOutlined } from "@ant-design/icons";
import type { CheckboxChangeEvent } from 'antd/es/checkbox';
import _, { cloneDeep } from "lodash";
import s from './index.module.scss';
import { handStore, tradeStore } from "@/stores";
import { calcInputVal, decryptRewrite, getAdvancedHandShopSelect, getCostPrice, scmPackTips } from "../../utils";
import Icon from "@/components/Icon";
import SenderSelectModal from "./components/senderSelectModal";
import ReceiverSelectModal from "./components/receiverSelectModal";
import SavaGoodsSelect from "./components/SaveGoodsSelect";
import { TradeHandTradeCreatedRequest } from "@/types/trade/handOrder";
import {
	ItemSysItemListOfItemRelationPlatformItemViewResponse, SysItem,
} from "@/types/schemas/warehouse/system";
import AddrSelect from "@/components-biz/AddrSelect";
import { TradeAddressParseBeAiParseApi, TradeAddressParseBeParseApi, TradeHandTradeCreatedApi, TradeHandTradeUpdataApi } from "@/apis/trade/handOrder";
import { IndexSettingEditRecipientSettingApi, IndexSettingEditSenderSettingApi } from "@/apis/user";
import { IPackage } from "../../interface";
import userStore from "@/stores/user";
import { TradePrintContentSetRule } from "@/types/trade/tradeSet";
import tradeSetStore from "@/stores/trade/tradeSet";
import { genePrintContent } from "@/utils/trade/printContent";
import event from "@/libs/event";
import sendPoint from "@/utils/pointTrack/sendPoint";
import { SettingGetRecipientSettingResponseObj, SettingGetSenderSettingObj } from "@/types/schemas/user";
import Pointer from "@/utils/pointTrack/constants";
import { ItemSysItemListOfItemRelationPlatformItemViewApi, SysItemGetSysItemDetailApi } from '@/apis/warehouse/system';
import { TradeOperateLogAddApi, TradeTradeDetailGetApi, TradeTradeDetailGetByPtTidApi } from "@/apis/trade";
import { TradeOperateLogAddRequest, TradeTradeDetailGetResponse } from "@/types/trade/index";
import { decryptFn } from '../ListItem/components/Decrypt';
import message from "@/components/message";
import { weightUnit } from '@/pages/Index/Settings/System/constants';
import { GoodsOrderTitle, ZeroStockVersionGoodsOrderTitle } from "./constant";
import history from "@/utils/history";
import ShopSingleSelect from "@/components-biz/ShopListSelect/shopSingleSelect";
import { PLAT_HAND, PLAT_HAND_SHOP_NAME, PLAT_JD, PLAT_OTHER, flagArr, allFlagArr, PLAT_SPH, PLAT_YZ, PLAT_DW, PLAT_MAP, DEFAULT_IMG, PLAT_FXG, PLAT_KTT } from "@/constants";
import { platform } from "@/types/schemas/common";
import QuickSetShopModal from "./components/QuickSetShopModal";
import { DEFAULT_SHOP_SELECT } from "../../constants";
import memoFn from "@/libs/memorizeFn";
import { accMul, removeZeroWidthChars } from "@/utils/util";
import FieldsPermissionCheck, { FieldsPermissionEnum } from "@/utils/permissionCheck/fieldsPermissionCheck";
import { local } from "@/libs/db";
import { isSourceScm } from "@/components-biz/ShopListSelect/shopListUtils";
import WeightInput from '@/components/Input/InputNumber/WeightInput';
import { isSourceHand } from "@/components-biz/ShopListSelect/shopListUtils";
import ChooseGoodsModal from "../ChangeAndAddGoods/ChooseGoodsModal";
import { useGetState, useReactive } from "ahooks";
import { allPlatform, GoodsTypeEnum } from "../ChangeAndAddGoods/constants";
import { SysExchangeTypeEnum } from "../BottomCom/constants";
import HandOrderQuickContent from './components/HandOrderQuickContent';
import AuthorMultiSelect from '@/components-biz/ShopListSelect/AuthorMultiSelect';

import { TradeOptEnum } from "@/utils/enum/trade";
import { FunctionPermissionEnum } from '@/utils/permissionCheck/functionPermissionCheck';
export interface IHandOrderModalProps {
	onOk: (pack: IPackage) => void;
	onEditOk: (pack: IPackage)=>void;
	handData: IPackage
}

let _dropMenuPriceList = [{
	key: 'price',
	name: '售价',
}, {
// 	 key: 'retailPrice',
// 	 name: '零售价',
// }, {
// 	 key: 'tradePrice',
// 	 name: '批发价',
// }, {
	 key: 'tagPrice',
	 name: '吊牌价',
}, {
	 key: 'costPrice',
	 name: '成本价',
	 fieldsPermission: FieldsPermissionEnum.成本价
}, {
	 key: 'customPrice',
	 name: '自定价'
}];

let zeroStockDropMenuPriceList = [{
	key: 'costPrice',
	name: '成本价',
	fieldsPermission: FieldsPermissionEnum.成本价
}, {
	 key: 'customPrice',
	 name: '自定价'
}];
/**
 * 手工单专用价格类型 1：网销价；2:零售价；3：批发价 4：吊牌价 5：成本价 6 自定义价
 */
const priceType = {
	price: "1",
	retailPrice: "2",
	tradePrice: "3",
	tagPrice: "4",
	costPrice: "5",
	customPrice: "6"
};

const priceTypeMap = {
	'1': 'price',
	'2': 'retailPrice',
	'3': 'tradePrice',
	'4': 'tagPrice',
	'5': 'costPrice',
	'6': 'customPrice',
};

const initialParams: TradeHandTradeCreatedRequest = {
	remarkFlag: '',
	orders: [],
	payment: '',
	discount: '',
	num: 0,
	shipInfo: '',
	tid: '',
	weight: 0,
	postFee: '',
	tradeEncodeType:1, // 1：自加密 2：平台加密
	authorList: undefined
};

/* eslint-disable no-template-curly-in-string */
const validateMessages = {
	required: '请输入${label}!',
};
/* eslint-enable no-template-curly-in-string */

interface IOptParams {
	senderAddrType: 'choose' | 'input',
	receiverAddrType: 'choose' | 'input',
	advancedAddress: string,
	autoSaveSender: boolean,
	autoSaveReceiver: boolean,
	copyTid: string,
	useOriginEncrypt: boolean,
}

interface IShopSelected {
	shopId?: string;
	plat?: string;
	sellerNick?: string;
}

let printContent : TradePrintContentSetRule = null;
let keyWords:string[] = null;
let advancedSetRes;
enum copyTypeEnum {
	"收件人信息" = "0",
	"订单编号" = "1",
	"系统单号" = "2"
}

const maxAddCount = 50;

const HandOrderModal = (props: IHandOrderModalProps) => {
	const { setIsShowHandOrderModal } = handStore;
	// const { userInfo, inventoryDeduct, isShowZeroStockVersion, hasFieldsPermission } = userStore;
	const { userInfo, inventoryDeduct, isShowZeroStockVersion, isDistributorAccount, hasFieldsPermission, hasFunctionPermission } = userStore;
	// const {
	// 	batchAddGoodsPackage,
	// 	batchAddGoodsTrade,
	// } = editGoodsStore;
	let dropMenuPriceList = isShowZeroStockVersion ? zeroStockDropMenuPriceList : _dropMenuPriceList;
	const { onOk, handData, onEditOk } = props;
	const platform = handData.platform;
	console.log('props2222',platform)
	const [form] = Form.useForm();
	const [loading, setLoading] = useState(false);
	const [copyLoading, setCopyLoading] = useState(false);
	const [shopSelected, setShopSelected] = useState<IShopSelected>({});

	const [copyType, setCopyType] = useState(
		!handData.copyTid?.length
			? copyTypeEnum.收件人信息
			: handData.isSplit
				? copyTypeEnum.系统单号
				: copyTypeEnum.订单编号
	);
	const [saveType, setSaveType] = useState('');
	const [editFlag, setEditFlag] = useState(false);
	const [firstEdit, setFirstEdit] = useState(false);
	const [isShowSenderSelectModal, setIsShowSenderSelectModal] = useState(false);
	const [isShowReceiverSelectModal, setIsShowReceiverSelectModal] = useState(false);
	const [isShowQuickSetShopModal, setIsShowQuickSetShopModal] = useState(false);
	const [isVirtualCode, setIsVirtualCode] = useState(false);
	const [optParams, setOptParams] = useState<IOptParams>({
		senderAddrType: 'choose',
		receiverAddrType: 'choose',
		advancedAddress: '',
		autoSaveSender: false,
		autoSaveReceiver: false,
		copyTid: "",
		useOriginEncrypt: platform == PLAT_FXG,
	});
	const [params, setParams] = useState<TradeHandTradeCreatedRequest>(_.cloneDeep({
		...initialParams,
		tradeEncodeType: platform == PLAT_FXG ? 2 : 1
	}));
	const [trade, setTrade] = useState<any>({});
	// 复制创建新单
	const [copyCreateTid, setCopyCreateTid] = useState('');
	const [toolTipTitle, setToolTipTitle] = useState('');
	const [decryptedError, setDecryptedError] = useState(false);
	const [isKg, setIsKg] = useState(false);
	const isFirst = useRef(true)
	const [notCloseModal, setNotCloseModal] = useState(local.get('handOrderModal-notCloseModal') || false); // 保存后不自动关闭弹框
	const isDecrypted = params.tradeEncodeType === 2

	// 弹框需要的数据
	const modalState = useReactive({
		visible: false,
		data: {} as any,
	});
	const [uniqIdList, setAllUniqIdList, getAllUniqIdListList] = useGetState([]);
	const [dataSource, setDataSource, getDataSource] = useGetState([]);

	//创建手工单回显列表
	const [detailOrders,setDetailOrders] = useState<any[]>([]);

	useEffect(() => {
		let uniqIdList = [];
		dataSource.forEach(i => {
			uniqIdList = uniqIdList.concat(i.uniqIdList);
		});
		setAllUniqIdList([...new Set(uniqIdList)]);
	}, [dataSource]);

	console.log('isDecrypted', isDecrypted,dropMenuPriceList);
	useEffect(() => {
		userStore.getUserSetting().then(res => {
			setIsKg(res?.weightUnit == weightUnit.显示kg);
			const getVirtualCode = localStorage.getItem(`${res?.userId}_VirtualCode`);
			setIsVirtualCode(getVirtualCode == 'true');
		});
		console.log(handData,'handDatahandData')
		let fn = async() => {
			try {
				await userStore.getSystemSetting();
				printContent = await tradeSetStore.getPrintContentSet();
				const filterWord = await tradeSetStore.getFilterWord();
				keyWords = filterWord;
				advancedSetRes = await memoFn.getAdvancedSet();
				if (handData && handData.trades && handData.trades.length > 0) {
					setEditFlag(true);
					dealEditParams();
				}
				if (handData && handData.copyTid) {
					const copyTid = handData.copyTid[0];
					const isSplit = handData.isSplit;
					setCopyType(isSplit ? copyTypeEnum.系统单号 : copyTypeEnum.订单编号);
					setOptParams(prev => ({
						...prev,
						copyTid,
					}));
					copyOrder(copyTid,handData.tradeEncodeType);
				}
				await userStore.getAuthorList(false); // 获取所有达人信息
			} catch (error) {
				console.error('获取发货内容设置失败', error);
			}
		};
		fn();
		sendPoint(Pointer.订单_订单打印_手工单_新建手工单_展现);
	// eslint-disable-next-line react-hooks/exhaustive-deps
	}, []);

	useEffect(() => {
		if (editFlag||isDecrypted) return;
		const defaultHandConfig = getAdvancedHandShopSelect(tradeStore.setting);
		let shopSelected;
		// eslint-disable-next-line default-case
		switch (defaultHandConfig.handShopSelect) {
			case DEFAULT_SHOP_SELECT.继续选择上次创建时的平台店铺:
				try {
					const shopInfo = localStorage.getItem(`${userStore?.userInfo?.userId}_HandOrderShopInfo`);
					const shopInfoObj = JSON.parse(shopInfo);
					const findShop = userStore?.shopList?.find(i => {
						return shopInfoObj?.plat == i.platform && shopInfoObj?.shopId == i.sellerId;
					});
					if (findShop) shopSelected = shopInfoObj;
				} catch (error) {
					console.log('error: ', error);
				}
				break;

			case DEFAULT_SHOP_SELECT.默认其他无店铺:
				shopSelected = { shopId: userStore?.userInfo?.userId, plat: PLAT_HAND, sellerNick: PLAT_HAND_SHOP_NAME };
				break;
		}

		form.setFieldsValue({ shopSelected });
	// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [tradeStore?.setting?.printSetExpandDTO?.defaultHandConfig, editFlag]);

	const setLocalMemoOfShopInfo = () => {
		const shopInfo = form.getFieldValue('shopSelected');
		localStorage.setItem(`${userStore?.userInfo?.userId}_HandOrderShopInfo`, JSON.stringify(shopInfo));
	};


	const getMobilePhone = (phone:string) => {
		let mobileArr = (phone ?? '')?.split('-');
		return {
			_receiverMobile: mobileArr[0],
			_receiverMobileSecret: mobileArr[1] || ""
		};
	};

	// * 编辑手工单 数据回显
	const dealEditParams = () => {
		const { isShowKg } = userStore;
		try {
			let _data = handData.trades[0];
			const authorsMap = new Map();
			console.log('_data_data',_data);
			let { discountAmount, orders, receiverAddress, receiverDistrict,
				receiverState, receiverName, receiverPhone,tradeEncodeType,receiverNameMask,receiverAddressMask,
				receiverPhoneMask,receiverMobileMask,senderName, senderPhone, senderAddress,
				receiverMobile, senderMobile, realTid, ptTid, tid, sellerMemoFlag, sellerId, platform, sellerNick, waitReceiveAmount } = _data;
			let _orders:any = [];
			orders.forEach((order:any, index) => {
				// 如果是无库存版本 没有商品的 order 将不展示
				if (order.numIid !== "-1") {
					_orders.push({
						...order,
						outerId: order.outerId,
						platformItemSkuList: [{
							picUrl: order.picPath,
							skuId: order.skuId,
							skuName: order.skuPropertiesName,
							skuOuterId: order.outerSkuId,
							platform: order.itemPlatform,
							sellerId: order.itemSellerId,
							relationSystemItemList: [{
								sysSkuId: order.systemSkuId,
								sysItemAlias: order.titleShort,
								sysSkuAlias: order.skuAlias,
								sysItemId: order.systemNumIid,
							}]
						}],
						picUrl: order.picPath,
						sysItemId: order.numIid,
						sysItemAlias: order.title,
						skuOuterId: order.outerId,
						sysSkuId: order.skuId,
						sysSkuName: order.skuPropertiesName,
						sysSkuAlias: order.skuAlias,
						num: order.num,
						discount: order.discount || 0,
						payment: order.payment || 0,
						weight: isShowKg ? ((order.weight || 0) / 1000 / order.num).toFixed(3) : (order.weight / order.num).toFixed(0),
						priceKey: priceTypeMap[(order.priceType + '') || '1'],
						apiPrice: order.price,
						type: order.systemNumIid && order.systemSkuId && order.numIid === order.systemNumIid && order.skuId === order.systemSkuId ? GoodsTypeEnum.系统货品 : GoodsTypeEnum.平台商品
					});
					authorsMap.set(order.authorId, { authorId: order.authorId, authorName: order.authorName, platform: order.platform});
				}
			});

			const authorList = Array.from(authorsMap.values()); // 去重后达人
			const filterAuthorList = authorList?.filter(item=> userStore.authorList?.find(d=>item.authorId == d.authorId)); // 需要在数据里面
			const newAuthorList = {authorList: filterAuthorList?.length ? [filterAuthorList[0]] : []}; // 手工单这里达人是单选，直接取第一个

			authorsMap.clear();

			let _params: any = {
				..._data,
				discount: discountAmount,
				num: orders.length,
				orderNum: orders.length,
				receiverCounty: receiverDistrict,
				receiverProvince: receiverState,
				orders: _orders,
				remarkFlag: sellerMemoFlag,
				tradeEncodeType,
				authorList: newAuthorList
			};
			setTrade(()=>({..._params}));
			
			const shopSelected = { shopId: sellerId, plat: platform, sellerNick };
			const { _receiverMobile, _receiverMobileSecret } = getMobilePhone(receiverMobile);
			let printContent = calcPrintContent(_orders);
			form.setFieldsValue({
				receiverName:tradeEncodeType == 2? receiverNameMask : receiverName,
				receiverMobile:tradeEncodeType == 2?receiverMobileMask: _receiverMobile,
				receiverPhone:tradeEncodeType == 2?receiverPhoneMask:receiverPhone,
				receiverAddress:tradeEncodeType == 2?receiverAddressMask:receiverAddress,
				senderName,
				senderMobile,
				senderPhone,
				senderAddress,
				receiverMobileSecret: _receiverMobileSecret,
				// 这儿还取shipinfo 是因为无库存版 可能会在此处设置一些信息
				shipInfo: printContent,
				tid: realTid || ptTid || tid,
				shopSelected,
				waitReceiveAmount,
				authorList: newAuthorList, 
			});
			setParams(() => {
				calcTotalInfo(_params, true);
				return _params;
			});
		} catch (error) {
			console.log('dealEditParams error:', error);
		}

	};

	// 复制订单
	const copyOrder = async(tid:string = "", tradeEncodeType = undefined) => {
		try {
			setCopyLoading(true);
			console.log("optParams.copyTid.trim():",optParams, optParams.copyTid.trim(), tid);
			const copyTid = tid || optParams.copyTid.trim();
			let optTid = optParams.copyTid.trim();
			let uglyCopyTid = '';
			if (optTid && !optTid.endsWith('A')) {
				uglyCopyTid = optTid + 'A';
			}
			if (copyTid) {
				// 根据要复制的订单编号查询订单
				const { trade, skuIds, sysSkuIds, orderObj }:{trade: IPackage,skuIds?:any[], sysSkuIds?:{sysItemId: string, sysSkuId: string}[], orderObj?: object} = await getCopyOrder(copyTid, uglyCopyTid);

				if (isSourceScm(trade)) {
					scmPackTips();
					setCopyLoading(false);
					return;
				}
				if (!isSourceHand(trade) && [PLAT_DW, PLAT_SPH, PLAT_YZ, PLAT_KTT].includes(trade.platform) && optParams.useOriginEncrypt) {
					const platTxt = PLAT_MAP[trade.platform];
					message.info(`${platTxt}不支持复制创建密文手工单`);
					setCopyLoading(false);
					return;
				}
				setTrade(() => trade);
				const oldParams = cloneDeep(params)
				if (PLAT_FXG == trade.platform && isFirst.current) {
					oldParams.useOriginEncrypt = true;
					oldParams.tradeEncodeType = 2;
					setOptParams(pre => ({ ...pre, useOriginEncrypt: true }))
					decryptAddress(trade, optParams.useOriginEncrypt || true);
					isFirst.current = false
				} else {
					oldParams.tradeEncodeType =  optParams.useOriginEncrypt ? 2 : 1
					decryptAddress(trade, tradeEncodeType ? tradeEncodeType == 1 : optParams.useOriginEncrypt);
				}
				// 解析订单地址
				console.log('useOriginEncrypt.form.setField22sValue',tradeEncodeType,trade,trade.trades?.[0]?.realTid ,optParams.useOriginEncrypt,params.tradeEncodeType);

				const { receiverState, receiverCity, receiverDistrict, sellerMemoFlag, sellerMemo, sellerId, platform, sellerNick, tid } = trade.trades[0];
				const { totalPlatformDiscount, totalPostFee, waitReceiveAmount, buyerNick, buyerOpenUid } = trade;
				const shopSelected = { shopId: sellerId, plat: platform, sellerNick };
				const realTid = trade?.trades?.[0]?.realTid || trade.ptTids?.[0] || trade.tids?.[0];
				const order = trade?.trades?.[0]?.orders?.[0];

				let newAuthorList = {authorList: []};
				if(order?.authorId){	
					const authorItem = userStore.authorList?.find(d=>order?.authorId == d.authorId);
					if(authorItem){
						newAuthorList = {authorList: [{ authorId: order.authorId, authorName: authorItem.authorName, platform: order.platform}]}
					}
				}

				setParams(()=>({
					...oldParams,
					waitReceiveAmount,
					receiverProvince: receiverState,
					receiverCity,
					receiverCounty: receiverDistrict,
					remarkFlag: sellerMemoFlag,
					sellerMemo,
					postFee: totalPostFee || "0.00",
					discount: totalPlatformDiscount || "0",
					realTid,
					oaid: trade.caid,
					buyerNick,
					buyerOpenUid,
					sellerId,
					authorList: newAuthorList
				}));
				console.log('useOriginEncrypt.form.setFieldsValue222', PLAT_FXG == trade.platform ,isFirst.current, params.useOriginEncrypt);
				form.setFieldsValue({
					shopSelected,
					waitReceiveAmount,
					tid: platform === PLAT_FXG && copyType == copyTypeEnum.订单编号 ? realTid : "",
					authorList: newAuthorList
				});
				setShopSelected(shopSelected);
				setDetailOrders([])
				// 获取货品信息
				sysSkuIds && sysSkuIds.length > 0 && getSysItemDetail(sysSkuIds, orderObj);
				// 获取商品信息
				skuIds && skuIds.length > 0 && getItemDetail(skuIds, orderObj);
				setCopyCreateTid(copyTid);
			} else {
				message.warning('请先输入订单编号');
			}
			setCopyLoading(false);
		} catch (error) {
			setCopyLoading(false);
			console.log('SysItemGetSysItemDetailApi error:', error);
		}
	};

	const getItemDetail = (skuList: {itemId:string, skuId:string, sellerId:string, platform:string}[], orderObj: object) => {
		const numIids = [];
		const skuIds = [];
		const platformList = [];
		const sellerIdList = [];
		skuList.forEach((item) => {
			numIids.push(item.itemId);
			skuIds.push(item.skuId);
			platformList.push(item.platform);
			sellerIdList.push(item.sellerId);
		});
		ItemSysItemListOfItemRelationPlatformItemViewApi({
			numIids: [...new Set(numIids)],
			skuIds: [...new Set(skuIds)],
			platformList: [...new Set(platformList)],
			sellerIdList: [...new Set(sellerIdList)]
		}).then((res) => {
			const {list = []} = res;
			let errorMsg = "";
			let orders = [];
			const ordersMap = {};
			list?.forEach((item) => {
				item.platformItemSkuList?.forEach((sku) => {
					if (item.numIid && sku.skuId) {
						ordersMap[`${item.numIid}_${sku.skuId}_${item.sellerId}_${item.platform}`] = {
							...item,
							...sku,
							platformItemSkuList: [sku]
						};
					} else {
						errorMsg = "订单中未初始化的商品将不会被复制";
						message.warning(errorMsg);
					}
				})
			})
			// console.log(ordersMap,'ordersMapordersMapordersMap')
			// console.log(orderObj,'orderObjorderObjorderObj')
			Object.keys(orderObj)?.forEach((key: string) => {
				const curOrder = ordersMap[key];
				console.log(curOrder,'curOrdercurOrdercurOrder')
				if (curOrder) {
					const { num = 0, discount = 0, payment = "0.00" } = orderObj[key] || {};
					let obj = {
						...curOrder,
						type:GoodsTypeEnum.平台商品,
						sysItemAlias: curOrder.title || '',
						sysSkuName: curOrder.skuName || '',
						skuOuterId: curOrder.skuOuterId || '',
						outerId: curOrder.skuOuterId,
						num: num || 0,
						weight: curOrder.weight || '0',
						discount: discount || "",
						priceKey: 'price',
						payment: payment || "0.00",
						apiPrice: (Number(curOrder.price) || 0).toFixed(2) || '0.00'
					};
					orders.push(obj);
				}
			})
			// console.log(posts,'curcurcurcur')
			// let orders = posts.reduce((pre, cur: any) => {
			// 	const numId = cur?.list[0]?.numIid;
			// 	const newCur = cur?.list[0]?.platformItemSkuList[0];
			// 	if (cur.list && numId && newCur.skuId) {
			// 		// 修复这里的键名拼接，使用模板字符串并添加下划线分隔
			// 		const key = `${numId}_${newCur.skuId}_${cur.list[0].sellerId}_${cur.list[0].platform}`;
			// 		// 添加默认值，防止解构时出现 undefined
			// 		const { num = 0, discount = 0, payment = "0.00" } = orderObj[key] || {};
			// 		let obj = {
			// 			...newCur,
			// 			...cur.list[0],
			// 			type:GoodsTypeEnum.平台商品,
			// 			sysItemAlias: cur?.list[0].title || '',
			// 			sysSkuName: newCur.skuName || '',
			// 			skuOuterId: newCur.skuOuterId || '',
			// 			outerId: newCur.skuOuterId,
			// 			num: num || 0,
			// 			weight: newCur.weight || '0',
			// 			discount: discount || "",
			// 			priceKey: 'price',
			// 			payment: payment || "0.00",
			// 			apiPrice: (Number(newCur.price) || 0).toFixed(2) || '0.00'
			// 		};
			// 		return pre.concat([obj]);
			// 	} else {
			// 		errorMsg = "订单中未初始化的商品将不会被复制";
			// 		return pre;
			// 	}
			// }, []);
			setDetailOrders(prev => {
				const newOrders = [...prev, ...orders];
				handleParamsChange('orders', newOrders);
				return newOrders;
			});
		}).catch((reason) => {
			console.log('reason::', reason);
		});
	};
	// 根据平台商品获取本地货品信息
	const getSysItemDetail = (skuIds:{sysItemId: string, sysSkuId: string}[], orderObj: object) => {
		const promises = skuIds.map((item) => {
			return SysItemGetSysItemDetailApi(item);
		});
		Promise.all(promises).then((posts) => {
			console.log(posts,'curcurcurcur')
			let errorMsg = "";
			let orders = posts.reduce((pre, cur: SysItem) => {
				// 这儿加个本地货品库存是否初始化判断 只有关联关系 但库存没有初始化的订单 将不会被添加
				// 大概率不会出现这种情况 主要为了避免 有关联关系后但没有库存初始化 订单被关闭时 会走到这边
				if (cur && cur.sysItemId && cur.sysSkuId) {
					const { num, discount, payment } = orderObj[`${cur.sysItemId}_${cur.sysSkuId}`];
					let obj = {
						...cur,
						type: GoodsTypeEnum.系统货品,
						sysOuterSkuId: cur.skuOuterId,
						num: num || 0,
						weight: cur.weight || '0',
						discount: discount || "",
						priceKey: 'price',
						payment: payment || "0.00",
						apiPrice: (Number(cur.price) || 0).toFixed(2) || '0.00'
					};
					return pre.concat([obj]);
				} else {
					errorMsg = "订单中未初始化的商品将不会被复制";
					return pre;
				}
			}, []);
			//if (errorMsg) message.warning('订单中存在商品未与本地货品关联，请先关联商品后复制');
			setDetailOrders(prev => {
				const newOrders = [...prev, ...orders];
				handleParamsChange('orders', newOrders);
				return newOrders;
			});
		}).catch((reason) => {
			console.log('reason::', reason);
		});
	};

	// 根据要复制的订单编号来查询订单
	const getCopyOrder = async(copyTid: string, uglyCopyTid?: string) => {
		const tids = [copyTid];
		uglyCopyTid && tids.push(uglyCopyTid);
		return new Promise<{
				trade: IPackage,
				skuIds?:{
					itemId:string,
					skuId:string,
					sellerId:string,
					platform:string,
				}[],
				sysSkuIds?: { sysItemId: string, sysSkuId: string }[],
				orderObj?: object
			}>((resolve, reject) => {
			const fn = copyType == copyTypeEnum.订单编号 ? TradeTradeDetailGetByPtTidApi : TradeTradeDetailGetApi;
			console.log('copyType', copyType);
			fn({
				tradeInfos: [
					{
						[copyType == copyTypeEnum.订单编号 ? 'ptTids' : 'tids']: tids
					}
				]
			}).then((res:TradeTradeDetailGetResponse) => {
				let skuIds:Array<{
					itemId:string,
					skuId:string,
					sellerId:string,
					platform:string,
				}> = [];
				let sysSkuIds:Array<{sysItemId: any, sysSkuId: any }> = [];
				let _trade:IPackage = {};
				let orderObj = {};
				if (res?.data?.list?.[0]?.isSplit && copyType == copyTypeEnum.订单编号) {
					form.resetFields();
					setParams(_.cloneDeep(initialParams));
					message.warning('订单已拆分，请根据系统单号创建');
					reject();
				}
				if (res.data && res.data.list && res.data.list[0] && res.data.list[0].trades[0]) {
					_trade = res.data.list[0];
					// 无库存版本和有库存没开启的订单 不需要复制商品
					if (!inventoryDeduct || userInfo?.version !== 1) {
						resolve({ trade: _trade });
						return;
					}
					for (let item of res.data.list[0].trades[0].orders) {
						console.log(item,'itemitemitem')

						// 新增判断逻辑：当sysItemId != itemId && sysItemId != null 且 sysSkuId != skuId && sysSkuId != null时为商品类型
						if (item.systemNumIid && item.systemSkuId &&
							item.systemNumIid !== item.numIid &&
							item.systemSkuId !== item.skuId) {
							// 这是平台商品 平台商品绑定了货品的
							orderObj[`${item.numIid}_${item.skuId ?? "0"}_${item.itemSellerId}_${item.itemPlatform}`] = {
								num: item.num,
								discount: item.discount || 0,
								payment: item.payment || 0,
								weight: item.weight || 0,
								apiPrice: item.price
							};
							skuIds.push({
								itemId: item.numIid,
								skuId: item.skuId ?? "0",
								sellerId: item.itemSellerId,
								platform: item.itemPlatform
							});
						} else if (item.systemNumIid && item.systemSkuId &&
								  item.systemNumIid === item.numIid &&
								  item.systemSkuId === item.skuId) {
							// 这是纯系统货品（systemNumIid == itemId && systemSkuId == skuId）
							orderObj[`${item.systemNumIid}_${item.systemSkuId}`] = {
								num: item.num,
								discount: item.discount || 0,
								payment: item.payment || 0,
								weight: item.weight || 0,
								apiPrice: item.price
							};
							sysSkuIds.push({
								sysItemId: item.systemNumIid,
								sysSkuId: item.systemSkuId
							});
						} else {
							// 这是平台商品（没有关联系统货品）
							//剩下的应该都是 sysItemId == null sysSkuId == null  的，就不做if判断了
							orderObj[`${item.numIid}_${item.skuId ?? "0"}_${item.itemSellerId}_${item.itemPlatform}`] = {
								num: item.num,
								discount: item.discount || 0,
								payment: item.payment || 0,
								weight: item.weight || 0,
								apiPrice: item.price
							};
							skuIds.push({
								itemId: item.numIid,
								skuId: item.skuId ?? "0",
								sellerId: item.itemSellerId,
								platform: item.itemPlatform
							});
						}
					}
					resolve({ trade: _trade, skuIds, sysSkuIds, orderObj });
				} else {
					message.warning('订单编号不存在，请确认后再试');
					reject();
				}
			});
		});
	};

	// 地址解密
	const decryptAddress = async (pack: IPackage, useOriginEncrypt: boolean) => {
		console.log('decryptAddress', pack, useOriginEncrypt);

		if (useOriginEncrypt) {
			form.setFieldsValue({
				receiverName: pack.receiverNameMask,
				receiverAddress: pack.receiverAddressMask,
				receiverMobile: pack.receiverMobileMask,
			});
		} else {
			const { successObj } = await decryptFn({
				togetherId: pack.togetherId,
				platform: pack.platform,
				mallId: pack.sellerId,
				encryptObj: {
					name: pack.receiverName,
					phone: pack.receiverMobile,
					address: pack.receiverAddress,
					buyerNick: pack.buyerNickOrigin || pack.buyerNick,
				},
				decryptArr: [{
					sellerId: pack.sellerId,
					platform: pack.platform,
					caid: pack.caid,
					tid: pack.encodeTid,
					ptTid: pack.encodePtTid || pack.encodeTid,
					sceneCode: 100,
					encodeReceiverPhone: pack.receiverPhone,
					encodeMobile: pack.receiverMobile,
					encodeReceiverName: pack.receiverName,
					encodeReceiverAddress: pack.receiverAddress,
				}],
				sellerNick: pack.sellerNick,
				sellerId: pack.sellerId,
				isDecrypted: pack.isDecrypted,
				pack,
				decryptLocation: 'copyOrder', // 添加一个传参标识来源一键复制
			});
			const { receiverName, receiverPhone, receiverMobile } = successObj[pack.togetherId]||{};
			const { _receiverMobile, _receiverMobileSecret } = getMobilePhone(receiverPhone);
			const { receiverAddress } = decryptRewrite(pack, successObj[pack.togetherId]);
			console.log('successObj',successObj,receiverAddress,_receiverMobile,receiverName);
			setDecryptedError(!receiverName||!receiverAddress||!_receiverMobile);
			form.setFieldsValue({
				receiverName,
				receiverMobile: _receiverMobile,
				receiverMobileSecret: _receiverMobileSecret,
				receiverPhone: receiverMobile,
				receiverAddress
			});
		}
	};
	console.log('useOriginEncrypt33333', optParams.useOriginEncrypt);


	const advancedParse = () => {
		//sendPoint(Pointer.订单_新建手工单_高级识别);
		if (optParams.advancedAddress.trim()) {
			TradeAddressParseBeParseApi({
				rawAddress: optParams.advancedAddress.trim(),
				requestPath: 'zzAddress',
			}).then(res => {
				let phone = res.phone;
				let name = res.name;
				let detail = res.address?.detail;
				let receiverMobileSecret = '';
				const arr = phone?.match(/-(\d{4})$/);
				if (arr?.length) {
					phone = phone.replace(arr[0], '');
					if (isVirtualCode) {
						receiverMobileSecret = arr[1] ?? '';
						name += `[${receiverMobileSecret}]`;
						detail += `[${receiverMobileSecret}]`;
					}
				}
				setParams(prev => ({
					...prev,
					tradeEncodeType: 1,
					receiverName: name,
					receiverMobile: phone,
					receiverProvince: res.address?.province,
					receiverCity: res.address?.city,
					receiverCounty: res.address?.district,
					receiverAddress: detail,
				}));
				form.setFieldsValue({
					receiverName: name,
					receiverMobile: phone,
					receiverMobileSecret,
					receiverAddress: detail,
				});
			});
		} else {
			message.warning('请先输入点内容');
		}
	};

	const advancedAiParse = () => {
		//sendPoint(Pointer.订单_新建_订单打印_手工单_AI识别);
		if (optParams.advancedAddress.trim()) {
			TradeAddressParseBeAiParseApi({
				rawAddress: optParams.advancedAddress.trim(),
			}).then(res => {
				let phone = res.phone;
				let name = res.name;
				let detail = res.address?.detail;
				let receiverMobileSecret = '';
				const arr = phone?.match(/-(\d{4})$/);
				if (arr?.length) {
					phone = phone.replace(arr[0], '');
					if (isVirtualCode) {
						receiverMobileSecret = arr[1] ?? '';
						name += `[${receiverMobileSecret}]`;
						detail += `[${receiverMobileSecret}]`;
					}
				}
				 // 生成发货信息内容
				 const shipContent = res.shipContent || '';
				setParams(prev => ({
					...prev,
					tradeEncodeType: 1,
					receiverName: name,
					receiverMobile: phone,
					receiverProvince: res.address?.province,
					receiverCity: res.address?.city,
					receiverCounty: res.address?.district,
					receiverAddress: detail,
					shipInfo: shipContent // 直接填充发货信息
				}));
				form.setFieldsValue({
					receiverName: name,
					receiverMobile: phone,
					receiverMobileSecret,
					receiverAddress: detail,
					shipInfo: shipContent // 直接填充发货信息
				});
			});
		} else {
			message.warning('请先输入点内容');
		}
	};

	const handleParamsChange = (key: string, val: any) => {
		setParams(prev => {
			if (key === 'postFee') {
				val = calcInputVal(val, 'decimal');
			}
			prev[key] = val;
			if (key === 'postFee') {
				calcTotalInfo(prev);
			}
			if (key === 'orders') {
				calcTotalInfo(prev);
				let printContent = calcPrintContent(val);
				form.setFieldsValue({
					shipInfo: printContent
				});
			}
			return { ...prev };
		});
	};

	const handleOrderChange = (index: number, key: string, val:number | string, reg: 'decimal' | 'integer', decimalNum = 2) => {
		if (key === "apiPrice") {
			setParams(prev => {
				prev.orders[index] = {
					...prev.orders[index],
					customPrice: val
				};
				return { ...prev };
			});
		}
		setParams(prev => {
			prev.orders[index][key] = val;

			if (key !== "payment") calcPayMent(prev.orders[index]);
			else calcDisCount(prev.orders[index]);
			calcTotalInfo(prev);
			let printContent = calcPrintContent(prev.orders);
			form.setFieldsValue({
				shipInfo: printContent
			});
			return { ...prev };
		});
	};

	const calcTotalInfo = (params: TradeHandTradeCreatedRequest, isEdit = false) => {
		params.weight = 0;
		params.num = 0;
		params.payment = '0';
		params.discount = '0';
		params.orders.forEach(order => {
			params.num += +order.num || 0;
			params.weight = +params.weight + (((+order.weight || 0) * (+order.num || 0)) || 0);
			// if (isKg && +params.weight) params.weight = +Number(+params.weight).toFixed(3);
			params.payment = (+params.payment + +order.payment || 0) + '';
			params.discount = (+params.discount + (+order.discount || 0)) + '';
		});
		if (!isEdit) {
			params.payment = (+params.payment + (+params.postFee || 0)) + '';
		}
		params.payment = (+params.payment).toFixed(4);
		params.discount = (+params.discount).toFixed(4);
	};

	const calcPayMent = (order: SysItem) => {
		order.payment = '';
		if (+order.num) {
			order.payment = +(+order.num * +order.apiPrice - (order.discount || 0)) || 0;
			if (order.payment.toFixed) {
				order.payment = order.payment.toFixed(4);
			}
		}
	};

	const calcDisCount = (order: SysItem) => {
		order.discount = '';
		if (+order.num) {
			order.discount = +(order.apiPrice * order.num - order.payment) || 0;
			if (order.discount.toFixed) {
				order.discount = order.discount.toFixed(4);
			}
		}
	};

	const calcPrintContent = (orders: any) => {
		console.log('ordersordersorders', orders);
		try {
			let pack = {
				trades: [{
					orders: [],
				}] as any,
			};
			orders.forEach((order: any) => {
				let temp: Record<string, any> = {};
				if (order.type == GoodsTypeEnum.系统货品) {
					temp = {
						itemNo: order.itemNo,
						isCombination: order.isCombination || order.combination,
						groupRelationRecordList: order.groupRelationRecordList || order.groupCombinationList,
						isChecked: order.num > 0,
						outerId: order.skuOuterId || order.outerId,
						titleShort: order.sysItemAlias,
						title: order.sysItemAlias,
						outerSkuId: order.skuOuterId || order.outerId,
						stall: '',
						num: order.num,
						numIid: order.sysItemId,
						skuId: order.sysSkuId,
						skuAlias: order.sysSkuAlias,
						skuPropertiesName: order.sysSkuName,
						price: order[order.priceKey],
						sysOuterId: order.sysOuterId || '',
						sysOuterSkuId: order.sysOuterSkuId || '',
						warehouseSlotName: order.warehouseSlotName,
					};
				} else {
					let platformItemSku = (order as ItemSysItemListOfItemRelationPlatformItemViewResponse["data"]["list"][0]).platformItemSkuList?.[0] || {};
					let relationSystemItem = platformItemSku?.relationSystemItemList?.[0] || {};
					console
					.log(relationSystemItem,'relationSystemItem')
					temp = {
						isChecked: order.num > 0,
						outerId: order.outerId,
						titleShort: relationSystemItem.sysItemAlias,
						title: order.title,
						outerSkuId: platformItemSku.skuOuterId || order.outerId,
						stall: '',
						num: order.num,
						numIid: order.numIid,
						skuId: platformItemSku.skuId,
						skuAlias: relationSystemItem.sysSkuAlias,
						skuPropertiesName: platformItemSku.skuName,
						price: order[order.priceKey],
						skuOuterId: order.skuOuterId || '',
						//sysOuterId: relationSystemItem.skuOuterId || '',
						sysOuterSkuId: relationSystemItem.skuOuterId || '',
						warehouseSlotName: relationSystemItem.warehouseSlotName,
					};
				}

				pack.trades[0].orders.push(temp);
			});
			console.log(pack,'packpackpackpack')
			return genePrintContent(pack, printContent, keyWords, advancedSetRes);
		} catch (error) {
			console.error(error);
			return '';
		}
	};

	const deleteOrder = (index: number) => {
		const orders = [...params.orders];
		orders.splice(index, 1)
		const newParams = {
			...params,
			orders
		}
		calcTotalInfo(newParams);
		let printContent = calcPrintContent(orders);
			form.setFieldsValue({
				shipInfo: printContent
			});
		setParams(newParams);
		setDataSource(dataSource.filter((_, i) => i !== index));
	};

	const renderGoodsList = (selectRows: ItemSysItemListOfItemRelationPlatformItemViewResponse["data"]["list"] | any) => {
		let orders: any = [];
		if (isShowZeroStockVersion) {
			// * 零库存版 添加平台商品
			orders = (selectRows as ItemSysItemListOfItemRelationPlatformItemViewResponse["data"]["list"]).map((goods) => {
				let platformItemSku = goods.platformItemSkuList?.[0] || {};
				let relationSystemItemList = platformItemSku.relationSystemItemList?.[0] || {};

				return {
					...goods,
					rowId: goods.rowId,
					sysItemAlias: goods.title || '',
					sysSkuName: platformItemSku.skuName || '',
					skuOuterId: platformItemSku.skuOuterId || '',
					num: '1',
					weight: relationSystemItemList.weight || '',
					priceKey: 'costPrice',
					payment: relationSystemItemList.costPrice || '0.00',
					apiPrice: relationSystemItemList.costPrice || '0.00',
					costPrice: relationSystemItemList.costPrice || '0.00',
				};
			});
			console.log('orders', orders);
		} else {
			const sysSkuList = selectRows.reduce(function(a:any, b:any) {
				return a.concat(b.sysSkuList);
			}, []);
			orders = sysSkuList.map((sku: SysItem) => ({
				...sku,
				num: '1',
				weight: sku.weight || '',
				priceKey: 'price',
				payment: sku.price || '0.00',
				apiPrice: sku.price || '0.00',
				sysOuterId: sku.outerId,
				sysOuterSkuId: sku.skuOuterId
			}));
		}
		handleParamsChange('orders', params.orders.concat(orders));
	};

	// 常用商品选择
	const handleSavaGoodsSelectChange = (value:string[], list:any[] ,type:number) => {
		console.log('%c [ 常用商品选择 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', value);
		onGoodsSelected(list,type);
	};

	const handleMenuClick = (e: any, order: SysItem, index: number) => {
		console.log("handleMenuClick:", e);
		setParams(prev => {
			prev.orders[index].priceKey = e.key;
			prev.orders[index].apiPrice = order[e.key] || '0.00';
			calcPayMent(prev.orders[index]);
			calcTotalInfo(prev);
			let printContent = calcPrintContent(prev.orders);
			form.setFieldsValue({
				shipInfo: printContent
			});
			return {
				...prev,
			};
		});
	};

	const renderPriceCom = (order: SysItem, index: number) => {
		if (editFlag && !firstEdit) {
			getSystemPrice(order, index);
			setFirstEdit(true);
		}
		order = {...order.platformItemSkuList?.[0]||{},...order}
		return (
			<Menu onClick={ (e) => { handleMenuClick(e, order, index); } }>
				{dropMenuPriceList.map(item => {
					if (!hasFieldsPermission(item.fieldsPermission)) {
						return (
							<FieldsPermissionCheck fieldsPermission={ FieldsPermissionEnum.成本价 } customMsg="无查看和操作权限">
								<Menu.Item key={ item.key }>
									{item.name}: {order[item.key]}
								</Menu.Item>
							</FieldsPermissionCheck>
						);
					} else {
						return (
							<Menu.Item key={ item.key }>
								{item.name}: {item.key === "costPrice" ? getCostPrice(order[item.key] || '0.00') : Number(order[item.key] || '0.00').toFixed(2) }
							</Menu.Item>
						);
					}
				})}
			</Menu>
		);
	};

	const handleSenderInput = async(values: any, params: TradeHandTradeCreatedRequest, optParams: IOptParams) => {
		const { address, guess } = await TradeAddressParseBeParseApi({
			rawAddress: values.senderCustomAddress?.trim(),
			requestPath: 'onlyAddress',
		});
		params.senderAddress = address?.detail;
		form.setFieldsValue({
			'senderAddress': address?.detail,
		});
		let needWarning = false;
		if (guess?.guessC !== false) {
			needWarning = true;
			params.senderCity = '';
			form.setFieldsValue({
				'senderAddrObj.cityName': '',
			});
		} else {
			params.senderCity = address?.city;
			form.setFieldsValue({
				'senderAddrObj.cityName': address?.city,
			});
		}
		if (guess?.guesssP !== false) {
			needWarning = true;
			params.senderProvince = '';
			form.setFieldsValue({
				'senderAddrObj.provinceName': '',
			});
		} else {
			params.senderProvince = address?.province;
			form.setFieldsValue({
				'senderAddrObj.provinceName': address?.province,
			});
		}
		if (guess?.guessD !== false) {
			needWarning = true;
			params.senderDistrict = '';
			params.senderCounty = "";
			form.setFieldsValue({
				'senderAddrObj.countyName': '',
			});
		} else {
			params.senderDistrict = address?.district;
			params.senderCounty = address?.district;
			form.setFieldsValue({
				'senderAddrObj.countyName': address?.district,
			});
		}
		if (needWarning) {
			optParams.senderAddrType = 'choose';
		}

		return needWarning;
	};

	const handleReceiverInput = async(values: any, params: TradeHandTradeCreatedRequest, optParams: IOptParams) => {
		const { address = {}, guess } = await TradeAddressParseBeParseApi({
			rawAddress: values.receiverCustomAddress?.trim() || '',
			requestPath: 'onlyAddress',
		});

		params.receiverAddress = address?.detail;
		form.setFieldsValue({
			'receiverAddress': address?.detail,
		});
		let needWarning = false;
		if (guess?.guessC !== false) {
			needWarning = true;
			params.receiverCity = '';
			form.setFieldsValue({
				'receiverAddrObj.cityName': '',
			});
		} else {
			params.receiverCity = address?.city;
			form.setFieldsValue({
				'receiverAddrObj.cityName': address?.city,
			});
		}
		if (guess?.guesssP !== false) {
			needWarning = true;
			params.receiverProvince = '';
			form.setFieldsValue({
				'receiverAddrObj.provinceName': '',
			});
		} else {
			params.receiverProvince = address?.province;
			form.setFieldsValue({
				'receiverAddrObj.provinceName': address?.province,
			});
		}
		if (guess?.guessD !== false) {
			needWarning = true;
			params.receiverCounty = '';
			form.setFieldsValue({
				'receiverAddrObj.countyName': '',
			});
		} else {
			params.receiverCounty = address?.district;
			form.setFieldsValue({
				'receiverAddrObj.countyName': address?.district,
			});
		}
		if (needWarning) {
			optParams.receiverAddrType = 'choose';
		}

		return needWarning;
	};

	const handleSave = (type: string) => {
		if (loading) return;
		// 判断是否超出最大数量
		if (params?.orders?.length > maxAddCount) {
			message.error(`最多支持添加${maxAddCount}个商品`);
			return;
		}
		if (type === saveType) {
			form.submit();
		} else {
			setSaveType(type);
		}
	};

	useEffect(() => {
		if (saveType) {
			form.submit();
		}
	// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [saveType]);

	const handleFinish = async(values: any) => {
		let _params = {
			...params,
			...values
		};
		console.log('_params22', _params);

		if (isKg) {
			_params.weight = _params.weight,
			_params.orders = _params.orders.map(i => ({ ...i, weight: i.weight }));

		}
		if (!_params.receiverMobile && !_params.receiverPhone) {
			message.warning('收件人手机号和固话必须填写一个，请检查');
			return;
		}
		if (_params.tradeEncodeType==1 &&_params.receiverPhone && !/^(\d{3,4}|\d{3,4}-)?\d{7,8}$/.test(_params.receiverPhone)) {
			message.warning('收件人固话格式不正确，请检查');
			return;
		}
		if (_params.tradeEncodeType==1 &&_params.receiverMobile && !/^1\d{10}$/.test(_params.receiverMobile)) {
			message.warning('收件人手机号格式不正确，请检查');
			return;
		}
		if (optParams.receiverAddrType === 'input') {
			if (await handleReceiverInput(values, _params, optParams)) {
				setParams(_params);
				setOptParams({ ...optParams });
				message.warning('收件人信息不完整，请检查');
				return;
			}
		}

		if (optParams.senderAddrType === 'input') {
			if (await handleSenderInput(values, _params, optParams)) {
				setParams(_params);
				setOptParams({ ...optParams });
				message.warning('发件人信息不完整，请检查');
				return;
			}
		}
		const { plat, shopId, sellerNick } = _params.shopSelected;
		if (!plat || !shopId) {
			message.warning("请选择店铺");
			return;
		}
		sendPointAboutSelectPlat(plat);
		let hasDiffShop = false;
		if (plat != PLAT_OTHER && plat != PLAT_HAND) {
			// 只有在非"其他"和非"手工"平台下才需要检查商品与店铺是否匹配
			hasDiffShop = _params?.orders.some(item => {
				// 跳过没有平台信息的商品（系统货品）
				if(!item?.platform && !item?.sellerId) {
					return false;
				}
				// 检查商品的平台和店铺ID是否与选择的一致
				return item?.platform !== plat || item?.sellerId !== shopId;
			});
		}
		if (hasDiffShop) {
			message.warning("选择的平台店铺与商品不一致，请确认后再提交");
			return;
		}

		_params.receiverProvince = _params.receiverAddrObj?.provinceName || _params.receiverProvince || '';
		_params.receiverCity = _params.receiverAddrObj?.cityName || _params.receiverCity || '';
		_params.receiverCounty = _params.receiverAddrObj?.countyName || _params.receiverCounty || '';

		_params.senderProvince = _params.senderAddrObj?.provinceName || _params.senderProvince || '';
		_params.senderCity = _params.senderAddrObj?.cityName || _params.senderCity || '';
		_params.senderCounty = _params.senderAddrObj?.countyName || _params.senderCounty || '';

		if (!_params.receiverProvince || !_params.receiverCity || !_params.receiverCounty || !_params.receiverAddress) {
			message.warning('收件人信息不完整，请检查');
			return;
		}

		if (_params.receiverAddress.length > 128) {
			message.warning('收件人地址过长，最长120，请检查');
			return;
		}

		let keyArr = ['senderProvince', 'senderCity', 'senderCounty', 'senderAddress', 'senderName', 'senderMobile'];
		if (!keyArr.every(key => _params[key]) && !keyArr.every(key => !_params[key])) {
			message.warning('发件人信息不完整，请检查');
			return;
		} else if (keyArr.every(key => _params[key])) {
			if (!/^1\d{10}$/.test(_params.senderMobile)) {
				message.warning('发件人手机号格式不正确，请检查');
				return;
			}

			if (_params.senderAddress.length > 200) {
				message.warning('发件人地址过长，最长200，请检查');
				return;
			}
		}

		if (_params.orders.some((order: any) => !(+order.num))) {
			message.warning('商品数量不能为0');
			return;
		}

		if (_params.orders.some((order: any) => +order.payment < 0)) {
			message.warning('商品实付金额不能少于0');
			return;
		}

		let maxLimit = 1000000000;
		if (_params.weight >= maxLimit) {
			message.warning('订单重量超过最大限制999999999');
			return;
		}
		if (_params.payment >= maxLimit) {
			message.warning('实付金额超过最大限制999999999.99');
			return;
		}
		if (_params.discount >= maxLimit) {
			message.warning('优惠金额超过最大限制999999999.99');
			return;
		}
		if (_params.postFee >= maxLimit) {
			message.warning('运费超过最大限制999999999.99');
			return;
		}
		if (_params.num >= maxLimit) {
			message.warning('数量超过最大限制999999999');
			return;
		}
		const shopParams:{sellerId: string, platform: string, sellerNick?: string} = {
			sellerId: shopId,
			platform: plat
		};
		if (plat !== PLAT_HAND) shopParams.sellerNick = sellerNick;

		console.log(_params.orders, '_params.orders');

		let apiParams: TradeHandTradeCreatedRequest = {
			orders: _params.orders.map((order: any) => {
				if (order.type == GoodsTypeEnum.平台商品) {
					console.log(order,'orderorderorder')
					// * 零库存版
					let platformItemSku = (order as ItemSysItemListOfItemRelationPlatformItemViewResponse["data"]["list"][0]).platformItemSkuList?.[0] || {};
					let relationSystemItem = platformItemSku?.relationSystemItemList?.[0] || {};
					return {
						itemSellerId: platformItemSku.sellerId,
						itemSellerNick: platformItemSku.sellerNick,
						itemPlatform: platformItemSku.platform,
						color: platformItemSku.color,
						size: platformItemSku.size,
						picPath: platformItemSku.picUrl,
						numIid: order.numIid,
						itemId: order.numIid,
						sysItemId: relationSystemItem.sysItemId,
						title: order.title,
						shortTitle: relationSystemItem.sysItemAlias,
						outerId: order.outerId,
						outerSkuId: platformItemSku.skuOuterId,
						skuId: platformItemSku.skuId,
						sysSkuId: relationSystemItem.sysSkuId,
						skuName: platformItemSku.skuName,
						skuAlias: relationSystemItem.sysSkuAlias,
						price: order[order.priceKey],
						num: order.num,
						discount: order.discount || 0,
						payment: order.payment || 0,
						weight: order.weight || 0,
						refundStatus: order.refundStatus || "",
						oid: order.oid,
						priceType: priceType[order.priceKey],
						platform: order.platform,
						authorName: values?.authorList?.authorList?.[0]?.authorName,
						authorId: values?.authorList?.authorList?.[0]?.authorId,
					};
				} else {
					// * 库存版
					return {
						color: order.sysColor,
						size: order.sysSize,
						picPath: order.picUrl,
						numIid: order.sysItemId,
						itemId: order.sysItemId,
						sysItemId: order.sysItemId,
						title: order.sysItemAlias,
						shortTitle: order.sysItemAlias,
						outerId: order.skuOuterId,
						outerSkuId: order.skuOuterId,
						skuId: order.sysSkuId,
						sysSkuId: order.sysSkuId,
						skuName: order.sysSkuName,
						skuAlias: order.sysSkuAlias,
						price: order[order.priceKey],
						num: order.num,
						discount: order.discount || 0,
						payment: order.payment || 0,
						weight: order.weight || 0,
						refundStatus: order.refundStatus || "",
						oid: order.oid,
						priceType: priceType[order.priceKey],
						authorName: values?.authorList?.authorList?.[0]?.authorName,
						authorId: values?.authorList?.authorList?.[0]?.authorId,
					};
				}
			}),
			tid: _params.tid,
			tradeEncodeType: _params.tradeEncodeType,
			receiverName: _params.receiverName,
			receiverPhone: _params.receiverPhone || '',
			// 虚拟号需要拼接一下
			receiverMobile: `${_params.receiverMobile || ''}${_params.receiverMobileSecret ? ('-' + _params.receiverMobileSecret) : ""}`,
			receiverProvince: _params.receiverProvince,
			receiverCity: _params.receiverCity,
			receiverCounty: _params.receiverCounty,
			receiverAddress: removeZeroWidthChars(_params.receiverAddress),
			shipInfo: _params.shipInfo,
			senderName: _params.senderName || '',
			senderPhone: _params.senderPhone || '',
			senderMobile: _params.senderMobile || '',
			senderProvince: _params.senderProvince || '',
			senderCity: _params.senderCity || '',
			senderCounty: _params.senderCounty || '',
			senderAddress: removeZeroWidthChars(_params.senderAddress || ''),
			sellerMemo: _params.sellerMemo || '',
			remarkFlag: _params.remarkFlag || '',
			num: _params.num,
			discount: _params.discount || 0,
			weight: _params.weight || 0,
			payment: _params.payment,
			postFee: _params.postFee || 0,
			orderNum: _params.num,
			createTime: _params.createTime || "",
			waitReceiveAmount: _params.waitReceiveAmount,
			buyerNick: trade.buyerNick,
			buyerOpenUid: trade.buyerOpenUid,
			sellerId: trade.sellerId,
			realTid: _params.realTid,
			...shopParams
		};
		if (_params.tradeEncodeType === 2) {
			console.log('_params',_params);
			apiParams.oaid = _params.oaid
			apiParams.receiverNameMask = trade.receiverNameMask
			apiParams.receiverPhoneMask = trade.receiverPhoneMask
			apiParams.receiverMobileMask = trade.receiverMobileMask
			apiParams.receiverAddressMask = removeZeroWidthChars(trade.receiverAddressMask)
			apiParams.queryReceiverName = trade.queryReceiverName
			apiParams.idxEncodeReceiverName = trade.idxEncodeReceiverName
			apiParams.idxEncodeReceiverMobile = trade.idxEncodeReceiverMobile
			apiParams.idxEncodeReceiverPhone = trade.idxEncodeReceiverPhone
			apiParams.idxEncodeReceiverAddress = trade.idxEncodeReceiverAddress
			apiParams.receiverName = trade.receiverName
			apiParams.receiverAddress = removeZeroWidthChars(trade.receiverAddress)
			apiParams.receiverMobile = trade.receiverMobile
		}
		console.log('apiParams',apiParams,trade);
		// if (userInfo?.version === 1 && inventoryDeduct) {
		// 	if (!apiParams.orders.length) {
		// 		message.warning('请至少选择一个商品');
		// 		return;
		// 	}
		// }

		setLoading(true);
		if (editFlag) {
			// 编辑
			TradeHandTradeUpdataApi({
				...apiParams,
				tid: handData.trades[0].tid
			}).then(async(res) => {
				message.success('更新成功');
				onEditOk && await onEditOk(res);
				saveNext(res, _params);
				setLoading(false);
				setIsShowHandOrderModal(notCloseModal);
				let params:any = await compareOrder(apiParams, res.togetherId);
				TradeOperateLogAddApi(params);
			}).catch(() => {
				setLoading(false);
			});
		} else {
			setLocalMemoOfShopInfo();
			// 新增
			TradeHandTradeCreatedApi(apiParams).then(async(res) => {
				message.success('创建成功');
				if (copyCreateTid) {
					res && TradeOperateLogAddApi([{ operateType: TradeOptEnum.新建, operateResult: 1, operateContent: `导入创建订单：${copyCreateTid}`, platform: "hand", tids: res.togetherId }]);
					setCopyCreateTid("");
				} else {
					res && TradeOperateLogAddApi([{ operateType: TradeOptEnum.新建, operateResult: 1, operateContent: `手工新建订单：${res.togetherId}`, platform: "hand", tids: res.togetherId }]);
				}

				onOk && await onOk(res);
				saveNext(res, _params);
				setLoading(false);
				setIsShowHandOrderModal(notCloseModal);
			}).catch(() => {
				setLoading(false);
			});
		}
	};

	const sendPointAboutSelectPlat = (plat:platform) => {
		switch (plat) {
			case PLAT_HAND:
				sendPoint(Pointer.订单_新建手工单_无店铺保存);
				break;
			case PLAT_OTHER:
				sendPoint(Pointer.订单_新建手工单_虚拟店铺保存);
				break;
			default:
				sendPoint(Pointer.订单_新建手工单_线上店铺保存);
				break;
		}
	};
	// 订单修改前后比较
	const compareOrder = (apiParams:TradeHandTradeCreatedRequest, tids:string) => {
		// { operateType: 8, operateContent: content, platform: "hand", tids: res.togetherId }
		// eslint-disable-next-line no-async-promise-executor
		return new Promise(async(resolve, reject) => {
			const oldTrade = { ...handData.trades[0] };
			const oldOrders = [...oldTrade.orders];
			let content = "";
			let params:TradeOperateLogAddRequest = [];
			const { senderName, senderMobile, senderProvince, senderCity, senderCounty, senderAddress, senderPhone,
				receiverName, receiverMobile, receiverProvince, receiverCity, receiverCounty, receiverAddress, receiverPhone,
				orders
			} = apiParams;
			let {
				senderName: oldSenderName,
				senderMobile: oldSenderMobile,
				senderProvince: oldSenderProvince,
				senderCity: oldSenderCity,
				senderCounty: oldSenderCounty,
				senderAddress: oldSenderAddress,
				senderPhone: oldSenderPhone,
				receiverName: oldReceiverName,
				receiverMobile: oldReceiverMobile,
				receiverState: oldReceiverProvince,
				receiverCity: oldReceiverCity,
				receiverDistrict: oldReceiverCounty,
				receiverAddress: oldReceiverAddress,
				receiverPhone: oldReceiverPhone,
				// orders: oldOrders
			} = oldTrade;
			// 发件人比较
			if (senderName !== oldSenderName
				|| senderMobile !== oldSenderMobile
				|| senderProvince !== oldSenderProvince
				|| senderPhone !== oldSenderPhone
				|| senderCity !== oldSenderCity
				|| senderCounty !== oldSenderCounty
				|| senderAddress !== oldSenderAddress
			) {
				// 没有填写发件人时获取默认发件人
				if (!oldSenderName && !oldSenderMobile) {
					try {
						let defaultSender:any = await getDefaultSender();
						oldSenderName = defaultSender.senderName;
						oldSenderMobile = defaultSender.senderMobile;
						oldSenderProvince = defaultSender.senderProvince;
						oldSenderCity = defaultSender.senderCity;
						oldSenderCounty = defaultSender.senderDistrict;
						oldSenderAddress = defaultSender.senderAddressDetail;
					} catch (error) {
						console.log("error:", error);
					}
				}
				content = `修改发件人:
							原：${oldSenderName},${oldSenderMobile},${oldSenderPhone},${oldSenderProvince}${oldSenderCity}${oldSenderCounty}${oldSenderAddress}
							新：${senderName},${senderMobile},${senderPhone},${senderProvince}${senderCity}${senderCounty}${senderAddress}
							`;
				// params.push({ operateType: 8, operateContent: content, platform: "hand", tids })
			}
			// 收件人比较
			if (receiverName !== oldReceiverName
				|| receiverMobile !== oldReceiverMobile
				|| receiverProvince !== oldReceiverProvince
				|| receiverPhone !== oldReceiverPhone
				|| receiverCity !== oldReceiverCity
				|| receiverCounty !== oldReceiverCounty
				|| receiverAddress !== oldReceiverAddress
			) {
				content += `修改收件人:
							原：${oldReceiverName},${oldReceiverMobile},${oldReceiverPhone},${oldReceiverProvince}${oldReceiverCity}${oldReceiverCounty}${oldReceiverAddress}
							新：${receiverName},${receiverMobile},${receiverPhone},${receiverProvince}${receiverCity}${receiverCounty}${receiverAddress}
							`;
			}
			content && params.push({ operateType: 8, operateContent: content, platform: "hand", operateResult: 1, tids });
			content = "";
			let oldOrdersObj = {};
			for (let item of oldOrders) {
				oldOrdersObj[item.oid] = item;
			}
			for (let item of orders) {
				// orderObj[item.numIid] = item;
				// 如果原来有，就对比商品内容 货品简称 规格名称 数量 实付金额
				let oldItem = oldOrdersObj[item.oid] || null;
				const { title = "", payment = 0, shortTitle = "", skuName = "", num = 0 } = item;
				if (oldItem) {
					const { title: oldTitle = "", payment: oldPayment = 0, titleShort: oldTitleShort = "", skuPropertiesName: oldSkuPropertiesName = "", num: oldNum = 0 } = oldItem;
					if (
						title !== oldTitle
						|| payment !== oldPayment
						|| shortTitle !== oldTitleShort
						|| skuName !== oldSkuPropertiesName
						|| num !== oldNum
					) {
						content += `修改货品:
						原：【${oldTitle || oldTitleShort} ${oldSkuPropertiesName}】,数量 ${oldNum},实付金额 ${oldPayment}
						新：【${shortTitle || title} ${skuName}】,数量 ${num},实付金额 ${payment}
						`;
					}
					// 删除已有的
					delete oldOrdersObj[item.oid];
				} else {
					// 没有就为新增商品
					content += `新增货品:
					【${shortTitle || title} ${skuName}】,数量 ${num},实付金额 ${payment}
					`;
				}
			}
			// 如果对象中还有就是编辑时删掉的
			if (Object.keys(oldOrdersObj).length > 0) {
				for (let item in oldOrdersObj) {
					const { titleShort = "", title = "", skuPropertiesName = "", num = 0, payment = "" } = oldOrdersObj[item];
					content += `删除货品:
					【${titleShort || title} ${skuPropertiesName}】,数量 ${num},实付金额 ${payment}
					`;
				}
			}
			content && params.push({ operateType: 8, operateContent: content, platform: "hand", tids, operateResult: 1 });
			// 商品内容比较
			if (params.length) {
				resolve(params);
			} else {
				reject();
			}
		});

	};
	// 获得默认发件人
	const getDefaultSender = () => {
		return new Promise((resolve, reject) => {
			userStore.getUserInfo().then(res => {
				const { userId } = res;
				const params = {
					userId,
					pageNo: 1,
					pageSize: 300,
				};
				userStore.getSenderSetting(params).then((res) => {
					if (res && res.list && res.list.length > 0) {
						let defaultSender = res.list[0];
						resolve(defaultSender);
					} else {
						reject();
					}
				});
			}).catch(() => {
				reject();
			});

		});
	};
	const saveNext = async(res: IPackage, _params:any) => {
		if (saveType === 'print') {
			event.emit("printBatch.printAct", { printType: 'kdd', singleOrder: tradeStore.tradeListStore.list.find(item => item.togetherId === res.togetherId) });
		}

		if (optParams.autoSaveSender) {
			const senderParam = {
				senderName: _params.senderName,
				senderMobile: _params.senderMobile,
				senderProvince: _params.senderProvince,
				senderCity: _params.senderCity,
				senderDistrict: _params.senderCounty,
				senderAddressDetail: _params.senderAddress,
			};
			await IndexSettingEditSenderSettingApi(senderParam);
			userStore.getSenderSetting({
				pageNo: 1,
				pageSize: 300,
				userId: userStore?.userInfo?.userId
			});
		}
		if (optParams.autoSaveReceiver) {
			const receiverParam = {
				recipientName: _params.receiverName,
				recipientNick: _params.receiverName,
				recipientMobile: _params.receiverMobile || _params.receiverPhone,
				recipientProvince: _params.receiverProvince,
				recipientCity: _params.receiverCity,
				recipientDistrict: _params.receiverCounty,
				recipientAddressDetail: _params.receiverAddress,
			};
			await IndexSettingEditRecipientSettingApi(receiverParam);
		}
	};
	const handleReset = () => {
		Modal.confirm({
			content: '是否清空手工单信息？',
			onOk: () => {
				setOptParams(prev => ({
					...prev,
					advancedAddress: '',
				}));
				setDataSource([]);
				setParams(_.cloneDeep(initialParams));
				form.resetFields();
			}
		});
	};

	const handleSenderChange = (sender: SettingGetSenderSettingObj) => {
		form.setFieldsValue({
			senderName: sender.senderName,
			senderPhone: sender.senderMobile.indexOf('-') > -1 ? sender.senderMobile : '',
			senderMobile: sender.senderMobile.indexOf('-') > -1 ? '' : sender.senderMobile,
			senderAddress: sender.senderAddressDetail,
		});
		setParams(prev => ({
			...prev,
			senderName: sender.senderName,
			senderPhone: sender.senderMobile.indexOf('-') > -1 ? sender.senderMobile : '',
			senderMobile: sender.senderMobile.indexOf('-') > -1 ? '' : sender.senderMobile,
			senderProvince: sender.senderProvince,
			senderCity: sender.senderCity,
			senderCounty: sender.senderDistrict,
		}));
	};

	const handleReceiverChange = (receiver: SettingGetRecipientSettingResponseObj) => {
		form.setFieldsValue({
			receiverMobileSecret: '',
			receiverName: receiver.recipientName,
			receiverPhone: receiver.recipientMobile.indexOf('-') > -1 ? receiver.recipientMobile : '',
			receiverMobile: receiver.recipientMobile.indexOf('-') > -1 ? '' : receiver.recipientMobile,
			receiverAddress: receiver.recipientAddressDetail,
		});
		setParams(prev => ({
			...prev,
			tradeEncodeType:1,
			receiverName: receiver.recipientName,
			receiverPhone: receiver.recipientMobile.indexOf('-') > -1 ? receiver.recipientMobile : '',
			receiverMobile: receiver.recipientMobile.indexOf('-') > -1 ? '' : receiver.recipientMobile,
			receiverProvince: receiver.recipientProvince,
			receiverCity: receiver.recipientCity,
			receiverCounty: receiver.recipientDistrict,
			receiverAddress: receiver.recipientAddressDetail,
		}));
	};
	// 编辑时如果没有价格获取价格列表 网销价、成本价等
	const getSystemPrice = async(order: any, index:number) => {
		if (!order.getPriced && order.systemNumIid && order.systemSkuId) {
			fetchSystemList({
				sysItemId: order.systemNumIid,
				sysSkuId: order.systemSkuId,
				index
			}).then((res:any) => {
				const { price = "0.00", retailPrice = "0.00", tradePrice = "0.00", tagPrice = "0.00", costPrice = "0.00", customPrice = "0.00" } = res;
				setParams(prev => {
					prev.orders[index] = {
						...prev.orders[index],
						price,
						retailPrice,
						tradePrice,
						tagPrice,
						costPrice,
						customPrice,
						getPriced: true
					};
					return { ...prev };
				});
			}).catch(err => {
				// message.error('获取商品价格失败');
			});

		}
	};

	const fetchSystemList = async(params:{sysItemId: string, sysSkuId: string, index: number}) => {
		return new Promise((resolve, reject) => {
			const { sysItemId, sysSkuId, index } = params;
			const search = {
				sysItemId,
				sysSkuId
			};
			SysItemGetSysItemDetailApi(search).then((res) => {
				if (res) resolve(res);
				else reject();
			}).catch((err) => {
				reject();
			});
		});
	};

	const addGoodsComp = () => (
		<div className="r-flex r-ai-c">
			{
				editFlag && (
					<Tooltip placement="right" title="提示：非系统货品信息可在发货信息内自定义编辑，重新选择或修改已添加系统货品会清除原自定义编辑内容">
						<span className={ s.toolTip }>?</span>
					</Tooltip>
				)
			}
			<span className="r-ml-10" style={ { color: "rgba(107,107,107,.65)" } }>
				<span>已添加 / 可添加 ：</span>
				<span>{params?.orders?.length || 0} / {maxAddCount}</span>
			</span>
		</div>
	);

	const getZeroStockVersionDisabledValue = (item: TradeHandTradeCreatedRequest['orders'][number]) => {
		if (item.platform) {
			return `${item.platform}_${item.numIid}_${item.platformItemSkuList?.[0]?.skuId}`;
		} else {
			// 编辑的时候numIid带了平台信息
			let [platform1, numIid] = item.numIid?.split('_');
			let [platform2, skuId] = item.platformItemSkuList?.[0]?.skuId?.split('_');
			return `${platform1}_${numIid}_${skuId}`;
		}
	};

	const handleVirtualCodeChange = (e) => {
		const nextVal = e.target.checked;
		setIsVirtualCode(nextVal);
		localStorage.setItem(`${userStore?.userInfo?.userId}_VirtualCode`, `${nextVal}`);
	};

	// 保存后不自动关闭弹框
	const handleSaveNotCloseModal = (e:CheckboxChangeEvent) => {
		let checked = e.target.checked;
		setNotCloseModal(checked);
		local.set('handOrderModal-notCloseModal', checked);
	};

	const onValuesChange = (changedValues, allValues) => {
		if (changedValues?.shopSelected) setShopSelected(changedValues?.shopSelected);
		setToolTipTitle(allValues.receiverAddress);
	};

	const defaultParams = useMemo(() => {
		const {shopSelected = {}} =  form.getFieldsValue();
		if (shopSelected?.plat == "other" || shopSelected?.plat == "hand") return {};
		return {
			platform: shopSelected?.plat,
			shopId: shopSelected?.shopId,
		}
	}, [form, shopSelected, params])

	const unlock = () => {
		console.log('editFlageditFlag2222',editFlag);

		decryptAddress(trade, false)
		setParams(prev => ({
			...prev,
			tradeEncodeType: 1
		}))
	};

	const isHandOrder = isSourceHand(trade) && (handData.isCreate || editFlag)

// 生成rowKey
const getRowKey = (dataSourceItem, index) => {
	return `${dataSourceItem.rowId}_${dataSourceItem.skuId}_${dataSourceItem.sysSkuId}_${index}`;
};

// 唯一的作用是选择商品或者货品时禁止选择已经选择过的商品或货品
const getUniqIdList = (type, dataSourceItem) => {
	let uniqId = [];
	if (type === GoodsTypeEnum.平台商品) {
		/**
		 * 对于手工单而言，选择商品或者货品的时候，平台和店铺是可以任意更换的
		  * 所以，所有平台下的当前商品都不应该被选择
		 */
		if (dataSourceItem.platform === PLAT_HAND) {
			allPlatform.forEach(platform => {
				uniqId.push(`${platform}_${dataSourceItem.numIid}_${dataSourceItem.skuId}`);
			});
		} else {
			uniqId.push(`${dataSourceItem.platform}_${dataSourceItem.numIid}_${dataSourceItem.skuId}`);
		}
	} else if (type === GoodsTypeEnum.系统货品) {
		uniqId.push(`${dataSourceItem.platform}_${dataSourceItem.sysItemId}_${dataSourceItem.sysSkuId}`);
	}
	return uniqId;
};

	// 将选择的平台商品抹平转换成dataSourceItem结构
	const getDataSourceItemsByPlatformGoods = (selectedItems) => {
		const dataSourceItems = [];
		selectedItems.forEach((item, index) => {
			const skuInfo = item?.platformItemSkuList?.[0] || {};
			const relationSystemItemInfo = skuInfo?.relationSystemItemList?.[0] || {};

			let dataSourceItem = {
				sysExchangeType: SysExchangeTypeEnum.新增的商品,
				itemPicUrl: item.itemPicUrl, // 商品图片
				title: item.title,
				outerId: item.outerId,
				numIid: item.numIid,
				itemAlias: relationSystemItemInfo.sysItemAlias, // 商品没有简称
				skuId: skuInfo.skuId,
				skuOuterId: skuInfo.skuOuterId, // 平台商品的规格编码
				skuPicUrl: skuInfo.picUrl, // 规格图片
				skuName: skuInfo.skuName,
				skuAlias: skuInfo.skuAlias, // 规格别名
				sellerId: skuInfo.sellerId,
				sellerNick: skuInfo.sellerNick,
				platform: skuInfo.platform,
				price: skuInfo.price || relationSystemItemInfo.price || 0,
				newNum: item?.num || 1,
				type: GoodsTypeEnum.平台商品,
				isCombination: relationSystemItemInfo.isCombination, // 关联的系统货品是不是组合货品
				sysPicUrl: relationSystemItemInfo.picUrl,
				sysOuterId: relationSystemItemInfo.outerId, // 系统货品编码 new
				sysSkuOuterId: relationSystemItemInfo.skuOuterId,
				sysItemAlias: relationSystemItemInfo.sysItemAlias, // 货品简称
				sysItemId: relationSystemItemInfo.sysItemId,
				sysSkuId: relationSystemItemInfo.sysSkuId,
				sysSkuName: relationSystemItemInfo.sysSkuName,
				sysSkuAlias: relationSystemItemInfo.sysSkuAlias, // 货品规格别名
				rowId: item.rowId,
				warehouseSlotName: relationSystemItemInfo.warehouseSlotName,
			};
			dataSourceItem["key"] = getRowKey(dataSourceItem, index);
			dataSourceItem["uniqIdList"] = getUniqIdList(GoodsTypeEnum.平台商品, dataSourceItem);
			dataSourceItems.push(dataSourceItem);
		});
		return dataSourceItems;
	};

	// 系统货品
	const getDataSourceItemsBySysItems = (selectedItems) => {
		const dataSourceItems = [];
		selectedItems.forEach((item, index) => {
			const skuInfo = item?.sysSkuList[0] || {};
			// const relationSystemItemInfo = skuInfo?.groupCombinationList?.[0] || {};

			let dataSourceItem = {
				sysExchangeType: SysExchangeTypeEnum.新增的货品,
				itemPicUrl: item.itemPicUrl,
				title: skuInfo.sysItemAlias, // 映射货品规格名称
				outerId: item.outerId,
				numIid: item.numIid,
				itemAlias: skuInfo?.sysItemAlias, // 货品简称
				skuId: skuInfo.skuId,
				skuOuterId: skuInfo.skuOuterId, // 映射货品规格编码
				skuPicUrl: skuInfo.picUrl,
				skuName: skuInfo.sysSkuName, // 映射货品规格名称
				skuAlias: item.skuAlias, // 商品规格别名
				sellerId: skuInfo.sellerId,
				sellerNick: skuInfo.sellerNick,
				platform: skuInfo.platform,
				price: skuInfo.price || 0,
				newNum: item?.num || 1,
				type: GoodsTypeEnum.系统货品,
				isCombination: skuInfo.isCombination,
				sysPicUrl: skuInfo.picUrl,
				sysOuterId: skuInfo.outerId, // 系统货品编码 new
				sysSkuOuterId: skuInfo.skuOuterId,
				sysItemAlias: skuInfo?.sysItemAlias, // 货品简称
				sysItemId: skuInfo.sysItemId,
				sysSkuId: skuInfo.sysSkuId,
				sysSkuName: skuInfo.sysSkuName,
				sysSkuAlias: skuInfo.sysSkuAlias, // 货品规格别名
				rowId: item.rowId,
				warehouseSlotName: item.warehouseSlotName,
			};
			dataSourceItem["key"] = getRowKey(dataSourceItem, index);
			dataSourceItem["uniqIdList"] = getUniqIdList(GoodsTypeEnum.系统货品, dataSourceItem);
			dataSourceItems.push(dataSourceItem);
		});
		return dataSourceItems;
	};

		// 一次选择只能要么货品、要么商品
		const onGoodsSelected = (selectRows, type) => {
			console.log('%c [ 添加的数据是 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', type == 0 ? '平台商品' : '系统货品', selectRows);

			if (selectRows.length) {
				const dataSourceTemp = cloneDeep(getDataSource());
				let formData = {};

				let dataSourceItems = [];
				let orders:any = [];
				if (type == GoodsTypeEnum.平台商品) {
					dataSourceItems =
						getDataSourceItemsByPlatformGoods(selectRows);
					// * 零库存版 添加平台商品
					console.log(selectRows, "sysSkuListsysSkuListsysSkuList1");
					orders = (
						selectRows as ItemSysItemListOfItemRelationPlatformItemViewResponse["data"]["list"]
					).map((goods) => {
						let platformItemSku =
							goods.platformItemSkuList?.[0] || {};
						let relationSystemItemList =
							platformItemSku.relationSystemItemList?.[0] || {};

						const order = {
							...goods,
							type: GoodsTypeEnum.平台商品,
							rowId: goods.rowId,
							sysItemAlias: goods.title || "",
							sysSkuName: platformItemSku.skuName || "",
							skuOuterId: platformItemSku.skuOuterId || "",
							color: platformItemSku.color || "",
							size: platformItemSku.size || "",
							num: goods?.num || "1",
							weight: relationSystemItemList.weight || "",
							priceKey: "costPrice",
							payment: relationSystemItemList.costPrice || "0.00",
							apiPrice:
								relationSystemItemList.costPrice || "0.00",
							costPrice:
								relationSystemItemList.costPrice || "0.00",
							warehouseSlotName:
								relationSystemItemList.warehouseSlotName,
						};
						calcPayMent(order); //计算支付金额
						return order;
					});
				} else if (type == GoodsTypeEnum.系统货品) {
					dataSourceItems = getDataSourceItemsBySysItems(selectRows);
					const sysSkuList = selectRows.flatMap((b) => {
						if (b.sysSkuList) {
							return b.sysSkuList.map((item) => ({
								...item,
								num: b.num,
							}));
						}
						return [];
					});
					console.log(sysSkuList, "sysSkuListsysSkuListsysSkuList12");
					orders = sysSkuList.map((sku: SysItem) => {
						const order = {
							...sku,
							type: GoodsTypeEnum.系统货品,
							color: sku?.sysColor || "",
							size: sku?.sysSize || "",
							num: sku?.num || "1",
							weight: sku.weight || "",
							priceKey: "price",
							payment: sku.price || "0.00",
							apiPrice: sku.price || "0.00",
							sysOuterId: sku.outerId,
							sysOuterSkuId: sku.skuOuterId,
							warehouseSlotName: sku.warehouseSlotName,
						};
						calcPayMent(order); // 计算支付金额
						return order;
					});
				}
				const allKey = dataSourceTemp?.map(item => item.key) || [];
				dataSourceItems = dataSourceItems?.filter(item => !allKey.includes(item.key));
				dataSourceItems?.forEach((dataSourceItem, index) => {
					formData[`${dataSourceItem.key}@num`] = dataSourceItem?.newNum || 1;
					dataSourceTemp.push(dataSourceItem);
				});
				handleParamsChange('orders', params.orders.concat(orders));
				setDataSource(dataSourceTemp);

				setTimeout(() => {
					form.setFieldsValue(formData);
				}, 10);
			}
		};

		const handleHadSender = () => {
			if(!hasFunctionPermission(FunctionPermissionEnum.发件人信息编辑)) {
				message.error("无修改权限")
	   return;
			}
			setIsShowSenderSelectModal(true);

		}

		const orderInfo =(order) =>{
			// console.log(order,'orderorderorderorder')
			let imgSrc = order.itemPicUrl||order.picUrl;

			return <div style={{width:'440px', display:'flex',paddingLeft:'20px'}}>
						<Popover
						//overlayClassName={ s['popover'] }
						placement="right"
						 content={ <Image preview={false}  width={200}height={200} src={ imgSrc || DEFAULT_IMG} /> }>
							<Image
							preview={false}  width={40}height={40} src={ imgSrc|| DEFAULT_IMG } />
						</Popover>
					<div>
							{/* 商品名称（商家编码）；简称
							    规格名称（规格编码）；别名（货品规格编码） --库存版*/}
						<div className={ `r-ml-8 r-c-666` }style={{maxWidth:'370px'}}>
							<span className="r-mr-4">
							{ order.title||'' }
							</span>
							{order.outerId && (
								<span className="r-mr-4">
									({order.outerId})
								</span>
							)}
							{order.sysItemAlias && (
								<span className="r-mr-4">
									{order.sysItemAlias}
								</span>
							)}
						</div>
						<div className={ `r-ml-8 r-c-999` } style={{maxWidth:'370px'}}>
							<span className="r-mr-4">
                        		{ order.sysSkuName || '' }
							</span>
							{order.skuOuterId && (
								<span className="r-mr-4">
									({order.skuOuterId})
								</span>
							)}
							{order.sysSkuAlias && (
								<span className="r-mr-4">
									 {order.sysSkuAlias}
								</span>
							)}
							{order.sysOuterSkuId && (
								<span className="r-mr-4">
									({order.sysOuterSkuId})
								</span>
							)}
						</div>
					</div>

				</div>
		}
	return (
		<Modal
			centered
			visible
			width={ 1200 }
			bodyStyle={{ height: 'calc(100vh - 200px)' }}
			onCancel={ () => { setIsShowHandOrderModal(false); } }
			title={ (
				<div>
					<span className="r-fs-16 r-bold">{editFlag ? "编辑手工单" : "新建手工单"}</span>
					<span className="r-fs-12 r-ml-12" style={ { color: '#777' } }>免责声明：由于【收件地址】复杂，请您务必检查【收件地址】是否正确，本软件不承担因个人未检查而导致快递配送错误的问题</span>
				</div>
			) }
			footer={ null }
			destroyOnClose
			maskClosable={ false }
			className="hand-order-modal"
		>
			<Form
				form={ form }
				labelCol={ {
					style: { width: 80 }
				} }
				style={ { paddingBottom: 30 } }
				onFinish={ handleFinish }
				// scrollToFirstError
				validateMessages={ validateMessages }
				onValuesChange={ onValuesChange }
				onFinishFailed={() => {
					setTimeout(() => {
						const errorElement = document.querySelector('.hand-order-modal').querySelector('.ant-form-item-has-error');
						if (errorElement) {
							errorElement.scrollIntoView({ behavior: 'smooth' });
						}
					}, 100);
				}}
			>
				<Form.Item label="一键粘贴">
					<Radio.Group
						className="r-flex r-ai-c"
						style={ { height: 32 } }
						value={ copyType }
						onChange={ (e) => { setCopyType(e.target.value)} }
					>
						<Radio value={ copyTypeEnum.收件人信息 } >
							收件人信息
						</Radio>
						<Radio value={ copyTypeEnum.订单编号 } >
							订单编号
						</Radio>
						<Radio value={ copyTypeEnum.系统单号 } >
							系统单号
						</Radio>
					</Radio.Group>
				</Form.Item>
				{
					copyType === copyTypeEnum.收件人信息 ? (
						// label="一键粘贴"
						<Form.Item >
							<Input.TextArea
								style={ { marginLeft: 80, width: 804, height: 88 } }
								onInput={ (e: any) => {
									e.persist();
									setOptParams(prev => ({
										...prev,
										advancedAddress: e.target?.value
									}));
								} }
								value={ optParams.advancedAddress }
								placeholder={`1.可将复制的收件人信息粘贴至此处（识别结果仅供参考，请注意核对）
2.已接入AI智能识别，支持补填地址、区分发货内容等功能，可点击AI识别立即体验`}
							/>
							<span className="r-ml-8" style={ { verticalAlign: 'top', display: 'inline-flex' } } >
							<div style={{ display: 'flex', flexDirection: 'column', marginRight: 8 }}>
									<Button
										data-point={Pointer.订单_订单打印_手工单_高级识别}
										type="primary"
										onClick={ advancedParse }
									>
										高级识别
									</Button>
									<div style={{ position: 'relative', width: 100 }}>
										<Tooltip title="现已接入AI智能识别（支持地址智能补全、发货内容智能识别）" placement='bottom'>
											<Button
												data-point={Pointer.订单_新建_订单打印_手工单_AI识别}
												type="primary"
												onClick={ advancedAiParse }
												style={{ marginTop: 10, width: '100%' }}  // 宽度继承父级
											>
												AI识别
											</Button>
										</Tooltip>
										<span className={ s.newIcon }>new</span>
									</div>

								</div>
								<span className="r-ml-12">
									<Checkbox checked={ isVirtualCode } onChange={ handleVirtualCodeChange }>解析虚拟号</Checkbox>
									<Tooltip title="地址识别时自动解析虚拟号分机号并填充至收件人与详细地址，目前仅菜鸟、拼多多电子面单支持虚拟号打印"><QuestionCircleOutlined /></Tooltip>
								</span>
							</span>
						</Form.Item>
					) : null
				}
				{
					copyType === copyTypeEnum.订单编号 ? (
						// label="复制订单"
						<Form.Item>
							<div style={ { display: 'flex', alignItems: 'center' } }>
								<Input
									style={ { marginLeft: 80, width: 708} }
									onInput={ (e: any) => {
										e.persist();
										setOptParams(prev => ({
											...prev,
											copyTid: e.target?.value
										}));
									} }
									value={ optParams.copyTid }
									placeholder="请输入订单编号"
								/>
								<Button data-point={ Pointer.订单_手工单_一键复制 } loading={ copyLoading } type="primary" className="r-ml-8" style={ { display: 'flex', alignItems: 'center' } } onClick={ () => { copyOrder(); } }>
									一键复制
								</Button>
								{
									isHandOrder
										? null
										: <div style={{ paddingLeft: 12, userSelect: 'none'}}>
												<Checkbox
													checked={optParams.useOriginEncrypt}
													onChange={(e) => {
														setOptParams(prev => ({
															...prev,
															useOriginEncrypt: e.target.checked
														}));
													}}
												>
													使用密文展示收件人信息
												<Tooltip title={<div>
													<div>1、仅复制平台订单时生效</div>
													<div>2、创建时不解密原订单，不消耗解密额度</div>
													<div>3、原订单加密有周期限制，创建后请及时进行打印发货</div>
													<div>4、解密操作会消耗解密额度，保存后会进行系统加密</div>
													</div> } placement="right">
														<QuestionCircleOutlined  className="r-pointer r-ml-4"/>
													</Tooltip>
												</Checkbox>
										</div>
								}
							</div>
						</Form.Item>
					) : null
				}
				{
					copyType === copyTypeEnum.系统单号 ? (
						<Form.Item>
							<div style={ { display: 'flex',alignItems: 'center'} }>
								<Input
									style={ { marginLeft: 80, width: 708 } }
									onInput={ (e: any) => {
										e.persist();
										setOptParams(prev => ({
											...prev,
											copyTid: e.target?.value
										}));
									} }
									value={ optParams.copyTid }
									placeholder="请输入系统单号"
								/>
								<Button data-point={ Pointer.订单_手工单_一键复制 } loading={ copyLoading } type="primary" className="r-ml-8" style={ { display: 'flex', alignItems: 'center'} } onClick={ () => { copyOrder(); } }>
									一键复制
								</Button>
								{
									isHandOrder
										? null
										: <div style={{ paddingLeft: 12, userSelect: 'none' }}>
												<Checkbox
													checked={optParams.useOriginEncrypt}
													onChange={(e) => {
														setOptParams(prev => ({
															...prev,
															useOriginEncrypt: e.target.checked
														}));
													}}
												>
													使用密文展示收件人信息
													<Tooltip title={<div>
														<div>1、仅复制平台订单时生效</div>
														<div>2、创建时不解密原订单，不消耗解密额度</div>
														<div>3、原订单加密有周期限制，创建后请及时进行打印发货</div>
														<div>4、解密操作会消耗解密额度，保存后会进行系统加密</div>
													</div>} placement="right">
														<QuestionCircleOutlined className="r-pointer r-ml-4" />
													</Tooltip>
												</Checkbox>
										</div>
								}
							</div>
						</Form.Item>
					) : null
				}
				{
					decryptedError&&!optParams.useOriginEncrypt && !isHandOrder ?
						<div style={{ paddingLeft: 80, position: 'relative', display: 'flex' }}>
							<Form.Item name="receiverAddress" >
								<span style={{ color: '#FF4D4F', display:'flex', alignItems:'center'}}>提示：解密失败，勾选【使用密文展示收件人】使用原订单密文方式创建手工单
									<Button type="link" className="r-click" onClick={() => {
										setOptParams(prev => ({
											...prev,
											useOriginEncrypt: true
										}));
									}}>一键勾选</Button>
								</span>
							</Form.Item>
						</div>:null
				}
				<div className="r-flex">
					<Form.Item
						label="选择店铺"
						name="shopSelected"
						required
						rules={ [{ required: true }] }
					>
						<ShopSingleSelect searchShop disabled={ editFlag || isDecrypted } style={ { width: 160 } } isHideAllPlat isHideAllShop isHasHandPlat />
					</Form.Item>
					<Button type="link" className="r-click" onClick={ () => { history.push('/shops'); } }>新增虚拟店铺</Button>
					<Button type="link" className="r-click" onClick={ () => setIsShowQuickSetShopModal(true) } >快捷设置店铺</Button>
				</div>
				<div className="r-flex">
					<Form.Item
						label="收件人"
						required
						name="receiverName"
						rules={[{ required: true }]}
						style={{display:'flex',alignItems:'center'}}
					>
						<Input
							disabled={isDecrypted}
							className={ s['w-160'] }
							placeholder="收件人姓名"
							maxLength={ 32 }
						/>
					</Form.Item>
					{ isDecrypted ? <Icon type="suo" onClick={()=>unlock()} style={ { color: '#FF4D4F', marginLeft: 5, marginRight: 5,position:'relative',top:'8px' } } /> : null }
					<Button data-point={ Pointer.订单_订单打印_手工单_已有联系人 } type="link" className="r-click" onClick={ () => { setIsShowReceiverSelectModal(true); } }>已有收件人</Button>
					<Input.Group compact style={ { width: "auto", display: "flex" } }>
						<Form.Item
							label="手机"
							// required
							className="r-mr-32 r-flex"
							style={ { display: "flex" } }
							labelCol={ {
								style: { width:isDecrypted?48:74 }
							} }
							// rules={ [{ required: true }] }
							name="receiverMobile"
						>
							<Input
								disabled={isDecrypted}
								className={ s['w-160'] }
								placeholder="手机号"
							/>
						</Form.Item>
						<span className="r-c-999 r-lh-32">--</span>
						<Form.Item
							style={{marginRight:isDecrypted? 0 : 20}}
							name="receiverMobileSecret"
						>
							<Input
								disabled={isDecrypted || shopSelected?.plat === PLAT_FXG}
								className={ s['w-60'] }
							/>
						</Form.Item>
						{ isDecrypted ? <Icon type="suo" onClick={()=>unlock()} style={ { color: '#FF4D4F', marginLeft: 5, marginRight: 5,position:'relative',top:'8px' } } /> : null }
					</Input.Group>

					<Form.Item
						label="固话"
						labelCol={ {
							style: { width:isDecrypted ? 53 : 60 }
						} }
						name="receiverPhone"
					>
						<Input
							disabled={isDecrypted}
							className={ s['w-160'] }
							placeholder="固话"
						/>
					</Form.Item>
					{ isDecrypted ? <Icon type="suo" onClick={()=>unlock()} style={ { color: '#FF4D4F', marginLeft: 5, marginRight: 5,position:'relative',top:'8px' } } /> : null }
				</div>
				{optParams.receiverAddrType === 'choose' ? (
					<div>
						<div className="r-flex">
							<Form.Item
								label="收件地址"
								required
								name="receiverAddrObj"
							>
								<AddrSelect
									disabled={isDecrypted}
									province={ params.receiverProvince }
									city={ params.receiverCity }
									country={params.receiverCounty}
									selectWidth={160}
									marginRight={8}
									formItem
									triggerChange
									onChange={ (addr) => {
										setParams(prev => ({
											...prev,
											receiverProvince: addr?.provinceName || '',
											receiverCity: addr?.cityName || '',
											receiverCounty: addr?.countyName || '',
										}));
									} }
								/>
							</Form.Item>

							<div
								style={{opacity:isDecrypted?0.5:1,pointerEvents:isDecrypted?'none':'auto',cursor:isDecrypted?'not-allowed':'pointer'}}
								onClick={ () => {
									setOptParams(prev => ({
										...prev,
										receiverAddrType: 'input'
									}));
								} }
								className={ `r-click r-ml-10 r-mr-10 ${s["change-mode-label"]}` }
							>输
							</div>
							<Checkbox
								disabled={isDecrypted}
								onChange={ (e) => { setOptParams(prev => ({ ...prev, autoSaveReceiver: e.target.checked })); } }
								checked={ optParams.autoSaveReceiver }
								style={ { marginTop: 6 } }
							>同时保存收件人
							</Checkbox>
						</div>
						<div style={ { paddingLeft: 80, position: 'relative' ,display:'flex'} }>
							<Tooltip title={ toolTipTitle } placement="right">
									<Form.Item name="receiverAddress" >
									<Input disabled={isDecrypted} style={{ width: 496 }} placeholder="请输入详细地址" maxLength={128} />
								</Form.Item>
							</Tooltip>
							{ isDecrypted ? <Icon type="suo" onClick={()=>unlock()} style={ { color: '#FF4D4F', marginLeft: 5, marginRight: 5,position:'relative',top:'8px' } } /> : null }
						</div>
					</div>
				) : (
					<div className="r-flex">
						<Form.Item
							rules={ [{ required: true }] }
							label="收件地址"
							required
							name="receiverCustomAddress"
						>
								<Input
									style={{width: 496}}
									placeholder=""
							/>
						</Form.Item>
						<div
							onClick={ () => {
								setOptParams(prev => ({
									...prev,
									receiverAddrType: 'choose'
								}));
							} }
							className={ `r-click ${s["change-mode-label"]} r-ml-10 r-mr-10` }
						>选
						</div>
						<Checkbox
							onChange={ (e) => { setOptParams(prev => ({ ...prev, autoSaveReceiver: e.target.checked })); } }
							checked={ optParams.autoSaveReceiver }
							style={ { marginTop: 6 } }
						>同时保存收件人
						</Checkbox>
					</div>
				)}
				{
					params.tradeEncodeType != 2 && shopSelected?.plat == PLAT_FXG && (
						<div className="r-relative" style={ { color: '#f00', marginLeft: 80, top: -16 } }>
							提示：明文地址手工单使用抖音面单取号时填写地址不生效，收件地址将与填写订单地址保持一致
						</div>
					)
				}
				{/* 商品 */}
				<Form.Item label="商品信息">
				<div className="r-flex r-ai-c">
					{/* <div className="r-flex r-ai-c">
						{isShowZeroStockVersion ? (
							<ZeroStockOnlineListModal
								from={ FetchSysItemListEnum.订单 }
								type="checkbox"
								showColumnKeys={ ['itemNo', 'barCode', 'setCommon'] }
								onOk={ renderGoodsList }
								defaultParams={ defaultParams }
								maxAddCount={ maxAddCount }
								addedCount={ params?.orders?.length || 0 }
								disabled={ { key: 'uniqId', value: params.orders.map(item => getZeroStockVersionDisabledValue(item)) || [] } }
								onClose={ () => { setShowSetCommon(false); } }
								visible={ showSetCommon }
							>
								<Button type="primary" disabled={ (params?.orders?.length || 0) === maxAddCount }>添加商品（老的）</Button>
							</ZeroStockOnlineListModal>
						) : (
							<ProductListModal
								from={ FetchSysItemListEnum.订单 }
								onOk={ renderGoodsList }
								maxAddCount={ maxAddCount }
								addedCount={ params?.orders?.length || 0 }
								disabled={ { key: 'sysSkuId', value: params.orders.map(item => item.sysSkuId) } }
								onClose={ () => { setShowSetCommon(false); } }
								visible={ showSetCommon }
								showColumnKeys={ ['setCommon'] }
							>
								<Button type="primary" disabled={ (params?.orders?.length || 0) === maxAddCount }>添加商品（老的）</Button>
							</ProductListModal>
						) }
						{addGoodsComp()}
					</div> */}

					<Button type="primary" onClick={()=>{modalState.visible = true}} disabled={ (params?.orders?.length || 0) === maxAddCount }>添加商品</Button>
					<ChooseGoodsModal
						hasCollect
						addedCount={ params?.orders?.length || 0 }
						maxAddCount={maxAddCount}
						inputTitle='要添加的商品'
						visible={ modalState.visible }
						type="checkbox"
						defaultParams={ defaultParams }
						disabled={ { key: 'uniqId', value: getAllUniqIdListList() } }
						onOk={ (selectRows, type) => {
							onGoodsSelected(selectRows, type);
						} }
						onClose={ () => { modalState.visible = false; } }
						hasBatchedNum
						chooseType="addGoods"
						localKey={isShowZeroStockVersion ? undefined : 'ADD_HAND_GOODS_TAB_KEY'}
					/>
					{addGoodsComp()}
					</div>
				</Form.Item>

				{/* 常用商品 */}
				<Form.Item label="" wrapperCol={ { style: { marginLeft: 80 } } }>
					<div className="r-flex r-ai-c">
						<SavaGoodsSelect style={ { width: 160 } } selectChange={ handleSavaGoodsSelectChange } toSetCommmon={ () => { modalState.visible = true; } } />
						<div className="r-flex r-ai-c r-ml-18">
							<Form.Item label="代收金额" name="waitReceiveAmount" style={{padding: 0, margin: 0,marginLeft:68}}>
								<InputNumber
									className={ s['w-160'] }
									min={ 0 }
									// max={ 99999999 }
									precision={ 2 }
								/>
							</Form.Item>
							<span className="r-ml-8">元</span>
							<Tooltip title="货到付款建议使用菜鸟和拼多多面单，其他平台的面单发货需要确认模版是否支持选代收服务" placement="topLeft" arrowPointAtCenter>
								<QuestionCircleOutlined  className="r-pointer r-ml-4"/>
							</Tooltip>
						</div>
					</div>

				</Form.Item>

				{
					params.orders.length ? (
						<Form.Item label=" " colon={ false } >
							<div className={ `${s['goods-total-title']}` }>
								{
									isShowZeroStockVersion ? (
										ZeroStockVersionGoodsOrderTitle.map((item) => (
											<div style={ { width: item.width, marginLeft: 8 } }>{
												item.title === "重量"
													? `重量 (${isKg ? 'kg' : 'g'})`
													: item.title
											}
											</div>
										))
									) : (
										GoodsOrderTitle.map((item) => (
											<div style={ { width: item.width, marginLeft: 8 } }>{
												item.title === "重量"
													? `重量 (${isKg ? 'kg' : 'g'})`
													: item.title
											}
											</div>
										))
									)
								}
							</div>

						</Form.Item>
					) : null
				}
				{params.orders?.map((order, index) => (
					<Form.Item label=" " colon={ false } key={ index }>
						<div style={ { display: "flex", paddingBottom: 8, borderBottom: "1px solid #f0f0f0" } }>
							{orderInfo(order)}

							{/* 单价 */}
							<Input
								className={ `${s['w-80']} r-ml-8 ${s['h-30']}` }
								disabled={ order.priceKey !== 'customPrice' }
								onChange={ (e: any) => { handleOrderChange(index, 'apiPrice', e.target.value, 'decimal'); } }
								value={ order.apiPrice }
							/>
							<Dropdown getPopupContainer={ (e) => e.parentElement } className={ `${s['w-80']} ${s['h-30']}` } overlay={ renderPriceCom(order, index) } trigger={ ['click'] }>
								<Button
									type="text"
								>{dropMenuPriceList.find(item => item.key === order.priceKey)?.name} <DownOutlined />
								</Button>
							</Dropdown>

							{/* 数量 */}
							<Input
								className={ `${s['w-70']} ${s['h-30']} r-ml-8` }
								onInput={ (e: any) => { handleOrderChange(index, 'num', e.target.value, 'integer'); } }
								value={ order.num }
								placeholder="数量"
							/>
							<Input
								className={ `${s['w-70']} ${s['h-30']} r-ml-8` }
								onChange={ (e: any) => { handleOrderChange(index, 'discount', e.target.value, 'decimal'); } }
								placeholder="优惠"
								value={ order.discount }
							/>
							{/* <span className="r-mr-8">实付：</span> */}
							<Input
								className={ `${s['w-100']} ${s['h-30']} r-ml-8` }
								onChange={ (e: any) => {
									handleOrderChange(index, 'payment', e.target.value, 'decimal');
								} }
								value={ order.payment }
							/>
							{/* 重量 */}
							<WeightInput className="r-ml-8" value={order.weight} isKg={isKg} onChange={(v) => handleOrderChange(index, 'weight', v, isKg ? 'decimal' : 'integer', 3)}/>

							<span className="r-click r-ml-8" onClick={ () => { deleteOrder(index); } }>删除</span>
						</div>
					</Form.Item>
				))}
				{params.orders.length ? (
					<Form.Item label=" " colon={ false }>
						<div className={ `r-flex ${s['goods-total-con']}` }>
							<span className="r-bold">合计：</span>
							<Form.Item
								className="r-flex r-mb-0"
								label="数量"
								labelCol={ {
									style: { width: 50, }
								} }
							>
								<Input className={ s['w-80'] } disabled value={ params.num } />
							</Form.Item>
							<Form.Item
								className="r-flex r-mb-0"
								label="总重量"
								labelCol={ {
									style: { width: 60, }
								} }
							>
								<WeightInput disabled value={params.weight} isKg={ isKg} width={80}/>
							</Form.Item>
							<Form.Item
								className="r-flex r-mb-0"
								label="运费"
								labelCol={ {
									style: { width: 60, }
								} }
							>
								<Input
									className={ s['w-80'] }
									onInput={ (e: any) => { handleParamsChange('postFee', e.target.value); } }
									value={ params.postFee }
								/>
							</Form.Item>
							<Form.Item
								className="r-flex r-mb-0"
								label="优惠"
								labelCol={ {
									style: { width: 60, }
								} }
							>
								<Input className={ s['w-80'] } disabled value={ params.discount } />
							</Form.Item>
							<Form.Item
								className="r-flex r-mb-0"
								label="实付金额"
								labelCol={ {
									style: { width: 80, }
								} }
							>
								<Input className={ s['w-80'] } disabled value={ params.payment } />
								<span className="r-c-666 r-fs-12">（合计内容会自动计算，也可手动修改）</span>
							</Form.Item>

						</div>
					</Form.Item>

				) : ''}
				{/* 商品 end */}
				<div className="r-flex">
					<Form.Item label="发件人" name="senderName">
						<Input
							className={ s['w-160'] }
							placeholder="若不填写，使用默认"
							maxLength={ 32 }
							disabled={!hasFunctionPermission(FunctionPermissionEnum.发件人信息编辑)}
						/>
					</Form.Item>
					<Button
						data-point={ Pointer.订单_订单打印_手工单_已有发件人 }
						type="link"
						className="r-click r-ml-8"
						onClick={ handleHadSender }
					>已有发件人
					</Button>
					<Form.Item
						label="手机"
						className="r-mr-32"
						labelCol={ {
							style: { width: 66 }
						} }
						name="senderMobile"
					>
						<Input
							className={ s['w-160'] }
							placeholder="手机号"
							disabled={!hasFunctionPermission(FunctionPermissionEnum.发件人信息编辑)}
						/>
					</Form.Item>
					<Form.Item
						label="固话"
						labelCol={ {
							style: { width: 117 }
						} }
						name="senderPhone"
					>
						<Input
							className={ s['w-160'] }
							placeholder="固话"
							disabled={!hasFunctionPermission(FunctionPermissionEnum.发件人信息编辑)}
						/>
					</Form.Item>
				</div>

				{optParams.senderAddrType === 'choose' ? (
					<div>
					<div className="r-flex">
						<Form.Item label="发件地址" name="senderAddrObj">
							<AddrSelect
								province={ params.senderProvince }
								city={ params.senderCity }
								country={ params.senderCounty }
								formItem
								triggerChange
								selectWidth={160}
								marginRight={8}
								onChange={ (addr) => {
									setParams(prev => ({
										...prev,
										senderProvince: addr?.provinceName || '',
										senderCity: addr?.cityName || '',
										senderCounty: addr?.countyName || '',
									}));
								} }
								disabled={!hasFunctionPermission(FunctionPermissionEnum.发件人信息编辑)}
							/>
						</Form.Item>
							<div
								onClick={ () => {
									if(!hasFunctionPermission(FunctionPermissionEnum.发件人信息编辑)) {
										return
									}
									setOptParams(prev => ({
										...prev,
										senderAddrType: 'input'
									}));
								} }
								className={ `r-click r-ml-10 r-mr-10 ${s["change-mode-label"]}` }
							>输
							</div>
							<Checkbox
								onChange={ (e) => { setOptParams(prev => ({ ...prev, autoSaveSender: e.target.checked })); } }
								checked={ optParams.autoSaveSender }
								style={ { marginTop: 6 } }
								disabled={!hasFunctionPermission(FunctionPermissionEnum.发件人信息编辑)}
							>同时保存发件人
						</Checkbox>
						</div>
						<div style={ { paddingLeft: 72, position: 'relative' } }>
							<Form.Item className="r-ml-8" name="senderAddress">
								<Input
									style={{width:496}}
									placeholder=""
									maxLength={ 200 }
									disabled={!hasFunctionPermission(FunctionPermissionEnum.发件人信息编辑)}
									/>
							</Form.Item>
						</div>
					</div>
				) : (
					<div className="r-flex">
						<Form.Item
							label="发件地址"
							required
							rules={ [{ required: true }] }
							name="senderCustomAddress"
						>
							<Input
								style={{width: 496}}
								placeholder=""
							/>
						</Form.Item>
						<div
							onClick={ () => {
								setOptParams(prev => ({
									...prev,
									senderAddrType: 'choose'
								}));
							} }
							className={ `r-click r-ml-10 r-mr-10 ${s["change-mode-label"]}` }
						>选
						</div>
						{/* <Checkbox style={ { marginTop: 6 } }>同时保存发件人</Checkbox> */}
						<Checkbox
							onChange={ (e) => { setOptParams(prev => ({ ...prev, autoSaveSender: e.target.checked })); } }
							checked={ optParams.autoSaveSender }
							style={ { marginTop: 6 } }
						>同时保存发件人
						</Checkbox>
					</div>
				)}

				<div className="r-flex">
					<Form.Item
						label="订单编号"
						className="r-mr-12"
						name="tid"
						required={shopSelected?.plat === PLAT_FXG}
						rules={[{ required: shopSelected?.plat === PLAT_FXG, message: '抖音平台手工单需填写平台订单编号' }]}
						style={{ width: 320 }}
					>
						{/* 编辑情况 订单编号不支持更新 */}
						<Input disabled={ editFlag } style={{ width: 230 }} />
					</Form.Item>

					<Form.Item label="卖家备注" labelCol={{style:{width:65}}}>
						<div className="r-flex r-ai-c" style={ { whiteSpace: 'nowrap' } }>
							<Input
								onInput={ (e: any) => { handleParamsChange('sellerMemo', e.target.value); } }
								value={params.sellerMemo}
								style={{width:507}}
								maxLength={ 500 }
								placeholder="最多500字"
							/>
							<div className="r-ml-8 r-mr-16">
								{params.remarkFlag ? <Icon type="flag1" style={ { color: allFlagArr?.[+params.remarkFlag]?.color } } /> : ''}
							</div>
							<span className="r-mr-10">
								<span>旗帜：</span>
								{flagArr.map((item, index) => (
									<span key={ item.color } className="r-pointer r-mr-5" onClick={ () => { handleParamsChange('remarkFlag', index + ''); } }>
										<Icon type="flag1" style={ { color: item.color } } />
									</span>
								))}
							</span>
						</div>
					</Form.Item>
				</div>

				<Form.Item
					name="authorList"
					label="达人信息"
				>
					<AuthorMultiSelect
						bgHighLight
						placeholder="请选择达人"
						dropdownStyle={ { maxHeight: '200px' } }
						dropdownOpenChange={ (open) => {
							// console.log('%c [ 达人选择器触发 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', open);
						} }
						disabledAuthorIds={ [] }
						chooseOne
						getPopupContainer={ (e) => e.parentElement }
					/>
				</Form.Item>

				<Form.Item
					label="发货信息"
				>
					<div className="r-flex">
						<div>
							<Form.Item name="shipInfo" noStyle>
								<Input.TextArea
									// disabled={ (!!inventoryDeduct && !params.orders.length) || (!inventoryDeduct && !!params.orders.length) }
									style={ { width: 804, height: 88 } }
									placeholder="添加本地商品后将自动填充发货内容"
								/>
							</Form.Item>
							<HandOrderQuickContent
								onFill={content => {
									let shipInfo = form.getFieldValue("shipInfo");
									if (shipInfo?.trim()) {
										form.setFieldsValue({ shipInfo: `${shipInfo}，${content}` });
									} else {
										form.setFieldsValue({ shipInfo: content });
									}
								}}
							/>
						</div>
						<p className="r-c-error r-ml-8" style={ { fontSize: "12px", marginTop: "4px" } }>注意：修改发货信息后请立即打印，再次查询和翻页后修改内容将不生效</p>
					</div>
				</Form.Item>

				<div className={`${s['opt-con']}`}>
					<span>
						<Checkbox
							onChange={ handleSaveNotCloseModal }
							checked={ notCloseModal }
						>
							保存后不自动关闭弹框
						</Checkbox>
					</span>
					<div>
						<Button onClick={ handleReset }>清空</Button>
						<Button
							data-point={ Pointer.订单_订单打印_手工单_手工单保存 }
							loading={ loading && saveType === 'save' }
							onClick={ () => { handleSave('save'); } }
							type="primary"
							className="r-ml-8 r-mr-8"
						>保存
						</Button>
						<Button
							data-point={ Pointer.订单_订单打印_手工单_手工单保存 }
							loading={ loading && saveType === 'print' }
							onClick={ () => { handleSave('print'); } }
							type="primary"
						>保存并打印
						</Button>
					</div>
				</div>
			</Form>

			{isShowSenderSelectModal ? (
				<SenderSelectModal
					onClose={ () => { setIsShowSenderSelectModal(false); } }
					onOk={ (sender) => {
						handleSenderChange(sender);
					} }
				/>
			) : ''}

			{isShowReceiverSelectModal ? (
				<ReceiverSelectModal
					onClose={ () => { setIsShowReceiverSelectModal(false); } }
					onOk={ (receiver) => {
						handleReceiverChange(receiver);
					} }
				/>
			) : ''}

			{
				isShowQuickSetShopModal && <QuickSetShopModal onCancel={ () => { setIsShowQuickSetShopModal(false); } } />
			}
		</Modal>
	);
};

export default observer(HandOrderModal);
