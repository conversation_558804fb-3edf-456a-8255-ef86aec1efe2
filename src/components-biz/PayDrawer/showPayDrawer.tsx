import React, { useState, useCallback, useEffect } from 'react';
import NiceModal, { useModal } from '@ebay/nice-modal-react';
import { Drawer, Button, Checkbox, Modal, message, Steps } from 'antd';
import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { DISPLAY_MAP, PAY_TYPE } from './interface';
import styles from './index.module.less';
import Overview from './overview';
import userStore from '@/stores/user';
import { OrderRecordAddValueAddedOrderApi, OrderRecordGetAliPayPageWithSessionUserApi, OrderRecordGetOrderStatusApi } from '@/apis/trade';
import Flex from '@/components/Flex';
import sendPoint from '@/utils/pointTrack/sendPoint';
import Pointer from '@/utils/pointTrack/constants';

/*
 * @Author: 唐荣洋 <EMAIL>
 * @Date: 2025-08-26 16:34:59
 * @Description: PayDrawer 统一调用入口
 */

// 支付步骤枚举
const enum PayStep {
	ORDER_INFO,
	PAYMENT,
	SUCCESS
}

// 统一的 showPayDrawer 参数类型
export interface ShowPayDrawerProps {
	type: PAY_TYPE;
	title?: string;
	orderData?: {
		itemName: string;
		version: string;
		period: string;
		validUntil: string;
		price: number;
	};
	[key: string]: any;
}

// 步骤组件
const PaySteps = ({ currentStep }: { currentStep: PayStep }) => {

	return (
		<Steps className={ styles.stepsContainer } current={ currentStep } labelPlacement="vertical">
			<Steps.Step key={ PayStep.ORDER_INFO } title="提交订单" />
			<Steps.Step key={ PayStep.PAYMENT } title="支付订单" />
			<Steps.Step key={ PayStep.SUCCESS } title="完成订单" />
		</Steps>
	);
};

// 订单信息组件
const OrderInfo = ({ orderData, onNext }: { orderData:any, onNext: () => void }) => {
	const [agreed, setAgreed] = useState(false);
	const [isModalVisible, setIsModalVisible] = useState(false);
	console.log('orderData', orderData);
	const amount = (Number(orderData.amount) / 100).toFixed(2);
	return (
		<>
			<Modal
				width="70vw"
				okText="同意"
				title="快递助手ERP商家服务软件销售及服务合同"
				visible={ isModalVisible }
				onCancel={ () => setIsModalVisible(false) }
				onOk={ () => {
					setIsModalVisible(false);
					setAgreed(true);
				} }
			>
				<div style={ { height: '70vh' } }>
					<iframe src="./order/protocol.html" width="100%" height="100%" title="协议" />
				</div>
			</Modal>
			<div className={ `${styles.orderInfo} ${styles.contentSlide} ${styles.slideIn}` }>
				<h3 style={ { marginBottom: '16px' } }>订单信息</h3>
				<div className={ styles.orderTable }>
					<table className={ styles.table }>
						<thead>
							<tr>
								<th>增购项目</th>
								<th>版本</th>
								<th>周期</th>
								<th>总价</th>
							</tr>
						</thead>
						<tbody>
							<tr>
								<td>{orderData.typeName}</td>
								<td>{orderData.versionName}</td>
								<td>
									{orderData.cycleName}
									<br />
									<small style={ { color: '#999' } }>
										有效期至：{dayjs(orderData.expiryTime).format('YYYY-MM-DD HH:mm:ss')}
									</small>
								</td>
								<td>{amount}元</td>
							</tr>
						</tbody>
					</table>
				</div>

				<Flex alignItems="center" className={ styles.agreement }>
					<Checkbox
						checked={ agreed }
						onChange={ (e) => setAgreed(e.target.checked) }
						className={ styles.checkbox }
					>
						已阅读并同意签署
					</Checkbox>
					<Button type="link" className={ styles.link } onClick={ () => setIsModalVisible(true) }>《快递助手ERP 销售协议》</Button>
				</Flex>

				<div className={ styles.footer }>
					<div className={ styles.totalPrice }>
						<span className={ styles.label }>实付款：</span>
						<span className={ styles.price }>¥ {amount}</span>
					</div>
					<Button
						type="primary"
						onClick={
							() => {
								if (agreed) {
									onNext();
								} else {
									message.error('请阅读并确认签署《快递助手ERP销售协议》后完成支付');
								}
							}
						}
					>
						同意并付款
					</Button>
				</div>
			</div>
		</>
	);
};

// 支付信息组件
const PaymentInfo = ({ orderData, onNext, zfbData }: { orderData: any, onNext: () => void, zfbData: any }) => {
	const amount = (Number(orderData.amount) / 100).toFixed(2);
	useEffect(() => {
		if (zfbData) {
			// 设置轮询，每秒调用一次
			const interval = setInterval(() => {
				OrderRecordGetOrderStatusApi({
					outTradeNo: orderData.outTradeNo,
				}).then(res => {
					console.log('轮询结果:', res);
					// 1成功 0 失败
					if (res) {
						clearInterval(interval);
						onNext();
					}
				});
			}, 1000);

			// 清理定时器
			return () => {
				clearInterval(interval);
			};
		}
	}, [zfbData, orderData.outTradeNo]);
	return (
		<div className={ `${styles.paymentInfo} ${styles.contentSlide} ${styles.slideIn}` }>
			<div className={ styles.info }>
				<div className={ styles.infoItem }>
					<span className={ styles.label }>增购项目：</span>
					{orderData.typeName}
				</div>
				<div className={ styles.infoItem }>
					<span className={ styles.label }>支付金额：</span>
					{amount} 元
				</div>
				<div className={ styles.infoItem }>
					<span className={ styles.label }>收款方：</span>
					杭州其乐融融科技有限公司
				</div>
			</div>
			<div className={ styles.qrcodeWrap }>
				<div className={ styles.qrCode }>
					<iframe title="支付" srcDoc={ zfbData } className={ styles.qrCodeUrl } width="100%" height="100%" />
				</div>
				<button type="button" className={ styles.completeBtn } onClick={ () => window.location.reload() }>
					已完成支付
				</button>
			</div>
		</div>
	);
};

// 支付成功组件
const PaymentSuccess = () => {
	const [count, setCount] = useState(5);
	useEffect(() => {
		const timer = setInterval(() => {
			setCount(count - 1);
		}, 1000);
		return () => clearInterval(timer);
	}, [count]);
	useEffect(() => {
		if (count === 0) {
			window.location.reload();
		}
	}, [count]);
	return (
		<div className={ `${styles.successContent} ${styles.contentSlide} ${styles.slideIn}` }>
			<div className={ styles.successIcon }>
				<CheckOutlined />
			</div>
			<div className={ styles.successTitle }>支付成功</div>
			<button type="button" className={ styles.backBtn } onClick={ () => window.location.reload() }>
				{count}秒后台自动刷新页面
			</button>
		</div>
	);
};

const PayDrawer = NiceModal.create((props: ShowPayDrawerProps) => {
	const modal = useModal();
	const [currentStep, setCurrentStep] = useState<PayStep>(PayStep.ORDER_INFO);
	const [showPaySteps, setShowPaySteps] = useState(false);
	const [orderInfo, setOrderInfo]:any = useState({});
	const [zfbData, setZfbData] = useState({});
	const { userInfo } = userStore;
	const handleStepChange = useCallback((nextStep: PayStep) => {
		setTimeout(() => {
			setCurrentStep(nextStep);
		}, 300);
	}, []);

	const handleNextStep = useCallback(() => {
		if (currentStep < PayStep.SUCCESS) {
			handleStepChange(currentStep + 1);
		}
	}, [currentStep, handleStepChange]);

	const handleStartPay = useCallback((params: any) => {
		OrderRecordAddValueAddedOrderApi(params).then(res => {
			if (res) {
				setShowPaySteps(true);
				setCurrentStep(PayStep.ORDER_INFO);
				setOrderInfo({ ...params, ...res });
			}
			console.log('resresres', res);
		}).catch(err => {
			console.log('err', err);
		});
	}, []);

	const onPayZfb = () => {
		OrderRecordGetAliPayPageWithSessionUserApi({
			outTradeNo: orderInfo.outTradeNo,
			amount: orderInfo.amount,
		}).then(res => {
			setZfbData(res);
			handleNextStep();
		});
	};
	const renderContent = () => {
		switch (currentStep) {
			case PayStep.ORDER_INFO:
				return <OrderInfo orderData={ orderInfo } onNext={ onPayZfb } />;
			case PayStep.PAYMENT:
				return <PaymentInfo zfbData={ zfbData } orderData={ orderInfo } onNext={ handleNextStep } />;
			case PayStep.SUCCESS:
				return <PaymentSuccess />;
			default:
				return null;
		}
	};
	const onClose = () => {
		if (showPaySteps) {
			if (currentStep === PayStep.ORDER_INFO) {
				setShowPaySteps(false);
			} else {
				modal.hide();
				setTimeout(() => {
					window.location.reload();
				}, 300);
			}
		} else {
			setShowPaySteps(false);
			setCurrentStep(PayStep.ORDER_INFO);
			modal.hide();
		}
	};
	return (
		<Drawer
			afterVisibleChange={ (visible) => {
				if (visible) {
					sendPoint(DISPLAY_MAP[props.type].showPoint);
				}
			} }
			destroyOnClose
			zIndex={ 1001 }
			width={ 1000 }
			title={ showPaySteps ? '立即订购' : '' }
			placement="right"
			onClose={ onClose }
			visible={ modal.visible }
			className={ styles.payDrawer }
			closeIcon={ null }
			extra={ <CloseOutlined onClick={ onClose } /> }
		>
			{!showPaySteps ? (
				<Overview type={ props.type } userInfo={ userInfo } activeRange={ orderInfo?.cycleStatus } activeVersion={ orderInfo?.version } onPay={ handleStartPay } />
			) : (
				<>
					<PaySteps currentStep={ currentStep } />
					<div className={ styles.content }> {renderContent()} </div>
				</>
			)}
		</Drawer>
	);
});

// 统一的 showPayDrawer 方法
export default function showPayDrawer(props: ShowPayDrawerProps) {
	NiceModal.show(PayDrawer, props as any);
}

