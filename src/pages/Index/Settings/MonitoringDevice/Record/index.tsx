import React, { useCallback, useEffect, useRef, useState } from "react";
import { observer } from 'mobx-react';
import { Button, Form, Input, Select, Modal, Space, Tag } from "antd";
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import cs from "classnames";
import { useActivate, useUnactivate } from "react-activation";
import { fontStyle } from "html2canvas/dist/types/css/property-descriptors/font-style";
import message from "@/components/message";
import userStore from '@/stores/user';
import videoMonitorStore from '@/stores/trade/videoMonitor';
import SearchTable from "@/components/SearchTableVirtual";
import NormalLayout from "@/components-biz/layouts/NormalLayout";
import { DatePickerKey, getCacheDateRange } from '@/components/DateRangeComp/kdzsRangePickerUtil';
import { FormItemConfig } from '@/components/SearchTable/FormWidthRow';
import KdzsDateRangePicker1 from "@/components/DateRangeComp/kdzsRangePicker1";
import s from "./index.module.scss";
import { VideoLogMonitorQueryVideoLogApi, IndexMonitoringQueryMonitoringDeviceApi } from '@/apis/setting/monitoringDevice';
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from '@/utils/pointTrack/constants'; 

const { Option } = Select;

// 默认查询参数
const initialRangeTime = getCacheDateRange(DatePickerKey.setting_monitoringRecord) || [dayjs().subtract(1, 'M').startOf('day'), dayjs().endOf('day')];
const defaultParams = {
	operateType: undefined,
	monitorType: undefined,
	operateName: undefined,
	exNumber: undefined,
	tid: undefined,
	refundId: undefined,
	rangeTime: initialRangeTime,
};

const OperateType = {
	1: '售后扫描登记',   
	2: '后置打印',
	3: '验货发货',
	4: '称重发货',
	5: '扫描打印',
	6: '批量扫描登记'
};

const MonitorType = {
	1: 'IPC监控设备',
	2: 'USB摄像头'
};

/**
 * 监控视频记录管理
 */
const MonitoringRecord = observer((props) => {
	const { userInfo } = userStore;
	const { userId, subUserId } = userInfo || {};

	const { 
		checkKdzsPrintComponent, 
		checkKdzsPrintComponentStatus,
		isKdzsPrintComponent,
		sendMessage,
		disconnectWs,
		isSocketConnected,
		connectWs,
		clearMessageCallbacks,
		setNvrDeviceList
	} = videoMonitorStore;

	const [form] = Form.useForm();
	const ref = useRef<any>();

	const [formData, setFormData] = useState<any>({ ...defaultParams });
	const [dataSource, setDataSource] = useState([]); // 当前显示的数据
	const [nvrDevices, setNvrDevices] = useState<any[]>([]);

	const handleAction = async(record: any, type: 'preview' | 'download') => {
		console.log(record);
		sendPoint(type === 'preview' ? Pointer.设置_监控设备管理_监控视频记录_预览 : Pointer.设置_监控设备管理_监控视频记录_下载);

		// 先判断有没有安装控件，没有安装则提示下载控件
		await checkKdzsPrintComponentStatus();

		if (!isSocketConnected) {
			Modal.error({
				title: '连接控件失败',
				content: '无法连接控件，请检查当前控件是否存在或正常使用。',
				okText: '重新连接',
				onOk: () => {
					connectWs();
				}
			});
			return;
		}

		const { erpVideoId, erpCameraId, channel } = record?.extend || {};

		const device = nvrDevices?.find(item => item.id == erpVideoId);

		const { deviceIp, deviceAccountName, deviceAccountPassword, devicePort } = device || {};

		if (!device) {
			Modal.error({
				title: '连接录像机失败',
				content: '无法连接录像机，请检查当前摄录像机是否存在或正常使用。',
				onOk: () => {
					
				}
			});
			return;
		}

		message.loading({
			content: type === 'preview' ? '正在预览视频...' : '正在下载视频...',
			key: 'downloadVideo',
			duration: 0,
		});
		// 判断是否能连接录像机
		sendMessage('login_nvr_device', {
			device_ip: deviceIp,
			device_username: deviceAccountName,
			device_password: deviceAccountPassword
		}, (res) => {
			// 根据返回结果更新验证状态
			if (res.msg) {
				// message.error(res.msg);
				Modal.error({
					title: '连接录像机失败',
					content: '无法连接录像机，请检查当前摄录像机是否存在或正常使用。',
					onOk: () => {
						
					}
				});
				message.destroy('downloadVideo');
				return;
			}

			if (res?.result === 0) {
				sendMessage('download_nvr_video', {
					nvr_device: {
						device_ip: deviceIp,
						device_username: deviceAccountName,
						device_password: deviceAccountPassword,
						device_channel: Number(channel)
					},
					outSid: record?.bizCode, // 快递单号
					type: type === 'preview' ? 0 : 1, // 0预览 1 下载
					start_time: dayjs(record?.videoStartTime).valueOf(),
					end_time: dayjs(record?.videoEndTime).valueOf()
				}, (res) => {
					message.destroy('downloadVideo');
					// 根据返回结果更新验证状态
					if (res?.data?.video_path) {
						if (type === 'preview') {
							window.open(res?.data?.video_path, '_blank');
						} else {
							Modal.info({
								title: '下载完成',
								centered: true,
								content: (
									<div>
										文件路径：{res?.data?.video_path}
									</div>
								)
							});
						}
					}
					if (res.msg) {
						message.error(res.msg);
					}
				});
			}
		});
		
	};

	// 表格列定义
	const columns: ColumnsType<any> = [
		{
			title: '序号',
			width: 40,
			dataIndex: 'index',
			render: (text, record, index) => index + 1
		},
		{
			title: '对应功能',
			width: 120,
			dataIndex: 'operateType',
			render: (text, record) => {
				return OperateType[record?.operateType];
			}
		},
		{
			title: '监控类型',
			width: 120,
			dataIndex: 'monitorType',
			render: (text, record) => {
				return MonitorType[record?.monitorType];
			}
		},
		{
			title: '操作人',
			width: 120,
			dataIndex: 'operateName',
			render: (text) => text
		},
		{
			title: '开始时间',
			width: 160,
			dataIndex: 'videoStartTime',
			render: (text) => text
		},
		{
			title: '结束时间',
			width: 160,
			dataIndex: 'videoEndTime',
			render: (text) => text
		},
		{
			title: '扫描内容',
			width: 160,
			dataIndex: 'bizCode',
			render: (text) => text || '-'
		},
		{
			title: '快递单号',
			width: 160,
			dataIndex: 'exNumber',
			render: (text) => text || '-'
		},
		{
			title: '订单编号',
			width: 160,
			dataIndex: 'tid',
			render: (text) => text || '-'
		},
		{
			title: '售后单号',
			width: 160,
			dataIndex: 'refundId',
			render: (text) => text || '-'
		},
		{
			title: '本地视频时长',
			width: 100,
			dataIndex: 'videoDuration',
			render: (text) => {
				return text ? `${text}秒` : '-';
			}
		},
		{
			title: '操作',
			width: 100,
			dataIndex: 'operation',
			render: (_, record) => (
				<div className={ cs(s.operation, 'r-flex r-ai-c') }>
					{
						record?.monitorType === 1 && (
							<>
								<Button type="link" size="small" style={ { color: '#1890ff', fontSize: 12 } } onClick={ () => handleAction(record, 'preview') }>
									预览
								</Button>
								<Button type="link" size="small" style={ { color: '#1890ff', fontSize: 12 } } onClick={ () => handleAction(record, 'download') }>
									下载
								</Button>
							</>
						)
					}
					
				</div>
			)
		}
	];

	// 查询表单配置
	const FormFieldList: FormItemConfig[] = [
		{
			label: '',
			name: 'operateType',
			children: (
				<Select style={ { width: 160 } } placeholder="对应功能" allowClear className={ formData.operateType ? 'high-light-bg' : '' }>
					<Option value={ 1 }>售后 - 售后扫描登记</Option>
					<Option value={ 2 }>订单 - 后置打印</Option>
					<Option value={ 3 }>订单 - 验货发货</Option>
					<Option value={ 4 }>订单 - 称重发货</Option>
					<Option value={ 5 }>档口 - 扫描打印</Option>
					<Option value={ 6 }>售后 - 批量扫描登记</Option>
				</Select>
			)
		},
		{
			label: '',
			name: 'monitorType',
			children: (
				<Select style={ { width: 160 } } placeholder="监控类型" allowClear className={ formData.monitorType ? 'high-light-bg' : '' }>
					<Option value={ 1 }>IPC监控设备</Option>
					<Option value={ 2 }>USB摄像头</Option>
				</Select>
			)
		},
		{
			label: '',
			name: 'operateName',
			children: <Input placeholder="请输入操作人" style={ { width: 160 } } allowClear className={ formData.operateName ? 'high-light-bg' : '' } />
		},
		{
			label: '',
			name: 'exNumber',
			children: <Input placeholder="请输入快递单号" style={ { width: 160 } } allowClear className={ formData.exNumber ? 'high-light-bg' : '' } />
		},
		{
			label: '',
			name: 'tid',
			children: <Input placeholder="请输入订单编号" style={ { width: 160 } } allowClear className={ formData.tid ? 'high-light-bg' : '' } />
		},
		{
			label: '',
			name: 'refundId',
			children: <Input placeholder="请输入售后单号" style={ { width: 160 } } allowClear className={ formData.refundId ? 'high-light-bg' : '' } />
		},
		{
			label: '',
			name: 'rangeTime',
			children: <KdzsDateRangePicker1 style={ { width: 159 } } cacheQuickChoose datePickerKey={ DatePickerKey.setting_monitoringRecord } useServeTime className="rangeTime high-light-bg" />
		}
	];

	// 获取NVR存储设备列表
	const fetchNvrDevices = async() => {
		try {
			const response = await IndexMonitoringQueryMonitoringDeviceApi({});
			if (response?.length) {
				setNvrDevices(response);
				setNvrDeviceList(response);
			}
		} catch (error) {
			console.error('获取NVR设备列表失败:', error);
		}
	};

	// 查询接口
	const fetchSystemList = async(info: any) => {
		const { rangeTime, ...rest } = info;
		console.log('%c [ info ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', 'info', info);
		const params = {
			...rest,
			monitorType: info?.monitorType,
			operateType: info?.operateType, // 售后扫描登记
			startTime: rangeTime?.[0] ? dayjs(rangeTime[0]).format('YYYY-MM-DD HH:mm:ss') : undefined,
			endTime: rangeTime?.[1] ? dayjs(rangeTime[1]).format('YYYY-MM-DD HH:mm:ss') : undefined,
			pageNo: info.pageNo || 1,
			pageSize: info.pageSize || 30,
		};

		if (!params?.startTime && params?.endTime) {
			message.error("开始时间不能为空");
			return;
		}
		if (!params?.endTime && params?.startTime) {
			message.error("结束时间不能为空");
			return;
		}
		
		return VideoLogMonitorQueryVideoLogApi(params);
	};

	// 数据适配器
	const responseAdapter = (data: any) => {
		const { list = [], total = 0 } = data || {};
		list.forEach(item => {
			item.extend = JSON.parse(item.extend || '{}');
		});
		setDataSource(list);
		fetchNvrDevices(); // 获取NVR存储设备列表
		return { list, total };
	};

	// 表格变化处理
	const _onChange = (pagination: any, filters: any, sorter: any, { action }: any) => {
		// 处理表格变化
	};

	// 表单字段变化处理
	const onFieldsChange = (changedValues: any, allValues: any) => {
		setFormData(allValues);
	};

	const onReset = () => {
		form.resetFields();
		setFormData(defaultParams);
	};

	const init = async() => {
		await checkKdzsPrintComponent(); // 检测快递助手ERP聚合控件
		connectWs(); // 连接ERP聚合控件
	};

	const cleanup = () => {
		// 清理消息回调，避免内存泄漏
		clearMessageCallbacks();
	};

	useEffect(() => {
		init();

		return () => {
			cleanup();
			disconnectWs();
		};
	}, []);

	useEffect(() => {
		sendPoint(Pointer.设置_监控设备管理_监控视频记录_点击);
	}, []);

	useUnactivate(() => {
		cleanup();
	});

	return (
		<NormalLayout className="print-batch-search-con" id="monitoringRecord">
			<div className={ s.monitoringRecord }>
				<SearchTable
					ref={ ref }
					pageSizeId="MonitoringRecordListTable"
					form={ form }
					fetchData={ fetchSystemList }
					responseAdapter={ responseAdapter }
					onReset={ onReset }
					searchBtnPoint={ Pointer.设置_监控设备管理_监控视频记录_查询 }
					searchBtnText="查询"
					resetBtnText="重置"
					showSearch
					rowFormConfig={ {
						defaultParams,
						formList: FormFieldList,
						size: 'small',
						colProps: {}
					} }
					baseTableConfig={ {
						// dataSource,
						onFieldsChange,
						rowKey: 'id',
						columns,
						pagination: {
							defaultPageSize: 30,
							pageSizeOptions: [30, 50, 100, 200, 500],
						},
						cachePgination: true,
						isStickyHeader: true,
						stickyTop: 92,
						headerColSet: {
							resizeId: `MonitoringRecordListTable_width_${userStore?.userInfo?.userId}`,
						},
						enableRowClick: true,
					} }
					onChange={ ({ pageNo, pageSize }) => {
						_onChange({ current: pageNo, pageSize }, undefined, undefined, { action: 'paginate' });
					} }
				/>
			</div>
		</NormalLayout>
	);
});

export default MonitoringRecord;
