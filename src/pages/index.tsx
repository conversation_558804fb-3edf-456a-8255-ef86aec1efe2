import "@raycloud/ads/dist-px/advert/advert.css";
import React, { lazy, ReactElement, ReactNode, Suspense, useEffect, useState } from "react";
import { Provider } from "mobx-react";
import {
	Router,
	Route,
	Switch,
	RouteComponentProps,
	Redirect
} from "react-router-dom";
import { ConfigProvider } from "antd";
import locale from 'antd/lib/locale/zh_CN';
import KeepAlive, { AliveScope } from 'react-activation';
import history from '@/utils/history';
import CustomizeRenderEmpty from '@/components/CustomizeRenderEmpty';
import 'dayjs/locale/zh-cn';
import { initApp } from "@/utils/init";
import BasicLayout from "@/components-biz/layouts/BasicLayout";
import routes from '@/routers/root';
import ErrorBoundary from '@/components-biz/ErrorBoundary';
import '@/pages/Trade/index.scss';
import SplitOrder from "./Trade/components/SplitOrder";
import PrivateRoute from '@/routers/PrivateRoute';

const LoginPage = lazy(() => import(/* webpackChunkName: "Login" */ '@/pages/Index/Login/Login'));
const AboutPage = lazy(() => import(/* webpackChunkName: "About" */ '@/pages/Index/About'));
const ChannelPage = lazy(() => import(/* webpackChunkName: "Channel" */ '@/pages/Index/Channel'));
const RegisterPage = lazy(() => import(/* webpackChunkName: "Register" */ '@/pages/Index/Login/Register'));
const InviteDistributionPage = lazy(() => import(/* webpackChunkName: "InviteDistribution" */ '@/pages/Index/Login/InviteDistribution'));
const InitializePage = lazy(() => import(/* webpackChunkName: "Initialize" */ '@/pages/Index/Initialize'));
const ForgetPwdPage = lazy(() => import(/* webpackChunkName: "ForgetPwd" */ '@/pages/Index/Login/ForgetPwd'));
const AuthorizePage = lazy(() => import(/* webpackChunkName: "Authorize" */ '@/pages/Index/Authorize'));
const PayGuidePage = lazy(() => import(/* webpackChunkName: "PayGuide" */ '@/pages/Index/PayGuide'));
const PageError = lazy(() => import(/* webpackChunkName: "ErrorPage" */ '@/pages/Index/Error'));
const AuthorRes = lazy(() => import(/* webpackChunkName: "AuthorRes" */ '@/pages/Index/AuthorRes'));
const LevelComparePage = lazy(() => import(/* webpackChunkName: "LevelCompare" */ '@/pages/Index/LevelCompare'));
const SupplyIntro = lazy(() => import(/* webpackChunkName: "SupplyIntro" */ '@/pages/Index/SupplyIntro'));
const AnnualActivity = lazy(() => import(/* webpackChunkName: "AnnualActivity" */ "@/pages/AnnualActivity/index"));
const LargeScreen = lazy(() => import(/* webpackChunkName: "LargeScrren" */ "@/pages/LargeScreen/index"));
const SemPage = lazy(() => import(/* webpackChunkName: "Sem" */ "@/pages/Index/Sem"));
const CheckWxBind = lazy(() => import(/* webpackChunkName: "CheckWxBind" */ "@/pages/Index/CheckWxBind/index"));

interface RouteProps extends RouteComponentProps {
	component?: any;
	path: string;
	exact?: boolean;
	layout?: any;
}

const App = () => {
	const [customize, setCustomize] = useState(true);

	useEffect(() => {
		initApp();
		// SplitOrder().getConfig();
	}, []);

	return (
		<ConfigProvider
			// getPopupContainer={ trigger => trigger?.parentElement } // Select Dropdown DatePicker TimePicker Popover Popconfirm 会跟随滚动条上下移动
			locale={ locale }
			renderEmpty={ customize && CustomizeRenderEmpty }
			// componentSize="small"
			autoInsertSpaceInButton={ false }
		>
			<Router history={ history }>
				<Suspense fallback={ <></> }>
					<Switch>
						<Route exact path="/login" component={ LoginPage } />
						<Route exact path="/about" component={ AboutPage } />
						<Route exact path="/channel" component={ ChannelPage } />
						<Route exact path="/sem" component={ SemPage } />
						<Route exact path="/inviteDistribution/:code?/:inviteType?" component={ InviteDistributionPage } />
						<Route exact path="/register" component={ RegisterPage } />
						<Route exact path="/forgetPwd" component={ ForgetPwdPage } />
						<Route exact path="/initialize" component={ InitializePage } />
						<Route exact path="/payGuide" component={ PayGuidePage } />
						<Route exact path="/levelCompare" component={ LevelComparePage } />
						<Route exact path="/authorize" component={ AuthorizePage } />
						<Route exact path="/error/:errorCode?" component={ PageError } />
						<Route exact path="/author/:res?" component={ AuthorRes } />
						<Route exact path="/supplyIntro" component={ SupplyIntro } />
						<Route exact path="/annual/activity" component={ AnnualActivity } />
						<Route exact path="/largeScreen" component={ LargeScreen } />
						<Route exact path="/checkWxBind" component={ CheckWxBind } />

						<Route path="/" >
							<ErrorBoundary>
								<AliveScope>
									<BasicLayout>
										<Suspense fallback={ <></> }>
											<Switch>
												{routes.map(
													({ path, ...rest }): ReactElement<RouteProps> => {
														return (
															<PrivateRoute
																key={ path }
																path={ path }
																{ ...rest }
															/>
														);
													}
												)}
											</Switch>
										</Suspense>
									</BasicLayout>
								</AliveScope>
							</ErrorBoundary>
						</Route>

						<Redirect to="/" />
					</Switch>
				</Suspense>
			</Router>

		</ConfigProvider>
	);
};

export default App;
