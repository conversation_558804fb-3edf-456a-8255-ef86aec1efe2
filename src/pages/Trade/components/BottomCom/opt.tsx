// * 重复业务逻辑提取
// * bottomCom和utils文件太大 不易维护

import React from 'react';
import { Modal } from "antd";
import { toJS } from 'mobx';
import { dealPlatAsHandPlat, transferOtherToHand } from "@/components-biz/ShopListSelect/shopListUtils";
import { PRINT_MAP, PLAT_ALI, PLAT_C2M, PLAT_TB, TEMP_MAP, HL_PLAT, HL_LABEL_PLAT, PLAT_SPH, PLAT_DW, PLAT_KTT, PLAT_FXG, PLAT_HAND } from "@/constants";
import userStore from "@/stores/user";
import { TradeChoiceType } from "@/utils/enum/trade";
import Pointer from "@/utils/pointTrack/constants";
import sendPoint from "@/utils/pointTrack/sendPoint";
import { IPackage } from "../../interface";
import { getUnPromiseLogisticsOrder } from "../AbnormalManageModal/abnormalOrder";
import { tradeStore } from "@/stores";
import memoFn from '@/libs/memorizeFn';
import { refluxOrderPrintSettingsEnum } from '@/types/trade/index';
import { templateType4PlatformTypeEnum } from '../../constants';

/**
 * 检查订单是否和模板匹配
 * @param orderList
 * @param isManualImportResend 是否为手动导入单号并重新发货
 * @returns
 */
export const checkOrdersWithTemp = async(orderList: IPackage[], tempLate?:any, isManualImportResend?: boolean) => {
	// 如果是手动导入单号并重新发货，则不进行校验，直接返回原订单列表
	if (isManualImportResend) {
		return orderList;
	}
	const advancedRes = await memoFn.getAdvancedSet();
	let isUsePlatTemp = advancedRes?.refluxOrderPrintSet == refluxOrderPrintSettingsEnum.使用对应平台面单打印;

	const { selectedTemp, isMergePrint, setting } = tradeStore;
	const { handleChoiceChange } = tradeStore.tradeListStore;
	let tempLateInfo = tempLate || selectedTemp;

	// 跳过无法打印的订单
	let notSupportedOrders: IPackage[] = [];
	let notSupportedDeWu: IPackage[] = []; // 得物品牌直发—普通履约、多仓发货标签的订单需匹配得物模版，其他的不需要限制
	let notSupportedFxgHandOrders = []; // 抖音明文手工单使用抖音电子面单打印限制
	if (isMergePrint && !tempLateInfo.userTemplateList.length) { // 融合打印没有模版组
		notSupportedOrders = [...orderList];
	} else if (isMergePrint) {
		let orderMatchSetting = setting?.groupPrintSetJsonString?.orderMatchSetting;
		console.log('%c [ orderMatchSetting ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', toJS(orderMatchSetting));
		notSupportedOrders = orderList?.filter((order: IPackage) => {
			let orderKddType = PRINT_MAP[order.platform];
			if (dealPlatAsHandPlat(order.platform, null, order)) {
				// 融合打印这里，快团团默认拼多多，排除了视频号和有赞模板
				let orderSettingType = orderMatchSetting.find(it => it.platform === transferOtherToHand(order.platform, order));
				orderKddType = orderSettingType?.bindControlType;
			}
			// 1688回流订单 明文和密文处理逻辑：
			if (order.platform == PLAT_ALI && order.hlPlatformType) {
				// 密文、或者高级设置中设定为平台的明文
				if (order.hlEncryptOrder || (isUsePlatTemp && !order.hlEncryptOrder && order.hlPlatformType !== 'hl-sph')) {
					const hlPlat = HL_PLAT[order.hlPlatformType];
					orderKddType = PRINT_MAP[hlPlat] || PRINT_MAP[PLAT_ALI];
				}
			}

			let tempType = tempLateInfo.userTemplateList.find((item:any) => {
				if ([13, 16].includes(Number(item.expressType))) {
					return orderKddType == 13;
				} else {
					return item.expressType == orderKddType;
				}
			});
			// 抖音回流密文订单，可以使用抖音电子面单打印
			if (order.platform == PLAT_ALI && order.hlPlatformType && order.hlPlatformType == 'hl-fxg' && tempType?.expressType == 8) {
				return false;
			}
			// 非抖店平台订单或手工单，不支持使用抖音电子面单打印
			if (tempType?.expressType == 8 && order.platform !== PLAT_FXG) {
				return true;
			}

			console.log('%c [ orderKddType ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', orderKddType, tempType, toJS(tempLateInfo.userTemplateList));

			return !tempType && ![PLAT_DW].includes(order?.platform);
		});
	} else {
		console.log('%c [ tempLateInfo ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', toJS(tempLateInfo));
		notSupportedOrders = orderList.filter((order: IPackage) => {
			let platform = [PLAT_ALI, PLAT_C2M].includes(order.platform) ? PLAT_TB : order.platform;
			// * 顺丰电子面单过滤掉
			if (tempLateInfo.KddType == 2) {
				return false;
			}
			// 1688回流订单 明文和密文处理逻辑：
			// 密文订单——只支持下游平台电子面单
			// 明文订单——只支持1688菜鸟电子面单，改为 通过高级设置判断支持哪种
			// hlEncryptOrder，true = 回流密文订单，false = 回流明文订单
			if (order.platform == PLAT_ALI && order.hlPlatformType) {
				const hlPlat = HL_PLAT[order.hlPlatformType];
				const noHlPrint = !dealPlatAsHandPlat(hlPlat) && hlPlat != TEMP_MAP[tempLateInfo.KddType];
				// 密文或者高级设置中设定为平台的明文
				if (order.hlEncryptOrder || (isUsePlatTemp && !order.hlEncryptOrder && order.hlPlatformType !== 'hl-sph')) {
					return noHlPrint;
				}
			}
			// 视频号电子面单不能打印手工单
			// if (order.source?.toLocaleLowerCase() == PLAT_HAND && tempLateInfo.KddType == templateType4PlatformTypeEnum.sph) {
			// 	return true;
			// }
			if (order.platform !== PLAT_FXG && TEMP_MAP[tempLateInfo.KddType] === PLAT_FXG) {
				return true;
			}

			// 抖音明文手工单使用抖音电子面单打印限制
			if (order.platform == PLAT_FXG && order.source?.toLocaleLowerCase() == PLAT_HAND && order?.trades?.[0]?.tradeEncodeType != 2 && tempLateInfo.KddType == 8) {
				notSupportedFxgHandOrders.push(order);
			}

			// 快团团模板判断，不能选视屏号、有赞模板
			if (order.platform == PLAT_KTT) {
				return [templateType4PlatformTypeEnum['sph'], templateType4PlatformTypeEnum['yz'], templateType4PlatformTypeEnum['dw']].includes(tempLateInfo.KddType);
			}

			return !dealPlatAsHandPlat(order.platform, null, order) && platform != TEMP_MAP[tempLateInfo.KddType];
		});
	}

	console.log(notSupportedOrders, 'notSupportedOrders');
	console.log(notSupportedFxgHandOrders, 'notSupportedFxgHandOrders');

	// 所选快递与指定快递检验
	let unPromiseLogisticsInfo = getUnPromiseLogisticsOrder(orderList, tempLate);
	let { userSetting } = userStore;

	// 弹出得物拦截校验弹框
	notSupportedDeWu = orderList.filter(order => {
		return order.platform == PLAT_DW && (order?.serviceTagList?.includes('ordinaryOrder') || order?.serviceTagList?.includes('warehouseOrder'));
	});
	const name = isMergePrint ? tempLateInfo?.exCode : tempLateInfo.ExCode;
	if (notSupportedDeWu?.length && !["DWTYMB"].includes(name)) {
		let isContinue = await checkAbnormalDeWu(tempLateInfo, notSupportedDeWu);
		if (!isContinue) {
			return Promise.reject();
		}

		let cancelChoiceOrders: Array<string> = [];
		notSupportedDeWu.forEach(item => {
			item.togetherId && cancelChoiceOrders.push(item.togetherId);
		});

		handleChoiceChange({
			type: TradeChoiceType.取消勾选指定订单,
			cancelChoiceOrders
		});

		orderList = orderList.filter((order: IPackage) => !cancelChoiceOrders.includes(order.togetherId));
	}

	// if (notSupportedFxgHandOrders.length > 0) {
	// 	return new Promise((resolve, reject) => {
	// 		Modal.confirm({
	// 			centered: true,
	// 			title: '系统提示',
	// 			content: (
	// 				<div className="r-c-warning" style={ { maxHeight: 600, overflowY: 'auto' } }>
	// 					<p>明文收件地址不支持抖音电子面单取号，建议使用菜鸟等其他平台电子面单打印</p>
	// 					<p>订单号：{notSupportedFxgHandOrders.map(i => `${i.togetherId}${i.hlPlatformType ? `(${HL_LABEL_PLAT[i.hlPlatformType]})` : ''}`).join(',')}</p>
	// 				</div>
	// 			),
	// 			okText: '跳过，继续打印',
	// 			cancelText: '取消',
	// 			onOk: () => {
	// 				sendPoint(Pointer.继续打印);
	// 				let cancelChoiceOrders: Array<string> = [];
	// 				notSupportedFxgHandOrders.forEach(item => {
	// 					item.togetherId && cancelChoiceOrders.push(item.togetherId);
	// 				});

	// 				handleChoiceChange({
	// 					type: TradeChoiceType.取消勾选指定订单,
	// 					cancelChoiceOrders
	// 				});

	// 				resolve(orderList.filter(order => !cancelChoiceOrders.includes(order.togetherId)));
	// 				window.errorCollection?.customMessageUpload({
	// 					type: `明文收件地址不支持抖音电子面单取号，建议使用菜鸟等其他平台电子面单打印【 跳过，继续打印 】`,
	// 					data: {
	// 						notSupportedFxgHandOrders,
	// 						isMergePrint,
	// 						tempLateInfo,
	// 					}
	// 				});
	// 			},
	// 			onCancel: () => {
	// 				sendPoint(Pointer.取消打印);
	// 				reject();
	// 				window.errorCollection?.customMessageUpload({
	// 					type: `明文收件地址不支持抖音电子面单取号，建议使用菜鸟等其他平台电子面单打印【 取消打印 】`,
	// 					data: {
	// 						notSupportedFxgHandOrders,
	// 						isMergePrint,
	// 						tempLateInfo,
	// 					}
	// 				});
	// 			}
	// 		});
	// 	});
	// }

	if (notSupportedOrders.length > 0) {
		return new Promise((resolve, reject) => {
			Modal.confirm({
				centered: true,
				title: '系统提示',
				content: (
					<div className="r-c-warning" style={ { maxHeight: 600, overflowY: 'auto' } }>
						<p>【{tempLateInfo.ExcodeName || tempLateInfo.groupName}】与以下订单所属平台不一致，请选择订单对应平台的模板打印。</p>
						<p>订单号：{notSupportedOrders.map(i => `${i.togetherId}${i.hlPlatformType ? `(${HL_LABEL_PLAT[i.hlPlatformType]})` : ''}`).join(',')}</p>
					</div>
				),
				okText: '跳过，继续打印',
				cancelText: '取消',
				onOk: () => {
					sendPoint(Pointer.继续打印);
					let cancelChoiceOrders: Array<string> = [];
					notSupportedOrders.forEach(item => {
						item.togetherId && cancelChoiceOrders.push(item.togetherId);
					});

					handleChoiceChange({
						type: TradeChoiceType.取消勾选指定订单,
						cancelChoiceOrders
					});

					resolve(orderList.filter(order => !cancelChoiceOrders.includes(order.togetherId)));
					window.errorCollection?.customMessageUpload({
						type: `快递模板与订单所属平台不一致，请选择订单对应平台的模板打印【 跳过，继续打印 】`,
						data: {
							notSupportedOrders,
							isMergePrint,
							tempLateInfo,
						}
					});
				},
				onCancel: () => {
					sendPoint(Pointer.取消打印);
					reject();
					window.errorCollection?.customMessageUpload({
						type: `快递模板与订单所属平台不一致，请选择订单对应平台的模板打印【 取消打印 】`,
						data: {
							notSupportedOrders,
							isMergePrint,
							tempLateInfo,
						}
					});
				}
			});
		});
	}

	if ((userSetting?.openAbnormalCheck === 1 || orderList.length === 1) && unPromiseLogisticsInfo.num > 0) {
		return new Promise((resolve, reject) => {
			Modal.confirm({
				centered: true,
				title: '提示',
				content: (
					<div style={ { maxHeight: 600, overflowY: 'auto' } }>
						【{unPromiseLogisticsInfo.togetherIds.join(',')}】订单<span style={ { color: '#ff3333' } }>所选快递与指定快递不符</span>，请确认是否继续打印
					</div>
				),
				okText: '继续打印',
				cancelText: '取消',
				onOk: () => {
					resolve(orderList);

					window.errorCollection?.customMessageUpload({
						type: `所选快递与指定快递检验: 所选快递与指定快递不符【 继续打印 】`,
						data: {
							isMergePrint,
							tempLateInfo,
							unPromiseLogisticsInfo,
							openAbnormalCheck: userSetting?.openAbnormalCheck,
							orderList
						}
					});
				},
				onCancel: () => {
					reject();

					window.errorCollection?.customMessageUpload({
						type: `所选快递与指定快递检验: 所选快递与指定快递不符【 取消打印 】`,
						data: {
							isMergePrint,
							tempLateInfo,
							unPromiseLogisticsInfo,
							openAbnormalCheck: userSetting?.openAbnormalCheck,
							orderList
						}
					});
				}
			});
		});
	}

	return Promise.resolve(orderList);
};

// 打印前校验选择模板与得物订单匹配是否匹配
const checkAbnormalDeWu = async(tempLateInfo, notSupportedDeWu) => {
	return new Promise((resolve, reject) => {
		Modal.confirm({
			centered: true,
			title: '系统提示',
			content: (
				<div className="r-c-warning" style={ { maxHeight: 600, overflowY: 'auto' } }>
					<p>【{tempLateInfo.ExcodeName || tempLateInfo.groupName}】不支持以下得物品牌直发订单处理，请选择【得物通用模板】使用官方电子面单打印</p>
					<p>订单号：{notSupportedDeWu.map(i => `${i.togetherId}${i.hlPlatformType ? `(${HL_LABEL_PLAT[i.hlPlatformType]})` : ''}`).join(',')}</p>
				</div>
			),
			okText: '跳过，继续打印',
			cancelText: '取消',
			onOk: () => {
				resolve(true);
				window.errorCollection?.customMessageUpload({
					type: `得物普通履约、多仓发货标签模板不匹配【 跳过，继续打印 】`,
					data: {
						notSupportedDeWu: notSupportedDeWu?.map(pack => pack.togetherId),
					}
				});
			},
			onCancel: () => {
				reject();
				window.errorCollection?.customMessageUpload({
					type: `得物普通履约、多仓发货标签模板不匹配【 取消打印 】`,
					data: {
						notSupportedDeWu: notSupportedDeWu?.map(pack => pack.togetherId),
					}
				});
			}
		});
	});
};

export const checkDeleteHandOrder = async(orderList: IPackage[]) => {
	return new Promise((resolve, reject) => {
		const { handleChoiceChange } = tradeStore.tradeListStore;
		let handPrintList = [];
		let otherPlatformList = [];
		let canDeleteList = [];
		let waveOrderList = []; // 新增：存储含有波次的订单
		let canDeleteOrderList = [];
		console.log(orderList, 'orderList');
		orderList.forEach((pack) => {
			let isHandPrinted = ['hand', 'exchange'].includes(pack?.source?.toLocaleLowerCase()) && pack?.waybillPrintStatus !== 'none';
			let isOther = !['hand', 'exchange'].includes(pack?.source?.toLocaleLowerCase());
			let hasWave = Array.isArray(pack.waveNoList) && pack.waveNoList.length > 0; // 检查是否含有波次
			if (hasWave) {
				waveOrderList.push(pack.togetherId);
			}
			if (isHandPrinted) {
				handPrintList.push(pack.togetherId);
			}
			if (isOther) {
				otherPlatformList.push(pack.togetherId);
			}
			if (!isHandPrinted && !isOther) {
				canDeleteList.push(pack.togetherId);
				canDeleteOrderList.push(pack);
			}
		});
		 // 如果是含波次的订单，直接拒绝
		 if (waveOrderList.length > 0) {
			Modal.confirm({
				title: "系统提示",
				content: "含有波次的订单不允许删除",
				okText: "我知道了",
				cancelButtonProps: { hidden: true },
				closable: true,
				centered: true,
			});
			reject();
			return;
		}


		if (handPrintList.length && handPrintList.length == orderList.length) {
			Modal.confirm({
				title: "系统提示",
				content: "已打印手工订单不可删除",
				okText: "我知道了",
				cancelButtonProps: { hidden: true },
				closable: true,
				centered: true,
			});
			reject();
			return;
		}
		if (otherPlatformList.length && otherPlatformList.length == orderList.length) {
			Modal.confirm({
				title: "系统提示",
				content: "平台订单不可删除",
				okText: "我知道了",
				cancelButtonProps: { hidden: true },
				closable: true,
				centered: true,
				onOk: () => {

				},
				onCancel: () => {

				}
			});
			reject();
			return;
		}

		if (canDeleteList.length && canDeleteList.length < orderList.length) {
			Modal.confirm({
				title: "系统提示",
				content: "勾选含平台订单或已打印手工单，可跳过后继续删除",
				okText: "跳过继续删除",
				cancelText: '取消',
				closable: true,
				centered: true,
				onOk: () => {
					let cancelChoiceOrders = orderList.filter((pack: IPackage) => {
						return !canDeleteList.includes(pack.togetherId);
					}).map(pack => pack.togetherId);

					handleChoiceChange({
						type: TradeChoiceType.取消勾选指定订单,
						cancelChoiceOrders
					});
					resolve(canDeleteOrderList);
				},
				onCancel: () => {
					reject();
				}
			});
			return;
		}
		resolve(orderList);
	});
};
