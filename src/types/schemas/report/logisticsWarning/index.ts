/**
 * ResponseBody<LogisticsUserConfigRes> :ResponseBody
 */
export interface TradeLogisticsGetUserConfigResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * LogisticsUserConfigRes
     */
    data?: {
        id?: number;
        userId?: string;
        userNick?: string;
        ptType?: string;
        useType?: number;
        onOff?: number;
        onOffTime?: string;
        sendWarnHour?: number;
        receiveWarnHour?: number;
        stopWarnHour?: number;
        deliveringWarnHour?: number;
        signWarnDay?: number;
        returnWarn?: number;
        lastSyncEndTime?: string;
        warnRedirectUri?: string;
        appSecret?: string;
        appKey?: string;
        curCount?: number;
        logisticsOrderLimit?: number;
        [k: string]: any;
    };
    [k: string]: any;
}

/**
 * LogisticsUserConfigRequest :LogisticsUserConfigRequest
 */
export interface TradeLogisticsSaveUserConfigRequest {
    /**
     * 开关0：关闭，1：开启
     */
    onOff?: number;
    /**
     * 发货未揽收
     */
    sendWarnHour?: number;
    /**
     * 揽件未更新
     */
    receiveWarnHour?: number;
    /**
     * 中转未更新
     */
    stopWarnHour?: number;
    /**
     * 派件未更新
     */
    deliveringWarnHour?: number;
    /**
     * 超时未签收
     */
    signWarnDay?: number;
    [k: string]: any;
}

/**
* ResponseBody<Object> :ResponseBody
*/
export interface TradeLogisticsSaveUserConfigResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * Object
     */
    data?: {
        [k: string]: any;
    };
    [k: string]: any;
}
/**
 * LogisticsInfoRequest :LogisticsInfoRequest
 */
export interface TradeLogisticsExportRequest {
    userId?: number;
    /**
     * id搜索，多个以逗号隔开
     */
    id?: string;
    /**
     * 订单编号，多个以逗号隔开
     */
    tid?: string;
    /**
     * 运单编号，多个以逗号隔开
     */
    ydNo?: string;
    /**
     * 模版id，多个以逗号隔开
     */
    exId?: string | number;
    /**
     * 包裹状态,1:待揽件,2:已揽件，无物流,3:有物流，未签收,4:已签收,5：派件中,6.退回件
     */
    expressPackageStatus?: number;
    logisticsType?: number;
    /**
     * NORMAL(1,"正常"),,WARN(2,"预警"),,ABNORMAL(3,"异常")
     */
    abnormalStatus?: number;
    sendTimeStart?: string;
    sendTimeEnd?: string;
    /**
     * 店铺信息 ,MultiShopDTO
     */
    multiShopS?: {
      sellerId?: number;
      platform?: string;
      [k: string]: any;
    }[];
    /**
     * 快递公司
     */
    kdCode?: string;
    pageSize?: number;
    pageNo?: number;
    [k: string]: any;
  }
  /**
 * ResponseBody<Void> :ResponseBody
 */
export interface TradeLogisticsExportResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * Void
     */
    data?: {
      [k: string]: any;
    };
    [k: string]: any;
  }
  

/**
 * LogisticsInfoRequest :LogisticsInfoRequest
 */
export interface TradeLogisticsGetLogisticsListRequest {
    userId?: number;
    /**
     * id搜索，多个以逗号隔开
     */
    id?: string;
    /**
     * 订单编号，多个以逗号隔开
     */
    tid?: string;
    /**
     * 运单编号，多个以逗号隔开
     */
    ydNo?: string;
    /**
     * 模版id
     */
    exId?: number | string;
    /**
     * 包裹状态,1:待揽件,2:已揽件，无物流,3:有物流，未签收,4:已签收
     */
    expressPackageStatus?: number;
    logisticsType?: number|string|null;
    /**
     * NORMAL(1,"正常"),,WARN(2,"预警"),,ABNORMAL(3,"异常")
     */
    abnormalStatus?: number;
    sendTimeStart?: string;
    sendTimeEnd?: string;
    /**
     * 快递公司
     */
    kdCode?: string;
    pageSize?: number;
    pageNo?: number;
    kdCodeList?: string[];
    kdNameList?: string[];
    [k: string]: any;
}


/**
 * ResponseBody<PageInfo<LogisticsInfoResponse>> :ResponseBody
 */
export interface TradeLogisticsGetLogisticsListResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * PageInfo
     */
    data?: {
        pageNum?: number;
        pageSize?: number;
        size?: number;
        startRow?: number;
        endRow?: number;
        pages?: number;
        prePage?: number;
        nextPage?: number;
        isFirstPage?: boolean;
        isLastPage?: boolean;
        hasPreviousPage?: boolean;
        hasNextPage?: boolean;
        navigatePages?: number;
        /**
         * :
         */
        navigatepageNums?: {
            [k: string]: any;
        }[];
        navigateFirstPage?: number;
        navigateLastPage?: number;
        total?: number;
        /**
         * T
         */
        list?: {
            /**
             * 自增主键
             */
            id?: number;
            /**
             * 用户id
             */
            userId?: string;
            /**
             * 用户类型
             */
            ptType?: string;
            /**
             * 店铺类型
             */
            shopType?: string;
            /**
             * 店铺名称
             */
            shopName?: string;
            /**
             * 订单编号
             */
            tid?: string;
            /**
             * 运单号
             */
            ydNo?: string;
            /**
             * 快递公司code
             */
            kdCode?: string;
            /**
             * 用户自定义快递模板名称
             */
            exCodeName?: string;
            /**
             * 快递包裹状态1:待揽件，2:已揽件无物流，3.有物流未签收，4.已签收
             */
            expressPackageStatus?: number;
            /**
             * 快递包裹状态更新时间
             */
            expressPackageUpTime?: string;
            /**
             * 快递包裹状态持续时间
             */
            expressPackageStatusHour?: number;
            /**
             * 快递状态类型1:正常件2:发货3:揽件4:节点，默认1
             */
            logisticsType?: number;
            /**
             * 最新的物流信息时间
             */
            lastTime?: string;
            /**
             * 最新的物流详情描述
             */
            lastDesc?: string;
            /**
             * 发件时间
             */
            sendTime?: string;
            /**
             * 上次同步时间（在下次查询时重置）
             */
            lastSyncTime?: string;
            /**
             * 异常状态(1.正常，2.预警，3.异常)
             */
            abnormalStatus?: number;
            /**
             * 物流状态
             */
            logisticsYunStatusVal?: string;
            /**
             * 异常件持续时长
             */
            abnormalHour?: number;
            ignoreStatus?: number;
            [k: string]: any;
        }[];
        [k: string]: any;
    };
    [k: string]: any;
}


/**
 * LogisticsInfoRequest :LogisticsInfoRequest
 */
export interface TradeLogisticsGetLogisticsDetailRequest {
    /**
     * id搜索，多个以逗号隔开
     */
    id?: string;
    /**
     * 订单编号，多个以逗号隔开
     */
    tid?: string;
    /**
     * 运单编号，多个以逗号隔开
     */
    ydNo?: string;
    /**
     * 旺旺，多个以逗号隔开
     */
    buyerNick?: string;
    /**
     * 买家id,多个以逗号隔开
     */
    buyerOpenUid?: string;
    /**
     * 模版id
     */
    exId?: string;
    /**
     * WAIT_PICK(1,"待揽件"),默认,NO_LOGISTICS(2,"已揽件"),,WAIT_SIGN(3,"派件中"),,OVER_SIGN(4,"已签收"),,RETURN(5,"退回件"),,TRANSPORT(6,"运输中")
     */
    expressPackageStatus?: string;
    logisticsType?: string;
    /**
     * NORMAL(1,"正常"),,WARN(2,"预警"),,ABNORMAL(3,"异常")
     */
    abnormalStatus?: string;
    sendTimeStart?: string;
    sendTimeEnd?: string;
    /**
     * 快递公司
     */
    kdCode?: string;
    pageSize?: number;
    pageNo?: number;
    [k: string]: any;
}


/**
* ResponseBody<LogisticsDetailResponseVO> :ResponseBody
*/
export interface TradeLogisticsGetLogisticsDetailResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * LogisticsDetailResponseVO
     */
    data?: {
        /**
         * 快递单号
         */
        ydNo?: string;
        /**
         * 快递公司code
         */
        kdCode?: string;
        /**
         * 快递公司名称
         */
        companyName?: string;
        /**
         * 物流状态
         */
        logisticsStatus?: string;
        /**
         * 物流主状态
         */
        logisticsStatusDesc?: string;
        /**
         * 节点状态 ,LogisticsTraceDetail
         */
        logisticsTraceDetailList?: {
            /**
             * 节点描述
             */
            action?: string;
            /**
             * 状态描述
             */
            statusDesc?: string;
            /**
             * 时间
             */
            statusTime?: string;
            [k: string]: any;
        }[];
        [k: string]: any;
    };
    [k: string]: any;
}

/**
* LogisticsInfoRequest :LogisticsInfoRequest
*/
export interface TradeLogisticsManualSyncLogisticsInfoRequest {
    /**
     * id搜索，多个以逗号隔开
     */
    id?: string;
    /**
     * 订单编号，多个以逗号隔开
     */
    tid?: string;
    /**
     * 运单编号，多个以逗号隔开
     */
    ydNo?: string;
    /**
     * 旺旺，多个以逗号隔开
     */
    buyerNick?: string;
    /**
     * 买家id,多个以逗号隔开
     */
    buyerOpenUid?: string;
    /**
     * 模版id
     */
    exId?: string;
    /**
     * WAIT_PICK(1,"待揽件"),默认,NO_LOGISTICS(2,"已揽件"),,WAIT_SIGN(3,"派件中"),,OVER_SIGN(4,"已签收"),,RETURN(5,"退回件"),,TRANSPORT(6,"运输中")
     */
    expressPackageStatus?: string;
    logisticsType?: string;
    /**
     * NORMAL(1,"正常"),,WARN(2,"预警"),,ABNORMAL(3,"异常")
     */
    abnormalStatus?: string;
    sendTimeStart?: string;
    sendTimeEnd?: string;
    /**
     * 快递公司
     */
    kdCode?: string;
    pageSize?: number;
    pageNo?: number;
    [k: string]: any;
}

/**
* ResponseBody<Object> :ResponseBody
*/
export interface TradeLogisticsManualSyncLogisticsInfoResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * Object
     */
    data?: {
        [k: string]: any;
    };
    [k: string]: any;
}

/**
 * List<BatchUpdateDealStatusRequest>
 */
export type TradeLogisticsBatchUpdateDealStatusRequest = {
  /**
   * 物流预警Id
   */
  id?: number;
  /**
   * 处理状态，待处理:0、处理中:1、已处理:2
   */
  dealStatus?: number;
  [k: string]: any;
}[];

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface TradeLogisticsBatchUpdateDealStatusResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * Void
   */
  data?: {
    [k: string]: any;
  };
  [k: string]: any;
}

/**
 * LogisticsInfoRequest :LogisticsInfoRequest
 */
export interface TradeLogisticsQueryLogisticsInfoCountRequest {
  userId?: number;
  getAllIds?: boolean;
  /**
   * id搜索，多个以逗号隔开
   */
  id?: string;
  /**
   * 订单编号，多个以逗号隔开
   */
  tid?: string;
  /**
   * 运单编号，多个以逗号隔开
   */
  ydNo?: string;
  /**
   * 模版id，多个以逗号隔开
   */
  exId?: string;
  /**
   * 包裹状态,1:待揽件,2:已揽件，无物流,3:有物流，未签收,4:已签收,5：派件中,6.退回件
   */
  expressPackageStatus?: number;
  logisticsType?: number;
  /**
   * NORMAL(1,"正常"),,WARN(2,"预警"),,ABNORMAL(3,"异常")
   */
  abnormalStatus?: number;
  sendTimeStart?: string;
  sendTimeEnd?: string;
  /**
   * 店铺信息 ,MultiShopDTO
   */
  multiShopS?: {
    sellerId?: number;
    platform?: string;
    [k: string]: any;
  }[];
  /**
   * 处理状态，待处理:0、处理中:1、已处理:2
   */
  dealStatus?: number;
  /**
   * 快递公司
   */
  kdCode?: string;
  pageSize?: number;
  pageNo?: number;
  [k: string]: any;
}

/**
 * ResponseBody<List<LogisticsInfoCountResponse>> :ResponseBody
 */
export interface TradeLogisticsQueryLogisticsInfoCountResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * LogisticsInfoCountResponse
   */
  data?: {
    /**
     * 用户id
     */
    userId?: string;
    /**
     * 6-12h
     */
    sixToTwelve?: number;
    /**
     * 12-18h
     */
    twelveToEighteen?: number;
    /**
     * 12-24h
     */
    twelveToTwentyFour?: number;
    /**
     * 超过24h
     */
    moreTwentyFour?: number;
    /**
     * 18-24h
     */
    eighteenToTwentyFour?: number;
    /**
     * 24-36h
     */
    twentyFourToThirtySix?: number;
    /**
     * 超过36h
     */
    moreThirtySix?: number;
    /**
     * 拒收类统计
     */
    refuseSignCount?: number;
    /**
     * 拒收类统计
     */
    returnCount?: number;
    /**
     * 拒收类统计
     */
    issueCount?: number;
    /**
     * 拒收类统计
     */
    retentionCount?: number;
    /**
     * 物流状态,RECEIVE("接单中"),,WAIT_ACCEPT("待揽件"),,ACCEPT("已揽件"),,TRANSPORT("运输中"),,DELIVERING("派件中"),,SIGN("已签收"),,FAILED("包裹异常"),,AGENT_SIGN("已代收")
     */
    logisticsYunStatusVal?: string;
    [k: string]: any;
  }[];
  [k: string]: any;
}

/**
 * ReqCacheRequest :ReqCacheRequest
 */
export interface TradeLogisticsQueryLogisticsSyncTimeRequest {
  /**
   * 缓存key
   */
  cacheKey: string;
  [k: string]: any;
}

/**
 * ResponseBody<LocalDateTime> :ResponseBody
 */
export interface TradeLogisticsQueryLogisticsSyncTimeResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * data
   */
  data?: string;
  [k: string]: any;
}

