import React, { useState, useCallback, useEffect, useMemo, useRef } from 'react';
import { Modal, Button, Tag, Empty } from 'antd';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { observer } from 'mobx-react';
import cs from 'classnames';
import { useRequest } from 'ahooks';
import BaseTable from '@/components/SearchTable/BaseTable';
import styles from './index.module.scss';
import PlatformIcon from '@/pages/Warehouse/StockSync/HandStockSync/components/PlatformIcon';
import history from "@/utils/history";
import event from '@/libs/event';
import userStore from "@/stores/user";
import { getAllShopMap } from '@/components-biz/ShopListSelect/shopListUtils';
import Icon from '@/components/Icon';
import message from '@/components/message';
import { TradeLivePrintIntervalFakerTradeToSeeApi, TradeLivePrintSendFakerTradeToSeeApi } from '@/apis/trade/live';

interface LivePrintModalProps {
  visible: boolean;
  currentLiveInfo: any;
  statusCount: any;
  remainTime: string;
  onClose: () => void;
  // 通过props传入数据和方法
  liveOrderList: any[];
  onClearOrderList: () => void;
  isLiving?: boolean;
  formData?: any;
}

const LivePrintModal: React.FC<LivePrintModalProps> = ({
	visible,
	currentLiveInfo,
	statusCount,
	remainTime,
	onClose,
	liveOrderList, // 从props接收
	onClearOrderList, // 从props接收
	isLiving = false,
	formData,
}) => {
	const [shopListExpanded, setShopListExpanded] = useState(false); // 店铺列表展开状态
	const [shopMap, setShopMap] = useState<any>({}); // 店铺信息映射
	const [isShowMoreShops, setIsShowMoreShops] = useState(false); // 是否显示展开收起按钮
	const containerRef = useRef(null);
	const lastItemRef = useRef(null);

	// 根据配置创建轮询
	const shouldStartPolling = useMemo(() => {
		return visible 
			   && userStore.fakerTradeConfig?.intervalSec 
			   && formData?.isOpenFakerTrade;
	}, [visible, userStore.fakerTradeConfig?.intervalSec, formData?.isOpenFakerTrade]);

	// 使用ahooks的useRequest进行轮询
	const { run: startPolling, cancel: stopPolling } = useRequest(
		TradeLivePrintIntervalFakerTradeToSeeApi,
		{
			manual: true,
			pollingInterval: shouldStartPolling ? userStore.fakerTradeConfig?.intervalSec * 1000 : 0,
			pollingWhenHidden: true, // 页面隐藏时停止轮询
			onSuccess: (res) => {
				console.log('%c [ 自动生成虚拟订单 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', res);
				// message.success('生成虚拟订单成功');
			},
			onError: (error) => {
				console.error('轮询接口调用失败:', error);
			}
		}
	);

	// 监听轮询条件变化
	useEffect(() => {
		if (!isLiving) {
			if (shouldStartPolling) {
				console.log('开始轮询，间隔时间:', userStore.fakerTradeConfig?.intervalSec, '秒');
				// 延迟轮询
				const timer = setTimeout(() => {
					startPolling({
						liveTradePrintTaskId: currentLiveInfo?.id,
					});
				}, (userStore.fakerTradeConfig?.intervalSec || 30) * 1000); // 等待一个轮询间隔后再开始
				
				// 返回清理函数，组件卸载或条件变化时清除定时器
				return () => {
					clearTimeout(timer);
				};
			} else {
				console.log('停止轮询');
				stopPolling();
			}
		}
	}, [shouldStartPolling, isLiving]);

	useEffect(() => {
		return () => {
			stopPolling();
		};
	}, []);

	// 获取店铺信息映射
	useEffect(() => {
		const fetchShopMap = async() => {
			try {
				const map = await getAllShopMap({ hasHand: false });
				setShopMap(map);
			} catch (error) {
				console.error('获取店铺信息失败:', error);
			}
		};
		
		if (visible) {
			fetchShopMap();
		}
	}, [visible]);

	// 监听店铺列表变化，判断是否需要显示展开收起按钮
	useEffect(() => {
		const checkIfNeedExpandCollapse = () => {
			if (!containerRef.current || !lastItemRef.current || !currentLiveInfo?.shopList?.length) {
				setIsShowMoreShops(false);
				return;
			}
			
			const containerRect = containerRef.current.getBoundingClientRect();
			const lastItemRect = lastItemRef.current.getBoundingClientRect();
			
			// 判断最后一个元素是否在第一行
			// 如果最后一个元素的top位置大于容器的top位置，说明不在第一行，需要展开收起功能
			const isMultiLine = lastItemRect.top > containerRect.top + 5; // 5px的容错值
			setIsShowMoreShops(isMultiLine);
		};

		if (visible && currentLiveInfo?.shopList?.length > 0) {
			// 延迟执行，确保DOM已渲染完成和shopMap已加载
			const timer = setTimeout(() => {
				checkIfNeedExpandCollapse();
			}, 200); // 增加延迟时间，确保shopMap加载完成
			
			// 监听窗口大小变化
			window.addEventListener('resize', checkIfNeedExpandCollapse);
			
			return () => {
				clearTimeout(timer);
				window.removeEventListener('resize', checkIfNeedExpandCollapse);
			};
		}
	}, [visible, currentLiveInfo?.shopList, Object.keys(shopMap).length]); // 添加shopMap.length作为依赖，确保shopMap加载完成后重新检查

	// 当弹框关闭时清空订单列表
	useEffect(() => {
		if (!visible) {
			onClearOrderList?.(); // 使用props传入的方法
			setShopListExpanded(false);
			stopPolling(); // 弹框关闭时停止轮询
		}
	}, [visible]);

	// 生成虚拟订单
	const handleGenerateFakerTrade = async() => {
		try {
			const res = await TradeLivePrintSendFakerTradeToSeeApi({
				liveTradePrintTaskId: currentLiveInfo?.id,
			});
			// message.success('生成虚拟订单成功');
			
		} catch (error) {
			console.error('生成虚拟订单失败:', error);
		}
	};

	// 定义订单列表的表格列
	const liveOrderColumns = useMemo(() => [
		{
			title: '买家昵称',
			dataIndex: 'buyerNick',
			key: 'buyerNick',
			width: 92,
			render: (text: string) => text || '-',
		},
		{
			title: '达人信息',
			dataIndex: 'authorName',
			key: 'authorName',
			width: 140,
			render: (text: string, row:any) => {
				return (
					<div className="r-flex r-fw-w">
						{
							row.authorIdList?.map((item:any, index:number) => {
								const authorName = row.authorNameList?.[index] || '-';
								const authorId = item;
								return (
									<div key={ authorId }>
										<span>{authorName || '-'}</span>
										{
											authorId && (
												<span className="r-c-black65 r-ml-4">({authorId})</span>
											)
										}
										{index !== row.authorIdList.length - 1 ? '，' : ''}
									</div>
								);
							})
						}
					</div>
				);
			},
		},
		{
			title: '店铺',
			dataIndex: 'sellerNick',
			key: 'sellerNick',
			width: 160,
			render: (value:string, row:any) => {
				return (
					<div className="r-flex r-js-c r-ai-c">
						<PlatformIcon platform={ row.platform } />
						<span className="r-c-black85 r-mr-4">{value}</span>
					</div>
				);
			}
		},
		{
			title: '订单编号',
			dataIndex: 'ptTid',
			key: 'ptTid',
			width: 160,
			render: (value:string) => {
				const tidAction = () => {
					history.push('/trade/index');
					setTimeout(() => {
						event.emit('tradeSetting.handleSearchParams', { ptTid: value });
					}, 0);
				};
				return (
					<>
						<span className="r-click" onClick={ tidAction }>{value}</span>
					</>
				);
			}
		},
		{
			title: '付款时间',
			dataIndex: 'payTime',
			key: 'payTime',
			width: 160,
			render: (text: any) => text || '-',
		}
		// {
		// 	title: '打印状态',
		// 	dataIndex: 'livePrintStatus',
		// 	key: 'livePrintStatus',
		// 	width: 100,
		// 	render: (status: number, record: any) => {
		// 		const statusMap = {
		// 			0: { text: '未打印', color: 'default' },
		// 			1: { text: '已打印', color: 'success' },
		// 			2: { text: '不符合', color: 'warning' },
		// 			3: { text: '异常', color: 'error' },
		// 		};
		// 		const statusInfo = statusMap[status] || { text: '未知', color: 'default' };
		// 		return (
		// 			<div>
		// 				<Tag color={ statusInfo.color }>{statusInfo.text}</Tag>
		// 				{status === 3 && record.errorMsg && (
		// 					<div className="r-text-xs r-text-red-500 r-mt-10" title={ record.errorMsg }>
		// 						{record.errorMsg.length > 10 ? `${record.errorMsg.slice(0, 10)}...` : record.errorMsg}
		// 					</div>
		// 				)}
		// 			</div>
		// 		);
		// 	},
		// },
	], []);

	// 渲染店铺信息
	const renderShopInfo = () => {
		const shopList = currentLiveInfo.shopList || [];
		// 通过比较总店铺数量和当前选中店铺数量来判断是否为全部店铺
		const isAllShops = Object.keys(shopMap).length > 0 && Object.keys(shopMap).length === shopList.length;
		
		// 增强店铺信息，添加shopName
		const enhancedShopList = shopList.map(shop => {
			const shopKey = `${shop.platform.toLowerCase()}_${shop.sellerId}`;
			const shopInfo = shopMap[shopKey];
			return {
				...shop,
				shopName: shopInfo?.sellerNick || shop.sellerId, 
			};
		});

		return (
			<div>
				{
					isAllShops && (
						<div className="r-mb-8">全部店铺</div>
					)
				}
				<div className="r-flex r-ai-fs r-jc-fs">
					<div 
						className={ `r-flex r-flex-1 r-fw-w r-gap-4 ${styles['shop-list-container']} ${!shopListExpanded ? styles['shop-list-collapsed'] : ''}` }
						ref={ containerRef }
					>
						{enhancedShopList.map((shop, index) => (
							<div 
								className="r-flex r-ai-c r-mr-8" 
								key={ shop.sellerId }
								ref={ index === enhancedShopList.length - 1 ? lastItemRef : null }
							>
								<PlatformIcon platform={ shop.platform } fontSize={ 14 } />
								<span className="r-c-black85">{shop.shopName}</span>
							</div>
						))}
					</div>
					{isShowMoreShops && (
						<div 
							className="r-flex r-ai-c r-pointer r-flex-shrink"
							onClick={ () => setShopListExpanded(!shopListExpanded) }
						>
							<span className="r-mr-4" style={ { color: '#1890FF' } }>{shopListExpanded ? '收起' : '展开'}</span>
							<Icon size={ 13 } style={ { color: '#1890FF' } } type={ shopListExpanded ? 'shouqi' : 'zhankai' } />
						</div>
					)}
				</div>
			</div>
		);
	};

	// 在组件中添加渲染达人信息的方法
	const renderAuthorInfo = () => {
		const authorList = currentLiveInfo.authorList || [];
		
		if (!authorList || authorList.length === 0) {
			return '-';
		}
		
		return (
			<div className="r-flex r-fw-w">
				{authorList.map((author, index) => (
					<div key={ author.authorId } className="r-c-black85">
						<span>{author.authorName}</span>
						<span className="r-c-black85 r-ml-4">(ID：{author.authorId}){index !== authorList.length - 1 ? '，' : ''}</span>
					</div>
				))}
			</div>
		);
	};

	return (
		<Modal
			wrapClassName={ styles["live-print-modal-wrap"] }
			maskStyle={ { top: "100px", zIndex: 998 } }
			centered
			title="自动打印"
			visible={ visible }
			width="800px"
			closable
			destroyOnClose
			maskClosable={ false }
			keyboard={ false }
			okText="关闭自动打印"
			onOk={ onClose }
			onCancel={ onClose }
			cancelButtonProps={ {
				style: {
					display: 'none'
				}
			} }
			// bodyStyle={ { maxHeight: 'calc(100vh - 240px)' } }
			footer={ (
				<div className="r-flex r-jc-sb r-ai-c">
					<div className={ styles.tip }>实时打印直播自动打印时，不可以直接关闭当前页，关闭即停止打印</div>
					<Button type="primary" onClick={ onClose }>关闭自动打印</Button>
				</div>
			) }
		>
			<div>
				{/* 直播信息显示 */}
				<div className={ styles["live-info-wrap"] }>
					<div className="r-flex r-ai-c">
						<div className="r-flex-shrink">直播场次：</div>
						<div className="r-flex-1">{currentLiveInfo.liveNo || '-'}</div>
					</div>
						
					{/* 只在达人直播模式下显示主播达人 */}
					{currentLiveInfo.liveType === 2 && (
						<div className="r-flex">
							<div className="r-flex-shrink">主播达人：</div>
							<div className="r-flex-1">
								{renderAuthorInfo()}
							</div>
						</div>
					)}
						
					<div className="r-flex">
						<div className="r-flex-shrink">直播店铺：</div>
						<div className="r-flex-1">
							{renderShopInfo()}
						</div>
					</div>
				</div>


				{/* 时间信息 */}
				<div className="r-flex r-jc-sb r-mt-16">
					<div>
						打印开始时间：{statusCount.livePrintTimeStart}
					</div>
					<div>
						剩余时间：{remainTime}
					</div>
				</div>
					
				<div className="r-mt-16">
					<div className={ styles["live-print-title"] }>
						自动打印中...
					</div>
					{/* 统计信息 */}
					<div className={ styles["live-print-count-wrap"] }>
						<div className="r-flex r-fd-c r-ai-c">
							<div className={ styles["live-print-count-num"] }>
								{statusCount.alreadyPrintCount}
							</div>
							<div className={ styles["live-print-count-text"] }>标签已打印数量</div>
						</div>
						<div className="r-flex r-fd-c r-ai-c">
							<div className={ styles["live-print-count-num2"] }>
								{statusCount.filterTradeNum}
							</div>
							<div className={ styles["live-print-count-text"] }>过滤订单数量</div>
						</div>
						<div className="r-flex r-fd-c r-ai-c">
							<div className={ styles["live-print-count-num3"] }>
								{statusCount.noPrintCount}
							</div>
							<div className={ styles["live-print-count-text"] }>队列中未打印数量</div>
						</div>
					</div>
				</div>

				{
					(!isLiving && formData?.isOpenFakerTrade) && (
						<div className={ cs(styles['faker-trade-wrap'], 'r-flex r-jc-fe r-mt-16') }>
							<div 
								className={ styles['faker-trade-btn'] }
								onClick={ handleGenerateFakerTrade }
							>
								生成虚拟订单
							</div>
						</div>
					)
				}

				{/* 直播订单列表 */}
				{
					!isLiving && (
						<div className="r-mt-8">
							<BaseTable
								size="small"
								bordered
								loading={ false }
								columns={ liveOrderColumns }
								dataSource={ liveOrderList }
								rowKey="tid"
								pagination={ false }
								scroll={ { y: 200 } } // 'calc(100vh - 680px)'
								locale={ {
									emptyText: (
										<div style={ { width: '100%', height: '120px', textAlign: 'center' } } className="r-flex r-fd-c r-jc-c r-ai-c">
											<Empty 
												image={ Empty.PRESENTED_IMAGE_SIMPLE }
												description={ <p>暂无订单数据</p> }
											/>
										</div>
									)
								} }
							/>
						</div>
					)
				}
			</div>
		</Modal>
	);
};

export default observer(LivePrintModal);
