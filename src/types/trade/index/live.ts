/**
 * ReqLiveAuthorPageQueryDTO :ReqLiveAuthorPageQueryDTO
 */
export interface TradeLiveAuthorQueryPageListRequest {
    /**
     * 时间类型,1.创建时间
     */
    timeType?: number;
    /**
     * 开始时间
     */
    startTime?: string;
    /**
     * 结束时间
     */
    endTime?: string;
    /**
     * 平台
     */
    platform?: string;
    /**
     * 达人名称
     */
    authorName?: string;
    /**
     * 达人id
     */
    authorId?: string;
    pageNo?: number;
    pageSize?: number;
    [k: string]: any;
  }

  
/**
 * ResponseBody<PageList<LiveAuthorRecordDTO>> :ResponseBody
 */
export interface TradeLiveAuthorQueryPageListResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * PageList
     */
    data?: {
      pageNo?: number;
      pageSize?: number;
      total?: number;
      /**
       * T
       */
      list?: {
        /**
         * @mbg.generated,id,表字段:id
         */
        id?: number;
        /**
         * @mbg.generated,用户ID,表字段:user_id
         */
        userId?: number;
        /**
         * @mbg.generated,平台,表字段:platform
         */
        platform?: string;
        /**
         * @mbg.generated,达人ID,表字段:author_id
         */
        authorId?: string;
        /**
         * @mbg.generated,达人名称,表字段:author_name
         */
        authorName?: string;
        /**
         * @mbg.generated,操作人,表字段:operator
         */
        operator?: string;
        /**
         * @mbg.generated,创建时间,表字段:gmt_created
         */
        gmtCreated?: string;
        /**
         * @mbg.generated,修改时间,表字段:gmt_modified
         */
        gmtModified?: string;
        /**
         * @mbg.generated,删除标识0已删除1未删除,表字段:enable_status
         */
        enableStatus?: number;
        [k: string]: any;
      }[];
      [k: string]: any;
    };
    [k: string]: any;
  }
  

/**
 * ReqLiveAuthorAddDTO :ReqLiveAuthorAddDTO
 */
export interface TradeLiveAuthorSaveRequest {
    /**
     * 添加方式,1:名称、id添加；,2:订单编号添加
     */
    addType?: number;
    /**
     * 达人ID
     */
    authorId?: string;
    /**
     * 达人名称
     */
    authorName?: string;
    /**
     * 平台
     */
    platform?: string;
    /**
     * 订单编号
     */
    ptTid?: string;
    [k: string]: any;
  }
  
/**
 * ResponseBody<Void> :ResponseBody
 */
export interface TradeLiveAuthorSaveResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * Void
     */
    data?: {
      [k: string]: any;
    };
    [k: string]: any;
  }
  

/**
 * ReqLiveAuthorDelDTO :ReqLiveAuthorDelDTO
 */
export interface TradeLiveAuthorDeleteRequest {
    /**
     * 记录ID
     */
    id?: number;
    [k: string]: any;
  }

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface TradeLiveAuthorDeleteResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * Void
     */
    data?: {
      [k: string]: any;
    };
    [k: string]: any;
  }
  

/**
 * ResponseBody<List<LiveAuthorRecordDTO>> :ResponseBody
 */
export interface TradeLivePrintGetAuthorListResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * LiveAuthorRecordDTO
   */
  data?: {
    /**
     * @mbg.generated,id,表字段:id
     */
    id?: number;
    /**
     * @mbg.generated,用户ID,表字段:user_id
     */
    userId?: number;
    /**
     * @mbg.generated,平台,表字段:platform
     */
    platform?: string;
    /**
     * @mbg.generated,达人ID,表字段:author_id
     */
    authorId?: string;
    /**
     * @mbg.generated,达人名称,表字段:author_name
     */
    authorName?: string;
    /**
     * @mbg.generated,操作人,表字段:operator
     */
    operator?: string;
    /**
     * @mbg.generated,直播状态0未直播1正在直播,表字段:live_status
     */
    liveStatus?: number;
    /**
     * @mbg.generated,直播打印任务ID，live_status=1情况下有值,表字段:live_trade_print_task_id
     */
    liveTradePrintTaskId?: number;
    /**
     * @mbg.generated,创建时间,表字段:gmt_created
     */
    gmtCreated?: string;
    /**
     * @mbg.generated,修改时间,表字段:gmt_modified
     */
    gmtModified?: string;
    /**
     * @mbg.generated,删除标识0已删除1未删除,表字段:enable_status
     */
    enableStatus?: number;
    [k: string]: any;
  }[];
  [k: string]: any;
}

export interface TradeLivePrintGetAuthorListRequest {
    [k: string]: any;
  }
export interface TradeLivePrintGetLiveShopListRequest {
    [k: string]: any;
  }

/**
 * ResponseBody<List<PlatformShopInfo>> :ResponseBody
 */
export interface TradeLivePrintGetLiveShopListResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * PlatformShopInfo
   */
  data?: {
    /**
     * 用户ID
     */
    userId?: number;
    /**
     * 平台类型
     */
    platform?: string;
    /**
     * 店铺ID
     */
    sellerId?: string;
    /**
     * 店铺名称
     */
    sellerNick?: string;
    [k: string]: any;
  }[];
  [k: string]: any;
}
/**
 * ReqLiveTradeSendFakerTradeDTO :ReqLiveTradeSendFakerTradeDTO
 */
export interface TradeLivePrintIntervalFakerTradeToSeeRequest {
  /**
   * 直播任务id
   */
  liveTradePrintTaskId?: number;
  /**
   * 跳过轮询检查
   */
  skipIntervalCheck?: boolean;
  [k: string]: any;
}
/**
 * ResponseBody<Void> :ResponseBody
 */
export interface TradeLivePrintIntervalFakerTradeToSeeResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * Void
   */
  data?: {
    [k: string]: any;
  };
  [k: string]: any;
}
/**
 * ReqLiveTradeSendFakerTradeDTO :ReqLiveTradeSendFakerTradeDTO
 */
export interface TradeLivePrintSendFakerTradeToSeeRequest {
  /**
   * 直播任务id
   */
  liveTradePrintTaskId?: number;
  /**
   * 跳过轮询检查
   */
  skipIntervalCheck?: boolean;
  [k: string]: any;
}
/**
 * ResponseBody<Void> :ResponseBody
 */
export interface TradeLivePrintSendFakerTradeToSeeResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * Void
   */
  data?: {
    [k: string]: any;
  };
  [k: string]: any;
}
