
// 平台&店铺、订单来源、时间范围、订单状态、打印状态、销售属性、订单缺货状态、商品包含、商品不包含、留言备注、退款状态、
// 省市区、买家昵称、订单编号、快递单号、供应商包含、供应商不包含、剩余发货时间、订单标记 、订单类型、订单标签、订单异常、

import { TradeSearchConditionConfig } from "@/types/trade/search/search";

// 商品种类、商品数量、订单金额、订单重量、达人、分销商
export const enum ConditionIdEnum {
	"平台&店铺" = 1,
    订单来源 = 2,
    时间范围 = 3,
    订单状态 = 4,
    打印状态 = 5,
    销售属性 = 6,
    缺货状态 =7,
    商品包含 = 8,
    商品不包含 = 9,
    留言备注 = 10,
    退款状态 = 11,
    省市区= 12,
    买家昵称=13,
    订单编号=14,
    快递单号 = 15,
    供应商包含 = 16,
    供应商不包含 =17,
    剩余发货时间= 18,
    订单标记= 19,
    订单类型=20,
    订单标签=21,
    订单异常=22,
    商品种类=23,
    商品数量=24,
    订单金额=25,
    订单重量=26,
    "达人名称/ID"=27,
    系统单号=28,
	发货仓库 = 29,
	有无设置简称 = 30,
	有无设置规格别名 = 31,
	有无设置重量 = 32,
	有无设置成本价 = 33,
}

export const searchConditionList:TradeSearchConditionConfig[] = [
	{
		disabled: true,
		"condition": "platform",
		"conditionName": "平台&店铺",
		"id": ConditionIdEnum["平台&店铺"],
		"selected": true,
		"sort": 1
	},
	{
		disabled: true,
		"condition": "orderSource",
		"conditionName": "订单来源",
		"id": ConditionIdEnum.订单来源,
		"selected": true,
		"sort": 2
	},
	{
		disabled: true,
		"condition": "timeType",
		"conditionName": "时间范围",
		"id": ConditionIdEnum.时间范围,
		"selected": true,
		"sort": 3
	},
	{
		disabled: true,
		"condition": "status",
		"conditionName": "订单状态",
		"id": ConditionIdEnum.订单状态,
		"selected": true,
		"sort": 4
	},
	{
		"condition": "printStatus",
		"conditionName": "打印状态",
		"id": ConditionIdEnum.打印状态,
		"selected": false,
		"sort": 5
	},
	{
		"condition": "sellAttribute",
		"conditionName": "销售属性",
		"id": ConditionIdEnum.销售属性,
		"selected": true,
		"sort": 6
	},
	{
		"condition": "goodStockStatus",
		"conditionName": "缺货状态",
		"id": ConditionIdEnum.缺货状态,
		"selected": true,
		"sort": 7
	},
	{
		"condition": "itemInclude",
		"conditionName": "商品包含",
		"id": ConditionIdEnum.商品包含,
		"selected": true,
		"sort": 8
	},
	{
		"condition": "itemNotInclude",
		"conditionName": "商品不包含",
		"id": ConditionIdEnum.商品不包含,
		"selected": true,
		"sort": 9
	},
	{
		"condition": "flagValue",
		"conditionName": "留言备注",
		"id": ConditionIdEnum.留言备注,
		"selected": true,
		"sort": 10
	},
	{
		disabled: true,
		"condition": "refundStatus",
		"conditionName": "退款状态",
		"id": ConditionIdEnum.退款状态,
		"selected": true,
		"sort": 11
	},
	{
		"condition": "areaContain",
		"conditionName": "省市区",
		"id": ConditionIdEnum.省市区,
		"selected": false,
		"sort": 12
	},
	{
		"condition": "buyerNick",
		"conditionName": "买家昵称",
		"id": ConditionIdEnum.买家昵称,
		"selected": false,
		"sort": 13
	},
	{
		"condition": "ptTid",
		"conditionName": "订单编号",
		"id": ConditionIdEnum.订单编号,
		"selected": false,
		"sort": 14
	},
	{
		"condition": "sid",
		"conditionName": "快递单号",
		"id": ConditionIdEnum.快递单号,
		"selected": false,
		"sort": 15
	},
	{
		"condition": "supplierIncludingList",
		"conditionName": "供应商包含",
		"id": ConditionIdEnum.供应商包含,
		"selected": false,
		"sort": 16
	},
	{
		"condition": "supplierNotIncludingList",
		"conditionName": "供应商不包含",
		"id": ConditionIdEnum.供应商不包含,
		"selected": false,
		"sort": 17
	},
	{
		"condition": "residueSendTime",
		"conditionName": "剩余发货时间",
		"id": ConditionIdEnum.剩余发货时间,
		"selected": false,
		"sort": 18
	},
	{
		"condition": "bizMark",
		"conditionName": "订单标记",
		"id": ConditionIdEnum.订单标记,
		"selected": false,
		"sort": 19
	},
	{
		"condition": "tradeType",
		"conditionName": "订单类型",
		"id": ConditionIdEnum.订单类型,
		"selected": false,
		"sort": 20
	},
	{
		"condition": "quickQuery",
		"conditionName": "订单标签",
		"id": ConditionIdEnum.订单标签,
		"selected": false,
		"sort": 21
	},
	{
		"condition": "exceptionFlag",
		"conditionName": "订单异常",
		"id": ConditionIdEnum.订单异常,
		"selected": false,
		"sort": 22
	},
	{
		"condition": "goodsTypeNumRange",
		"conditionName": "商品种类",
		"id": ConditionIdEnum.商品种类,
		"selected": false,
		"sort": 23
	},
	{
		"condition": "goodsTotalNumRange",
		"conditionName": "商品数量",
		"id": ConditionIdEnum.商品数量,
		"selected": false,
		"sort": 24
	},
	{
		"condition": "paymentRange",
		"conditionName": "订单金额",
		"id": ConditionIdEnum.订单金额,
		"selected": false,
		"sort": 25
	},
	{
		"condition": "weightRange",
		"conditionName": "订单重量",
		"id": ConditionIdEnum.订单重量,
		"selected": false,
		"sort": 26
	},
	{
		"condition": "authorIncludingList",
		"conditionName": "达人名称/ID",
		"id": ConditionIdEnum["达人名称/ID"],
		"selected": false,
		"sort": 27
	},
	{
		"condition": "tid",
		"conditionName": "系统单号",
		"id": ConditionIdEnum.系统单号,
		"selected": false,
		"sort": 28
	},
	{
		"condition": "storageIdList",
		"conditionName": "发货仓库",
		"id": ConditionIdEnum.发货仓库,
		"selected": false,
		"sort": 29
	},
	{
		"condition": "isSetAlias",
		"conditionName": "有无设置简称",
		"id": ConditionIdEnum.有无设置简称,
		"selected": false,
		"sort": 30
	},
	{
		"condition": "isSetSkuAlias",
		"conditionName": "有无设置规格别名",
		"id": ConditionIdEnum.有无设置规格别名,
		"selected": false,
		"sort": 31
	},
	// {
	// 	"condition": "isSetWeight",
	// 	"conditionName": "有无设置重量",
	// 	"id": ConditionIdEnum.有无设置重量,
	// 	"selected": false,
	// 	"sort": 32
	// },
	{
		"condition": "isSetCostPrice",
		"conditionName": "有无设置成本价",
		"id": ConditionIdEnum.有无设置成本价,
		"selected": false,
		"sort": 33
	},
];

export enum supplierOptionsEnum {
	供应商包含 = "supplierContain",
	// 供应商不包含 = "supplierNotContain",
}

export enum supplierOptionsNotContainerEnum {
	// 供应商包含 = "supplierContain",
	供应商不包含 = "supplierNotContain",
}

export const isSetAliasOptions_bhd =[
	{ label: '全部', value: '' },
	{ label: '已设置简称', value: 0 },
	{ label: '未设置简称', value: 1 },
]

export const isSetSkuAliasOptions_bhd =[
	{ label: '全部', value: '' },
	{ label: '已设置规格别名', value: 0 },
	{ label: '未设置规格别名', value: 1 },
]

export const isSetWeightOptions_bhd =[
	{ label: '全部', value: '' },
	{ label: '已设置重量', value: 0 },
	{ label: '未设置重量', value: 1 },
]

export const isSetCostPriceOptions_bhd =[
	{ label: '全部', value: '' },
	{ label: '已设置成本价', value: 0 },
	{ label: '未设置成本价', value: 1 },
]
