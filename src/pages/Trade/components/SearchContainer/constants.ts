import { constant } from "lodash";
import Pointer from "@/utils/pointTrack/constants";
import {
	PLAT_ALI,
	PLAT_FXG,
	PLAT_HAND,
	PLAT_PDD,
	PLAT_TB,
	PLAT_JD,
	allFlagColorObj,
} from "@/constants";
import { tradeStore } from "@/stores";
import userStore from "@/stores/user";

export interface ISearchItem {
	key: string | number;
	value: string;
	searchName?: string;
	point?: string;
}
interface ISelectOption {
	default: any;
	options: ISearchItem[];
}

export const PLATFORM_OPTIONS: ISelectOption = {
	default: "",
	options: [
		{
			key: "",
			value: "所有平台",
		},
		{
			key: PLAT_TB,
			value: "淘宝/天猫",
		},
		{
			key: PLAT_PDD,
			value: "拼多多",
		},
		{
			key: PLAT_FXG,
			value: "抖店",
		},
		{
			key: PLAT_ALI,
			value: "1688",
		},
		{
			key: PLAT_JD,
			value: "京东",
		},
		{
			key: PLAT_HAND,
			value: "手工订单",
		},
	],
};

export const TIME_TYPE_OPTIONS: ISelectOption = {
	default: "1",
	options: [
		{
			key: "1",
			value: "下单时间",
		},
		{
			key: "2",
			value: "付款时间",
		},
		{
			key: "3",
			value: "打印时间",
		},
		{
			key: "4",
			value: "发货时间",
		},
	],
};

export const ORDER_STATUS_OPTIONS: ISelectOption = {
	default: "WAIT_SELLER_SEND_GOODS",
	options: [
		{
			key: "WAIT_BUYER_PAY",
			value: "待付款",
			searchName: "待付款订单",
		},
		{
			key: "WAIT_SELLER_SEND_GOODS",
			value: "待发货",
			searchName: "待发货订单",
		},
		{
			key: "WAIT_BUYER_CONFIRM_GOODS",
			value: "已发货",
			searchName: "已发货订单",
		},
		{
			key: "TRADE_FINISHED",
			value: "交易成功",
			searchName: "交易成功订单",
		},
		{
			key: "TRADE_CLOSED",
			value: "已关闭",
			searchName: "已关闭订单",
		},
		{
			key: "ALL_STATUS",
			value: "全部状态",
			searchName: "全部订单",
		},
	],
};

// 添加分组的订单状态选项
export const ORDER_STATUS_OPTIONS_GROUPED = {
	default: "WAIT_SELLER_SEND_GOODS",
	options: [
		{
			label: "订单状态",
			options: [
				{
					key: "WAIT_BUYER_PAY",
					value: "待付款",
					searchName: "待付款订单",
				},
				{
					key: "WAIT_SELLER_SEND_GOODS",
					value: "待发货",
					searchName: "待发货订单",
				},
				{
					key: "WAIT_BUYER_CONFIRM_GOODS",
					value: "已发货",
					searchName: "已发货订单",
				},
				{
					key: "TRADE_FINISHED",
					value: "交易成功",
					searchName: "交易成功订单",
				},
				{
					key: "TRADE_CLOSED",
					value: "已关闭",
					searchName: "已关闭订单",
				},
				{
					key: "ALL_STATUS",
					value: "全部状态",
					searchName: "全部订单",
				},
			],
		},
		{
			label: "快捷筛选",
			options: [
				{
					key: "WAIT_SEND_GOODS",
					value: "待发货+先发货",
					searchName: "待发货+先发货订单",
					tip: "支持待发货+先发货订单一起查询排序，方便进行快递单打印及拣货配货工作",
				},
			],
		},
	],
};

export const ORDER_TYPE_OPTIONS: ISelectOption = {
	default: "",
	options: [
		{
			key: "",
			value: "订单类型",
		},
		{
			key: "pt",
			value: "普通订单",
		},
		{
			key: "exchange_goods",
			value: "换货订单",
		},
		{
			key: "re_goods",
			value: "补发订单",
		},
	],
};

// 全部、已打印、未打印、仅快递单打印、仅发货单打印、均打印快递单、均打印发货单，默认显示打印状态，值为空
// 仅快递单未打印 仅发货单未打印 商品标签未打印 商品标签已打印 快递单发货单未打印 快递单发货单已打印 均未打印 均已打印
export const ORDER_PRINT_STATUS: ISelectOption = {
	default: "",
	/**
	 * 打印标记
	 * 0 快递单与发货单未打印
	 * 1 快递单或发货单已打印
	 * 2 仅快递单已打印
	 * 3 仅发货单已打印
	 * 4 快递单与发货单已打印
	 * 5 快递单或发货单未打印
	 * 6 快递单未打印
	 * 7 发货单未打印
	 * 8 商品标签未打印
	 * 9 商品标签已打印
	 * 10 均未打印
	 * 11 均已打印
	 */
	options: [
		{
			key: "",
			value: "打印状态",
		},
		{
			key: "EMPTY",
			value: "全部",
		},
		{
			key: "2",
			value: "仅快递单已打印",
		},
		{
			key: "3",
			value: "仅发货单已打印",
		},
		{
			key: "8",
			value: "商品标签未打印",
		},
		{
			key: "9",
			value: "商品标签已打印",
		},
		{
			key: "0",
			value: "快递单发货单未打印",
		},
		{
			key: "4",
			value: "快递单发货单已打印",
		},
		{
			key: "10",
			value: "均未打印",
		},
		{
			key: "11",
			value: "均已打印",
		},
	],
};

// printStatus 打印状态，100 未打印，101已打印
// fhdPrintStatus 发货单打印状态，0 未打印，1已打印
// labelPrintStatus 商品标签打印状态，0未打印，1已打印
export const ORDER_PRINT_STATUS_TREE_DATA: any = [
	{
		title: "快递单",
		value: "printStatus",
		key: "0-0",
		children: [
			{ title: "快递单已打印", value: "101", key: "0-0-0" },
			{ title: "快递单未打印", value: "100", key: "0-0-1" },
		],
	},
	{
		title: "发货单",
		value: "fhdPrintStatus",
		key: "0-1",
		children: [
			{ title: "发货单已打印", value: "1", key: "0-1-0" },
			{ title: "发货单未打印", value: "0", key: "0-1-1" },
		],
	},
	{
		title: "商品标签",
		value: "labelPrintStatus",
		key: "0-2",
		children: [
			{ title: "商品标签已打印", value: "1", key: "0-2-0" },
			{ title: "商品标签未打印", value: "0", key: "0-2-1" },
		],
	},
];

// 动态生成常用标签(包含)选项的函数
export const getTradeNormalIncludeOptions = () => {
	const baseOptions = [
		{
			key: "merge",
			value: "合并订单",
		},
		{
			key: "pending",
			value: "挂起订单",
		},
		{
			key: "isFirstSend",
			value: "先发货订单",
		},
		{
			key: "preSale",
			tip: "支持淘宝、拼多多、抖店、快手平台",
			value: "预售订单(含预售商品)",
		},
		{
			key: "emptyBag",
			value: "空包订单",
		},
		{
			key: "haveGoodLabel",
			value: "已生成商品标签",
		},
		{
			key: "fare",
			value: "含运费",
		},
		{
			key: "present",
			tip: "支持拼多多、抖店、小红书、快手、淘宝平台",
			value: "含平台赠品",
		},
		{
			key: "sysGift",
			tip: "在赠品策略页面手动更新订单时，满足赠品策略的订单系统会自动进行添加赠品操作",
			value: "含系统赠品",
		},
		{
			key: "town",
			value: "乡镇",
		},
		{
			key: "partShip",
			value: "部分发货",
		},
		{
			key: "urge",
			tip: "支持淘宝、拼多多、抖店平台",
			value: "催发货",
		},
		{
			key: "delay",
			value: "发货时效可延迟",
		},
		{
			key: "ptChangeItem",
			value: "线上改商品",
		},
		{
			key: "sysChangeItem",
			value: "系统改商品",
		},
		{
			key: "modifyAddress",
			value: "改地址",
		},
		{
			key: "multiPack",
			tip: "支持淘宝、淘工厂、1688、拼多多、抖店、京东、快手平台，且需在系统内多包裹发货",
			value: "一单多包裹",
		},
		{
			key: "fullOrderShip",
			tip: "支持京东平台",
			value: "整单发货",
		},
		{
			key: "resend",
			tip: "支持淘宝、拼多多、抖店、快手、京东、视频号、小红书、淘工厂、有赞平台，且需在助手ERP内进行重新发货",
			value: "重新发货",
		},
		{
			key: "splitOrder",
			value: "拆分订单",
		},
		{
			key: "appendSend",
			value: "追加包裹",
			tip: "支持淘宝/天猫、拼多多、抖店、快手平台，且需要在助手ERP内进行追加包裹操作",
		},
		{
			key: "bizWave",
			value: "已生成拣货波次",
		},
		{
			key: "jingxi",
			value: "京喜订单",
			tip: "支持京东平台",
		},
	];

	return baseOptions;
};

// 动态生成常用标签(不包含)选项的函数
export const getTradeNormalUnincludeOptions = () => {
	const baseOptions = [
		{
			key: "noMerge",
			value: "合并订单",
		},
		{
			key: "noPending",
			value: "挂起订单",
		},
		{
			key: "noFirstSend",
			value: "先发货订单",
		},
		{
			key: "noPreSale",
			tip: "支持淘宝、拼多多、抖店、快手平台",
			value: "预售订单(含预售商品)",
		},
		{
			key: "noEmptyBag",
			value: "空包订单",
		},
		{
			key: "noGoodLabel",
			value: "已生成商品标签",
		},
		{
			key: "noFare",
			value: "含运费",
		},
		{
			key: "noPresent",
			tip: "支持拼多多、抖店、小红书、快手、淘宝平台",
			value: "含平台赠品",
		},
		{
			key: "!sysGift",
			tip: "在赠品策略页面手动更新订单时，满足赠品策略的订单系统会自动进行添加赠品操作",
			value: "含系统赠品",
		},
		{
			key: "noTown",
			value: "乡镇",
		},
		{
			key: "noPartShip",
			value: "部分发货",
		},
		{
			key: "nourge",
			tip: "支持淘宝、拼多多、抖店平台",
			value: "催发货",
		},
		{
			key: "noDelay",
			value: "发货时效可延迟",
		},
		{
			key: "noPtChangeItem",
			value: "线上改商品",
		},
		{
			key: "noSysChangeItem",
			value: "系统改商品",
		},
		{
			key: "noModifyAddress",
			value: "改地址",
		},
		{
			key: "noMultiPack",
			tip: "支持淘宝、淘工厂、1688、拼多多、抖店、京东、快手平台，且需在系统内多包裹发货",
			value: "一单多包裹",
		},
		{
			key: "noFullOrderShip",
			tip: "支持京东平台",
			value: "整单发货",
		},
		{
			key: "noResend",
			tip: "支持淘宝、拼多多、抖店、快手、京东、视频号、小红书、淘工厂、有赞平台，且需在助手ERP内进行重新发货",
			value: "重新发货",
		},
		{
			key: "noSplitOrder",
			value: "拆分订单",
		},
		{
			key: "!appendSend",
			value: "追加包裹",
			tip: "支持淘宝/天猫、拼多多、抖店、快手平台，且需要在助手ERP内进行追加包裹操作",
		},
		{
			key: "!bizWave",
			value: "已生成拣货波次",
		},
		{
			key: "!jingxi",
			value: "京喜订单",
			tip: "支持京东平台",
		},
	];

	return baseOptions;
};

// 使用函数替代静态数组
export const TRADE_LABEL_NORMAL_INCLUDE_OPTIONS =
	getTradeNormalIncludeOptions();
export const TRADE_LABEL_NORMAL_UNINCLUDE_OPTIONS =
	getTradeNormalUnincludeOptions();

export const SERVICE_TAG_TO_NEW = {
	"1": "designatedExpress",
	"2": "priorityDelivery",
	"3": "designatedPackMaterial",
	"4": "duoduoWholeSale",
	"5": "suspendDelivery",
	"6": "sfExpressFree",
};

// 其他标签包含
export const TRADE_LABEL_OTHER_INCLUDE_OPTIONS = [
	{
		key: "designatedExpress",
		tip: "支持拼多多、抖音、得物平台",
		value: "指定快递",
	},
	{
		key: "priorityDelivery",
		tip: "支持拼多多、抖音平台",
		value: "优先发货",
	},
	{
		key: "designatedPackMaterial",
		tip: "支持拼多多平台",
		value: "指定包材",
	},
	{
		key: "duoduoWholeSale",
		tip: "支持拼多多平台",
		value: "多多批发",
	},
	{
		key: "suspendDelivery",
		tip: "支持拼多多平台",
		value: "暂停发货",
	},
	{
		key: "sfExpressFree",
		tip: "支持拼多多、抖音、快手平台",
		value: "顺丰包邮",
	},
	{
		key: "jdjc",
		tip: "支持京东平台",
		value: "京仓订单",
	},
	{
		key: "jdyc",
		tip: "支持京东平台",
		value: "云仓订单",
	},
	{
		key: "jdjp",
		tip: "支持京东平台",
		value: "京配订单",
	},
	{
		key: "nojdjcyc",
		tip: "支持京东平台",
		value: "普通订单（非京仓/云仓）",
	},
	{
		key: "cod",
		tip: "支持淘宝、京东、手工单",
		value: "货到付款",
	},
	{
		key: "selectedAlliance",
		tip: "支持抖店平台",
		value: "精选联盟",
	},
	{
		key: "smallStoreSelfSelling",
		tip: "支持抖店平台",
		value: "小店自卖",
	},
	{
		key: "flashBuyingProducts",
		tip: "支持快手平台",
		value: "闪电购商品",
	},
	{
		key: "fxgAppointmentArrival",
		tip: "支持抖音平台",
		value: "承诺日达",
	},
	{
		key: "aliEntryServiceInfo",
		tip: "支持1688平台",
		value: "定制服务",
	},
	{
		key: "needSerialNumber",
		tip: "仅支持淘宝、天猫、抖音平台，发货时需填写商品识别码（SN码、IMEI号、ICCID码）",
		value: "商品识别码",
	},
	{
		key: "hasSfExpressService",
		tip: "支持淘宝、拼多多平台",
		value: "加运费发顺丰",
	},
	{
		key: 'authorOrder',
		tip: '支持抖音、快手、视频号平台',
		value: '达人订单'
	},
	{
		key: "deliveryOnDoor",
		tip: "支持京东、拼多多、小红书平台",
		value: "送货上门",
	},
	{
		key: "fxgRemoteDerict",
		tip: "支持抖音平台",
		value: "偏远直邮",
	},
	{
		key: "bicOrder",
		tip: "支持抖音平台，平台要求如珠宝类的订单（BIC订单）需要按照平台流程送到鉴定机构进行鉴定",
		value: "BIC质检订单",
	},
	{
		key: "bicInspected",
		value: "BIC已送检",
		tip: "抖店BIC质检订单已在ERP内操作BIC订单发货",
	},
	{
		key: "superLuckyBag",
		tip: "支持抖店平台",
		value: "福袋",
	},
	{
		key: "replenishSendOrder",
		tip: "支持淘工厂平台",
		value: "补寄订单",
	},
	{
		key: "bondedOrder",
		tip: "支持淘宝/天猫平台",
		value: "保税订单",
	},
	{
		key: "ciphertextHandTrade",
		value: "密文手工单",
	},
	{
		key: "presentOrder",
		tip: "支持淘宝、抖音、快手、视频号平台",
		value: "礼物订单",
	},
	{
		key: "brandDeliver",
		tip: "支持得物平台",
		value: "品牌直发",
	},
	{
		key: "ordinaryOrder",
		tip: "支持得物平台",
		value: "普通履约",
	},
	{
		key: "promiseLogistics",
		tip: "支持得物平台",
		value: "商家指定物流",
	},
	{
		key: "warehouseOrder",
		tip: "支持得物平台",
		value: "多仓发货",
	},
	{
		key: "clearanceOrder",
		tip: "支持淘宝、天猫平台",
		value: "清仓订单",
	},
	{
		key: "xsd_ptps",
		tip: "支持淘宝、天猫平台",
		value: "小时达:平台配送",
	},
	{
		key: "xsd_sjzps",
		tip: "支持淘宝、天猫平台",
		value: "小时达:商家自配送",
	},
	{
		key: "aezy",
		tip: "支持1688平台",
		value: "AE自营订单",
	},
	{
		key: "gfcf",
		tip: "支持1688平台",
		value: "官方仓发",
	},
	{
		key: 'zt',
		tip: '支持快团团平台',
		value: '自提'
	},
	{
		key: 'tcps',
		tip: '支持快团团平台',
		value: '同城配送'
	},
	{
		key: 'wxwl',
		tip: '支持快团团平台',
		value: '无需物流'
	},

	// {
	// 	key: 'directMailActivity',
	// 	tip: '支持拼多多平台',
	// 	value: '直邮活动'
	// }
];

//中转/集运标签
export const TRADE_LABEL_TRANSFER_INCLUDE_OPTIONS = [
	{
		key: "jyXj",
		tip: "支持拼多多平台",
		value: "新疆集运",
	},
	{
		key: "jyXg",
		tip: "支持拼多多平台",
		value: "香港集运",
	},
	{
		key: "zzXz",
		tip: "支持拼多多平台",
		value: "西藏中转",
	},
	{
		key: "zzgs",
		tip: "支持拼多多平台",
		value: "甘肃中转",
	},
	{
		key: "zznmg",
		tip: "支持拼多多平台",
		value: "内蒙古中转",
	},
	{
		key: "zznx",
		tip: "支持拼多多平台",
		value: "宁夏中转",
	},
	{
		key: "zzqh",
		tip: "支持拼多多平台",
		value: "青海中转",
	},
	{
		key: "fxgLogisticsTransit",
		tip: "支持抖音、快手、京东、淘宝平台",
		value: "物流转运/集运",
	},
];

//1688回流订单
export const TRADE_LABEL_1688_INCLUDE_OPTIONS = [
	{
		key: "hl-tx",
		value: "淘系回流订单",
	},
	{
		key: "hl-pdd",
		value: "拼多多回流订单",
	},
	{
		key: "hl-fxg",
		value: "抖音回流订单",
	},
	{
		key: "hl-jd",
		value: "京东回流订单",
	},
	{
		key: "hl-ksxd",
		value: "快手回流订单",
	},
	{
		key: "hl-xhs",
		value: "小红书回流订单",
	},
	{
		key: "hl-sph",
		value: "微信回流订单",
	},
	{
		key: "hl-other",
		value: "其他回流订单",
	},
	{
		key: "hl-plaintext",
		tip: "仅支持1688回流订单明文订单搜索",
		value: "1688明文订单",
	},
	{
		key: "hl-ciphertext",
		tip: "仅支持1688回流订单密文订单搜索",
		value: "1688密文订单",
	},
];

// 其他标签不包含
export const TRADE_LABEL_OTHER_UNINCLUDE_OPTIONS = [
	{
		key: "noDesignatedExpress",
		tip: "支持拼多多、抖音、得物平台",
		value: "指定快递",
	},
	{
		key: "noPriorityDelivery",
		tip: "支持拼多多、抖音平台",
		value: "优先发货",
	},
	{
		key: "noDesignatedPackMaterial",
		tip: "支持拼多多平台",
		value: "指定包材",
	},
	{
		key: "noDuoduoWholeSale",
		tip: "支持拼多多平台",
		value: "多多批发",
	},
	{
		key: "noSuspendDelivery",
		tip: "支持拼多多平台",
		value: "暂停发货",
	},
	{
		key: "noSfExpressFree",
		tip: "支持拼多多、抖音、快手平台",
		value: "顺丰包邮",
	},
	{
		key: "nojdjc",
		tip: "支持京东平台",
		value: "京仓订单",
	},
	{
		key: "nojdyc",
		tip: "支持京东平台",
		value: "云仓订单",
	},
	{
		key: "nojdjp",
		tip: "支持京东平台",
		value: "京配订单",
	},
	{
		key: "jdjcyc",
		tip: "支持京东平台",
		value: "普通订单（非京仓/云仓）",
	},
	{
		key: "noCod",
		tip: "支持淘宝、京东、手工单",
		value: "货到付款",
	},
	{
		key: "noSelectedAlliance",
		tip: "支持抖店平台",
		value: "精选联盟",
	},
	{
		key: "noSmallStoreSelfSelling",
		tip: "支持抖店平台",
		value: "小店自卖",
	},
	{
		key: "noFlashBuyingProducts",
		tip: "支持快手平台",
		value: "闪电购商品",
	},
	{
		key: "noFxgAppointmentArrival",
		tip: "支持抖音平台",
		value: "承诺日达",
	},
	{
		key: "noAliEntryServiceInfo",
		tip: "支持1688平台",
		value: "定制服务",
	},
	{
		key: "noNeedSerialNumber",
		tip: "仅支持淘宝、天猫、抖音平台，发货时需填写商品识别码（SN码、IMEI号、ICCID码）",
		value: "商品识别码",
	},
	{
		key: "noHasSfExpressService",
		tip: "支持淘宝、拼多多平台",
		value: "加运费发顺丰",
	},
	{
		key: 'noAuthorOrder',
		tip: '支持抖音、快手、视频号平台',
		value: '达人订单'
	},
	{
		key: "noDeliveryOnDoor",
		tip: "支持京东、拼多多、小红书平台",
		value: "送货上门",
	},
	{
		key: "noFxgRemoteDerict",
		tip: "支持抖音平台",
		value: "偏远直邮",
	},
	{
		key: "noBicOrder",
		tip: "支持抖音平台，平台要求如珠宝类的订单（BIC订单）需要按照平台流程送到鉴定机构进行鉴定",
		value: "BIC质检订单",
	},
	{
		key: "!bicInspected",
		value: "BIC已送检",
		tip: "抖店BIC质检订单已在ERP内操作BIC订单发货",
	},
	{
		key: "noSuperLuckyBag",
		tip: "支持抖店平台",
		value: "福袋",
	},
	{
		key: "noReplenishSendOrder",
		tip: "支持淘工厂平台",
		value: "补寄订单",
	},
	{
		key: "!bondedOrder",
		tip: "支持淘宝/天猫平台",
		value: "保税订单",
	},
	{
		key: "!ciphertextHandTrade",
		value: "密文手工单",
	},
	{
		key: "!presentOrder",
		tip: "支持淘宝、抖音、快手、视频号平台",
		value: "礼物订单",
	},
	{
		key: "!brandDeliver",
		tip: "支持得物平台",
		value: "品牌直发",
	},
	{
		key: "!ordinaryOrder",
		tip: "支持得物平台",
		value: "普通履约",
	},
	{
		key: "!promiseLogistics",
		tip: "支持得物平台",
		value: "商家指定物流",
	},
	{
		key: "!warehouseOrder",
		tip: "支持得物平台",
		value: "多仓发货",
	},
	{
		key: "!clearanceOrder",
		tip: "支持淘宝、天猫平台",
		value: "清仓订单",
	},
	{
		key: "!xsd_ptps",
		tip: "支持淘宝、天猫平台",
		value: "小时达:平台配送",
	},
	{
		key: "!xsd_sjzps",
		tip: "支持淘宝、天猫平台",
		value: "小时达:商家自配送",
	},
	{
		key: "!aezy",
		tip: "支持1688平台",
		value: "AE自营订单",
	},
	{
		key: "!gfcf",
		tip: "支持1688平台",
		value: "官方仓发",
	},
	{
		key: '!zt',
		tip: '支持快团团平台',
		value: '自提'
	},
	{
		key: '!tcps',
		tip: '支持快团团平台',
		value: '同城配送'
	},
	{
		key: '!wxwl',
		tip: '支持快团团平台',
		value: '无需物流'
	},
	// {
	// 	key: 'noDirectMailActivity',
	// 	tip: '支持拼多多平台',
	// 	value: '直邮活动'
	// }
];
//中转/集运标签不包含
export const TRADE_LABEL_TRANSFER_UNINCLUDE_OPTIONS = [
	{
		key: "nojyXj",
		tip: "支持拼多多平台",
		value: "新疆集运",
	},
	{
		key: "nojyXg",
		tip: "支持拼多多平台",
		value: "香港集运",
	},
	{
		key: "nozzXz",
		tip: "支持拼多多平台",
		value: "西藏中转",
	},
	{
		key: "nozzgs",
		tip: "支持拼多多平台",
		value: "甘肃中转",
	},
	{
		key: "nozznmg",
		tip: "支持拼多多平台",
		value: "内蒙古中转",
	},
	{
		key: "nozznx",
		tip: "支持拼多多平台",
		value: "宁夏中转",
	},
	{
		key: "nozzqh",
		tip: "支持拼多多平台",
		value: "青海中转",
	},
	{
		key: "noFxgLogisticsTransit",
		tip: "支持抖音、快手、京东、淘宝平台",
		value: "物流转运/集运",
	},
];
//1688回流订单不包含
export const TRADE_LABEL_1688_UNINCLUDE_OPTIONS = [
	{
		key: "hl-noTx",
		value: "淘系回流订单",
	},
	{
		key: "hl-noPdd",
		value: "拼多多回流订单",
	},
	{
		key: "hl-noFxg",
		value: "抖音回流订单",
	},
	{
		key: "hl-noJd",
		value: "京东回流订单",
	},
	{
		key: "hl-noKsxd",
		value: "快手回流订单",
	},
	{
		key: "hl-noXhs",
		value: "小红书回流订单",
	},
	{
		key: "hl-noSph",
		value: "微信回流订单",
	},
	{
		key: "hl-noOther",
		value: "其他回流订单",
	},
	{
		key: "hl-noPlaintext",
		tip: "仅支持1688回流订单明文订单搜索",
		value: "1688明文订单",
	},
	{
		key: "hl-noCiphertext",
		tip: "仅支持1688回流订单密文订单搜索",
		value: "1688密文订单",
	},
];

// 订单标签
export const TRADE_LABEL_OPTIONS = [
	{
		label: "标签(包含)",
		options: [
			{
				label: "常用标签",
				arr: TRADE_LABEL_NORMAL_INCLUDE_OPTIONS,
			},
			{
				label: "其他标签",
				arr: TRADE_LABEL_OTHER_INCLUDE_OPTIONS,
			},
			{
				label: "中转/集运标签",
				arr: TRADE_LABEL_TRANSFER_INCLUDE_OPTIONS,
			},
			{
				label: "1688回流订单",
				arr: TRADE_LABEL_1688_INCLUDE_OPTIONS,
			},
		],
	},
	{
		label: "标签(不包含)",
		options: [
			{
				label: "",
				arr: TRADE_LABEL_NORMAL_UNINCLUDE_OPTIONS,
			},
			{
				label: "其他标签",
				arr: TRADE_LABEL_OTHER_UNINCLUDE_OPTIONS,
			},
			{
				label: "中转/集运标签",
				arr: TRADE_LABEL_TRANSFER_UNINCLUDE_OPTIONS,
			},
			{
				label: "1688回流订单",
				arr: TRADE_LABEL_1688_UNINCLUDE_OPTIONS,
			},
		],
	},
];

// 动态生成异常选项的函数
export const getTradeExceptionIncludeOptions = () => {
	const baseOptions = [
		{
			key: "unBindingItem",
			value: "商品未绑定",
		},
		{
			key: "risk",
			tip: "支持快手、京东、拼多多平台",
			value: "风控订单",
		},
		{
			key: "abnormalAddress",
			tip: '异常地址是指可能存在"骗运费"风险的异常地址订单，如【浙江省杭州市滨江区 新疆维吾尔族自治区阿克苏地区乌什县xx路xx栋xx号】',
			value: "异常地址",
		},
		{
			key: "pOrderUnfinished",
			tip: "支持快手平台",
			value: "拼团订单未完成",
		},
		{
			key: "platformStockOut",
			tip: "支持拼多多平台",
			value: "平台缺货",
		},
		{
			key: "onlineShip",
			value: "线上已发货",
		},
		{
			key: "abnormalOnlineRefunded",
			value: "线上已退款",
		},

	  {
		key: 'blackList',
		tip: '即系统标记黑名单买家的订单',
		value: '黑名单'
	  },
	  {
		key: 'sysGiftAbnormal',
		tip: '订单下存在子订单产生售后，请复核当前系统赠品是否无误',
		value: '系统赠品异常'
	  },
	  {
		key: 'acceptOrderFlagNo',
		tip: '支持得物平台，目前需前往店铺后台接单',
		value: '商家未接单'
	  },{
		key: 'bizWaveAbnormal',
		value: '波次拣货异常'
	  },
	  {
		key: 'wxfh',
		value: '无需发货',
		tip: '支持快团团平台',
	  },
	  {
		key: 'fhqbftsp',
		value: '发货前部分退商品',
		tip: '支持快团团平台',
	  },
	];

	return baseOptions;
};
// 使用函数替代静态数组
export const TRADE_EXCEPTION_INCLUDE_OPTIONS =
	getTradeExceptionIncludeOptions();

// 动态生成不包含异常选项的函数
export const getTradeExceptionUnincludeOptions = () => {
	const baseOptions = [
	  {
		key: 'noUnBindingItem',
		value: '商品未绑定'
	  },
	  {
		key: 'noRisk',
		tip: '支持快手、京东、拼多多平台',
		value: '风控订单'
	  },
	  {
		key: 'noAbnormalAddress',
		tip: '异常地址是指可能存在"骗运费"风险的异常地址订单，如【浙江省杭州市滨江区 新疆维吾尔族自治区阿克苏地区乌什县xx路xx栋xx号】',
		value: '异常地址'
	  },
	  {
		key: 'pOrderNotUnfinished',
		tip: '支持快手平台',
		value: '拼团订单未完成'
	  },
	  {
		key: 'noPlatformStockOut',
		tip: '支持拼多多平台',
		value: '平台缺货'
	  },
	  {
		key: 'noOnlineShip',
		value: '线上已发货'
	},
	{
		key: '!abnormalOnlineRefunded',
		value: '线上已退款'
	},
	{
		key: 'noBlackList',
		tip: '即系统标记黑名单买家的订单',
		value: '黑名单'
	  },
	  {
		key: '!sysGiftAbnormal',
		tip: '订单下存在子订单产生售后，请复核当前系统赠品是否无误',
		value: '系统赠品异常'
	  },
	  {
		key: '!acceptOrderFlagNo',
		tip: '支持得物平台，目前需前往店铺后台接单',
		value: '商家未接单'
	  },{
		key: '!bizWaveAbnormal',
		value: '波次拣货异常'
	  },
	  {
		key: '!wxfh',
		value: '无需发货',
		tip: '支持快团团平台',
	  },
	  {
		key: '!fhqbftsp',
		value: '发货前部分退商品',
		tip: '支持快团团平台',
	  },
	];

	return baseOptions;
};
export const TRADE_EXCEPTION_UNINCLUDE_OPTIONS =
	getTradeExceptionUnincludeOptions();

// 订单异常
export const TRADE_EXCEPTION_OPTIONS = [
	{
		label: "异常(包含)",
		options: [
			{
				label: "",
				arr: TRADE_EXCEPTION_INCLUDE_OPTIONS,
			},
		],
	},
	{
		label: "异常(不包含)",
		options: [
			{
				label: "",
				arr: TRADE_EXCEPTION_UNINCLUDE_OPTIONS,
			},
		],
	},
];

export const SELL_ATTR_OPTIONS: ISelectOption = {
	default: [],
	options: [
		{
			key: "1",
			value: "单商品单规格单件",
		},
		{
			key: "2",
			value: "单商品单规格多件",
		},
		{
			key: "3",
			value: "单商品多规格多件",
		},
		{
			key: "4",
			value: "多商品多规格多件",
		},
	],
};

export const GOOD_STOCK_STATUS_OPTIONS: ISelectOption = {
	default: "ALL",
	options: [
		{
			key: "ALL",
			value: "订单缺货状态",
		},
		{
			key: "HAS_STOCK",
			value: "有货",
		},
		{
			key: "PART_STOCK",
			value: "部分有货",
		},
		{
			key: "NO_STOCK",
			value: "缺货",
		},
	],
};

// 订单来源
export const ORDER_SOURCE_TYPE_OPTIONS: ISelectOption = {
	default: "",
	options: [
		{
			key: "",
			value: "订单来源",
		},
		{
			key: "platfromOrder",
			value: "平台订单",
		},
		{
			key: "handOrder",
			value: "手工订单",
		},
	],
};

// 订单来源
export const ORDER_SOURCE_TYPE_OPTIONS_PLAIN: ISelectOption = {
	default: "",
	options: [
		{
			key: "",
			value: "订单来源",
		},
		{
			key: "PLATFORM",
			value: "平台订单",
		},
		{
			key: "HAND",
			value: "手工订单",
		},
	],
};

export const LOGISTICS_TYPE_OPTIONS: ISelectOption = {
	default: "",
	options: [
		{
			key: "",
			value: "物流方式",
		},
		{
			key: "0",
			value: "快递",
		},
		{
			key: "2",
			value: "自提",
		},
	],
};

export const REFUND_TYPE_OPTIONS: ISelectOption = {
	default: "",
	options: [
		{
			key: "",
			value: "退款状态",
		},
		{
			key: "HAS_REFUND",
			value: "有退款",
		},
		{
			key: "NOT_REFUND",
			value: "无退款",
		},
	],
};

export const NEW_TIME_REMAINING_TYPE_OPTIONS: ISelectOption = {
	default: "",
	options: [
		{
			key: "",
			value: "剩余发货时间筛选",
		},
		{
			key: "none-none",
			value: "全部时间",
		},
		{
			key: "CUSTOM",
			value: "自定义",
		},
		{
			key: "none-0",
			value: "已超时",
		},
		{
			key: "none-6",
			value: "剩余时间小于6小时",
		},
		{
			key: "none-12",
			value: "剩余时间小于12小时",
		},
		{
			key: "none-24",
			value: "剩余时间小于24小时",
		},
		{
			key: "24-99",
			value: "剩余时间大于24小时",
		},
	],
};

export const TIME_REMAINING_TYPE_OPTIONS: ISelectOption = {
	default: "",
	options: [
		{
			key: "",
			value: "剩余发货时间筛选",
		},
		{
			key: "ALL",
			value: "全部时间",
		},
		{
			key: "CUSTOM",
			value: "自定义",
		},
		{
			key: "ALREADY_DELAY",
			value: "已超时",
		},
		{
			key: "LESS_THAN_SIX_HOURS",
			value: "剩余时间小于6小时",
		},
		{
			key: "LESS_THAN_TWELVE_HOURS",
			value: "剩余时间小于12小时",
		},
		{
			key: "LESS_THAN_TWENTY_FOUR_HOURS",
			value: "剩余时间小于24小时",
		},
		{
			key: "GREATER_THAN_TWENTY_FOUR_HOURS",
			value: "剩余时间大于24小时",
		},
	],
};

export const PEND_TYPE_OPTIONS: ISelectOption = {
	default: "",
	options: [
		{
			key: "",
			value: "是否挂起",
		},
		{
			key: "1",
			value: "有挂起",
		},
		{
			key: "0",
			value: "无挂起",
		},
	],
};

export const ABNORMAL_TYPE_OPTIONS: ISelectOption = {
	default: "",
	options: [
		{
			key: "",
			value: "订单异常",
		},
		{
			key: "1",
			value: "正常异常",
		},
		{
			key: "0",
			value: "锁定订单",
		},
	],
};

export const TRADE_SORT_OPTIONS: ISearchItem[] = [
	{
		key: 2,
		value: "付款时间由近到远",
		point: Pointer.订单_订单打印_订单排序_付款时间由近到远,
	},
	{
		key: 1,
		value: "付款时间由远到近",
		point: Pointer.订单_订单打印_订单排序_付款时间由远到近,
	},
	{
		key: 4,
		value: "下单时间由近到远",
		point: Pointer.订单_订单打印_订单排序_下单时间由近到远,
	},
	{
		key: 3,
		value: "下单时间由远到近",
		point: Pointer.订单_订单打印_订单排序_下单时间由远到近,
	},
	{
		key: 10,
		value: "按剩余发货时间排序",
		point: Pointer.订单_订单打印_订单排序_按剩余发货时间,
	},
	{
		key: 6,
		value: "实付金额由大到小",
		point: Pointer.订单_订单打印_订单排序_实付金额由大到小,
	},
	{
		key: 7,
		value: "实付金额由小到大",
		point: Pointer.订单_订单打印_订单排序_实付金额由小到大,
	},
	{
		key: 8,
		value: "宝贝数量由多到少",
		point: Pointer.订单_订单打印_订单排序_宝贝数量由多到少,
	},
	{
		key: 9,
		value: "宝贝数量由少到多",
		point: Pointer.订单_订单打印_订单排序_宝贝数量由少到多,
	},
	// {
	// 	key: 35,
	// 	value: '按订单重量，重的在前面',
	// 	point: Pointer.订单_订单打印_订单排序_按订单重量_重的在前面
	// },
	{
		key: 16,
		value: "按商家编码排序",
		point: Pointer.订单_订单打印_订单排序_按商家编码排序,
	},
	{
		key: 26,
		value: "按商家编码SKU编码升序",
		point: Pointer.订单_订单打印_订单排序_按商家编码SKU编码升序,
	},
	{
		key: 27,
		value: "按商家编码SKU编码降序",
		point: Pointer.订单_订单打印_订单排序_按商家编码SKU编码降序,
	},
	{
		key: 34,
		value: "仅按SKU编码排序，编码大的在前面",
		point: Pointer.订单_订单打印_订单排序_仅按SKU编码排序_编码大的在前面,
	},
	{
		key: 33,
		value: "仅按SKU编码排序，编码小的在前面",
		point: Pointer.订单_订单打印_订单排序_仅按SKU编码排序_编码小的在前面,
	},
	{
		key: 14,
		value: "按收件地址省份排序",
		point: Pointer.订单_订单打印_订单排序_按收件地址省份排序,
	},
	{
		key: 18,
		value: "按宝贝简称规格别名升序",
		point: Pointer.订单_订单打印_订单排序_按宝贝简称规格别名升序,
	},
	{
		key: 19,
		value: "按宝贝简称规格别名降序",
		point: Pointer.订单_订单打印_订单排序_按宝贝简称规格别名降序,
	},
	{
		key: 22,
		value: "按宝贝简称规格名称升序",
		point: Pointer.订单_订单打印_订单排序_按宝贝简称规格名称升序,
	},
	{
		key: 23,
		value: "按宝贝简称规格名称降序",
		point: Pointer.订单_订单打印_订单排序_按宝贝简称规格名称降序,
	},
	{
		key: 30,
		value: "按商品、规格排序，规格小的在前面",
		point: Pointer.订单_订单打印_订单排序_按商品_规格排序_规格小的在前,
	},
	{
		key: 31,
		value: "按商品、规格排序，规格大的在前面",
		point: Pointer.订单_订单打印_订单排序_按商品_规格排序_规格大的在前,
	},
	{
		key: 32,
		value: "按规格名称排序",
		point: Pointer.订单_订单打印_订单排序_按规格名称排序,
	},
	{
		key: 39,
		value: "按货品规格编码排序",
		point: Pointer.订单_订单打印_订单排序_按货品规格编码排序,
	},
	{
		key: 40,
		value: "按货品规格编码排序，数量由多到少",
		point: Pointer.订单_订单打印_订单排序_按货品规格编码排序,
	},
	{
		key: 20,
		value: "按店铺商品id排序",
		point: Pointer.订单_订单打印_订单排序_按店铺商品id排序,
	},
];

export const SAME_ITEM_TRADE_SORT_OPTIONS: ISearchItem[] = [
	{
		key: 28,
		value: "相同商品挨在一起,数量由多到少",
		point: Pointer.订单_订单打印_订单排序_相同商品挨在一起_数量由多到少,
	},
	{
		key: 29,
		value: "相同商品挨在一起，区分颜色，数量由多到少",
		point: Pointer.订单_订单打印_订单排序_相同商品挨在一起_区分颜色_数量由多到少,
	},
	{
		key: 36,
		value: "按宝贝简称、颜色、尺码从小到大排序（S、M、L）",
		point: Pointer[
			"订单_订单打印_订单排序_按宝贝简称、颜色，数量从多到少、尺码从小到大排序（S、M、L）"
		],
	},
	{
		key: 37,
		value: "按商家编码、颜色、尺码从小到大排序（S、M、L）",
		point: Pointer[
			"订单_订单打印_订单排序_按商家编码、颜色，数量从多到少、尺码从小到大排序（S、M、L）"
		],
	},
	{
		key: 38,
		value: "按规格编码、颜色、尺码从小到大排序（S、M、L）",
		point: Pointer[
			"订单_订单打印_订单排序_按规格编码、颜色，数量从多到少、尺码从小到大排序（S、M、L）"
		],
	},
];

export const enum ConditionIdEnum {
	"平台&店铺" = 1,
	"时间选择" = 2,
	"订单状态" = 3,
	"打印状态" = 4,
	"省市区" = 5,
	"留言备注" = 6,
	// 订单异常 = 7,
	// "商品查询" = 8,
	"买家昵称" = 9,
	"收件人" = 10,
	"手机号" = 11,
	"订单编号" = 12,
	"系统单号" = 12.1,
	"快递单号" = 13,
	"订单标记" = 15,
	"销售属性" = 16,
	"退款状态" = 17,
	"订单标签" = 18,
	"剩余发货时间" = 19,
	// "服务承诺" = 20,
	// 21
	"缺货状态" = 22,
	"订单异常" = 23,
	"订单来源" = 24,
	"市场" = 25,
	"档口" = 26,
	"供应商" = 27,
	"达人名称/ID" = 28,
	// 29
	"颜色尺码" = 30,
	"订单类型" = 31,
	"商品包含" = 32,
	"商品不包含" = 33,
	// 34
	// 35
	"商品数量" = 36,
	"商品种类" = 37,
	"订单金额" = 38,
	"订单重量" = 39,
	"线下备注" = 40,
	"选择分销商" = 41,
	"发货仓库" = 42,
	// "智选快递" = 43,
	"波次号" = 44,
	"唯一码" = 45,
}

// 批打定制查询条件
export const QueryConditionDefaultConfig = [
	{
		condition: "platform",
		disabled: "true",
		conditionName: "平台&店铺",
		id: ConditionIdEnum["平台&店铺"],
		selected: 1,
		sort: 1,
	},
	{
		condition: "timeType",
		disabled: "true",
		conditionName: "时间选择",
		id: ConditionIdEnum.时间选择,
		selected: 1,
		sort: 2,
	},
	{
		condition: "orderSource",
		conditionName: "订单来源",
		id: ConditionIdEnum.订单来源,
		selected: 1,
		sort: 3,
	},
	{
		condition: "status",
		disabled: "true",
		conditionName: "订单状态",
		id: ConditionIdEnum.订单状态,
		selected: 1,
		sort: 4,
	},
	{
		condition: "printStatus",
		conditionName: "打印状态",
		id: ConditionIdEnum.打印状态,
		selected: 1,
		sort: 5,
	},
	{
		condition: "areaContain",
		conditionName: "省市区",
		id: ConditionIdEnum.省市区,
		selected: 1,
		sort: 6,
	},
	{
		condition: "ptTid",
		conditionName: "订单编号",
		id: ConditionIdEnum.订单编号,
		selected: 1,
		sort: 7,
	},
	{
		condition: "tid",
		conditionName: "系统单号",
		id: ConditionIdEnum.系统单号,
		selected: 1,
		sort: 8,
	},
	{
		condition: "residueSendTime",
		conditionName: "剩余发货时间",
		id: ConditionIdEnum.剩余发货时间,
		selected: 1,
		sort: 9,
	},
	{
		condition: "itemInclude",
		conditionName: "商品包含",
		id: ConditionIdEnum.商品包含,
		selected: 1,
		sort: 10,
	},
	{
		condition: "itemNotInclude",
		conditionName: "商品不包含",
		id: ConditionIdEnum.商品不包含,
		selected: 1,
		sort: 11,
	},
	{
		condition: "colorSizeIncludingList",
		conditionName: "颜色尺码",
		id: ConditionIdEnum.颜色尺码,
		selected: 0,
		sort: 12,
	},
	{
		condition: "flagValue",
		conditionName: "留言备注",
		id: ConditionIdEnum.留言备注,
		selected: 1,
		sort: 13,
	},
	{
		condition: "sellAttribute",
		conditionName: "销售属性",
		id: ConditionIdEnum.销售属性,
		selected: 1,
		sort: 14,
	},
	{
		condition: "buyerNick",
		conditionName: "买家昵称",
		id: ConditionIdEnum.买家昵称,
		selected: 1,
		sort: 15,
	},
	{
		condition: "receiveName",
		conditionName: "收件人",
		id: ConditionIdEnum.收件人,
		selected: 1,
		sort: 16,
	},
	{
		condition: "mobile",
		conditionName: "手机号",
		id: ConditionIdEnum.手机号,
		selected: 1,
		sort: 17,
	},
	{
		condition: "sid",
		conditionName: "快递单号",
		id: ConditionIdEnum.快递单号,
		selected: 1,
		sort: 18,
	},
	{
		condition: "labelIdList",
		conditionName: "唯一码",
		id: ConditionIdEnum.唯一码,
		selected: 0,
		sort: 19,
	},
	{
		condition: "quickQuery",
		conditionName: "订单标签",
		id: ConditionIdEnum.订单标签,
		selected: 1,
		sort: 20,
	},
	{
		condition: "exceptionFlag",
		conditionName: "订单异常",
		id: ConditionIdEnum.订单异常,
		selected: 1,
		sort: 21,
	},
	{
		condition: "refundStatus",
		conditionName: "退款状态",
		id: ConditionIdEnum.退款状态,
		selected: 1,
		sort: 22,
	},
	{
		condition: "goodStockStatus",
		conditionName: "缺货状态",
		id: ConditionIdEnum.缺货状态,
		selected: 0,
		sort: 23,
	},
	{
		condition: "goodsTypeNumRange",
		conditionName: "商品种类",
		id: ConditionIdEnum.商品种类,
		selected: 0,
		sort: 24,
	},
	{
		condition: "goodsTotalNumRange",
		conditionName: "商品数量",
		id: ConditionIdEnum.商品数量,
		selected: 0,
		sort: 25,
	},
	{
		condition: "weightRange",
		conditionName: "订单重量",
		id: ConditionIdEnum.订单重量,
		selected: 0,
		sort: 26,
	},
	{
		condition: "paymentRange",
		conditionName: "订单金额",
		id: ConditionIdEnum.订单金额,
		selected: 0,
		sort: 27,
	},
	{
		condition: "tradeType",
		conditionName: "订单类型",
		id: ConditionIdEnum.订单类型,
		selected: 0,
		sort: 28,
	},
	{
		condition: "bizMark",
		conditionName: "订单标记",
		id: ConditionIdEnum.订单标记,
		selected: 0,
		sort: 29,
	},

	{
		condition: "sysMemo",
		conditionName: "线下备注",
		id: ConditionIdEnum.线下备注,
		selected: 0,
		sort: 30,
	},
	{
		condition: "supplierIncludingList",
		conditionName: "供应商",
		id: ConditionIdEnum.供应商,
		selected: 0,
		sort: 31,
	},
	{
		condition: "marketIncludingList",
		conditionName: "市场",
		id: ConditionIdEnum.市场,
		selected: 0,
		sort: 32,
	},
	{
		condition: "dangKouIncludingList",
		conditionName: "档口",
		id: ConditionIdEnum.档口,
		selected: 0,
		sort: 33,
	},
	{
		condition: "authorIncludingList",
		conditionName: "达人名称/ID",
		id: ConditionIdEnum["达人名称/ID"],
		selected: 0,
		sort: 34,
	},
	{
		condition: "distributor",
		conditionName: "选择分销商",
		id: ConditionIdEnum.选择分销商,
		selected: 0,
		sort: 35,
	},
	{
		condition: "storage",
		conditionName: "发货仓库",
		id: ConditionIdEnum.发货仓库,
		selected: 0,
		sort: 36,
	},
	{
		condition: "waveNo",
		conditionName: "波次号",
		id: ConditionIdEnum.波次号,
		selected: 1, // 1表示默认选中
		sort: 37, // 排序位置，可以根据需要调整
	},
	// {
	// 	"condition": "smartExpressTemplateId",
	// 	"conditionName": "智选快递",
	// 	"id": ConditionIdEnum.智选快递,
	// 	"selected": 0,
	// 	"sort": 36
	// },
];

// 批打定制查询条件：在默认条件上，更新用户保存的数据
export const defaultFormatCondition = (advancedRes?: any) => {
	const listMap = {};
	advancedRes?.queryConditionDTO
		?.split(",")
		?.filter((i) => i)
		?.forEach((item: string, index: number) => {
			const [id, selected] = item.split("_");
			if (!listMap[id]) {
				listMap[id] = {
					id,
					selected: Number(selected),
					sort: index + 1,
				};
			} else {
				console.log("listMap[id]: ", listMap[id]);
			}
		});
	const newList = QueryConditionDefaultConfig.map((item) => {
		const mapItem = listMap[item.id];
		const newItem = { ...item };
		if (mapItem) {
			newItem.selected = mapItem.selected;
			newItem.sort = mapItem.sort;
		} else {
			console.log(`后端没有返回 ${newItem.id} ${newItem.conditionName}`);
			// 商品包含、不包含，采用旧的商品查询勾选
			if (
				[ConditionIdEnum.商品不包含, ConditionIdEnum.商品包含].includes(
					item.id
				) &&
				listMap[8]
			) {
				newItem.selected = listMap[8].selected;
				newItem.sort = listMap[8].sort;
			}
			// 智选快递，没开启不勾选，开启默认勾选
			// if(item.id == ConditionIdEnum.智选快递){
			// 	newItem.selected = tradeStore?.smartExpressSwitch ? 1 : 0;
			// }
		}
		return newItem;
	});
	newList.sort((a, b) => a.sort - b.sort);

	const checkWhitePresList = newList.filter((v) => {
		if (
			v.id == ConditionIdEnum.波次号 &&
			!userStore.hasWaveManagePermission
		) {
			return false;
		}
		return true;
	});
	return checkWhitePresList;
};

export const MAX_VALUE = 999999999;
export const MIN_VALUE = 0;

export const RANGE_OPTION = {
	default: "",
	options: [
		{
			key: "",
			value: "选择区间筛选:商品数量/订单金额等",
		},
		{
			key: "goodsTotalNumRange",
			value: "商品数量",
		},
		{
			key: "goodsTypeNumRange",
			value: "商品种类",
		},
		{
			key: "paymentRange",
			value: "订单金额",
		},
		{
			key: "weightRange",
			value: "订单重量",
		},
		// {
		// 	key: 'orderRange',
		// 	value: '订单数量'
		// }
	],
};

// 老的预设查询条件，没有引用了
export const FLAG_VALUE = {
	default: "",
	options: [
		{
			key: "1",
			value: "有留言",
		},
		{
			key: "2",
			value: "有备注",
		},
		{
			key: "11",
			value: "无留言",
		},
		{
			key: "12",
			value: "无备注",
		},
		{
			key: "3",
			value: "有留言和备注",
		},
		{
			key: "4",
			value: "无留言和备注",
		},
		{
			key: "0",
			value: allFlagColorObj.灰旗,
		},
		{
			key: "5",
			value: allFlagColorObj.红旗,
		},
		{
			key: "6",
			value: allFlagColorObj.黄旗,
		},
		{
			key: "7",
			value: allFlagColorObj.绿旗,
		},
		{
			key: "8",
			value: allFlagColorObj.蓝旗,
		},
		{
			key: "9",
			value: allFlagColorObj.紫旗,
		},
	],
};

export const SERVICE_TAG = {
	default: "",
	options: [
		{
			key: "",
			value: "服务标签",
		},
		{
			key: "3",
			value: "指定包材",
		},
		{
			key: "1",
			value: "指定快递",
		},
		{
			key: "2",
			value: "优先发货",
		},
		{
			key: "4",
			value: "多多批发",
		},
		{
			key: "5",
			value: "暂停发货",
		},
		{
			key: "6",
			value: "顺丰包邮",
		},
	],
};

export const EXCEPTION_FLAG = {
	default: "",
	options: [
		{
			key: "",
			value: "异常订单",
		},
		{
			key: "unBindingItem",
			value: "商品未绑定",
		},
		{
			key: "ptChangeItem",
			value: "线上改商品",
		},
	],
};

// ALL("全部"),
// * NOT_GENERATE("未生成"),
// * ALREADY_GENERATE("已生成"),
export const LABEL_GENERATOR_STATUS = {
	default: "ALL",
	options: [
		{
			key: "ALL",
			value: "全部小标签",
		},
		{
			key: "NOT_GENERATE",
			value: "未生成小标签",
		},
		{
			key: "ALREADY_GENERATE",
			value: "已生成小标签",
		},
	],
};
