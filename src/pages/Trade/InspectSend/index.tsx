/*
 * @Author: try <EMAIL>
 * @Date: 2024-04-09 17:11:29
 * @Description: 
 */
import React, { useEffect, useState } from "react";
import { observer } from "mobx-react";
import { <PERSON><PERSON>, Spin, Tooltip } from "antd";
import dayjs from "dayjs";
import { CheckCircleFilled, CloseCircleFilled, ExclamationCircleFilled, SoundOutlined } from "@ant-design/icons";
import { getMd5Str, randomHex } from "@/utils/util";
import TradeDetailCard from "./components/TradeDetailCard";
import styles from "./index.module.scss";
import voiceSendSuccess from '@/assets/mp3/发货成功.mp3';
import voiceHandleFail from '@/assets/mp3/处理失败.mp3';
import { playAudio } from "../ScanPrint/utils";
import { tradeStore } from "@/stores";
import Header from "./components/Header";
import userStore from "@/stores/user";
import Flex from "@/components/Flex";
import CardContent from "./components/CardContent";
import ScanRecord from "./components/ScanRecord";
import { useStores } from "@/stores/tool";
import ContentSetting from "./components/ContentSetting";
import { local } from "@/libs/db";
import SelectShootingDeviceModal from "@/components-biz/SelectShootingDeviceModal";
import videoMonitorStore from "@/stores/trade/videoMonitor";

const SELECTED_SHOOTING_DEVICES_CACHE_KEY = 'SELECTED_SHOOTING_DEVICES_INSPECT_SEND';

const randomStr = `${dayjs().valueOf()}_${randomHex()}`;
const InspectSend = () => {
	const {
		InspectSendStore: {
			getContentSettingConfig, tradeList, showLoading, contentSettingModalVisible, totalNum,
			videoMonitorState,
			setVideoMonitorState,
			selectedShootingDevices,
			setSelectedShootingDevices,
			getAdvancedSettingConfig,
			nvrDevices,
			setNvrDevices,
		},
	} = tradeStore;
	const [visible, setVisible] = useState(false);
	const { userInfo } = userStore;
	const [isShowChooseModel, setIsShowChooseModel] = useState(false); // 是否显示选择拍摄设备
	const getTradeDetailCard = () => {
		console.log('tradeList3343', tradeList);
		
		const tradeDetailList = [];
		tradeList.forEach(pack => {
			tradeDetailList.push(
				<div key={ pack.togetherId } style={ { marginBottom: '16px' } }>
					<TradeDetailCard pack={ pack } />
				</div>
			);
		});
		return tradeDetailList;
	};
	const store: typeof userStore = useStores('userStore');
	const { userId = '', subUserId = "" } = store.userInfo;
	const uniqueCode = getMd5Str(`${userId}_${subUserId}_${randomStr}`);
	const { 
		checkKdzsPrintComponent,
		connectWs,
		disconnectWs,
		videoMonitor
	} = videoMonitorStore;
	const [ishow, setIsShow] = useState(false); // 新增状态
	// InspectSend/index.tsx
	useEffect(() => {
		const init = async() => {
			userStore.getUserSetting();
			await getContentSettingConfig();
			await getAdvancedSettingConfig(); 
			// 在设置完成后检查并连接控件
			if (videoMonitorState && videoMonitor) {
				await handleReconnect();
			}
		};
		init();
		return () => {
			disconnectWs();
		};
	}, [videoMonitor, userInfo?.userId]);
	
	// 保留原有的useEffect用于响应设置变化
	useEffect(() => {
		if (videoMonitorState && videoMonitor) {
			handleReconnect(); // 初始化视频监控相关
		} else {
			disconnectWs(); // 断开ERP聚合控件连接
		}
	}, [videoMonitorState, videoMonitor]);
	// 校验并更新缓存的设备信息
	const validateAndUpdateCachedDevices = async() => {
		try {
		// 获取缓存的设备信息
			const cachedDevices = local.getByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY) || [];
		
			if (cachedDevices.length === 0) {
				return;
			}

			// 获取最新的设备列表
			const currentDeviceList = await videoMonitorStore.getDeviceCameraList();
			// 过滤掉禁用的设备
			const enabledDevices = currentDeviceList.filter(item => item.status === 1);

			// 校验缓存中的设备是否还存在
			const validDevices = cachedDevices.filter((cachedDevice: any) => enabledDevices.find((device: any) => device.id === cachedDevice.id));

			// 校验是否属于同一个NVR
			if (validDevices.length > 0) {
				const nvrIds = validDevices.map((device: any) => device.deviceId);
				const uniqueNvrIds = Array.from(new Set(nvrIds));
				if (uniqueNvrIds.length > 1) {
				// 只保留第一个NVR下的设备
					const firstNvrId = nvrIds[0];
					const sameNvrDevices = validDevices.filter((device: any) => device.deviceId === firstNvrId);
					local.setByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY, sameNvrDevices);
					setSelectedShootingDevices(sameNvrDevices);
				} else {
					local.setByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY, validDevices);
					setSelectedShootingDevices(validDevices);
				}
			} else {
				local.setByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY, []);
				setSelectedShootingDevices([]);
			}

			// 获取nvr设备列表
			const nvrDevices = await videoMonitorStore.getDeviceList();
			setNvrDevices(nvrDevices);
		} catch (error) {
			console.error('校验缓存设备失败:', error);
			// 清除无效缓存
			local.removeByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY);
			setSelectedShootingDevices([]);
			setNvrDevices([]);
		}
	};
	const handleReconnect = async() => {
		console.log('重新连接');
		await validateAndUpdateCachedDevices();
		await checkKdzsPrintComponent();
		connectWs();
	};

	   // 监听状态变化，更新 ishow
	useEffect(() => {
		console.log(videoMonitor, videoMonitorState, 'videoMonitor, videoMonitorState');
		// 确保 userInfo 已经加载完成
		if (userInfo?.userId) {
			const newIshow = videoMonitor == 1 && videoMonitorState;
			setIsShow(newIshow);
		}
	}, [videoMonitor, videoMonitorState, userInfo?.userId]);
	// 处理选择拍摄设备确认
	const handleSelectShootingDeviceConfirm = (selectedDevices: any[]) => {
		// 缓存选中的设备
		local.setByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY, selectedDevices);
		setSelectedShootingDevices(selectedDevices);
		setIsShowChooseModel(false);
	};

	// 处理选择拍摄设备取消
	const handleSelectShootingDeviceCancel = () => {
		setIsShowChooseModel(false);
	};

	// 获取选中的设备名称显示文本
	const getSelectedDevicesText = () => {
		if (selectedShootingDevices.length === 0) {
			return "请选择拍摄设备";
		} else {
			return selectedShootingDevices.map(item => item.cameraName).join(",");
		}
	};
	return (
		<>
			{contentSettingModalVisible && <ContentSetting />}
			<ScanRecord uniqueCode={ uniqueCode } visible={ visible } onCancel={ () => setVisible(false) } />
			<Flex className={ styles.inspectSend } style={ { minHeight: "100%" } }>
				<Header uniqueCode={ uniqueCode } />
				<CardContent />
				<Flex.Column className={ styles['trade-wrap'] } justifyContent="space-between">
					<Spin spinning={ showLoading }>
						<div style={ { overflow: 'auto', height: `calc(100vh - 170px )` } }>
							<Flex className={ styles.infos } alignItems="center" justifyContent="space-between">
								<div>
									<Tooltip
										title={
											(
												<div>
													<p>统计快递包裹验货发货情况，合单情况下部分发货成功将记为发货成功</p>
												</div>
											)
										}
									>
										<ExclamationCircleFilled style={ { fontSize: '14px', color: '#FD8204', marginRight: '6px' } } />
									</Tooltip>
									本次扫描统计：
									<span style={ { marginLeft: '16px' } }>验货包裹 { totalNum.inspectionPackageNum}</span>
									<span style={ { marginLeft: '16px' } }>核验商品 { totalNum.verifyGoodsNum}</span>
									<span style={ { marginLeft: '16px' } }>发货成功 { totalNum.sendSuccessNum}</span>
									<span style={ { marginLeft: '16px' } }>发货失败 { totalNum.sendFailNum}</span>
								</div>
								<span onClick={ () => setVisible(true) } className={ styles.recordBtn }>本次验货记录</span>
							</Flex>
							<div className={ styles['trade-item-wrap'] }>
								{getTradeDetailCard()}
							</div>
						</div>
					</Spin>
					<div className={ styles["sound-warning-container"] }>
						<SoundOutlined />
						<span className="text">请提前打开声音，以保证能够听到提示音</span>
						<div className="success-info" onClick={ () => playAudio(voiceSendSuccess) }>
							<CheckCircleFilled />
							<span className="r-ml-4">成功提示音</span>
						</div>
						<div className="fail-info" onClick={ () => playAudio(voiceHandleFail) }>
							<CloseCircleFilled />
							<span className="r-ml-4">失败提示音</span>
						</div>
						{
							ishow ? (
								<Button 
									type="primary" 
									className="r-ml-16" 
									style={ { backgroundColor: "#1890ff", borderColor: "#1890ff" } }
									onClick={ () => {
										setIsShowChooseModel(true);
									} }
								>
									{getSelectedDevicesText()}
								</Button>
							) : null
						}
						{/* 选择拍摄设备弹框 */}
						<SelectShootingDeviceModal
							visible={ isShowChooseModel }
							onCancel={ handleSelectShootingDeviceCancel }
							onConfirm={ handleSelectShootingDeviceConfirm }
							defaultSelectedDevices={ selectedShootingDevices }
						/>
					</div>
				</Flex.Column>
			</Flex>
		</>
	);

};

export default observer(InspectSend);