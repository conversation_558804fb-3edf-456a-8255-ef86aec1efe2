import { Base } from '@/types/schemas/index';
import { platform } from '../common';
/**
 * SubAccountUserRequestVo :SubAccountUserRequestVo
 */
export interface AuthorityEditSubAccountUserRequest {
	/**
	 * 表字段:id,编辑时需要填入
	 */
	id?: number;
	/**
	 * 账号用户id
	 */
	userId?: number;
	/**
	 * 手机号
	 */
	mobile?: string;
	/**
	 * 子账号用户名
	 */
	subAccountName?: string;
	/**
	 * 密码
	 */
	password?: string;
	/**
	 * 权限id
	 */
	authorityId?: number;
	/**
	 * 权限串码，权限时传入,使用逗号隔开
	 */
	authorityDetail?: string;
	/**
	 * 角色id
	 */
	roleId?: number;
	/**
	 * 类型1手动创建2自动创建
	 */
	type?: number;
	/**
	 * 会话
	 */
	session?: string;
	/**
	 * 短信验证码
	 */
	verifyCode?: string;
	/**
	 * 修改类型,1：修改权限,2：修改账号名称,3：修改密码,4：修改手机号,5：冻结,6:解冻,7：删除
	 */
	editType?: number;
	apiName?: string;
	[k: string]: any;
  }
/**
 * ResponseBody<SubAccountUserDto> :ResponseBody
 */
export interface AuthorityEditSubAccountUserResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * SubAccountUserDto
	 */
	data?: {
	  /**
	   * 表字段:id
	   */
	  id?: number;
	  /**
	   * 账号用户id
	   */
	  userId?: number;
	  /**
	   * 手机号
	   */
	  mobile?: string;
	  /**
	   * 平台店铺id
	   */
	  sellerId?: number;
	  /**
	   * 平台店铺子账号id
	   */
	  subSellerId?: number;
	  /**
	   * 子账号用户名
	   */
	  subAccountName?: string;
	  /**
	   * 密码
	   */
	  password?: string;
	  /**
	   * 权限id
	   */
	  authorityId?: number;
	  /**
	   * 权限id
	   */
	  authorityDetail?: string;
	  /**
	   * 角色id
	   */
	  roleId?: number;
	  /**
	   * 类型1手动创建2自动创建
	   */
	  type?: number;
	  /**
	   * 会话
	   */
	  accessToken?: string;
	  /**
	   * 平台tbpddfxg
	   */
	  platform?: string;
	  /**
	   * 删除标识1已删除0未删除
	   */
	  enableStatus?: number;
	  /**
	   * 登录时间
	   */
	  loginTime?: string;
	  /**
	   * 账号状态1正常0冻结
	   */
	  status?: number;
	  [k: string]: any;
	};
	[k: string]: any;
  }

/**
 * BranchAuthorityReqDTO :BranchAuthorityReqDTO
 */
export interface IndexAuthoritySelectExpressBranchAuthorityRequest {
	/**
	 * 主账户ID
	 */
	userId?: number;
	/**
	 * 子账号ID
	 */
	subUserId?: number;
	[k: string]: any;
  }
  /**
 * ResponseBody<ExpressBranchAuthorityVO> :ResponseBody
 */
export interface IndexAuthoritySelectExpressBranchAuthorityResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * ExpressBranchAuthorityVO
	 */
	data?: {
	  /**
	   * 是否默认全选,true:全选false:不全选,需要根据结果集来勾选
	   */
	  defaultSelectAll?: boolean;
	  /**
	   * 面单类型集合 ,ExpressTypeVO
	   */
	  expressTypeVOList?: {
		/**
		 * 面单类型
		 */
		expressType?: number;
		/**
		 * 网点信息集合 ,BranchVO
		 */
		branchVOList?: {
		  /**
		   * 店铺ID
		   */
		  sellerId?: number;
		  /**
		   * 快递公司编码
		   */
		  exCode?: string;
		  /**
		   * 网点编码
		   */
		  branchCode?: string;
		  /**
		   * 号段编码
		   */
		  segmentCode?: string;
		  [k: string]: any;
		}[];
		[k: string]: any;
	  }[];
	  [k: string]: any;
	};
	[k: string]: any;
  }

/**
 * BranchReqDTO :BranchReqDTO
 */
export interface IndexAuthoritySelectBranchListRequest {
	/**
	 * 用户ID
	 */
	userId?: number;
	/**
	 * 平台
	 */
	platform?: string;
	/**
	 * 店铺ID集合 ,Long
	 */
	sellIdList?: number[];
	[k: string]: any;
  }

/**
 * BaseResponse :BaseResponse
 */
export interface IndexAuthoritySelectBranchListResponse {
	success?: boolean;
	requestId?: string;
	errorMsg?: string;
	[k: string]: any;
  }

export interface PlatformShopDto {
	/**
	 * id
	 */
	id?: number;
	/**
	 * 账号用户id
	 */
	userId?: number;
	/**
	 * 平台店铺方的mallId
	 */
	sellerId?: number;
	/**
	 * 平台店铺名
	 */
	sellerNick?: string;
	/**
	 * 平台店铺简称
	 */
	sellerAbbreviation?: string;
	/**
	 * 平台店铺过期时间
	 */
	sellerExpireTime?: string;
	/**
	 * 平台
	 */
	platform?: string;
	/**
	 * 最后一次授权时间
	 */
	authTime?: string;
	/**
	 * 绑定时间
	 */
	bindTime?: string;
	tokenExpireTime?: string;
	/**
	 * 操作用户id
	 */
	operatorUserId?: number;
	/**
	 * 平台
	 */
	operatorUserName?: string;
	/**
	 * 快递单发件人id ,Integer
	 */
	senderSetting?: number[];
	/**
	 * 快递单发件人 ,String
	 */
	senderSettingList?: string[];
	/**
	 * 发货单发件人id ,Integer
	 */
	invoicesSenderSetting?: number[];
	/**
	 * 发货单发件人 ,String
	 */
	invoicesSenderSettingList?: string[];
	/**
	 * 会话状态0未知1有效
	 */
	tokenStatus?: number;
	/**
	 * 店铺状态0暂停1开启2失效
	 */
	status?: number;
	/**
	 * 删除标识1未删除0已删除
	 */
	enableStatus?: number;
	/**
	 * 刷新token
	 */
	refreshToken?: string;
	/**
	 * accessToken
	 */
	accessToken?: string;
	memberId?: string;
	openId?: string;
	/**
	 * 淘宝平台商家类型0-淘宝1-天猫
	 */
	tbSellerType?: number;
	token?: string;
	/**
	 * 是否淘宝特价版用户
	 */
	tjbSeller?: boolean;
	/**
	 * 外部店铺名称
	 */
	outerSellerNick?: string;
	[k: string]: any;
  }

/**
 * ResponseBody<List<PlatformShopDto>> :ResponseBody
 */
export interface IndexPlatformShopGetUnexpiredPlatformShopListResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * PlatformShopDto
	 */
	data?: PlatformShopDto[];
	[k: string]: any;
  }

/**
 * ResponseBody :ResponseBody
 */
export interface IndexUserGetCaptchaResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    [k: string]: any;
}

/**
* VerifyCodeRequestVo :VerifyCodeRequestVo
*/
export interface IndexUserSendSmsCodeRequest {
    /**
     * 手机号
     */
    mobile?: string;
    /**
     * 发送验证码页面1注册2登录3修改密码
     */
    type?: number;
    apiName?: string;
    verifyCode?: string;
    [k: string]: any;
}

/**
* ResponseBody :ResponseBody
*/
export interface IndexUserSendSmsCodeResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    [k: string]: any;
}

export interface IndexUserChangeUserMobileRequest {
    /**
     * 原手机号
     */
    oldMobile?: string;
    /**
     * 新手机号
     */
    newMobile?: string;
    oldMobileVerifyCode?: string;
    newMobileVerifyCode?: string;
}
/**
 * ResponseBody<UserDto> :ResponseBody
 */
export interface IndexUserChangeUserMobileResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    data?: boolean;
    [k: string]: any;
}


/**
* UserRequestVo :UserRequestVo
*/
export interface IndexUserRegisterRequest {
    /**
     * 用户id
     */
    userId?: number;
    /**
     * 子账号用户id
     */
    subUserId?: number;
    /**
     * 手机号
     */
    mobile?: string;
    /**
     * 账号名
     */
    accountName?: string;
    /**
     * 图形验证码
     */
    captcha?: string;
    /**
     * 短信验证码
     */
    verifyCode?: string;
    /**
     * 密码
     */
    password?: string;
    /**
     * 登录方式1密码登录2验证码登录
     */
    loginType?: number;
    /**
     * 页码
     */
    pageNo?: number;
    /**
     * 页面大小
     */
    pageSize?: number;
    apiName?: string;
    [k: string]: any;
}

/**
* ResponseBody<UserDto> :ResponseBody
*/
export interface IndexUserRegisterResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * UserDto
     */
    data?: {
        /**
         * 用户id
         */
        userId?: number;
        /**
         * 用户昵称
         */
        userName?: string;
        /**
         * 子账号用户id
         */
        subUserId?: number;
        /**
         * 子账号用户名
         */
        subUserName?: string;
        /**
         * 密码
         */
        password?: string;
        /**
         * 手机号
         */
        mobile?: string;
        /**
         * 分库分表配置
         */
        dbConfig?: string;
        /**
         * 登录时间
         */
        loginTime?: string;
        /**
         * 用户版本
         */
        version?: number;
        /**
         * 过期时间
         */
        expireTime?: string;
        /**
         * 可用单量
         */
        quantity?: number;
        status?: number;
        /**
         * 删除标识1已删除0未删除
         */
        enableStatus?: number;
        /**
         * 平台店铺id
         */
        sellerId?: number;
        /**
         * 平台店铺子账号id
         */
        subSellerId?: number;
        [k: string]: any;
    };
    [k: string]: any;
}

/**
* UserRequestVo :UserRequestVo
*/
export interface IndexUserLoginRequest {
    /**
     * 用户id
     */
    userId?: number;
    /**
     * 子账号用户id
     */
    subUserId?: number;
    /**
     * 手机号
     */
    mobile?: string;
    /**
     * 账号名
     */
    accountName?: string;
    /**
     * 图形验证码
     */
    captcha?: string;
    /**
     * 短信验证码
     */
    verifyCode?: string;
    /**
     * 密码
     */
    password?: string;
    /**
     * 登录方式1密码登录2验证码登录
     */
    loginType?: number;
    /**
     * 页码
     */
    pageNo?: number;
    /**
     * 页面大小
     */
    pageSize?: number;
    apiName?: string;
    [k: string]: any;
}

export interface IShopInfo {
    /**
     * id
     */
    id?: number;
    /**
     * 账号用户id
     */
    userId?: number;
    /**
     * 平台店铺方的mallId
     */
    sellerId?: string;
    /**
     * 平台店铺名
     */
    sellerNick?: string;
    /**
     * 平台店铺简称
     */
    sellerAbbreviation?: string;
    /**
     * 平台店铺过期时间
     */
    sellerExpireTime?: string;
    /**
     * 平台
     */
    platform?: platform;
    /**
     * accessToken
     */
    accessToken?: string;
    /**
     * 最后一次授权时间
     */
    authTime?: string;
    /**
     * 绑定时间
     */
    bindTime?: string;
    /**
     * 操作用户id
     */
    operatorUserId?: number;
    /**
     * 平台
     */
    operatorUserName?: string;
    /**
     * 启用状态1开启0暂停 2过期
     */
    status?: number;
    [k: string]: any;
}
/**
* ResponseBody<UserDto> :ResponseBody
*/
export interface IndexUserLoginResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * UserDto
     */
    data?: {
        /**
         * 用户id
         */
        userId?: number;
        /**
         * 用户昵称
         */
        userName?: string;
        /**
         * 子账号用户id
         */
        subUserId?: number;
        /**
         * 子账号用户名
         */
        subUserName?: string;
        /**
         * 密码
         */
        password?: string;
        /**
         * 手机号
         */
        mobile?: string;
        /**
         * 分库分表配置
         */
        dbConfig?: string;
        /**
         * 登录时间
         */
        loginTime?: string;
        /**
         * 用户版本
         */
        version?: number;
        /**
         * 过期时间
         */
        expireTime?: string;
        /**
         * 可用单量
         */
        quantity?: number;
        status?: number;
        /**
         * 删除标识1已删除0未删除
         */
        enableStatus?: number;
        /**
         * 平台店铺id
         */
        sellerId?: number;
        /**
         * 平台店铺子账号id
         */
        subSellerId?: number;
        [k: string]: any;
    };
    [k: string]: any;
}


/**
* UserRequestVo :UserRequestVo
*/
export interface IndexUserCheckMobileRequest {
    /**
     * 用户id
     */
    userId?: number;
    /**
     * 子账号用户id
     */
    subUserId?: number;
    /**
     * 手机号
     */
    mobile?: string;
    /**
     * 账号名
     */
    accountName?: string;
    /**
     * 图形验证码
     */
    captcha?: string;
    /**
     * 短信验证码
     */
    verifyCode?: string;
    /**
     * 密码
     */
    password?: string;
    /**
     * 登录方式1密码登录2验证码登录
     */
    loginType?: number;
    /**
     * 页码
     */
    pageNo?: number;
    /**
     * 页面大小
     */
    pageSize?: number;
    apiName?: string;
    [k: string]: any;
}

/**
* ResponseBody<Boolean> :ResponseBody
*/
export interface IndexUserCheckMobileResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * data
     */
    data?: boolean;
    [k: string]: any;
}


/**
* ResponseBody<UserDto> :ResponseBody
*/
export interface IndexUserGetUserInfoResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * UserDto
     */
    data?: UserInfoInf;
    [k: string]: any;
}

export interface UserInfoInf {
    /**
     * 用户id
     */
    userId?: number;
    /**
     * 用户昵称
     */
    userName?: string;
    /**
     * 子账号用户id
     */
    subUserId?: number;
    /**
     * 子账号用户名
     */
    subUserName?: string;
    /**
     * 密码
     */
    password?: string;
    /**
     * 手机号
     */
    mobile?: string;
    /**
     * 分库分表配置
     */
    dbConfig?: string;
    /**
     * 登录时间
     */
    loginTime?: string;
    /**
     * 用户版本
     */
    version?: number;
    /**
     * 过期时间
     */
    expireTime?: string;
    /**
     * 可用单量
     */
    quantity?: number;
    status?: number;
    /**
     * 删除标识1已删除0未删除
     */
    enableStatus?: number;
    /**
     * 平台店铺id
     */
    sellerId?: number;
    /**
     * 平台店铺子账号id
     */
    subSellerId?: number;

    whiteListSetting?: string;

    /**
     * 用户权限id
     */
    authorityDetail?: string;
    remainingTime?: string;
    shopNum?: number;
    level?: 0 | 1 | 2 | 3; // 免费版 ｜ 标配版（老用户） ｜ 高配版 | 标配版
    [k: string]: any;
}
/**
* UserVersionRecordRequestVo :UserVersionRecordRequestVo
*/
export interface IndexUserChooseVersionRecordRequest {
    /**
     * 用户版本1库存版2零库存版
     */
    version?: number;
    apiName?: string;
    [k: string]: any;
}

/**
* ResponseBody<UserDto> :ResponseBody
*/
export interface IndexUserChooseVersionRecordResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * UserDto
     */
    data?: {
        /**
         * 用户id
         */
        userId?: number;
        /**
         * 用户昵称
         */
        userName?: string;
        /**
         * 子账号用户id
         */
        subUserId?: number;
        /**
         * 子账号用户名
         */
        subUserName?: string;
        /**
         * 密码
         */
        password?: string;
        /**
         * 手机号
         */
        mobile?: string;
        /**
         * 分库分表配置
         */
        dbConfig?: string;
        /**
         * 登录时间
         */
        loginTime?: string;
        /**
         * 用户版本
         */
        version?: number;
        /**
         * 过期时间
         */
        expireTime?: string;
        /**
         * 可用单量
         */
        quantity?: number;
        status?: number;
        /**
         * 删除标识1已删除0未删除
         */
        enableStatus?: number;
        /**
         * 平台店铺id
         */
        sellerId?: number;
        /**
         * 平台店铺子账号id
         */
        subSellerId?: number;
        [k: string]: any;
    };
    [k: string]: any;
}

/**
 * UserRequestVo :UserRequestVo
 */
export interface IndexUserChangePasswordRequest {
    /**
     * 用户id
     */
    userId?: number;
    /**
     * 子账号用户id
     */
    subUserId?: number;
    /**
     * 手机号
     */
    mobile?: string;
    /**
     * 账号名
     */
    accountName?: string;
    /**
     * 图形验证码
     */
    captcha?: string;
    /**
     * 短信验证码
     */
    verifyCode?: string;
    /**
     * 密码
     */
    password?: string;
    /**
     * 登录方式1密码登录2验证码登录
     */
    loginType?: number;
    /**
     * 页码
     */
    pageNo?: number;
    /**
     * 页面大小
     */
    pageSize?: number;
    apiName?: string;
    [k: string]: any;
}

/**
* ResponseBody :ResponseBody
*/
export interface IndexUserChangePasswordResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    [k: string]: any;
}

/**
 * PlatformShopQueryRequestVo :PlatformShopQueryRequestVo
 */
export interface PlatformShopGetPlatformShopsRequest {
    /**
     * 平台店铺tbpddfxg
     */
    platformType?: {
        [k: string]: any;
    };
    /**
     * 店铺状态1已绑定（开启）0暂停
     */
    status?: number;
    /**
     * 店铺名称
     */
    sellerNick?: string;
    apiName?: string;
    [k: string]: any;
}

/**
* ResponseBody<PageList<PlatformShopDto>> :ResponseBody
*/
export interface PlatformShopGetPlatformShopsResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * PageList
     */
    data?: {
        pageNo?: number;
        pageSize?: number;
        total?: number;
        /**
         * T
         */
        list?: IShopInfo[];
        [k: string]: any;
    };
    [k: string]: any;
}

/**
 * SystemRequestVo :SystemRequestVo
 */
export interface SettingGetSystemSettingRequest {
    /**
     * id
     */
    id?: number;
    /**
     * 用户id
     */
    userId?: number;
    /**
     * 同步修改平台商家编码1同步0不同步
     */
    syncModifyProductCode?: number;
    /**
     * 同步修改平台商家编码1同步0不同步
     */
    orderOutboundWay?: number;
    /**
     * 店铺到期提醒时间
     */
    expireRemindDays?: number;
    /**
     * 订单是否需要扣减库存0不需要1需要
     */
    inventoryDeduct?: number;
    /**
     * 订单扣减库存时间
     */
    inventoryDeductTime?: string;
    [k: string]: any;
}

/**
 * ResponseBody<SystemSettingDto> :ResponseBody
 */
export interface SettingGetSystemSettingResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * SystemSettingDto
     */
    data?: {
        /**
         * 用户id
         */
        userId?: number;
        /**
         * 同步修改平台商家编码1同步0不同步
         */
        syncModifyProductCode?: number;
        /**
         * 同步修改平台商家编码1同步0不同步
         */
        orderOutboundWay?: number;
        /**
         * 店铺到期提醒时间
         */
        expireRemindDays?: number;
        /**
         * 订单是否需要扣减库存0不需要1需要
         */
        inventoryDeduct?: number;
        /**
         * 订单扣减库存时间
         */
        openAbnormalCheck?: number;
        inventoryDeductTime?: any;
        groupStockNumOutWay?: number; // 0 出库子商品  1 出库组合商品
        readOnlyFields?: string[]; // 大促降级字段
        readOnlyDesc?: string; // 大促降级文案
        dropShippingFlag?: number; // 0关闭 1开启 默认0
        printTagSet?: string;
        [k: string]: any;
    };
    [k: string]: any;
}

export interface setSystemSettingInf {
    setSystemSettingInf?: number;
    /**
        * 同步修改平台商家编码1同步0不同步
        */
    syncModifyProductCode?: number;
    /**
     * 同步修改平台商家编码1同步0不同步
     */
    orderOutboundWay?: number;
    /**
     * 店铺到期提醒时间
     */
    expireRemindDays?: number;
    /**
     * 订单是否需要扣减库存0不需要1需要
     */
    inventoryDeduct?: number;
    /**
     * 订单扣减库存时间
     */
    inventoryDeductTime?: string;
    /**
     * 系统剩余发货时间设置
     */
    systemLastShipTime?: number;
    /**
     * 平台店铺手工单支持剩余发货时间配置
     */
    handOrderShipTime?: number;
    [k: string]: any;
}
export interface setUserSettingInf {
    /**
        * 同步修改平台商家编码1同步0不同步
        */
    syncModifyProductCode?: number;
    /**
     * 同步修改平台商家编码1同步0不同步
     */
    orderOutboundWay?: number;
    /**
     * 店铺到期提醒时间
     */
    expireRemindDays?: number;
    /**
     * 订单是否需要扣减库存0不需要1需要
     */
    inventoryDeduct?: number;
    /**
     * 订单扣减库存时间
     */
    inventoryDeductTime?: string;
    [k: string]: any;
}

/**
 * UserSettingRequestVo :UserSettingRequestVo
 */

/**
 * UserSettingRequestVo :UserSettingRequestVo
 */
export interface SettingGetUserSettingRequest {
    weightUnit?: number;
    openAbnormalCheck?: number;
    abnormalAddressCheck?: number;
    abnormalExpressArriveCheck?: number;
    abnormalRefundCheck?: number;
    abnormalStockWarnCheck?: number;
    longStayWithoutCheck?: number;
    longStayWithoutTime?: number;
    extraSetting?: any;
    lowProfitTradeConfig?:any
    /**
     * id
     */
    id?: number;
    /**
     * 账号用户id
     */
    userId?: number;
    /**
     * 子账号用户id
     */
    subUserId?: number;
    /**
     * 登录后同步订单1同步0不同步
     */
    syncOrder?: number;
    /**
     * 打印后是否提醒发货1是0否
     */
    remindShip?: number;
    /**
     * 是否显示模板余额1是0否
     */
    templateBalance?: number;
    // *
    autoSearch?: number;
    isShowBranches?: number;
}

/**
* ResponseBody<UserSettingDto> :ResponseBody
*/
export interface SettingGetUserSettingResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * UserSettingDto
     */
    data?: {
        /**
         * 账号用户id
         */
        userId?: number;
        /**
         * 子账号用户id
         */
        subUserId?: number;
        /**
         * 登录后同步订单1同步0不同步
         */
        syncOrder?: number;
        /**
         * 打印后是否提醒发货1是0否
         */
        remindShip?: number;
        /**
         * 是否显示模板余额1是0否
         */
        templateBalance?: number;
        isShowBranches?: number; // 是否展示网点名称
        readOnlyFields?: string[]; // 大促降级字段
        readOnlyDesc?: string; // 大促降级文案
        [k: string]: any;
    };
}

/**
 * SystemRequestVo :SystemRequestVo
 */
export interface SettingSaveSystemSettingRequest {
    /**
     * id
     */
    id?: number;
    /**
     * 用户id
     */
    userId?: number;
    /**
     * 同步修改平台商家编码1同步0不同步
     */
    syncModifyProductCode?: number;
    /**
     * 同步修改平台商家编码1同步0不同步
     */
    orderOutboundWay?: number;
    /**
     * 店铺到期提醒时间
     */
    expireRemindDays?: number;
    /**
     * 订单是否需要扣减库存0不需要1需要
     */
    inventoryDeduct?: number;
    /**
     * 订单扣减库存时间
     */
    inventoryDeductTime?: string;
    dropShippingFlag?: number;
    [k: string]: any;
}


/**
* ResponseBody<SystemSettingDto> :ResponseBody
*/
export interface SettingSaveSystemSettingResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * SystemSettingDto
     */
    data?: {
        /**
         * 用户id
         */
        userId?: number;
        /**
         * 同步修改平台商家编码1同步0不同步
         */
        syncModifyProductCode?: number;
        /**
         * 同步修改平台商家编码1同步0不同步
         */
        orderOutboundWay?: number;
        /**
         * 店铺到期提醒时间
         */
        expireRemindDays?: number;
        /**
         * 订单是否需要扣减库存0不需要1需要
         */
        inventoryDeduct?: number;
        /**
         * 订单扣减库存时间
         */
        inventoryDeductTime?: string;
        [k: string]: any;
    };
    [k: string]: any;
}

/**
 * UserSettingRequestVo :UserSettingRequestVo
 */
export interface SettingSaveUserSettingRequest {
    /**
     * id
     */
    id?: number;
    /**
     * 账号用户id
     */
    userId?: number;
    /**
     * 子账号用户id
     */
    subUserId?: number;
    /**
     * 登录后同步订单1同步0不同步
     */
    syncOrder?: number;
    /**
     * 打印后是否提醒发货1是0否
     */
    remindShip?: number;
    /**
     * 是否显示模板余额1是0否
     */
    templateBalance?: number;
    [k: string]: any;
}

/**
 * ResponseBody<UserSettingDto> :ResponseBody
 */
export interface SettingSaveUserSettingResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * UserSettingDto
     */
    data?: {
        /**
         * 账号用户id
         */
        userId?: number;
        /**
         * 子账号用户id
         */
        subUserId?: number;
        /**
         * 登录后同步订单1同步0不同步
         */
        syncOrder?: number;
        /**
         * 打印后是否提醒发货1是0否
         */
        remindShip?: number;
        /**
         * 是否显示模板余额1是0否
         */
        templateBalance?: number;
        [k: string]: any;
    };
    [k: string]: any;
}

/**
 * SubAccountUserRequestVo :SubAccountUserRequestVo
 */
export interface AuthoritySaveSubAccountUserRequest {
    /**
     * 表字段:id
     */
    id?: number;
    /**
     * 账号用户id
     */
    userId?: number;
    /**
     * 手机号
     */
    mobile?: string;
    /**
     * 子账号用户名
     */
    subAccountName?: string;
    /**
     * 密码
     */
    password?: string;
    /**
     * 权限id
     */
    authorityId?: number;
    /**
     * 权限id
     */
    authorityDetail?: string;
    /**
     * 角色id
     */
    roleId?: number;
    /**
     * 类型1手动创建2自动创建
     */
    type?: number;
    /**
     * 会话
     */
    session?: string;
    [k: string]: any;
}

/**
 * ResponseBody<SubAccountUserDto> :ResponseBody
 */
export interface AuthoritySaveSubAccountUserResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * SubAccountUserDto
     */
    data?: {
        /**
         * 表字段:id
         */
        id?: number;
        /**
         * 账号用户id
         */
        userId?: number;
        /**
         * 手机号
         */
        mobile?: string;
        /**
         * 子账号用户名
         */
        subAccountName?: string;
        /**
         * 密码
         */
        password?: string;
        /**
         * 权限id
         */
        authorityId?: number;
        /**
         * 权限id
         */
        authorityDetail?: string;
        /**
         * 角色id
         */
        roleId?: number;
        /**
         * 类型1手动创建2自动创建
         */
        type?: number;
        /**
         * 会话
         */
        session?: string;
        [k: string]: any;
    };
    [k: string]: any;
}

/**
 * SubAccountUserQueryVo :SubAccountUserQueryVo
 */
export interface AuthorityGetSubAccountUserListRequest {
    /**
     * 用户名称
     */
    userName?: string;
    /**
     * 手机号
     */
    mobile?: string;
    /**
     * 查询类型
     */
    type?: string;
    /**
     * 账号状态
     */
    status?: number;
    userId?: number;
    apiName?: string;
    [k: string]: any;
}


/**
* ResponseBody<List<SubAccountUserDto>> :ResponseBody
*/
export interface AuthorityGetSubAccountUserListResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * SubAccountUserDto
     */
    data?: AuthorityGetSubAccountUserListObj[];
    [k: string]: any;
}

export interface AuthorityGetSubAccountUserListObj {
    /**
     * 表字段:id
     */
    id?: number;
    /**
     * 账号用户id
     */
    userId?: number;
    /**
     * 手机号
     */
    mobile?: string;
    /**
     * 平台店铺id
     */
    sellerId?: number;
    /**
     * 平台店铺子账号id
     */
    subSellerId?: number;
    /**
     * 子账号用户名
     */
    subAccountName?: string;
    /**
     * 密码
     */
    password?: string;
    /**
     * 权限id
     */
    authorityId?: number;
    /**
     * 权限id
     */
    authorityDetail?: string;
    /**
     * 角色id
     */
    roleId?: number;
    /**
     * 类型1手动创建2自动创建
     */
    type?: number;
    /**
     * 会话
     */
    accessToken?: string;
    /**
     * 平台tbpddfxg
     */
    platform?: string;
    /**
     * 删除标识1已删除0未删除
     */
    enableStatus?: number;
    /**
     * 登录时间
     */
    loginTime?: string;
    /**
     * 账号状态1正常0冻结
     */
    status?: number;
    dataAuthority?: string;
    funcAuthority?: string;
    [k: string]: any;
}

/**
 * SenderSettingQueryRequestVo :SenderSettingQueryRequestVo
 */
export interface SettingGetSenderSettingRequest {
    /**
     * id
     */
    id?: number;
    /**
     * 账号用户id
     */
    userId?: number;
    /**
     * 平台店铺id
     */
    sellerId?: number;
    /**
     * 发件人姓名手机号
     */
    senderNameOrMobile?: string;
    [k: string]: any;
}

/**
 * ResponseBody<PageList<SenderSettingDto>> :ResponseBody
 */
export interface SettingGetSenderSettingResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * PageList
     */
    data?: {
        pageNo?: number;
        pageSize?: number;
        total?: number;
        /**
         * T
         */
        list?: SettingGetSenderSettingObj[];
        [k: string]: any;
    };
    [k: string]: any;
}

export interface SettingGetSenderSettingObj {
    /**
     * id
     */
    id?: number;
    /**
     * 账号用户id
     */
    userId?: number;
    /**
     * 平台店铺方的mallId
     */
    sellerId?: string;
    /**
     * 发件人姓名
     */
    senderName?: string;
    /**
     * 发件人手机号
     */
    senderMobile?: string;
    /**
     * 发件人电话号
     */
    senderPhone?: string;
    /**
     * 发件人邮编
     */
    senderPostCode?: string;
    /**
     * 发件人省
     */
    senderProvince?: string;
    /**
     * 发件人市
     */
    senderCity?: string;
    /**
     * 发件人区
     */
    senderDistrict?: string;
    /**
     * 发件人详细地址
     */
    senderAddressDetail?: string;
    /**
     * 是否是默认1是0否
     */
    isDefault?: number;
    /**
     * 删除标识1已删除0未删除
     */
    enableStatus?: number;
    [k: string]: any;
}

/**
 * RecipientSettingQueryRequestVo :RecipientSettingQueryRequestVo
 */
export interface SettingGetRecipientSettingRequest {
    /**
     * id
     */
    id?: number;
    /**
     * 账号用户id
     */
    userId?: number;
    /**
     * 平台店铺id
     */
    sellerId?: number;
    /**
     * 发件人姓名手机号
     */
    recipientNameOrMobile?: string;
    [k: string]: any;
}


/**
* ResponseBody<PageList<RecipientSettingDto>> :ResponseBody
*/
export interface SettingGetRecipientSettingResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * PageList
     */
    data?: {
        pageNo?: number;
        pageSize?: number;
        total?: number;
        /**
         * T
         */
        list?: SettingGetRecipientSettingResponseObj[];
        [k: string]: any;
    };
    [k: string]: any;
}

export interface SettingGetRecipientSettingResponseObj {
    /**
     * id
     */
    id?: number;
    /**
     * 账号用户id
     */
    userId?: number;
    /**
     * 平台店铺方的mallId
     */
    sellerId?: string;
    /**
     * 收件人姓名
     */
    recipientName?: string;
    /**
     * 收件人昵称
     */
    recipientNick?: string;
    /**
     * 收件人手机号
     */
    recipientMobile?: string;
    /**
     * 收件人电话号
     */
    recipientPhone?: string;
    /**
     * 收件人省
     */
    recipientProvince?: string;
    /**
     * 收件人市
     */
    recipientCity?: string;
    /**
     * 收件人区
     */
    recipientDistrict?: string;
    /**
     * 收件人详细地址
     */
    recipientAddressDetail?: string;
    /**
     * 删除标识1已删除0未删除
     */
    enableStatus?: number;
    // 置顶时间
    setTopTime?: string;
    [k: string]: any;
}

/**
 * PlatformShopQueryRequestVo :PlatformShopQueryRequestVo
 */
export interface IndexPlatformShopGetPlatformShopsRequest {
    /**
     * 平台店铺tbpddfxg
     */
    platformType?: {
        [k: string]: any;
    };
    /**
     * 店铺状态0暂停1开启2失效
     */
    status?: number;
    /**
     * 店铺名称
     */
    sellerNick?: string;
    userId?: number;
    /**
     * 页码
     */
    pageNo?: number;
    /**
     * 页面大小
     */
    pageSize?: number;
    apiName?: string;
    refresh?: 0 | 1;
    [k: string]: any;
}

/**
 * ResponseBody<PageList<PlatformShopDto>> :ResponseBody
 */
export interface IndexPlatformShopGetPlatformShopsResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * PageList
     */
    data?: {
        pageNo?: number;
        pageSize?: number;
        total?: number;
        /**
         * T
         */
        list?: IndexPlatformShopGetPlatformShopsResponseObj[];
        [k: string]: any;
    };
    [k: string]: any;
}

export interface IndexPlatformShopGetPlatformShopsResponseObj {
    /**
     * id
     */
    id?: number;
    /**
     * 账号用户id
     */
    userId?: number;
    /**
     * 平台店铺方的mallId
     */
    sellerId?: number;
    /**
     * 平台店铺名
     */
    sellerNick?: string;
    /**
     * 平台店铺简称
     */
    sellerAbbreviation?: string;
    /**
     * 平台店铺过期时间
     */
    sellerExpireTime?: string;
    /**
     * 平台
     */
    platform?: platform;
    /**
     * 最后一次授权时间
     */
    authTime?: string;
    /**
     * 绑定时间
     */
    bindTime?: string;
    /**
     * 操作用户id
     */
    operatorUserId?: number;
    /**
     * 平台
     */
    operatorUserName?: string;
    /**
     * 快递单发件人id ,Integer
     */
    senderSetting?: number[];
    /**
     * 快递单发件人 ,String
     */
    senderSettingList?: string[];
    /**
     * 发货单发件人id ,Integer
     */
    invoicesSenderSetting?: number[];
    /**
     * 发货单发件人 ,String
     */
    invoicesSenderSettingList?: string[];
    /**
     * 会话状态0未知1有效
     */
    tokenStatus?: number;
    /**
     * 店铺状态0暂停1开启2失效
     */
    status?: number;
    /**
     * 删除标识1未删除0已删除
     */
    enableStatus?: number;
    /**
     * 刷新token
     */
    refreshToken?: string;
    /**
     * accessToken
     */
    accessToken?: string;
    [k: string]: any;
}

/**
 * PlatformShopEditRequestVo :PlatformShopEditRequestVo
 */
export interface IndexPlatformShopEditPlatformShopRequest {
    /**
     * id
     */
    id?: number;
    /**
     * 店铺状态0暂停1开启2失效暂停和开启时传值其余可不传
     */
    status?: number;
    /**
     * 删除标识true删除其余不删（不删除时可以不传）
     */
    isDelete?: boolean;
    /**
     * 快递单发件人 ,Integer
     */
    senderSetting?: number[];
    /**
     * 发货单发件人 ,Integer
     */
    invoicesSenderSetting?: number[];
    userId?: number;
    apiName?: string;
    [k: string]: any;
}


/**
* ResponseBody :ResponseBody
*/
export interface IndexPlatformShopEditPlatformShopResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    [k: string]: any;
}

/**
 * SenderSettingQueryRequestVo :SenderSettingQueryRequestVo
 */
export interface IndexSettingGetSenderSettingRequest {
    /**
     * id
     */
    id?: number;
    /**
     * 账号用户id
     */
    userId?: number;
    /**
     * 发件人姓名
     */
    senderName?: string;
    /**
     * 发件人手机号
     */
    senderMobile?: string;
    /**
     * 页码
     */
    pageNo?: number;
    /**
     * 页面大小
     */
    pageSize?: number;
    apiName?: string;
    [k: string]: any;
}


/**
* ResponseBody<PageList<SenderSettingDto>> :ResponseBody
*/
export interface IndexSettingGetSenderSettingResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * PageList
     */
    data?: {
        pageNo?: number;
        pageSize?: number;
        total?: number;
        /**
         * T
         */
        list?: IndexSettingGetSenderSettingResponseObj[];
        [k: string]: any;
    };
    [k: string]: any;
}

export interface IndexSettingGetSenderSettingResponseObj {
    /**
     * id
     */
    id?: number;
    /**
     * 账号用户id
     */
    userId?: number;
    /**
     * 发件人姓名
     */
    senderName?: string;
    /**
     * 发件签名
     */
    senderSign?: string;
    /**
     * 发件人手机号
     */
    senderMobile?: string;
    /**
     * 发件人电话号
     */
    senderPhone?: string;
    /**
     * 发件人邮编
     */
    senderPostCode?: string;
    /**
     * 发件人省
     */
    senderProvince?: string;
    /**
     * 发件人市
     */
    senderCity?: string;
    /**
     * 发件人区
     */
    senderDistrict?: string;
    /**
     * 发件人详细地址
     */
    senderAddressDetail?: string;
    /**
     * 是否是默认1是0否
     */
    isDefault?: number;
    /**
     * 删除标识1已删除0未删除
     */
    enableStatus?: number;
    [k: string]: any;
}


/**
 * SenderSettingQueryRequestVo :SenderSettingQueryRequestVo
 */
export interface IndexSettingGetInvoicesSenderSettingRequest {
    /**
     * id
     */
    id?: number;
    /**
     * 账号用户id
     */
    userId?: number;
    /**
     * 发件人姓名
     */
    senderName?: string;
    /**
     * 发件人手机号
     */
    senderMobile?: string;
    /**
     * 页码
     */
    pageNo?: number;
    /**
     * 页面大小
     */
    pageSize?: number;
    apiName?: string;
    [k: string]: any;
}


/**
* ResponseBody<PageList<InvoicesSenderSettingDto>> :ResponseBody
*/
export interface IndexSettingGetInvoicesSenderSettingResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * PageList
     */
    data?: {
        pageNo?: number;
        pageSize?: number;
        total?: number;
        /**
         * T
         */
        list?: {
            /**
             * id
             */
            id?: number;
            /**
             * 账号用户id
             */
            userId?: number;
            /**
             * 发货标题
             */
            shippingTitle?: string;
            /**
             * 经办人
             */
            manager?: string;
            /**
             * 店铺名称
             */
            shopName?: string;
            /**
             * 联系电话
             */
            mobile?: string;
            /**
             * 发件地址
             */
            senderAddress?: string;
            /**
             * 友情提示
             */
            tips?: string;
            /**
             * 是否是默认1是0否
             */
            isDefault?: number;
            /**
             * 删除标识0已删除1未删除
             */
            enableStatus?: number;
            [k: string]: any;
        }[];
        [k: string]: any;
    };
    [k: string]: any;
}

/**
 * SenderSettingRequestVo :SenderSettingRequestVo
 */
export interface IndexSettingEditSenderSettingRequest {
    /**
     * id
     */
    id?: number;
    /**
     * 账号用户id
     */
    userId?: number;
    /**
     * 发件人姓名
     */
    senderName?: string;
    /**
     * 发件人手机号
     */
    senderMobile?: string;
    /**
     * 发件人电话号
     */
    senderPhone?: string;
    /**
     * 发件人邮编
     */
    senderPostCode?: string;
    /**
     * 发件人省
     */
    senderProvince?: string;
    /**
     * 发件人市
     */
    senderCity?: string;
    /**
     * 发件人区
     */
    senderDistrict?: string;
    /**
     * 发件人详细地址
     */
    senderAddressDetail?: string;
    /**
     * 是否是默认1是0否
     */
    isDefault?: number;
    /**
     * 删除标识true删除其余不传
     */
    isDelete?: boolean;
    apiName?: string;
    [k: string]: any;
}

/**
* ResponseBody :ResponseBody
*/
export interface IndexSettingEditSenderSettingResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    [k: string]: any;
}

/**
 * InvoicesSenderSettingRequestVo :InvoicesSenderSettingRequestVo
 */
export interface IndexSettingEditInvoicesSenderSettingRequest {
    /**
     * id
     */
    id?: number;
    /**
     * 账号用户id
     */
    userId?: number;
    /**
     * 发货标题
     */
    shippingTitle?: string;
    /**
     * 经办人
     */
    manager?: string;
    /**
     * 店铺名称
     */
    shopName?: string;
    /**
     * 联系电话
     */
    mobile?: string;
    /**
     * 发件地址
     */
    senderAddress?: string;
    /**
     * 友情提示
     */
    tips?: string;
    /**
     * 删除标识true删除其余不传
     */
    isDelete?: boolean;
    apiName?: string;
    [k: string]: any;
}


/**
* ResponseBody :ResponseBody
*/
export interface IndexSettingEditInvoicesSenderSettingResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    [k: string]: any;
}


/**
 * RecipientSettingEditRequestVo :RecipientSettingEditRequestVo
 */
export interface IndexSettingEditRecipientSettingRequest {
    /**
     * id
     */
    id?: number;
    /**
     * 是否设置成置顶
     */
    isSetToTop?: boolean;
    /**
     * 账号用户id
     */
    userId?: number;
    /**
     * 收件人姓名
     */
    recipientName?: string;
    /**
     * 收件人昵称
     */
    recipientNick?: string;
    /**
     * 收件人手机号
     */
    recipientMobile?: string;
    /**
     * 收件人电话号
     */
    recipientPhone?: string;
    /**
     * 收件人省
     */
    recipientProvince?: string;
    /**
     * 收件人市
     */
    recipientCity?: string;
    /**
     * 收件人区
     */
    recipientDistrict?: string;
    /**
     * 收件人详细地址
     */
    recipientAddressDetail?: string;
    /**
     * 删除标识true删除其余不传
     */
    isDelete?: boolean;
    apiName?: string;
    [k: string]: any;
}

/**
* ResponseBody :ResponseBody
*/
export interface IndexSettingEditRecipientSettingResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    [k: string]: any;
}

/**
 * SubAccountUserRequestVo :SubAccountUserRequestVo
 */
export interface IndexAuthorityEditSubAccountUserRequest {
    /**
     * 表字段:id,编辑时需要填入
     */
    id?: number;
    /**
     * 账号用户id
     */
    userId?: number;
    /**
     * 手机号
     */
    mobile?: string;
    /**
     * 子账号用户名
     */
    subAccountName?: string;
    /**
     * 密码
     */
    password?: string;
    /**
     * 权限id
     */
    authorityId?: number;
    /**
     * 权限串码，编辑权限时传入,使用逗号隔开
     */
    authorityDetail?: string;
    /**
     * 店铺权限
     */
    shopAuthority?: string;
    /**
     * 功能权限（不包含）
     */
    funcAuthority?: string;
    /**
     * 本次取消勾选权限集合 ,String
     */
    cancelAuthorityList?: string[];
    /**
     * 本次新增勾选权限集合 ,String
     */
    increasedAuthorityList?: string[];
    /**
     * 数据权限（不包含）
     */
    dataAuthority?: string;
    /**
     * 角色id
     */
    roleId?: number;
    /**
     * 类型1手动创建2自动创建
     */
    type?: number;
    /**
     * 会话
     */
    session?: string;
    /**
     * 短信验证码
     */
    verifyCode?: string;
    /**
     * 修改类型,1：修改权限,2：修改账号名称,3：修改密码,4：修改手机号,5：冻结,6:解冻,7：删除
     */
    editType?: number;
    apiName?: string;
    [k: string]: any;
}



/**
 * ResponseBody<SubAccountUserDto> :ResponseBody
 */
export interface IndexAuthorityEditSubAccountUserResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * SubAccountUserDto
     */
    data?: {
        /**
         * 表字段:id
         */
        id?: number;
        /**
         * 账号用户id
         */
        userId?: number;
        /**
         * 手机号
         */
        mobile?: string;
        /**
         * 平台店铺id
         */
        sellerId?: number;
        /**
         * 平台店铺子账号id
         */
        subSellerId?: number;
        /**
         * 子账号用户名
         */
        subAccountName?: string;
        /**
         * 密码
         */
        password?: string;
        /**
         * 权限id
         */
        authorityId?: number;
        /**
         * 权限id
         */
        authorityDetail?: string;
        /**
         * 店铺权限
         */
        shopAuthority?: string;
        /**
         * 功能权限（不包含）
         */
        funcAuthority?: string;
        /**
         * 数据权限（不包含）
         */
        dataAuthority?: string;
        /**
         * 角色id
         */
        roleId?: number;
        /**
         * 类型1手动创建2自动创建
         */
        type?: number;
        /**
         * 会话
         */
        accessToken?: string;
        /**
         * 平台tbpddfxg
         */
        platform?: string;
        /**
         * 删除标识1已删除0未删除
         */
        enableStatus?: number;
        /**
         * 登录时间
         */
        loginTime?: string;
        /**
         * 账号状态1正常0冻结
         */
        status?: number;
        /**
         * 是否是主账号
         */
        isMainAccount?: boolean;
        /**
         * 是否设置密码
         */
        isSetPassword?: boolean;
        /**
         * 有无售后权限0-无1-有
         */
        hasAfterSaleAuthority?: number;
        [k: string]: any;
    };
    [k: string]: any;
}


/**
 * PopupSettingEditRequestVo :PopupSettingEditRequestVo
 */
export interface IndexSettingEditPopupSettingRequest {
    /**
     * id
     */
    id?: number;
    /**
     * 账号用户id
     */
    userId?: number;
    /**
     * 设置
     */
    popupSetting?: any;
    apiName?: string;
    [k: string]: any;
}


/**
* ResponseBody :ResponseBody
*/
export interface IndexSettingEditPopupSettingResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    [k: string]: any;
}

/**
 * ReqOperateShopParamsDTO :ReqOperateShopParamsDTO
 */
export interface OperateShopRequest {
    /**
     * 操作类型,1添加,2停用删除,3重新启用
     */
    opType?: number;
    /**
     * 店铺ID
     */
    sellerId?: string;
    /**
     * 平台
     */
    platform?: string;
    [k: string]: any;
}

export interface indexBuyerRefundCountInf {
    platformType: string;
    count: string;
    sellerNick: string;
}

/**
 * FeedbackRequestVo :FeedbackRequestVo
 */
export interface IndexSaveFeedbackRequest {
    /**
     * 不传
     */
    userId?: number;
    /**
     * 不传
     */
    subUserId?: number;
    /**
     * 意见反馈
     */
    content?: string;
    /**
     * 联系方式
     */
    contactDetails?: string;
    apiName?: string;
    [k: string]: any;
}

export interface orderViewInf {
    key: string;
    totalCount: string | number;
    icon: any;
    title: String;
    list: indexBuyerRefundCountInf[];
    path: string;
}

/**
 * CommonRemarkQueryRequestVo :CommonRemarkQueryRequestVo
 */
export interface IndexRemarkGetCommonRemarkRequest {
    userId?: number;
    /**
     * 页码
     */
    pageNo?: number;
    /**
     * 页面大小
     */
    pageSize?: number;
    apiName?: string;
    [k: string]: any;
}


/**
* ResponseBody<PageList<CommonRemarkDto>> :ResponseBody
*/
export interface IndexRemarkGetCommonRemarkResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * PageList
     */
    data?: {
        pageNo?: number;
        pageSize?: number;
        total?: number;
        /**
         * T
         */
        list?: {
            /**
             * id
             */
            id?: number;
            /**
             * 用户id
             */
            userId?: number;
            /**
             * 内容
             */
            content?: string;
            /**
             * 删除标识0已删除1未删除
             */
            enableStatus?: number;
            [k: string]: any;
        }[];
        [k: string]: any;
    };
    [k: string]: any;
}


/**
 * CommonRemarkEditRequestVo :CommonRemarkEditRequestVo
 */
export interface IndexRemarkEditCommonRemarkRequest {
    id?: number;
    userId?: number;
    content?: string;
    isDelete?: boolean;
    apiName?: string;
    [k: string]: any;
}

/**
 * LoginLogQueryRequestVo :LoginLogQueryRequestVo
 */
export interface IndexAuthorityGetLoginLogRequest {
    userId?: number;
    subUserId?: number;
    /**
     * 页码
     */
    pageNo?: number;
    /**
     * 页面大小
     */
    pageSize?: number;
    apiName?: string;
    [k: string]: any;
}

/**
 * ResponseBody<PageList<LoginLogDto>> :ResponseBody
 */
export interface IndexAuthorityGetLoginLogResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * PageList
     */
    data?: {
        pageNo?: number;
        pageSize?: number;
        total?: number;
        /**
         * T
         */
        list?: {
            /**
             * id
             */
            id?: number;
            /**
             * 用户id
             */
            userId?: number;
            /**
             * 子账号用户id
             */
            subUserId?: number;
            /**
             * ip
             */
            ip?: string;
            /**
             * 登录时间
             */
            loginTime?: string;
            /**
             * 删除标识0已删除1未删除
             */
            enableStatus?: number;
            [k: string]: any;
        }[];
        [k: string]: any;
    };
    [k: string]: any;
}

/**
 * ShopInfoRequest :ShopInfoRequest
 */
export interface ItemGetShopItemCountRequest {
    /**
     * 平台:PDD,TB
     */
    platform: string
    /**
     * 店铺id
     */
    sellerId: number;
    [k: string]: any;
}


/**
 * ResponseBody<ShopInfoResponse> :ResponseBody
 */
export interface ItemGetShopItemCountResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * ShopInfoResponse
     */
    data?: {
        /**
         * 平台:PDD,TB
         */
        platform?: {
            [k: string]: any;
        };
        /**
         * 店铺id
         */
        sellerId?: number;
        /**
         * 商品总数量(在售+仓库)
         */
        total?: number;
        [k: string]: any;
    };
    [k: string]: any;
}

/**
 * ResponseBody<List<ShopDTO>> :ResponseBody
 */
export interface ItemGetNotImportShopsResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * ShopDTO
     */
    data?: {
        /**
         * 账号id
         */
        userId?: number;
        /**
         * 平台:PDD,TB
         */
        platform?: string;
        /**
         * 店铺id
         */
        sellerId?: number;
        /**
         * 店铺名称
         */
        sellerNick?: string;
        /**
         * 是否导入商品
         */
        isImportItem?: boolean;
        [k: string]: any;
    }[];
    [k: string]: any;
}


/**
 * ExpressArriveQuery :ExpressArriveQuery
 */
export interface IndexExpressArriveGetExpressArriveListRequest {
    /**
     * 快递公司名
     */
    expressName?: string;
    /**
     * 是否可达1可达0不可达
     */
    arriveAble?: number;
    /**
     * 记录是否生效,{@code1生效}{@code0不生效}
     */
    effective?: number;
    /**
     * 平台用户Id
     */
    platUserId?: number;
    /**
     * 平台名称
     */
    platform?: string;
    /**
     * 页码
     */
    pageNo?: number;
    /**
     * 页面大小
     */
    pageSize?: number;
    /**
     * 是否需要查询总数
     */
    needCount?: boolean;
    [k: string]: any;
}

/**
 * ResponseBody<List<ExpressArriveVo>> :ResponseBody
 */
export interface IndexExpressArriveGetExpressArriveListResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * ExpressArriveVo
     */
    data?: {
        id?: number;
        name?: string;
        platUserId?: number;
        exCode?: string;
        expressName?: string;
        arriveAble?: number;
        /**
         * Area
         */
        arriveArea?: {
            p?: string;
            c?: string;
            d?: string;
            t?: string;
            a?: string;
            [k: string]: any;
        }[];
        customArea?: string;
        effective?: number;
        exCompanyId?: number;
        [k: string]: any;
    }[];
    [k: string]: any;
}

/**
 * ResponseBody :ResponseBody
 */
export interface IndexExpressArriveDeleteExpressArriveByIdResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    [k: string]: any;
}

export interface IndexExpressArriveDeleteExpressArriveByIdRequest {
    platform: string;
    id: number;
    [k: string]: any;
}

export interface IndexExpressArriveSaveOrEditExpressArriveRequest {
    id?: number;
    name?: string;
    platUserId?: number;
    exCode?: string;
    expressName?: string;
    arriveAble?: number;
    customArea?: string;
    arriveArea?: string;
    exCompanyId?: number;
    effective?: number;
    platform?: string;
    [k: string]: any;
}

/**
 * ResponseBody :ResponseBody
 */
export interface IndexExpressArriveSaveOrEditExpressArriveResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    [k: string]: any;
}

/**
 * ExpressArriveRequest :ExpressArriveRequest
 */
export interface IndexExpressArriveCloseOrOpenExpressArriveRequest {
    /**
     * 平台名称
     */
    platform?: string;
    /**
     * 平台用户id
     */
    platUserId?: number;
    /**
     * 记录id
     */
    id?: number;
    /**
     * 记录是否生效1生效0不生效
     */
    effective?: number;
    [k: string]: any;
}

/**
 * ResponseBody<Boolean> :ResponseBody
 */
export interface IndexExpressArriveCloseOrOpenExpressArriveResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * data
     */
    data?: boolean;
    [k: string]: any;
}

/**
 * ExpressArriveExcelImportRequest :ExpressArriveExcelImportRequest
 */
export interface IndexExpressArriveExcelImportAnalysisExpressArriveRequest {
    /**
     * MultipartFile
     */
    file: {
        [k: string]: any;
    };
    [k: string]: any;
}

/**
 * ResponseBody<ImportResponseVO> :ResponseBody
 */
export interface IndexExpressArriveExcelImportAnalysisExpressArriveResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * ImportResponseVO
     */
    data?: {
        /**
         * 导入总条数
         */
        totalCount?: number;
        /**
         * 导入成功总数
         */
        successNum?: number;
        /**
         * 暂存失败信息
         */
        errorMsg?: string;
        /**
         * 分析后的结果文本
         */
        value?: string;
        [k: string]: any;
    };
    [k: string]: any;
}

/**
 * ResponseBody<NewUserGuideDTO> :ResponseBody
 */
export interface IndexNewUserGuideSelectResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * NewUserGuideDTO
     */
    data?: {
        id?: number;
        /**
         * 用户id
         */
        userId?: number;
        /**
         * 是否店铺授权true:是false:否
         */
        shopAuth?: boolean;
        /**
         * 是否完成商品同步true:是false:否
         */
        itemSync?: boolean;
        /**
         * 是否完成订单同步true:是false:否
         */
        tradeSync?: boolean;
        /**
         * 是否完成订单打印&发货true:是false:否
         */
        tradePrintAndSend?: boolean;
        firstShow?: boolean; // 是否第一次展示
        skipGuide?: boolean; // 是否跳过引导
        [k: string]: any;
    };
    [k: string]: any;
}


/**
 * NewUserGuideDTO :NewUserGuideDTO
 */
export interface IndexNewUserGuideUpdateRequest {
    id?: number;
    /**
     * 用户id
     */
    userId?: number;
    /**
     * 是否店铺授权true:是false:否
     */
    shopAuth?: boolean;
    /**
     * 是否完成商品同步true:是false:否
     */
    itemSync?: boolean;
    /**
     * 是否完成订单同步true:是false:否
     */
    tradeSync?: boolean;
    /**
     * 是否完成订单打印&发货true:是false:否
     */
    tradePrintAndSend?: boolean;
    firstShow?: boolean;
    skipGuide?: boolean;
    [k: string]: any;
}

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface IndexNewUserGuideUpdateResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * Void
     */
    data?: {
        [k: string]: any;
    };
    [k: string]: any;
}


/**
 * AfterSaleAuthorityQueryReqVo :AfterSaleAuthorityQueryReqVo
 */
export interface IndexAuthorityGetAfterSaleAuthoritySetListRequest {
    /**
     * 是否是主账号
     */
    isMainAccount: boolean;
    /**
     * 有无售后权限0-无1-有
     */
    hasAfterSaleAuthority: number;
    /**
     * 子账号用户id
     */
    id?: number;
    /**
     * 用户id
     */
    userId?: number;
    [k: string]: any;
}

/**
 * WebResponse :WebResponse
 */
export interface IndexAuthorityGetAfterSaleAuthoritySetListResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * Object
     */
    data?: {
        /**
         * 账号用户id
         */
        userId: number;
        /**
         * 平台 tb pdd fxg
         */
        platform: string;
        /**
         * 平台店铺id
         */
        sellerId: number;
        /**
         * 平台店铺名
         */
        sellerNick: string;
        /**
         * 店铺子账号集合
         */
        subSellerList: {
            /**
             * sub_user_id
             */
            subUserId: number;
            /**
             * 平台店铺id
             */
            sellerId: number;
            /**
             * 平台店铺子账号id
             */
            subSellerId: number;
            /**
             * 子账号用户名
             */
            subAccountName: string;
            /**
             * 是否有售后权限 0-无 1-有
             */
            hasAfterSaleAuthority: number;
            [k: string]: any;
        }[];
        [k: string]: any;
    }[];
    [k: string]: any;
}

/**
 * AfterSaleAuthorityReqVo :AfterSaleAuthorityReqVo
 */
export interface IndexAuthorityUpdateAfterSaleAuthorityRequest {
    /**
     * 是否是主账号
     */
    isMainAccount: boolean;
    /**
     * 子账号用户id
     */
    id?: number;
    /**
     * 店铺及子账号 ,AfterSaleAuthorityDetailReqVo
     */
    list?: {
        /**
         * 平台tbpddfxg
         */
        platform?: string;
        /**
         * 平台店铺id
         */
        sellerId?: number;
        /**
         * 平台店铺名
         */
        sellerNick?: string;
        /**
         * sub_user_id
         */
        subUserId?: number;
        [k: string]: any;
    }[];
    /**
     * 账号用户id
     */
    userId?: number;
    [k: string]: any;
}



/**
 * ResponseBody<Void> :ResponseBody
 */
export interface IndexAuthorityUpdateAfterSaleAuthorityResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * Void
     */
    data?: {
        [k: string]: any;
    };
    [k: string]: any;
}

/**
 * WebResponse :WebResponse
 */
export interface PrintCenterExCompanyGetExCompanyAllResponse {
    /**
     * 响应状态码
     */
    result?: number;
    /**
     * 对result的状态文字描述
     */
    message?: string;
    apiName?: string;
    /**
     * 实际传送的对象 ,Object
     */
    data?: {
        cnLogisticsCode: string;
        created: string;
        dyCode: string;
        enableStatus: boolean;
        exCode: string;
        exName: string;
        exType: number;
        id: number;
        jdLogisticsId: string;
        modified: string;
        pddLogisticsId: string;
        sort: number;
        wpCode: string;
        [k: string]: any;
    }[];
    success?: boolean;
    [k: string]: any;
}


export interface IndexUserCheckInvitationCodeRequest {
    invitationCode: string;
    [k: string]: any;
}

/**
 * WebResponse :WebResponse
 */
export interface IndexUserCheckInvitationCodeResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * Object
     */
    data?: {
        [k: string]: any;
    };
    [k: string]: any;
}



/**
 * SystemOperateLogRequestVo :SystemOperateLogRequestVo
 */
export interface IndexSystemOperateLogQueryByConditionRequest {
    /**
     * 系统日志分类
     */
    operateLogClassify?: string;
    /**
     * 操作时间开始
     */
    startTime?: string;
    /**
     * 操作时间结束
     */
    endTime?: string;
    /**
     * 操作人
     */
    operateName?: string;
    /**
     * 页码
     */
    pageNo?: number;
    /**
     * 页面大小
     */
    pageSize?: number;
    apiName?: string;
    [k: string]: any;
}
/**
* ResponseBody<PageList<SystemOperateLogDto>> :ResponseBody
*/
export interface IndexSystemOperateLogQueryByConditionResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * PageList
     */
    data?: {
        pageNo?: number;
        pageSize?: number;
        total?: number;
        /**
         * T
         */
        list?: {
            /**
             * 日志分类
             */
            classify?: number;
            /**
             * 操作时间
             */
            operateTime?: string;
            /**
             * 操作内容
             */
            operateContent?: string;
            /**
             * 操作结果
             */
            operateResult?: string;
            /**
             * IP地址
             */
            operateIp?: string;
            /**
             * 操作人
             */
            operateName?: string;
            [k: string]: any;
        }[];
        [k: string]: any;
    };
    [k: string]: any;
}
/**
 * SystemOperateLogUploadVo :SystemOperateLogUploadVo
 */
export interface IndexSystemOperateLogUploadRequest {
    /**
     * 日志操作类型
     */
    operateLogType?: any;
    /**
     * 操作结果true:成功false:失败
     */
    operateResult?: boolean;
    /**
     * 订单编号
     */
    tid?: string;
    [k: string]: any;
}

/**
 * ActivityVo :ActivityVo
 */
export interface IndexActivityGetActivityInfoRequest {
    userId?: number;
    /**
     * 需要查询,或者完成的活动idList，本次活动默认，1,2 ,Long
     */
    activityIdList?: number[];
    apiName?: string;
    [k: string]: any;
}

/**
* ResponseBody<ActivityRecordDto> :ResponseBody
*/
export interface IndexActivityGetActivityInfoResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * ActivityRecordDto
     */
    data?: {
        /**
         * 领取记录集合 ,ActivityRecord
         */
        activityRecordList?: {
            /**
             * 用户ID
             */
            userId?: number;
            /**
             * 店铺ID
             */
            sellerId?: string;
            /**
             * 平台类型
             */
            platform?: string;
            /**
             * 关联活动的id
             */
            activityId?: number;
            /**
             * 活动名称
             */
            activityName?: string;
            /**
             * 活动完成状态0:未完成，1:已完成
             */
            status?: number;
            /**
             * 领取结果信息
             */
            msg?: string;
            /**
             * 成功领取时间
             */
            giftGetTime?: string;
            /**
             * 配置创建时间
             */
            gmtCreate?: string;
            /**
             * 配置更新时间
             */
            gmtModified?: string;
            [k: string]: any;
        }[];
        [k: string]: any;
    };
    [k: string]: any;
}


/**
* ActivityVo :ActivityVo
*/
export interface IndexActivityFinishActivityRequest {
    userId?: number;
    /**
     * 需要查询,或者完成的活动idList，本次活动默认，1,2 ,Long
     */
    activityIdList?: number[];
    apiName?: string;
    [k: string]: any;
}

/**
* ResponseBody<Void> :ResponseBody
*/
export interface IndexActivityFinishActivityResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * Void
     */
    data?: {
        [k: string]: any;
    };
    [k: string]: any;
}

/**
* ResponseBody<RspGetTakeGoodsLabelActivityDataCountDTO> :ResponseBody
*/
export interface IndexActivityGetTakeGoodsLabelActivityDataCountResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * RspGetTakeGoodsLabelActivityDataCountDTO
     */
    data?: {
        /**
         * 小标签打印面单数统计主订单唯独
         */
        alreadyPrintKddCount?: number;
        /**
         * 小标签打印面单天数统计(哪些天打印过小标签)
         */
        alreadyPrintKddDayCount?: number;
        [k: string]: any;
    };
    [k: string]: any;
}

/**
 * WebResponse :WebResponse
 */
export interface LoginCustomShopCreateResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * Object
     */
    data?: {
        [k: string]: any;
    };
    [k: string]: any;
}

export interface ItemSysSkuGetGroupPackageOperationLogRequest {
    [k: string]: any;
}
/**
* ResponseBody<PageList<SysSkuDTO>> :ResponseBody
*/
export interface ItemSysSkuGetGroupPackageOperationLogResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * 记录数，等于0时表示没有日志
     */
    data?: number;
    [k: string]: any;
}




/**
 * WebResponse :WebResponse
 */
export interface IndexUserCheckMobileUserTypeResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * Object
     */
    data?: any;
    [k: string]: any;
}

/**
 * ResponseBody :ResponseBody
 */
export interface IndexUserHeartbeatResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    data: {
        /**
         * 用户归属环境
         */
        env: string;
        /**
         * 环境退出模式 0: 正常模式、1:强制模式
         */
        envGrayExitType: string;
        /**
         * 当前时间
         */
        currentTime: number;
        [k: string]: any;
    };
    [k: string]: any;
}

export interface IndexEnvEnvSetRequest {
    env: string;
}


/**
 * ResponseBody<Object> :ResponseBody
 */
export interface IndexEnvEnvSetResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * Object
     */
    data?: {
        /**
         * 切换的环境
         */
        env: string;
        /**
         * 切换的域名
         */
        url: string;
        [k: string]: any;
    };
    [k: string]: any;
}
/**
 * ResponseBody<List<LogisticsNumResponse>> :ResponseBody
 */
export interface TradeLogisticsQueryLogisticsWarnIndexCountResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * LogisticsNumResponse
     */
    data?: {
        /**
         * 数量
         */
        num?: number;
        /**
         * 发货未揽收:2,揽收未更新:3,中转未更新:4,派件预警:5,超时未签收:6
         */
        logisticsType?: number;
        [k: string]: any;
    }[];
    [k: string]: any;
}

/**
 * ReqUpdateMergeMaxSizeDTO :ReqUpdateMergeMaxSizeDTO
 */
export interface IndexSettingUpdateMergeMaxSizeRequest {
    /**
     * 合单最大数量
     */
    mergeMaxSize: number;
    [k: string]: any;
}
/**
* ResponseBody<Void> :ResponseBody
*/
export interface IndexSettingUpdateMergeMaxSizeResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * Void
     */
    data?: {
        [k: string]: any;
    };
    [k: string]: any;
}


/**
 * WebResponse :WebResponse
 */
export interface IndexAuthorityGetAuthorityTemplateResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * Object
     */
    data?: {
        /**
         * 表字段:id
         */
        id?: number;
        /**
         * 权限名
         */
        authorityName?: string;
        /**
         * 上级权限id
         */
        parentId?: string;
        /**
         * 删除标识0已删除1未删除
         */
        enableStatus?: number;
        /**
         * 创建时间
         */
        gmtCreated?: string;
        /**
         * 修改时间
         */
        gmtModified?: string;
        templateCode?: string;
        children?: IndexAuthorityGetAuthorityTemplateResponse['data']; // 构建tree结构
        functionChildren?: IndexAuthorityGetAuthorityTemplateResponse['data']; // 构建tree下面的功能权限结构
        key: number; // tree key
        [k: string]: any;
    }[];
    [k: string]: any;
}

export interface PlatformsWayBillInfo{
	/**
	 * 快递code
	 */
	platform?: string;
	/**
	 * 默认网点信息 ,DefaultWaybillInfo
	 */
	defaultWaybillInfos?: {
	  /**
	   * 快递code
	   */
	  exCode?: string;
	  /**
	   * 网点编码
	   */
	  branchCode?: string;
	  [k: string]: any;
	}[];
	[k: string]: any;
  }


/**
 * UserAddressWaybillUpdateRequest :UserAddressWaybillUpdateRequest
 */
export interface IndexAuthorityUpdateUserAddressWaybillRequest {
	/**
	 * 用户Id
	 */
	userId?: number;
	/**
	 * 用户Id
	 */
	subUserId?: number;
	/**
	 * 平台下的网点信息集合 ,PlatformsWayBillInfo
	 */
	platformsWayBillInfos?: PlatformsWayBillInfo[];
	[k: string]: any;
  }
/**
 * ResponseBody<Boolean> :ResponseBody
 */
export interface IndexAuthorityUpdateUserAddressWaybillResponse {
	success?: boolean;
	errorCode?: number;
	errorMessage?: string;
	/**
	 * data
	 */
	data?: boolean;
	[k: string]: any;
  }


export interface ExpressTypeDTO {
	/**
	 * 面单类型
	 */
	expressType?: number;
	/**
	 * 面单类型翻译
	 */
	expressTypeDesc?: string;
	/**
	 * 网点信息集合 ,BranchDTO
	 */
	branchDTOList?: {
	  /**
	   * 店铺ID
	   */
	  sellerId?: number;
	  /**
	   * 店铺名称
	   */
	  sellerNick?: string;
	  /**
	   * 快递公司编码
	   */
	  exCode?: string;
	  /**
	   * 快递公司翻译
	   */
	  exDesc?: string;
	  /**
	   * 网点编码
	   */
	  branchCode?: string;
	  /**
	   * 网点名称
	   */
	  branchName?: string;
	  /**
	   * 号段编码
	   */
	  segmentCode?: string;
	  /**
	   * 号段翻译
	   */
	  segmentDesc?: string;
	  [k: string]: any;
	}[];
	[k: string]: any;
  }


export interface BranchAuthoritySubmitDTO {
	 /**
     * 操作用户ID
     */
	 userId?: number;
	 /**
	  * 被操作子用户ID
	  */
	 subUserId?: number;
	 /**
	  * 面单类型集合 ,ExpressTypeDTO
	  */
	 expressTypeDTOList?: ExpressTypeDTO[];
	 [k: string]: any;
}

export interface TradeSyncAbnormalOrTokenExpiredShopListRequest {
	[k: string]: any;
  }
/**
 * 异常的店铺列表
 */
export interface TradeSyncAbnormalOrTokenExpiredShopListResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    data?: {
      /**
       * 平台
       */
      platform?: string;
      platformName?: string;
      /**
       * 平台店铺方的mallId
       */
      sellerId?: number;
      /**
       * 平台店铺名
       */
      sellerNick?: string;
      /**
       * 最近订单同步时间
       */
      latestOrderSyncTime?: string;
      /**
       * 异常类型 1. 授权过期 2. 订单同步异常
       */
      abnormalType?: number;
      /**
       * 异常描述
       */
      abnormalDesc?: string;
      [k: string]: any;
    }[];
    [k: string]: any;
  }
  


/**
 * TradeGetSellerFlagDTO :TradeGetSellerFlagDTO
 */
export interface TradeSellerInfoGetSellerFlagListRequest {
    /**
     * 店铺id列表 ,TradeGetSellerFlagInfo
     */
    sellerFlagInfoList: {
      /**
       * 店铺Id
       */
      sellerId: number;
      /**
       * 平台类型
       */
      platform: string;
      [k: string]: any;
    }[];
    [k: string]: any;
  }
  /**
 * ResponseBody<List<TradeGetSellerFlagVO>> :ResponseBody
 */
export interface TradeSellerInfoGetSellerFlagListResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * TradeGetSellerFlagVO
     */
    data?: {
      /**
       * 旗帜
       */
      sellerFlag?: string;
      /**
       * 旗帜标签备注
       */
      sellerFlagTag?: string;
      /**
       * 店铺ID
       */
      sellerId?: string;
      /**
       * 店铺简称
       */
      sellerName?: string;
      [k: string]: any;
    }[];
    [k: string]: any;
  }
  /**
 * TradeGetHistorySellerMemosDTO :TradeGetHistorySellerMemosDTO
 */
export interface TradeSellerInfoGetHistorySellerMemosListRequest {
    /**
     * 系统订单编号
     */
    tid: string;
    /**
     * 平台订单编号
     */
    ptTid: string;
    /**
     * 店铺id列表
     */
    sellerId: number;
    /**
     * 平台类型
     */
    platform: string;
    [k: string]: any;
  }
  /**
 * ResponseBody<List<TradeGetHistorySellerMemosVO>> :ResponseBody
 */
export interface TradeSellerInfoGetHistorySellerMemosListResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * TradeGetHistorySellerMemosVO
     */
    data?: {
      /**
       * 插旗ID
       */
      sellerFlag?: string;
      /**
       * 插旗备注
       */
      sellerFlagTag?: string;
      /**
       * 订单备注
       */
      memo?: string;
      /**
       * 备注创建者ID
       */
      operatorId?: string;
      /**
       * 店铺ID
       */
      sellerId?: number;
      /**
       * 店铺简称
       */
      sellerName?: string;
      /**
       * 平台订单号
       */
      ptTid?: string;
      /**
       * 系统订单编号
       */
      tid?: string;
      /**
       * 备注创建时间戳
       */
      createTime?: string;
      [k: string]: any;
    }[];
    [k: string]: any;
  }


/**
 * List<PlatformShopEditSortRequest>
 */
export type IndexPlatformShopEditPlatformShopSortRequest = {
    /**
     * 店铺id
     */
    sellerId?: number;
    /**
     * 平台店铺名
     */
    sellerNick?: string;
    /**
     * 排序
     */
    sort?: number;
    apiName?: string;
    [k: string]: any;
  }[];

/**
 * ResponseBody :ResponseBody
 */
export interface IndexPlatformShopEditPlatformShopSortResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    [k: string]: any;
  }

  
export interface IndexSettingUpdateUserShopRemindRequest {
    apiName?: string;
    value?: string;
    userConfigSettingEnum?:
      | "SMART_SELECT_EXPRESS_SWITCH"
      | "SMART_SELECT_EXPRESS_LIST"
      | "SHOP_AUTH_ABNORMAL_REMIND"
      | "BHD_QUERY_CONDITION"
      | "BHD_LABEL_QUERY_CONDITION"
      | "SHOP_SERVICE_EXPIRED_REMIND"
      | "SHOP_ORDER_SYNC_ABNORMAL_REMIND";
    [k: string]: any;
  }

export interface IndexSettingUpdateUserShopRemindResponse {
    /**
     * 是否成功
     */
    success?: boolean;
    /**
     * 返回码
     */
    errorCode?: number;
    /**
     * 返回消息
     */
    errorMessage?: string;
    /**
     * 返回数据
     */
    data?: {
      [k: string]: any;
    };
    [k: string]: any;
  }

  
/**
 * ReqUpdateMergeStrategyConfigDTO :ReqUpdateMergeStrategyConfigDTO
 */
export interface IndexSettingUpdateMergeStrategyConfigRequest {
    /**
     * 合单最大数量，mergeConfigType=GLOBAL(全局统一)传入
     */
    mergeMaxSize: number;
    /**
     * 最大合单，统一配置，对应枚举RdsTradeMergeStrategyMergeSizeConfigEnum<br>,GLOBAL：全局统一，SHOP店铺
     */
    mergeConfigType: string;
    /**
     * 店铺配置,mergeConfigType=SHOP（店铺）时必填 ,ShopMergeConfig
     */
    shopMergeConfigList?: {
      /**
       * 店铺id
       */
      sellerId?: number;
      /**
       * 平台类型
       */
      platform?: string;
      /**
       * 全局合单大小,合单数量必须在2-100之间
       */
      mergeMaxSize?: number;
      [k: string]: any;
    }[];
    /**
     * 系统判断为“缺货”true:缺货false：不缺货
     */
    sysStockOut?: boolean;
    /**
     * 系统判断为“部分缺货”true:部分缺货false：不部分缺货
     */
    sysPartStockOut?: boolean;
    /**
     * 预售订单true:是false：否
     */
    preSale?: boolean;
    /**
     * 拆分订单true:是false：否
     */
    splitOrder?: boolean;
    /**
     * 手工合单订单true:是false：否
     */
    manualMergeOrder?: boolean;
    /**
     * 手工取消订单true:是false：否
     */
    manualSplitMergeOrder?: boolean;
    [k: string]: any;
  }

/**
 * ResponseBody<Void> :ResponseBody
 */
export interface IndexSettingUpdateMergeStrategyConfigResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * Void
     */
    data?: {
      [k: string]: any;
    };
    [k: string]: any;
  }

/**
 * OperateTimeRecordQueryVO :OperateTimeRecordQueryVO
 */
export interface IndexSettingSelectUserOperateTimeRequest {
    /**
     * @mbg.generated类型，MERGE_STRATEGY_CONFIG:合单策略调整时间,表字段:type ,String
     */
    typeList?: string[];
    [k: string]: any;
  }

/**
 * ResponseBody<List<OperateTimeRecordVO>> :ResponseBody
 */
export interface IndexSettingSelectUserOperateTimeResponse {
    success?: boolean;
    errorCode?: number;
    errorMessage?: string;
    /**
     * OperateTimeRecordVO
     */
    data?: {
      /**
       * @mbg.generated表字段:id
       */
      id?: number;
      /**
       * @mbg.generated用户id,表字段:user_id
       */
      userId?: number;
      /**
       * @mbg.generated子账号用户id,表字段:sub_user_id
       */
      subUserId?: number;
      /**
       * @mbg.generated类型，MERGE_STRATEGY_CONFIG:合单策略调整时间,表字段:type
       */
      type?: string;
      /**
       * @mbg.generated时间,表字段:time
       */
      time?: string;
      /**
       * @mbg.generated创建时间,表字段:gmt_created
       */
      gmtCreated?: string;
      /**
       * @mbg.generated修改时间,表字段:gmt_modified
       */
      gmtModified?: string;
      [k: string]: any;
    }[];
    [k: string]: any;
  }
  

/**
 * QueryAfterAuthorityUserReqDto :QueryAfterAuthorityUserReqDto
 */
export interface QueryAfterAuthorityUserListRequest {
  /**
   * userId
   */
  userId?: number;
  /**
   * subUserId
   */
  subUserId?: number;
  [k: string]: any;
}

/**
 * ResponseBody<List<QueryAfterAuthorityUserResDto>> :ResponseBody
 */
export interface QueryAfterAuthorityUserListResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * QueryAfterAuthorityUserResDto
   */
  data?: {
    /**
     * 主账号用户id
     */
    userId?: number;
    /**
     * 子账号用户id
     */
    subUserId?: number;
    /**
     * 平台tbpddfxg
     */
    platform?: string;
    /**
     * 平台店铺id
     */
    sellerId?: number;
    /**
     * 平台店铺名
     */
    sellerNick?: string;
    /**
     * 平台店铺子账号id
     */
    subSellerId?: number;
    /**
     * 子账号用户名
     */
    subAccountName?: string;
    /**
     * 会话
     */
    accessToken?: string;
    /**
     * 是否店铺退款子账号
     */
    hasCanRefundSubUser?: boolean;
    [k: string]: any;
  }[];
  [k: string]: any;
}

/**
 * 铺货设置接口响应数据结构
 */
export interface PlatformShopPuhuoSetting {
 success?: boolean;
 errorCode?: number;
 errorMessage?: string;
 /**
  * ListData
  */
 data?: {
   /**
    * T
    */
   list?: {
     /**
      * 是否选择当前模板
      */
     selected?: number;
     /**
      * 设置模板id
      */
     id?: number;
     /**
      * 设置模板名称
      */
     templateName?: string;
     /**
      * 货源平台1688PPW
      */
     fromPlatform?: string;
     /**
      * 平台
      */
     platform: string;
     /**
      * 店铺id
      */
     shopId: number;
     /**
      * 店铺名
      */
     shopName?: string;
     /**
      * 平台用户唯一标识
      */
     userId?: string;
     /**
      * 是否开启图片空间自动清理
      */
     pictureClean?: boolean;
     /**
      * 类目匹配方式,1智能匹配（默认）,2手动匹配,3指定类目铺货
      */
     matchType?: number;
     /**
      * 平台类目id,<p>,这里类目是是多级拼接，1级,2级,3级
      */
     platformCid?: string;
     /**
      * 平台类目名称,<p>,这里类目是是多级拼接，1级>2级>3级
      */
     platformCidName?: string;
     /**
      * 其他配置信息json格式,{,"autoDelivery":1,,"autoFill":1,,"autoWhiteBackground":1,,"deliveryTime":1,,"freightTemplate":0,,"groupPrice":{,"removeDecimalPoint":1,,"removeMethod":0,},,"inventorySettings":1,,"payTenForOneFake":1,,"preSale":0,,"promiseToShip":0,,"sellingPriceSetting":{,"markup":0,,"percentage":0,,"removeDecimalPoint":1,,"removeMethod":0,},,"sevenDay":1,,"shelfSetting":1,,"singlePrice":{,"markup":0,,"removeDecimalPoint":1,,"removeMethod":0,},,"underlinedPrice":{,"markup":0,,"removeDecimalPoint":1,,"removeMethod":0,},,<p>,} ,ShopSettingBo
      */
     extraSetting?: {
       platform?: string;
       /**
        * 平台：全平台,<p>,上架设置1：直接上架2：存放仓库
        */
       shelfSetting?: number;
       /**
        * 平台：抖音即时零售,上架门店选择直接上架时对应的门店配置 ,ShelfStoresSetting
        */
       shelfStores?: {
         /**
          * 0:所有门店，1：部分选中门店
          */
         type?: number;
         /**
          * type为1时，这里是选择的门店集合 ,ShelfStores
          */
         shelfStoresList?: {
           /**
            * 门店id
            */
           storeId?: number;
           /**
            * 门店名称
            */
           name?: string;
           [k: string]: any;
         }[];
         [k: string]: any;
       };
       /**
        * 平台：抖音即时零售,商品商家编码
        */
       goodsMerchantCode?: string;
       /**
        * 平台：抖音即时零售,<p>,重量设置 ,WeightInfo
        */
       weightInfo?: {
         /**
          * 重量值
          */
         value?: string;
         /**
          * 单位
          */
         unit?: string;
         [k: string]: any;
       };
       /**
        * 平台：淘宝、抖音、快手、微店、微信小商店,<p>,商品售价设置 ,SellingPriceSetting
        */
       sellingPriceSetting?: {
         /**
          * 价格类型0：代发价，1：批发价默认代发价
          */
         priceType?: number;
         /**
          * 百比例
          */
         percentage?: number;
         /**
          * 加价
          */
         markup?: string;
         /**
          * 是否去掉小数点1=直接去掉0=不去掉（保留角分）2=固定小数
          */
         removeDecimalPoint?: number;
         /**
          * 固定小数点后的内容
          */
         fixedPointPrice?: string;
         /**
          * 冲突字段基于removeDecimalPoint是直接去掉的情况下,0=四舍五入1=直接去掉,废弃
          */
         removeMethod?: number;
         /**
          * 冲突类型,<p>,售价类型：0-分销价1-利润率
          */
         sellingPriceType?: number;
         [k: string]: any;
       };
       /**
        * 平台：除拼多多都有,<p>,追加运费设置 ,AppendFreightSetting
        */
       appendFreightSetting?: {
         /**
          * 追加运费开关0:关闭1:开启,新用户默认关闭
          */
         flag?: number;
         /**
          * 省
          */
         province?: string;
         /**
          * 运费占比
          */
         freightRate?: number;
         /**
          * 追加金额
          */
         appendAmount?: number;
         [k: string]: any;
       };
       /**
        * 平台：淘宝、天猫,<p>,商家编码设置 ,MerchantCode
        */
       merchantCode?: {
         /**
          * 商家开关0:关闭1:开启
          */
         flag?: number;
         /**
          * 商家编码
          */
         code?: string;
         [k: string]: any;
       };
       /**
        * 平台：全平台,<p>,库存计数：1：使用商品来源2：统一修改库存
        */
       inventoryNumSettings?: number;
       /**
        * 抖音店长：sku库存低于这个值设置库存为0
        */
       lowerStockToZero?: number;
       /**
        * 抖音店长：sku库存低于这个值sku设置为下线
        */
       lowerStockOffline?: number;
       /**
        * 抖音店长：来源库存小于0时将库存设置为这个数
        */
       fromStockZeroSetTo?: number;
       /**
        * 两件折扣->满两件打折默认95,示例：95（代表商品上已设置9.5折的折扣值）
        */
       twoPiecesDiscount?: number;
       /**
        * 闲鱼发货地区id
        */
       districtId?: number;
       /**
        * 闲鱼发货地区名称
        */
       districtNames?: string;
       /**
        * 闲鱼上货店铺列表 ,String
        */
       publishUserName?: string[];
       /**
        * 闲鱼物流费用:单位元
        */
       logisticFee?: string;
       /**
        * 平台：全平台,<p>,统一修改库存数量
        */
       inventoryNums?: number;
       /**
        * 平台：全平台,<p>,标题设置 ,TitleSettings
        */
       titleSettings?: {
         /**
          * 标题前缀
          */
         pre?: string;
         /**
          * 标题后缀
          */
         end?: string;
         /**
          * 替换关键词 ,ReplaceStr
          */
         replaces?: {
           /**
            * 替换前
            */
           before?: string;
           /**
            * 替换之后
            */
           after?: string;
           [k: string]: any;
         }[];
         /**
          * 1：超长删最前，2：超长删最后
          */
         moreThanDel?: number;
         [k: string]: any;
       };
       /**
        * 平台：全平台,<p>,规格设置 ,SkipSettings
        */
       skipSettings?: {
         /**
          * 跳过重复铺货true是false否
          */
         isSkipRepeatDistrib?: boolean;
         [k: string]: any;
       };
       /**
        * 跳过设置-需保存到Oss内容 ,SkipSettingsOss
        */
       skipSettingsOss?: {
         /**
          * 标题删除关键词","隔开
          */
         titleDel?: string;
         /**
          * 标题跳过关键词","隔开
          */
         titleSkip?: string;
         /**
          * SKU删除关键词","隔开
          */
         skuDel?: string;
         /**
          * SKU跳过关键词","隔开
          */
         skuSkip?: string;
         /**
          * 代发价低于多少价格跳过单位分
          */
         priceLowSkip?: string;
         /**
          * 代发价高于多少价格跳过单位分
          */
         priceHighSkip?: string;
         /**
          * SKU低于多少价格跳过单位分
          */
         skuPriceLowSkip?: string;
         /**
          * SKU高于多少价格跳过单位分
          */
         skuPriceHighSkip?: string;
         [k: string]: any;
       };
       /**
        * 平台：全平台,<p>,SKU规格名称 ,SkuSpecificationName
        */
       skuSpecificationName?: {
         /**
          * 删除关键字
          */
         del?: string;
         /**
          * 标题前缀
          */
         pre?: string;
         /**
          * 标题后缀
          */
         end?: string;
         /**
          * 替换关键词 ,ReplaceStr
          */
         replaces?: {
           /**
            * 替换前
            */
           before?: string;
           /**
            * 替换之后
            */
           after?: string;
           [k: string]: any;
         }[];
         [k: string]: any;
       };
       /**
        * 平台：全平台,<p>,主图设置：是否删除主图0:不删除1：删除
        */
       isDeletePicture?: number;
       /**
        * 平台：全平台,<p>,主图设置：删除主图第几张
        */
       deletePictureNum?: number;
       /**
        * 平台：淘宝,<p>,运费模版类型1-复制货源运费模板2-使用本店运费模板默认2
        */
       freightTemplateType?: number;
       /**
        * 平台：全平台,<p>,运费模板默认-1
        */
       freightTemplate?: string;
       /**
        * 平台：全平台,<p>,物流模板默认-1
        */
       logisticTemplate?: string;
       /**
        * 商品配送方式,4-快递发货;8-同城配送;16-到店自提可多选多选,分割
        */
       itemDeliveryList?: string;
       /**
        * 平台：拼多多、快手,<p>,发货时间,<p>,快手拼多多用承诺发货默认-1,单位：秒，取值86400,172800,259200,-1代表发货时间不承诺前端传了24,48,72
        */
       promiseToShip?: number;
       /**
        * 是否预售默认0-现货发货，1-预售发货，2-阶梯发货，默认0
        */
       preSale?: number;
       /**
        * 平台：抖店、拼多多、快手,<p>,七天无理由,<p>,抖店是否支持七天无理由退货0不支持，1支持，2支持（拆封后不支持）,<p>,快手类目退款规则列表：1：支持7天无理由退货，4：不支持7天无理由退货，5：支持7天无理由退货(拆封后不支持)，6：支持7天无理由退货(激活后不支持)，7：支持7天无理由退货(安装后不支持)，8：支持7天无理由退货(定制类不支持)，9：支持7天无理由退货(使用后不支持)，10：支持7天无理由退货(开窗后不支持)，11：随时退过期退，12：过期自动退，13：不可退
        */
       sevenDay?: number;
       /**
        * 平台：快手,服务承诺 ,ServicePromise
        */
       servicePromise?: {
         /**
          * 平台：快手,坏了包退0未选中，1选中默认
          */
         freshRotRefund?: number;
         /**
          * 平台：快手,破损包退0未选中，1选中默认
          */
         brokenRefund?: number;
         /**
          * 平台：快手,过敏包退0未选中，1选中默认
          */
         allergyRefund?: number;
         [k: string]: any;
       };
       /**
        * 平台：淘宝,<p>,自定义类目id逗号分割1,2,3
        */
       sellerCatIds?: string;
       /**
        * 平台：淘宝,<p>,发货时间,<p>,淘宝时间类型：0：48小时内发货2：大于48小时发货3：24小时发货,默认为0
        */
       deliveryTimeType?: number;
       /**
        * 平台：淘宝,<p>,发货时间天数例如3，deliveryTimeType为2时生效,<p>
        */
       tbDeliveryTime?: number;
       /**
        * 品牌自定义映射关系 ,BrandCustomGroup
        */
       brandCustomMapping?: {
         /**
          * SimpleCategory
          */
         brandCategory?: {
           fullCid?: string;
           fullCname?: string;
           [k: string]: any;
         };
         /**
          * SimpleBrand
          */
         simpleBrand?: {
           brandId?: string;
           brandName?: string;
           [k: string]: any;
         };
         [k: string]: any;
       }[];
       /**
        * 平台：淘宝抖音,<p>,主图视频上传开关1上传0不上传
        */
       imageVideoUpload?: number;
       /**
        * 平台：全平台,<p>,详情外链及其内容过滤1打开0关闭
        */
       remove1688OutLink?: number;
       /**
        * 平台:全平台,<p>,白底图自动生成1打开0关闭
        */
       generateWhitePic?: number;
       /**
        * 平台:全平台,<p>,生成第几张白底图
        */
       generateWhitePicNum?: number;
       /**
        * 平台：全平台,<p>,详情描述：删除详情图,<p>,删除详情图类型1:删除详情图前后几张2：删除详情图第几张3：两个都选
        */
       deleteDetailPicType?: number;
       /**
        * 平台：全平台,<p>,详情描述：删除详情图前几张,<p>,删除详情图张数：配合deleteDetailPicType使用,多张用";"隔开
        */
       deleteDetailPicNumPre?: string;
       /**
        * 平台：全平台,<p>,详情描述：删除详情图后几张,<p>,删除详情图张数：配合deleteDetailPicType使用,多张用";"隔开
        */
       deleteDetailPicNumEnd?: string;
       /**
        * 平台：全平台,<p>,详情描述：指定位置删除详情图,<p>,删除详情图张数：配合deleteDetailPicType使用,多张用";"隔开
        */
       deleteDetailPicNum?: string;
       /**
        * 平台：全平台,<p>,详情页前添加详情图 ,String
        */
       addDetailPicPre?: string[];
       /**
        * 平台：全平台,<p>,详情页前添加文字转图片
        */
       addDetailPreText?: string;
       /**
        * 平台：全平台,<p>,详情页前添加文字转图片的图片
        */
       addDetailPreTextPic?: string;
       /**
        * 平台：全平台,<p>,详情页后添加详情图 ,String
        */
       addDetailPicEnd?: string[];
       /**
        * 平台：淘宝,<p>,图片空间：节省图片空间
        */
       zipImage?: boolean;
       /**
        * 平台：淘宝,<p>,图片空间：节省图片空间，图片大小，单位kb张
        */
       zipImageSize?: number;
       /**
        * 平台：全平台,<p>,详情页后添加文字转图片
        */
       addDetailEndText?: string;
       /**
        * 平台：全平台,<p>,详情页后添加文字转图片的图片
        */
       addDetailEndTextPic?: string;
       /**
        * 0:第一张主图，1：第二张主图，2：第三张主图，3：第四张主图，4：第五张主图，999：末尾主图，-1：白底图 ,Integer
        */
       addMainPicToDetail?: number[];
       /**
        * 0：主图添加到详情页前，1：主图添加到详情页后，2：主图添加详情页随机位置
        */
       addMainPicToDetailPosition?: number;
       /**
        * 平台：全平台,<p>,支持违禁词过滤1支持0不支持
        */
       filterForbiddenWord?: number;
       /**
        * 平台：京东,<p>,品牌id
        */
       brandId?: number;
       /**
        * 平台：京东,<p>,品牌名称
        */
       brandName?: string;
       /**
        * 平台：抖店,<p>,必须三包服务:1：保修期内提供维修（寄修）2：延长售后服务有效期
        */
       mustThreeGuaranteesType?: number;
       /**
        * 平台：抖店,<p>,必须三包服务天数：,<p>,单位：天数
        */
       mustThreeGuaranteesDays?: number;
       /**
        * 平台：抖店,<p>,可选三包服务:0:不包含三包1：保修期内提供维修（寄修）2：延长售后服务有效期
        */
       notMustThreeGuaranteesType?: number;
       /**
        * 平台：抖店,<p>,可选三包服务天数,<p>,单位：天数
        */
       notMustThreeGuaranteesDays?: number;
       /**
        * 基础品牌设置 ,CommonBrandSetting
        */
       commonBrandSetting?: {
         /**
          * 本店品牌必填时货源有品牌，则本店品牌设置配置项
          */
         mustOnFromItemExistConf?: number;
         /**
          * 本店品牌必填时货源无品牌，则本店品牌设置配置项
          */
         mustOnFromItemWithoutConf?: number;
         /**
          * 本店品牌非必填时则本店品牌设置配置项
          */
         noMustOnItemConf?: number;
         [k: string]: any;
       };
       /**
        * 抖音-支付方式：0：货到付款1：在线支付2：在线支付货到付款
        */
       payMethod?: number;
       /**
        * 抖音每次限购数量
        */
       perCumulativePurchase?: number;
       /**
        * 抖音单用户累计限购数量
        */
       cumulativePurchase?: number;
       /**
        * 抖音每次至少购买
        */
       perLeastPurchase?: number;
       /**
        * 发货模式：0：现货发货1：全款预售发货2：使用商品来源
        */
       deliveryMethod?: number;
       /**
        * 预售时间：
        */
       preSaleTime?: string;
       /**
        * 拼多多团购价 ,GroupPrice
        */
       groupPrice?: {
         /**
          * 价格类型0：代发价，1：批发价默认代发价
          */
         priceType?: number;
         /**
          * 是否去掉小数点1=去掉0=不去掉2=固定小数
          */
         removeDecimalPoint?: number;
         /**
          * 0=四舍五入1=直接去掉
          */
         removeMethod?: number;
         /**
          * 固定小数点后的内容
          */
         fixedPointPrice?: string;
         [k: string]: any;
       };
       /**
        * 拼多多单买; ,SinglePrice
        */
       singlePrice?: {
         /**
          * 加价
          */
         markup?: string;
         /**
          * 是否去掉小数点1=去掉0=不去掉
          */
         removeDecimalPoint?: number;
         /**
          * 0=四舍五入1=直接去掉
          */
         removeMethod?: number;
         [k: string]: any;
       };
       /**
        * 自动发货1=自动发货0=不自动
        */
       autoDelivery?: number;
       /**
        * 平台：全平台,<p>,白底图：自动生成白底图1=自动0=不自动（弃用）
        */
       autoWhiteBackground?: number;
       /**
        * 平台：拼多多,<p>,假一罚十,<p>,加1赔101=开启0=不开启
        */
       payTenForOneFake?: number;
       /**
        * 平台：拼多多,<p>,警告提醒,<p>,是否忽略异常0忽略1不忽略默认1不忽略
        */
       ignoreEditWarn?: number;
       /**
        * 过滤二维码1=开启0=不开启默认开启
        */
       qrcodeFilter?: number;
       /**
        * 抖音商品划线价 ,UnderlinedPrice
        */
       underlinedPrice?: {
         /**
          * 划线价比例
          */
         percentage?: number;
         /**
          * 加价
          */
         markup?: string;
         /**
          * 是否去掉小数点1=去掉0=不去掉
          */
         removeDecimalPoint?: number;
         /**
          * 0=四舍五入1=直接去掉
          */
         removeMethod?: number;
         [k: string]: any;
       };
       /**
        * 平台：淘宝、快手,<p>,减库存类型：1-拍下减库存2-付款减库存
        */
       inventorySettings?: number;
       /**
        * 平台：抖店,<p>,客服电话
        */
       mobile?: string;
       /**
        * 平台：小红书1688抖店,发货时间-1=不承诺999=当天发货2=48h两天、3=72h3天、5=5天、7=7天、10=10天、15=15天1=1天次日发货
        */
       deliveryTime?: number;
       /**
        * 预售结束后发货：单位天
        */
       afterSaleDeliveryTime?: number;
       /**
        * 支付完成后发货：单位天
        */
       afterPayDeliveryTime?: number;
       /**
        * 爆单设置 ,ExplosiveOrderSetting
        */
       explosiveOrderSetting?: {
         /**
          * 1开启0关闭
          */
         open?: number;
         /**
          * 1=智能匹配0=手动选择
          */
         matchCategory?: number;
         /**
          * 类目id
          */
         categoryId?: string;
         /**
          * 类目名称
          */
         categoryName?: string;
         /**
          * 服务保障 ,Integer
          */
         serviceGuarantee?: number[];
         /**
          * 起始价格
          */
         startPrice?: string;
         /**
          * 结束价格
          */
         endPrice?: string;
         /**
          * 上货量
          */
         distribNum?: number;
         /**
          * 标志1=开启0=关闭
          */
         offShelfFlag?: number;
         /**
          * 动销设置
          */
         offShelfDay?: number;
         /**
          * 动销操作：1删除，2下架
          */
         offShelfAct?: number;
         [k: string]: any;
       };
       /**
        * 智能匹配自定义类目映射关系组 ,SimpleCategoryGroup
        */
       smartMatchCustomMapping?: {
         /**
          * SimpleCategory
          */
         fromSimpleCategory?: {
           fullCid?: string;
           fullCname?: string;
           [k: string]: any;
         };
         /**
          * SimpleCategory
          */
         toSimpleCategory?: {
           fullCid?: string;
           fullCname?: string;
           [k: string]: any;
         };
         [k: string]: any;
       }[];
       /**
        * 属性预设设置 ,ShopAttributeBo
        */
       attributeDefault?: {
         name?: string;
         value?: string;
         isForce?: number;
         msg?: string;
         [k: string]: any;
       }[];
       /**
        * 货源同步开关
        */
       itemSourceSync?: boolean;
       /**
        * 货源库存同步配置 ,StockSyncConfig
        */
       stockSyncConfig?: {
         /**
          * 标志1=开启0=关闭
          */
         flag?: number;
         /**
          * 选项1,2,3对应单选按钮1,2,3
          */
         stockSyncOption?: number;
         /**
          * stockSyncOption为1时，是否开启低于多少时不修改库存选项
          */
         stockModifyOption?: number;
         /**
          * stockSyncOption为1时，低于多少时不修改库存
          */
         stockModifyMinNum?: number;
         /**
          * 货源库存低于100时提醒
          */
         stockWarnMinNum?: number;
         [k: string]: any;
       };
       /**
        * 货源价格同步配置 ,PriceSyncConfig
        */
       priceSyncConfig?: {
         /**
          * 标志1=开启0=关闭
          */
         flag?: number;
         /**
          * 货源涨价配置，1-仅提醒
          */
         priceUpOption?: number;
         /**
          * 货源降价配置，1-仅提醒
          */
         priceDownOption?: number;
         [k: string]: any;
       };
       /**
        * 货源状态同步配置 ,StatusSyncConfig
        */
       statusSyncConfig?: {
         /**
          * 标志1=开启0=关闭
          */
         flag?: number;
         /**
          * 货源sku失效配置，1-仅提醒
          */
         skuInvalidOption?: number;
         /**
          * 货源sku上架配置，1-仅提醒
          */
         skuUpOption?: number;
         /**
          * 货源sku下架配置，1-仅提醒
          */
         skuDownOption?: number;
         [k: string]: any;
       };
       /**
        * 平台：拼多多,主图视频是否上传到多多视频，也叫视频详解,<p>,先不给默认值，防止序列化到其他平台去
        */
       uploadDuoduoVideo?: boolean;
       [k: string]: any;
     };
     /**
      * scenceType==2：多店同步业务场景下，关联店铺的运费模版设置 ,MultipleSyncInfoSettingBo
      */
     multipleSyncInfo?: {
       /**
        * FreightTemplate
        */
       multipleFreightTemplateRelation?: {
         /**
          * 平台：全平台,<p>,运费模板默认0
          */
         freightTemplateId?: string;
         /**
          * 关联的店铺id
          */
         toShopId?: string;
         [k: string]: any;
       }[];
       [k: string]: any;
     };
     /**
      * 跳过复制设置项 ,SkipCopyInfoSettingBo
      */
     skipCopyInfo?: {
       /**
        * 设置了需要跳过的类目 ,SimpleCategory
        */
       skipCopyCategorySetting?: {
         fullCid?: string;
         fullCname?: string;
         [k: string]: any;
       }[];
       [k: string]: any;
     };
     /**
      * 指定复制设置项 ,SpecialCopyInfoSettingBo
      */
     specialCopyInfo?: {
       /**
        * 设置了需要指定的类目 ,SimpleCategory
        */
       specialCopyCategorySetting?: {
         fullCid?: string;
         fullCname?: string;
         [k: string]: any;
       }[];
       [k: string]: any;
     };
     /**
      * 自动下单
      */
     autoOrder?: boolean;
     /**
      * 自动备注
      */
     autoMemo?: boolean;
     /**
      * 自动售后
      */
     autoRefund?: boolean;
     /**
      * 是否默认模板
      */
     isDefault?: number;
     /**
      * 更新时间
      */
     modified?: string;
     shopType?: string;
     /**
      * 场景区分：1：铺货业务，2：多店同步业务
      */
     scenceType?: number;
     /**
      * 查询订单设置
      */
     queryOrderSetting?: boolean;
     /**
      * 版本链路
      */
     linkType?: number;
     /**
      * 尺码表 ,SizeInfoSettingBo
      */
     sizeInfo?: {
       /**
        * (该参数为map)
        */
       sizeInfoMap?: {
         /**
          * String
          */
         mapKey?: {
           [k: string]: any;
         };
         /**
          * CategoryInfo
          */
         mapValue?: {
           cTreeId?: string;
           cTreeName?: string;
           cName?: string;
           [k: string]: any;
         };
         [k: string]: any;
       };
       templateId?: string;
       templateName?: string;
       [k: string]: any;
     }[];
     [k: string]: any;
   }[];
   [k: string]: any;
 };
 [k: string]: any;
}

export interface PlatformShopPuhuoSettingData {
   list: {
        /**
         * 是否选择当前模板
         */
        selected?: number;
        /**
         * 设置模板id
         */
        id?: number;
        /**
         * 设置模板名称
         */
        templateName?: string;
        /**
         * 货源平台1688PPW
         */
        fromPlatform?: string;
        /**
         * 平台
         */
        platform: string;
        /**
         * 店铺id
         */
        shopId: number;
        /**
         * 店铺名
         */
        shopName?: string;
        /**
         * 平台用户唯一标识
         */
        userId?: string;
        /**
         * 是否开启图片空间自动清理
         */
        pictureClean?: boolean;
        /**
         * 类目匹配方式,1智能匹配（默认）,2手动匹配,3指定类目铺货
         */
        matchType?: number;
        /**
         * 平台类目id,<p>,这里类目是是多级拼接，1级,2级,3级
         */
        platformCid?: string;
        /**
         * 平台类目名称,<p>,这里类目是是多级拼接，1级>2级>3级
         */
        platformCidName?: string;
        /**
         * 其他配置信息json格式,{,"autoDelivery":1,,"autoFill":1,,"autoWhiteBackground":1,,"deliveryTime":1,,"freightTemplate":0,,"groupPrice":{,"removeDecimalPoint":1,,"removeMethod":0,},,"inventorySettings":1,,"payTenForOneFake":1,,"preSale":0,,"promiseToShip":0,,"sellingPriceSetting":{,"markup":0,,"percentage":0,,"removeDecimalPoint":1,,"removeMethod":0,},,"sevenDay":1,,"shelfSetting":1,,"singlePrice":{,"markup":0,,"removeDecimalPoint":1,,"removeMethod":0,},,"underlinedPrice":{,"markup":0,,"removeDecimalPoint":1,,"removeMethod":0,},,<p>,} ,ShopSettingBo
         */
        extraSetting?: {
          platform?: string;
          /**
           * 平台：全平台,<p>,上架设置1：直接上架2：存放仓库
           */
          shelfSetting?: number;
          /**
           * 平台：抖音即时零售,上架门店选择直接上架时对应的门店配置 ,ShelfStoresSetting
           */
          shelfStores?: {
            /**
             * 0:所有门店，1：部分选中门店
             */
            type?: number;
            /**
             * type为1时，这里是选择的门店集合 ,ShelfStores
             */
            shelfStoresList?: {
              /**
               * 门店id
               */
              storeId?: number;
              /**
               * 门店名称
               */
              name?: string;
              [k: string]: any;
            }[];
            [k: string]: any;
          };
          /**
           * 平台：抖音即时零售,商品商家编码
           */
          goodsMerchantCode?: string;
          /**
           * 平台：抖音即时零售,<p>,重量设置 ,WeightInfo
           */
          weightInfo?: {
            /**
             * 重量值
             */
            value?: string;
            /**
             * 单位
             */
            unit?: string;
            [k: string]: any;
          };
          /**
           * 平台：淘宝、抖音、快手、微店、微信小商店,<p>,商品售价设置 ,SellingPriceSetting
           */
          sellingPriceSetting?: {
            /**
             * 价格类型0：代发价，1：批发价默认代发价
             */
            priceType?: number;
            /**
             * 百比例
             */
            percentage?: number;
            /**
             * 加价
             */
            markup?: string;
            /**
             * 是否去掉小数点1=直接去掉0=不去掉（保留角分）2=固定小数
             */
            removeDecimalPoint?: number;
            /**
             * 固定小数点后的内容
             */
            fixedPointPrice?: string;
            /**
             * 冲突字段基于removeDecimalPoint是直接去掉的情况下,0=四舍五入1=直接去掉,废弃
             */
            removeMethod?: number;
            /**
             * 冲突类型,<p>,售价类型：0-分销价1-利润率
             */
            sellingPriceType?: number;
            [k: string]: any;
          };
          singlePrice?: {
            /**
             * 价格类型0：代发价，1：批发价默认代发价
             */
            priceType?: number;
            /**
             * 百比例
             */
            percentage?: number;
            /**
             * 加价
             */
            markup?: string;
            /**
             * 是否去掉小数点1=直接去掉0=不去掉（保留角分）2=固定小数
             */
            removeDecimalPoint?: number;
            /**
             * 固定小数点后的内容
             */
            fixedPointPrice?: string;
            /**
             * 冲突字段基于removeDecimalPoint是直接去掉的情况下,0=四舍五入1=直接去掉,废弃
             */
            removeMethod?: number;
            /**
             * 冲突类型,<p>,售价类型：0-分销价1-利润率
             */
            sellingPriceType?: number;
            [k: string]: any;
          };
          groupPrice?: {
            /**
             * 价格类型0：代发价，1：批发价默认代发价
             */
            priceType?: number;
            /**
             * 百比例
             */
            percentage?: number;
            /**
             * 加价
             */
            markup?: string;
            /**
             * 是否去掉小数点1=直接去掉0=不去掉（保留角分）2=固定小数
             */
            removeDecimalPoint?: number;
            /**
             * 固定小数点后的内容
             */
            fixedPointPrice?: string;
            /**
             * 冲突字段基于removeDecimalPoint是直接去掉的情况下,0=四舍五入1=直接去掉,废弃
             */
            removeMethod?: number;
            /**
             * 冲突类型,<p>,售价类型：0-分销价1-利润率
             */
            sellingPriceType?: number;
            [k: string]: any;
          };
          marketPrice?: {
            /**
             * 价格类型0：代发价，1：批发价默认代发价
             */
            priceType?: number;
            /**
             * 百比例
             */
            percentage?: number;
            /**
             * 加价
             */
            markup?: string;
            /**
             * 是否去掉小数点1=直接去掉0=不去掉（保留角分）2=固定小数
             */
            removeDecimalPoint?: number;
            /**
             * 固定小数点后的内容
             */
            fixedPointPrice?: string;
            /**
             * 冲突字段基于removeDecimalPoint是直接去掉的情况下,0=四舍五入1=直接去掉,废弃
             */
            removeMethod?: number;
            /**
             * 冲突类型,<p>,售价类型：0-分销价1-利润率
             */
            sellingPriceType?: number;
            [k: string]: any;
          };
          /**
           * 平台：除拼多多都有,<p>,追加运费设置 ,AppendFreightSetting
           */
          appendFreightSetting?: {
            /**
             * 追加运费开关0:关闭1:开启,新用户默认关闭
             */
            flag?: number;
            /**
             * 省
             */
            province?: string;
            /**
             * 运费占比
             */
            freightRate?: number;
            /**
             * 追加金额
             */
            appendAmount?: number;
            [k: string]: any;
          };
          /**
           * 平台：淘宝、天猫,<p>,商家编码设置 ,MerchantCode
           */
          merchantCode?: {
            /**
             * 商家开关0:关闭1:开启
             */
            flag?: number;
            /**
             * 商家编码
             */
            code?: string;
            [k: string]: any;
          };
          /**
           * 平台：全平台,<p>,库存计数：1：使用商品来源2：统一修改库存
           */
          inventoryNumSettings?: number;
          /**
           * 抖音店长：sku库存低于这个值设置库存为0
           */
          lowerStockToZero?: number;
          /**
           * 抖音店长：sku库存低于这个值sku设置为下线
           */
          lowerStockOffline?: number;
          /**
           * 抖音店长：来源库存小于0时将库存设置为这个数
           */
          fromStockZeroSetTo?: number;
          /**
           * 两件折扣->满两件打折默认95,示例：95（代表商品上已设置9.5折的折扣值）
           */
          twoPiecesDiscount?: number;
          /**
           * 闲鱼发货地区id
           */
          districtId?: number;
          /**
           * 闲鱼发货地区名称
           */
          districtNames?: string;
          /**
           * 闲鱼上货店铺列表 ,String
           */
          publishUserName?: string[];
          /**
           * 闲鱼物流费用:单位元
           */
          logisticFee?: string;
          /**
           * 平台：全平台,<p>,统一修改库存数量
           */
          inventoryNums?: number;
          /**
           * 平台：全平台,<p>,标题设置 ,TitleSettings
           */
          titleSettings?: {
            /**
             * 标题前缀
             */
            pre?: string;
            /**
             * 标题后缀
             */
            end?: string;
            /**
             * 替换关键词 ,ReplaceStr
             */
            replaces?: {
              /**
               * 替换前
               */
              before?: string;
              /**
               * 替换之后
               */
              after?: string;
              [k: string]: any;
            }[];
            /**
             * 1：超长删最前，2：超长删最后
             */
            moreThanDel?: number;
            [k: string]: any;
          };
          /**
           * 平台：全平台,<p>,规格设置 ,SkipSettings
           */
          skipSettings?: {
            /**
             * 跳过重复铺货true是false否
             */
            isSkipRepeatDistrib?: boolean;
            [k: string]: any;
          };
          /**
           * 跳过设置-需保存到Oss内容 ,SkipSettingsOss
           */
          skipSettingsOss?: {
            /**
             * 标题删除关键词","隔开
             */
            titleDel?: string;
            /**
             * 标题跳过关键词","隔开
             */
            titleSkip?: string;
            /**
             * SKU删除关键词","隔开
             */
            skuDel?: string;
            /**
             * SKU跳过关键词","隔开
             */
            skuSkip?: string;
            /**
             * 代发价低于多少价格跳过单位分
             */
            priceLowSkip?: string;
            /**
             * 代发价高于多少价格跳过单位分
             */
            priceHighSkip?: string;
            /**
             * SKU低于多少价格跳过单位分
             */
            skuPriceLowSkip?: string;
            /**
             * SKU高于多少价格跳过单位分
             */
            skuPriceHighSkip?: string;
            [k: string]: any;
          };
          /**
           * 平台：全平台,<p>,SKU规格名称 ,SkuSpecificationName
           */
          skuSpecificationName?: {
            /**
             * 删除关键字
             */
            del?: string;
            /**
             * 标题前缀
             */
            pre?: string;
            /**
             * 标题后缀
             */
            end?: string;
            /**
             * 替换关键词 ,ReplaceStr
             */
            replaces?: {
              /**
               * 替换前
               */
              before?: string;
              /**
               * 替换之后
               */
              after?: string;
              [k: string]: any;
            }[];
            [k: string]: any;
          };
          /**
           * 平台：全平台,<p>,主图设置：是否删除主图0:不删除1：删除
           */
          isDeletePicture?: number;
          /**
           * 平台：全平台,<p>,主图设置：删除主图第几张
           */
          deletePictureNum?: number;
          /**
           * 平台：淘宝,<p>,运费模版类型1-复制货源运费模板2-使用本店运费模板默认2
           */
          freightTemplateType?: number;
          /**
           * 平台：全平台,<p>,运费模板默认-1
           */
          freightTemplate?: string;
          /**
           * 平台：全平台,<p>,物流模板默认-1
           */
          logisticTemplate?: string;
          /**
           * 商品配送方式,4-快递发货;8-同城配送;16-到店自提可多选多选,分割
           */
          itemDeliveryList?: string;
          /**
           * 平台：拼多多、快手,<p>,发货时间,<p>,快手拼多多用承诺发货默认-1,单位：秒，取值86400,172800,259200,-1代表发货时间不承诺前端传了24,48,72
           */
          promiseToShip?: number;
          /**
           * 是否预售默认0-现货发货，1-预售发货，2-阶梯发货，默认0
           */
          preSale?: number;
          /**
           * 平台：抖店、拼多多、快手,<p>,七天无理由,<p>,抖店是否支持七天无理由退货0不支持，1支持，2支持（拆封后不支持）,<p>,快手类目退款规则列表：1：支持7天无理由退货，4：不支持7天无理由退货，5：支持7天无理由退货(拆封后不支持)，6：支持7天无理由退货(激活后不支持)，7：支持7天无理由退货(安装后不支持)，8：支持7天无理由退货(定制类不支持)，9：支持7天无理由退货(使用后不支持)，10：支持7天无理由退货(开窗后不支持)，11：随时退过期退，12：过期自动退，13：不可退
           */
          sevenDay?: number;
          /**
           * 平台：快手,服务承诺 ,ServicePromise
           */
          servicePromise?: {
            /**
             * 平台：快手,坏了包退0未选中，1选中默认
             */
            freshRotRefund?: number;
            /**
             * 平台：快手,破损包退0未选中，1选中默认
             */
            brokenRefund?: number;
            /**
             * 平台：快手,过敏包退0未选中，1选中默认
             */
            allergyRefund?: number;
            [k: string]: any;
          };
          /**
           * 平台：淘宝,<p>,自定义类目id逗号分割1,2,3
           */
          sellerCatIds?: string;
          /**
           * 平台：淘宝,<p>,发货时间,<p>,淘宝时间类型：0：48小时内发货2：大于48小时发货3：24小时发货,默认为0
           */
          deliveryTimeType?: number;
          /**
           * 平台：淘宝,<p>,发货时间天数例如3，deliveryTimeType为2时生效,<p>
           */
          tbDeliveryTime?: number;
          /**
           * 品牌自定义映射关系 ,BrandCustomGroup
           */
          brandCustomMapping?: {
            /**
             * SimpleCategory
             */
            brandCategory?: {
              fullCid?: string;
              fullCname?: string;
              [k: string]: any;
            };
            /**
             * SimpleBrand
             */
            simpleBrand?: {
              brandId?: string;
              brandName?: string;
              [k: string]: any;
            };
            [k: string]: any;
          }[];
          /**
           * 平台：淘宝抖音,<p>,主图视频上传开关1上传0不上传
           */
          imageVideoUpload?: number;
          /**
           * 平台：全平台,<p>,详情外链及其内容过滤1打开0关闭
           */
          remove1688OutLink?: number;
          /**
           * 平台:全平台,<p>,白底图自动生成1打开0关闭
           */
          generateWhitePic?: number;
          /**
           * 平台:全平台,<p>,生成第几张白底图
           */
          generateWhitePicNum?: number;
          /**
           * 平台：全平台,<p>,详情描述：删除详情图,<p>,删除详情图类型1:删除详情图前后几张2：删除详情图第几张3：两个都选
           */
          deleteDetailPicType?: number;
          /**
           * 平台：全平台,<p>,详情描述：删除详情图前几张,<p>,删除详情图张数：配合deleteDetailPicType使用,多张用";"隔开
           */
          deleteDetailPicNumPre?: string;
          /**
           * 平台：全平台,<p>,详情描述：删除详情图后几张,<p>,删除详情图张数：配合deleteDetailPicType使用,多张用";"隔开
           */
          deleteDetailPicNumEnd?: string;
          /**
           * 平台：全平台,<p>,详情描述：指定位置删除详情图,<p>,删除详情图张数：配合deleteDetailPicType使用,多张用";"隔开
           */
          deleteDetailPicNum?: string;
          /**
           * 平台：全平台,<p>,详情页前添加详情图 ,String
           */
          addDetailPicPre?: string[];
          /**
           * 平台：全平台,<p>,详情页前添加文字转图片
           */
          addDetailPreText?: string;
          /**
           * 平台：全平台,<p>,详情页前添加文字转图片的图片
           */
          addDetailPreTextPic?: string;
          /**
           * 平台：全平台,<p>,详情页后添加详情图 ,String
           */
          addDetailPicEnd?: string[];
          /**
           * 平台：淘宝,<p>,图片空间：节省图片空间
           */
          zipImage?: boolean;
          /**
           * 平台：淘宝,<p>,图片空间：节省图片空间，图片大小，单位kb张
           */
          zipImageSize?: number;
          /**
           * 平台：全平台,<p>,详情页后添加文字转图片
           */
          addDetailEndText?: string;
          /**
           * 平台：全平台,<p>,详情页后添加文字转图片的图片
           */
          addDetailEndTextPic?: string;
          /**
           * 0:第一张主图，1：第二张主图，2：第三张主图，3：第四张主图，4：第五张主图，999：末尾主图，-1：白底图 ,Integer
           */
          addMainPicToDetail?: number[];
          /**
           * 0：主图添加到详情页前，1：主图添加到详情页后，2：主图添加详情页随机位置
           */
          addMainPicToDetailPosition?: number;
          /**
           * 平台：全平台,<p>,支持违禁词过滤1支持0不支持
           */
          filterForbiddenWord?: number;
          /**
           * 平台：京东,<p>,品牌id
           */
          brandId?: number;
          /**
           * 平台：京东,<p>,品牌名称
           */
          brandName?: string;
          /**
           * 平台：抖店,<p>,必须三包服务:1：保修期内提供维修（寄修）2：延长售后服务有效期
           */
          mustThreeGuaranteesType?: number;
          /**
           * 平台：抖店,<p>,必须三包服务天数：,<p>,单位：天数
           */
          mustThreeGuaranteesDays?: number;
          /**
           * 平台：抖店,<p>,可选三包服务:0:不包含三包1：保修期内提供维修（寄修）2：延长售后服务有效期
           */
          notMustThreeGuaranteesType?: number;
          /**
           * 平台：抖店,<p>,可选三包服务天数,<p>,单位：天数
           */
          notMustThreeGuaranteesDays?: number;
          /**
           * 基础品牌设置 ,CommonBrandSetting
           */
          commonBrandSetting?: {
            /**
             * 本店品牌必填时货源有品牌，则本店品牌设置配置项
             */
            mustOnFromItemExistConf?: number;
            /**
             * 本店品牌必填时货源无品牌，则本店品牌设置配置项
             */
            mustOnFromItemWithoutConf?: number;
            /**
             * 本店品牌非必填时则本店品牌设置配置项
             */
            noMustOnItemConf?: number;
            [k: string]: any;
          };
          /**
           * 抖音-支付方式：0：货到付款1：在线支付2：在线支付货到付款
           */
          payMethod?: number;
          /**
           * 抖音每次限购数量
           */
          perCumulativePurchase?: number;
          /**
           * 抖音单用户累计限购数量
           */
          cumulativePurchase?: number;
          /**
           * 抖音每次至少购买
           */
          perLeastPurchase?: number;
          /**
           * 发货模式：0：现货发货1：全款预售发货2：使用商品来源
           */
          deliveryMethod?: number;
          /**
           * 预售时间：
           */
          preSaleTime?: string;

          /**
           * 自动发货1=自动发货0=不自动
           */
          autoDelivery?: number;
          /**
           * 平台：全平台,<p>,白底图：自动生成白底图1=自动0=不自动（弃用）
           */
          autoWhiteBackground?: number;
          /**
           * 平台：拼多多,<p>,假一罚十,<p>,加1赔101=开启0=不开启
           */
          payTenForOneFake?: number;
          /**
           * 平台：拼多多,<p>,警告提醒,<p>,是否忽略异常0忽略1不忽略默认1不忽略
           */
          ignoreEditWarn?: number;
          /**
           * 过滤二维码1=开启0=不开启默认开启
           */
          qrcodeFilter?: number;
          /**
           * 抖音商品划线价 ,UnderlinedPrice
           */
          underlinedPrice?: {
            /**
             * 划线价比例
             */
            percentage?: number;
            /**
             * 加价
             */
            markup?: string;
            /**
             * 是否去掉小数点1=去掉0=不去掉
             */
            removeDecimalPoint?: number;
            /**
             * 0=四舍五入1=直接去掉
             */
            removeMethod?: number;
            [k: string]: any;
          };
          /**
           * 平台：淘宝、快手,<p>,减库存类型：1-拍下减库存2-付款减库存
           */
          inventorySettings?: number;
          /**
           * 平台：抖店,<p>,客服电话
           */
          mobile?: string;
          /**
           * 平台：小红书1688抖店,发货时间-1=不承诺999=当天发货2=48h两天、3=72h3天、5=5天、7=7天、10=10天、15=15天1=1天次日发货
           */
          deliveryTime?: number;
          /**
           * 预售结束后发货：单位天
           */
          afterSaleDeliveryTime?: number;
          /**
           * 支付完成后发货：单位天
           */
          afterPayDeliveryTime?: number;
          /**
           * 爆单设置 ,ExplosiveOrderSetting
           */
          explosiveOrderSetting?: {
            /**
             * 1开启0关闭
             */
            open?: number;
            /**
             * 1=智能匹配0=手动选择
             */
            matchCategory?: number;
            /**
             * 类目id
             */
            categoryId?: string;
            /**
             * 类目名称
             */
            categoryName?: string;
            /**
             * 服务保障 ,Integer
             */
            serviceGuarantee?: number[];
            /**
             * 起始价格
             */
            startPrice?: string;
            /**
             * 结束价格
             */
            endPrice?: string;
            /**
             * 上货量
             */
            distribNum?: number;
            /**
             * 标志1=开启0=关闭
             */
            offShelfFlag?: number;
            /**
             * 动销设置
             */
            offShelfDay?: number;
            /**
             * 动销操作：1删除，2下架
             */
            offShelfAct?: number;
            [k: string]: any;
          };
          /**
           * 智能匹配自定义类目映射关系组 ,SimpleCategoryGroup
           */
          smartMatchCustomMapping?: {
            /**
             * SimpleCategory
             */
            fromSimpleCategory?: {
              fullCid?: string;
              fullCname?: string;
              [k: string]: any;
            };
            /**
             * SimpleCategory
             */
            toSimpleCategory?: {
              fullCid?: string;
              fullCname?: string;
              [k: string]: any;
            };
            [k: string]: any;
          }[];
          /**
           * 属性预设设置 ,ShopAttributeBo
           */
          attributeDefault?: {
            name?: string;
            value?: string;
            isForce?: number;
            msg?: string;
            [k: string]: any;
          }[];
          /**
           * 货源同步开关
           */
          itemSourceSync?: boolean;
          /**
           * 货源库存同步配置 ,StockSyncConfig
           */
          stockSyncConfig?: {
            /**
             * 标志1=开启0=关闭
             */
            flag?: number;
            /**
             * 选项1,2,3对应单选按钮1,2,3
             */
            stockSyncOption?: number;
            /**
             * stockSyncOption为1时，是否开启低于多少时不修改库存选项
             */
            stockModifyOption?: number;
            /**
             * stockSyncOption为1时，低于多少时不修改库存
             */
            stockModifyMinNum?: number;
            /**
             * 货源库存低于100时提醒
             */
            stockWarnMinNum?: number;
            [k: string]: any;
          };
          /**
           * 货源价格同步配置 ,PriceSyncConfig
           */
          priceSyncConfig?: {
            /**
             * 标志1=开启0=关闭
             */
            flag?: number;
            /**
             * 货源涨价配置，1-仅提醒
             */
            priceUpOption?: number;
            /**
             * 货源降价配置，1-仅提醒
             */
            priceDownOption?: number;
            [k: string]: any;
          };
          /**
           * 货源状态同步配置 ,StatusSyncConfig
           */
          statusSyncConfig?: {
            /**
             * 标志1=开启0=关闭
             */
            flag?: number;
            /**
             * 货源sku失效配置，1-仅提醒
             */
            skuInvalidOption?: number;
            /**
             * 货源sku上架配置，1-仅提醒
             */
            skuUpOption?: number;
            /**
             * 货源sku下架配置，1-仅提醒
             */
            skuDownOption?: number;
            [k: string]: any;
          };
          /**
           * 平台：拼多多,主图视频是否上传到多多视频，也叫视频详解,<p>,先不给默认值，防止序列化到其他平台去
           */
          uploadDuoduoVideo?: boolean;
          [k: string]: any;
        };
        /**
         * scenceType==2：多店同步业务场景下，关联店铺的运费模版设置 ,MultipleSyncInfoSettingBo
         */
        multipleSyncInfo?: {
          /**
           * FreightTemplate
           */
          multipleFreightTemplateRelation?: {
            /**
             * 平台：全平台,<p>,运费模板默认0
             */
            freightTemplateId?: string;
            /**
             * 关联的店铺id
             */
            toShopId?: string;
            [k: string]: any;
          }[];
          [k: string]: any;
        };
        /**
         * 跳过复制设置项 ,SkipCopyInfoSettingBo
         */
        skipCopyInfo?: {
          /**
           * 设置了需要跳过的类目 ,SimpleCategory
           */
          skipCopyCategorySetting?: {
            fullCid?: string;
            fullCname?: string;
            [k: string]: any;
          }[];
          [k: string]: any;
        };
        /**
         * 指定复制设置项 ,SpecialCopyInfoSettingBo
         */
        specialCopyInfo?: {
          /**
           * 设置了需要指定的类目 ,SimpleCategory
           */
          specialCopyCategorySetting?: {
            fullCid?: string;
            fullCname?: string;
            [k: string]: any;
          }[];
          [k: string]: any;
        };
        /**
         * 自动下单
         */
        autoOrder?: boolean;
        /**
         * 自动备注
         */
        autoMemo?: boolean;
        /**
         * 自动售后
         */
        autoRefund?: boolean;
        /**
         * 是否默认模板
         */
        isDefault?: number;
        /**
         * 更新时间
         */
        modified?: string;
        shopType?: string;
        /**
         * 场景区分：1：铺货业务，2：多店同步业务
         */
        scenceType?: number;
        /**
         * 查询订单设置
         */
        queryOrderSetting?: boolean;
        /**
         * 版本链路
         */
        linkType?: number;
        /**
         * 尺码表 ,SizeInfoSettingBo
         */
        sizeInfo?: {
          /**
           * (该参数为map)
           */
          sizeInfoMap?: {
            /**
             * String
             */
            mapKey?: {
              [k: string]: any;
            };
            /**
             * CategoryInfo
             */
            mapValue?: {
              cTreeId?: string;
              cTreeName?: string;
              cName?: string;
              [k: string]: any;
            };
            [k: string]: any;
          };
          templateId?: string;
          templateName?: string;
          [k: string]: any;
        }[];
        [k: string]: any;
      }[]
}

export interface CategoryTreeRequest {
  platform: string;
  sellerId?: string;
  parentCategory?:string;
  cName?:string
}
export interface CategoryTreeResponse {
  data: CategoryTreeItem[];
}

export interface CategoryTreeItem {
  categoryId: string;
  isLeaf: boolean;
  level: number;
  name: string;
  parentId: string;
  children?: CategoryTreeItem[];
}