import axios, { instance } from "@/utils/request";
import {
	TradeLiveAuthorQueryPageListRequest,
	TradeLiveAuthorQueryPageListResponse,
	TradeLiveAuthorSaveRequest,
	TradeLiveAuthorSaveResponse,
	TradeLiveAuthorDeleteRequest,
	TradeLiveAuthorDeleteResponse,
	TradeLivePrintGetAuthorListRequest,
	TradeLivePrintGetAuthorListResponse,
	TradeLivePrintGetLiveShopListRequest,
	TradeLivePrintGetLiveShopListResponse,
	TradeLivePrintIntervalFakerTradeToSeeRequest,
	TradeLivePrintIntervalFakerTradeToSeeResponse,
	TradeLivePrintSendFakerTradeToSeeRequest,
	TradeLivePrintSendFakerTradeToSeeResponse
} from "@/types/trade/index/live";



/**
* 达人分页查询
*/
export function TradeLiveAuthorQueryPageListApi(data: TradeLiveAuthorQueryPageListRequest) {
	return axios.post<unknown, TradeLiveAuthorQueryPageListResponse>('/trade/liveAuthor/queryPageList', data).then(res => res.data);
}

/**
* 添加、修改达人
*/
export function TradeLiveAuthorSaveApi(data: TradeLiveAuthorSaveRequest) {
	return axios.post<unknown, TradeLiveAuthorSaveResponse>('/trade/liveAuthor/save', data).then(res => res.data);
}


/**
* 删除达人
*/
export function TradeLiveAuthorDeleteApi(data: TradeLiveAuthorDeleteRequest) {
	return axios.post<unknown, TradeLiveAuthorDeleteResponse>('/trade/liveAuthor/delete', data).then(res => res.data);
}

/**
* 达人下拉框
*/
export function TradeLivePrintGetAuthorListApi(data: TradeLivePrintGetAuthorListRequest) {
	return axios.post<unknown, TradeLivePrintGetAuthorListResponse>('/trade/livePrint/getAuthorList', data).then(res => res.data);
}

/**
* 获取正在直播中的店铺信息
*/
export function TradeLivePrintGetLiveShopListApi(data: TradeLivePrintGetLiveShopListRequest) {
	return axios.post<unknown, TradeLivePrintGetLiveShopListResponse>('/trade/livePrint/getLiveShopList', data).then(res => res.data);
}

/**
* 轮询生成虚拟订单
*/
export function TradeLivePrintIntervalFakerTradeToSeeApi(data: TradeLivePrintIntervalFakerTradeToSeeRequest) {
	return axios.post<unknown, TradeLivePrintIntervalFakerTradeToSeeResponse>('/trade/livePrint/intervalFakerTradeToSee', data).then(res => res.data);
}

/**
* 生成虚拟订单
*/
export function TradeLivePrintSendFakerTradeToSeeApi(data: TradeLivePrintSendFakerTradeToSeeRequest) {
	return axios.post<unknown, TradeLivePrintSendFakerTradeToSeeResponse>('/trade/livePrint/sendFakerTradeToSee', data).then(res => res.data);
}