import React, { useEffect, useState } from 'react';
import styles from './index.module.scss';

// 图片资源引入
import Banner1 from './assets/banner-1.png';
import We from './assets/we.png';
import Product from './assets/product.png';
import Fuli from './assets/fuli.png';
import Zixun from './assets/zixun.png';
import Icon from '@/components/Icon';

const Channel: React.FC = () => {
	const [activeTab, setActiveTab] = useState('我们是谁');
	const [isTabsFixed, setIsTabsFixed] = useState(false);

	useEffect(() => {
		// 重置导航
		if (window.kdHepler && window.kdHepler.resetNav) {
			window.kdHepler.resetNav(5);
		}

		// 滚动监听
		const handleScroll = () => {
			const scrollTop = getScrollTop();
			if (scrollTop >= 445) {
				if (!isTabsFixed) {
					setIsTabsFixed(true);
				}
			} else if (isTabsFixed) {
				setIsTabsFixed(false);
			}

			// 根据滚动位置更新当前激活的标签
			const sections = ['我们是谁', '我们的优势', '服务保障', '加入我们'];
			const sectionElements = sections.map(id => document.getElementById(id));

			for (let i = sectionElements.length - 1; i >= 0; i--) {
				const element = sectionElements[i];
				if (element) {
					const rect = element.getBoundingClientRect();
					if (rect.top <= 100) {
						setActiveTab(sections[i]);
						break;
					}
				}
			}
		};

		window.addEventListener('scroll', handleScroll);

		return () => {
			window.removeEventListener('scroll', handleScroll);
		};
	}, [isTabsFixed]);

	const getScrollTop = () => {
		if (window.pageYOffset) {
			return window.pageYOffset;
		} else if (document.compatMode && document.compatMode !== 'BackCompat') {
			return document.documentElement.scrollTop;
		} else if (document.body) {
			return document.body.scrollTop;
		}
		return 0;
	};

	const handleTabClick = (tabName: string) => {
		setActiveTab(tabName);

		// 平滑滚动到对应区域
		const targetElement = document.getElementById(tabName);
		if (targetElement) {
			targetElement.scrollIntoView({
				behavior: 'smooth',
				block: 'start'
			});
		}
	};

	return (
		<div style={ { background: '#fff' } }>
			<nav className={ styles.navBar }>
				<div className={ styles.navLogoContainer }>
					<Icon style={ { color: "#FD8204" } } size={ 216 } type="xinzhushouerplogo" />
					<div />
				</div>
			</nav>
			<div className={ styles['channel-wrap'] }>
				<div className={ `${styles['channel-header']} ${styles['align-c']}` }>
					<div className={ `${styles['inline-block']} ${styles['height-100']} ${styles['channel-header-l']}` }>
						<div className={ styles['channel-header-label-box'] }>
							<div className={ styles['channel-header-label'] }>0保证金</div>
							<div className={ styles['channel-header-label'] }>0加盟费</div>
						</div>
						<p className={ styles['channel-header-test1'] }>快递助手ERP合伙人招募</p>
						<p className={ styles['channel-header-test2'] }>超多福利｜超高佣金｜全程帮扶｜携手同行</p>
						<a
							href="https://ding.cjfx.cn/f/vigmwamk"
							className={ styles['channel-header-zixun'] }
							target="_blank"
							rel="noopener noreferrer"
						>
							立即咨询
						</a>
					</div>
					<div className={ `${styles['inline-block']} ${styles['height-100']} ${styles['channel-header-r']}` }>
						<img src={ Banner1 } alt="banner" />
					</div>
				</div>

				<div className={ styles['channel-con'] }>
					<div className={ `${styles['channel-tabs']} ${isTabsFixed ? styles['tabs-fixed'] : ''}` }>
						<div className={ styles['channel-tabs-con'] }>
							<a
								className={ `${styles['channel-tab']} ${activeTab === '我们是谁' ? styles.act : ''}` }
								href="#我们是谁"
								onClick={ (e) => {
									e.preventDefault();
									handleTabClick('我们是谁');
								} }
							>
								我们是谁
							</a>
							<a
								className={ `${styles['channel-tab']} ${activeTab === '我们的优势' ? styles.act : ''}` }
								href="#我们的优势"
								onClick={ (e) => {
									e.preventDefault();
									handleTabClick('我们的优势');
								} }
							>
								我们的优势
							</a>
							<a
								className={ `${styles['channel-tab']} ${activeTab === '服务保障' ? styles.act : ''}` }
								href="#服务保障"
								onClick={ (e) => {
									e.preventDefault();
									handleTabClick('服务保障');
								} }
							>
								服务保障
							</a>
							<a
								className={ `${styles['channel-tab']} ${activeTab === '加入我们' ? styles.act : ''}` }
								href="#加入我们"
								onClick={ (e) => {
									e.preventDefault();
									handleTabClick('加入我们');
								} }
							>
								加入我们
							</a>
						</div>
					</div>
					<div className={ styles['channel-we'] } id="我们是谁">
						<div className={ styles['module-title'] }>
							<h3>我们是谁</h3>
							<p />
						</div>
						<div className={ styles['channel-we-con'] }>
							<div className={ styles['channel-we-text'] }>
								<div>快递助手ERP是杭州其乐融融科技有限公司旗下的明星产品，为中小商家提供多平台店铺订单、库存、售后、代发、报表等一站式电商运营服务。
									目前支持淘宝/天猫、拼多多、抖店、1688、快手、京东、微信小店、小红书、淘工厂、有赞、得物、快团团等十多个国内电商平台，总付费用户量超过1w+，并仍在稳步增长中。
								</div>
							</div>
							<img src={ We } className={ styles['channel-we-img'] } alt="我们是谁" />
						</div>
					</div>
					<div className={ styles['channel-part1'] } id="我们的优势">
						<div className={ styles['module-title'] }>
							<h3>我们的优势</h3>
							<p />
						</div>
						<div className={ styles['channel-product'] }>
							<img src={ Product } alt="产品优势" style={ { width: '1080px' } } />
						</div>
					</div>
					<div className={ styles['channel-part3'] } id="服务保障">
						<div className={ styles['module-title'] }>
							<h3>服务保障</h3>
							<p />
						</div>
						<div className={ styles['channel-plat'] }>
							<img src={ Fuli } alt="服务保障" />
						</div>
					</div>
					<div className={ styles['channel-join-we'] } id="加入我们">
						<div className={ styles['module-title'] }>
							<h3>加入我们</h3>
							<p />
						</div>
						<div className={ styles['channel-join-we-con'] }>
							<a
								href="https://ding.cjfx.cn/f/vigmwamk"
								className={ `${styles['channel-join-we-btn']} ${styles['channel-join-we-solid-btn']}` }
								target="_blank"
								rel="noopener noreferrer"
							>
								立即加入
							</a>
							<div className={ `${styles['channel-join-we-btn']} ${styles['channel-join-we-hollow-btn']}` }>
								扫码咨询
								<div className={ styles['channel-join-we-QRCode'] }>
									<img src={ Zixun } alt="咨询二维码" />
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default Channel;
