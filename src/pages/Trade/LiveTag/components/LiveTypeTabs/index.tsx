import React, { useState, useEffect } from 'react';
import cs from 'classnames';
import { useHistory } from "react-router-dom";
import styles from './index.module.scss';

// 导入图片资源
import shopIcon from '@/assets/image/live/店铺直播@2x.png';
import shopHoverIcon from '@/assets/image/live/店铺直播************';
import shopBgImage from '@/assets/image/live/bg_店铺直播@1x.png';
import shopBgImage2 from '@/assets/image/live/1920_店铺*********';
import shopDesImg from '@/assets/image/live/店铺直播_文字@2x.png';
import shopDesIcon from '@/assets/image/live/img_店铺直播@2x.png';

import talentIcon from '@/assets/image/live/达人直播@2x.png';
import talentHoverIcon from '@/assets/image/live/达人直播************';
import talentBgImage from '@/assets/image/live/bg_达人直播@1x.png';
import talentBgImage2 from '@/assets/image/live/1920_达人*********';
import talentDesImg from '@/assets/image/live/达人直播_文字@2x.png';
import talentDesIcon from '@/assets/image/live/img_达人直播@2x.png';

import danmuIcon from '@/assets/image/live/弹幕扣号直播(即将上线...)@2x.png';
import icon1 from '@/assets/image/live/使用流程@2x.png';
import icon2 from '@/assets/image/live/路径@2x.png';

interface LiveTypeTabsProps {
  activeKey: string;
  onChange: (key: string) => void;
}

interface TabItem {
  key: string;
  title: string;
  icon: string;
  hoverIcon: string;
  bgImage: string;
  bgImage2: string;
  desImg: string;
  desIcon: string;
  disabled?: boolean;
}

const tabItems: TabItem[] = [
	{
		key: 'shop',
		title: '店铺直播',
		icon: shopIcon,
		hoverIcon: shopHoverIcon,
		bgImage: shopBgImage,
		bgImage2: shopBgImage2,
		desImg: shopDesImg,
		desIcon: shopDesIcon,
	},
	{
		key: 'talent',
		title: '达人直播',
		icon: talentIcon,
		hoverIcon: talentHoverIcon,
		bgImage: talentBgImage,
		bgImage2: talentBgImage2,
		desImg: talentDesImg,
		desIcon: talentDesIcon,
	},
	{
		key: 'danmu',
		title: '弹幕扣号直播（即将上线）',
		icon: danmuIcon,
		hoverIcon: danmuIcon,
		bgImage: '',
		bgImage2: '',
		disabled: true,
		desImg: '',
		desIcon: ''
	}
];

// 自定义hook来检测屏幕宽度
const useScreenWidth = () => {
	const [screenWidth, setScreenWidth] = useState(window.innerWidth);

	useEffect(() => {
		const handleResize = () => {
			setScreenWidth(window.innerWidth);
		};

		window.addEventListener('resize', handleResize);
		return () => window.removeEventListener('resize', handleResize);
	}, []);

	return screenWidth;
};

const LiveTypeTabs: React.FC<LiveTypeTabsProps> = ({ activeKey, onChange }) => {
	const screenWidth = useScreenWidth();
	const history = useHistory();
    
	// console.log('%c [ screenWidth ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', screenWidth);

	const handleTabClick = (key: string, disabled?: boolean) => {
		if (!disabled) {
			onChange(key);
		}
	};

	// 根据屏幕宽度选择背景图
	const getBgImage = (item: TabItem) => {
		if (screenWidth > 1900 && item.bgImage2) {
			return item.bgImage2;
		}
		return item.bgImage;
	};

	return (
		<div className={ styles['live-type-tabs'] }>
			<div className={ styles['tabs-header'] }>
				<div className={ styles['tabs-header-content'] }>
					{tabItems.map((item) => (
						<div
							key={ item.key }
							className={ cs(styles['tab-item'], {
								[styles['active']]: activeKey === item.key,
								[styles['disabled']]: item.disabled,
							}) }
							onClick={ () => handleTabClick(item.key, item.disabled) }
						>
							<div className={ styles['tab-icon'] }>
								<img 
									src={ activeKey === item.key ? item.hoverIcon : item.icon } 
									alt={ item.title }
								/>
							</div>
						</div>
					))}
				</div>

				<div className={ styles['tabs-header-tip'] }>
					<img 
						src={ icon1 } 
						alt="使用流程" 
						className={ cs(styles.icon, 'r-pointer') } 
						onClick={ () => {
							window.open('https://www.yuque.com/hangzhouqilerongrongkejiyouxiangongsi/hi5ca3/tn6b19mbbpc0evi6?singleDoc#', '_blank');
						} }
					/>
					<div className={ styles.dot }>1</div>
					<div className={ styles.title }>直播订单设置</div>
					<img src={ icon2 } alt="" className={ styles.icon2 } />
					<div className={ styles.dot }>2</div>
					<div className={ styles.title }>自动打印标签</div>
					<img src={ icon2 } alt="" className={ styles.icon2 } />
					<div className={ styles.dot }>3</div>
					<div
						className={ styles.titleClick }
						onClick={ () => {
							history.push("/trade/postPrint");
						} }
					>扫标签打印发货
					</div>
				</div>  
			</div>
      
			<div className={ styles['tab-content'] }>
				{tabItems.map((item) => (
					activeKey === item.key && (
						<div key={ item.key } className={ styles['content-wrapper'] }>
							<div 
								className={ styles['content-bg'] }
								style={ { backgroundImage: `url(${getBgImage(item)})` } }
							/>

							<img src={ item.desImg } alt="" className={ styles['content-description'] } />
							<img src={ item.desIcon } alt="" className={ styles['content-description2'] } />
						</div>
					)
				))}
			</div>
		</div>
	);
};

export default LiveTypeTabs;
