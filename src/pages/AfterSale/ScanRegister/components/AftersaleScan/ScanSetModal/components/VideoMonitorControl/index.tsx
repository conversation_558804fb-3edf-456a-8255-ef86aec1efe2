import React, { useEffect } from 'react';
import { Radio, Popover, Button } from 'antd';
import { observer } from 'mobx-react';
import Icon from '@/components/Icon';
import message from "@/components/message";
import videoMonitorStore from '@/stores/trade/videoMonitor';
import { tradeStore } from '@/stores';
import userStore from '@/stores/user';

interface VideoMonitorControlProps {
    videoMonitorState: boolean;
    onVideoMonitorChange: (enabled: boolean) => void;
	name:string
	isScanRegister?:boolean
}

const VideoMonitorControl: React.FC<VideoMonitorControlProps> = ({
	videoMonitorState,
	onVideoMonitorChange,
	name,
	isScanRegister
}) => {
	const { 
		isSocketConnected,
		checkKdzsPrintComponent,
		connectWs,
		disconnectWs,
		clearMessageCallbacks,
	} = videoMonitorStore;
	const { userInfo } = userStore;
	const { whiteListSetting } = userInfo || {};
	const whiteListSettingObj = JSON.parse(whiteListSetting || '{}');
	const { videoMonitor } = whiteListSettingObj || {}; // 视频监控开关
	const handleReconnect = async() => {
		console.log('重新连接');
		await checkKdzsPrintComponent();
		connectWs();
	};

	const handleDownload = () => {
		console.log('下载控件');
		const downloadUrl = (tradeStore.controlDonloadData || []).find(it => it.name === "快递助手ERP聚合打印控件")?.controlsArr[0]?.downLink;
		if (downloadUrl) {
			window.location.href = downloadUrl;
		} else {
			message.warning('请联系客服获取下载地址');
		}
	};

	return (
		<>
			<div className="config-item">
				<div className="config-label">
					{name || '视频监控：'}
					<Radio.Group
						value={ videoMonitorState ? 1 : 0 }
						style={ { marginLeft: 8 } }
						onChange={ e => {
							if (e.target.value === 1 && videoMonitor !== 1) {
								message.warning(isScanRegister ? "请联系销售开启功能后使用" : "视频监控需增购后使用，具体增购费用请联系销售增购后使用");
								return;
							}
							onVideoMonitorChange(e.target.value === 1);
						} }
					>
						<Radio value={ 0 }>关闭</Radio>
						<Radio value={ 1 } style={ { marginRight: 0 } }>开启</Radio>
					</Radio.Group>
					<Popover 
						content={ (
							<div>
								目前支持USB监控摄像头（多个品牌）以及IPC监控设备（海康威视等品牌），具体详见<a href="https://www.yuque.com/hangzhouqilerongrongkejiyouxiangongsi/hi5ca3/glroqagd4kb2gwx8?singleDoc#" target="_blank" rel="noreferrer" style={ { color: "#1890ff" } }>使用手册</a>
							</div>
						) }
						overlayStyle={ { maxWidth: 350 } }
					>
						<span>
							<Icon type="wenhao-xian" className="r-c-999 r-pointer" />
						</span>
					</Popover>
				</div>
			</div>

			{videoMonitorState ? (
				<div className="config-item" style={ { marginTop: 8, marginBottom: 8 } }>
					<div className="config-label r-flex r-ai-c">
						控件连接状态：
						{isSocketConnected ? (
							<span className="r-c-success">已连接</span>
						) : (
							<>
								<span style={ { color: "rgba(0, 0, 0, 0.45)" } }>未连接</span>
								<Button 
									type="link" 
									style={ { padding: 0, marginLeft: 8, color: "#1890ff" } }
									onClick={ handleReconnect }
								>
									重新连接
								</Button>
								<Button 
									type="link" 
									style={ { padding: 0, marginLeft: 8, color: "#1890ff" } }
									onClick={ handleDownload }
								>
									下载控件
								</Button>
							</>
						)}
					</div>
				</div>
			) : null}
		</>
	);
};

export default observer(VideoMonitorControl);
