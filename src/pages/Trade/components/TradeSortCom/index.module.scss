.itemLine {
	padding    : 4px 12px;
	line-height: 24px;
	cursor     : pointer;

	&:hover {
		background-color: #f9f9f9;
	}
}

.itemWrapper {
	overflow  : auto;
	max-height: 200px;
}

.infoPopContainer {
	width         : 360px;
	padding-bottom: 10px;
}

.title {
	margin-bottom   : 4px;
	color           : rgba(0, 0, 0, 0.85);
	font-size       : 16px;
	font-weight     : 500;
	line-height     : 24px;
	margin-top      : 16px;
	display         : inline-block;
	background-image: linear-gradient(to bottom, white 50%, #fceddc 50%);
}

.infoTitle {
	color      : #FD8204;
	font-size  : 18px;
	font-weight: 500;
}

.ruleContent {
	line-height: 20px;
	font-size  : 12px;
	color      : #333333;
}

.descDisable {

	.down,
	.up {
		color: rgba(0, 0, 0, 0.25) !important;
	}
}

.arrow {
	display                   : flex;
	flex-direction            : column;
	align-items               : center;
	justify-content           : center;
	border                    : 1px solid #d9d9d9;
	height                    : 32px;
	width                     : 32px;
	font-size                 : 12px;
	border-top-right-radius   : 2px;
	border-bottom-right-radius: 2px;
	margin-left               : -1px;
	color                     : rgba(0, 0, 0, 0.25);
	cursor                    : pointer;

	&.demo {
		width    : 20px;
		border   : none;
		display  : inline-flex;
		padding  : 0;
		transform: translateY(-4px);
		margin   : 0;

	}

	.up {
		transform: translateY(2px)
	}

	.down {
		transform: translateY(-2px)
	}

	&.isDesc {
		.down {
			color: #FD8204;
		}
	}

	&.isUp {
		.up {
			color: #FD8204;
		}
	}
}

.tradeSort {
	display     : flex;
	align-items : center;
	margin-right: 12px;

	:global {
		.ant-select-selector {
			border-top-right-radius   : 0 !important;
			border-bottom-right-radius: 0 !important;

			.ant-select-selection-item .customEdit {
				display: none;
			}
		}
	}
}

.row-dragging {
	background     : #fafafa;
	border         : 1px solid #ccc;
	display        : flex !important;
	align-items    : center !important;
	justify-content: space-between !important;
	padding        : 0 20px 0 4px !important;
	height         : 32px;

	.moveIcon {
		color: #f90;
		width: 20px;
	}
}

.sortTable {
	:global {
		.ant-table-cell {
			vertical-align: middle;
		}
	}
}


.modalTitle {
	font-size  : 16px;
	font-weight: 500;
}

.modalText {
	font-size: 12px;
	color    : rgba(0, 0, 0, 0.45);
}

.rule {
	border-radius: 2px;
	padding      : 9px 16px;
	background   : #FFFBE6;
	box-sizing   : border-box;
	border       : 1px solid #FFE58F;
	font-size    : 14px;
	color        : rgba(0, 0, 0, 0.85);
	line-height  : 22px;
	margin-bottom: 16px;
}

.addIcon {
	height       : 32px;
	border-radius: 2px 0px 0px 2px;
	display      : flex;
	align-items  : center;
	text-wrap    : nowrap;
	padding      : 0 12px;
	font-size    : 14px;
	background   : #FAFAFA;
	box-shadow   : inset 1px 0px 0px 0px #D9D9D9, inset 0px 1px 0px 0px #D9D9D9, inset 0px -1px 0px 0px #D9D9D9;
}

.addSelect {
	margin-left: -1px;
	width      : 350px !important;

	:global {
		.ant-select-selector {
			border-top-left-radius   : 0 !important;
			border-bottom-left-radius: 0 !important;
		}
	}


}

.ruleItem {
	display: flex;
}

.moveIcon {
	cursor: grab;
	color : '#999';

	&:hover {
		color: #f90;
	}
}

.showSelect {

	// width: 380px;
	:global {
		.rc-virtual-list-holder {
			max-height: 320px !important;
		}
	}
}

.customNameStyle {
	max-width    : 250px;
	overflow     : hidden;
	text-overflow: ellipsis;
}