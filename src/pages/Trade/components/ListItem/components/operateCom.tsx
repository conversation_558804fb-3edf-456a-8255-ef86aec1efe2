import React, { useImperative<PERSON>and<PERSON>, useState } from "react";
import { <PERSON><PERSON>, Dropdown, <PERSON>u, Modal } from "antd";
import { observer } from "mobx-react";
import { runInAction } from 'mobx';
import { editGoodsStore, handStore, tradeStore } from "@/stores";
import { OccupyTradeOptType, handTradeOperateList, pendingTradeOperateList, riskControlTradeOperateList, tradeOperateList } from "@/pages/Trade/constants";
import { AFTERSALE_HANDORDER, PLAT_DW, PLAT_HAND, PLAT_SCM, PLAT_SCMHAND, stockVersion } from "@/constants";
import userStore from "@/stores/user";
import { IgnoreStatus, PendingStatus, TradeOptEnum, TradeStatus } from "@/utils/enum/trade";
import { getDeleteOrderTip, getEditGoodsTip, getHandEditTip, tradeStatus } from "@/pages/Trade/utils";
import { isSourceHand, isSourceScm } from "@/components-biz/ShopListSelect/shopListUtils";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import message from "@/components/message";
import { IPackage } from "@/pages/Trade/interface";
import { sendDelivery } from "../../BottomCom/utils";
import event from "@/libs/event";
import { TradeHandTradeCopyApi, TradeHandTradeDeleteApi } from "@/apis/trade/handOrder";
import { TradeOperateLogAddApi, TradeTradeDetailGetApi } from "@/apis/trade";
import { TradeCustomerAdd, TradeCustomerModify } from "@/apis/setting/customerManage";
import s from '@/pages/Trade/index.module.scss';
import FunctionPermissionCheck from "@/utils/permissionCheck/functionPermissionCheck";
import SplitOrder from '../../SplitOrder';
import { SysExchangeTypeEnum } from '@/pages/Trade/components/BottomCom/constants';


const OperateCom = observer(
	(props: {
		pack: IPackage;
		cRef?: React.Ref<any>
	}) => {
		const { pack, cRef } = props;
		const { isPending, isExpand, togetherId, noGoodsLink, trades, sellerId, platform, sellerNick, tids } = pack;
		const { tradeListStore: { toggleTogetherDetail, addHandOrder, deleteTradeSplice }, isMergePrint, selectedTemp, selectedTempGroup } = tradeStore;
		const { updateTradeOccupied } = tradeStore.tradeOptStore;
		const { setIsShowHandOrderModal, setHandData } = handStore;
		const {
			setIsShowChangeGoodsModal,
			setChangeGoodsData,
			setIsShowBatchChangeGoodsModal,
			setBatchChangeGoodsPackage,
			setIsShowBatchAddGoodsModal,
			setBatchAddGoodsPackage
		} = editGoodsStore;
		const [loading, setLoading] = useState<boolean>(false);
		const ignoreType = trades[0]?.ignoreType;
		// 过滤拆分订单操作按钮
		let _handTradeOperateList = handTradeOperateList;
		let _tradeOperateList = tradeOperateList;

		let _riskControlTradeOperateList = riskControlTradeOperateList;
		let _pendingTradeOperateList = pendingTradeOperateList;
		// * 无库存版、库存版不扣减库存，部分发货、待付款、待发货下不需要手动预占库存
		if (userStore?.userInfo?.version === stockVersion.无库存版
			|| (userStore?.userInfo?.version === stockVersion.库存版 && !userStore?.inventoryDeduct)
			|| pack.trades.every(trade => ![TradeStatus.卖家部分发货, TradeStatus.等待买家付款, TradeStatus.等待卖家发货].includes((trade.status as TradeStatus)))
		) {
			_handTradeOperateList = handTradeOperateList.filter(item => !['occupyTradeStock', 'freedTradeStock'].includes(item.action));
			_tradeOperateList = tradeOperateList.filter(item => !['occupyTradeStock', 'freedTradeStock'].includes(item.action));
		}
		// 合单不展示创建手工单
		if (pack.trades.length > 1) {
			_handTradeOperateList = _handTradeOperateList.filter(item => {
				return (!['createHandOrder', 'handEdit'].includes(item.action)
					&& !(item.action === 'deleteOrder' && (tradeStatus(pack).hadPrinted || tradeStatus(pack).hadSend))
				);
			});
			_tradeOperateList = _tradeOperateList.filter(item => item.action !== "createHandOrder");
		} else {
			_handTradeOperateList = _handTradeOperateList.filter(item => !(['deleteOrder', 'handEdit'].includes(item.action) && (tradeStatus(pack).hadPrinted || tradeStatus(pack).hadSend)));
		}

		// 如果是换商品、添加赠品的手工单，不能编辑手工单
		if (isSourceHand(pack) && pack.trades.some(trade => trade.orders.some(order => [SysExchangeTypeEnum.新增的商品, SysExchangeTypeEnum.新增的货品, SysExchangeTypeEnum.置换商品, SysExchangeTypeEnum.置换货品].includes(order?.sysExchangeType)))) {
			_handTradeOperateList = _handTradeOperateList.filter(item => !(['handEdit'].includes(item.action)));
		}

		// 含有波次的手工单不允许编辑和删除
		if (isSourceHand(pack) && Array.isArray(pack.waveNoList) && pack.waveNoList.length > 0) {
			_handTradeOperateList = _handTradeOperateList.filter(item => !(['handEdit', 'deleteOrder'].includes(item.action)));
		}

		// * 售后换货/补发订单不支持以下功能 (售后创建的手工单支持换商品、添加赠品)
		if (pack.source === AFTERSALE_HANDORDER) {
			_handTradeOperateList = _handTradeOperateList.filter(item => !(['handEdit', 'handCopy'].includes(item.action)));
		}

		/**
		 * 仅待付款、待发货才显示空单标记和取消空单标记按钮
		 */
		const isAllowMarkEmpty = pack.trades.every(trade => [TradeStatus.等待买家付款, TradeStatus.等待卖家发货].includes(trade.status as TradeStatus));
		if (isAllowMarkEmpty) {
			if ([IgnoreStatus.自动标记, IgnoreStatus.手动标记].includes(ignoreType)) {
				_handTradeOperateList = _handTradeOperateList.filter(item => !['markEmpty'].includes(item.action));
				_tradeOperateList = _tradeOperateList.filter(item => !['markEmpty'].includes(item.action));
			} else if ([IgnoreStatus.手动取消, IgnoreStatus.非空单].includes(ignoreType) || ignoreType === undefined) {
				_handTradeOperateList = _handTradeOperateList.filter(item => !['cancelMarkEmpty'].includes(item.action));
				_tradeOperateList = _tradeOperateList.filter(item => !['cancelMarkEmpty'].includes(item.action));
			}
		} else {
			_handTradeOperateList = _handTradeOperateList.filter(item => !['markEmpty', 'cancelMarkEmpty'].includes(item.action));
			_tradeOperateList = _tradeOperateList.filter(item => !['markEmpty', 'cancelMarkEmpty'].includes(item.action));
		}
		/**
		 * 仅异常订单、系统赠品异常显示处理异常
		 */
		if (!noGoodsLink && !pack?.serviceTagList.includes('sysGiftAbnormal')) {
			_tradeOperateList = _tradeOperateList.filter(item => !['dealAbnormal'].includes(item.action));
			_riskControlTradeOperateList = _riskControlTradeOperateList.filter(item => !['dealAbnormal'].includes(item.action));
			_pendingTradeOperateList = _pendingTradeOperateList.filter(item => !['dealAbnormal'].includes(item.action));
			_handTradeOperateList = _handTradeOperateList.filter(item => !['dealAbnormal'].includes(item.action));
		}

		// 分销订单按钮隐藏
		if (isSourceScm(pack)) {
			_handTradeOperateList = _handTradeOperateList.filter(item => !(['handEdit', 'deleteOrder', 'handCopy', 'createHandOrder'].includes(item.action)));
			_tradeOperateList = _tradeOperateList.filter(item => !(['changeGoods', 'addGiftGoods', 'createHandOrder'].includes(item.action)));
		}
		// 分销订单按钮隐藏
		if (pack?.serviceTagList?.includes('blackList')) {
			_handTradeOperateList = _handTradeOperateList.filter(item => !(['addBlackList'].includes(item.action)));
			_tradeOperateList = _tradeOperateList.filter(item => !(['addBlackList'].includes(item.action)));
		} else {
			_handTradeOperateList = _handTradeOperateList.filter(item => !(['removeBlackList'].includes(item.action)));
			_tradeOperateList = _tradeOperateList.filter(item => !(['removeBlackList'].includes(item.action)));
		}

		// 有波次的订单不显示拆分订单按钮
		if (Array.isArray(pack.waveNoList) && pack.waveNoList.length > 0) {
			_handTradeOperateList = _handTradeOperateList.filter(item => !['splitOrder', 'changeGoods', 'addGiftGoods', 'markEmpty', 'cancelMarkEmpty'].includes(item.action));
			_tradeOperateList = _tradeOperateList.filter(item => !['splitOrder', 'changeGoods', 'addGiftGoods', 'markEmpty', 'cancelMarkEmpty'].includes(item.action));
			_riskControlTradeOperateList = _riskControlTradeOperateList.filter(item => !['markEmpty', 'cancelMarkEmpty'].includes(item.action));
			_pendingTradeOperateList = _pendingTradeOperateList.filter(item => !['markEmpty', 'cancelMarkEmpty'].includes(item.action));
		}

		// 添加商品2.0 : 手工单也可以换商品、添加赠品
		const getEditGoodsBtnDisabled = (pack) => {
			const trades = pack.trades;
			if (pack.waybillPrintStatus === 'already') {
				return true;
			}
			if (pack.shipListPrintStatus === 'already') {
				return true;
			}
			// 拆单的订单不能编辑
			// if (pack?.isSplit) {
			// 	return true;
			// }

			if (pack?.serviceTagList?.includes('gfcf')) {
				return true;
			}

			if (!(trades.some(trade => trade.orders.some(order => [TradeStatus.等待卖家发货].includes(order.status))))) {
				return true;
			}
			if (trades.every(trade => trade.isPreShip)) {
				return true;
			}
			if (trades.every(trade => trade.isPrintFhd == 1)) {
				return true;
			}
			if (trades.every(trade => trade.isPrintKdd == 1)) {
				return true;
			}
			const hasLabel = trades.every(trade => trade.orders.every(order => order.labelstatus));
			if (hasLabel) {
				return true;
			}
			const hasPrint = trades.every(trade => trade.orders.every(order => order.labelPrintStatus));
			if (hasPrint) {
				return true;
			}
			return false;
		};
		const editBtnDisabled = getEditGoodsBtnDisabled(pack);
		if (editBtnDisabled) { // 隐藏编辑商品按钮
			_handTradeOperateList = _handTradeOperateList.filter(item => !['changeGoods', 'addGiftGoods'].includes(item.action));
			_tradeOperateList = _tradeOperateList.filter(item => !['changeGoods', 'addGiftGoods'].includes(item.action));
		}
		const actionMap = {
			splitOrder: (pack: IPackage) => {
				sendPoint(Pointer.单个拆单);
				sendPoint(Pointer.订单_订单打印_订单更多操作_拆分订单);
				SplitOrder({
					checkedList: [pack],
					isBatch: false
				});
			},
			dealAbnormal: (pack: IPackage) => {
				sendPoint(Pointer.订单_订单打印_订单操作_处理异常_异常_展现);
				sendPoint(Pointer.订单_订单打印_订单更多操作_处理异常);
				console.log(pack.trades, 'params3333params3333');
				if (pack.source == PLAT_SCMHAND) {
					let sellerId = "";
					let platform = "";
					let hasDiffSellerOrder = false;
					pack.trades?.forEach(trade => {
						trade.orders?.forEach(order => {
							if (!sellerId && !platform) {
								sellerId = order.itemSellerId || pack.sellerId;
								platform = order.itemPlatform || pack.platform;
								pack.itemSellerId = sellerId;
								pack.itemPlatform = platform;
							} else if (sellerId !== order.itemSellerId || platform !== order.itemPlatform) {
								hasDiffSellerOrder = true;
							}
						});
					});
					if (hasDiffSellerOrder) {
						message.warn('当前分销订单不允许处理异常，请前往代发商品管理处理');
						return;
					}
				} else if (pack.source == 'HAND') {
					let sellerId = "";
					let platform = "";
					let hasDiffSellerId = false;
					console.log(pack.trades, 'params3333params3333666');
					pack.trades?.forEach(trade => {
						trade.orders?.forEach(order => {
							if (!sellerId && !platform) {
								sellerId = order.itemSellerId || pack.sellerId;
								platform = order.itemPlatform || pack.platform;
								pack.itemSellerId = sellerId;
								pack.itemPlatform = platform;
							} else if ((sellerId !== order.itemSellerId && order.itemSellerId)
									  || (platform !== order.itemPlatform && order.itemPlatform)) {
								console.log(order.itemSellerId, 'params3333params333366688');
								hasDiffSellerId = true;
							}
						});
					});
					if (hasDiffSellerId)pack.hasDiffSellerId = true;
					console.log(pack, 'params3333params333366677');
				}

				runInAction(() => {
					tradeStore.setIsShowDealAbnormalModal(true);
					tradeStore.setDealAbnormalPackage(pack);
				});
			},
			markEmpty: (pack: IPackage) => {
				sendPoint(Pointer.订单_订单打印_订单操作_标记为空单);
				sendPoint(Pointer.订单_订单打印_订单更多操作_标记为空单);
				tradeStore.tradeOptStore.batchUpdateIgnore([pack], IgnoreStatus.手动标记);
			},
			cancelMarkEmpty: (pack: IPackage) => {
				sendPoint(Pointer.订单_订单打印_订单操作_取消空单标记);
				tradeStore.tradeOptStore.batchUpdateIgnore([pack], IgnoreStatus.手动取消);
			},
			pending: (pack: IPackage) => {
				sendPoint(Pointer.订单_订单打印_订单更多操作_挂起);
				tradeStore.tradeOptStore.handleTradePending([pack], PendingStatus.挂起);
			},
			// 换商品 （鼠标右键、更多）
			changeGoods: (pack: IPackage) => {
				const tip = getEditGoodsTip(pack);
				if (tip) {
					message.warn(tip + '换商品');
					return;
				}
				sendPoint(Pointer.订单_更多操作_编辑商品);
				sendPoint(Pointer.订单_订单打印_订单更多操作_换商品);

				if (pack?.trades?.length > 1) {
					// 直接走批量换商品,并直接填充数据
					runInAction(() => {
						setIsShowBatchChangeGoodsModal(true);
						setBatchChangeGoodsPackage([pack], true);
					});
				} else {
					runInAction(() => {
						setIsShowChangeGoodsModal(true);
						setChangeGoodsData({
							packInfo: pack,
							trades: [pack?.trades[0]]
						});
					});
				}

			},
			// 添加赠品
			addGiftGoods: (pack: IPackage) => {
				sendPoint(Pointer.订单_订单打印_订单更多操作_添加赠品);
				const tip = getEditGoodsTip(pack);
				if (tip) {
					message.warn(tip + '添加赠品');
					return;
				}

				// 批量添加赠品
				runInAction(() => {
					setIsShowBatchAddGoodsModal(true);
					setBatchAddGoodsPackage([pack], pack?.trades?.length > 1 ? {} : pack?.trades?.[0]);
				});
			},
			cancelPending: (pack: IPackage) => {
				tradeStore.tradeOptStore.handleTradePending([pack], PendingStatus.取消挂起);
			},
			sendTrade: (pack: IPackage) => {
				// sendDelivery(pack.togetherId);
				sendPoint(Pointer.订单_更多操作_发货);
				if (pack.platformStockOut) {
					message.warn('该订单进入平台缺货处理，不能进行该操作，请等待平台处理');
					return;
				}
				if (pack.pOrderUnfinished) {
					message.warn('该订单属于拼团订单未完成，不能进行该操作，请拼团完成后处理');
					return;
				}
				sendDelivery({
					togetherId: pack.togetherId
				});
			},
			printKDD: (pack: IPackage) => {
				sendPoint(Pointer.订单_更多操作_打印快递单);
				if (pack.platformStockOut) {
					message.warn('该订单进入平台缺货处理，不能进行该操作，请等待平台处理');
					return;
				}
				if (pack.pOrderUnfinished) {
					message.warn('该订单属于拼团订单未完成，不能进行该操作，请拼团完成后处理');
					return;
				}
				event.emit("printBatch.printAct", { printType: 'kdd', singleOrder: pack });
			},
			printFHD: (pack: IPackage) => {
				sendPoint(Pointer.订单_更多操作_打印发货单);
				if (pack.platformStockOut) {
					message.warn('该订单进入平台缺货处理，不能进行该操作，请等待平台处理');
					return;
				}
				if (pack.pOrderUnfinished) {
					message.warn('该订单属于拼团订单未完成，不能进行该操作，请拼团完成后处理');
					return;
				}
				event.emit("printBatch.printAct", { printType: 'fhd', singleOrder: pack });
			},
			inputSid: (pack: IPackage) => {
				sendPoint(Pointer.订单_更多操作_手写单号);
				let tip = '';
				if ([PLAT_DW].includes(pack.platform)) {
					tip = '得物订单不支持手写单号';
				}
				if (!pack.trades.some(trade => [TradeStatus.卖家部分发货, TradeStatus.等待卖家发货, TradeStatus.等待买家确认收货, TradeStatus.交易成功].includes(trade.status as TradeStatus))) {
					tip = '该订单状态暂时不支持手写单号';
				// } else if (pack.isMerge && pack.mergeTradeCount > 1) {
				// 	tip = '该订单属于合单订单，暂时不支持手写单号';
				} else if ((!isMergePrint && +selectedTemp?.Exid !== -901) || (isMergePrint && +selectedTempGroup?.id !== -901)) {
					tip = '快递模板请选手动导入单号';
				} else if (pack.pOrderUnfinished) {
					tip = '该订单属于拼团订单未完成，不能进行该操作，请拼团完成后处理';
				} else if (pack.platformStockOut) {
					tip = '该订单进入平台缺货处理，不能进行该操作，请等待平台处理';
				} else if (pack.productOrders?.every(order => !order.isChecked)) {
					tip = '请勾选订单下商品后进行手写单号';
				}
				if (tip) {
					message.warn(tip);
					return;
				}
				tradeStore.setEditSidModalProps(pack);
				tradeStore.setIsShowEditSidModal(true);
			},
			tradeOptLog: (pack: IPackage) => {
				sendPoint(Pointer.订单_更多操作_订单操作日志);
				tradeStore.setTradeOptLogPack(pack);
				tradeStore.setIsShowTradeOptLogModal(true);
			},
			handEdit: (pack: IPackage) => {
				sendPoint(Pointer.订单_更多操作_编辑);
				const tip = getHandEditTip(pack);
				if (tip) {
					message.warn(tip);
					return;
				}
				setIsShowHandOrderModal(true);
				setHandData(pack);
			},
			handCopy: (pack: IPackage) => {
				sendPoint(Pointer.订单_更多操作_复制);
				if (pack.source == PLAT_SCM) {
					message.warn('分销推送订单不支持复制');
					return;
				}
				const { togetherId, printContentOrigin, printContent, platform, sellerId } = pack;
				TradeHandTradeCopyApi({ tids: togetherId, shipInfo: (printContentOrigin || printContent), platformType: platform, sellerId }).then((res:any) => {
					if (res.success && res.data) {
						message.success('复制成功');
						addHandOrder(res.data);
						res.data.togetherId && tradeOperateLogAdd({
							operateType: TradeOptEnum.复制,
							operateContent: `复制创建新订单,原订单编号：${togetherId}, 新订单编号：${res.data.togetherId}`,
							platform: "hand",
							tids: res.data.togetherId
						});
					}
				}).catch((err) => {
					console.log("TradeHandTradeCopyApi err:", err);
				});
			},
			deleteOrder: (pack: IPackage) => {
				sendPoint(Pointer.订单_更多操作_删除订单);
				const tip = getDeleteOrderTip(pack);
				if (tip) {
					message.warn(tip);
					return;
				}
				Modal.confirm({
					centered: true,
					title: '确认',
					content: '确认删除该订单？',
					width: '400px',
					onOk: () => {
						const { platform, sellerId } = pack;
						TradeHandTradeDeleteApi({ tid: pack.togetherId, platform, sellerId, source: pack.source }).then((res:any) => {
							if (res.success) {
								message.success('删除成功');
								deleteTradeSplice(pack.togetherId);
							}
						}).catch((err) => {
							console.log("TradeHandTradeDeleteApi err:", err);
						});
					}
				});

			},
			occupyTradeStock: (pack: IPackage) => {
				sendPoint(Pointer.订单_订单打印_订单操作_占用订单库存);
				updateTradeOccupied(OccupyTradeOptType.占用, [pack]);
			},
			freedTradeStock: (pack: IPackage) => {
				sendPoint(Pointer.订单_订单打印_订单操作_释放订单库存);
				updateTradeOccupied(OccupyTradeOptType.释放, [pack]);
			},
			createHandOrder: (pack: IPackage) => {
				console.log('createHandOrder123123123');
				sendPoint(Pointer.订单_更多操作_创建手工单);
				setIsShowHandOrderModal(true);
				setHandData({
					isCreate: true,
					tradeEncodeType: 2,
					copyTid: pack.tids,
					isSplit: pack.isSplit,
					platform: pack.platform
				});
			},
			addBlackList: (pack: IPackage) => {
				sendPoint(Pointer.订单_更多操作_标记黑名单);
				sendPoint(Pointer.订单_订单打印_订单更多操作_标记为黑名单);
				TradeCustomerAdd({
					platform: pack.platform,
					tidList: pack.togetherId.split('|'),
					operateType: 1,
				}).then(res => {
					updateTradeList([pack]);
					message.success('已标记为黑名单');
				});
			},
			removeBlackList: (pack: IPackage) => {
				sendPoint(Pointer.订单_更多操作_取消黑名单标记);
				TradeCustomerAdd({
					platform: pack.platform,
					tidList: pack.togetherId.split('|'),
					operateType: 0,
				}).then(res => {
					updateTradeList([pack]);
					message.success('已取消标记黑名单');
				});
			}
		};
		// 更新订单列表，该方法可以公用。只需要传入勾选的packList就行，
		const updateTradeList = async(packList) => {
			const tradeInfos = [];
			packList.forEach(pack => {
				tradeInfos.push({
					tids: pack.togetherId.split('|'),
					platform: pack.platform,
					sellerId: pack.sellerId
				});
			});
			const { data: { list } } = await TradeTradeDetailGetApi({ tradeInfos });
			if (list?.length) {
				const { tradeListStore: { handleUpdateList } } = tradeStore;
				handleUpdateList({
					type: "batchUpdateTrade",
					data: list,
				});
			}
		};
		const tradeOperateLogAdd = ({ operateType, operateContent, platform, tids }:{operateType: number, operateContent: string, platform: string, tids: string}) => {
			TradeOperateLogAddApi([{
				operateType,
				operateContent,
				operateResult: 1,
				platform,
				tids
			}]);
		};
		const handleOpt = (act: string) => {
			actionMap[act](tradeStore.tradeListStore.getListByTogetherId(togetherId)[0]);
		};
		// eslint-disable-next-line react-hooks/exhaustive-deps
		const menu = (
			<Menu className={ `${s["operate-container"]} r-flex r-fd-c` }>
				{/* 手工单 更多 */}
				{(isSourceHand(pack) || pack.source === AFTERSALE_HANDORDER) ? (
					(pack.occupiedStockStatus === 1
						? _handTradeOperateList.filter(item => item.action !== 'occupyTradeStock')
						: _handTradeOperateList.filter(item => item.action !== 'freedTradeStock')
					).map(operate => {
						const MenuItem = (
							<Menu.Item key={ operate.action } className={ `${s["operate-item"]} operate-item r-pointer` } onClick={ () => { handleOpt(operate.action); } }>
								{operate.name}
							</Menu.Item>
						);
						// if (operate.name == '拆分订单' && !userStore.isShowSplitMenu) {
						// 	return null;
						// } else
						if (operate.functionPermission) {
							return <FunctionPermissionCheck functionPermission={ operate.functionPermission }>{MenuItem}</FunctionPermissionCheck>;
						} else {
							return MenuItem;
						}
					})
				) : (pack.isLimited) ? (
					// 针对拼多多 风控中订单 不能进行发货和打印快递单或发货单操作
					_riskControlTradeOperateList.map(operate => (
						<Menu.Item key={ operate.action } className={ `${s["operate-item"]} operate-item r-pointer` } onClick={ () => { handleOpt(operate.action); } }>{operate.name}</Menu.Item>
					))) : (
					(pack.occupiedStockStatus === 1
						? _tradeOperateList.filter(item => item.action !== 'occupyTradeStock')
						: _tradeOperateList.filter(item => item.action !== 'freedTradeStock')
					).map(operate => {
						const MenuItem = (
							<Menu.Item key={ operate.action } className={ `${s["operate-item"]} operate-item r-pointer` } onClick={ () => { handleOpt(operate.action); } }>{operate.name}</Menu.Item>
						);
						// if ((pack.platform === PLAT_DW) && ['换商品', '添加赠品'].includes(operate.name)) {
						// 	return null
						// 	// return <Menu.Item key={ operate.action } className={ `${s["operate-item"]} operate-item r-pointer` } disabled>{operate.name}</Menu.Item>;
						// }

						// if (operate.name == '拆分订单' && !userStore.isShowSplitMenu) {
						// 	return null;
						// } else
						if (operate.functionPermission) {
							return <FunctionPermissionCheck functionPermission={ operate.functionPermission }>{MenuItem}</FunctionPermissionCheck>;
						} else {
							return MenuItem;
						}
					})
				)}
			</Menu>
		);

		// eslint-disable-next-line react-hooks/exhaustive-deps
		const pendingMenu = (
			<Menu className={ `r-flex r-fd-c ${s["operate-container"]}` }>
				{_pendingTradeOperateList.map(operate => (
					<Menu.Item key={ operate.action } className={ `${s["operate-item"]} operate-item r-pointer` } onClick={ () => { handleOpt(operate.action); } }>{operate.name}</Menu.Item>
				))}
			</Menu>
		);

		useImperativeHandle(cRef, () => {
			return ({
				getMenu: () => (isPending ? pendingMenu : menu)
			});

		}, [isPending, pendingMenu, menu]);

		return (
			<div className="r-flex r-ai-c batch_tbtlt_detail r-as-s" onClick={ (e) => { e.stopPropagation(); } }>
				<Button
					type="link"
					className="toggleTogetherDetail toggleDetailCon clickable"
					onClick={ () => { toggleTogetherDetail(pack); } }
					style={ { paddingLeft: 0, fontSize: '12px' } }
				>
					{isExpand ? "收起" : "展开"}
				</Button>
				<Dropdown
					overlay={ isPending ? pendingMenu : menu }
					placement="bottomCenter"
					overlayClassName="operate-com-dropdown"
				>
					<Button type="link" style={ { fontSize: '12px', paddingLeft: 0, } }>更多</Button>
				</Dropdown>
			</div>
		);
	}
);

export default OperateCom;
