
export const enum FhdRuleEnum {
	'按订单商品展示顺序排序' = 1,
	'按商家编码+规格编码升序' = 2,
	'按规格编码升序' = 3,
	'按货品简称+货品规格编码升序' = 4,
	'按货品简称+货品规格别名升序' = 5,
	'按货品规格编码升序' = 6,
	'按规格名称升序' = 7,
	'按货品货位升序' = 8,
}
interface TableData {
	tb_sjbm_b: string; // 商家编码
	tb_sjbm_kh: string; // 规格编码
	tb_bdhp_jc: string; // 货品简称
	tb_hpsjbm: string; // 货品规格编码
	tb_hpggbm: string; // 货品规格别名
	tb_cpggAll: string; // 规格名称
	tb_warehouseSlotName: string; //货品货位
	[k: string]: any;
}
interface FhdRuleSortDataProps {
	tableDataArray: TableData[];
	rule?: FhdRuleEnum;
}

export const fhdRuleSortData = (props: FhdRuleSortDataProps) => {
	const {
		tableDataArray,
		rule = FhdRuleEnum.按订单商品展示顺序排序,
	} = props;

	if (!rule || rule == FhdRuleEnum.按订单商品展示顺序排序) {
		return;
	}
	// 避免rule传入类型不确定，统一转化成number
	const getSortNameVal = (item: TableData) => {
		switch (Number(rule)) {
			case FhdRuleEnum["按商家编码+规格编码升序"]:
				return `${item.tb_sjbm_b}${item.tb_sjbm_kh}`;

			case FhdRuleEnum.按规格编码升序:
				return `${item.tb_sjbm_kh}`;

			case FhdRuleEnum["按货品简称+货品规格别名升序"]:
				return `${item.tb_bdhp_jc}${item.tb_hpggbm}`;

			case FhdRuleEnum["按货品简称+货品规格编码升序"]:
				return `${item.tb_bdhp_jc}${item.tb_hpsjbm}`;

			case FhdRuleEnum.按货品规格编码升序:
				return `${item.tb_hpsjbm}`;

			case FhdRuleEnum.按规格名称升序:
				return `${item.tb_cpggAll}`;

			case FhdRuleEnum.按货品货位升序:
				return `${item.tb_warehouseSlotName}`;
			return `${item.tb_hpsjbm}`;

			default:
				return '';
		}
	};
	const sortFn = (a:TableData, b:TableData) => {
		let val = 0;
		if (getSortNameVal(a) > getSortNameVal(b)) val = 1;
		else if (getSortNameVal(a) < getSortNameVal(b)) val = -1;
		return val;

	};
	tableDataArray.sort(sortFn);

};
