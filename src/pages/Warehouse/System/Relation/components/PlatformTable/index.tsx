
import React, { useCallback, useEffect, useRef, useState, ReactNode, useMemo } from 'react';
import { Button, Checkbox, Image, Popover, Select, Modal, Space, Tooltip, Radio, Form, Dropdown, Menu, Table, Spin, Badge, Tag, Switch } from 'antd';
import _ from "lodash";
import { ExclamationCircleOutlined, WarningOutlined, SettingOutlined, QuestionCircleFilled, DownloadOutlined, DownOutlined, FileExcelOutlined, UpOutlined, CopyOutlined } from '@ant-design/icons';
import { useForm } from 'antd/es/form/Form';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import { ColumnsType } from 'antd/lib/table';
import cs from 'classnames';
import { useSetState } from 'ahooks';
import { observer } from 'mobx-react';
import { useLocation } from "react-router-dom";
import s from './index.module.scss';
import { GreenButton, BlueButton } from '@/components/Botton';
import SearchTable from '@/components/SearchTableVirtual';
import ProductListModal from "@/components-biz/Product/List/Model";
import { SearchTableRefProps } from '@/components/SearchTable/SearchTable';
import { FormItemConfig } from '@/components/SearchTable/FormWidthRow';
import BatchImportModal from "./components/BatchImportModal";
import InputMulti from '@/components/Input/InputMulti';
import ShopMultiSelect from '@/components-biz/ShopListSelect/shopMultiSelect';
import { getPlatformDetailLink } from '@/pages/AfterSale/TradeList/utils';
import { clearParams } from '@/utils/stringHelper';
import KdzsDateRangePicker1 from "@/components/DateRangeComp/kdzsRangePicker1";
import { ItemItemUserConfigQueryConfigApi } from '@/apis/trade/search';
import {
	ItemSysSkuGetSysSkusBySkuOuterIdApi,
	ItemAsyncGetProgressApi,
	updateItemByIdApi,
	updateSkuByIdApi,
	batchDeleteBySkuIdsApi,
	ItemSysItemDeleteItemRelationWithBatchApi,
	ItemSysItemSaveItemRelationApi,
	ItemSysItemChangeItemRelationApi,
	ItemSyncBatchImportItemsApi,
	ItemSysItemCreateCombinationBindingApi,
	SysItemGetSysItemListApi,
	ItemSkuUpdVarietySkuOuterIdAndQueryApi,
	ItemSysItemBindRelationSysSkuApi
} from '@/apis/warehouse/system';
import {
	ItemSkuUpdateSkuRequest,
	ItemSysItemListOfItemRelationPlatformItemViewRequest,
	ItemSysItemListOfItemRelationPlatformItemViewResponse,
	ItemSysItemDeleteItemRelationWithBatchRequest,
	ItemSysItemSaveItemRelationRequest,
	ItemSysItemCreateCombinationBindingRequest
} from '@/types/schemas/warehouse/system';
import WaresInfo from '@/components-biz/WaresInfo';
import SyncWaresModal from "@/pages/Warehouse/StockSync/HandStockSync/components/SyncWaresModal";
import { DEFAULT_IMG, PLAT_JD, PLAT_XHS, PLAT_YZ } from '@/constants';
import Input from '@/components/Input/InputSearch';
import Pointer from "@/utils/pointTrack/constants";
import sendPoint from "@/utils/pointTrack/sendPoint";
import PlatformIcon from '@/pages/Warehouse/StockSync/HandStockSync/components/PlatformIcon';
import ProgressModal from "@/components-biz/ProgressModal/index";
import QuickGenModal, { getLocalQuickSetting } from '@/components-biz/QuickGenModal';
import { getOuterId, getSkuOuterId, genIndexNo, SPECIAL_PLAT, genQuickAddMap } from '@/components-biz/QuickGenModal/utils';
import QuickClearModal, { syncText } from '@/components-biz/QuickClearModal';
import QuickUpdateModal from '@/components-biz/QuickUpdateModal';
import UserStore from "@/stores/user";
import SameSkuTable from '../../../Archives/components/ImportPlatItem/components/RelationTable/components/SameSku';
import message from '@/components/message';
import Icon from '@/components/Icon';
import { ExcelImportType } from '@/pages/Warehouse/constants';
import { getPlatAndShops, isSourceScm } from '@/components-biz/ShopListSelect/shopListUtils';
import { downloadCenter } from '@/pages/Index/DownloadCenter/utils';
import { ModulesFunctionEnum } from '@/types/schemas/setting/download';
import { tradeStore } from '@/stores';
import { belongStartAndEnd, compareRowId } from '@/pages/Warehouse/utils';
import useGetState from '@/utils/hooks/useGetState';
import { changeItemData } from '@/utils/util';
import FunctionPermissionCheck, { FunctionPermissionEnum } from '@/utils/permissionCheck/functionPermissionCheck';
import CombinedChoose from '../../../Archives/components/CombinedChoose';
import SysDataSetting from './components/SysDataSetting';
import SearchWithSkuOuterId from './components/SearchWithSkuOuterId';
import warehouseStore from '@/stores/warehouse';
import useExtand from '@/utils/hooks/useCacheExtand';
import { copyToPaste, splitFxgTid } from '@/utils';
import { FieldsPermissionEnum } from '@/utils/permissionCheck/fieldsPermissionCheck';
import SameMergeDrawer from './components/SameMergeDrawer';
import SameMergeSkusDrawer from './components/SameMergeDrawer/skus';
import SearchSameByAI from './components/SameMergeDrawer/SearchSameByAI';
import { PageNameControlEnum } from "@/components-biz/LowVersionControlModal/constant";
import LowVersionControlModal, { lowVersionLock } from "@/components-biz/LowVersionControlModal";
import { fetchWithPaginationOptimization } from './utils/paginationOptimization';
import { local } from "@/libs/db";
import AI_Same from "@/assets/image/AI_same.png";
import AI_Search_Same from "@/assets/image/AI_search_same.png";
import { getImageThumbnail } from '@/utils/img.scale';

const popGetGroupList = async(sysSkuId) => {
	const { list } = await SysItemGetSysItemListApi({
		sysSkuIdList: [sysSkuId],
		isCombination: 1
	});
	return list[0].sysSkuList[0].groupCombinationList;
};

interface confirmInfoProps {
	visible: boolean;
	params?: ItemSysItemSaveItemRelationRequest;
	[key: string]: any;
}

interface batchCopyInfoProps {
	visible: boolean;
	importGoodsNum: number;
	importGoods: any;
	[key: string]: any;
}
export interface AssistantTableProps {
	visible?: boolean,
	expandNode?: any
}
const { Option } = Select;
const { confirm } = Modal;
const MAX_SHOW_COUNT: number = 3;

/**
 * `HoverableCell` is a wrapper component that shows additional hoverable content (e.g., copy icon).
 * When hovered, a copy icon appears that allows copying text to the clipboard.
 *
 * @param {React.ReactNode} children - The content to render inside the cell.
 * @param {string} text - The text that will be copied when the copy icon is clicked.
 * @param {boolean} hover - 用于是否展示复制按钮
 * @param {React.HTMLAttributes<HTMLDivElement>} other - Additional props to pass to the outer div.
 */
interface HoverableCellProps extends React.HTMLAttributes<HTMLDivElement> {
	children: React.ReactNode;
	text: string;
	hover: boolean;
}

const HoverableCell: React.FC<HoverableCellProps> = ({ children, text, hover, ...other }) => {
	return (
		<div
			{ ...other }
		>
			{children}
			{text && (
				<CopyOutlined
					hidden={ !hover }
					onClick={ () => { copyToPaste(text); } }
					className={ cs('r-fc-black-65', 'r-pointer') }
					style={ { marginLeft: '6px' } }
				/>
			)}
		</div>
	);
};

const getSameSkuOuterIdMap = (dataSource: any[]): any => {
	const map = new Map();
	dataSource.forEach((d: any) => {
		const varietySkuOuterId = d.varietySkuOuterId;
		if (varietySkuOuterId) {
			map.set(varietySkuOuterId, (map.get(varietySkuOuterId) || 0) + 1);
		}
	});
	return map;
};

const getSameOuterIdMap = (dataSource: any[]): any => {
	const map = new Map();
	let _curNumIid = "";
	dataSource.forEach((d: any) => {
		const { varietyOuterId, numIid } = d;
		if (numIid && _curNumIid != numIid && varietyOuterId) {
			map.set(varietyOuterId, (map.get(varietyOuterId) || 0) + 1);
			_curNumIid = numIid;
		}
	});
	return map;
};

/**
 * 商家平台视角
 * @param props
 * @returns
 */
const AssistantTable: React.FC<AssistantTableProps> = (props: AssistantTableProps) => {
	const [form] = useForm();
	const tableRef = useRef<SearchTableRefProps>();
	const [curSearchParams, setCurSearchParams] = useState({});
	const [syncOuter, setSyncOuter] = useState(false);
	const [platfromCodeAsSysCode, setPlatfromCodeAsSysCode] = useState(false);
	const [syncWaresModalVisible, setSyncWaresModalVisible] = useState(false);
	const [sameMergeDrawerVisible, setSameMergeDrawerVisible] = useState(false);
	const [sameMergeSkusDrawerVisible, setSameMergeSkusDrawerVisible] = useState(false);
	const [isSameMergeHand, setIsSameMergeHand] = useState(false);
	const [dataSameMergeHand, setDataIsSameMergeHand] = useState([]);
	const [dataSource, setDataSource] = useState<any[]>([]);
	const [codeOverflow, setCodeOverflow] = useState([]);
	const [downloadExcelModalVisible, setDownloadExcelModalVisible] = useState(false); // 导出Excel
	const [exportType, setExportType] = useState(1); // 导出Excel类型
	const [importModalVisible, setImportModalVisible] = useState<boolean>(false);
	const [exportLoading, setExportLoading] = useState(false);
	const [oldSelectObj, setOldSelectObj] = useState({ index: "", checked: false });
	const [loading, setLoading] = useState(false);

	const [quickLoading, setQuickLoading] = useState(false);
	const [isHideDelete, setIsHideDelete, getIsHideDelete] = useGetState(false);
	const [onlyShowDeleteSku, setOnlyShowDeleteSku, getOnlyShowDeleteSku] = useGetState(false);
	const [skuOuterIdDifferentFlag, setSkuOuterIdDifferentFlag, getSkuOuterIdDifferentFlag] = useGetState(false);
	const [skuOuterIdRepeatFlag, setSkuOuterIdRepeatFlag, getSkuOuterIdRepeatFlag] = useGetState(false);
	const [tradeWaitSendFlag, setTradeWaitSendFlag, getTradeWaitSendFlag] = useGetState(null);
	const [barCodeRepeatFlag, setBarCodeRepeatFlag, getBarCodeRepeatFlag] = useGetState(false);
	const [relationSysItem, setRelationSysItem, getRelationSysItem] = useGetState(null);
	const [infoModalShow, setInfoModalShow] = useState(false); // 转化组合商品弹框
	const [createLoading, setCreateLoading] = useState(false);
	const [extands, { add, set, remove, clear }] = useExtand();
	const [hoverTr, sethoverTr] = useState(null);
	const [operateBtnsDefault, setOperateBtnsDefault] = useState(local.get("relationPlatfromTableOperateBtnsDefault") || {
		createCombinationType: "default",
	});


	const [tradeFindType, setTradeFindType] = useState(String(local.get("relationTradeFindType") ?? "1"));

	const { userCustomSettingConfig, getUserCustomSettingConfig, changeUserCustomSettingConfig, getSysDataSettingConfig, sysDataSettingConfig, changeSysDataSettingConfig } = warehouseStore;

	const [batchCopyInfo, setBatchCopyInfo] = useSetState<batchCopyInfoProps>({
		visible: false,
		importGoods: [],
		importGoodsNum: 0,
	});
	const [confirmInfo, setConfirmInfo, getConfirmInfo] = useGetState<confirmInfoProps>({
		visible: false,
		params: [],
		sysSkuOuterId: ''
	});
	const [sameSku, setSameSku] = useState({
		sameSkuVisible: false,
		sameSkuDataSource: [],
		resolve: () => { }
	});
	const [loopObj, setLoopObj] = useState({
		asyncCode: '',
		isStart: false,
	});
	const [quickAddInfo, setQuickAddInfo] = useState({
		visible: false,
	});
	const [quickUpdateInfo, setQuickUpdateInfo] = useState({
		visible: false,
		list: []
	});

	const [quickClearInfo, setQuickClearInfo] = useState({
		visible: false,
		type: null
	});
	const [isBlackUser, setIsBlackUser] = useState(false);
	const [checkVersionControl, setCheckVersionControl] = useState(false);
	const [excelImportType, setExcelImportType] = useState(ExcelImportType.导入更新平台商品);
	const [searchSameByAIVisible, setSearchSameByAIVisible] = useState(false);
	const [searchSameByAIItem, setSearchSameByAIItem] = useState({});
	const [mergeMode, setMergeMode] = useState("");
	const [searchSameByAIItems, setSearchSameByAIItems] = useState({});

	const onSearchSameByAI = async(item) => {
		sendPoint(Pointer.商品_货品与商品关系_平台商品视角_点击相似款);
		const res = await ItemItemUserConfigQueryConfigApi({ itemUserConfigBizEnum: "item_sku_2_vec_user_flag" });
		let flag = false;
		try {
			flag = JSON.parse(res.find(i => i.biz === "item_sku_2_vec_user_flag")?.value)?.flag;
		} catch (error) {
			console.log(error);
		}
		if (!flag) {
			message.error("功能未初始化，请联系客服处理");
			return;
		}
		const { relationSystemItem = {} } = item;
		const mergeItem = {
			title: item.title,
			numIid: item.numIid,
			skuName: item.skuName,
			skuId: item.skuId,
			sellerId: item.sellerId,
			sellerNick: item.sellerNick,
			platform: item.platform,
			outerId: item.outerId,
			skuOuterId: item.skuOuterId,
			stockNum: item.stockNum,
			price: item.price,
			picUrl: item.picUrl,
			itemPicUrl: item.itemPicUrl,
			opsType: relationSystemItem?.sysSkuId && !relationSystemItem.isCheck ? "BIND" : "CREATE",
			sysSkuInfo: relationSystemItem?.sysSkuId && !relationSystemItem.isCheck ? {
				sysSkuId: relationSystemItem.sysSkuId,
				sysItemId: relationSystemItem.sysItemId,
				sysItemAlias: relationSystemItem.sysItemAlias,
				sysSkuOuterId: relationSystemItem.skuOuterId,
				sysSkuName: relationSystemItem.sysSkuName,
				salableItemStock: relationSystemItem.salableItemStock,
				sysPrice: relationSystemItem.price,
				sysPicUrl: relationSystemItem.picUrl || item.picUrl || item.itemPicUrl,
				isCombination: relationSystemItem.isCombination,
			} : {},
		};
		setSearchSameByAIItem(mergeItem);
		setSearchSameByAIVisible(true);
	};

	const onSearchSameSkusByAI = async(item) => {
		console.log('onSearchSameSkusByAI', item);
		// sendPoint(Pointer.商品_货品与商品关系_平台商品视角_点击相似款);
		const res = await ItemItemUserConfigQueryConfigApi({ itemUserConfigBizEnum: "item_sku_2_vec_user_flag" });
		let flag = false;
		try {
			flag = JSON.parse(res.find(i => i.biz === "item_sku_2_vec_user_flag")?.value)?.flag;
		} catch (error) {
			console.log(error);
		}
		if (!flag) {
			message.error("功能未初始化，请联系客服处理");
			return;
		}
		setSearchSameByAIItems(item);
		setSameMergeSkusDrawerVisible(true);
	};

	const onAddConfirm = (items) => {
		setSearchSameByAIVisible(false);
		setDataIsSameMergeHand([{
			itemRecommendVoList: [searchSameByAIItem, ...items],
			groupKey: `${items[0]?.title}_${items[0]?.skuName}`
		}]);
		setMergeMode("searchSameMerge");
		setIsSameMergeHand(true);
		setSameMergeDrawerVisible(true);
	};

	const handleSetBtnsDefault = (fieldName, value) => {
		setOperateBtnsDefault(prev => {
			local.set('relationPlatfromTableOperateBtnsDefault', {
				...prev,
				[fieldName]: value
			});
			return {
				...prev,
				[fieldName]: value
			};
		});
	};

	const afterSyncWaresOK = useCallback(() => {
		tableRef.current.refresh();
	}, []);
	const afterSameMergeOK = useCallback(() => {
		tableRef.current.refresh();
	}, []);

	const location = useLocation();

	// 添加useEffect处理URL参数
	useEffect(() => {
		// 解析URL中的numIids参数
		const query = new URLSearchParams(location.search);
		const numIids = query.get('numIids');

		if (numIids) {
			// 设置表单值
			form.setFieldsValue({ numIids });

			// 延迟一下再触发查询，确保表单已经设置好值
			setTimeout(() => {
				tableRef.current?.search();
			}, 300);
		}
	}, [location.search]);

	const onChangeExportType = (e: any) => {
		setExportType(e.target.value);
		const localUserId = UserStore.userInfo.subUserId || UserStore.userInfo.userId;
		if (localUserId) {
			localStorage.setItem(`ExportType_${localUserId}`, e.target.value);
		}
	};
	// 功能控制 如果tj关闭对应功能 返回true
	 const getBlackUserList = async() => {
		// 高级版不去判断页面功能开关
		 // const { level } = await UserStore.getUserInfo();
		// if (level === VersionEnum.高级版) return false;
		 const res = await UserStore.getUserAuthorityBlacklist(); // 是否每次重新请求下
		setIsBlackUser(res);
	};
	useEffect(() => {
		getBlackUserList();
	}, []);
	const tradeFindTypeChange = (e) => {
		e.stopPropagation();
		e.preventDefault();
		local.set("relationTradeFindType", e.target.value);
		setTradeFindType(e.target.value);
	};
	const onMergeHand = (onlyCombination = false) => {
		if (!onlyCombination && selectedRows.length < 2) {
			message.warning('请先勾选至少两件商品！');
			return;
		}
		const mergeData = selectedRows.map(item => {
			const { relationSystemItem = {} } = item;
			return {
				title: item.title,
				numIid: item.numIid,
				skuName: item.skuName,
				skuId: item.skuId,
				sellerId: item.sellerId,
				sellerNick: item.sellerNick,
				platform: item.platform,
				outerId: item.outerId,
				skuOuterId: item.skuOuterId,
				stockNum: item.stockNum,
				price: item.price,
				picUrl: item.picUrl,
				itemPicUrl: item.itemPicUrl,
				opsType: relationSystemItem?.sysSkuId && !relationSystemItem.isCheck ? "BIND" : "CREATE",
				sysSkuInfo: relationSystemItem?.sysSkuId && !relationSystemItem.isCheck ? {
					sysSkuId: relationSystemItem.sysSkuId,
					sysItemId: relationSystemItem.sysItemId,
					sysItemAlias: relationSystemItem.sysItemAlias,
					sysSkuOuterId: relationSystemItem.skuOuterId,
					sysSkuName: relationSystemItem.sysSkuName,
					salableItemStock: relationSystemItem.salableItemStock,
					sysPrice: relationSystemItem.price,
					sysPicUrl: relationSystemItem.picUrl || item.picUrl || item.itemPicUrl,
					isCombination: relationSystemItem.isCombination,
				} : {},
			};
		});
		setMergeMode(onlyCombination ? "onlyCombination" : "");
		setDataIsSameMergeHand([{
			itemRecommendVoList: mergeData,
			groupKey: `${selectedRows[0]?.title}_${selectedRows[0]?.skuName}`
		}]);
		setIsSameMergeHand(true);
		setSameMergeDrawerVisible(true);
	};

	const onCloseDownloadExcelModal = () => {
		setDownloadExcelModalVisible(false);
	};
	useEffect(() => {
		getSysDataSettingConfig(true);
		getUserCustomSettingConfig(true);
	}, []);


	useEffect(() => {
		tradeStore.tradeListStore.keyDown();
		return () => {
			tradeStore.tradeListStore.cancelKeyDown();
		};
	}, []);

	useEffect(() => {
		const localUserId = UserStore.userInfo.subUserId || UserStore.userInfo.userId;
		if (localUserId) {
			if (downloadExcelModalVisible) {
				const num = localStorage.getItem(`ExportType_${localUserId}`);
				num && setExportType(Number(num));
			}
		}
	}, [downloadExcelModalVisible]);

	const onSyncOuterToggle = (val: boolean) => {
		setSyncOuter(val);
		const localUserId = UserStore.userInfo.subUserId || UserStore.userInfo.userId;
		if (localUserId) {
			localStorage.setItem(`SyncOuter_${localUserId}`, String(val));
		}
	};

	const onPlatfromCodeAsSysCodeToggle = (val: boolean) => {
		setPlatfromCodeAsSysCode(val);
		const localUserId = UserStore.userInfo.subUserId || UserStore.userInfo.userId;
		if (localUserId) {
			localStorage.setItem(`PlatfromCodeAsSysCode_${localUserId}`, String(val));
		}
	};

	useEffect(() => {
		const localUserId = UserStore.userInfo.subUserId || UserStore.userInfo.userId;
		if (localUserId) {
			if (batchCopyInfo.visible) {
				const trueStr = localStorage.getItem(`SyncOuter_${localUserId}`);
				trueStr && setSyncOuter(trueStr == 'true');

				const trueStr2 = localStorage.getItem(`PlatfromCodeAsSysCode_${localUserId}`);
				trueStr2 && setPlatfromCodeAsSysCode(trueStr2 == 'true');
			}
		}
	}, [batchCopyInfo.visible]);

	// 导出excel文档
	const onDownloadExcel = async() => {
		let params = { ...curSearchParams, exportType };
		if (exportType === 1) {
			// 导出已勾选
			if (selectedRows.length === 0) {
				message.warning('当前未勾选任何货品！');
				return;
			}
			const set = new Set();
			params['skuIds'] = selectedRows.map(i => {
				if (i.numIid) set.add(i.numIid);
				return i.skuId;
			}).filter(i => i);
			params['numIids'] = [...set];
		}
		// params['tradeWaitSendFlag'] = getTradeWaitSendFlag();
		setExportLoading(true);
		await downloadCenter({
			permission_source: FunctionPermissionEnum.导出平台商品,
			requestParams: params,
			fileName: '平台商品',
			module: UserStore.isShowZeroStockVersion ? ModulesFunctionEnum.货品与商品关系_0库存版 : ModulesFunctionEnum.货品与商品关系_库存版
		});
		setExportLoading(false);
		onCloseDownloadExcelModal();

	};
	// 当前条件刷新
	const systemRefresh = useCallback(() => {
		tableRef.current.refresh();
	}, []);

	const itemSkuOuterIdChange = async(value: any, row: ItemSysItemListOfItemRelationPlatformItemViewResponse["data"]["list"][0]) => {
		if (value == row.varietySkuOuterId) return;
		// !! 如果有绑定货品：则失焦的时候，用不到请求响应值，只需要回显输入的内容即可，先于请求回显速度更快
		const isBindSysSku = row.relationSystemItem?.sysSkuId;
		const isCheck = row.relationSystemItem?.isCheck;
		if (!isBindSysSku || isCheck) {
			setDataSource(dataSource.map(d => {
				if (row.rowId === d.rowId) {
					if (value == '') {
						d.relationSystemItem = {};
						d.hasSystemItem = false;
					}
				}
				return d;
			}));
		}
		const res = await ItemSkuUpdVarietySkuOuterIdAndQueryApi({ skuId: row.skuId, numIid: row.numIid, varietySkuOuterId: value });
		// !! 如果未绑定货品：则失焦的时候，用请求响应值，插入当前 row 作为推荐值
		if (!isBindSysSku || isCheck) {
			setDataSource(dataSource.map(d => {
				if (row.rowId === d.rowId) {
					if (value !== '') {
						d.relationSystemItem = res.length ? { ...res[0], isCheck: true } : {};
						d.hasSystemItem = !!res.length;
					} else {
						d.relationSystemItem = {};
						d.hasSystemItem = false;
					}
					d.varietySkuOuterId = value;
				}
				return d;
			}));
		}
		if (isBindSysSku && !isCheck) {
			setDataSource(dataSource.map(d => {
				if (row.rowId === d.rowId) {
					d.varietySkuOuterId = value;
				}
				return d;
			}));
		}
	};

	// 根据商家编码查询是否有对应货品sku
	const onSkuOuterIdChange = (e: any, row: any) => {
		const { target: { value } } = e;
		if (e.key == 'Enter') {
			e.preventDefault();
		}
		itemSkuOuterIdChange(value, row);
		sendPoint(Pointer.商品_货品与商品关系_平台商品视角_修改平台规格商家编码);
	};

	const onClearOuterIdOk = async() => {
		if (selectedRows.length == 0) {
			return message.warn('请先选择需要快速生成编码的商品');
		}
		setQuickLoading(true);
		try {
			let list = [...selectedRows];
			const setting = await getLocalQuickSetting();
			const tipsOuterIdSet = new Set();
			const nameArr = [];
			if (setting.isOuterIdChecked) {
				const updateOuterMap = new Map();
				nameArr.push('商家编码');
				let dix = -1;
				list.forEach((d: any) => {
					if (!d.isCollapse) {
						if (d.colSpan) dix++;
						const _varietyOuterId = getOuterId(setting, d, dix);
						if (_varietyOuterId) {
							d.varietyOuterId = _varietyOuterId;
							updateOuterMap.set(d.numIid, _varietyOuterId);
						} else if (!updateOuterMap?.[d.numIid]) {
							d.varietyOuterId = '';
							updateOuterMap.set(d.numIid, "");
						}
					}
				});
				if (updateOuterMap.size) {
					await updateItemByIdApi([...updateOuterMap.entries()].map(([numIid, varietyOuterId]) => ({ numIid, varietyOuterId })));
				}
			}
			if (setting.isSkuIdChecked) {
				const updateSkuParams: ItemSkuUpdateSkuRequest = [];
				nameArr.push('规格编码');
				list.forEach((d: any) => {
					if (!d.isCollapse) {
						if (setting.genSkuIdRule.includes('商家编码') && d.varietyOuterId == '') {
							tipsOuterIdSet.add(d.title);
						} else {
							d.varietySkuOuterId = getSkuOuterId(setting, d);
							updateSkuParams.push({ skuId: d.skuId, numIid: d.numIid, varietySkuOuterId: d.varietySkuOuterId });
						}
					}
				});
				if (tipsOuterIdSet.size) {
					Modal.warning({
						title: '提示',
						centered: true,
						okText: '确定',
						icon: <ExclamationCircleOutlined />,
						content: <div style={ { maxHeight: '300px', overflow: 'auto' } }>平台商品{[...tipsOuterIdSet].map(i => (`【${i}】`)).join('、')}商家编码为空，请编辑后再次填充</div>,
					});
				}
				if (updateSkuParams.length) {
					await updateSkuByIdApi(updateSkuParams);
				}
			}
			tableRef.current.refresh();
			if (!tipsOuterIdSet.size && nameArr.length) message.success(`快速生成${nameArr.join('、')}成功`);
		} catch (error) { console.log(error); }
		setQuickLoading(false);
		setQuickAddInfo({ visible: false });
	};

	const onQuickClear = ({ type, clearType }) => {
		let list;
		const resetFormData = {};
		const updateSkuParams: ItemSkuUpdateSkuRequest = [];
		const updateOuterMap = new Map();
		if (type == 'sku') {
			const map = clearType == syncText.清空重复 && getSameSkuOuterIdMap(dataSource);
			list = dataSource.map((d: any) => {
				if (d.isCollapse) return d;
				if ((clearType == syncText.清空勾选 && d.isChecked) || (clearType == syncText.全部清空 || map?.get?.(d.varietySkuOuterId) > 1)) {
					d.varietySkuOuterId = '';
					resetFormData[d.rowId] = '';
					updateSkuParams.push({ skuId: d.skuId, numIid: d.numIid, varietySkuOuterId: '' });
					if (!d.hasSystemItem || d.relationSystemItem.isCheck) {
						d.relationSystemItem = {};
					}
				}
				return d;
			});
		}
		if (type == 'outer') {
			const map = clearType == syncText.清空重复 && getSameOuterIdMap(dataSource);
			console.log("map::::", map);

			list = dataSource.map((d: any) => {
				if (d.isCollapse) return d;
				if ((clearType == syncText.清空勾选 && d.isChecked) || (clearType == syncText.全部清空 || map?.get?.(d.varietyOuterId) > 1)) {
					d.varietyOuterId = '';
					resetFormData[d.groupId] = '';
					updateOuterMap.set(d.numIid, '');
				}
				return d;
			});
		}
		updateSkuParams.length && updateSkuByIdApi(updateSkuParams);
		updateOuterMap.size && updateItemByIdApi([...updateOuterMap.keys()].map((i: any) => ({ numIid: i, varietyOuterId: '' })));
		setDataSource(list);
		form.setFieldsValue(resetFormData);
	};

	const [batchToggleStatus, setBatchToggleStatus] = useState(false);

	// 表单元素
	const FormFieldList: FormItemConfig[] = [
		{
			name: "",
			colProps: { span: 24 },
			children: (
				<div className="r-pb-16 r-mb-8 r-flex r-jc-sb" style={ { borderBottom: "1px solid #eee" } }>
					<div>
						<Radio.Group
							value={ getTradeWaitSendFlag() }
							// options={ [
							// 	{ label: '全部', value: null },
							// 	{ label: '有待发货订单', value: "tradeWaitSendFlag" },
							// 	{ label: '近3天有销量(不含今天)', value: "trade3DaySalesVolumeFlag" }
							// ] }
							optionType="button"
							onChange={ (e) => onChangeTradeWaitSendFlag(e) }
						>
							<Radio.Button value={ null }>全部</Radio.Button>
							<Radio.Button value="tradeWaitSendFlag">
								<Tooltip title="即根据最近24小时内的待发货订单进行查询，可能存在些许误差">有待发货订单</Tooltip>
								<Popover
									placement="bottom"
									content={ (
										<Radio.Group
											value={ tradeFindType }
											onChange={ (e) => tradeFindTypeChange(e) }
										>
											<Space direction="vertical">
												<Radio value="1">查询结果精确到规格</Radio>
												<Radio value="2">查询结果精确到商品</Radio>
											</Space>
										</Radio.Group>
									) }
								>
									<SettingOutlined className="r-c-999 r-ml-8" onClick={ (e) => e.preventDefault() } />
								</Popover>
							</Radio.Button>
							<Radio.Button value="trade3DaySalesVolumeFlag">近3天有销量(不含今天)</Radio.Button>
						</Radio.Group>
						<Radio.Group
							value={ getRelationSysItem() }
							className="r-ml-16"
							options={ [
								{ label: '全部', value: null },
								{ label: '未关联', value: false },
								{ label: '已关联', value: true }
							] }
							optionType="button"
							onChange={ (e) => relationSysItemChange(e) }
						>
							{/* <Radio value="">全部</Radio>
						<Radio value="">未关联</Radio>
						<Radio value="">已关联</Radio> */}
						</Radio.Group>
					</div>
					<div className="r-flex r-ai-c">
						<Popover
							content={
								<img width={ 500 } height={ 300 } src="https://kdzs-erp-item-web-frontend.oss-cn-zhangjiakou.aliyuncs.com/assets/gif.gif" alt="" className="r-block" />
							}
						>
							<img src={ AI_Search_Same } height={ 22 } alt="" />
							<Switch
								className="r-ml-5"
								checked={ sysDataSettingConfig?.itemAiSimilarityFlag === 1 }
								onChange={ (e) => {
									changeSysDataSettingConfig({
										...sysDataSettingConfig,
										itemAiSimilarityFlag: e ? 1 : 0
									});
								} }
								checkedChildren="开"
								unCheckedChildren="关"
							/>
						</Popover>
					</div>
				</div>
			)
		},
		// {
		// 	name: "relationSysItem",
		// 	colProps: { span: 12 },
		// 	children: (
		// 		<Radio.Group
		// 			options={ [
		// 				{ label: '全部', value: null },
		// 				{ label: '未关联', value: false },
		// 				{ label: '已关联', value: true }
		// 		  ] }
		// 			optionType="button"
		// 		>
		// 			{/* <Radio value="">全部</Radio>
		// 			<Radio value="">未关联</Radio>
		// 			<Radio value="">已关联</Radio> */}
		// 		</Radio.Group>
		// 	)
		// },
		{
			name: "platformInfo",
			// className: cs(s.condition2),
			children: (
				<ShopMultiSelect
					isSendPoint
					size="small"
					style={ { width: '159px' } }
				/>
			),
		},
		{
			name: 'numIids',
			children: <InputMulti maxInputNum={ 500 } placeholder="商品ID" numErrorMsg="单次查询最多筛选500个请重新输入" size="small" />
		},
		{
			name: "titleList",
			children: (
				<Tooltip title="支持输入多个关键词，使用空格隔开，空格表示并且">
					<Form.Item name="titleList" style={ { margin: 0 } }><Input placeholder="商品名称" /></Form.Item>
				</Tooltip>
			),
		},
		{
			name: 'skuNameList',
			children: (
				<Tooltip title="支持输入多个关键词，使用空格隔开，空格表示并且">
					<Form.Item name="skuNameList" style={ { margin: 0 } }><Input placeholder="规格" /></Form.Item>
				</Tooltip>
			),
		},
		{
			name: 'outerId',
			children: (
				<Tooltip title="默认模糊搜索，如需精确搜索请在字段开头加上@@">
					<Form.Item name="outerId" style={ { margin: 0 } }><Input placeholder="商家编码" /></Form.Item>
				</Tooltip>
			)
		},
		{
			name: 'skuOuterId',
			children: (
				<Tooltip title="默认模糊搜索，如需精确搜索请在字段开头加上@@">
					<Form.Item name="skuOuterId" style={ { margin: 0 } }><Input placeholder="规格编码" /></Form.Item>
				</Tooltip>
			)
		},
		{
			name: 'sysItemAlias',
			children: <Input placeholder="货品简称" />
		},
		{
			name: 'sysSkuName',
			children: <Input placeholder="货品规格名称" />
		},
		{
			name: 'sysOuterId',
			children: <Input placeholder="货品编码" />
		},
		{
			name: 'sysSkuOuterId',
			children: (
				<Tooltip title="默认精确搜索，如需模糊搜索请在字段开头或结尾加上*">
					<Form.Item name="sysSkuOuterId" style={ { margin: 0 } }><Input placeholder="货品规格编码" /></Form.Item>
				</Tooltip>
			)
		},
		{
			name: 'existItemNo',
			children: (
				<Select className={ cs('r-w-full') } placeholder="有无货号" size="small" style={ { width: 160 } } allowClear>
					<Option value={ null } key={ null }>全部</Option>
					<Option value={ 0 } key={ 0 }>无货号</Option>
					<Option value={ 1 } key={ 1 }>有货号</Option>
				</Select>)
		},
		{
			name: 'existOuterId',
			children: (
				<Select className={ cs('r-w-full') } placeholder="有无商家编码" size="small" style={ { width: 160 } } allowClear>
					<Option value={ null } key={ null }>全部</Option>
					<Option value={ 3 } key={ 3 }>有线上商家编码</Option>
					<Option value={ 2 } key={ 2 }>无线上商家编码</Option>
					<Option value={ 0 } key={ 0 }>无商家编码</Option>
					<Option value={ 1 } key={ 1 }>有商家编码</Option>
				</Select>)
		},
		{
			name: 'existSkuOuterId',
			children: (
				<Select className={ cs('r-w-full') } placeholder="有无规格编码" size="small" style={ { width: 160 } } allowClear>
					<Option value={ null } key={ null }>全部</Option>
					<Option value={ 3 } key={ 3 }>有线上规格编码</Option>
					<Option value={ 2 } key={ 2 }>无线上规格编码</Option>
					<Option value={ 1 } key={ 1 }>有规格编码</Option>
					<Option value={ 0 } key={ 0 }>无规格编码</Option>
				</Select>)
		},
		// {
		// 	name: 'relationSysItem',
		// 	children: (
		// 		<Select className={ cs('r-w-full') } placeholder="是否关联货品" size="small" style={ { width: 160 } } allowClear>
		// 			<Option value={ null } key={ null }>全部</Option>
		// 			<Option value key={ 0 }>已关联</Option>
		// 			<Option value={ false } key={ 1 }>未关联</Option>
		// 		</Select>)
		// },
		{
			name: 'approveStatus',
			children: (
				<Select className={ cs('r-w-full') } placeholder="销售状态" size="small" style={ { width: 160 } } allowClear>
					<Option value={ null } key={ null }>全部</Option>
					<Option value="onsale" key={ 0 }>出售中</Option>
					<Option value="instock" key={ 1 }>已下架</Option>
					<Option value="delete" key={ 2 }>已删除</Option>
				</Select>)
		},
		{
			name: 'itemSkuCreateTime',
			children: (
				<KdzsDateRangePicker1 placeholder={ ['上新时间起', '上新时间止'] } allowClear style={ { width: 160 } } />
			)
		}
	];

	const onSaveSellerCode = (e: any, row: any) => {
		if (e.key == 'Enter') {
			e.preventDefault();
		}
		let varietyOuterId = e.target.value;
		if (varietyOuterId == row.varietyOuterId) return;
		updateItemByIdApi([{
			numIid: row.numIid,
			varietyOuterId
		}]);
		setDataSource(prev => {
			prev.forEach(item => {
				if (item.groupId == row.groupId) {
					item.varietyOuterId = varietyOuterId;
				}
			});
			return [...prev];
		});
		if (varietyOuterId.length <= 128) {
			setCodeOverflow(codeOverflow.filter(i => i != row.groupId));
		}
		sendPoint(Pointer.商品_货品与商品关系_平台商品视角_修改平台商品商家编码);
	};

	const batchCheckOperate = () => {
		const arr = dataSource.filter(i => i.relationSystemItem?.isCheck);
		handleCopyConfirm(arr, 'BIND', true);
		// ItemBatchImportItems('', arr, 'BIND');
	};

	// 批量复制-点击确定-相同规格编码生成提示
	const batchCopyConfirm = () => {
		const importGoods = batchCopyInfo.importGoods;
		const sameSkuMap = {};
		importGoods.forEach((s: any) => {
			if (s.varietySkuOuterId) {
				if (!sameSkuMap[s.varietySkuOuterId]) {
					sameSkuMap[s.varietySkuOuterId] = [];
				}
				sameSkuMap[s.varietySkuOuterId].push(s);
			}
		});
		let sameSkuArr: ItemSysItemListOfItemRelationPlatformItemViewResponse["data"]["list"][0] = [];

		Object.keys(sameSkuMap).forEach(s => {
			if (sameSkuMap[s].length > 1) {
				sameSkuMap[s].forEach((sku: ItemSysItemListOfItemRelationPlatformItemViewResponse["data"]["list"][0], i: number) => {
					if (i === 0) {
						sku.rowSpan = sameSkuMap[s].length;
					}
					sameSkuArr.push({ ...sku, skuRelations: [sku] });
				});
			}
		});

		if (sameSkuArr.length) {
			setSameSku({
				sameSkuDataSource: sameSkuArr,
				sameSkuVisible: true,
				resolve: (mergeSkus) => {
					ItemBatchImportItems(FunctionPermissionEnum.复制批量复制, importGoods, 'CREATE', true, mergeSkus);
				}
			} as any);
		} else {
			ItemBatchImportItems('', importGoods, 'CREATE', true);
		}
		setBatchCopyInfo({ ...batchCopyInfo, visible: false });
	};

	const ItemBatchImportItems = async(code = '', rows: any, operateType: any, isUseModalCheck = false, mergeSkus?: any) => {
		if (rows.length == 0) return;
		const outerIdTipsFlag = warehouseStore?.sysDataSettingConfig?.outerIdTipsFlag;
		const controlSyncFlag = rows[0]?.controlSync;
		const params = {
			sellerId: rows[0].sellerId,
			platformType: rows[0].platform,
			isUploadOuterId: controlSyncFlag !== undefined ? controlSyncFlag : isUseModalCheck ? syncOuter : (outerIdTipsFlag == 1),
			isUpdateAlias: isUseModalCheck ? platfromCodeAsSysCode : false,
			bindRelations: [],
		};
		console.log("params:::", params);
		let overflowArr: string[] = [];
		const specialPlat = SPECIAL_PLAT.includes(params.platformType);
		rows.forEach((row: any) => {
			if (row?.varietyOuterId?.length > 128) {
				overflowArr.push(row.groupId);
			}
			const { relationSystemItem } = row;
			const isUpdateOuterId = row?.skuOuterId !== row?.varietySkuOuterId;
			if (mergeSkus && mergeSkus[row.varietySkuOuterId]) {
				const primarySku: any = mergeSkus[row.varietySkuOuterId]['primarySku'];
				const subSku: any[] = mergeSkus[row.varietySkuOuterId]['subSku'];
				if (primarySku.rowId === row.rowId) {
					const itemSKus = subSku.map(sub => {
						return {
							numIid: sub.numIid,
							skuId: sub.skuId,
							varietySkuOuterId: sub.varietySkuOuterId
						};
					});
					params.bindRelations.push({
						sellerId: row.sellerId,
						platformType: row.platform,
						sysItemAlias: isUseModalCheck && platfromCodeAsSysCode && row.varietyOuterId ? row.varietyOuterId : undefined,
						isUpdateOuterId,
						varietyOuterId: row.varietyOuterId,
						numIid: row.numIid,
						skuId: specialPlat && row.skuId == row.numIid ? '0' : row.skuId,
						operateType: 'CREATE',
						varietySkuOuterId: row.varietySkuOuterId || '',
						itemSKus
					});
				}
			} else {
				params.bindRelations.push(
					{
						sellerId: row.sellerId,
						platformType: row.platform,
						sysItemAlias: isUseModalCheck && platfromCodeAsSysCode && row.varietyOuterId ? row.varietyOuterId : undefined,
						isUpdateOuterId,
						numIid: row.numIid,
						skuId: specialPlat && row.skuId == row.numIid ? '0' : row.skuId,
						operateType,
						varietyOuterId: row.varietyOuterId,
						varietySkuOuterId: row.varietySkuOuterId || '',
						sysItemId: relationSystemItem.sysItemId,
						sysSkuId: relationSystemItem.sysSkuId,
						sysSkuOuterId: relationSystemItem.skuOuterId
					}
				);
			}
		});
		if (overflowArr.length && (syncOuter || platfromCodeAsSysCode)) {
			setCodeOverflow(overflowArr);
			message.warn('商品编码请限制在128字内');
			return;
		} else {
			setCodeOverflow([]);
		}
		setLoading(true);
		const res = await ItemSyncBatchImportItemsApi({
			...params,
			permission_source: code
		});
		setLoading(false);
		if (res.length > 0) {
			message.success("更新成功");
			setBatchCopyInfo({ visible: false });
			tableRef.current.refresh();
		}
	};

	const batchDeleteBySkuIds = (rows: any) => {
		console.log('rows22', rows);
		let skuIdList = rows.map((i: any) => ({
			numIid: i.numIid,
			sellerId: i.sellerId,
			skuId: SPECIAL_PLAT.includes(i.platform) && i.skuId == i.numIid ? '0' : i.skuId
		}));
		if (!skuIdList.length) return;
		confirm({
			centered: true,
			title: '提示',
			icon: <ExclamationCircleOutlined />,
			content: <>确定删除这{skuIdList.length}种平台商品吗？<br />删除仅对系统内生效，不删除平台商品，如需维护此商品关联关系可重新下载该商品</>,
			okText: '确认',
			cancelText: '取消',
			onOk: async() => {
				await batchDeleteBySkuIdsApi({ skuDTOList: skuIdList });
				// setDataSource(dataSource.filter(i => !skuIdList.includes(i.skuId)));
				message.success('删除成功');
				tableRef.current.refresh();
			}
		});
	};
	const dealShift = (record) => {
		const { rowId } = record;
		if (oldSelectObj.index) {
			const { index, checked } = oldSelectObj;
			const [startRowId, endRowId] = compareRowId(rowId, index);

			setDataSource(prev => {
				prev.forEach((item) => {
					if (belongStartAndEnd({ startRowId, endRowId, compareRowId: item.rowId }) && item.isItemShow) {
						item.isChecked = checked;
					}
				});
				return [...prev];
			});
		}
	};

	// group选择
	const onCheckedGroup = (e: CheckboxChangeEvent, record: any) => {
		setDataSource(prev => {
			prev.forEach(item => {
				if (item.groupId == record.groupId) {
					item.isChecked = e.target.checked;
				}
			});
			return [...prev];
		});
	};
	// 单选
	const onCheckedItem = (e: CheckboxChangeEvent, record: any) => {
		const { isShiftDown } = tradeStore.tradeListStore;
		if (!isShiftDown) {
			let checked = false;
			setDataSource(prev => {
				let prevIndex = prev.findIndex(d => d.rowId === record.rowId);
				if (prevIndex > -1) {
					prev[prevIndex].isChecked = e.target.checked;
					checked = e.target.checked;
				}
				return [...prev];
			});
			setOldSelectObj({
				index: record.rowId,
				checked
			});
		} else {
			dealShift(record);
		}
	};
	// 全选
	const onCheckAllChange = (e: CheckboxChangeEvent) => {
		setDataSource(prev => {
			prev.forEach(item => {
				item.isChecked = e.target.checked;
			});

			return [...prev];
		});
	};

	const checkedGroup = useMemo(() => {
		let filterList = dataSource.filter(item => !item.isCollapse);
		const map = {
			'group_all': { num: 0, total: filterList.length, checked: false }
		};
		filterList.forEach(item => {
			if (!map[item.groupId]) map[item.groupId] = { num: 0, total: item.colSpan, checked: false };
			if (item.isChecked) {
				map[item.groupId].num++;
				map['group_all'].num++;
				if (map['group_all'].num == map['group_all'].total) map['group_all'].checked = true;
				if (map[item.groupId].num == map[item.groupId].total) map[item.groupId].checked = true;
			}
		});

		return map;
	}, [dataSource]);

	const selectedRows = useMemo(() => {
		return dataSource.filter(item => !item.isCollapse && item.isChecked);
	}, [dataSource]);

	const isBatchCheck = useMemo(() => {
		return dataSource.some(i => {
			return i?.relationSystemItem?.isCheck;
		});
	}, [dataSource]);

	// 展开更多货品
	const toggleList = (row: any) => {
		dataSource.forEach((i, index) => {
			if (i.groupId === row.groupId) {
				extands.includes(row.groupId)
					? remove(row.groupId)
					: add(row.groupId);
				i.collapseShow = !i.collapseShow;
				if (i.collapseShow) {
					i.isItemShow = true;
				} else if (index < MAX_SHOW_COUNT) {
					i.isItemShow = true;
				} else {
					i.isItemShow = false;
				}
			}
		});
		setDataSource([...dataSource]);
	};

	const batchToggleList = () => {
		sendPoint(Pointer.商品_货品与商品关系_批量展开收起);
		dataSource.forEach(i => {
			i.collapseShow = !batchToggleStatus;
		});
		const extandKeys = new Set(
			dataSource
				.filter((i) => i?.colSpan > 2)
				.map((i) => String(i.groupId))
		);
		batchToggleStatus ? clear() : set(Array.from(extandKeys));
		setDataSource([...dataSource]);
		setBatchToggleStatus(!batchToggleStatus);
	};

	const onClickImg = (platform, detailId) => {
		const goodsDetailUrl = getPlatformDetailLink(platform, detailId);
		if (goodsDetailUrl) {
			window.open(goodsDetailUrl, '_blank');
		}
	};

	const handleSkuOuterIdBind = (item, row) => {
		console.log('handleSkuOuterIdBind');
		return new Promise((resolve, reject) => {
			const { relationSystemItem = {} } = row;
			const { sysSkuId, isCheck } = relationSystemItem;
			const selectRows = [{ sysSkuList: [item] }];
			setConfirmInfo({
				resolve,
				reject,
				type: sysSkuId && !isCheck ? `modify` : 'add',
				selectRows,
				row,
				sysSkuOuterId: item?.skuOuterId,
				visible: false
			});
			console.log("sysSkuId && !isCheck ? `modify` : 'add',:", sysSkuId && !isCheck ? `modify` : 'add',);
			setTimeout(() => {
				addBeforeConfim(row, selectRows, sysSkuId && !isCheck ? `modify` : 'add');
			}, 100);
		});

	};

	const [popTableDataSource, setPopTableDataSource] = useState([]);
	const [popLoading, setPopLoading] = useState(true);
	const handlePopChange = async(open, sysSkuId) => {
		if (open) {
			const group = await popGetGroupList(sysSkuId);
			setPopTableDataSource(group);
			setPopLoading(false);
		} else {
			setPopTableDataSource([]);
			setPopLoading(true);
		}
	};
	function escapeRegExp(string) {
		return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // $& 表示整个匹配的字符串
	}
	const keywordHighlight = (keyword, text) => {
		if (!keyword?.trim()) {
			return text;
		}
		let result = text;
		const keywordArr = escapeRegExp(keyword.split(" ").filter(Boolean).join("|"));
		result = result?.replace(new RegExp(`(${keywordArr})`, 'gi'), '<span style="color:#f00">$1</span>');
		return <span dangerouslySetInnerHTML={ { __html: result } } />;
	};

	const onSetChange = (e, value) => {
		const setting = { ...userCustomSettingConfig };
		if (setting?.customSetJson) {
			setting.customSetJson[value] = e.target.checked;
		}
		changeUserCustomSettingConfig(setting);
	};

	const platformSkuTitle = () => {
		const settingArr = [{
			label: '平台规格名称',
			value: 'showPlatformSkuName',
		}, {
			label: '平台规格ID',
			value: 'showPlatformSkuId',
		}, {
			label: '条形码',
			value: 'showPlatformBarCode',
		}, {
			label: '货号',
			value: 'showPlatformItemNo',
		}, {
			label: '平台规格售价',
			value: 'showPlatformSkuPrice',
		}];
		const content = settingArr.map(item => {
			return (
				<div className="r-pd-4">
					<Checkbox checked={ userCustomSettingConfig?.customSetJson?.[item.value] } onChange={ (e) => onSetChange(e, item.value) } >{item.label}</Checkbox>
				</div>
			);
		});
		const title = (
			<div className="r-flex">
				<div>平台规格</div>
				<Popover placement="bottom" title={ null } content={ content } trigger="click">
					<Icon className="r-ml-5 r-c-gray" type="guigeshezhi" size={ 16 } />
				</Popover>
			</div>
		);
		return title;
	};

	const systemSkuTitle = () => {
		const settingArr = [{
			label: '货品规格名称',
			value: 'showSysSkuName',
		}, {
			label: '条形码',
			value: 'showSysBarCode',
		}, {
			label: '货号',
			value: 'showSysItemNo',
		}, {
			label: '货品规格售价',
			value: 'showSysSkuPrice',
		}];
		const content = settingArr.map(item => {
			return (
				<div className="r-pd-4">
					<Checkbox checked={ userCustomSettingConfig?.customSetJson?.[item.value] } onChange={ (e) => onSetChange(e, item.value) } >{item.label}</Checkbox>
				</div>
			);
		});
		const title = (
			<div className="r-flex">
				<div>系统规格</div>
				<Popover placement="bottom" title={ null } content={ content } trigger="click">
					<Icon className="r-ml-5 r-c-gray" type="guigeshezhi" size={ 16 } />
				</Popover>
			</div>
		);
		return title;
	};

	// 表格列定义，与AntdTable使用一致
	const columns: ColumnsType<unknown> = [

		{
			title: <Checkbox checked={ checkedGroup.group_all.checked } indeterminate={ !checkedGroup.group_all.checked && !!checkedGroup.group_all.num } onChange={ onCheckAllChange } />,
			align: 'center',
			width: 32,
			fixed: "left",
			className: cs(s.noline),
			render: (text, row: ItemSysItemListOfItemRelationPlatformItemViewResponse["data"]["list"][0]) => {
				const { colSpan } = row;
				const checkNode = (
					<Checkbox
						onChange={ e => onCheckedGroup(e, row) }
						checked={ checkedGroup[row.groupId]?.checked }
						indeterminate={ !checkedGroup[row.groupId]?.checked && !!checkedGroup[row.groupId]?.num }
					/>
				);
				return colSpan && <>{checkNode}</>;
			},
		},
		{
			title: '序号',
			align: 'center',
			width: 42,
			className: cs(s.noline),
			fixed: "left",
			render: (text, row: ItemSysItemListOfItemRelationPlatformItemViewResponse["data"]["list"][0]) => {
				const { colSpan, groupIndex } = row;
				return <>{colSpan && groupIndex}</>;
			},
		},
		// {
		// 	title: '货品/店铺',
		// 	width: 120,
		// 	dataIndex: 'title',
		// 	render: (text, row: ItemSysItemListOfItemRelationPlatformItemViewResponse["data"]["list"][0]) => {
		// 		const { colSpan } = row;
		// 		return (
		// 			<>{colSpan && (
		// 				<div className="r-flex">
		// 					<PlatformIcon platform={ row?.platform } />{ row?.sellerNick }
		// 				</div>
		// 			)}
		// 			</>
		// 		);
		// 	},

		// },
		{
			title: <>平台商品<span className="r-pointer r-fc-1890FF r-ml-8" onClick={ () => setQuickClearInfo({ visible: true, type: 'outer' }) }>清空</span></>,
			width: 240,
			dataIndex: 'title',
			render: (text, row: ItemSysItemListOfItemRelationPlatformItemViewResponse["data"]["list"][0]) => {
				const { colSpan, platform, skuId, numIid, } = row;
				let id = numIid;
				if ([PLAT_XHS, PLAT_JD].includes(platform)) {
					id = skuId;
				}
				return (
					<>{colSpan && (
						<>
							<div className="r-flex">
								<PlatformIcon platform={ row?.platform } /> <div className="r-mb-4 colorfont"> {row?.sellerNick}</div>
							</div>
							<HoverableCell className="r-pointer r-fc-1890FF" text={ text } hover={ row?.isHovered }>
								<span onClick={ () => onClickImg(platform, id) }>{keywordHighlight(form.getFieldValue("titleList"), text)}</span>
							</HoverableCell>
							<HoverableCell className="r-mt-4 r-mb-4 colorfont" text={ row.numIid } hover={ row?.isHovered }>
								商品ID: {row.numIid}
							</HoverableCell>
							<div className="colorfont">线上商家编码: {row.outerId}</div>
							<Form.Item
								shouldUpdate
								name={ row.groupId }
								className={ cs('r-mb-0', 'r-mt-4') }
							>
								<Input
									className={ cs(`${codeOverflow.includes(row.groupId) ? s.outerInput : ''}`) }
									placeholder="商家编码"
									title={ row.varietyOuterId }
									style={ { width: '100%' } }
									onBlur={ (e) => onSaveSellerCode(e, row) }
									onPressEnter={ (e) => onSaveSellerCode(e, row) }
								/>
							</Form.Item>
							{
								sysDataSettingConfig?.itemAiSimilarityFlag === 1 && (
									<div className="r-pointer r-mt-4" onClick={ () => onSearchSameSkusByAI(row) }>
										<img src={ AI_Same } height={ 13 } alt="" />
									</div>
								)
							}
						</>
					)}
					</>
				);
			},
			onCell: (row: any, index: number) => {
				return {
					style: row.colSpan ? {} : { borderTop: 0 },
					className: row.isCollapse ? s['not-show-next-cell'] : '',
				};
			}
		},
		{
			title: '',
			align: 'center',
			width: 32,
			id: 'checkCol',
			className: cs(s.noline),
			render: (text, row: ItemSysItemListOfItemRelationPlatformItemViewResponse["data"]["list"][0]) => {
				const clickItem = (
					<>
						<div
							style={ {
								background: "#fff",
								textAlign: "start",
								margin: '-8px',
								padding: '0 8px',
								height: 'calc(100% + 14px)',
								width: 'calc(100% + 16px)',
								lineHeight: 'calc(100% + 16px)',
								transform: 'translateY(-2px)',
							} }
							onClick={ () => toggleList(row) }
							className={ s.collapseBtn }
							data-point={
								row?.collapseShow
									? Pointer[
										"商品_货品与商品关系_收起货品规格"
									]
									: Pointer[
										"商品_货品与商品关系_展开更多货品规格"
									]
							}
						>
							{row.collapseShow ? "收起" : "展开更多"}规格 {row.collapseShow ? <UpOutlined /> : <DownOutlined />}
							
						</div>
					</>
				);
				return (
					<>
						{!row?.isCollapse ? (
							<Checkbox
								onChange={ (e) => onCheckedItem(e, row) }
								checked={ row.isChecked }
							/>
						) : clickItem}
					</>
				);
			}
		},
		// {
		// 	title: '图片',
		// 	width: 64,
		// 	className: cs(s.noline),
		// 	render: (text, row: ItemSysItemListOfItemRelationPlatformItemViewResponse["data"]["list"][0]) => {
		// 		if (row.isCollapse) return null;
		// 		const { picUrl: img, platform, numIid, skuId } = row;
		// 		let id = numIid;
		// 		if ([PLAT_XHS, PLAT_JD].includes(platform)) {
		// 			id = skuId;
		// 		}
		// 		const popContent:ReactNode = (<Image width={ 200 } height={ 200 } src={ img || DEFAULT_IMG } fallback={ DEFAULT_IMG } preview={ false } />);
		// 		return (
		// 			<>
		// 				<div className={ s.imageWrap }>
		// 					<Popover placement="right" content={ popContent }>
		// 						<Image
		// 							width={ 48 }
		// 							height={ 48 }
		// 							src={ img || DEFAULT_IMG }
		// 							fallback={ DEFAULT_IMG }
		// 							preview={ false }
		// 							className={ cs(
		// 								"r-pointer",
		// 								s["image-container"]
		// 							) }
		// 							onClick={ () => onClickImg(platform, id) }
		// 						/>
		// 					</Popover>
		// 					{row.enableStatus == 0 ? (
		// 						<div className={ s.deleteGoodsLabel }>
		// 							平台已删
		// 						</div>
		// 					) : (
		// 						""
		// 					)}
		// 				</div>
		// 			</>
		// 		);
		// 	}
		// },
		{
			title: platformSkuTitle(),
			width: 236,
			className: cs(s.noline),
			render: (text, row: ItemSysItemListOfItemRelationPlatformItemViewResponse["data"]["list"][0]) => {
				if (row.isCollapse) return null;
				const { skuName, barCode, itemNo, skuId, numIid, platform, skuUuid, price } = row;
				const { picUrl: img, } = row;
				let id = numIid;
				if ([PLAT_XHS, PLAT_JD].includes(platform)) {
					id = skuId;
				}
				if ([PLAT_YZ].includes(platform)) {
					id = skuUuid;
				}
				const popContent: ReactNode = (<Image width={ 200 } height={ 200 } src={ img || DEFAULT_IMG } fallback={ DEFAULT_IMG } preview={ false } />);
				const { showPlatformBarCode, showPlatformItemNo, showPlatformSkuId, showPlatformSkuName, showPlatformSkuPrice } = userCustomSettingConfig?.customSetJson || {};
				return (
					<div className="r-flex" style={ { gap: '8px' } }>
						<div className={ s.imageWrap }>
							<Popover placement="right" content={ popContent }>
								<Image
									width={ 48 }
									height={ 48 }
									src={ getImageThumbnail({
										noScale: false,
										url: img || DEFAULT_IMG,
										width: 48,
										height: 48
									}) }
									fallback={ DEFAULT_IMG }
									preview={ false }
									className={ cs(
										"r-pointer",
										s["image-container"]
									) }
									onClick={ () => {
										if (!UserStore.hasFieldsPermission(FieldsPermissionEnum.供应商店铺信息) && isSourceScm(row)) {
											return;
										} else {
											onClickImg(platform, id);
										}
									} }
								/>
							</Popover>
							{
								sysDataSettingConfig?.itemAiSimilarityFlag === 1 && (
									<div className="r-pointer" onClick={ () => onSearchSameByAI(row) }>
										<img src={ AI_Same } height={ 13 } alt="" />
									</div>
								)
							}
							{row.enableStatus == 0 ? (
								<div className={ s.deleteGoodsLabel }>
									平台已删
								</div>
							) : (
								""
							)}
						</div>
						<div
							style={ { wordBreak: "break-all" } }
							className={ cs("r-fs-12", "r-fc-black-45") }
						>
							{
								showPlatformSkuName && <div className={ s["sysSkuName"] }>{keywordHighlight(form.getFieldValue("skuNameList"), skuName)}</div>
							}
							{
								showPlatformSkuId && (
									<div>
										规格ID：
										{SPECIAL_PLAT.includes(platform)
										&& skuId == "0"
											? numIid
											: skuId}
									</div>
								)
							}
							{showPlatformBarCode && <div>条码：{barCode}</div>}
							{showPlatformItemNo && <div>货号：{itemNo}</div>}
							{showPlatformSkuPrice && <div>售价：{price}</div>}
						</div>
					</div>
				);
			}
		},
		{
			title: (
				<div className="r-flex r-ai-c">
					<span className="r-bold r-wb-ka">规格编码</span>
					<span className="r-pointer r-fc-1890FF r-ml-8" onClick={ () => setQuickClearInfo({ visible: true, type: 'sku' }) }>清空</span>
				</div>
			),
			align: "center",
			width: 176,
			dataIndex: "skuOuterId",
			className: cs('table-right-border'),
			render: (text, row: ItemSysItemListOfItemRelationPlatformItemViewResponse["data"]["list"][0]) => {
				if (row.isCollapse) return null;
				console.log('rowrow', row);
				const { varietySkuOuterId, relationSystemItem = {} } = row;
				const SystemSkuOuterId = relationSystemItem.skuOuterId;
				const isSameCode = varietySkuOuterId && (varietySkuOuterId === SystemSkuOuterId);
				const samecount = varietySkuOuterId ? dataSource.filter(i => i.varietySkuOuterId == row.varietySkuOuterId)?.length : 0;
				return (
					<div className={ s['skuOuterId'] }>
						<div className="r-mb-4 r-flex">
							<Tooltip title="商家后台展示的规格编码">
								<div className="colorfont r-ta-l">线上规格编码: {row.skuOuterId}</div>
							</Tooltip>
						</div>
						<div className="r-flex">
							<div>
								<Form.Item
									shouldUpdate
									name={ row.rowId }
									className={ cs('r-mb-0') }
								>
									{
										warehouseStore?.sysDataSettingConfig?.skuOuterIdSuggestRuleFlag == 0 ? (
											<Input
												placeholder="规格编码"
												style={ { width: '100%' } }
												onBlur={ (e) => onSkuOuterIdChange(e, row) }
												title={ varietySkuOuterId }
											/>
										) : (
											<SearchWithSkuOuterId onSkuOuterIdChange={ (e) => onSkuOuterIdChange(e, row) } onSkuOuterIdBind={ (item) => { handleSkuOuterIdBind(item, row); } } skuOuterId={ row.varietySkuOuterId } />
										)
									}

								</Form.Item>
								{samecount > 1 && <div className={ cs('r-fs-12', 'r-c-999', 'r-ta-l') }>规格编码重复！</div>}
							</div>
							{
								!isSameCode && SystemSkuOuterId && (
									<Tooltip title="编码与关联货品规格编码不同">
										<WarningOutlined className={ s.warningTip } />
									</Tooltip>
								)
							}
						</div>
					</div>
				);
			},
			onCell: (row: any, index: number) => {
				return {
					style: row.isCollapse ? { borderRight: 0 } : {},
				};
			}
		},
		{
			title: '系统货品',
			width: 236,
			key: 'platformGoods',
			className: cs(s.noline),
			render: (text, row: ItemSysItemListOfItemRelationPlatformItemViewResponse["data"]["list"][0], index) => {
				if (row.isCollapse) return null;
				const { relationSystemItem = {} } = row;
				const { sysSkuId, picUrl, sysItemAlias, isCheck, isCombination } = relationSystemItem;
				let popColumns = [];
				if (isCombination === 1) {
					popColumns = [{
						title: '子货品简称',
						dataIndex: 'sysItemAlias',
						key: 'sysItemAlias',
						width: 200,
						render(text) {
							return <div className={ s['popRowText'] }>{text}</div>;
						}
					},
					{
						title: '子货品规格',
						dataIndex: 'sku',
						key: 'sku',
						width: 480,
						render(text, record) {
							const { picUrl, sysSkuName, skuOuterId } = record;
							return (
								<div className={ s['popContentTable'] }>
									<div className={ s.imageWrap }>
										<Image
											width={ 48 }
											height={ 48 }
											src={ picUrl || DEFAULT_IMG }
											preview={ false }
											className={ cs(
												"r-pointer",
												s["image-container"]
											) }
										/>
									</div>
									<div>
										<div className={ s['popRowDisplay'] }>
											<div >
												货品规格编码：{skuOuterId && skuOuterId}
											</div> <CopyOutlined
												onClick={ () => { copyToPaste(splitFxgTid(skuOuterId)); } }
												className={ cs('r-pointer') }
												style={ { marginLeft: '6px', color: "#68a4f8" } }
											/>
										</div>
										<div>规格别名：{sysSkuName && sysSkuName}</div>
									</div>
								</div>
							);
						}
					},
					{
						title: '组成比例',
						dataIndex: 'groupProportionNum',
						key: 'groupProportionNum',
						width: 80
					}];
				}
				return (
					<div className={ s["platformGoods"] }>
						{
							sysSkuId ? (
								<>
									<WaresInfo imgUrl={ picUrl } skuName={ sysItemAlias } isFontBlack row={ row } />
									<div className="r-mt-5">
										<Popover
											placement="topLeft"
											onVisibleChange={ (open) => handlePopChange(open, sysSkuId) }
											content={
												(
													(
														<div className={ s['popContent'] }>
															{
																popLoading ? <Spin />
																	: <Table bordered pagination={ false } columns={ popColumns } dataSource={ popTableDataSource } scroll={ { y: '480px', x: '600px' } } rowClassName={ s['popTableRow'] } className={ s['popTable'] } />
															}
														</div>
													)
												)
											}
										>
											{isCombination === 1 && <Tag color="blue">组合装</Tag>}
										</Popover>
									</div>
								</>
							) : <div style={ { color: '#F5821F' } }>没有对应的快递助手货品！</div>
						}
						<div className={ s['platformGoods-icon'] }>
							<Icon type="guanlian" size={ 24 } style={ { color: sysSkuId && !isCheck ? '#52C41A' : 'rgba(0,0,0,0.25)' } } />
						</div>
					</div>
				);
			},
		},
		{
			title: systemSkuTitle(),
			width: 136,
			className: cs(s.noline),
			render: (text, row: ItemSysItemListOfItemRelationPlatformItemViewResponse["data"]["list"][0]) => {
				if (row.isCollapse) return null;
				const { relationSystemItem = {} } = row;
				const { sysSkuName, barCode, itemNo, sysSkuId, price } = relationSystemItem;
				const { showSysBarCode, showSysItemNo, showSysSkuName, showSysSkuPrice } = userCustomSettingConfig?.customSetJson || {};
				return (
					<>
						{sysSkuId ? (
							<div
								style={ { wordBreak: "break-all" } }
								className={ cs("r-fs-12", "r-fc-black-65") }
							>
								{showSysSkuName && sysSkuName}
								{showSysBarCode && <div>条码：{barCode}</div>}
								{showSysItemNo && <div>货号：{itemNo}</div>}
								{showSysSkuPrice && <div>售价：{price}</div>}
							</div>
						) : (
							""
						)}
					</>
				);
			}
		},
		{
			title: '货品规格编码',
			align: "center",
			width: 136,
			dataIndex: "skuOuterId",
			render: (text, row: ItemSysItemListOfItemRelationPlatformItemViewResponse["data"]["list"][0]) => {
				if (row.isCollapse) return null;
				const { relationSystemItem = {} } = row;
				const { skuOuterId } = relationSystemItem;
				return (
					<>
						<div style={ { textAlign: 'left', cursor: 'pointer' } }>
							{skuOuterId}
						</div>
					</>
				);
			},
		},
		{
			title: (
				<div className="r-flex r-jc-c">
					操作
					<Tooltip title="展开/收起所有货品规格">
						<div>
							<Icon onClick={ batchToggleList } size={ 14 } style={ { color: '#FD8204' } } type={ batchToggleStatus ? 'shouqi' : 'zhankai' } />
						</div>
					</Tooltip>
					{isBatchCheck
						&& (
							<FunctionPermissionCheck functionPermission={ FunctionPermissionEnum.确认批量确认 }>
								<GreenButton disabled={ !UserStore.hasFunctionPermission(FunctionPermissionEnum.确认批量确认) } data-point={ Pointer.商品_货品与商品关系_平台商品视角_批量确认 } size="small" className="r-ml-5" type="primary" onClick={ batchCheckOperate }>批量确认</GreenButton>
							</FunctionPermissionCheck>
						)}
				</div>
			),
			width: 160,
			align: "center",
			id: 'control',
			render: (text, row: ItemSysItemListOfItemRelationPlatformItemViewResponse["data"]["list"][0]) => {
				if (row.isCollapse) return null;
				const { skuId: platformSkuId, numIid: platformNumIid, sellerId, platform, relationSystemItem = {}, title, skuName } = row;
				const { sysItemId, sysSkuId, isCheck } = relationSystemItem;
				const deleteParams: ItemSysItemDeleteItemRelationWithBatchRequest = {
					reqDeleteItemRelationDTOs: [{
						sysItemId,
						sysSkuId,
						platformSkuId,
						platformNumIid,
						platform,
						sellerId
					}]
				};
				const modalTitle = `(${title} : ${skuName})`;
				return (
					<>
						<Space>
							<FunctionPermissionCheck functionPermission={ sysSkuId ? FunctionPermissionEnum.商品更换 : FunctionPermissionEnum.商品添加 }>
								<Button disabled={ !UserStore.hasFunctionPermission(sysSkuId ? FunctionPermissionEnum.商品更换 : FunctionPermissionEnum.商品添加) } size="small" type="primary" data-point={ Pointer.商品_货品与商品关系_平台商品视角_添加 }>
									<ProductListModal
										type="radio"
										code={ sysSkuId ? FunctionPermissionEnum.商品更换 : FunctionPermissionEnum.商品添加 }
										isShowTitle
										title={ modalTitle }
										beforeOk={ (selectRows: any) => {
											return new Promise((resolve, reject) => {
												setConfirmInfo({
													resolve,
													reject,
													type: sysSkuId && !isCheck ? `modify` : 'add',
													selectRows,
													row,
													sysSkuOuterId: selectRows[0]?.sysSkuList[0]?.skuOuterId,
													visible: false
												});
												setTimeout(() => {
													addBeforeConfim(row, selectRows, sysSkuId && !isCheck ? `modify` : 'add');
												}, 500);
											});
										} }
									>
										{sysSkuId ? <Tooltip title="更换绑定本地货品">更换</Tooltip> : <Tooltip title="指定本地货品绑定平台商品">添加</Tooltip>}
									</ProductListModal>
								</Button>
							</FunctionPermissionCheck>
							{
								sysSkuId && !isCheck && (
									<FunctionPermissionCheck functionPermission={ FunctionPermissionEnum.解除批量解除 }>
										<Tooltip placement="topRight" title="解除当前已绑定的本地货品">
											<Button
												disabled={ !UserStore.hasFunctionPermission(FunctionPermissionEnum.解除批量解除) }
												data-point={ Pointer.商品_货品与商品关系_平台商品视角_解除 }
												danger
												onClick={ () => { removePlateFormItem(deleteParams, '确定解除该平台商品与快递助手货品的关联关系吗？', row); } }
												size="small"
												type="primary"
											>
												解除
											</Button>
										</Tooltip>
									</FunctionPermissionCheck>

								)
							}


							{
								!sysSkuId && (
									<FunctionPermissionCheck functionPermission={ FunctionPermissionEnum.复制批量复制 }>
										<Tooltip placement="topRight" title="复制平台商品到本地ERP内">
											<BlueButton
												disabled={ !UserStore.hasFunctionPermission(FunctionPermissionEnum.复制批量复制) }
												onClick={
													() => handleCopyConfirm(row, 'CREATE')
												}
												size="small"
												type="primary"
												data-point={ Pointer.商品_货品与商品关系_平台商品视角_单个复制 }
											>
												复制
											</BlueButton>
										</Tooltip>
									</FunctionPermissionCheck>
								)
							}

							{
								isCheck && (
									<FunctionPermissionCheck functionPermission={ FunctionPermissionEnum.确认批量确认 }>
										<Tooltip placement="topRight" title="确认该商品绑定该本地货品">
											<GreenButton
												disabled={ !UserStore.hasFunctionPermission(FunctionPermissionEnum.确认批量确认) }
												onClick={
													() => handleCopyConfirm(row, 'BIND')
												}
												size="small"
												type="primary"
												data-point={ Pointer.商品_货品与商品关系_平台商品视角_单个确认 }
											>
												确认
											</GreenButton>
										</Tooltip>
									</FunctionPermissionCheck>
								)
							}


						</Space>
						{
							(!sysSkuId || isCheck)
							&& (
								<FunctionPermissionCheck functionPermission={ FunctionPermissionEnum.删除批量删除 }>
									<Button size="small" type="link" disabled={ !UserStore.hasFunctionPermission(FunctionPermissionEnum.删除批量删除) } data-point={ Pointer.商品_货品与商品关系_平台商品视角_单个删除 } className={ s.deletePlatGoods } onClick={ () => batchDeleteBySkuIds([row]) }>删除平台商品</Button>
								</FunctionPermissionCheck>
							)
						}
						{/* <Button size="small" type="link" disabled={ !UserStore.hasFunctionPermission(FunctionPermissionEnum.删除批量删除) } data-point={ Pointer.商品_货品与商品关系_平台商品视角_单个删除 } className={ s.deletePlatGoods } onClick={ () => batchDeleteBySkuIds([row]) }>创建组合货品并绑定</Button> */}
					</>
				);
			}
		}
	];

	const handleCopyConfirm = (row, control, batch = false) => {
		const { outerIdTipsFlag } = warehouseStore?.sysDataSettingConfig || {};

		// 通用批量操作处理函数
		const handleBatchOperation = (operationType) => {
			ItemBatchImportItems(operationType, batch ? row : [row], control);
		};

		// 防抖处理，防止重复点击
		const debouncedOperation = _.debounce(handleBatchOperation, 500, {
			leading: true,
			trailing: false
		});

		let operationType = '';
		if (!batch) {
			operationType = control === 'BIND'
				? FunctionPermissionEnum.确认批量确认
				: FunctionPermissionEnum.复制批量复制;
		}
		let sysSkuOuterId = '';
		let showConfirm = null;
		if (batch) {
			showConfirm = row.some(i => {
				let Id = control === 'BIND'
					? i?.relationSystemItem?.skuOuterId
					: i?.varietySkuOuterId;
				return Id && i?.skuOuterId !== Id;
			});

		} else {
			sysSkuOuterId = control === 'BIND'
				? row?.relationSystemItem?.skuOuterId
				: row?.varietySkuOuterId;

			showConfirm = control === 'BIND' ? row?.skuOuterId !== sysSkuOuterId && sysSkuOuterId : true;
		}
		if (outerIdTipsFlag === 2 && showConfirm) {
			// 如果 outerIdTipsFlag 为 2，则直接显示确认信息
			setConfirmInfo({ visible: true, row, control, reject: () => { }, operationType, batch, sysSkuOuterId });
		} else {
			debouncedOperation(operationType);
		}
	};

	// 接口查询、查询参数重装
	const fetchSystemList = async(info: ItemSysItemListOfItemRelationPlatformItemViewRequest) => {
		console.log('info:::', info);
		const filterInfo: ItemSysItemListOfItemRelationPlatformItemViewRequest = {};
		FormFieldList.forEach((i: { name: string }) => {
			filterInfo[i.name] = info[i.name];
		});
		const { platformInfo = {}, ...infoRest } = filterInfo;
		const { shopId, platform } = await getPlatAndShops(platformInfo, true);
		// 处理日期范围参数
		if (infoRest.itemSkuCreateTime && infoRest.itemSkuCreateTime.length) {
			infoRest.itemSkuCreateStartTime = infoRest.itemSkuCreateTime[0]?.format('YYYY-MM-DD HH:mm:ss');
			infoRest.itemSkuCreateEndTime = infoRest.itemSkuCreateTime[1]?.format('YYYY-MM-DD HH:mm:ss');
			delete infoRest.itemSkuCreateTime;
		  }
		const _search: ItemSysItemListOfItemRelationPlatformItemViewRequest = {
			pageNo: info.pageNo,
			pageSize: info.pageSize,
			platformList: platform,
			sellerIdList: shopId,
			...infoRest,
			itemSkuCreateStartTime: infoRest.itemSkuCreateStartTime,
			itemSkuCreateEndTime: infoRest.itemSkuCreateEndTime,
			numIids: infoRest.numIids?.trim() ? infoRest.numIids?.split(",") : "",
			titleList: infoRest.titleList?.trim() ? infoRest.titleList?.split(" ") : "",
			skuNameList: infoRest.skuNameList?.trim() ? infoRest.skuNameList?.split(" ") : "",
			tradeWaitSendFlag: getTradeWaitSendFlag() === "tradeWaitSendFlag",
			trade3DaySalesVolumeFlag: getTradeWaitSendFlag() === "trade3DaySalesVolumeFlag",
			skuOuterIdDifferentFlag: getSkuOuterIdDifferentFlag(),
			skuOuterIdRepeatFlag: getSkuOuterIdRepeatFlag(),
			barCodeRepeatFlag: getBarCodeRepeatFlag(),
			relationSysItem: getRelationSysItem(),
			hideDeleteSku: getIsHideDelete(),
			onlyShowDeleteSku: getOnlyShowDeleteSku(),
			tradeFindType,
		};
		const search = clearParams(_search, true);
		setCurSearchParams(search);

		// 使用分页优化工具函数
		return fetchWithPaginationOptimization(search, info.pageSize);
	};

	// 基于平台商品拆分维度
	const responseAdapter = (data: ItemSysItemListOfItemRelationPlatformItemViewResponse["data"]) => {
		// data = mock.data
		genQuickAddMap(data.list);
		const formData = {};
		const tableList: any = [];
		let dataSourceIndex = 0;
		// data.list.forEach((item, i: any) => {
		// 	item.indexNo = genIndexNo(data, i);
		// 	const groupId = `groupId_${item.numIid}__${i}`;
		// 	if (item.varietyOuterId == undefined) item.varietyOuterId = '';
		// 	formData[groupId] = item.varietyOuterId;
		// 	let itemLen = item.platformItemSkuList.length;
		// 	if (getIsHideDelete()) {
		// 		itemLen = item.platformItemSkuList.filter((item) => (item.enableStatus !== 0))?.length;
		// 	}
		// 	if (getOnlyShowDeleteSku()) {
		// 		itemLen = item.platformItemSkuList.filter((item) => (item.enableStatus == 0))?.length;
		// 	}
		// 	let newItemLen = itemLen > MAX_SHOW_COUNT ? itemLen + 1 : itemLen;
		// 	item.platformItemSkuList.forEach((platformItem: any, index: number) => {
		// 		if (!((getIsHideDelete() && platformItem.enableStatus == 0) || (getOnlyShowDeleteSku() && platformItem.enableStatus !== 0))) {
		// 			if (platformItem.varietySkuOuterId == undefined) platformItem.varietySkuOuterId = '';
		// 			if (platformItem.itemNo == undefined) platformItem.itemNo = '';
		// 			if (platformItem.color == undefined) platformItem.color = '';
		// 			if (platformItem.size == undefined) platformItem.size = '';
		// 			const obj = {
		// 				rowId: `${i}_${index}_${itemLen}`,
		// 				groupIndex: i + 1,
		// 				collapseIndex: index,
		// 				groupId,
		// 				...(index === 0 ? { colSpan: itemLen } : {}),
		// 				...item,
		// 				...platformItem,
		// 				relationSystemItem: platformItem.relationSystemItemList[0] || {},
		// 				hasSystemItem: Boolean(platformItem.relationSystemItemList.length),
		// 				// collapseShow: dataSource[dataSourceIndex]?.collapseShow,
		// 				collapseShow: false,
		// 				isItemShow: index < MAX_SHOW_COUNT,
		// 			};
		// 			formData[obj.rowId] = obj.varietySkuOuterId;
		// 			// 展开规格的点击判断
		// 			tableList.push(obj);
		// 			dataSourceIndex++;
		// 		}
		// 	});
		// 	const tmpItem = tableList[tableList.length - 1];
		// 	if (itemLen > MAX_SHOW_COUNT && tmpItem) {
		// 		tableList.push({ isCollapse: true, rowId: `${i}_${itemLen}_${newItemLen}`, groupId: tmpItem.groupId, collapseShow: tmpItem.collapseShow, });
		// 	}
		// });
		data.list.forEach((item, i: any) => {
			item.indexNo = genIndexNo(data, i);
			const groupId = `groupId_${item.numIid}__${i}`;
			if (item.varietyOuterId == undefined) item.varietyOuterId = '';
			formData[groupId] = item.varietyOuterId;
		
			// 先过滤规格列表
			const filteredSkuList = item.platformItemSkuList.filter((platformItem: any) => {
				return !((getIsHideDelete() && platformItem.enableStatus == 0) || (getOnlyShowDeleteSku() && platformItem.enableStatus !== 0));
			});
		
			let itemLen = filteredSkuList.length;
			let newItemLen = itemLen > MAX_SHOW_COUNT ? itemLen + 1 : itemLen;
		
			// 使用过滤后的索引遍历和构建 obj
			filteredSkuList.forEach((platformItem: any, filteredIndex: number) => {
				if (platformItem.varietySkuOuterId == undefined) platformItem.varietySkuOuterId = '';
				if (platformItem.itemNo == undefined) platformItem.itemNo = '';
				if (platformItem.color == undefined) platformItem.color = '';
				if (platformItem.size == undefined) platformItem.size = '';
				const obj = {
					rowId: `${i}_${filteredIndex}_${itemLen}`,
					groupIndex: i + 1,
					collapseIndex: filteredIndex, // 使用过滤后的索引
					groupId,
					...(filteredIndex === 0 ? { colSpan: itemLen } : {}),
					...item,
					...platformItem,
					relationSystemItem: platformItem.relationSystemItemList[0] || {},
					hasSystemItem: Boolean(platformItem.relationSystemItemList.length),
					collapseShow: false,
					isItemShow: filteredIndex < MAX_SHOW_COUNT, // 基于过滤后索引设置默认显示
				};
				formData[obj.rowId] = obj.varietySkuOuterId;
				tableList.push(obj);
			});
			const tmpItem = tableList[tableList.length - 1];
		
			if (itemLen > MAX_SHOW_COUNT && tmpItem) {
				tableList.push({ isCollapse: true, rowId: `${i}_${itemLen}_${newItemLen}`, groupId: tmpItem.groupId, collapseShow: tmpItem.collapseShow, });
			}
		});
		if (extands?.length) {
			tableList.forEach((i) => {
				if (extands.includes(String(i.groupId))) {
					i.collapseShow = true;
					if (i?.hasOwnProperty('isItemShow')) i.isItemShow = true;
				}
			});
		}
		setDataSource(tableList);
		setBatchToggleStatus(false);
		setTimeout(() => {
			form.setFieldsValue(formData);
		}, 0);
		return {
			list: tableList,
			total: data.total
		};
	};

	const tableRowDataSource = useMemo(() => {
		return dataSource.filter(i => (i.isCollapse || i.collapseIndex < MAX_SHOW_COUNT || i.collapseShow));
	}, [dataSource]);

	const batchCopy = () => {
		let waitCheckNum = 0;
		let arr = selectedRows.filter(i => {
			if (i.relationSystemItem?.isCheck) waitCheckNum++;
			return !i.isCollapse && !i.relationSystemItem.sysSkuId;
		});
		if (waitCheckNum > 0) {
			message.warn('请先批量确认绑定关系');
			return true;
		}
		if (arr.length == 0) {
			message.warn('所选商品已建立绑定关系');
			return true;
		}
		setBatchCopyInfo({ visible: true, importGoodsNum: arr.length, importGoods: arr });
	};

	const batchDelete = () => {
		let arr = selectedRows.filter(i => !i.isCollapse && (!i.relationSystemItem.sysSkuId || i.relationSystemItem.isCheck));
		if (!arr.length) {
			message.warn('仅支持未匹配或匹配待确认的平台商品');
			return;
		}
		batchDeleteBySkuIds(arr);
	};


	const batchRemove = () => {
		console.log('selectedRows', selectedRows);
		const params: ItemSysItemDeleteItemRelationWithBatchRequest = {
			reqDeleteItemRelationDTOs: []
		};
		selectedRows.forEach((row, index: number) => {
			const { skuId: platformSkuId, numIid: platformNumIid, sellerId, platform, relationSystemItem = {} } = row;
			const { sysItemId, sysSkuId, isCheck } = relationSystemItem;
			const obj: ItemSysItemDeleteItemRelationWithBatchRequest['reqDeleteItemRelationDTOs'][0] = {
				sysItemId, //	货品id
				sysSkuId, //	货品skuId
				platformSkuId, //	平台skuId
				platformNumIid, //	平台商品Id
				platform, //	平台
				sellerId//	店铺id
			};
			if (sysItemId && !isCheck) params.reqDeleteItemRelationDTOs.push(obj);
		});
		let removeNum = params.reqDeleteItemRelationDTOs.length;
		if (removeNum == 0) {
			message.warn('没有可解除的平台商品与快递助手货品');
			return;
		}
		removePlateFormItem(params, `确定要将这${removeNum}种平台商品与快递助手货品解除关联关系吗？`);
	};

	const removePlateFormItem = (params: ItemSysItemDeleteItemRelationWithBatchRequest = {}, content: string, row = null) => {
		// 单个、批量、全部
		confirm({
			centered: true,
			title: '解除提示',
			icon: <ExclamationCircleOutlined />,
			content,
			okText: '确认解除',
			cancelText: '取消',
			onOk: async() => {
				await ItemSysItemDeleteItemRelationWithBatchApi(params);
				if (row) {
					row.relationSystemItem = {};
					const _dataSource = changeItemData({ itemObj: row, data: dataSource, keys: ["rowId"] });
					setDataSource(_dataSource);
				} else {
					tableRef.current.refresh();
				}
			}
		});
	};
	const addBeforeConfim = async(row: any, selectRows: any, type: string) => {
		if (!selectRows.length) {
			message.error("请选择快递助手货品");
			return;
		}
		const { skuOuterId: sysSkuOuterId } = selectRows[0].sysSkuList[0];
		const { skuOuterId: platformSkuOuterId } = row;
		const outerIdTipsFlag = warehouseStore?.sysDataSettingConfig?.outerIdTipsFlag;

		if (sysSkuOuterId !== platformSkuOuterId && outerIdTipsFlag == 2) {
			// 弹窗确认
			setConfirmInfo((prev) => {
				return { ...prev, visible: true };
			});
		} else if (outerIdTipsFlag == 1 && sysSkuOuterId !== platformSkuOuterId) {
			return bindSysItem(true);
		} else {
			return bindSysItem(false);
		}
	};
	const bindSysItem = async(shouldSync) => {
		const { row = {}, type, selectRows, resolve, reject, control, operationType, batch } = getConfirmInfo();

		// 控制操作的处理逻辑提取
		if (control) {
			batch ? (row[0].controlSync = shouldSync) : (row.controlSync = shouldSync);
			ItemBatchImportItems(operationType, batch ? row : [row], control);
			setConfirmInfo({ visible: false });
			return;
		}

		try {
			// 解构平台商品信息和系统货品信息
			const { skuId: platformSkuId, numIid: platformNumIid, platform, sellerId, relationSystemItemList = [] } = row;
			const { sysItemId, sysSkuId, skuOuterId: sysSkuOuterId } = selectRows[0]?.sysSkuList[0] || {};
			const uploadSkuOuterIdFlag = shouldSync ? 1 : 0;

			// 公共的更新行数据逻辑提取
			const updateRowData = () => {
				row.relationSystemItem = { ...selectRows[0].sysSkuList[0] };
				row.relationSystemItemList = [{ ...selectRows[0].sysSkuList[0] }];
				row.varietySkuOuterId = sysSkuOuterId;
				changeData(row);
			};

			// if (type === 'modify') {
			// 	const { sysItemId: beforeChangeSysItemId, sysSkuId: beforeChangeSysSkuId } = relationSystemItemList[0] || {};
			// 	const params = {
			// 		beforeChangeSysItemId,
			// 		beforeChangeSysSkuId,
			// 		sysItemId,
			// 		sysSkuId,
			// 		platformSkuId,
			// 		platformNumIid,
			// 		platform,
			// 		sellerId,
			// 		isUploadSkuOuterId,
			// 		...(shouldSync && { sysSkuOuterId }),
			// 	};
			// 	await ItemSysItemChangeItemRelationApi(params);
			// } else if (type === 'add') {
			// 	const params = [{
			// 		sysItemId,
			// 		sysSkuId,
			// 		platformSkuId,
			// 		platformNumIid,
			// 		platform,
			// 		sellerId,
			// 		isUploadSkuOuterId,
			// 		...(shouldSync && { sysSkuOuterId }),
			// 	}];
			// 	await ItemSysItemSaveItemRelationApi(params);
			// }
			ItemSysItemBindRelationSysSkuApi({
				platform,
				sellerId,
				numIid: platformNumIid,
				skuId: platformSkuId,
				sysSkuId,
				updateVarietySkuOuterIdFlag: true,
				uploadSkuOuterIdFlag: shouldSync
			});
			updateRowData();
			resolve(true);
			setConfirmInfo({ visible: false });
		} catch (error) {
			console.error('Error in bindSysItem:', error);
			setConfirmInfo({ visible: false });
			reject(error);
		}
	};

	const changeData = (item) => {
		const _dataSource = changeItemData({ itemObj: item, data: dataSource, keys: ["rowId"] });
		setDataSource(_dataSource);
	};

	const batchUpdatePlatformId = () => {
		if (selectedRows.length == 0) {
			message.warn("请先选择");
			return;
		}
		setQuickUpdateInfo({ visible: true, list: selectedRows.map(i => ({ numIid: i.numIid, skuId: i.skuId, sellerId: i.sellerId })) });
	};

	const onQuickUpdateSuccess = () => {
		tableRef.current.refresh();
	};

	const handleCreateCombinedGoods = (useAI = false) => {
		if (selectedRows.length == 0) {
			message.warn("请先选择");
			return;
		}
		let waitCheckNum = 0;
		let arr = selectedRows.filter(i => {
			if (i.relationSystemItem?.isCheck || i.relationSystemItem.sysSkuId) waitCheckNum++;
			return !i.isCollapse && !i.relationSystemItem.sysSkuId;
		});
		if (arr.length == 0) {
			message.warn('所选商品已建立绑定关系');
			return;
		}
		if (waitCheckNum > 0) {
			message.warn('请先批量确认绑定关系');
			return;
		}
		if (useAI) {
			onMergeHand(true);
		} else {
			setInfoModalShow(true);
		}
	};

	const ExcelImportMenu = (
		<Menu>
			<Menu.Item
				data-point={ Pointer.商品_货品与商品关系_平台商品视角_导入更新平台商品 }
				key={ ExcelImportType.导入更新平台商品 }
				onClick={ () => { setExcelImportType(ExcelImportType.导入更新平台商品); setImportModalVisible(true); } }
			>
				{ExcelImportType[ExcelImportType.导入更新平台商品]}
			</Menu.Item>
			<Menu.Item
				key={ ExcelImportType.导入商家编码 }
				onClick={ () => { setExcelImportType(ExcelImportType.导入商家编码); setImportModalVisible(true); } }
			>
				{ExcelImportType[ExcelImportType.导入商家编码]}
			</Menu.Item>
		</Menu>
	);
	const moreMenu = (
		<Menu>
			<FunctionPermissionCheck functionPermission={ FunctionPermissionEnum.解除批量解除 }>
				<Menu.Item
					key="batchRemove"
					onClick={ () => { batchRemove(); } }
					disabled={ !UserStore.hasFunctionPermission(FunctionPermissionEnum.解除批量解除) || !selectedRows.length }
				>
					批量解除
				</Menu.Item>
			</FunctionPermissionCheck>

			<FunctionPermissionCheck functionPermission={ FunctionPermissionEnum.删除批量删除 }>
				<Menu.Item
					key="batchDelete"
					onClick={ () => { batchDelete(); } }
					disabled={ !UserStore.hasFunctionPermission(FunctionPermissionEnum.删除批量删除) || !selectedRows.length }
				>
					批量删除
				</Menu.Item>
			</FunctionPermissionCheck>

		</Menu>
	);

	const filterColSpan = (cols) => {
		if (!Array.isArray(cols)) return cols;

		const checkIndex = cols.findIndex(i => i.id === 'checkCol');
		if (checkIndex === -1) return cols; // 检查是否找到 checkCol

		cols.forEach((col, idx) => {
			col.getCellProps = (_, r) => {
				if (r?.isCollapse) {
					if (idx === checkIndex) {
						return { colspan: cols.length - checkIndex };
					} else if (idx > checkIndex) {
						return { style: { display: 'none' } };
					}
					return;
				}
				if (idx < checkIndex && r?.platformItemSkuList?.length > 1 && r?.collapseIndex !== r?.platformItemSkuList?.length - 1) {
					return { style: { borderBottom: "none" } };
				}
				if (idx < checkIndex && r?.collapseShow) {
					return { style: { borderBottom: "none" } };
				}
				if (idx === checkIndex) {
					return { style: { borderRight: "none" } };
				}
			};
		});
		return cols;
	};



	const additionalFormNode = (
		<div className="r-flex r-jc-sb r-w-full">
			<div className="r-flex">
				<FunctionPermissionCheck
					functionPermission={ FunctionPermissionEnum.手动合并 }
				>
					<Tooltip placement="topRight" title="可以将相同款的平台商品，一并绑定在同一个系统货品上">
						<Badge size="small" count="new" offset={ [-10, -2] } style={ { boxShadow: "none", height: 16, padding: "1px 5px", borderRadius: 8 } }>
							<Button
								disabled={
									!UserStore.hasFunctionPermission(
										FunctionPermissionEnum.手动合并
									)
								}
								type="primary"
								onClick={ () => {
									sendPoint(Pointer.商品_货品与商品关系_平台商品视角_手动合并);
									onMergeHand();
								} }
							>
								手动合并
							</Button>
						</Badge>
					</Tooltip>
				</FunctionPermissionCheck>
				{
					!isBlackUser && (
						<FunctionPermissionCheck
							functionPermission={ FunctionPermissionEnum.同款推荐合并 }
						>
							<Tooltip placement="top" title="针对同一系统货品对于多个平台商品的情况，系统可根据相同款推荐规则，将相同的平台商品进行分组，一并绑定在同一个系统货品上">
								{/* <Badge size="small" count="限免" offset={ [-10, -2] } style={ { boxShadow: "none", height: 16, padding: "1px 5px", borderRadius: 8 } }> */}
								<Button
									disabled={
										!UserStore.hasFunctionPermission(
											FunctionPermissionEnum.同款推荐合并
										)
									}
									className={ cs("r-ml-8", s["custom-color"]) }
									onClick={ async() => {
										if (await lowVersionLock(PageNameControlEnum.商品推荐合并)) {
											setCheckVersionControl(true);
											return;
										}
										sendPoint(Pointer.商品_货品与商品关系_平台商品视角_同款推荐合并);
										setMergeMode("");
										setIsSameMergeHand(false);
										setSameMergeDrawerVisible(true);
									} }
								>
									同款推荐合并
								</Button>
								{/* </Badge> */}
							</Tooltip>
						</FunctionPermissionCheck>
					)
				}
				<FunctionPermissionCheck
					functionPermission={ FunctionPermissionEnum.下载平台商品 }
				>
					<Button
						disabled={
							!UserStore.hasFunctionPermission(
								FunctionPermissionEnum.下载平台商品
							)
						}
						data-point={
							Pointer.商品_货品与商品关系_平台商品视角_下载平台商品
						}
						className={ cs("r-ml-8", s["custom-color"]) }
						onClick={ () => {
							setSyncWaresModalVisible(true);
						} }
					>
						下载平台商品
					</Button>
				</FunctionPermissionCheck>
				<FunctionPermissionCheck
					functionPermission={ FunctionPermissionEnum.导出平台商品 }
				>
					<Button
						disabled={
							!UserStore.hasFunctionPermission(
								FunctionPermissionEnum.导出平台商品
							)
						}
						data-point={
							Pointer.商品_货品与商品关系_平台商品视角_导出平台商品
						}
						className={ cs("r-ml-8") }
						onClick={ () => {
							setDownloadExcelModalVisible(true);
						} }
					>
						<FileExcelOutlined />
						导出平台商品
					</Button>
				</FunctionPermissionCheck>
				<FunctionPermissionCheck
					functionPermission={ FunctionPermissionEnum.导入平台商品 }
				>
					<Dropdown
						overlay={ ExcelImportMenu }
						disabled={
							!UserStore.hasFunctionPermission(
								FunctionPermissionEnum.导入平台商品
							)
						}
					>
						<Button className="r-ml-8" icon={ <DownloadOutlined /> }>
							Excel导入
							<DownOutlined />
						</Button>
					</Dropdown>
				</FunctionPermissionCheck>
				<FunctionPermissionCheck
					functionPermission={ FunctionPermissionEnum.平台批量更新编码 }
				>
					<Button
						disabled={
							!UserStore.hasFunctionPermission(
								FunctionPermissionEnum.平台批量更新编码
							)
						}
						className={ cs("r-ml-8") }
						onClick={ batchUpdatePlatformId }
					>
						批量更新平台编码
					</Button>
				</FunctionPermissionCheck>
				<FunctionPermissionCheck
					functionPermission={ FunctionPermissionEnum.复制批量复制 }
				>
					<Tooltip
						overlayInnerStyle={ {
							width: "270px",
							textAlign: "center",
						} }
						title="批量复制已勾选的平台商品到本地ERP内"
					>
						<Button
							className="r-ml-8"
							data-point={
								Pointer.商品_货品与商品关系_平台商品视角_批量复制
							}
							disabled={
								!UserStore.hasFunctionPermission(
									FunctionPermissionEnum.复制批量复制
								) || !selectedRows.length
							}
							onClick={ batchCopy }
						>
							批量复制
						</Button>
					</Tooltip>
				</FunctionPermissionCheck>
				{/* <Button disabled={ !isBatchCheck } data-point={ Pointer.商品_货品与商品关系_平台商品视角_批量确认 } size="small" className="r-ml-5" onClick={ batchCheckOperate }>批量确认</Button> */}
				{/* <Button
					className="r-ml-8"
					onClick={ () => {
						handleCreateCombinedGoods();
					} }
				>
					创建组合货品并绑定
				</Button>
				<Button
					className="r-ml-8"
					onClick={ () => {
						handleCreateCombinedGoods(true);
					} }
				>
					AI解析组合并绑定
				</Button> */}
				<Dropdown.Button
					onClick={ _.debounce(() => {
						if (operateBtnsDefault.createCombinationType === "useAI") {
							handleCreateCombinedGoods(true);
						} else {
							handleCreateCombinedGoods();
						}
					}, 500, {
						leading: true,
						trailing: false
					}) }
					className="r-mr-8"
					style={ { marginLeft: "8px" } }
					overlay={ (
						<Menu>
							<Menu.Item
								key="default"
								onClick={ _.debounce(() => handleCreateCombinedGoods(), 500, {
									leading: true,
									trailing: false
								}) }
							>
								<div className="r-flex r-jc-sb r-ai-c">
									<span className="r-mr-8">创建组合货品并绑定</span>
									{
										operateBtnsDefault?.createCombinationType === "default"
											? (
												<Icon
													className="r-c-warning"
													type="guding"
													onClick={ (e) => {
														e.stopPropagation();
													} }
												/>
											)
											: (
												<Icon
													className="r-c-999"
													type="weiguding"
													onClick={ (e) => {
														e.stopPropagation();
														handleSetBtnsDefault("createCombinationType", "default");
													} }
												/>
											)
									}
								</div>
							</Menu.Item>
							<Menu.Item key="useAI" onClick={ ({ key }) => handleCreateCombinedGoods(true) }>
								<div className="r-flex r-jc-sb r-ai-c">
									<span className="r-mr-8">AI解析组合并绑定</span>
									{
										operateBtnsDefault?.createCombinationType === "useAI"
											? (
												<Icon
													className="r-c-warning"
													type="guding"
													onClick={ (e) => {
														e.stopPropagation();
													} }
												/>
											)
											: (
												<Icon
													className="r-c-999"
													type="weiguding"
													onClick={ (e) => {
														e.stopPropagation();
														handleSetBtnsDefault("createCombinationType", "useAI");
													} }
												/>
											)
									}
								</div>
							</Menu.Item>
						</Menu>
					) }
					key="createCombinationType"
				>
					{ operateBtnsDefault?.createCombinationType === "useAI" ? "AI解析组合并绑定" : "创建组合货品并绑定" }
				</Dropdown.Button>
				<Dropdown
					overlay={ moreMenu }
					disabled={ !selectedRows.length }
					placement="bottomLeft"
				>
					<Button className="r-ml-8">
						批量操作
						<DownOutlined />
					</Button>
				</Dropdown>
			</div>
			<div>
				<SysDataSetting />
			</div>
		</div>
	);

	const onCheckShowDelete = (e) => {
		const { checked } = e.target;
		setIsHideDelete((prev) => checked);
		if (checked) setOnlyShowDeleteSku(false);
		tableRef.current.refresh();
	};
	
	const onCheckJustShowDelete = (e) => {
		const { checked } = e.target;
		setOnlyShowDeleteSku((prev) => checked);
		if (checked) setIsHideDelete(false);
		tableRef.current.refresh();
	};

	const onChangeTradeWaitSendFlag = (e) => {
		const { value } = e.target;
		if (value) sendPoint(Pointer.库存版_勾选有待发货订单_查询);
		setTradeWaitSendFlag((prev) => value);
		tableRef.current.refresh();
	};

	const onChangeDiffCode = (e) => {
		const { checked } = e.target;
		setSkuOuterIdDifferentFlag((prev) => checked);
		tableRef.current.refresh();
	};

	const onChangeSameCode = (e) => {
		const { checked } = e.target;
		setSkuOuterIdRepeatFlag((prev) => checked);
		tableRef.current.refresh();
	};

	const onChangeBarCodeRepeat = (e) => {
		const { checked } = e.target;
		setBarCodeRepeatFlag((prev) => checked);
		tableRef.current.refresh();
	};

	const relationSysItemChange = (e) => {
		const { value } = e.target;
		setRelationSysItem((prev) => value);
		tableRef.current.refresh();
	};

	const topAdditionalFormNode = (
		<Space>
			<Checkbox className="r-ml-14" checked={ getIsHideDelete() } onChange={ (e) => onCheckShowDelete(e) }>
				<span style={ { whiteSpace: 'nowrap' } }>隐藏已删除规格</span>
			</Checkbox>
			<Checkbox checked={ getOnlyShowDeleteSku() } onChange={ (e) => onCheckJustShowDelete(e) }>
				<span style={ { whiteSpace: 'nowrap' } }>仅显示已删除规格</span>
			</Checkbox>

			{/* <Checkbox className="r-mr-36" checked={ getTradeWaitSendFlag() } onChange={ (e) => onChangeTradeWaitSendFlag(e) }>
				<Tooltip title="即根据最近24小时内的待发货订单进行查询，可能存在些许误差">
					<span>有待发货订单</span>
					<span className={ s.menuLinkLimitIcon } >new</span>
				</Tooltip>
			</Checkbox> */}
			<Checkbox checked={ getSkuOuterIdDifferentFlag() } onChange={ (e) => onChangeDiffCode(e) }><span style={ { whiteSpace: 'nowrap' } }>编码与关联货品规格编码不同</span></Checkbox>
			<Checkbox checked={ getSkuOuterIdRepeatFlag() } onChange={ (e) => onChangeSameCode(e) }><span style={ { whiteSpace: 'nowrap' } }>规格编码重复</span></Checkbox>
			{/* <Checkbox checked={ getBarCodeRepeatFlag() } onChange={ (e) => onChangeBarCodeRepeat(e) }><span style={ { whiteSpace: 'nowrap' } }>条形码重复</span></Checkbox> */}
		</Space>
	);

	const emptyNode = (
		<div>
			<img style={ { width: '147px' } } src="https://img.alicdn.com/imgextra/i3/69942425/O1CN01XalmOh1Tmh4RNp7Gp_!!69942425.png" alt="" />
			<p className="r-mb-40 r-mt-14 r-fs-14" style={ { color: 'rgb(255, 77, 79)' } }>暂无数据</p>
		</div>
	);
	// 步骤弹框保存回调
	const onChooseSave = async(data: any[] = []) => {
		try {
			setCreateLoading(true);
			let newList: any[] = [];
			if (data.length) {
				data.forEach(item => {
					let newItem = item?.sysSkuList?.[0];
					// newItem.skuOuterId = skuOuterId;
					if (newItem) {
						const _item = {
							groupProportionNum: item.groupProportionNumChangeValue ?? newItem.groupProportionNum,
							sysItemId: newItem.sysItemId,
							sysSkuId: newItem.sysSkuId,
						};
						newList.push(_item);
					}
				});
			}
			const itemSkuList = selectedRows?.map((item) => (
				{
					platform: item.platform,
					numIid: item.numIid,
					skuId: item.skuId,
					sellerId: item.sellerId,
				}
			));
			const params: ItemSysItemCreateCombinationBindingRequest = {
				itemSkuList,
				groupRelationRecordInfoList: newList,
			};
			const res = await ItemSysItemCreateCombinationBindingApi(params);
			setInfoModalShow(false);
			setCreateLoading(false);
			tableRef.current.refresh();
			message.success("创建并绑定成功");
			console.log("onChooseSave res:", res);
		} catch (error) {
			setCreateLoading(false);
			setInfoModalShow(false);
		}

	};

	// 步骤弹框取消回调
	const onChooseCancel = () => {
		console.log('onChooseCancel');
		setInfoModalShow(false);
	};
	useEffect(() => {
		props.visible && sendPoint(Pointer['商品_货品与商品关系_平台商品视角_展现']);
	}, [props.visible]);

	if (!props.visible) return null;

	return (
		<div>
			<SearchTable<
				ItemSysItemListOfItemRelationPlatformItemViewResponse["data"]["list"][0]
			>
				pageSizeId="platformTable"
				ref={ tableRef }
				form={ form }
				fetchData={ fetchSystemList }
				responseAdapter={ responseAdapter }
				searchBtnPoint={
					Pointer["商品_货品与商品关系_平台商品视角_查询"]
				}
				searchBtnText="查询"
				onReset={ () => {
					setTradeWaitSendFlag(null);
					setRelationSysItem(null);
					setIsHideDelete(false);
					setOnlyShowDeleteSku(false);
					setSkuOuterIdDifferentFlag(false);
					setSkuOuterIdRepeatFlag(false);
					setBarCodeRepeatFlag(false);
				} }
				resetBtnText="重置"
				showSearchToggle
				additionalFormNode={ topAdditionalFormNode }
				showSearch
				rowFormConfig={ {
					expandNode: props.expandNode,
					formList: FormFieldList,
					defaultParams: {
						approveStatus: "onsale",
					},
					colProps: {},
				} }
				hidePagination={ getTradeWaitSendFlag() }
				baseTableConfig={ {
					noGap: true,
					innerTableStyle: {
						paddingTop: 0
					},
					rowKey: "rowId",
					groupId: "groupId",
					locale: {
						emptyText: emptyNode,
					},
					expandContext: additionalFormNode,
					cachePgination: true,
					pagination: {
						pageSizeOptions: [10, 20, 50, 100, 200, 500],
					},
					columns: filterColSpan(columns),
					scrollExtraHeight: getTradeWaitSendFlag() ? -40 : 0,
					dataSource: tableRowDataSource,
					colRewidth: true,
					stickyTop: 186,
					isStickyHeader: true,
				} }
				tableExtra={ (
					<div className={ cs(s["relation-head"]) }>
						<div
							style={ { color: "#fff" } }
							className={ cs("r-bg-52C41A") }
						>
							<Space>
								平台商品
								<Tooltip title="通过已设置规则快速填充规格编码商家编码">
									<Button
										data-point={
											Pointer.商品_货品与商品关系_平台商品视角_快速填充
										}
										type="primary"
										className={ s.quickBtn }
										loading={ quickLoading }
										onClick={ onClearOuterIdOk }
									>
										快速生成编码
									</Button>
								</Tooltip>
								<SettingOutlined
									data-point={
										Pointer.商品_货品与商品关系_平台商品视角_快速填充设置
									}
									className={ s.settingIcon }
									onClick={ () => setQuickAddInfo({
										visible: true,
									}) }
								/>
								<Tooltip title="支持商家编码、规格编码根据需要进行自定编辑与简化，通过快速填充可以批量对编码进行维护">
									<QuestionCircleFilled />
								</Tooltip>
							</Space>
						</div>
						<div
							style={ { color: "#fff" } }
							className={ cs("r-bg-F5821F") }
						>
							系统货品
						</div>
					</div>
				) }
			/>
			{/* 相同sku选择 */}
			<SameSkuTable
				dataSource={ sameSku.sameSkuDataSource }
				visible={ sameSku.sameSkuVisible }
				cancel={ () => {
					setSameSku({ ...sameSku, sameSkuVisible: false });
				} }
				onOk={ sameSku.resolve }
			/>
			<SyncWaresModal
				code={ FunctionPermissionEnum.下载平台商品 }
				visible={ syncWaresModalVisible }
				isDownload
				afterOK={ afterSyncWaresOK }
				onCancel={ () => {
					setSyncWaresModalVisible(false);
				} }
			/>
			<SameMergeDrawer
				code={ FunctionPermissionEnum.同款推荐合并 }
				visible={ sameMergeDrawerVisible }
				isHand={ isSameMergeHand }
				mergeHandData={ dataSameMergeHand }
				afterOK={ afterSameMergeOK }
				mergeMode={ mergeMode }
				onCancel={ () => {
					setSameMergeDrawerVisible(false);
				} }
			/>
			<SameMergeSkusDrawer
				visible={ sameMergeSkusDrawerVisible }
				searchSameByAIItems={ searchSameByAIItems }
				afterOK={ afterSameMergeOK }
				onCancel={ () => {
					setSameMergeSkusDrawerVisible(false);
				} }
			/>
			<Modal
				centered
				visible={ getConfirmInfo().visible }
				title="系统提示"
				zIndex={ 1001 }
				maskClosable={ false }
				onCancel={ () => {
					setConfirmInfo({ visible: false });
					return getConfirmInfo().reject();
				} }
				footer={ [
					<Button
						type="primary"
						onClick={ () => {
							bindSysItem(true);
						} }
						key="tongbu"
					>
						同步到线上
					</Button>,
					<Button
						key="butongbu"
						onClick={ () => {
							bindSysItem(false);
						} }
						type="primary"
					>
						不同步
					</Button>,
					<Button
						onClick={ () => {
							setConfirmInfo({ visible: false });
							return getConfirmInfo().reject();
						} }
						key="cancel"
					>
						取消
					</Button>
				] }
			>
				{getConfirmInfo().batch ? '将变化的规格编码更新到线上店铺' : `确定将线上平台商品的规格编码更换为${getConfirmInfo().sysSkuOuterId}`}
			</Modal>

			<Modal
				centered
				visible={ batchCopyInfo.visible }
				title="批量复制"
				onCancel={ () => setBatchCopyInfo({ visible: false }) }
				onOk={ batchCopyConfirm }
				maskClosable={ false }
			>
				批量导入 {batchCopyInfo.importGoodsNum} 个平台商品
				<div>
					<Checkbox
						checked={ platfromCodeAsSysCode }
						onChange={ (e) => {
							onPlatfromCodeAsSysCodeToggle(e.target.checked);
						} }
					>
						<span className="r-c-error">
							使用平台商家编码做为系统商品简称
						</span>
					</Checkbox>
					<br />
					<Checkbox
						checked={ syncOuter }
						onChange={ (e) => {
							onSyncOuterToggle(e.target.checked);
						} }
					>
						<span className="r-c-error">
							将变化的规格编码更新到线上店铺
						</span>
					</Checkbox>
				</div>
			</Modal>
			<ProgressModal
				loopObj={ {
					...loopObj,
					api: ItemAsyncGetProgressApi,
				} }
				modalProps={ {
					title: "批量导入商品进度查询",
				} }
				onClose={ () => {
					setLoopObj((prev) => ({
						...prev,
						isStart: false,
					}));
				} }
				onSuccess={ (res) => {
					setLoopObj((prev) => ({
						...prev,
						isStart: false,
					}));
					setBatchCopyInfo({ visible: false });
					tableRef.current.refresh();
				} }
			/>
			{/* 导出Excel */}
			<Modal
				centered
				visible={ downloadExcelModalVisible }
				title="导出平台商品"
				okText="导出"
				onOk={ onDownloadExcel }
				onCancel={ onCloseDownloadExcelModal }
				confirmLoading={ exportLoading }
				destroyOnClose
				maskClosable={ false }
			>
				<div>
					导出类型：
					<Radio.Group
						value={ exportType }
						onChange={ onChangeExportType }
					>
						<Radio value={ 1 }>导出勾选商品</Radio>
						<Radio value={ 2 }>导出当前查询结果</Radio>
					</Radio.Group>
				</div>
			</Modal>
			{/* 导入弹窗 */}
			<BatchImportModal
				visible={ importModalVisible }
				onOkAfter={ systemRefresh }
				onClose={ () => {
					setImportModalVisible(false);
				} }
				excelImportType={ excelImportType }
			/>
			<QuickGenModal
				visible={ quickAddInfo.visible }
				onCancel={ () => setQuickAddInfo({ visible: false }) }
			/>
			<QuickClearModal
				visible={ quickClearInfo.visible }
				type={ quickClearInfo.type }
				onConfirm={ onQuickClear }
				onCancel={ () => setQuickClearInfo({ visible: false, type: null }) }
				from="AssistantTable"
			/>
			<QuickUpdateModal
				visible={ quickUpdateInfo.visible }
				list={ quickUpdateInfo.list }
				onSuccess={ onQuickUpdateSuccess }
				onCancel={ () => setQuickUpdateInfo({ visible: false, list: [] }) }
			/>
			{/* 保存当前页面所有更改的loading */}
			<Modal
				wrapClassName={ s.platformBatchSaveLoading }
				visible={ loading }
				maskClosable={ false }
				getContainer={ document.body }
				footer={ null }
				bodyStyle={ { display: "none" } }
				destroyOnClose
			/>
			<CombinedChoose
				onSave={ onChooseSave }
				onCancel={ onChooseCancel }
				isVisible={ infoModalShow }
				list={ [] }
				type="add"
				step2_OkText="创建组合货品并绑定"
				loading={ createLoading }
			/>
			{
				checkVersionControl ? <LowVersionControlModal pageName={ PageNameControlEnum.商品推荐合并 } closable onCancel={ () => { setCheckVersionControl(false); } } /> : null
			}
			<SearchSameByAI mode="checkbox" searchSameByAIItem={ searchSameByAIItem } onAddConfirm={ onAddConfirm } onCancel={ () => { setSearchSameByAIVisible(false); } } visible={ searchSameByAIVisible } />
		</div>
	);
};
export default observer(AssistantTable);
