.live-type-tabs {
    background: #ffffff;
    padding: 0 16px 16px;

    .tabs-header {
        display: flex;
        gap: 32px;
        padding: 0 0 0 16px;
        border-width: 0px 0px 1px 0px;
        border-style: solid;
        border-color: rgba(0, 0, 0, 0.06);
        height: 48px;

        .tabs-header-content {
            display: flex;
            gap: 32px;
            height: 100%;
            flex: 1;
        }

        .tabs-header-tip {
            flex-shrink: 0;
            width: 600px;
            height: 40px;
            border-radius: 8px;
            padding: 0px 16px;
            background: linear-gradient(180deg, #FFFFFF 34%, #FFF9EF 100%);
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .icon {
                width: 76px;
            }

            .icon2 {
                width: 24px;
            }

            .dot {
                width: 24px;
                height: 24px;
                border-radius: 32px;
                background: #FD8204;
                box-sizing: border-box;
                font-size: 16px;
                line-height: 14px;
                font-weight: 600;
                justify-content: center;
                display: flex;
                align-items: center;
                color: #FFFFFF;
                letter-spacing: 0px;
            }

            .title {
                font-size: 14px;
                color: rgba(0, 0, 0, 0.85);
                line-height: 22px;
            }

            .titleClick {
                font-size: 14px;
                color: #1890FF;
                line-height: 22px;
                cursor: pointer;
            }
        }

        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            padding: 12px 0;
            position: relative;
            min-width: 64px;
            border-bottom: 2px solid transparent;
            z-index: 2;

            &:hover:not(.disabled) {
                transform: translateY(-1px);
            }

            &.active {
                position: relative;
                transition: all 0.3s ease;

                &::after {
                    content: '';
                    position: absolute;
                    bottom: -3px;
                    left: 0;
                    right: 0;
                    height: 2px;
                    background: linear-gradient(90deg, #6068F4 34%, #853CE8 88%);
                }
            }

            &.disabled {
                cursor: not-allowed;
            }

            .tab-icon {
                img {
                    height: 16px;
                    object-fit: contain;
                }
            }
        }
    }

    .tab-content {
        margin-top: 16px;
        position: relative;

        .content-wrapper {
            position: relative;
            height: 170px;
            width: 100%;
            background: #F1F4FF;
            border-radius: 2px;
            overflow: hidden;

            .content-bg {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 1;
                background-size: 100% 100%;
                background-repeat: no-repeat;
                background-position: center;
            }

            .content-description {
                position: absolute;
                top: 33px;
                left: 47px;
                width: auto;
                height: 98px;
                z-index: 2;
            }

            .content-description2 {
                position: absolute;
                top: 10px;
                right: 100px;
                width: 200px;
                height: auto;
                z-index: 2;
            }
        }
    }
}