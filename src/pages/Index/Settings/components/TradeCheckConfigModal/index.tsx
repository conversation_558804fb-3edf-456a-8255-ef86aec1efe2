import React, { useEffect, useState, useMemo } from 'react';
import { Alert, Button, Checkbox, Modal, Radio, Table, message, Tooltip } from 'antd';
import { Link } from 'react-router-dom';
import { observer } from 'mobx-react';
import userStore from '@/stores/user';
import { getWarehouseVersion } from '@/pages/AfterSale/TradeList/utils';
import { SettingSaveUserSettingApi } from '@/apis/user';
import Icon from "@/components/Icon";
import LowProfitConfigModal from './components/LowProfitConfigModal';
import ZeroStockConfigModal from './components/ZeroStockConfigModal';
import s from './index.module.scss';

const SYSTEM_DATA = [
	// { title: "订单所属店铺授权过期", node: '打印快递单前；发货前', opt: '不可强制打印发货' },
	// { title: "订单所属店铺订购过期", node: '打印快递单前；发货前', opt: '不可强制打印发货' },
	{ title: "订单所属店铺长期未同步", node: '打印快递单前；发货前', opt: '不可强制打印发货' },
	{ title: "分销关系异常", node: '打印快递单前；发货前', opt: '不可强制打印发货' },
	{ title: "分销订单不存在", node: '打印快递单前；发货前', opt: '不可强制打印发货' },
	{ title: "分销订单产生售后", node: '打印快递单前；发货前', opt: '不可强制打印发货' },
	{ title: "模板与以下订单所属平台不一致", node: '打印快递单前', opt: '不可强制打印发货' },
	{ title: "抖音承诺日达订单发货模板校验", node: '打印快递单前；发货前', opt: '不可强制打印发货' },
	{ title: "退款成功商品发货拦截", node: '发货前', opt: '不可强制发货' },
	{ title: "未勾选商品订单", node: '打印快递单前', opt: '不可强制打印快递单' },
	{ title: "3C设备识别码未填写", node: '发货前', opt: '不可强制打印发货' },
	{ title: "挂起订单拦截", node: '打印快递单前；发货前', opt: '不可强制打印发货' },
	{ title: "发货前未获取单号发货", node: '发货前', opt: '不可强制发货' },
	{ title: "买家修改地址异常", node: '打印快递单前', opt: '可强制操作' },
	{ title: "线上改商品异常", node: '打印快递单前', opt: '可强制操作' },
	{ title: "线上已发货订单", node: '打印快递单前；发货前', opt: '可强制操作' },
	{ title: "线上已退款订单", node: '打印快递单前；发货前', opt: '可强制操作' },
	{ title: "存在待发货的系统商品", node: '打印快递单前；发货前', opt: '可强制操作' },
	{ title: "订单在当前页已操作过发货", node: '打印快递单前；发货前', opt: '可强制操作' },
	{ title: "存在已发货订单商品", node: '打印快递单前；发货前', opt: '可强制操作' },
	{ title: "BIC质检订单", node: '打印快递单前；发货前', opt: '不可强制打印发货' },
	{ title: "订单正在执行自动策略", node: '打印快递单前；发货前', opt: '不可强制打印发货' },
	{ title: "列表加载商品数据与数据库商品数据不一致", node: '打印快递单前；发货前', opt: '不可强制打印发货' },
	{ title: "得物订单商家未接单", node: '打印快递单前；发货前', opt: '不可强制打印发货' },
	{ title: "AE自营订单不可发货", node: '发货前', opt: '不可强制发货' },
	{ title: "官方仓发不可打印发货", node: '打印快递单前；发货前', opt: '不可强制打印发货' }
];

const SYSTEM_DATA2 = [
	{ title: "订单所属店铺长期未同步", node: '打印快递单前；发货前；生成波次前', opt: '不可强制打印发货' },
	{ title: "分销关系异常", node: '打印快递单前；发货前；生成波次前', opt: '不可强制打印发货' },
	{ title: "分销订单不存在", node: '打印快递单前；发货前；生成波次前', opt: '不可强制打印发货' },
	{ title: "分销订单产生售后", node: '打印快递单前；发货前；生成波次前', opt: '不可强制打印发货' },
	{ title: "模板与以下订单所属平台不一致", node: '打印快递单前', opt: '不可强制打印发货' },
	{ title: "抖音承诺日达订单发货模板校验", node: '打印快递单前；发货前；生成波次前', opt: '不可强制打印发货' },
	{ title: "退款成功商品发货拦截", node: '发货前；生成波次前', opt: '不可强制发货' },
	{ title: "未勾选商品订单", node: '打印快递单前；生成波次前', opt: '不可强制打印快递单' },
	{ title: "3C设备识别码未填写", node: '发货前；生成波次前', opt: '不可强制打印发货' },
	{ title: "挂起订单拦截", node: '打印快递单前；发货前；生成波次前', opt: '不可强制打印发货' },
	{ title: "未获取单号", node: '发货前；生成波次前', opt: '不可强制发货' },
	{ title: "买家修改地址异常", node: '打印快递单前', opt: '可强制操作' },
	{ title: "线上改商品异常", node: '打印快递单前', opt: '可强制操作' },
	{ title: "线上已发货订单", node: '打印快递单前；发货前；生成波次前', opt: '可强制操作' },
	{ title: "存在待发货的系统商品", node: '打印快递单前；发货前', opt: '可强制操作' },
	{ title: "订单在当前页已操作过发货", node: '打印快递单前；发货前', opt: '可强制操作' },
	{ title: "存在已发货订单商品", node: '打印快递单前；发货前；生成波次前', opt: '可强制操作' },
	{ title: "BIC质检订单", node: '打印快递单前；发货前；生成波次前', opt: '不可强制打印发货' },
	{ title: "订单正在执行自动策略", node: '打印快递单前；发货前；生成波次前', opt: '不可强制打印发货' },
	{ title: "列表加载商品数据与数据库商品数据不一致", node: '打印快递单前；发货前；生成波次前', opt: '不可强制打印发货' },
	{ title: "得物订单商家未接单", node: '打印快递单前；发货前；生成波次前', opt: '不可强制打印发货' },
	{ title: "AE自营订单不可发货", node: '发货前；生成波次前', opt: '不可强制发货' },
	{ title: "官方仓发不可打印发货", node: '打印快递单前；发货前；生成波次前', opt: '不可强制打印发货' },
	{ title: "已生成拣货波次", node: '打印快递单、生成波次前', opt: '不可强制生成' },
	{ title: "商品未设置货位", node: '生成波次前', opt: '不可强制生成' }
];

interface TradeCheckConfigModalInf {
    visible: boolean;
		onCancel: () => void;
}
const TradeCheckConfigModal:React.FC<TradeCheckConfigModalInf> = (props) => {
	const [open, setOpen] = useState(false);
	const { onCancel } = props;
	const [isStock, setIsStock] = useState(true);
	const [loading, setLoading] = useState(false);
	const [extraSetting, setExtraSetting] = useState({});
	const [userSetting, setUserSetting] = useState({});
	const [lowProfitConfigVisible, setLowProfitConfigVisible] = useState(false);
	const [zeroStockConfigVisible, setZeroStockConfigVisible] = useState(false);
	const hasWaveManagePermission = userStore.hasWaveManagePermission;

	useEffect(() => {
		if (props.visible) {
			userStore.getUserSetting().then((res:any) => {
				console.log('%c [ res ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', res);
				setExtraSetting(res.extraSetting);
				setUserSetting(res);
			});
		}
		getWarehouseVersion().then(res => { setIsStock(res); });
		return () => {
			setOpen(false);
		};
	}, [props.visible]);

	const onSubmit = () => {
		setLoading(true);
		SettingSaveUserSettingApi({ ...userSetting, extraSetting }).then(async(res) => {
			if (res) {
				message.success('保存成功');
				let currentSetting = { ...userStore.userSetting, extraSetting };
				userStore.setUserSetting(currentSetting);
				onCancel();
			} else {
				message.success('保存失败');
			}
		}).finally(() => {
			setLoading(false);
		});
	};
	const ERROR_TYPES = [
		{
			title: '商品未绑定本地货品',
			value: 'abnormalItemUnbindCheckNew',
			...extraSetting['abnormalItemUnbindCheckNew'] || {},
			waveBeforeCheck: true, // 生成波次前
		},
		{ title: '含退款商品的订单', forceCheck: true, value: 'abnormalRefundCheckNew', ...extraSetting['abnormalRefundCheckNew'] || {} },
		{ title: '异常地址(双地址)', value: 'abnormalAddressCheckNew', ...extraSetting['abnormalAddressCheckNew'] || {} },
		{
			title: (
				<div style={ { display: 'flex', alignItems: 'center' } }>
					<span>快递不可达订单检测</span>
					<span>
						<Link to="/settings/expressCanUp">
							<Button type="link" className={ s.checkModalItem }>配置不可达地区</Button>
						</Link>
					</span>
				</div>
			),
			value: 'abnormalExpressArriveCheckNew',
			...extraSetting['abnormalExpressArriveCheckNew'] || {}
		},
		isStock ? {
			title: '库存不足订单(缺货)',
			splitCheck: true,
			forceCheck: true,
			value: 'abnormalStockWarnCheckNew',
			...extraSetting['abnormalStockWarnCheckNew'] || {},
			waveBeforeCheck: true, // 生成波次前
		} : false,
		{
			title: (
				<div style={ { display: 'flex', alignItems: 'center' } }>
					<span>低利润订单检测</span>
					<span>
						<Button
							type="link"
							className={ s.checkModalItem }
							onClick={ () => setLowProfitConfigVisible(true) }
						>
							低利润订单配置
						</Button>
					</span>
				</div>
			),
			splitCheck: true,
			forceCheck: true,
			supportForceAction: true,
			value: 'abnormalLowProfitTradeConfigCheck',
			...extraSetting['abnormalLowProfitTradeConfigCheck'] || {},
		},
		{
			title: '系统赠品异常',
			value: 'abnormalSysGiftCheckNew',
			...extraSetting['abnormalSysGiftCheckNew'] || {},
			printBeforeCheck: true, // 选中
			sendBeforeCheck: true, // 选中
			waveBeforeCheck: true, // 生成波次前
			printBeforeDisabled: true, // 不可点击
			forceCheck: true, // 可强制可不强制，默认为“不可强制”
		},
		{ title: '模板与订单业务不匹配', disableCheck: true, forceCheck: true, value: 'abnormalTradeTemplateCheckNew', ...extraSetting['abnormalTradeTemplateCheckNew'] || {} },
		{ title: "换货手工单对应的售后单异常", disableCheck: true, forceCheck: true, value: 'abnormalExchangeAfterSalesTradeCheck', ...extraSetting['abnormalExchangeAfterSalesTradeCheck'] || {} },
		{ title: '小时达平台配送订单', disableCheck: true, forceCheck: true, value: 'abnormalXsdPtpsCheck', ...extraSetting['abnormalXsdPtpsCheck'] || {} }

	].filter(Boolean);

	const supportForceActionChange = (v, record) => {
		if (record.value === 'abnormalTradeTemplateCheckNew' && v.target.value) {
			const content = (
				<div>
					<div>是否确定<span className="r-c-error">允许</span>模板与订单业务不匹配订单进行强制打单发货？</div>
					<div>（<span className="r-c-error">平台/用户对于订单有指定快递要求时，如发货违规，可能面临平台处罚</span>）</div>
				</div>
			);
			Modal.confirm({
				title: '系统提醒',
				content,
				onOk() {
					setExtraSetting(pre => {
						return {
							...pre, [record.value]: { ...pre[record.value], supportForceAction: v.target.value }
						};
					});
				}
			});
		} else if (record.value === 'abnormalExchangeAfterSalesTradeCheck' && v.target.value) {
			const content = (
				<div>
					<div>是否确定<span className="r-c-error">允许</span>换货手工单对应的售后单异常订单进行强制打单发货？</div>
					<div>（售后单异常是指售后单的售后类型有变更或售后单状态有变更；<span className="r-c-error">谨慎开启，容易多发导致资损</span>）</div>
				</div>
			);
			Modal.confirm({
				title: '系统提醒',
				content,
				onOk() {
					setExtraSetting(pre => {
						return {
							...pre, [record.value]: { ...pre[record.value], supportForceAction: v.target.value }
						};
					});
				}
			});
		} else {
			setExtraSetting(pre => {
				return {
					...pre, [record.value]: { ...pre[record.value], supportForceAction: v.target.value }
				};
			});
		}
	};

	// 根据波次权限显示一些文案
	const modalObj = useMemo(() => {
		if (!hasWaveManagePermission) {
			return {
				tip1: `系统将根据配置内容，在打印、发货前对订单进行异常判断并进行提示。`,
				tip2: `勾选后此类订单在打印前/发货前会被检测到，可设置是否可强制打印发货`,
				systemData: SYSTEM_DATA
			};
		} else {
			// 有波次管理权限
			return {
				tip1: `系统将根据配置内容，在生成波次前、打印、发货前对订单进行异常判断并进行提示。`,
				tip2: `勾选后此类订单在打印前/发货/生成波次前会被检测到，可设置是否可强制打印发货`,
				systemData: SYSTEM_DATA2,
			};
		}
	}, [hasWaveManagePermission]);

	const columns = useMemo(() => {
		if (!hasWaveManagePermission) {
			return [
				{
					title: '异常类型',
					dataIndex: 'title'
				},
				{
					title: '异常提醒节点',
					dataIndex: 'node',
					render(_, record) {
						record.printBeforeCheck = !!record.printBeforeCheck;
						record.sendBeforeCheck = !!record.sendBeforeCheck;
						if (record.disableCheck) {
							return <div>打印、发货前提醒</div>;
						}
						if (!record.splitCheck) {
							return (
								<div>
									<Checkbox
										disabled={ record.printBeforeDisabled ?? false }
										checked={ record.printBeforeCheck }
										onChange={ e => setExtraSetting(pre => {
											return {
												...pre,
												[record.value]: {
													...pre[record.value],
													printBeforeCheck: e.target.checked,
													sendBeforeCheck: e.target.checked,
												}
											};
										}) }
									><span className="r-fc-black-65">打印、发货前提醒</span>
									</Checkbox>
								</div>
							);
						}
						return (
							<div>
								<Checkbox
									checked={ record.printBeforeCheck }
									onChange={ e => setExtraSetting(pre => {
										return { ...pre, [record.value]: { ...pre[record.value], printBeforeCheck: e.target.checked } };
									}) }
								><span className="r-fc-black-65">打印快递单前</span>
								</Checkbox>
								<Checkbox
									checked={ record.sendBeforeCheck }
									onChange={ e => setExtraSetting(pre => {
										return { ...pre, [record.value]: { ...pre[record.value], sendBeforeCheck: e.target.checked } };
									}) }
								><span className="r-fc-black-65">发货前</span>
								</Checkbox>
							</div>
						);
					}
				},
				{
					title: '异常订单强制操作限制',
					dataIndex: 'supportForceAction',
					render(supportForceAction, record) {
						supportForceAction = !!supportForceAction;
						if (!record.forceCheck) {
							return <span className="r-fc-black-65">可强制操作</span>;
						}
						return (
							<Radio.Group
								// disabled={ !record.printBeforeCheck && !record.sendBeforeCheck }
								value={ supportForceAction }
								onChange={ (v) => {
									supportForceActionChange(v, record);
								} }
							>
								<Radio value><span className="r-fc-black-65">可强制</span></Radio>
								<Radio value={ false }><span className="r-fc-black-65">不可强制</span></Radio>
							</Radio.Group>
						);
					}
				}
			];
		} else {
			return [
				{
					title: '异常类型',
					dataIndex: 'title'
				},
				{
					title: '异常提醒节点',
					dataIndex: 'node',
					render(_, record) {
						record.printBeforeCheck = !!record.printBeforeCheck;
						record.sendBeforeCheck = !!record.sendBeforeCheck;
						record.waveBeforeCheck = !!record.waveBeforeCheck;
						if (record.disableCheck) {
							return <div>打印、发货、生成波次前提醒</div>;
						}
						if (!record.splitCheck) {
							return (
								<div>
									<Checkbox
										disabled={ record.printBeforeDisabled ?? false }
										checked={ record.printBeforeCheck }
										onChange={ e => setExtraSetting(pre => {
											return {
												...pre,
												[record.value]: {
													...pre[record.value],
													printBeforeCheck: e.target.checked,
													sendBeforeCheck: e.target.checked,
													waveBeforeCheck: ['abnormalItemUnbindCheckNew'].includes(record.value) ? true : e.target.checked, // 始终需要勾选的放这
												}
											};
										}) }
									>
										<span className="r-fc-black-65">打印、发货、生成波次前提醒</span>
										{
											record.value == 'abnormalItemUnbindCheckNew' && (
												<Tooltip title="生成波次前会强制校验，且不可强制操作" overlayStyle={ { maxWidth: 400 } }>
													<span style={ { color: "#666" } }>
														<Icon type="wenhao-xian" size={ 16 } />
													</span>
												</Tooltip>
											)
										}
									</Checkbox>
								</div>
							);
						}
						return (
							<div>
								<Checkbox
									checked={ record.printBeforeCheck }
									onChange={ e => setExtraSetting(pre => {
										return { ...pre, [record.value]: { ...pre[record.value], printBeforeCheck: e.target.checked } };
									}) }
								><span className="r-fc-black-65">打印快递单前</span>
								</Checkbox>
								<Checkbox
									checked={ record.sendBeforeCheck }
									onChange={ e => setExtraSetting(pre => {
										return { ...pre, [record.value]: { ...pre[record.value], sendBeforeCheck: e.target.checked } };
									}) }
								><span className="r-fc-black-65">发货前</span>
								</Checkbox>

								{
									['abnormalStockWarnCheckNew', 'abnormalLowProfitTradeConfigCheck'].includes(record.value) && (
										<Checkbox
											disabled={ record.value == 'abnormalStockWarnCheckNew' }
											checked={ record.waveBeforeCheck }
											onChange={ e => setExtraSetting(pre => {
												return { ...pre, [record.value]: { ...pre[record.value], waveBeforeCheck: e.target.checked } };
											}) }
										>
											<span className="r-fc-black-65">生成波次前</span>
											<Tooltip title="生成波次前会强制校验，且不可强制操作" overlayStyle={ { maxWidth: 400 } }>
												<span style={ { color: "#666" } }>
													<Icon type="wenhao-xian" size={ 16 } />
												</span>
											</Tooltip>
										</Checkbox>
									)
								}

							</div>
						);
					}
				},
				{
					title: '异常订单强制操作限制',
					dataIndex: 'supportForceAction',
					render(supportForceAction, record) {
						supportForceAction = !!supportForceAction;
						if (!record.forceCheck) {
							return <span className="r-fc-black-65">可强制操作</span>;
						}
						return (
							<Radio.Group
								// disabled={ !record.printBeforeCheck && !record.sendBeforeCheck }
								value={ supportForceAction }
								onChange={ (v) => {
									supportForceActionChange(v, record);
								} }
							>
								<Radio value><span className="r-fc-black-65">可强制</span></Radio>
								<Radio value={ false }><span className="r-fc-black-65">不可强制</span></Radio>
							</Radio.Group>
						);
					}
				}
			];
		}
	}, [hasWaveManagePermission]);

	return (
		<>
			<Modal
				centered
				title="异常订单检测配置"
				width={ !hasWaveManagePermission ? 800 : 940 }
				onCancel={ onCancel }
				bodyStyle={ { overflowY: 'auto', maxHeight: 'calc(100vh - 200px)' } }
				visible={ props.visible }
				maskClosable={ false }
				footer={ [
					<Button type="default" onClick={ onCancel }>取消</Button>,
					<Button loading={ loading } type="primary" onClick={ onSubmit }>确定</Button>
				] }
			>
				<div className="r-ta-l">
					<Alert showIcon type="warning" message={ modalObj.tip1 } />
					<div className="r-flex r-mb-16 r-mt-16 r-ai-c" style={ { lineHeight: 1 } }>
						<h4 className="r-mr-6">异常订单检测</h4>
						<span className="r-fs-12 r-fc-black-45">{modalObj.tip2}</span>
					</div>
					<Table
						rowClassName={ s.rowClassName }
						className={ s.tableClassName }
						pagination={ false }
						bordered
						columns={ columns }
						dataSource={ ERROR_TYPES as any }
					/>

					<div className="r-flex r-mt-16 r-ai-c" style={ { lineHeight: 1 } }>
						<h4 className="r-mr-6">系统默认异常提醒</h4>
						<span className="r-fs-12 r-fc-F5821F" style={ { cursor: 'pointer', userSelect: 'none' } } onClick={ () => setOpen(!open) }>{open ? '收起' : '查看'}</span>
					</div>
					<div className="r-mt-16" style={ { display: open ? 'block' : 'none' } }>
						<Table
							className={ s.tableClassName }
							pagination={ false }
							bordered
							rowClassName={ s.rowClassName }
							columns={ [
								{
									title: '异常类型',
									dataIndex: 'title',
									render(txt) {
										return <span >{ txt}</span>;
									}
								},
								{
									title: '异常提醒节点',
									dataIndex: 'node',
									render(txt) {
										return <span>{ txt}</span>;
									}
								},
								{
									title: '异常订单强制操作限制',
									dataIndex: 'opt',
									render(txt) {
										return <span >{ txt}</span>;
									}
								}
							] }
							dataSource={ modalObj.systemData }
						/>
					</div>
				</div>
			</Modal>
			<LowProfitConfigModal
				visible={ lowProfitConfigVisible }
				onOk={ (config) => setUserSetting(config) }
				onCancel={ () => setLowProfitConfigVisible(false) }
			/>
		</>
	);
};

export default observer(TradeCheckConfigModal);
