import React, { useState, useEffect } from 'react';
import { Modal, Spin, Row, Checkbox, Form, message, Tooltip, InputNumber } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import styles from './index.module.scss';
import { GetRealTimeRefundConfigApi, UpdateRefundGlobalConfigApi } from '@/apis/aftersale/trade';

interface AdvancedSettingModalProps {
	visible: boolean,
	countSetting: any,
	onUpdateSetting: (data:any) => void,
	onCancel: () => void,
	updateConfig?: Function;
	isDistributor?: boolean;
}

const CountSettingModal: React.FC<AdvancedSettingModalProps> = (props) => {
	const { visible, onCancel, onUpdateSetting, countSetting } = props;
	const [form] = Form.useForm();
	const [updateTime, setUpdateTime] = useState(0);

	useEffect(() => {
		if (visible && countSetting) {
			GetRealTimeRefundConfigApi({
				configQueryList: ["logisticsInterceptTabCacheTime"]
			}).then(res => {
				setUpdateTime(Number(res?.logisticsInterceptTabCacheTime || 0) / 60);
			});
			form.setFieldsValue(countSetting);
		}
	}, [visible]);
	
	const onSave = () => {
		form.validateFields().then((values) => {
			console.log('%c [ save values ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', values);
			onUpdateSetting && onUpdateSetting(values);
		});
	};

	const inputRateProps = {
		formatter: (value: any) => {
			if (value && value > 0) {
				if (value > 30) {
					return 30;
				} else {
					return Number(value).toFixed(0);
				}
			} else {
				return 7;
			}
		},
	};

	return (
		<>
			<Modal
				centered
				width={ 600 }
				title="统计设置"
				visible={ visible }
				// confirmLoading
				maskClosable={ false }
				onCancel={ onCancel }
				onOk={ onSave }
				okText="保存"
				// footer={ false }
				className={ styles.modal }
			>
				<Form form={ form } preserve={ false }>
					<div>
						数据统计时间：添加时间为近
						<Form.Item noStyle label="售后单统计" name="statisticsDay" className={ styles['setting-item'] }>
							<InputNumber className="r-ml-8 r-mr-8" min={ 1 } controls={ false } style={ { width: 50 } } { ...inputRateProps } />
						</Form.Item>
						天的快递拦截数据
					</div>
					<div className="r-flex r-mt-8">
						<div>注意事项：</div>
						<div>
							<div>数据非实时，每{updateTime}分钟更新一次；</div>
							<div className="r-mt-8">快捷筛选统计数值不会跟随下方查询条件变化。</div>
						</div>
					</div>
				</Form>
			</Modal>
		</>
	);
};

export default CountSettingModal;
