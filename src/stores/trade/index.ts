import { makeAutoObservable, autorun, runInAction, computed, observable } from "mobx";
import { searchParamsType } from "../../types/schemas/report/grossProfit/index";
import { IColumn, IColumnItem, IPrintSet, IProductSetting } from "@/types/trade/index";
import { IKddTemp, IKddTempGroup, IBhdXbqTemp } from "@/types/schemas/report/index";
import { IPackage, IScanningLabelInfo, Obj } from "@/pages/Trade/interface";
import { defaultColWidth } from "@/pages/Trade/constants";
import TradeListStore, { 排序 } from "./tradeList";
import GoodsTagListStore from "./goodsTag";
import LiveTagListStore from "./liveTag";
import scanPrintStore from './scanPrint';
import ListFilterStore from "./listFilter";
import TradeOptStore from "./tradeOpt";
import TradePrintStore from "./tradePrint";
import tradeSetStore from "./tradeSet";
import foreignManage from "../foreignManage";
import { ItemFilterSettingSelectApi, TradeUserGetUserCustomSetApi } from "@/apis/trade";
import { ItemSmartExpressGetListApi } from "@/apis/setting/smartExpress";
import events from "@/utils/events";
import { PrintCenterExCompanyGetExCompanyAllResponse } from "@/types/schemas/user";
import { TradeDictQueryDictResponse, TradeQueryTradeRequest, TradeSearchFlagRule, TradeUserMarkLabelItem } from "@/types/trade/search/search";
import { hotGoodsDefaultColumnConfig, TEMPLATE_ZMJ } from "@/constants";
import { avoidRepeatReq, getMd5Str } from "@/utils/util";
import { IndexSettingGetUserHandleTaskProgressResponse } from "@/types/schemas/warehouse/syncstock";
import RefundLabelScanPrintStore from "./refundLabelScanPrint";
import { IExceptionWarnSet } from "@/pages/ExceptionWarn/utils";
import InspectSendStore from "./inspectSend";
import PostPrintStore from "./postPrint/index";

export class Store {
	/**
	 * 初始化可观察数据，不使用该方法，视图不能更新
	 */
	constructor() {
		makeAutoObservable(this);
	}

	abnormalAddressCount: number = 0;

	setAbnormalAddressCount = (abnormalAddressCount: number) => {
		this.abnormalAddressCount = abnormalAddressCount;
	}

	@computed get isMergePrint() {
		return this.setting?.groupPrintSetJsonString?.openMergePrint === 2;
	}

	/** !SECTION
	 *
	 * 打印中心初始化状态
	 */
	printCenterInitStatus: boolean = false

	setPrintCenterInitStatus = (isInit: boolean) => {
		this.printCenterInitStatus = isInit;
	}

	/** !SECTION
	 *
	 * lodop安装状态
	 */
	lodopInstallStatus: boolean = true

	setLodopInstallStatus = (isInstall: boolean) => {
		this.lodopInstallStatus = isInstall;
	}

	/**
	 * 高级设置
	 */
	setting: IPrintSet = {};


	setSetting = (setting: IPrintSet) => {
		events.emit('updatePrintModel', setting, this.setting);
		this.setting = setting;
		if (!window.erpData) window.erpData = {};
		window.erpData.advancedSetting = setting;
		const { setFilterOrderSetting, filterOrderSetting } = tradeSetStore;
		if (setting.filterOrder === 2 && !filterOrderSetting) {
			avoidRepeatReq(ItemFilterSettingSelectApi, {}).then(res => {
				runInAction(() => {
					setFilterOrderSetting(res);
				});
			});
		}
	};

	/**
	 * 手写单号
	 */
	selectedHandTemp = {};

	setSelectedHandTemp = (temp) => {
		this.selectedHandTemp = temp;
	};

	/**
	 * 选择的快递模板
	 */
	selectedTemp: IKddTemp = {};

	setSelectedTemp = (temp: IKddTemp) => {
		this.selectedTemp = temp;
	};

	/**
	 * 选择的发货单模板
	 */
	selectedFhdTemp: IKddTemp = {};

	setSelectedFhdTemp = (temp: IKddTemp) => {
		this.selectedFhdTemp = temp;
	};
	/**
	 * 当前用户模板组是否为空
	 */

	showAddGroup: boolean = false;

	setShowAddGroup = (flag: boolean) => {
		this.showAddGroup = flag;
	};

	/**
	* 选择的快递模板组
	*/

	selectedTempGroup: IKddTempGroup = {};

	setSelectedTempGroup = (temp: IKddTempGroup) => {
		this.selectedTempGroup = temp;
	};

	/**
	 * 选择的小标签模板
	 */

	selectedBxdXbqTemp: IBhdXbqTemp = {};

	setSelectedBxdXbqTemp = (temp: IBhdXbqTemp) => {
		this.selectedBxdXbqTemp = temp;
	};
	/**
	 * 选择的直播小标签模板
	 */

	selectedZbdXbqTemp: IBhdXbqTemp = {};

	setSelectedZbdXbqTemp = (temp: IBhdXbqTemp) => {
		this.selectedZbdXbqTemp = temp;
	};
	/**
	 * 选择的退货单小标签模板
	 */

	selectedthdXbqTemp: IBhdXbqTemp = {};

	setSelectedthdXbqTemp = (temp: IBhdXbqTemp) => {
		this.selectedthdXbqTemp = temp;
	};

	// 单独保存一份快递模板列表，因为运费模板页面选择的模板列表不受“聚合打印”开关的影响

	expressTemplateList: IKddTemp[] = [];

	setExpressTemplateList = (tempList: IKddTemp[]) => {
		this.expressTemplateList = tempList.map(item => {
			if (!item.Mode_ListShowId) {
				item.Mode_ListShowId = item.Exid;
			}
			return item;
		});
	};

	/**
	 * 快递单模板 修改以前的方式，由打印中心传入
	 */

	kddTempList: IKddTemp[] = [];

	setKddTempList = (tempList: IKddTemp[]) => {
		foreignManage.setKddTempList(tempList);
		this.kddTempList = tempList.map(item => {
			if (!item.Mode_ListShowId) {
				item.Mode_ListShowId = item.Exid;
			}
			return item;
		});
	};

	getKddTempList = () => {
		return this.kddTempList;
	}

	/**
	 * 发货单模板 修改以前的方式，由打印中心传入
	 */

	fhdTempList: IKddTemp[] = [];

	setFhdTempList = (tempList: IKddTemp[]) => {
		this.fhdTempList = tempList;
	};

	/**
	 * 小标签模板 修改以前的方式，由打印中心传入
	 */

	bhdXbqTempList: IKddTemp[] = [];

	setBhdXbqTempList = (tempList: IKddTemp[]) => {
		this.bhdXbqTempList = tempList;
	};

	getBhdXbqTempList = () => {
		return this.bhdXbqTempList;
	}
	/**
	 * 直播小标签模板 修改以前的方式，由打印中心传入
	 */

	zbdXbqTempList: IKddTemp[] = [];

	setZbdXbqTempList = (tempList: IKddTemp[]) => {
		this.zbdXbqTempList = tempList;
	};

	getZbdXbqTempList = () => {
		return this.zbdXbqTempList;
	}
	/**
	 * 退货单小标签模板 修改以前的方式，由打印中心传入
	 */

	thdXbqTempList: IKddTemp[] = [];

	setThdXbqTempList = (tempList: IKddTemp[]) => {
		this.thdXbqTempList = tempList;
	};

	getThdXbqTempList = () => {
		return this.thdXbqTempList;
	}

	/**
	 * 吊牌小标签模板 修改以前的方式，由打印中心传入
	 */

	dpTempList: IKddTemp[] = [];

	setDpTempList = (tempList: IKddTemp[]) => {
		this.dpTempList = tempList;
	};

	getDpTempList = () => {
		return this.dpTempList;
	}

	/**
	 * 发件人信息
	 */
	fjrInfoMap: any = {
		kdd: []
	};

	// 爆款标签列配置
	hotGoodsColumnConfig: IColumn['userConfig'];

	setHotGoodsColumnConfig = (val) => {
		this.hotGoodsColumnConfig = val;
	}

	// 列配置
	columnConfig: IColumn = {
		userConfig: [],
		default: [],
		id: '',
	}

	setUserColumnConfig = (columnConfig: any) => {
		this.columnConfig.userConfig = columnConfig;
	}

	setColumnConfig = (data: IColumn) => {
		data?.userConfig.forEach((item, index) => {
			if (!item.width && data.default[index]?.width) {
				item.width = data?.default[index].width;
			}
		});
		// 测试代码test111
		// data.userConfig = data.userConfig.filter((item) => item.key !== 'tradeIndexPrintContent');
		// data.userConfig.push({
		// 	"index": data.userConfig.length + 1,
		// 	"ischecked": true,
		// 	"isedit": true,
		// 	"key": "tradeIndexPrintContent",
		// 	"name": "发货内容",
		// 	"width": 245
		// });

		this.columnConfig = data;
	}

	setUserColumnConfigWidth = (key: string, width: number) => {
		this.columnConfig.userConfig.find(item => item.key === key).width = width;
	}

	getColWidth = (key: string) => {
		let defaultWidth = defaultColWidth[key];
		let width = (window.location.hash.indexOf('hotGoodsScanPrint') > -1 ? this.hotGoodsColumnConfig : this.columnConfig.userConfig).find(item => item.key === key)?.width || defaultWidth;
		return width < defaultWidth ? defaultWidth : width;
	}

	// 处理异常
	isShowDealAbnormalModal: boolean = false;

	dealAbnormalPackage: IPackage = {}

	setIsShowDealAbnormalModal = (val: boolean) => {
		this.isShowDealAbnormalModal = val;
	}

	setDealAbnormalPackage = (val: IPackage) => {
		this.dealAbnormalPackage = val;
	}

	isShowBatchSysMemoModal: boolean = false;

	isShowBatchSysMemoPicModal: boolean = false;

	modifySysMemoPackage: IPackage[] = [];

	modifySysMemoPicPackage: IPackage = {};

	setIsShowBatchSysMemoModal = (val: boolean) => {
		if (!val) {
			this.modifyMemoPackage = [];
		}
		this.isShowBatchSysMemoModal = val;
	}

	setIsShowBatchSysMemoPicModal = (val: boolean) => {
		if (!val) {
			this.modifySysMemoPicPackage = [];
		}
		this.isShowBatchSysMemoPicModal = val;
	}

	setModifySysMemoPackage = (val: IPackage[]) => {
		this.modifySysMemoPackage = val;
	}

	setModifySysMemoPicPackage = (val: IPackage) => {
		this.modifySysMemoPicPackage = val;
	}

	// * 批量修改备注
	isShowBatchModifyMemoModal: boolean = false;

	modifyMemoPackage: IPackage[] | IScanningLabelInfo[] = [];

	setIsShowBatchModifyMemoModal = (val: boolean) => {
		if (!val) {
			this.modifyMemoPackage = [];
		}
		this.isShowBatchModifyMemoModal = val;
	}

	setModifyMemoPackage = (val: IPackage[] | IScanningLabelInfo[]) => {
		this.modifyMemoPackage = val;
	}

	// * 批量指定供应商发货
	isShowBatchSupplierSendModal: boolean = false;

	setIsBatchSupplierSendModal = (val: boolean) => {
		if (!val) {
			this.batchSupplierSendPackage = [];
		}
		this.isShowBatchSupplierSendModal = val;
	}

	batchSupplierSendPackage: IPackage[] = [];

	setBatchSupplierSendPackage = (val: IPackage[]) => {
		this.batchSupplierSendPackage = val;
	}

	// * 批量复制
	isShowBatchCopyTradeInfoModal: boolean = false;

	setIsBatchCopyTradeInfoModal = (val: boolean) => {
		if (!val) {
			this.batchSupplierSendPackage = [];
		}
		this.isShowBatchCopyTradeInfoModal = val;
	}

	batchCopyTradeInfoPackage: IPackage[] = [];

	setBatchCopyTradeInfoPackage = (val: IPackage[]) => {
		this.batchCopyTradeInfoPackage = val;
	}

	// * 批量修改本地标记
	isShowBatchModifyFlagModal: boolean = false;

	setIsShowBatchModifyFlagModal = (val: boolean) => {
		if (!val) {
			this.modifyFlagPackage = [];
		}
		this.isShowBatchModifyFlagModal = val;
	}

	modifyFlagPackage: IPackage[] = [];

	setModifyFlagPackage = (val: IPackage[]) => {
		this.modifyFlagPackage = val;
	}

	defaultProductSetting: IProductSetting = {
		showColumn: 1,
		showGoodsSelect: false,
		showItemTitle: 0,
		showOuterId: 0,
		showPartConsignItems: 0,
		showPicture: 1,
		showShortTitle: 0,
		showSkuAlias: 0,
		showSkuOuterId: 0,
		showSkuTitle: 1,
		showStockWarn: 0,
		showSysOuterId: 0,
		showSysOuterSkuId: 0,
		showSysPicPath: 0
	}

	// * 产品内容设置
	productSetting: IProductSetting = this.defaultProductSetting;

	setProductSetting = (val: IProductSetting) => {
		if (val.customSetJson) {
			val.showStockAllocation = val.customSetJson.showGoodsStockBox;
			val.showSysGoodsStockBox = val.customSetJson.showSysGoodsStockBox;

		}
		this.productSetting = val;
	}

	// * 获取产品内容设置
	getProductSetting = async() => {
		try {
			if (!this.productSetting || !this.productSetting?.userId) {
				const res = await TradeUserGetUserCustomSetApi({});
				console.log('%c [ 产品内容设置 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', res);
				this.setProductSetting(res);
				return res;
			} else {
				return this.productSetting;
			}
		} catch (error) {
			return this.defaultProductSetting;
		}
	}

	productSettingVisible :boolean = false;

	setProductSettingVisible = (val: boolean) => {
		this.productSettingVisible = val;
	}

	// * 批量导出
	isShowBatchExportModal: boolean = false;

	setIsShowBatchExportModal = (val: boolean) => {
		this.isShowBatchExportModal = val;
	}

	// * 订单操作日志
	isShowTradeOptLogModal: boolean = false;

	setIsShowTradeOptLogModal = (val: boolean) => {
		this.isShowTradeOptLogModal = val;
	}

	/** 操作按钮自定义 */
	optBtnCustomVisible: boolean = false;

	setOptBtnCustomVisible = (val: boolean) => {
		this.optBtnCustomVisible = val;
	}

	tradeOptLogPack: IPackage = null;

	setTradeOptLogPack = (val: IPackage) => {
		this.tradeOptLogPack = val;
	}

	auxiliaryLine = {
		show: false,
		x: 0,
	}

	setAuxiliaryLine = (obj: {
		show: boolean,
		x?: number,
	}) => {
		this.auxiliaryLine = {
			...this.auxiliaryLine,
			...obj,
		};
	}

	fixedTemplateComHeight: number = 0;

	setFixedTemplateComHeight = (height: number) => {
		this.fixedTemplateComHeight = height;
	}

	expressCompanyList: PrintCenterExCompanyGetExCompanyAllResponse['data'] = [];

	setExpressCompanyList = (companyList: PrintCenterExCompanyGetExCompanyAllResponse['data']) => {
		this.expressCompanyList = companyList;
	}

	memoUpdating: boolean = false;

	setMemoUpdating = (val: boolean) => {
		this.memoUpdating = val;
	}

	smartExpressSwitch: boolean = false;

	setSmartExpressSwitch = (val: boolean) => {
		this.smartExpressSwitch = val;
	}

	smartExpressList: any[] = [];

	setSmartExpressList = (val) => {
		this.smartExpressList = val;
	}

	getSmartExpressSwitch = async() => {
		let res = await ItemSmartExpressGetListApi({});
		// 智选快递的开关
		if (res?.smartExpressConfigVO) {
			this.setSmartExpressSwitch(res.smartExpressConfigVO.openStatus);
		}
		if (res?.smartExpressTemplateVOList) {
			this.setSmartExpressList(res.smartExpressTemplateVOList);
		}
	}

	importExpress: any = {};

	setImportExpress = (val: any) => {
		this.importExpress = val;
	}

	storeSearchParams: Partial<TradeQueryTradeRequest> = null;

	setStoreSearchParams = (val: Partial<TradeQueryTradeRequest>) => {
		this.storeSearchParams = val;
	}

	oldSearchParamsMd5 = ''

	searchParamsMd5 = ''

	setSearchParamsMd5 = (val: Partial<TradeQueryTradeRequest>) => {
		const { sysSkuId, sysItemId, skuId, itemId, ...rest } = val;
		this.oldSearchParamsMd5 = this.searchParamsMd5;
		this.searchParamsMd5 = getMd5Str(JSON.stringify(rest));
	}

	setOldSearchParamsMd5 = (md5Str:string) => {
		this.oldSearchParamsMd5 = md5Str;
	}

	getNewParamsMd5 = (val: Partial<TradeQueryTradeRequest>) => {
		const { sysSkuId, sysItemId, skuId, itemId, ...rest } = val;
		return getMd5Str(JSON.stringify(rest));
	}

	tradeCanReachObj: Obj = null;

	setTradeCanReachObj = (val: Obj) => {
		this.tradeCanReachObj = val;
	}

	// 异常预警的设置值
	exceptionWarnSet: IExceptionWarnSet = {}

	setExceptionWarnSet = (val: IExceptionWarnSet) => {
		this.exceptionWarnSet = val;
	}

	tradeAdvanceSetObj: Obj = {};

	setTradeAdvanceSetObj = (val: Obj) => {
		this.tradeAdvanceSetObj = val;
	}

	selectPrinterAndTemplateModalVisible: boolean = false;

	setSelectPrinterAndTemplateModalVisible = (val: boolean) => {
		this.selectPrinterAndTemplateModalVisible = val;
	}

	taskProgressDetailList: IndexSettingGetUserHandleTaskProgressResponse['data']['progressDetailList'] = []

	setTaskProgressDetailList = (val) => {
		this.taskProgressDetailList = val;
	}


	isShowEditSidModal: boolean = false;

	setIsShowEditSidModal = (val: boolean) => {
		this.isShowEditSidModal = val;
	}

	editSidModalProps: Obj = {};

	setEditSidModalProps = (val: Obj) => {
		this.editSidModalProps = val;
	}

	/**
	 * 显示店铺名称还是简称
	 */
	isShowShopName: boolean = localStorage.getItem('isShowShopName') !== 'false';

	setIsShowShopName = (val: boolean) => {
		this.isShowShopName = !!val;
		localStorage.setItem('isShowShopName', !!val);
	}

	// 备注排序
	sellerMemoSort: number = 排序.不排序;

	setSellerMemoSort = (num) => {
		this.sellerMemoSort = num;
	}

	/** 剩余发货时间 */
	residueSendTimeRes: TradeDictQueryDictResponse['data'] = {};

	setResidueSendTimeRes(val: TradeDictQueryDictResponse['data']) {
		this.residueSendTimeRes = val;
	}

	// * 查询前是否同步订单
	syncBeforeSearchObj = {
		switch: false,
		desc: '',
	};

	setSyncBeforeSearchObj = (obj: typeof this.syncBeforeSearchObj) => {
		console.log('setSyncBeforeSearchObj', obj);
		this.syncBeforeSearchObj = obj;

	}

	/** 控件下载地址 */
	controlDonloadData = [];

	setControlDonloadData(data) {
		this.controlDonloadData = data;
	}

	flagList: TradeSearchFlagRule[];

	setFlagList = (val: typeof this.flagList) => {
		this.flagList = val;
	}

	markList: TradeUserMarkLabelItem[];

	setMarkList = (val: typeof this.markList) => {
		this.markList = val;
	}

	warningPrintCount :any = {
		summaryNum: '',
		getYdAndNoPrintNum: '',
		printedAndUnShopNum: '',
	};

	setWarningPrintCount = (val: Object) => {
		this.warningPrintCount = val;
	}

	warningCountForShipFail: string = '';

	setWarningCountForShipFail = (val: string) => {
		this.warningCountForShipFail = val;
	}

	tradeCount:number = 0;

	timeOutTradeCount:number = 0;

	waitSendTradeCount:number = 0;

	setTimeOutTradeCount = (val: number) => {
		this.timeOutTradeCount = val;
	}

	setTradeCount = (val: number) => {
		this.tradeCount = val;
	}

	setWaitSendTradeCount = (val: number) => {
		this.waitSendTradeCount = val;
	}

	shopTradeCount: Array<[]> = [] as any;

	shopTradeCountMap: Object = {} as any;

	setShopTradeCount = (val: Array<[]>) => {
		const shopTradeCount = val?.map(item => {
			return {
				...item,
				sellerNick: item.sellerNick || "无店铺"
			};
		});
		this.shopTradeCount = shopTradeCount;
		const shopTradeCountMap:any = {};
		this.shopTradeCount?.forEach(item => {
			shopTradeCountMap[item.sellerId] = item;
		});
		this.shopTradeCountMap = shopTradeCountMap;
	}

	tradeOrderSortType: string = "";

	setTradeOrderSortType = (val: string) => {
		console.log(val);
		this.tradeOrderSortType = val;
	}

	tradeOrderSortTypeList = [];

	setTradeOrderSortTypeList = (val) => {
		console.log(val);
		this.tradeOrderSortTypeList = val;
	}

	// 添加一个新字段来控制是否全部展开
	isAllExpanded: boolean = true;

	// 添加对应的setter方法
	setIsAllExpanded = (val: boolean) => {
		this.isAllExpanded = val;
	}

	batchScanVideoMonitorState = false;
	
	setBatchScanVideoMonitorState = (val: boolean) => {
		this.batchScanVideoMonitorState = val;
	}

	batchScanselectedShootingDevices = [];

	setbatchScanSelectedShootingDevices = (v) => {
		this.batchScanselectedShootingDevices = v;
	}

	batchScannvrDevices = [];

	setbatchScanNvrDevices = (v) => {
		this.batchScannvrDevices = v;
	}

	// * 通过下面的形式 避免大量的store写在同一个文件中
	tradeListStore = new TradeListStore(this);

	listFilterStore = new ListFilterStore(this);

	tradeOptStore = new TradeOptStore(this);

	tradePrintStore = new TradePrintStore(this);

	goodsTagListStore = new GoodsTagListStore(this);

	liveTagListStore = new LiveTagListStore(this);

	refundLabelScanPrintStore = new RefundLabelScanPrintStore(this);

	postPrintStore = new PostPrintStore(this);

	InspectSendStore = new InspectSendStore(this)

	scanPrintStore = scanPrintStore;
}

export type omitProps = Omit<Partial<Store>, 'InspectSendStore' | 'refundLabelScanPrintStore' | 'listFilterStore' | 'tradeListStore' | 'tradeOptStore' | 'tradePrintStore' | 'goodsTagListStore' | 'liveTagListStore' | 'postPrintStore'>;
const store = new Store();

export default store;
