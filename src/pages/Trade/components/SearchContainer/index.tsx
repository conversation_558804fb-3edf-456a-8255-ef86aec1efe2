/* eslint-disable react-hooks/exhaustive-deps */
import React, { Fragment, useCallback, useEffect, useState, useRef, useMemo } from 'react';
import cs from 'classnames';
import { Button, Select, Form, Input, Checkbox, Popover } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useRequest, useToggle } from 'ahooks';
import { observer } from 'mobx-react';
import useGetState from '@/utils/hooks/useGetState';
import s from './index.module.scss';
import {
	GOOD_STOCK_STATUS_OPTIONS,
	LOGISTICS_TYPE_OPTIONS,
	MAX_VALUE,
	MIN_VALUE,
	ORDER_PRINT_STATUS,
	ORDER_STATUS_OPTIONS,
	SELL_ATTR_OPTIONS, TIME_TYPE_OPTIONS, TRADE_LABEL_OPTIONS, TRADE_EXCEPTION_OPTIONS,
	defaultFormatCondition,
	ConditionIdEnum
} from './constants';
import {
	getGoodsInfoSelectOptions,
	getPlaceholderTips,
	handleGoodsInfoSearchParams,
	itemInfoEnumValue
} from './itemInfoUtils';
import message from "@/components/message";

import {
	SearchPrintStatus,
	SearchRefundStatus,
	SearchSellAttr,
	SearchGoodStockStatus,
	SearchTimeType,
	SearchTradeStatus,
	SearchTradeType,
	checkValueIsEmpty,
	OrderSourceType,
	NewSearchPrintStatus,
	getValueByOldStatus,
	SearchBatchNo
} from './SearchCondition';
import AddressSelect from './AddressSelect/addressSelect';
import OrderMarkSelect from './OrderMarkSelect';
// import KdzsDateRangePicker from '@/components/DateRangeComp/kdzsRangePicker';
import { SysMemoModeEnum, TradeQueryGatherParams, TradeQueryTradeRequest, TradeSearchConditionConfig } from '@/types/trade/search/search';
import KdzsDateRangePicker1 from '@/components/DateRangeComp/kdzsRangePicker1';
import FlagAndMemoSelect from './FlagAndMemoSelect/flagAndMemoSelect';
// import SmartExpressTemplateSelect from './SmartExpressTemplateSelect';
import InputMulti from '@/components/Input/InputMulti';
import ShopMultiSelect from '@/components-biz/ShopListSelect/shopMultiSelect';
import SearchRuleSort, { FromPageEnum } from './SearchRuleSort';
import memoFn from '@/libs/memorizeFn';
import { getMultiShops } from '@/components-biz/ShopListSelect/shopListUtils';
import Icon from '@/components/Icon';
import RangeSelect from './RangeSelect';
import { DatePickerKey, getCacheDateRange } from '@/components/DateRangeComp/kdzsRangePickerUtil';
import sendPoint from '@/utils/pointTrack/sendPoint';
import Pointer from '@/utils/pointTrack/constants';
import userStore from '@/stores/user';
import { tradeStore } from '@/stores';
import event from '@/libs/event';
import { getUrlData, isBeyondInput } from '../../utils';
import history from '@/utils/history';
import { flagGroup } from "@/constants";
import RemainingDeliveryTime from '@/components-biz/RemainingDeliveryTime';
import TopPrint from '@/components-biz/TopPrint';
import TopPrintSimple from '@/components-biz/TopPrint/index.simple';
import { platform } from '@/types/schemas/common';
import { TopPrintSearchItemTopMode, TopPrintSearchModel, TopPrintSearchPerspective } from '@/pages/Trade/constants';
import { autoMergeConfig, orderPreOccupiedStock } from '@/pages/Index/Settings/System/constants';
import InputArrayMulti from '@/components/Input/InputArrayMulti';
import InputSelect from '@/components/Input/InputSelect';
import { DistributorSelect } from '@/components-biz/Distribution';
import LowVersionControlModal, { lowVersionLock } from '@/components-biz/LowVersionControlModal';
import { PageNameControlEnum } from '@/components-biz/LowVersionControlModal/constant';
import PresetQueryNavBar from './PresetQueryNavBar';
import { getIncludeIngColorOrSize, getIsColorAndSizePrecise, gramsToKilograms, includeIngColorOrSizeEnum, kilogramsToGrams, adjustTimeRange } from './utils';
import FlagSelect from './FlagAndMemoSelect/flagSelect';
import { FunctionPermissionEnum } from '@/utils/permissionCheck/functionPermissionCheck';
import { local } from '@/libs/db';
import { StockStorageGetApi } from '@/apis/warehouse/entrepot';

export interface ISearchContainerProps {
	handleSearch: (params: TradeQueryTradeRequest) => void
	isSearching: boolean;
	initParam?: {
		platform: platform;
		sellerId: string;
		type: string;
	};
	locationSearch?:string; // 用于打开新标签页 携带参数
}

const PreciseSearchTip = () => {
	return (
		<div style={ { width: '320px', fontSize: '12px' } }>
			<p>以搜索宝贝名称为例，如搜索“连衣裙”：</p>
			<p>未开启精确查询：宝贝名称包含连衣裙的，如：中年连衣裙，夏天连衣裙，查询结果都会展示。</p>
			<p>开启了精确查询：宝贝名称完全为“连衣裙”，查询结果才会展示。</p>
		</div>
	);
};

const getTradeInitSearchTime = () => {
	return getCacheDateRange(DatePickerKey.trade_search) || [dayjs().subtract(1, 'M').startOf('day'), dayjs().endOf('day')];
};

export const searchInitValue : Partial<TradeQueryGatherParams> = {
	platformInfo: {
		plats: [],
		plat_sellerIds: [],
	},
	timeType: TIME_TYPE_OPTIONS.default,
	searchTime: getTradeInitSearchTime(),
	status: ORDER_STATUS_OPTIONS.default,
	printStatus: ORDER_PRINT_STATUS.default,
	flagValue: '',
	buyerMessage: '',
	sellerMemo: '',
	goodsIncludeStatus: '1',
	goodsNotIncludeStatus: '0',
	sysMemoMode: SysMemoModeEnum.线下备注,
	includeIngColorOrSizeTwo: local.get('includeIngColorOrSizeTwo') || includeIngColorOrSizeEnum['颜色尺码包含(模糊)'],
	sellAttributeList: SELL_ATTR_OPTIONS.default,
	goodStockStatus: GOOD_STOCK_STATUS_OPTIONS.default,
	logisticsType: LOGISTICS_TYPE_OPTIONS.default,
	bizMarkObj: {
		value: '',
		bizMark: '',
	},
	precise: [],
	customizeResidueSendTime: '',
	selectTimeType: 1,
	storageIdList: [],
	// smartExpressTemplateId: undefined,
};

export const getRange = (range: number[] = []) => {
	return `${range[0] || MIN_VALUE}-${range[1] || MAX_VALUE}`;
};

export const getRangeKg = (range: number[] = [], isShowKg) => {
	if (isShowKg) return `${((range[0] || MIN_VALUE) * 1000)}-${(((range[1] || 0) * 1000 || MAX_VALUE))}`;
	return `${range[0] || MIN_VALUE}-${range[1] || MAX_VALUE}`;
};

export const isRange = (value:number[], range?:number[],) : boolean => {
	if (value?.length) {
		const minVal = Number(value[0]);
		const maxVal = Number(value[1]);
		if (value[0]) {
			if (isNaN(minVal)) {
				return false;
			}
		}
		if (value[1]) {
			if (isNaN(maxVal)) {
				return false;
			}
		}

		if (range) {
			if (
				(value[0] && (minVal < range[0] || minVal > range[1]))
				|| (value[1] && (maxVal < range[0] || maxVal > range[1]))
			) {
				return false;
			}
		}
	}
	return true;
};

const verifySearchParams = (params: Partial<TradeQueryGatherParams>, isShowKg:boolean) : boolean => {
	const { paymentRange, orderRange, goodsTotalNumRange, goodsTypeNumRange } = params.range || {};
	const { weightRange } = params;
	const maxInputNum = 200;
	// 单独校验kg重量数值范围isShowKg
	console.log('verifySearchParams111', isShowKg, params, weightRange, isRange(weightRange, [1, 99999]));
	if (isShowKg && !isRange(weightRange, [0.000001, 99])) {
		message.error('订单重量(单位Kg)输入超过限制，请调整数值在0.000001-99之间');
		return false;
	}
	if (!isShowKg && !isRange(weightRange, [0.001, 99999])) {
		message.error('订单重量(单位g)输入超过限制，请调整数值在0.001-99999之间');
		return false;
	}
	if (isBeyondInput(params?.buyerMessage, 50)) {
		message.error('单次查询最多筛选50个留言，请重新输入');
		return false;
	}
	if (params.buyerMessage && params.buyerMessage.length > 1000) {
		message.error('留言最多输入1000个字数，请重新输入');
		return false;
	}
	if (isBeyondInput(params?.sellerMemo, 50)) {
		message.error('单次查询最多筛选50个备注，请重新输入');
		return false;
	}
	if (params.sellerMemo && params.sellerMemo.length > 1000) {
		message.error('备注最多输入1000个字数，请重新输入');
		return false;
	}
	if (!isRange(orderRange, [1, 99999])) {
		message.error('订单数量输入超过限制，请调整数值在1-99999之间');
		return false;
	}
	if (!isRange(goodsTypeNumRange, [1, 99999])) {
		message.error('商品种类输入超过限制，请调整数值在1-99999之间');
		return false;
	}
	if (!isRange(paymentRange, [0])) {
		message.error('订单金额输入不正确，订单金额只能输入大于0的数值');
		return false;
	}
	if (isBeyondInput(params.buyerNick, maxInputNum)) {
		message.error(`同时查询买家昵称的数量不能超过${maxInputNum}个`);
		return false;
	}
	if (isBeyondInput(params.mobile, maxInputNum)) {
		message.error(`同时查询手机号的数量不能超过${maxInputNum}个`);
		return false;
	}
	if (isBeyondInput(params.receiveName, maxInputNum)) {
		message.error(`同时查询收件人的数量不能超过${maxInputNum}个`);
		return false;
	}
	let goodsInfoIncludeNum = params.goodsIncludeStatus == itemInfoEnumValue.商品包含 ? [500, 500] : [500, 500];
	let goodsInfoNotIncludeNum = params.goodsNotIncludeStatus == itemInfoEnumValue.商品不包含 ? [500, 500] : [500, 500];
	const placeholderTips = getPlaceholderTips();
	if (params.shortNameIncludingList?.length > goodsInfoIncludeNum?.[0]) {
		message.error(`同时查询 ${placeholderTips[params.goodsIncludeStatus]?.[0]} 的数量不能超过${goodsInfoIncludeNum?.[0]}个`);
		return false;
	}
	if (params.skuIncludingList?.length > goodsInfoIncludeNum?.[1]) {
		message.error(`同时查询 ${placeholderTips[params.goodsIncludeStatus]?.[1]} 的数量不能超过${goodsInfoIncludeNum?.[1]}个`);
		return false;
	}
	if (params.shortNameNotIncludingList?.length > goodsInfoNotIncludeNum?.[0]) {
		message.error(`同时查询 ${placeholderTips[params.goodsNotIncludeStatus]?.[0]} 的数量不能超过${goodsInfoNotIncludeNum?.[0]}个`);
		return false;
	}
	if (params.skuNotIncludingList?.length > goodsInfoNotIncludeNum?.[1]) {
		message.error(`同时查询 ${placeholderTips[params.goodsNotIncludeStatus]?.[1]} 的数量不能超过${goodsInfoNotIncludeNum?.[1]}个`);
		return false;
	}
	if (isBeyondInput(params.sid, 1000)) {
		message.error(`同时查询快递单号的数量不能超过${1000}个`);
	}
	if (isBeyondInput(params.tid, 1000)) {
		message.error(`同时查询订单号的数量不能超过${1000}个`);
		return false;
	}
	if (isBeyondInput(params.labelIdList, 1000)) {
		message.error(`同时查询唯一码的数量不能超过${1000}个`);
		return false;
	}
	return true;
};

// 订单打印搜索栏
const SearchContainer = (props: ISearchContainerProps) => {
	const [form] = Form.useForm();
	const topPrintComp = useRef(null);
	const topPrintComp2 = useRef(null);
	const { handleSearch, isSearching, initParam, locationSearch } = props;
	const [searchSortVisible, setSearchSortVisible] = useState<boolean>(false);
	const [showAll, { toggle: toggleShowAll, set: setToggleShowAll }] = useToggle();
	const [conditionSet, setConditionSet] = useState<TradeSearchConditionConfig[]>([]);
	const [isCanGoodStock, setIsCanGoodStock] = useState<boolean>(false);
	const [formData, setFormData] = useState<TradeQueryGatherParams>({});
	const [topItemData, setTopItemData, getTopItemData] = useGetState({});
	const [checkVersionControl, setCheckVersionControl] = useState(false);
	const [hasTopItemValue, setHasTopItemValue, getHasTopItemValue] = useGetState(false);
	const [showTopInfo, setShowTopInfo] = useState(true);
	const [storageInfoList, setStorageInfoList] = useState([]);
	const isSearchRef = useRef(null);
	const containerRef = useRef(null);
	const lastItemRef = useRef(null);
	const distributorSelectRef = useRef(null);
	const [ruleContent, setRuleContent] = useState<any>({ selectTimeType: 1 });


	const {
		setImportExpress,
		setSelectedHandTemp,
		selectedTemp,
		setSelectedTemp,
		selectedTempGroup,
		setSelectedTempGroup,
		isMergePrint
	} = tradeStore;
	const {
		userInfo: {
			userId,
			version,
			whiteListSetting,
		},
		hasFunctionPermission,
		isShowKg,
		isSupplierAccount, isDistributorAccount, isFreeSupplierAccount
	} = userStore;

	useEffect(() => {
		try {
			let res = JSON.parse(whiteListSetting);
			setShowTopInfo(res?.itemTopClose != 1);
		} catch (error) {
			console.error('解析报错', error);
		}
	}, [whiteListSetting]);

	useEffect(() => {
		if (locationSearch) {
			// 打印记录跳转 搜索订单号
			let res = getUrlData('', 'tid');
			handleSearchParams({ tid: res?.tid, status: 'ALL_STATUS' });
		}
	}, [locationSearch]);

	useEffect(() => {
		StockStorageGetApi({ checkStorageAuthorityFlag: true }).then((res) => {
			if (res && res.length > 0) {
				setStorageInfoList(res);
			}
		});
	}, []);


	// 清除选中模板
	const clearSelectTemp = useCallback(() => {
		const kddTempInfo = isMergePrint ? {
			localDefaultId: localStorage.getItem('defaultChoosedKddTempGroup' + userId),
			selectTemp: selectedTempGroup,
			set: setSelectedTempGroup
		} : {
			localDefaultId: localStorage.getItem('defaultChoosedKddTemp' + userId),
			selectTemp: selectedTemp,
			set: setSelectedTemp
		};
		// 设置了默认模板不做相关操作
		if (!!kddTempInfo.localDefaultId || (!kddTempInfo.selectTemp?.Exid && !kddTempInfo.selectTemp?.id)) return;
		kddTempInfo.set({});
	}, [selectedTemp, selectedTempGroup, isMergePrint, userId]);

	// 如果不填传店铺值
	const getTradeMultiShops = async(platformInfo) => {
		let { plats, plat_sellerIds } = platformInfo || {};
		if (initParam.platform && initParam.sellerId) {
			plats = [initParam.platform];
			plat_sellerIds = [`${initParam.platform}_${initParam.sellerId}`];
		}
		let multiShops = await getMultiShops({ plats, plat_sellerIds });
		return multiShops;
	};

	const getParams = async(v: Partial<TradeQueryGatherParams>) => {
		let flagValue;
		let flagSelValue;
		let sellerFlag;
		if (v.flagValue?.includes('_')) {
			flagSelValue = v.flagValue;
		} else {
			flagValue = v.flagValue;
		}

		flagValue = flagValue === '-1' ? '10' : flagValue;

		if (v.sellerFlag?.length > 1) {
			let curFlag:any[] = [];
			flagGroup.forEach(item => {
				if (v.sellerFlag.includes(item.toSellerFlag)) {
					curFlag.push(item.value);
				}
			});
			flagSelValue = curFlag.toString() + '_1';
		} else {
			sellerFlag = v?.sellerFlag?.toString();
		}

		let multiShops = await getTradeMultiShops(v.platformInfo);

		console.log('v.status', v.status);
		 // 处理特殊状态 WAIT_SEND_GOODS
		 if (v.status === 'WAIT_SEND_GOODS') {
			// 添加 sysStatus 参数
			v.sysStatus = 'WAIT_SEND_GOODS';
			v.status = 'WAIT_SELLER_SEND_GOODS';
		}
		// const range = v.range || {};
		const params: TradeQueryTradeRequest = {
			waveNo: v.waveNo || '', // 添加波次号参数
			isPlatformEmptyQuery: !v.platformInfo?.plat_sellerIds?.length && !v.platformInfo?.plats?.length,
			multiShopS: multiShops || [],
			startTime: v.searchTime ? v.searchTime[0]?.format("YYYY-MM-DD HH:mm:ss") : '',
			endTime: v.searchTime ? v.searchTime[1]?.format("YYYY-MM-DD HH:mm:ss") : '',
			timeType: v.timeType,
			status: v.status,
			sysStatus: v.sysStatus || '',
			allPrintStatus: checkValueIsEmpty(v.allPrintStatus),
			selValue: v.addressInfo?.selValue || '',
			areaJson: v.addressInfo?.areaJson || '',
			areaContain: v.addressInfo?.areaContain,
			sellerFlag,
			flagValue,
			flagSelValue,
			sellerMemo: v.sellerMemo || '',
			buyerMessage: v.buyerMessage || '',
			buyerMessageOrSellerMemo: v.buyerMessageOrSellerMemo || '',
			buyerNick: v.buyerNick || '',
			receiveName: v.receiveName || '',
			mobile: v.mobile || '',
			ptTid: v.ptTid || '',
			tid: v.tid || '',
			sid: v.sid || '',
			labelIdList: v.labelIdList?.replaceAll("，", ",")?.split(',')?.filter(Boolean) || [],
			printStatus: v.printStatus,
			fhdPrintStatus: v.fhdPrintStatus,
			labelPrintStatus: v.labelPrintStatus,
			sellAttributeList: v.sellAttributeList || [],
			goodStockStatus: v.goodStockStatus || '',
			marketIncludingList: v.marketIncludingList?.map(i => i.trim()),
			dangKouIncludingList: v.dangKouIncludingList?.map(i => i.trim()),
			supplierIncludingList: v.supplierIncludingList?.map(i => i.trim()),
			authorIncludingList: v.authorIncludingList?.map(i => i.trim()),
			colorIncludingList: v.colorIncludingList?.map(i => i.trim()),
			sizeIncludingList: v.sizeIncludingList?.map(i => i.trim()),
			includeIngColorOrSize: getIncludeIngColorOrSize(v.includeIngColorOrSizeTwo),
			isColorAndSizePrecise: getIsColorAndSizePrecise(v.includeIngColorOrSizeTwo),
			sysMemoMode: v.sysMemoMode,
			sysMemoList: v.sysMemoList?.map(i => i.trim()),
			refundStatus: v.refundStatus,
			bizMark: v.bizMarkObj?.bizMark || '',
			bizMarkContain: v.bizMarkObj?.bizMarkContain,
			payment: getRange(v.paymentRange),
			weightRange: getRangeKg(v.weightRange, isShowKg), // 根据用户重量isShowKg配置调整参数
			orderRange: getRange(v.orderRange),
			goodsTotalNum: getRange(v.goodsTotalNumRange),
			goodsTypeNum: getRange(v.goodsTypeNumRange),
			isPrecise: Array.isArray(v.precise) ? v.precise?.includes?.('isPrecise') : v.precise,
			isPreciseByTrade: Array.isArray(v.precise) ? v.precise?.includes?.('isPreciseByTrade') : v.precise,
			customizeResidueSendTime: v.customizeResidueSendTime,
			tradeLabelList: v.tradeLabelList,
			tradeExceptionList: v.tradeExceptionList,
			commentsRemarks: '',
			tradeExpressImportLogSequence: v?.tradeExpressImportLogSequence || null,
			orderSource: v?.orderSource,
			tradeTypeTagList: v?.tradeType ? [v.tradeType] : [],
			distributorUserIds: v?.distributorUserIds,
			smartExpressTemplateId: v?.smartExpressTemplateId, // 智选快递
			changeSelectAll: v.changeSelectAll || undefined,
			storageIdList: v?.storageIdList?.map((item) => (Number(item))) || [],
		};
		// 处理商品查询的参数
		handleGoodsInfoSearchParams(v, params);
		return params;
	};

	const handleFinish = useCallback(async(v: Partial<TradeQueryGatherParams>) => {
		clearSelectTemp();
		if (!verifySearchParams(v, isShowKg)) {
			return;
		}
		console.log('xxx333x', v);

		const _params = await getParams(v);
		const params: TradeQueryTradeRequest = {
			..._params,
			...getTopItemData()
			// logisticsType: v.logisticsType || '',
			// pendingStatus: v.pendingStatus
		};
		if (!getHasTopItemValue()) {
			// 如果爆款组件没有值，就取表单中的
			Object.assign(params, {
				sellAttributeList: v.sellAttributeList || ''
			});
		} else { // 如果爆款打单有值，触发埋点逻辑
			params.equalFlag = 1; // 如果爆款有值，参数需要改成“1-包含”
			const { model, perspective } = topPrintComp?.current?.getData();
			if (version === 1) {
				if (perspective === TopPrintSearchPerspective["itemPerspective"]) {
					if (model === TopPrintSearchModel["MODEL_GOOD"]) {
						sendPoint(Pointer.订单_爆款打印_商品视角_按商品_库存版);
					} else if (model === TopPrintSearchModel["MODEL_SKU"]) {
						sendPoint(Pointer.订单_爆款打印_商品视角_按款_库存版);
					}
				} else if (perspective === TopPrintSearchPerspective["sysPerspective"]) {
					if (model === TopPrintSearchModel["MODEL_GOOD"]) {
						sendPoint(Pointer.订单_爆款打印_货品视角_按商品_库存版);
					} else if (model === TopPrintSearchModel["MODEL_SKU"]) {
						sendPoint(Pointer.订单_爆款打印_货品视角_按款_库存版);
					}
				}
			} else if (version === 2) {
				if (model === TopPrintSearchModel["MODEL_GOOD"]) {
					sendPoint(Pointer.订单_爆款打印_商品视角_按商品_零库存版);
				} else if (model === TopPrintSearchModel["MODEL_SKU"]) {
					sendPoint(Pointer.订单_爆款打印_商品视角_按款_零库存版);
				}
			}
		}
		// 如果表单中有销售属性，以表单为准
		if (v.sellAttributeList?.length) {
			params["sellAttributeList"] = v.sellAttributeList;
		}
		if (!v?.tradeExpressImportLogSequence) {
			setImportExpress({});
		}
		setSelectedHandTemp({});
		console.log('params', JSON.parse(JSON.stringify(params)));

		handleSearch && handleSearch(params);
	}, [handleSearch, clearSelectTemp, isShowKg]);

	const handleShowAllCondition = () => {
		toggleShowAll();
	};

	const handleSetSearchRules = async() => {
		await getAdvancedSet(true);
		setSearchSortVisible(true);
	};

	const handleSearchRuleSortOK = (queryConditionDTOList:TradeSearchConditionConfig[]) => {
		setConditionSet(queryConditionDTOList);
		setSearchSortVisible(false);
	};

	const resetForm = () => {
		topPrintComp?.current?.resetValue();
		topPrintComp2?.current?.resetValue();
		form.resetFields();
	};

	const reset = () => {
		resetForm();

		event.emit('PresetQueryNavBar.reset');
	};

	// 初始化爆款查询、搜索栏
	const handlePresetReset = () => {
		resetForm();
	};

	const handleSearchParams = (params?:Partial<TradeQueryGatherParams>) => {
		reset(); // 重置下搜索条件和预设
		searchInitValue.searchTime = getTradeInitSearchTime();
		if (params) {
			// 打印记录 可用于
			form.setFieldsValue(params);
			handleFinish({ ...searchInitValue, ...params });
		} else {
			handleFinish(searchInitValue);
		}
	};

	// 重新查询，需要确保不在请求中
	const handleReSearch = () => {
		form.submit();
	};

	useEffect(() => {
		// 此处只是为了处理初始进入后 没有手动点击查询 searchParams为空时 代码中执行到查询列表 列表返回为空的情况
		event.on('tradeSetting.handleSearchParams', handleSearchParams);
		event.on('tradeSetting.handleReSearch', handleReSearch);

		return () => {
			event.off('tradeSetting.handleSearchParams', handleSearchParams);
			event.off('tradeSetting.handleReSearch', handleReSearch);
		};
	}, []);

	useEffect(() => {
		lowVersionLock(PageNameControlEnum.小标签半备货模式);
	}, []);

	// 获取搜索条件设置
	const getAdvancedSet = async(refresh = false) => {
		let advancedRes;
		try {
			advancedRes = await memoFn.getAdvancedSet(refresh);
		} catch (error) {
			console.log(error);
		}
		const newList = defaultFormatCondition(advancedRes);
		console.log('%c [ newList ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', newList);
		setConditionSet(newList as TradeSearchConditionConfig[]);
	};

	useEffect(() => {
		getAdvancedSet();
	}, [userStore.hasWaveManagePermission]);

	useEffect(() => {
		searchInitValue.searchTime = getTradeInitSearchTime();
		userStore.getUserSetting().then(res => {
			// 带参数跳转 可不必自动查询
			const { location } = history;
			if (res?.autoSearch && !location.search) {
				handleFinish(searchInitValue);
			}
		});
	}, [handleSearch]);

	useEffect(() => {
		userStore.getSystemSetting().then(res => {
			setIsCanGoodStock(res.inventoryDeduct == 1 && [orderPreOccupiedStock.下单占用库存, orderPreOccupiedStock.付款占用库存].includes(res.orderPreOccupiedStock));
		});
	}, []);

	// 处理高级查询信息回填
	const handlePreConditionInfo = (data:any, isSubmit = true) => {
		if (data?.distributorUserIds) {
			setTimeout(() => {
				distributorSelectRef.current.resetStatus();
			}, 0);
		}
		let _formData = form.getFieldsValue();
		setToggleShowAll(true);
		resetForm();
		setTimeout(() => {
			if (data.endTime) {
				_formData.searchTime[1] = data.endTime;
			} else if (data.payEndTime) {
				const result = adjustTimeRange(new Date(), _formData.searchTime[0], _formData.searchTime[1], data.payEndTime);
				_formData.timeType = "2";
				if (result?.startTime && result?.endTime) {
					_formData.searchTime[0] = dayjs(result.startTime);
					_formData.searchTime[1] = dayjs(result.endTime);
				}
			}
			setFormData({
				...data,
				flagValue: data.flagValue,
				goodsIncludeStatus: data.goodsIncludeStatus,
				goodsNotIncludeStatus: data.goodsNotIncludeStatus,
			} as any);
			
			form.setFieldsValue({
				...data,
				searchTime: _formData.searchTime,
				timeType: _formData.timeType,
			});
			if (isSubmit) {
				setTimeout(() => {
					form.submit();
				}, 200);
			}
		}, 100);
	};

	const onFormValueChange = (changedValues:{}, allValues:TradeQueryGatherParams) => {
		// 如果留言备注更改了，清空备注或留言的输入框内容
		if ('flagValue' in changedValues) {
			form.setFieldsValue({
				buyerMessage: '',
				sellerMemo: ''
			});
		}
		if ('allPrintStatus' in changedValues) {
			form.setFieldsValue({ ...allValues.allPrintStatus });
		}
		if ('includeIngColorOrSizeTwo' in changedValues) {
			local.set('includeIngColorOrSizeTwo', changedValues.includeIngColorOrSizeTwo);
		}
		setFormData({ ...allValues, ...allValues.allPrintStatus });
	};

	// 处理打印异常查询
	const handleWarnPrintSearch = async(data:any) => {
		const multiShops = await getMultiShops({});
		const params = {
			isPlatformEmptyQuery: true,
			multiShopS: multiShops,
			startTime: dayjs().add(-1, 'm').format('YYYY-MM-DD HH:mm:ss'),
			endTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
			timeType: '1',
			status: 'ALL_STATUS',
			...data,
		};
		handleSearch && handleSearch(params);
	};

	// 发货超时预警查询
	const handleTimeoutHandleSearch = async(data:any) => {
		const multiShops = await getMultiShops({});
		const params = {
			isPlatformEmptyQuery: true,
			multiShopS: multiShops,
			startTime: dayjs().subtract(3, 'month').format('YYYY-MM-DD 00:00:00'),
			endTime: dayjs().format('YYYY-MM-DD 23:59:59'),
			timeType: '1',
			status: 'WAIT_SELLER_SEND_GOODS',
			...data,
		};
		console.log(params, 'paramsparamsparamsparams233');
		handleSearch && handleSearch(params);
	};

	const topItemOnChange = (data) => {
		console.log(data, 'paramsparamsparamsparams2');
		const { timeoutHandleQuery, type, model, perspective, selectedGoods, selectedSkus, filterType, orderWarningQuery } = data;
		if (orderWarningQuery) {
			handleWarnPrintSearch(data);
			return;
		}
		if (timeoutHandleQuery) {
			handleTimeoutHandleSearch(data);
			return;
		}
		const params = {
			itemId: '',
			sysItemId: '',
			skuId: '',
			sysSkuId: '',
			sysOuterSkuId: '',
			sellAttributeList: [],
			shortNameIncludingForTopItem: '',
			skuIncludingForTopItem: ''
		};

		if (type == TopPrintSearchItemTopMode.open) {
			if (perspective == TopPrintSearchPerspective.itemPerspective) {
				params.itemId = selectedGoods?.join(',');
				params.skuId = selectedSkus?.join(',');
			} else {
				params.sysItemId = selectedGoods?.join(',');
				params.sysSkuId = selectedSkus?.join(',');
			}
		} else {
			if (selectedGoods.length > 0 || selectedSkus.length > 0) {
				if (model === TopPrintSearchModel["MODEL_GOOD"]) { // 按商品
					// const selectedGoodData = selectedGoods[0]?.props?.originData;
					if (perspective === TopPrintSearchPerspective["itemPerspective"]) { // 商品视角
						params["itemId"] = selectedGoods?.map(s => s?.props?.originData?.numIid)?.join();
						params["skuId"] = selectedGoods?.map(s => s?.props?.originData?.skuId)?.join();
					} else if (perspective === TopPrintSearchPerspective["sysPerspective"]) { // 货品视角
						params["sysOuterSkuId"] = selectedGoods?.map(s => s?.props?.originData?.skuOuterId);
					}
					if (filterType === "onlyInclude") {
						params['sellAttributeList'] = [5];
					}
				} else if (model === TopPrintSearchModel["MODEL_SKU"]) { // 按款
					const shortNameIncluding = [];
					const skuIncluding = [];
					const itemId = [];
					const skuId = [];
					const sysItemId = [];
					const sysSkuId = [];
					if (selectedGoods.length > 0) {
						console.log(686, selectedGoods, selectedSkus);
						selectedGoods.forEach(goodOption => {
							const data = goodOption?.props?.originData;
							if (perspective === TopPrintSearchPerspective["itemPerspective"]) { // 商品视角
								itemId.push(data.numIid);
							} else if (perspective === TopPrintSearchPerspective["sysPerspective"]) { // 货品视角
								sysItemId.push(data.sysItemId);
							}
						});
					}
					if (selectedSkus.length > 0) {
						selectedSkus.forEach(skuOption => {
							const data = skuOption?.props?.originData;
							if (perspective === TopPrintSearchPerspective["itemPerspective"]) { // 商品视角
								skuId.push(data.skuId);
							} else if (perspective === TopPrintSearchPerspective["sysPerspective"]) { // 货品视角
								sysSkuId.push(data.sysSkuId);
							}
						});
					}
					if (filterType === "onlyInclude") {
						if (selectedSkus.length === 0) {
							params['sellAttributeList'] = [6];
						} else {
							params['sellAttributeList'] = [5];
						}
					}
					if (perspective === TopPrintSearchPerspective["itemPerspective"]) { // 商品视角
						params["itemId"] = itemId.join();
						params["skuId"] = skuId.join();
					} else if (perspective === TopPrintSearchPerspective["sysPerspective"]) { // 货品视角
						params["sysItemId"] = sysItemId.join();
						params["sysSkuId"] = sysSkuId.join();
					}
					// params["shortNameIncludingForTopItem"] = shortNameIncluding.join('@');
					// params["skuIncludingForTopItem"] = skuIncluding.join('@');
				}
			}
			if (selectedGoods.length === 0 && selectedSkus.length === 0) {
				setHasTopItemValue(false);
			} else {
				form.setFieldsValue({
					shortNameIncluding: '',
					skuIncluding: ''
				});
				setHasTopItemValue(true);
			}
		}
		setTopItemData(params);
	};

	const TRADE_LABEL_OPTIONS_1 = useMemo(() => {
		const isMergeClose = userStore.systemSetting?.mergeOrder == autoMergeConfig.关闭;
		const filterArr = ['noMerge', 'merge'];
		if (isMergeClose) {
			const val = form.getFieldValue('tradeLabelList');
			if (val?.some(v => filterArr.includes(v))) {
				form.setFieldsValue({ tradeLabelList: val.filter(v => !filterArr.includes(v)) });
			}
		}
		TRADE_LABEL_OPTIONS.forEach(op => {
			op.options.forEach(item => {
				item.arr.forEach(i => {
					if (filterArr.includes(i.key)) {
						i.tip = isMergeClose ? '系统未开启自动合单，无法查询' : '';
						i.disabled = isMergeClose;
					}
				});
			});
		});
		const hasWaveManagePermission = userStore.hasWaveManagePermission;
		if (!hasWaveManagePermission) {
			TRADE_LABEL_OPTIONS.forEach(op => {
				op.options.forEach(item => {
					item.arr = item.arr.filter(i => i.value !== '已生成拣货波次');
				});
			});
		}
		console.log(userStore.hasWaveManagePermission, TRADE_LABEL_OPTIONS, 'TRADE_LABEL_OPTIONSTRADE_LABEL_OPTIONS');
		return [...TRADE_LABEL_OPTIONS];
	}, [userStore.systemSetting?.mergeOrder, userStore.hasWaveManagePermission]);

	const TRADE_EXCEPTION_OPTIONS_1 = (useMemo(() => {
		const hasWaveManagePermission = userStore.hasWaveManagePermission;
		if (!hasWaveManagePermission) {
			TRADE_EXCEPTION_OPTIONS.forEach(op => {
				op.options.forEach(item => {
					item.arr = item.arr.filter(i => i.value !== '波次拣货异常');
				});
			});
		}
		return [...TRADE_EXCEPTION_OPTIONS];
	}, [userStore.hasWaveManagePermission]));

	// 保存预设查询条件
	const savePresetQueryCondition = () => {
		event.emit('PresetQueryNavBar.savePresetQueryCondition');
	};

	const itemInfoQueryNotIncludeList = useMemo(() => {
		return getGoodsInfoSelectOptions(userStore.userInfo?.version, false);
	}, [userStore.userInfo?.version]);

	const itemInfoQueryIncludeList = useMemo(() => {
		return getGoodsInfoSelectOptions(userStore.userInfo?.version, true);
	}, [userStore.userInfo?.version]);

	const itemInfoQueryNotIncludePlaceholder = useMemo(() => {
		return itemInfoQueryNotIncludeList.filter(i => i.value == formData.goodsNotIncludeStatus)?.[0]?.placeholder ?? ['商品名称/简称/编码/ID', '规格名称/编码/别名'];
	}, [itemInfoQueryNotIncludeList, formData.goodsNotIncludeStatus]);

	const itemInfoQueryIncludePlaceholder = useMemo(() => {
		return itemInfoQueryIncludeList.filter(i => i.value == formData.goodsIncludeStatus)?.[0]?.placeholder ?? ['商品名称/简称/编码/ID', '规格名称/编码/别名'];
	}, [itemInfoQueryIncludeList, formData.goodsIncludeStatus]);

	const itemInfoQueryIncludeMaxNum = useMemo(() => {
		if (!('goodsIncludeStatus' in formData) || formData.goodsIncludeStatus == itemInfoEnumValue.商品包含) {
			return [500, 500];
		}
		return [500, 500];
	}, [formData.goodsIncludeStatus, showAll]);

	const itemInfoQueryNotIncludeMaxNum = useMemo(() => {
		if (!('goodsNotIncludeStatus' in formData) || formData.goodsNotIncludeStatus == itemInfoEnumValue.商品不包含) {
			return [500, 500];
		}
		return [500, 500];
	}, [formData.goodsNotIncludeStatus, showAll]);

	const [selectedValues, setSelectedValues] = useState([]);
	const handleChange = (value) => {
		setSelectedValues(value);
	};

	return (
		<div className={ cs(s.searchContainer) }>
			{showTopInfo && hasFunctionPermission(FunctionPermissionEnum.爆款查询) ? (
				<div className="r-mb-8">
					{/* <FunctionPermissionCheck functionPermission={ FunctionPermissionEnum.爆款查询 } noFitWidth={ false }> */}
					<TopPrint onChange={ topItemOnChange } cRef={ topPrintComp } cRef2={ topPrintComp2 } />
					{/* </FunctionPermissionCheck> */}
				</div>
			) : ''}

			<Form
				name="trade-search"
				className={ `${s['trade-search-form']} print-batch-search-con ${s.arrayMultiStyle}` }
				size="small"
				layout="inline"
				form={ form }
				onValuesChange={ onFormValueChange }
				initialValues={ searchInitValue }
				onFinish={ handleFinish }
			>
				{
					conditionSet.map((item) => {
						return item?.selected == 1 || showAll ? (
							<Fragment key={ item.id }>
								{/* 平台列表 & 店铺列表  */}
								{item.id == ConditionIdEnum['平台&店铺'] ? (
									<Form.Item className={ cs(s.condition2) } name="platformInfo">
										<ShopMultiSelect
											bgHighLight
											isSendPoint
											style={ { width: '100%', flex: "1 1 0%" } }
											isHasHandPlat
										/>
									</Form.Item>
								) : ''}

								{/* 订单来源 orderSource  */}
								{item.id == ConditionIdEnum.订单来源 ? (
									<Form.Item className={ cs(s.condition1) } name="orderSource">
										<OrderSourceType hideLight bgHighLight isSupplierAccount={ isSupplierAccount || isFreeSupplierAccount } />
									</Form.Item>
								) : ''}

								{/* 订单来源 orderSource, 选择选择分销商  */}
								{item.id == ConditionIdEnum.选择分销商 && (isSupplierAccount || isFreeSupplierAccount) ? (
									<Form.Item className={ cs(s.condition2) } name="distributorUserIds">
										<DistributorSelect hideLight roleType="supplier" ref={ distributorSelectRef } />
									</Form.Item>
								) : ''}

								{/* 时间类型 */}
								{ item.id == ConditionIdEnum.时间选择 ? (
									<>
										<Form.Item className={ s.condition1 } name="timeType">
											<SearchTimeType hideLight bgHighLight />
										</Form.Item>
										<Form.Item className={ s.condition2 } name="searchTime">
											{/* <KdzsDateRangePicker datePickerKey={ DatePickerKey.trade_search } cacheQuickChoose /> */}
											<KdzsDateRangePicker1 bgHighLight datePickerKey={ DatePickerKey.trade_search } cacheQuickChoose useServeTime />
										</Form.Item>
									</>
								) : ''}

								{/* 订单状态 */}
								{ item.id == ConditionIdEnum.订单状态 ? (
									<Form.Item className={ s.condition1 } name="status">
										<SearchTradeStatus bgHighLight />
									</Form.Item>
								) : ''}

								{/* 订单状态 */}
								{ item.id == ConditionIdEnum.订单类型 ? (
									<Form.Item className={ s.condition1 } name="tradeType">
										<SearchTradeType hideLight bgHighLight />
									</Form.Item>
								) : ''}

								{/* 打印状态 */}
								{ item.id == ConditionIdEnum.打印状态 ? (
									<Form.Item className={ s.condition1 } name="allPrintStatus" >
										<NewSearchPrintStatus bgHighLight />
									</Form.Item>
								) : ''}


								{/* 波次号 - 新增 */}
								{item.id == ConditionIdEnum.波次号 ? (
									<Form.Item className={ cs(s.condition1) } name="waveNo">
										{userStore.hasWaveManagePermission ? <SearchBatchNo /> : null}
									</Form.Item>
								) : ''}

								{ item.id == ConditionIdEnum.发货仓库 ? (
									<Form.Item name="storageIdList" style={ { minWidth: 160 } } className={ cs(s.condition2) } >
										<Select className={ selectedValues?.length > 0 ? s['high-light'] : '' } onChange={ handleChange } showArrow placeholder="请选择发货仓库" mode="multiple" allowClear maxTagCount={ 1 } >
											{storageInfoList?.map((item) => {
												return (
													<Select.Option value={ item.id }>
														{item.storageName}（{item.storageTypeDesc}）
													</Select.Option>
												);
											})}
										</Select>
									</Form.Item>
								) : ''}

								{/* 省市区 */}
								{ item.id == ConditionIdEnum.省市区 ? (
									<Form.Item className={ cs(s.condition1) } name="addressInfo">
										<AddressSelect hideLight bgHighLight isSendPoint isInitValue />
									</Form.Item>
								) : ''}

								{/* 留言备注 */}
								{item.id == ConditionIdEnum.线下备注 ? (
									<Form.Item noStyle shouldUpdate={ (pre, cur) => pre.sysMemoMode != cur.sysMemoMode }>
										{({ getFieldValue }) => {
											const isMore = getFieldValue('sysMemoMode') == SysMemoModeEnum.有线下备注;
											const hasMoreValue = getFieldValue('sysMemoList')?.length;
											return (
												<Form.Item className={ isMore ? cs(s.condition2) : cs(s.condition1) }>
													<Input.Group compact style={ { display: "flex", alignItems: "center" } }>
														<Form.Item noStyle name="sysMemoMode" >
															<Select className={ hasMoreValue ? 'high-light-bg' : '' } style={ { flex: 1, } }>
																<Select.Option value={ SysMemoModeEnum.线下备注 } >
																	线下备注
																</Select.Option>
																<Select.Option value={ SysMemoModeEnum.有线下备注 } >
																	有线下备注
																</Select.Option>
																<Select.Option value={ SysMemoModeEnum.无线下备注 } >
																	无线下备注
																</Select.Option>
															</Select>
														</Form.Item>
														{
															isMore ? (
																<Form.Item noStyle name="sysMemoList">
																	<InputArrayMulti
																		className={ `${formData.sysMemoList ? 'high-light-bg' : ''}` }
																		size="small"
																		maxInputNum={ 50 }
																		maxTagCount={ 1 }
																		maxTagTextLength={ 8 }
																		open={ false }
																		tokenSeparators={ null }
																		style={ { flex: 1, width: 192 } }
																	/>
																</Form.Item>
															) : null
														}

													</Input.Group>
												</Form.Item>
											);
										}}
									</Form.Item>
								) : ''}

								{/* 留言备注 */}
								{item.id == ConditionIdEnum.留言备注 ? (
									<Form.Item className={ cs(s.condition1) } name="flagValue" >
										<FlagAndMemoSelect hideLight bgHighLight fromTrade />
									</Form.Item>
								) : ''}

								{/* 留言/备注模糊搜索 */}
								{ item.id == ConditionIdEnum.留言备注 && ['-1'].includes(formData.flagValue) ? (
									<>
										<Form.Item className={ cs(s.condition1) } name="buyerMessageOrSellerMemo">
											<InputMulti
												placeholder="留言或备注内容"
												maxInputNum={ 50 }
												className={ `${formData.buyerMessageOrSellerMemo ? 'high-light-bg' : ''}` }
												maxInputLength={ 1000 }
												lengthErrorMsg="最多输入1000个字数，请重新输入"
												numErrorMsg="单次查询最多筛选50个请重新输入,"
												style={ { width: '100%}' } }
												size="small"
											/>
										</Form.Item>
										{/* <Form.Item className={ cs(s.condition1) } name="buyerMessage">
											<InputMulti
												placeholder="留言内容"
												maxInputNum={ 50 }
												maxInputLength={ 1000 }
												lengthErrorMsg="留言最多输入1000个字数，请重新输入"
												numErrorMsg="单次查询最多筛选50个留言请重新输入,"
												style={ { width: '100%}' } }
												size="small"
											/>
										</Form.Item>
										<Form.Item className={ cs(s.condition1) } name="sellerMemo">
											<InputMulti
												placeholder="备注内容"
												maxInputNum={ 50 }
												maxInputLength={ 1000 }
												lengthErrorMsg="备注最多输入1000个字数，请重新输入"
												numErrorMsg="单次查询最多筛选50个备注，请重新输入"
												style={ { width: '100%}' } }
												size="small"
											/>
										</Form.Item> */}
										<Form.Item className={ cs(s.condition1) } name="sellerFlag">
											<FlagSelect
												className={ `${formData.sellerFlag?.length ? 'high-light-bg' : ''}` }
												placeholder="旗帜"
												style={ { width: '100%}' } }
												size="small"
											/>
										</Form.Item>
									</>
								) : ''}

								{/* 商品查询 不包含 */}
								{item.id == ConditionIdEnum.商品不包含 ? (
									<Form.Item className={ cs(s.condition3) } >
										<Input.Group compact style={ { display: "flex", alignItems: "center" } }>
											<Form.Item noStyle name="goodsNotIncludeStatus" >
												<Select
													disabled={ hasTopItemValue }
													style={ { flex: 0.6 } }
													className={ `${(formData.shortNameNotIncludingList?.length || formData.skuNotIncludingList?.length) ? 'high-light-bg' : ''}` }
													onChange={ () => sendPoint(Pointer.订单_订单打印_订单查询_查询选项_商品查询) }
													// options={ itemInfoQueryNotIncludeList }
													dropdownMatchSelectWidth={ false }
													optionLabelProp="label"
												>
													{itemInfoQueryNotIncludeList.map(si => {
														return (
															<Select.Option value={ si.value } label={ si.label }>
																{si.label}
																<div className="r-fs-12">{si.text}</div>
															</Select.Option>
														);
													})}
												</Select>
											</Form.Item>
											{
												itemInfoQueryNotIncludePlaceholder?.[0] ? (
													<Form.Item noStyle name="shortNameNotIncludingList">
														<InputArrayMulti
															className={ `${formData.shortNameNotIncludingList?.length ? 'high-light-bg' : ''} ` }
															disabled={ hasTopItemValue }
															size="small"
															placeholder={ itemInfoQueryNotIncludePlaceholder[0] }
															maxInputNum={ itemInfoQueryNotIncludeMaxNum?.[0] }
															maxTagCount={ 1 }
															maxTagTextLength={ 8 }
															open={ false }
															tokenSeparators={ null }
															style={ { flex: 1, width: 192 } }
														/>
													</Form.Item>
												) : ''
											}
											{
												itemInfoQueryNotIncludePlaceholder?.[1] ? (
													<Form.Item noStyle name="skuNotIncludingList" >
														<InputArrayMulti
															className={ `${formData.skuNotIncludingList?.length ? 'high-light-bg' : ''}` }
															disabled={ hasTopItemValue }
															size="small"
															placeholder={ itemInfoQueryNotIncludePlaceholder[1] }
															maxInputNum={ itemInfoQueryNotIncludeMaxNum?.[1] }
															maxTagCount={ 1 }
															maxTagTextLength={ 8 }
															open={ false }
															tokenSeparators={ null }
															style={ { flex: 1, width: 192 } }
														/>
													</Form.Item>
												) : ''
											}
										</Input.Group>
									</Form.Item>
								) : ''}

								{/* 商品查询 包含*/}
								{item.id == ConditionIdEnum.商品包含 ? (
									<Form.Item className={ cs(s.condition3) } >
										<Input.Group compact style={ { display: "flex", alignItems: "center" } }>
											<Form.Item noStyle name="goodsIncludeStatus" >
												<Select
													disabled={ hasTopItemValue }
													style={ { flex: 0.6 } }
													className={ `${(formData.shortNameIncludingList?.length || formData.skuIncludingList?.length) ? 'high-light-bg' : ''} ` }
													onChange={ () => sendPoint(Pointer.订单_订单打印_订单查询_查询选项_商品查询) }
													// options={ itemInfoQueryIncludeList }
													dropdownMatchSelectWidth={ false }
													optionLabelProp="label"
												>
													{itemInfoQueryIncludeList.map(si => {
														return (
															<Select.Option value={ si.value } label={ si.label }>
																{si.label}
																<div className="r-fs-12">{si.text}</div>
															</Select.Option>
														);
													})}
												</Select>
											</Form.Item>
											{
												itemInfoQueryIncludePlaceholder?.[0] ? (
													<Form.Item noStyle name="shortNameIncludingList">
														<InputArrayMulti
															className={ `${formData.shortNameIncludingList?.length ? 'high-light-bg' : ''}` }
															disabled={ hasTopItemValue }
															size="small"
															placeholder={ itemInfoQueryIncludePlaceholder[0] }
															maxInputNum={ itemInfoQueryIncludeMaxNum?.[0] }
															maxTagCount={ 1 }
															maxTagTextLength={ 8 }
															open={ false }
															tokenSeparators={ null }
															style={ { flex: 1, width: 192 } }
														/>
													</Form.Item>
												) : ''
											}
											{
												itemInfoQueryIncludePlaceholder?.[1] ? (
													<Form.Item noStyle name="skuIncludingList" >
														<InputArrayMulti
															className={ `${formData.skuIncludingList?.length ? 'high-light-bg' : ''} ` }
															disabled={ hasTopItemValue }
															size="small"
															placeholder={ itemInfoQueryIncludePlaceholder[1] }
															maxInputNum={ itemInfoQueryIncludeMaxNum?.[1] }
															maxTagCount={ 1 }
															maxTagTextLength={ 8 }
															open={ false }
															tokenSeparators={ null }
															style={ { flex: 1, width: 192 } }
														/>
													</Form.Item>
												) : ''
											}
										</Input.Group>
									</Form.Item>
								) : ''}

								{/* 订单标签（原快捷查询&服务标签） */}
								{item.id == ConditionIdEnum.订单标签 ? (
									<Form.Item className={ cs(s.condition2) } name="tradeLabelList">
										<InputSelect bgHighLight optionsList={ TRADE_LABEL_OPTIONS_1 } size="small" />
									</Form.Item>
								) : ''}

								{/* 订单异常(原异常订单&缺货状态) */}
								{item.id == ConditionIdEnum.订单异常 ? (
									<Form.Item className={ cs(s.condition2) } name="tradeExceptionList">
										<InputSelect bgHighLight optionsList={ TRADE_EXCEPTION_OPTIONS_1 } size="small" />
									</Form.Item>
								) : ''}


								{/* 买家昵称 */}
								{item.id == ConditionIdEnum.买家昵称 ? (
									<Form.Item className={ cs(s.condition1) } name="buyerNick">
										<InputMulti className={ `${formData.buyerNick ? 'high-light-bg' : ''}` } placeholder="买家昵称" style={ { width: '100%}' } } size="small" />
									</Form.Item>
								) : ''}

								{/* 收件人姓名 */}
								{item.id == ConditionIdEnum.收件人 ? (
									<Form.Item className={ cs(s.condition1) } name="receiveName">
										<InputMulti className={ `${formData.receiveName ? 'high-light-bg' : ''}` } placeholder="收件人" style={ { width: '100%}' } } size="small" />
									</Form.Item>
								) : ''}

								{/* 手机号 */}
								{item.id == ConditionIdEnum.手机号 ? (
									<Form.Item className={ cs(s.condition1) } name="mobile">
										<InputMulti className={ `${formData.mobile ? 'high-light-bg' : ''}` } placeholder="手机号" style={ { width: '100%}' } } size="small" />
									</Form.Item>
								) : ''}

								{/* 订单编号 */}
								{item.id == ConditionIdEnum.订单编号 ? (
									<Form.Item className={ cs(s.condition1) } name="ptTid">
										<InputMulti
											className={ `${formData.ptTid ? 'high-light-bg' : ''} high-placeholder` }
											maxInputNum={ 1000 }
											placeholder="订单编号"
											style={ { width: '100%}' } }
											size="small"
										/>
									</Form.Item>
								) : ''}

								{/* 系统编号 */}
								{item.id == ConditionIdEnum.系统单号 ? (
									<Form.Item className={ cs(s.condition1) } name="tid">
										<InputMulti className={ `${formData.tid ? 'high-light-bg' : ''} high-placeholder` } maxInputNum={ 1000 } placeholder="系统单号" style={ { width: '100%}' } } size="small" />
									</Form.Item>
								) : ''}

								{/* 快递单号 */}
								{item.id == ConditionIdEnum.快递单号 ? (
									<Form.Item className={ cs(s.condition1) } name="sid">
										<InputMulti className={ `${formData.sid ? 'high-light-bg' : ''} high-placeholder` } maxInputNum={ 1000 } placeholder="快递单号" style={ { width: '100%}' } } size="small" />
									</Form.Item>
								) : ''}

								{/* 唯一码 */}
								{item.id == ConditionIdEnum.唯一码 ? (
									<Form.Item className={ cs(s.condition1) } name="labelIdList">
										<InputMulti className={ `${formData.labelIdList ? 'high-light-bg' : ''}` } maxInputNum={ 1000 } placeholder="唯一码" style={ { width: '100%}' } } size="small" />
									</Form.Item>
								) : ''}

								{/* 业务逻辑拆分 */}
								{/* {item.condition === 'rangeFilter' ? (
									<Form.Item className={ cs(s.condition2) } name="range">
										<RangeSelect />
									</Form.Item>
								) : ''} */}

								{item.id == ConditionIdEnum.商品数量 ? (
									<Form.Item className={ cs(s.condition2) } name="goodsTotalNumRange">
										<RangeSelect bgHighLight rangeType="goodsTotalNumRange" />
									</Form.Item>
								) : ''}

								{item.id == ConditionIdEnum.商品种类 ? (
									<Form.Item className={ cs(s.condition2) } name="goodsTypeNumRange">
										<RangeSelect bgHighLight rangeType="goodsTypeNumRange" />
									</Form.Item>
								) : ''}

								{item.id == ConditionIdEnum.订单金额 ? (
									<Form.Item className={ cs(s.condition2) } name="paymentRange">
										<RangeSelect bgHighLight rangeType="paymentRange" />
									</Form.Item>
								) : ''}

								{item.id == ConditionIdEnum.订单重量 ? (
									<Form.Item className={ cs(s.condition2) } name="weightRange">
										<RangeSelect bgHighLight rangeType="weightRange" />
									</Form.Item>
								) : ''}

								{/* 销售属性 */}
								{item.id == ConditionIdEnum.销售属性 ? (
									<Form.Item className={ s.condition1 } name="sellAttributeList" >
										<SearchSellAttr bgHighLight />
									</Form.Item>
								) : ''}

								{/* 缺货状态 */}
								{item.id == ConditionIdEnum.缺货状态 && userStore.inventoryDeduct ? (
									<>
										<Form.Item className={ s.condition1 } name="goodStockStatus" >
											<SearchGoodStockStatus bgHighLight disable={ !isCanGoodStock } onClick={ () => { !isCanGoodStock && setCheckVersionControl(true); } } />
										</Form.Item>
										{
											checkVersionControl ? <LowVersionControlModal pageName={ PageNameControlEnum.小标签半备货模式 } closable onCancel={ () => { setCheckVersionControl(false); } } /> : null
										}
									</>
								) : ''}

								{/* 退款状态 */}
								{item.id === ConditionIdEnum.退款状态 ? (
									<Form.Item className={ s.condition1 } name="refundStatus">
										<SearchRefundStatus bgHighLight />
									</Form.Item>
								) : ''}

								{/* 订单标记 */}
								{item.id == ConditionIdEnum.订单标记 ? (
									<Form.Item className={ cs(s.condition1) } name="bizMarkObj">
										<OrderMarkSelect hideLight bgHighLight />
									</Form.Item>
								) : ''}

								{/* 剩余时间 */}
								{item.id === ConditionIdEnum.剩余发货时间 ? (
									<Form.Item className={ cs(s.condition1) } name="customizeResidueSendTime">
										<RemainingDeliveryTime hideLight bgHighLight />
									</Form.Item>
								) : ''}


								{/* 供应商 */}
								{item.id == ConditionIdEnum.供应商 ? (
									<Form.Item className={ cs(s.condition1) } name="supplierIncludingList">
										<InputArrayMulti
											size="small"
											className={ `${formData.supplierIncludingList ? 'high-light-bg' : ''}` }
											placeholder="供应商"
											maxInputNum={ 500 }
											maxTagCount={ 1 }
											maxTagTextLength={ 1 }
											open={ false }
											enterSearch={ false }
											tokenSeparators={ null }
											style={ { flex: 1 } }
										/>
									</Form.Item>
								) : ''}

								{/* 档口 */}
								{item.id == ConditionIdEnum.档口 ? (
									<Form.Item className={ cs(s.condition1) } name="dangKouIncludingList">
										<InputArrayMulti
											className={ `${formData.dangKouIncludingList ? 'high-light-bg' : ''}` }
											size="small"
											placeholder="档口"
											maxInputNum={ 500 }
											maxTagCount={ 1 }
											maxTagTextLength={ 1 }
											open={ false }
											enterSearch={ false }
											tokenSeparators={ null }
											style={ { flex: 1 } }
										/>
									</Form.Item>
								) : ''}

								{/* 市场 */}
								{item.id == ConditionIdEnum.市场 ? (
									<Form.Item className={ cs(s.condition1) } name="marketIncludingList">
										<InputArrayMulti
											size="small"
											className={ `${formData.marketIncludingList ? 'high-light-bg' : ''}` }
											placeholder="市场"
											maxInputNum={ 500 }
											maxTagCount={ 1 }
											maxTagTextLength={ 1 }
											open={ false }
											enterSearch={ false }
											tokenSeparators={ null }
											style={ { flex: 1 } }
										/>
									</Form.Item>
								) : ''}

								{/* 达人 */}
								{item.id == ConditionIdEnum['达人名称/ID'] ? (
									<Form.Item className={ cs(s.condition1) } name="authorIncludingList">
										<InputArrayMulti
											size="small"
											className={ `${formData.authorIncludingList ? 'high-light-bg' : ''}` }
											placeholder="达人名称/ID"
											maxInputNum={ 500 }
											maxTagCount={ 1 }
											maxTagTextLength={ 1 }
											open={ false }
											enterSearch={ false }
											tokenSeparators={ null }
											style={ { flex: 1 } }
										/>
									</Form.Item>
								) : ''}

								{/* 颜色尺码 */}
								{item.id == ConditionIdEnum.颜色尺码 ? (
									<Form.Item className={ cs(s.condition3) } >
										<Input.Group compact style={ { display: "flex", alignItems: "center" } }>
											<Form.Item noStyle name="includeIngColorOrSizeTwo" >
												<Select style={ { flex: 0.8 } } className={ `${(formData.sizeIncludingList || formData.colorIncludingList) ? 'high-light-bg' : ''}` }>
													<Select.Option value={ includeIngColorOrSizeEnum['颜色尺码包含(模糊)'] } >
														颜色尺码包含(模糊)
													</Select.Option>
													<Select.Option value={ includeIngColorOrSizeEnum['颜色尺码不包含(模糊)'] } >
														颜色尺码不包含(模糊)
													</Select.Option>
													<Select.Option value={ includeIngColorOrSizeEnum['颜色尺码包含(精确)'] } >
														颜色尺码包含(精确)
													</Select.Option>
													<Select.Option value={ includeIngColorOrSizeEnum['颜色尺码不包含(精确)'] } >
														颜色尺码不包含(精确)
													</Select.Option>
												</Select>
											</Form.Item>
											<Form.Item noStyle name="colorIncludingList">
												<InputArrayMulti
													className={ `${formData.colorIncludingList ? 'high-light-bg' : ''}` }
													size="small"
													placeholder="颜色"
													maxInputNum={ 50 }
													maxTagCount={ 1 }
													maxTagTextLength={ 8 }
													open={ false }
													tokenSeparators={ null }
													style={ { flex: 1, width: 192 } }
												/>
											</Form.Item>
											<Form.Item noStyle name="sizeIncludingList" >
												<InputArrayMulti
													className={ `${formData.sizeIncludingList ? 'high-light-bg' : ''}` }
													size="small"
													placeholder="尺码"
													maxInputNum={ 50 }
													maxTagCount={ 1 }
													maxTagTextLength={ 8 }
													open={ false }
													tokenSeparators={ null }
													style={ { flex: 1, width: 192 } }
												/>
											</Form.Item>
											<Popover content="颜色尺码查询提醒：目前仅支持淘宝、拼多多、抖店、快手、淘工厂平台">
												<div className="r-ai-c" style={ { display: 'flex' } }><Icon type="wenhao-xian" className="r-c-999 r-pointer r-ml-5" /></div>
											</Popover>
										</Input.Group>
									</Form.Item>
								) : ''}

								{/* 智选快递 */}
								{/* {item.id == ConditionIdEnum['智选快递'] ? (
									<Form.Item className={ cs(s.condition1) } name="smartExpressTemplateId">
										<SmartExpressTemplateSelect
											bgHighLight
										/>
									</Form.Item>
								) : ''} */}
							</Fragment>
						) : '';
					})
				}
				<Form.Item name="precise" >
					<Checkbox.Group>
						<span className="" >
							<Checkbox onChange={ () => { sendPoint(Pointer.订单_订单打印_订单查询_勾选项_宝贝精确查询); } } value="isPrecise" >宝贝精确查询</Checkbox>
							<Popover content={ <PreciseSearchTip /> } title="宝贝精确查询">
								<span className={ s.tip }>
									<Icon type="wenhao-xian" className="r-c-999 r-pointer" />
								</span>
							</Popover>
						</span>
					</Checkbox.Group>
				</Form.Item>
				<Form.Item >
					<Button style={ { padding: '0 10px' } } type="link" onClick={ handleShowAllCondition }>{showAll ? '默认查询条件' : '所有查询条件'}</Button>
					<Button type="link" onClick={ handleSetSearchRules }>自定义查询条件</Button>
				</Form.Item>
				<Form.Item noStyle >
					<Button data-point={ Pointer.订单_订单打印_订单查询_查询 } type="primary" className="kdzs-search-btn" style={ { padding: 0, width: '75px' } } htmlType="submit" loading={ isSearching } >查询</Button>
					<Button data-point={ Pointer.订单_订单打印_订单查询_重置 } className="r-ml-10 kdzs-search-btn" style={ { padding: 0, width: '75px' } } onClick={ reset } disabled={ isSearching }>重置</Button>
					{
						 hasFunctionPermission(FunctionPermissionEnum.预设条件管理) && <Button className="r-ml-8" onClick={ () => savePresetQueryCondition() } data-point={ Pointer['点击-保存为预设条件'] }>保存为预设条件</Button>
					}
				</Form.Item>
			</Form>

			{/* 预设查询条件 */}
			<PresetQueryNavBar
				isSearching={ isSearching }
				handlePreConditionInfo={ handlePreConditionInfo }
				conditionSet={ conditionSet }
				isCanGoodStock={ isCanGoodStock }
				handlePresetReset={ handlePresetReset }
				form={ form }
			/>

			{/* 自定义查询条件 */}
			{searchSortVisible
				? (
					<SearchRuleSort
						visible={ searchSortVisible }
						searchConditions={ conditionSet }
						handleOk={ handleSearchRuleSortOK }
						handleCancel={ () => { setSearchSortVisible(false); } }
						fromPage={ FromPageEnum.批打 }
					/>
				) : ''}
		</div>
	);
};

export default observer(SearchContainer);
