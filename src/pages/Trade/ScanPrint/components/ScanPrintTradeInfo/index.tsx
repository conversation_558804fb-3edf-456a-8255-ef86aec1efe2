import React, { useEffect, useLayoutEffect, useMemo, useState, useCallback, useRef } from "react";
import cs from "classnames";
import Table, { ColumnsType } from "antd/lib/table";
import { Button, Checkbox, Modal, Popover, Tooltip } from "antd";
import { observer } from "mobx-react";
import _ from "lodash";
import { runInAction, toJS } from "mobx";
import { CheckboxChangeEvent } from "antd/lib/checkbox";
import { useLocation } from "react-router-dom";
import dayjs from "dayjs";
import { useSize } from 'ahooks';
import { useActivate, useAliveController, useUnactivate } from 'react-activation';
import message from '@/components/message';
import s from "../../index.module.scss";
import { DEFAULT_IMG, PRINT_MAP, flagAudio, allFlagArr, PLAT_SCMHAND } from "@/constants";
import { getTradeStatusLabel, getTradeFlag, getTradeFlagTag
} from "@/pages/Trade/utils";
import scanPrintStore from "@/stores/trade/scanPrint";
import { ItemTakeGoodsLabelSelectTradeInfoByLabelIdResponse } from "@/types/trade/takeGoodsLabel";
import { IScanningLabelInfo } from "@/pages/Trade/interface";
import { ScanPrintPrintStatus, TemplateEnum, ViewSize, showShortNickLocal, buyerMessageFlagShowStyleEnum, PreviewSize } from "../../constants";
import { focusLabelInput, makeTradeData4ScanPrint, playAudio } from "../../utils";
import WaresInfo from "@/components-biz/WaresInfo";
import { local } from "@/libs/db";
import Pointer from "@/utils/pointTrack/constants";
import { tradeStore } from "@/stores";
import userStore from "@/stores/user";
import { getVersionConstants } from "@/constants/versionUtils";
import { getReachableHash, judgeExpressReach, TradeReachableCompanyParam } from "@/utils/trade/judgeExpressReach";
import 订单已挂起 from '@/assets/mp3/订单已挂起.mp3';
import voice商品已关闭 from '@/assets/mp3/商品已关闭.mp3';
import voice商品已发货 from '@/assets/mp3/商品已发货.mp3';
import 地址不可达 from '@/assets/mp3/地址不可达.mp3';
import 留言备注关键词 from '@/assets/mp3/留言备注关键词.mp3';
import 买家留言 from '@/assets/mp3/买家留言.mp3';
import 卖家备注 from '@/assets/mp3/卖家备注.mp3';
import 指定地区 from '@/assets/mp3/指定地区.mp3';
import Icon from "@/components/Icon";
import BatchModifyMemoModal from "@/pages/Trade/components/BatchModifyMemoModal";
import event from "@/libs/event";
import PlatformIcon from "@/pages/Warehouse/StockSync/HandStockSync/components/PlatformIcon";
import useGetState from "@/utils/hooks/useGetState";
import timer, { logTypeEnum } from "@/libs/timer";
import { TradeQueryBgTradeForScanApi } from "@/apis/report/kddLog";
import CustomScanPrintTradeInfoModal from '../CustomScanPrintTradeInfo';
import TradeLabelCom from "@/pages/Trade/components/ListItem/components/TradeLabelCom";
import TradeExceptionCom from "@/pages/Trade/components/ListItem/components/TradeExceptionCom";
import BuyerNickComp from "@/components-biz/BuyerNickComp";
import ColSortSetting from '../ColSortSetting';
import '@/pages/Trade/components/ListItem/index.scss';
import styleModule from './index.module.scss';
import { FieldsPermissionEnum } from "@/utils/permissionCheck/fieldsPermissionCheck";
import { isSourceScm } from "@/components-biz/ShopListSelect/shopListUtils";
import Flex from "@/components/Flex";
import { preCheckPrintLimit } from '@/utils/print/shopTemp';
import videoMonitorStore from "@/stores/trade/videoMonitor";

// playAudio多件混扫分拣码提示临时兼容方案 等待几百毫秒 需要等分拣码播报完 再进行了其他语音提示
// 后续建议语音播报单纯抽离出来优化 队列形式播报
const DELAY_TIME = 2300;


export interface IScanPrintTradeInfoProps { }

export type IData = ItemTakeGoodsLabelSelectTradeInfoByLabelIdResponse["data"]["packageList"][number]['takeGoodsLabelInfos'][number];

const ScanPrintTradeInfo = observer((props: IScanPrintTradeInfoProps) => {
	const { isShowZeroStockVersion } = userStore;
	const [data, setData] = useState<IData[]>([]);
	const [preparedData, setPreparedData] = useState<string[]>([]); // 手动备齐的oid
	const [indeterminate, setIndeterminate] = useState(false);
	const [checkAll, setCheckAll] = useState(false);
	const [tradeCount, setTradeCount] = useState(0);
	const [tableColumns, setTableColumns] = useState(null);
	const [togetherId, setTogetherId] = useState<string[]>([]);
	const [currentTakeGoodsLabelInfo, setCurrentTakeGoodsLabelInfo] = useState<IScanningLabelInfo>({});
	const [selectedAll, setSelectedAll] = useState(false);
	const [forcePrintLoading, setForcePrintLoading] = useState(false);
	const [showShortNick, setShowShortNick] = useState<boolean>(false);
	const [_spaceKeyPrint, setSpaceKeyPrint, getSpaceKeyPrint] = useGetState();
	const [windowSize, setWindowSize] = useState({
		ww: window.innerWidth - 500,
		ws: 800,
	});
	const { pathname } = useLocation();
	let {
		scanPrintSetting,
		setTakeGoodsLabelInfos,
		takeGoodsLabelInfos,
		scanPrintFormSetting,
		scanPrintTempInfo,
		searchScanPrintFormSetting,
		clearScanPrintInfo,
		getScanPrintSetting,
		playVoiceSuccess,
		setPlayVoiceSuccess,
		setForceNoPrint,
		setPrevLabelId,
		viewSize, // 字体大小
		previewSize, // 预览图大小
		scanPrintShowListSet, // 显示设置
		packageInfoVos, // 和批打一样的订单详情，处理订单标签、订单异常的数据
		columnConfig,
		columnConfigToJs,
		updateForceScanPrepOrderIds,
		getColumnList, // 列配置
		selectedShootingDevices,
		videoMonitorState,
	} = scanPrintStore;
	const {
		goodsTagListStore: {
			getIsFirstSend
		},
		setModifyMemoPackage,
		setIsShowBatchModifyMemoModal,
		isShowBatchModifyMemoModal,
		isMergePrint,
		kddTempList
	} = tradeStore;
	const { 
		clearVideoMonitor,
		startVideoMonitor,
		videoMonitor
	} = videoMonitorStore;
	const [isShowBeforePrintModal, setIsShowBeforePrintModal] = useState(false);
	const [preSendMode, setPreSendMode] = useState(false);
	const [scanPrintTradeInfoVisible, setScanPrintTradeInfoVisible] = useState(false); // 显示设置

	useEffect(() => {
		const allowSpaceKeyPrint = scanPrintSetting?.scanSet?.spaceKeyPrint;
		setSpaceKeyPrint(allowSpaceKeyPrint);
	}, [scanPrintSetting?.scanSet]);

	const onKeyDown = useCallback((event) => {
		// 判断用户是否开启了空格打印
		const allowSpaceKeyPrint = getSpaceKeyPrint();
		// 判断用户是否处于输入状态
		// const isInputStatus = ['input', 'textarea'].indexOf(event.target.tagName.toLowerCase()) > -1;
		// 判断当前是否有弹窗出现
		const allModal = document.querySelectorAll('.pup_express_box,.ant-modal-wrap');
		let hasModalShow = false;
		for (let key = 0; key < allModal.length; key++) {
			if (allModal[key]?.style?.display !== 'none') {
				hasModalShow = true;
				customLogPost(`当前有弹框`, { });
			}
		}
		// 判断下这种全局的loading
		let pageLoadingDom = document.querySelector('.pageLoading');
		if (pageLoadingDom) {
			hasModalShow = true;
			customLogPost(`当前正在打印`, { });
		}
		if (!hasModalShow) {
			// don't trigger your shortcut operation
			if (event.keyCode === 32 || event.code === 'Space') {
				const e = window.event || event;
				if (e.preventDefault) {
					e.preventDefault();
				} else {
					event.returnValue = false;
				}
				if (allowSpaceKeyPrint) {
					console.log('%c [ onKeyDown ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', '触发空格打印');
					beforePartPrint({ isForcePrint: false });
					customLogPost(`用户空格打印`, { allowSpaceKeyPrint });
				}
			}
		}
	}, [data, getSpaceKeyPrint]);

	const handleResize = () => {
		// console.log('%c [ handleResize ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', '111');
		let dom = document.getElementById('ScanPrintTradeInfo');
		if (dom) {
			let { right } = dom?.getBoundingClientRect();
			// console.log('%c [ right ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', right);
			let width = window.innerWidth - right - 40;
			setWindowSize({
				ww: width,
				ws: width < 800 ? 800 : width,
			});
		}
	};

	useEffect(() => {
		handleResize();
	}, [viewSize]);

	 // 使用 useMemo 确保引用一致
	const debouncedOnKeyDown = useMemo(() => _.debounce(onKeyDown, 1000, { leading: true, trailing: false }), [onKeyDown]);

	const debouncedResize = useMemo(() => _.debounce(handleResize, 200, { leading: false, trailing: true }), []);

	useActivate(() => {
		console.log('%c [ useActivate ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', '注册空格、窗口检测');
		if (data && pathname.includes('/trade/scanPrint')) {
			window.removeEventListener("keydown", debouncedOnKeyDown, false);
			window.addEventListener("keydown", debouncedOnKeyDown, false);

			window.removeEventListener('resize', debouncedResize, false);
			window.addEventListener('resize', debouncedResize, false);
		}
	});

	useUnactivate(() => {
		console.log('%c [ useUnactivate ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', '取消空格、取消窗口检测');
		window.removeEventListener("keydown", debouncedOnKeyDown, false);

		window.removeEventListener('resize', debouncedResize, false);
	});

	useEffect(() => {
		if (data && pathname.includes('/trade/scanPrint')) {
			window.removeEventListener('resize', debouncedResize, false);
			window.addEventListener('resize', debouncedResize, false);
		} else {
			window.removeEventListener('resize', debouncedResize, false);
		}
		return () => {
			window.removeEventListener('resize', debouncedResize, false);
		};
	}, [data, pathname, debouncedResize]);

	useEffect(() => {
		// console.log('%c [ pathname ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', pathname, data);
		if (data && pathname.includes('/trade/scanPrint')) {
			window.removeEventListener("keydown", debouncedOnKeyDown, false);
			window.addEventListener("keydown", debouncedOnKeyDown, false);
		} else {
			window.removeEventListener("keydown", debouncedOnKeyDown, false);
		}

		return () => {
			window.removeEventListener("keydown", debouncedOnKeyDown, false);
		};
	}, [data, pathname, debouncedOnKeyDown]);

	useEffect(() => {
		console.log(takeGoodsLabelInfos, 'takeGoodsLabelInfos');
		let _data: IData[] = [];
		let _tradeCount = 0;
		let tids: string[] = [];
		let currentScan: IData = {};
		_.cloneDeep(takeGoodsLabelInfos).filter(item => !item.isHide).forEach((item, index) => {
			_data.push(item);
			_tradeCount++;
			tids.push(item.tid);
			item.takeGoodsLabelInfos.forEach(labelInfo => {
				console.log('labelInfo', labelInfo);

				if (labelInfo.currentScan) {
					currentScan = {
						...labelInfo,
						...item,
						parentId: item.tid,
						parent: item,
					};
				}
				if (!labelInfo.isHide) {
					const isChecked = Number(labelInfo.preparedNum) === Number(labelInfo.goodsNum) && !labelInfo.isAbnormal;
					// console.log('%c [ isChecked ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', isChecked);
					_data.push({
						...labelInfo,
						isChecked,
						parentId: item.tid,
						parent: item,
					});
				}
			});
		});
		setTradeCount(_tradeCount);
		console.log(JSON.parse(JSON.stringify(_data)), 'data');
		setData(_data);
		setPreparedData([]);
		setCurrentTakeGoodsLabelInfo(currentScan);
		setTogetherId(tids);
	}, [takeGoodsLabelInfos]);

	const sellerEditFlag = useMemo(() => {
		return takeGoodsLabelInfos.filter(item => item.sellerMemo || (item.sellerFlag && item.sellerFlag != 0)).length > 0;
	}, [takeGoodsLabelInfos]);

	const customLogPost = (dataType: string, data:any = {}) => {
		window.errorCollection?.customMessageUpload({
			type: `扫描打印【${dataType}】`,
			data: {
				scanPrintSetting,
				scanPrintFormSetting,
				searchScanPrintFormSetting: toJS(searchScanPrintFormSetting),
				...data
			}
		});
	};

	useLayoutEffect(() => {
		// 多件订单混扫备齐模式 多判断alreadyScan 代表当前商品是否已经扫描过
		const mixScanMode = !!scanPrintSetting?.scanSet?.mixScanMode;
		// 非多件订单混扫备齐模式 判断动画逻辑
		const noMixScanModeAniShow = !_.isEmpty(currentTakeGoodsLabelInfo) && !currentTakeGoodsLabelInfo.isAbnormal;
		// 汇总动画逻辑
		const isShowAddOneAnimate = mixScanMode ? (!currentTakeGoodsLabelInfo.alreadyScan && noMixScanModeAniShow) : noMixScanModeAniShow;
		if (isShowAddOneAnimate) {
			let el = document.querySelector(`#oid-${currentTakeGoodsLabelInfo.oid}`);
			if (el) {
				el.classList.add(s['addOneAnimate']);
				setTimeout(() => {
					el.classList.remove(s['addOneAnimate']);
				}, 5000);
			}
		}
	}, [currentTakeGoodsLabelInfo, scanPrintSetting?.scanSet?.mixScanMode]);

	useEffect(() => {
		if (!data || !data.length) return;
		let checkOrders = data.filter(item => item.oid && item.isChecked);
		let selectedLen = checkOrders?.length;
		let allList = data.filter(item => item.oid);
		const allWaitSendList = data.filter(order => order.orderStatus === "WAIT_SELLER_SEND_GOODS");
		let allLen = allList.length;
		console.log('备齐发货data', data, selectedLen, allList, allWaitSendList);
		if (selectedLen && selectedLen != allLen) {
			setIndeterminate(true);
		} else {
			setIndeterminate(false);
		}
		setCheckAll(selectedLen === allLen);
		const hasAllChecked = selectedLen === allLen; // 判断是不是全部勾选了
		let hasAllWaitSendChecked = false;
		if (allWaitSendList.length > 0) {
			hasAllWaitSendChecked = allWaitSendList.every(order => order.isChecked); // 判断待发货的是不是全部勾选了
		}
		if (hasAllChecked || hasAllWaitSendChecked) {
			setSelectedAll(true);
			getScanPrintSetting().then(setting => {
				/**
				 * 扫描设置 -- 已关闭的订单不自动打印
				 * 调整原因：
				 * 原先已关闭的订单禁止勾选，后来客户有强打的需求解除限制，导致已关闭的订单会自动打印，这里加个限制:相关配置暂不添加
				 */
				let ignoreCloseOrderAndPrint = setting?.scanSet?.ignoreCloseOrderAndPrint;
				let hasCloseOrder = checkOrders.some(item => item.orderStatus == 'TRADE_CLOSED'); // 勾选肯定是备齐了
				let hasCloseOrderAndPrepareNum = allList.some(item => item.orderStatus == 'TRADE_CLOSED' && item?.preparedNum && Number(item.preparedNum)); // 关闭订单不能有一个备齐数量，这里0竟然是字符串？
				let hasFirstSend = checkOrders.some(i => i.firstSend && i.ydNo);
				// 如果已关闭的子订单不存在备齐数量、对应订单不存在先发货的订单标识
				console.log('%c [ 111 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', allList, ignoreCloseOrderAndPrint, !hasCloseOrder, !hasCloseOrderAndPrepareNum, !hasFirstSend);
				if (ignoreCloseOrderAndPrint && !hasCloseOrder && !hasCloseOrderAndPrepareNum && !hasFirstSend) {
					// 允许自动打印
					customLogPost('忽略已关闭的子订单备齐打印，允许自动打印', { checkOrders });
				} else if (allList.some(item => item.orderStatus == 'TRADE_CLOSED')) {
					scanPrintTempInfo.scanInfoMsg = <span className={ s['scan-info-content-error'] }>订单已关闭，请点击打印手动打印</span>;
					playAudio(voice商品已关闭);
					customLogPost('已关闭的订单不自动打印');
					return;
				}

				// 扫描设置 -- 已打印快递单的不自动打印
				if (setting?.scanSet.printedKddNoAutoPrint && allList.some(item => +item.parent.isPrintKdd)) {
					scanPrintTempInfo.scanInfoMsg = <span className={ s['scan-info-content-error'] }>订单已打印过快递单，请点击打印手动打印</span>;
					customLogPost('已打印快递单的不自动打印');
					return;
				}
				// 扫描设置 -- 已备齐的不自动打印
				if (setting?.scanSet.preparedNoAutoPrint && allList.some(item => item.goodsNum === item.preparedNum)) {
					scanPrintTempInfo.scanInfoMsg = <span className={ s['scan-info-content-error'] }>订单已备齐，请点击打印手动打印</span>;
					customLogPost('已备齐的不自动打印');
					return;
				}
				// 扫描设置 -- 挂起订单不自动打印
				if (setting?.scanSet.pendingTradeNoAutoPrint && allList.some(item => item.parent.isPending)) {
					playAudio(订单已挂起, DELAY_TIME);
					console.log('=======2222订单已挂起=====');
					scanPrintTempInfo.scanInfoMsg = <span className={ s['scan-info-content-error'] }>订单已挂起，请点击打印手动打印</span>;
					customLogPost('挂起订单不自动打印');
					return;
				}
				// 扫描设置 -- 已发货不自动打印
				if (setting?.scanSet.alreadySendNoAutoPrint && allList.some(item => item.orderStatus === 'WAIT_BUYER_CONFIRM_GOODS')) {
					playAudio(voice商品已发货, DELAY_TIME);
					scanPrintTempInfo.scanInfoMsg = <span className={ s['scan-info-content-error'] }>订单已发货，请点击打印手动打印</span>;
					customLogPost('已发货不自动打印');
					return;
				}

				// 扫描设置 -- 有留言不自动打印
				if (setting?.scanSet.hadBuyerMessageNoAutoPrint && allList.some(item => item.parent.buyerMessage)) {
					playAudio(买家留言, DELAY_TIME);
					scanPrintTempInfo.scanInfoMsg = <span className={ s['scan-info-content-error'] }>订单有留言，请点击打印手动打印</span>;
					customLogPost('有留言不自动打印');
					return;
				}
				// 扫描设置 -- 有备注不自动打印
				if (setting?.scanSet.hadSellerMemoNoAutoPrint && allList.some(item => item.parent.sellerMemo)) {
					playAudio(卖家备注, DELAY_TIME);
					scanPrintTempInfo.scanInfoMsg = <span className={ s['scan-info-content-error'] }>订单有备注，请点击打印手动打印</span>;
					customLogPost('有备注不自动打印');
					return;
				}
				let flag = 0;
				// 扫描设置 -- 旗帜不自动打印
				if (setting?.scanSet.hadFlagNoAutoPrint
					&& setting?.scanSet.noAutoPrintFlags
					&& allList.some(item => {
						if (setting?.scanSet.noAutoPrintFlags.includes(item.parent.sellerFlag)) {
							flag = item.parent.sellerFlag;
							return true;
						}
						return false;
					})) {
					playAudio(flagAudio[flag], DELAY_TIME);
					scanPrintTempInfo.scanInfoMsg = <span className={ s['scan-info-content-error'] }>订单有{allFlagArr?.[+flag]?.name}，请点击打印手动打印</span>;
					customLogPost(`订单有${allFlagArr?.[+flag]?.name}，不自动打印`);
					return;
				}
				// 扫描设置 -- 留言备注关键词不自动打印
				// !! filter(Boolean)fix bug: http://issue.raycloud.com/browse/KDZSERP-4049
				const _memoOrMessageKeyWordsNoAutoPrintVal = (setting?.scanSet.memoOrMessageKeyWordsNoAutoPrintVal || "").split(',').filter(Boolean);
				if (
					setting?.scanSet.memoOrMessageKeyWordsNoAutoPrint
					&& _memoOrMessageKeyWordsNoAutoPrintVal.length > 0
					&& allList.some(item => (
						_memoOrMessageKeyWordsNoAutoPrintVal.some(key => (item.parent.sellerMemo?.indexOf(key) > -1 || item.parent.buyerMessage?.indexOf(key) > -1))
					))
				) {
					playAudio(留言备注关键词, DELAY_TIME);
					scanPrintTempInfo.scanInfoMsg = <span className={ s['scan-info-content-error'] }>订单有留言或备注关键字，请点击打印手动打印</span>;
					customLogPost(`留言备注关键词不自动打印`);
					return;
				}
				// 扫描设置 -- 指定省份不自动打印 todo
				if (setting?.scanSet.selectedProvinceNoAutoPrint && setting?.scanSet?.areaJson?.length > 0) {
					for (let item of allList) {
						const { receiverProvince, receiverCity, receiverCounty } = item.parent;
						let res = hasSelectedProvince({ prov: receiverProvince, city: receiverCity, county: receiverCounty, areaJson: setting?.scanSet?.areaJson });
						if (res) {
							playAudio(指定地区, DELAY_TIME);
							scanPrintTempInfo.scanInfoMsg = <span className={ s['scan-info-content-error'] }>订单有指定地区，请点击打印手动打印</span>;
							customLogPost(`指定省份不自动打印`);
							return;
						}
					}
				}
				// 扫描设置 快递不可达订单不自动打印
				if (setting?.scanSet.canNotReachNoAutoPrint && scanPrintTempInfo.takeGoodsLabelInfoId) {
					try {
						let _scanPrintSearchSetting = local.get('scanPrintSearchSetting');
						const params: TradeReachableCompanyParam[] = allList.map((item) => ({
							province: item.parent.receiverProvince,
							city: item.parent.receiverCity,
							district: item.parent.receiverCounty,
							togetherId: item.parent.tid,
							exId: _scanPrintSearchSetting?.template?.Exid ? (_scanPrintSearchSetting?.template?.Exid + '') : '', // 融合打印里面没有 Exid
							exCode: isMergePrint ? _scanPrintSearchSetting?.template?.exCode : _scanPrintSearchSetting?.template?.ExCode, // 融合打印exCode 是小写
						}));

						params.forEach(item => {
							item.md5 = getReachableHash(item);
						});

						// -1 不可用 0 不可达 1 可达 2 超区 3 未知 4 自提 5 未配置 6 不知道
						judgeExpressReach(params).then((res) => {
							let notReach = false;
							for (let key in res) {
								if (res[key].canReach !== 1) notReach = true;
							}
							if (notReach) {
								playAudio(地址不可达, DELAY_TIME);
								scanPrintTempInfo.scanInfoMsg = <span className={ s['scan-info-content-error'] }>订单不可达，请点击打印手动打印</span>;
								customLogPost(`快递不可达订单不自动打印`);
							} else {
								timer.start("自动打印");
								beforePartPrint({ isForcePrint: false, isAutoPrint: true, isNotShowBeforePrintModal: true });
								timer.stop(`自动打印`, {
									type: logTypeEnum['小标签']
								});
								playVoiceSuccess && playAudio(playVoiceSuccess);
								setPlayVoiceSuccess(null);

								let hasPreparedList = allList?.filter(item => preparedData?.includes(item.oid));
								customLogPost(`自动打印: ${hasPreparedList?.length ? '手动备齐' : '自动备齐'}`, { preparedData, hasPreparedList });
							}

						});
					} catch (error) {
						console.log('error:', error);
						customLogPost(`快递不可达订单不自动打印 error`, { error });
					}
				} else {
					playVoiceSuccess && playAudio(playVoiceSuccess);
					setPlayVoiceSuccess(null);
					timer.start("自动打印");
					beforePartPrint({ isForcePrint: false, isAutoPrint: true, isNotShowBeforePrintModal: true });
					timer.stop(`自动打印`, {
						type: logTypeEnum['小标签'],
					});

					let hasPreparedList = allList?.filter(item => preparedData?.includes(item.oid));
					customLogPost(`自动打印: ${hasPreparedList?.length ? '手动备齐' : '自动备齐'}`, { preparedData, hasPreparedList });
				}

			});
		} else {
			playVoiceSuccess && playAudio(playVoiceSuccess);
			setPlayVoiceSuccess(null);
		}
		// event.on(updateLabelInfo, updateLabelInfoToStore);
		// return () => {
		// 	event.off(updateLabelInfo, updateLabelInfoToStore);
		// };
	}, [data]);

	const hasSelectedProvince = ({ prov, city, county, areaJson }) => {
		let originAddrArr = [prov, city, county];
		const diffAddr = (addrArr, selectedAddrObj) => {
			const addr = addrArr[0];
			// if (selectedAddrObj.name === addr) {
			// 这儿没有用 等于 判断 是因为有些地区 后端保存 北京市 北京市 东城区 地址接口校验市 北京 北京市 东城区
			if (addr.indexOf(selectedAddrObj.name) > -1) {
				if (!selectedAddrObj.childList || !selectedAddrObj.childList.length) {
					return true;
				} else if (addrArr.length) {
					addrArr.shift();
					for (let item of selectedAddrObj.childList) {
						let res = diffAddr(addrArr, item);
						if (res) return true;
					}
				} else {
					return false;
				}
			} else {
				return false;
			}
		};
		for (let item of areaJson) {
			let res = diffAddr(originAddrArr, item);
			if (res) return true;
		}
		return false;
	};

	const onSelectChange = (e: CheckboxChangeEvent, index: number) => {
		console.log("selectedRowKeys changed: ", e);
		setData(prev => {
			prev[index].isChecked = !prev[index].isChecked;
			return [...prev];
		});
	};

	const onSelectAllChange = (e: CheckboxChangeEvent) => {
		let checked = e.target.checked;
		setData(prev => {
			prev.forEach(item => {
				if (item.oid && item.preparedNum === item.goodsNum && !item.isAbnormal) {
					item.isChecked = checked;
				}
			});
			return [...prev];
		});

	};

	const renderColCell = (row: any) => {
		return row.tid ? { colSpan: 0 } : {};
	};

	const showShopTypeOnChange = (showShortNick) => {
		setShowShortNick(showShortNick);
	};

	const getImgURL = (goodsInfo) => {
		if (isShowZeroStockVersion) {
			return goodsInfo?.picUrl || DEFAULT_IMG;
		}
		return goodsInfo?.sysSkuPicUrl || goodsInfo?.picUrl || DEFAULT_IMG;
	};

	// 先发货表格列
	let columns: ColumnsType<IScanningLabelInfo> = [
		{
			dataIndex: 'groupChecked',
			title: <Checkbox indeterminate={ indeterminate } checked={ checkAll } onChange={ onSelectAllChange } />,
			width: 42,
			render: (text, row, index) => {
				if (!row.tid) {
					return (
						<Checkbox
							checked={ row.isChecked }
							onChange={ (e) => { onSelectChange(e, index); } }
							disabled={ row.preparedNum != row.goodsNum || row.isAbnormal }
						/>
					);
				} else {
					return (
						<div className={ s["tradeStatusCol"] }>
							<span className="r-mr-8">系统单号{row.tradeIndex}：{row.tid}</span>
							<span className="r-mr-8">订单编号{row.tradeIndex}：{row?.ptTid || ''}</span>
							<span>（{getTradeStatusLabel(row.tradeStatus)}）</span>
							<span className="r-mr-48 r-ml-16">付款时间：{row.payTime}</span>
							<span>分拣码：{row.sortNumCode}{row.sortTradeSn}</span>
						</div>
					);
				}
			},
			onCell: (_data, index) => {
				return _data.tid ? { colSpan: 11 } : {};
			},
		},
		{
			dataIndex: 'tradeInfo',
			title: "商品信息",
			width: 300,
			render: (text, row) => {
				const previewPicSize = previewSize === PreviewSize.小 ? 0 : (previewSize === PreviewSize.大 ? 2 : 1);
				if (!row.tid) {
					return (
						<div className={ s["tradeInfoCol"] }>
							<WaresInfo previewPicSize={ previewPicSize } align="fs" imgSize={ 38 } imgUrl={ getImgURL(row) } />
							<div className="r-ml-2">
								<div className="r-mb-4">{row.title}</div>
								<div className="r-c-black45">{row.sysItemAlias}</div>
								{isShowZeroStockVersion && (
									<div className="r-c-black45">
										{row?.outerId}
									</div>
								)}
							</div>
						</div>
					);
				}
			},
			onCell: renderColCell,
		},
		{
			dataIndex: 'skuInfo',
			title: "商品规格",
			width: 180,
			render: (text, row) => {
				if (!row.tid) {
					return (
						<>
							<div>{row.skuName}</div>
							<div>{row.sysSkuAlias}</div>
							{isShowZeroStockVersion && (
								<div>
									{row?.outerSkuId}
								</div>
							)}
						</>
					);
				}
			},
			onCell: renderColCell,
		},
		{
			dataIndex: 'payment',
			title: "实付金额",
			width: 80,
			render: (text, row) => {
				return row.payment;
			},
			onCell: renderColCell,
		},
		{
			dataIndex: 'outerSkuId',
			title: "规格编码",
			width: 100,
			render: (text, row) => {
				if (!row.tid) {
					return (
						<>
							<div>{row.outerSkuId}</div>
							<div>{row.sysSkuOuterId}</div>
						</>
					);
				}
			},
			onCell: renderColCell,
		},
		{
			dataIndex: 'exName',
			title: "快递公司", // 先发货
			width: 100,
			align: 'center',
			render: (text, row) => {
				if (!row.tid) {
					return (
						<div>
							<p>{row.exName}</p>
							<p>{row.ydNo}</p>
						</div>
					);
				}
			},
			onCell: renderColCell,
		},
		{
			dataIndex: 'printKddCount',
			title: "打印次数", // 先发货
			width: 80,
			align: 'center',
			render: (text, row) => {
				if (!row.tid) {
					return <div>{row.printKddCount || 0}</div>;
				}
			},
			onCell: renderColCell,
		},
		{
			dataIndex: 'orderStatus',
			title: "商品状态",
			width: 100,
			render: (text, row) => {
				if (!row.tid) {
					return (
						<>
							<div>
								{getTradeStatusLabel(row.orderStatus)}
								{row?.firstSend ? (
									<Tooltip title="先发货订单，已在系统中申请单号发货但未打印快递单" placement="topLeft" arrowPointAtCenter>
										<span className="r-trade-first-send r-ml-4">发</span>
									</Tooltip>
								) : ''}
							</div>
							{+row.labelStockpileCount > 0 ? (
								<div className="r-c-error r-fs-14 r-fw-700">已屯货 {row.labelStockpileCount}</div>
							) : ''}
							{row.refundStatus === 'REFUND_ING' && <div className="r-c-error r-fs-14 r-fw-700">(退款中)</div>}
						</>
					);
				}
			},
			onCell: renderColCell,
		},
		{
			dataIndex: 'goodsNum',
			title: "宝贝数量",
			width: 80,
			align: 'center',
			render: (text, row) => {
				if (!row.tid) {
					return <div>{row.goodsNum}</div>;
				}
			},
			onCell: renderColCell,
		},
		{
			dataIndex: 'preparedNum',
			title: "备齐状态",
			width: 100,
			align: 'center',
			render: (text, row) => {
				if (!row.tid) {
					// 多件订单混扫备齐模式
					const mixScanMode = !!scanPrintSetting?.scanSet?.mixScanMode;
					// const number = row.isStockoutPrepared ? row.preparedNum : row.preparedNum;
					const number = row.preparedNum;
					const numberRender = (
						<>
							{ (mixScanMode && row.alreadyScanLabelSortCode && row.preparedNum) ? (
								<Popover
									content={ (
										<div>
											<div>已备齐：</div>
											<div>{row.alreadyScanLabelSortCode}</div>
										</div>
									) }
								>
									<span id={ `oid-${row.oid}` } className={ cs(s['prepare-status'], 'r-pointer ') }>
										{number || 0}
									</span>
								</Popover>
							) : (
								<span id={ `oid-${row.oid}` } className={ cs(s['prepare-status']) }>
									{number || 0}
								</span>
							)}
						</>
					);
					return (
						<div>
							{numberRender}
							{scanPrintSetting?.scanSet?.showOutStockComplete == 1 && row.isStockoutPrepared && row.goodsNum - Number(row.tmpPreparedNum || 0) != 0 && (
								<span style={ { color: '#ff0000', fontSize: 12 } }>(缺{ row.goodsNum - Number(row.tmpPreparedNum || 0) })</span>
							)}
						</div>
					);
				}
			},
			onCell: renderColCell,
		},
		{
			dataIndex: 'operation',
			fixed: false,
			title: "操作",
			width: 100,
			render: (text, row, index) => {
				if (!row.tid && !row.isAbnormal) {
					if (Number(row.goodsNum) === Number(row.preparedNum)) {
						return <div className="trade-status-success">(已备齐)</div>;
					} else {
						return (
							<>
								<div data-point={ Pointer.扫描打印_手动备齐_点击 } className="r-click" onClick={ () => { manualPrepare(row); } }>手动备齐</div>
								{scanPrintSetting?.scanSet?.showOutStockComplete == 1 ? (
									<span className="r-click" style={ { fontSize: 12 } } onClick={ () => { manualPrepare(row, true); } }>缺货强制备齐</span>
								) : null}
							</>
						);
					}
				}
			},
			onCell: renderColCell,
		}
	];

	const userConfig = _.cloneDeep(columnConfigToJs().userConfig);
	const getColumns = useMemo(() => {
		const columnMap = {};
		columns.forEach((item:any) => (columnMap[item.dataIndex] = item));
		const newCols = [{
			...columnMap['groupChecked'],
			key: 'groupChecked',
			index: 0,
			name: '所有'
		}]; // 固定第一项
		const list = userConfig || [];

		list?.forEach((i) => {
			let item = columnMap?.[i.key];
			if (i.ischecked && item) {
				// 操作固定
				if (i.key == 'operation') {
					item['fixed'] = i.isfixed ? 'right' : false;
				}
				newCols.push(item);
			}
		});

		let allWidth = 0;
		newCols?.forEach(item => {
			if (item.key !== 'groupChecked') {
				allWidth += item.width;
			}
		});
		// 总的宽度小于表格宽度，相应的列宽平分剩余宽度
		if (newCols?.length && allWidth < windowSize?.ww) {
			let w = Math.round(((windowSize.ww - allWidth - 42) / (newCols?.length - 1)) * 100) / 100;
			newCols?.forEach(item => {
				// 勾选框没必要增加宽度
				if (item.key !== 'groupChecked') {
					item.width += w;
				}
			});
		}

		// console.log('%c [ columns ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', newCols);
		return newCols;
	}, [userConfig, windowSize]);

	// 增量更新store中的takeGoodsLabelInfos数据
	// const updateLabelInfoToStore = (incrementalData) => {
	// 	const _takeGoodsLabelInfosTemp = cloneDeep(takeGoodsLabelInfos);
	// 	const updateObject = (source:object, newProps:object) => {
	// 		for (let key in newProps) {
	// 			if (Object.prototype.hasOwnProperty.call(newProps, key)) {
	// 				if (typeof newProps[key] === 'object' && Object.prototype.hasOwnProperty.call(source, key) && typeof source[key] === 'object') {
	// 					updateObject(source[key], newProps[key]);
	// 				} else {
	// 					source[key] = newProps[key];
	// 				}
	// 			}
	// 		}
	// 		return source;
	// 	};
	// 	setTimeout(() => {
	// 		_takeGoodsLabelInfosTemp.forEach(pack => {
	// 			if (Object.prototype.hasOwnProperty.call(incrementalData, "takeGoodsLabelInfos")) {
	// 				pack.takeGoodsLabelInfos.forEach((labelInfo) => {
	// 					updateObject(labelInfo, incrementalData.takeGoodsLabelInfos);
	// 				});
	// 			} else {
	// 				updateObject(pack, incrementalData);
	// 			}
	// 		});
	// 		setForceNoPrint(true);
	// 		setTimeout(() => setTakeGoodsLabelInfos(_takeGoodsLabelInfosTemp));
	// 		setTimeout(() => setForceNoPrint(false), 10);
	// 	});
	// };

	// 过滤表格数据列
	// const filterColumns = () => {
	// 	let newColumn = columns.filter((col:any) => !['快递公司', '打印次数'].includes(col?.title));
	// 	setTableColumns(newColumn);
	// 	console.log('%c [ columns ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', newColumn);
	// };

	useEffect(() => {
		getColumnList();
		// setTableColumns(columns);
		setForceNoPrint(false);

		const _showShortNick = local.get(showShortNickLocal);
		setShowShortNick(_showShortNick);
		event.on(showShortNickLocal, showShopTypeOnChange);
		return () => {
			event.off(showShortNickLocal, showShopTypeOnChange);
		};
	}, []);

	// useEffect(() => {
	// 	// 快递公司是否显示
	// 	getIsFirstSend().then((res) => {
	// 		console.log('判断先发货模式、混扫模式===快递公司、打印次数是否显示===', res, scanPrintSetting?.scanSet?.mixScanMode);
	// 		if (res && !scanPrintSetting?.scanSet?.mixScanMode) {
	// 			setTableColumns(columns);
	// 			setPreSendMode(res);
	// 		} else {
	// 			filterColumns();
	// 			setPreSendMode(false);
	// 		}
	// 	});
	// }, [scanPrintSetting?.scanSet?.mixScanMode]);

	const manualPrepare = (record: IScanningLabelInfo, stockout = false) => {
		let i = takeGoodsLabelInfos.findIndex(item => item.tid === record.parentId);
		if (stockout) {
			updateForceScanPrepOrderIds(record.oid);
		}
		console.log('manualPrepare===record', record);
		if (i > -1) {
			let j = takeGoodsLabelInfos[i].takeGoodsLabelInfos.findIndex(item => item.oid === record.oid);
			if (j > -1) {
				runInAction(() => {
					console.log('xxxxx=======XXXX');
					takeGoodsLabelInfos[i].takeGoodsLabelInfos[j].isStockoutPrepared = stockout;
					if (stockout) {
						takeGoodsLabelInfos[i].takeGoodsLabelInfos[j].tmpPreparedNum = record.preparedNum || 0;
					}
					takeGoodsLabelInfos[i].takeGoodsLabelInfos[j].preparedNum = record.goodsNum;
					takeGoodsLabelInfos[i].takeGoodsLabelInfos[j].manualPrepare = true;
					scanPrintTempInfo.scannedGoodsCount += record.goodsNum - (record.preparedNum || 0);
					scanPrintTempInfo.currentScanOrderInfo.preparedNum = record.goodsNum;
				});
			}
		}

		setData(prev => {
			let index = prev.findIndex(i => i.oid === record.oid);
			if (index > -1) {
				timer.start(`手动备齐`);
				prev[index].isStockoutPrepared = stockout;
				if (stockout) {
					prev[index].tmpPreparedNum = record.preparedNum;
				}
				prev[index].preparedNum = record.goodsNum;
				prev[index].isChecked = true;
				timer.stop(`手动备齐`, {
					type: logTypeEnum['小标签'],
					desc: `手动备齐oid: ${prev[index].oid}`
				});

				// 储存手动备齐的订单
				setPreparedData([...(preparedData || []), record.oid]);
			}
			// console.log('setData:::', prev);
			// console.log('takeGoodsLabelInfos:', takeGoodsLabelInfos);
			return [...prev];
		});
		customLogPost(`缺货强制备齐`, { record });
	};

	const skipTrade = () => {
		setPrevLabelId("");
		timer.start(`暂时跳过`);
		clearScanPrintInfo();
		timer.stop(`暂时跳过`, {
			type: logTypeEnum['小标签'],
		});
		setTimeout(() => {
			focusLabelInput();
		}, 500);
	};

	const getHistorySidsParams = () => {
		try {
			let params = {};
			let tids = '';
			const ordersOids = [];
			data.forEach(item => {
				if (item.isChecked) {
					ordersOids.push(item.oid);
				}
			});
			if (ordersOids.length > 0) {
				tids = `${currentTakeGoodsLabelInfo.tid}:${ordersOids.join(",")}`;
			}
			if (isMergePrint) {
				const curExpressType = PRINT_MAP[currentTakeGoodsLabelInfo.platform];
				const curTemplate = scanPrintFormSetting.template.userTemplateList.find(template => template.expressType == curExpressType);
				params = {
					kdCode: scanPrintFormSetting.template.exCode,
					kdType: curTemplate?.expressType,
					exid: curTemplate?.exId,
					userTemplateId: curTemplate?.userTemplateId,
					trades: JSON.stringify([{
						tids,
						togetherId: currentTakeGoodsLabelInfo.tid,
						sellerId: currentTakeGoodsLabelInfo.sellerId,
						platform: currentTakeGoodsLabelInfo.platform
					}])
				};
			} else {
				const curTemplateInfo = kddTempList.find(item => item.Mode_ListShowId == scanPrintFormSetting.templateInfo);
				params = {
					kdCode: curTemplateInfo?.ExCode,
					kdType: curTemplateInfo?.KddType,
					exid: curTemplateInfo?.Exid,
					userTemplateId: curTemplateInfo?.Mode_ListShowId,
					trades: JSON.stringify([{
						tids: currentTakeGoodsLabelInfo.tid,
						togetherId: currentTakeGoodsLabelInfo.tid,
						sellerId: currentTakeGoodsLabelInfo.sellerId,
						platform: currentTakeGoodsLabelInfo.platform
					}])
				};
			}
			return params;
		} catch (e) {
			console.log(e);
		}
	};

	// isForcePrint 强制打印 isAutoPrint 自动打印 isNotShowBeforePrintModal 不判断 有待发货的商品未勾选打印弹框
	const beforePartPrint = _.debounce(async({ isForcePrint = false, isAutoPrint = false, isNotShowBeforePrintModal = false, isClick = false }:{isForcePrint?:boolean, isAutoPrint?:boolean, isNotShowBeforePrintModal?:boolean, isClick?:boolean}) => {
		// 判断是否需要发送视频监控消息
		const isIpcModel = selectedShootingDevices?.length > 0; 
		const isVideoMonitor = videoMonitor && videoMonitorState;
		  // 检查autoSend设置，如果开启了打印后自动发货，则不在打印前清除视频监控
		  const isAutoSendEnabled = !!scanPrintSetting?.scanSet?.autoSend;
		  if (!isAutoSendEnabled && isClick) {
			  clearVideoMonitor(isVideoMonitor, isIpcModel);
		  }
		console.log('beforePartPrint', data);
		console.log('scanPrintSetting', scanPrintSetting);
		await preCheckPrintLimit();
		// 相同运单号订单单需要全部勾选才可以打印
		let selectYdNoArr = []; // 先发货勾选了的
		let noSelectYdNoArr = []; // 先发货没有勾选的
		const ydNoObj = {}; // 已发货 alreadySend 已勾选 selected 未勾选 noSelected
		let selectAllWaitSend = true;
		let historySids = [];
		// * 先发货需要根据运单号从底单捞
		let firstSendArr = data.filter(i => i.isChecked && i.firstSend && i.ydNo);
		let logReprint = false; // 先发货从底单获取取号时保持的信息进行打印
		console.log('firstSendArr', isForcePrint, firstSendArr);
		if (firstSendArr.length) {
			const params = {
				queryType: 1,
				startTime: dayjs().subtract(3, "M").startOf('d').format("YYYY-MM-DD HH:mm:ss"),
				endTime: dayjs().endOf('d').format("YYYY-MM-DD HH:mm:ss"),
				pageNo: 1,
				pageSize: 10,
				sellerIdList: [firstSendArr[0].sellerId],
				platformList: [firstSendArr[0].platform],
				ydNoList: [firstSendArr[0].ydNo]
			};
			timer.start(`快销先发货根据运单号查询底单记录`);
			let bgTradeInfo = await TradeQueryBgTradeForScanApi(params).catch((e) => console.log(e));
			let { orderIds } = bgTradeInfo?.data?.list?.[0] || {};
			if (orderIds) {
				firstSendArr.forEach(i => i.applySidOrderIds = orderIds);
				logReprint = true;
				if (isForcePrint) {
					historySids = [firstSendArr[0].ydNo];
				}
			}
			timer.stop(`快销先发货根据运单号查询底单记录`, {
				type: logTypeEnum['小标签'],
			});
		} else if (isForcePrint) {
			let forceList = data.filter(i => i.isChecked);
			console.log('forceList', forceList);
			const params = {
				queryType: 1,
				startTime: dayjs().subtract(3, "M").startOf('d').format("YYYY-MM-DD HH:mm:ss"),
				endTime: dayjs().endOf('d').format("YYYY-MM-DD HH:mm:ss"),
				pageNo: 1,
				pageSize: 10,
				sellerIdList: [forceList[0].sellerId],
				platformList: forceList[0].platform ? [forceList[0].platform] : [],
				ydNoList: forceList[0].ydNo ? [forceList[0].ydNo] : []
			};
			timer.start(`快销已发货根据运单号查询底单记录`);
			let bgTradeInfo = await TradeQueryBgTradeForScanApi(params).catch((e) => console.log(e));
			let { orderIds } = bgTradeInfo?.data?.list?.[0] || {};
			console.log('bgTradeInfo底单查询记录', bgTradeInfo, orderIds);
			if (orderIds) {
				forceList.forEach(i => i.applySidOrderIds = orderIds);
				if (isForcePrint) {
					// 打印那里没判空，这里做个空值过滤
					historySids = [forceList[0].ydNo].filter(Boolean);
				}
			}
			timer.stop(`快销已发货根据运单号查询底单记录`, {
				type: logTypeEnum['小标签'],
			});
		}
		data.forEach((item) => {
			if (item.oid && item.ydNo) {
				if (!ydNoObj[item.ydNo]) {
					ydNoObj[item.ydNo] = {
						noSelected: 0,
						alreadySend: 0,
						selected: 0,
						oidArr: []
					};
				}
				// 同一运单号下未勾选的
				if (item.firstSend && !item.isChecked) {
					ydNoObj[item.ydNo].noSelected++;
				}
				// 同一运单号下已勾选的
				if (item.isChecked && item.firstSend) {
					ydNoObj[item.ydNo].selected++;
					selectYdNoArr.push(item.ydNo);
				}
				// 同一运单号下已发货的
				if (!item.firstSend) {
					ydNoObj[item.ydNo].alreadySend++;
				}
				ydNoObj[item.ydNo].oidArr.push(item.oid);
			}
			if (item.oid && item.preparedNum === item.goodsNum && !item.isAbnormal && item.orderStatus === 'WAIT_SELLER_SEND_GOODS' && !item.isChecked) selectAllWaitSend = false;
		});

		// let canPrintList = data.filter(item => item.oid && item.preparedNum === item.goodsNum && !item.isAbnormal);
		// 有先发货的在后面进行判断
		if (!isNotShowBeforePrintModal && !selectAllWaitSend && !local.get('scanPrint.waitSendGoodsNotCheck') && !selectYdNoArr.length) {
			setIsShowBeforePrintModal(true);
		} else {
			partPrint(isAutoPrint, historySids, isForcePrint, ydNoObj, logReprint);
		}
	}, 1000, { leading: true, trailing: false });

	// 校验吊牌打印机和模板
	const checkTagPrintConfig:()=>{[k:string]:any} = async() => {
		return getScanPrintSetting().then(res => {
			const isPrintTag = res?.scanSet?.isPrintTag;
			if (isPrintTag) {
				const { tagPrinter, printer } = scanPrintFormSetting || {};
				if (!tagPrinter) {
					return ({
						success: false,
						reason: "请先设置吊牌打印机"
					});
				}
				if (tagPrinter === printer) {
					return ({
						success: false,
						reason: "吊牌打印机不能和快递单打印机相同"
					});
				}
				return userStore.getSystemSetting().then(res => {
					const printTagSet = res.printTagSet || "{}";
					try {
						const printTagSetObj = JSON.parse(printTagSet);
						const { tagTemplateSwitch, } = printTagSetObj;
						if (tagTemplateSwitch == TemplateEnum["无"]) {
							return ({
								success: false,
								reason: "吊牌模板未设置"
							});
						}
						return ({
							success: true
						});
					} catch (e) { console.log(e); }
				});
			}
			return ({
				success: true
			});
		});
	};

	const partPrint = async(isAutoPrint:boolean, historySids?:string[], isForcePrint?:boolean, ydNoObj?:any, logReprint?:boolean) => {
		console.log('%c [ partPrint ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', isAutoPrint, historySids, isForcePrint, ydNoObj, logReprint);
		// 如果开启了吊牌打印，吊牌打印机和快递单打印机需要互斥
		const { success, reason } = await checkTagPrintConfig();
		if (!success) {
			message.error(reason);
			customLogPost(`校验吊牌打印机和模板`);
			return;
		}
		console.log('data:::', data);
		let checkedList = data.filter(item => item.isChecked);
		if (!checkedList.length) {
			message.error('请先勾选要打印的商品');
			customLogPost(`请先勾选要打印的商品`);
			return;
		}
		// 单笔先发货订单直接触发打印 快递单模版切换慢于触发自动打印时机
		setTimeout(() => {
			makeTradeData4ScanPrint(checkedList, historySids, isForcePrint, ydNoObj, isAutoPrint, logReprint);
		}, 0);

	};
	const modifyMemo = (e: any) => {
		console.log('currentTakeGoodsLabelInfo:', currentTakeGoodsLabelInfo);
		e.stopPropagation();
		runInAction(() => {
			setModifyMemoPackage(takeGoodsLabelInfos);
			setIsShowBatchModifyMemoModal(true);
		});
	};
	const changeInfo = (params:any) => {
		setTakeGoodsLabelInfos(takeGoodsLabelInfos.map(item => {
			item.sellerMemo = item._sellerMemo;
			item.sellerFlag = item._sellerFlag;
			item.sellerFlagTag = item._sellerFlagTag;
			return item;
		}));
	};

	// 仅展当前扫码标签对应订单
	let isShowOne = scanPrintSetting?.scanSet?.buyerMessageFlagShowStyle === buyerMessageFlagShowStyleEnum.仅展当前扫码标签对应订单;

	const hasJingxi = packageInfoVos?.[0]?.serviceTagList?.includes('jingxi') || false;	// 重新展示显示信息

	const handleScanPrintTradeInfoChange = () => {
		setScanPrintTradeInfoVisible(false);
	};

	return (
		<div className={ cs(s["ScanPrintTradeInfo"]) }>
			<div className={ cs(s["tradeInfo"], s[viewSize]) } id="ScanPrintTradeInfo">
				<WaresInfo
					previewPicSize={ previewSize === PreviewSize.小 ? 0 : (previewSize === PreviewSize.大 ? 2 : 1) }
					imgSize={ viewSize == ViewSize.小 ? 200 : (viewSize == ViewSize.中 ? 300 : 400) }
					imgUrl={ getImgURL(currentTakeGoodsLabelInfo) }
					noScale
				/>
				{
					scanPrintShowListSet?.filter(item => item.selected)?.map((col) => (
						<React.Fragment key={ col.id }>
							{col.condition === "platformInfo" && (
								<div className="r-flex r-pl-8 r-mb-4">
									<div className="r-flex-1">
										<div>
											<span className={ cs(s["tradeInfo-line"]) } style={ { paddingLeft: 0 } }>
												店铺：<span className={ cs("r-bold") }>{!userStore.hasFieldsPermission(FieldsPermissionEnum.供应商店铺信息) && isSourceScm(currentTakeGoodsLabelInfo) ? '****' : showShortNick ? currentTakeGoodsLabelInfo.sellerAbbreviation : currentTakeGoodsLabelInfo.sellerNick}</span>
											</span>
										</div>
										<div className={ cs(s["tradeInfo-line"], "r-flex r-ai-c") } style={ { padding: 0, marginBottom: 0 } }>
											<span style={ { flexShrink: 0 } }>昵称：</span>
											<BuyerNickComp ptTid={ currentTakeGoodsLabelInfo?.ptTid || '' } tid={ currentTakeGoodsLabelInfo.tid } encryptuid={ currentTakeGoodsLabelInfo?.buyerOpenUid } platform={ currentTakeGoodsLabelInfo.platform } buyerNick={ currentTakeGoodsLabelInfo.buyerNick } sellerId={ currentTakeGoodsLabelInfo?.sellerId } />
										</div>
									</div>
									<div
										style={ {
											marginRight: 8,
											width: viewSize == ViewSize.小 ? 30 : (viewSize == ViewSize.中 ? 40 : 50),
											height: viewSize == ViewSize.小 ? 30 : (viewSize == ViewSize.中 ? 40 : 50),
										} }
									><PlatformIcon fontSize={ viewSize == ViewSize.小 ? 30 : (viewSize == ViewSize.中 ? 40 : 50) } platform={ currentTakeGoodsLabelInfo.platform } />
									</div>
								</div>
							)}
							{col.condition === "labelId" && (
								<span className={ cs(s["tradeInfo-line"]) }>
									唯一码：{searchScanPrintFormSetting?.id}
								</span>
							)}
							{col.condition === "tid" && (
								<span className={ cs(s["tradeInfo-line"]) }>
									系统单号：{currentTakeGoodsLabelInfo.tid}
									{tradeCount > 1 ? (
										<span
											style={ { transform: "scale(0.9)" } }
											className="r-trade-merge"
										>
											合单
										</span>
									) : ''}
								</span>
							)}
							{col.condition === "ptTid" && (
								<span className={ cs(s["tradeInfo-line"]) }>
									订单编号：{currentTakeGoodsLabelInfo?.ptTid || ''}
								</span>
							)}
							{col.condition === "tradeLabel" && (
								<div className="r-flex r-jc-sb">
									<span className={ cs(s["tradeInfo-line"]) } style={ { padding: '0 0 0 4px' } }>订单标识：</span>
									{
										packageInfoVos?.[0]?.trades?.length > 0 && (
											<div className="r-flex-1">
												<TradeLabelCom pack={ packageInfoVos?.[0] } colWidth="auto" />
											</div>
										)
									}
								</div>
							)}
							{col.condition === "serverLabel" && (
								<div className="r-flex r-jc-sb">
									<span className={ cs(s["tradeInfo-line"]) } style={ { padding: '0 0 0 4px' } }>订单异常：</span>
									{
										packageInfoVos?.[0]?.trades?.length > 0 && (
											<div className="r-flex-1">
												<TradeExceptionCom pack={ packageInfoVos?.[0] } colWidth="auto" />
											</div>
										)
									}
								</div>
							)}
							{col.condition === "title" && (
								<span className={ cs(s["tradeInfo-line"]) }>商品名称：{currentTakeGoodsLabelInfo.title}</span>
							)}
							{col.condition === "sysItemAlias" && (
								<span className={ cs(s["tradeInfo-line"]) }>{getVersionConstants('简称')}：{currentTakeGoodsLabelInfo.sysItemAlias}</span>
							)}
							{col.condition === "outerId" && (
								<span className={ cs(s["tradeInfo-line"]) }>商家编码：{currentTakeGoodsLabelInfo.outerId}</span>
							)}
							{col.condition === "skuName" && (
								<span className={ cs(s["tradeInfo-line"]) }>规格名称：{currentTakeGoodsLabelInfo.skuName}</span>
							)}
							{col.condition === "sysSkuAlias" && (
								<span className={ cs(s["tradeInfo-line"]) }>
									规格别名：<span className="r-bold">{currentTakeGoodsLabelInfo.sysSkuAlias}</span>
								</span>
							)}
							{col.condition === "outerSkuId" && (
								<span className={ cs(s["tradeInfo-line"]) }>规格商家编码：{currentTakeGoodsLabelInfo.outerSkuId}</span>
							)}
							{col.condition === "sysSkuOuterId" && (
								<>
									{isShowZeroStockVersion ? '' : (
										<span className={ cs(s["tradeInfo-line"]) }>
											货品规格编码：{currentTakeGoodsLabelInfo.sysSkuOuterId}
										</span>
									)}
								</>
							)}
							{col.condition === "orderStatus" && (
								<span className={ cs(s["tradeInfo-line"]) }>
									订单状态：{getTradeStatusLabel(currentTakeGoodsLabelInfo.orderStatus, false)}
								</span>
							)}
							{col.condition === "orderPrintStatus" && (
								<span className={ cs(s["tradeInfo-line"]) }>
									打印状态：{ScanPrintPrintStatus[currentTakeGoodsLabelInfo.orderPrintStatus || 'UN_PRINT']}
								</span>
							)}
							{col.condition === "ydNo" && (
								<span className={ cs(s["tradeInfo-line"]) }>
									快递单号：{currentTakeGoodsLabelInfo.ydNo}
								</span>
							)}
							{col.condition === "receiverInfo" && (
								<span className={ cs(s["tradeInfo-line"]) }>
									地址：{currentTakeGoodsLabelInfo.receiverProvince || ""}
									{currentTakeGoodsLabelInfo.receiverProvince === currentTakeGoodsLabelInfo.receiverCity ? "" : currentTakeGoodsLabelInfo.receiverCity}
									{currentTakeGoodsLabelInfo.receiverCounty || ""}
									{currentTakeGoodsLabelInfo.receiverTown}
								</span>
							)}
							{col.condition === "sellerMemo" && (
								<>
									{/* 需要展示所有合单的留言备注 */}
									<div className={ cs(s["tradeInfo-line"]) } style={ { fontSize: "16px" } }>
										备注：
										{takeGoodsLabelInfos.map((packItem, index) => {
											let hasContent = packItem.sellerMemo || packItem.sellerFlag > 0;
											let isShow = !isShowOne || (isShowOne && currentTakeGoodsLabelInfo.tid === packItem.tid);
											return hasContent && isShow ? (
												<span>
													{getTradeFlag(0, null, packItem?.sellerFlag)}
													{getTradeFlagTag(packItem?.sellerFlag, packItem?.sellerFlagTag)}
													<span className="r-mr-6 r-bold">{packItem.sellerMemo}</span>
													{(index != takeGoodsLabelInfos.length - 1 && hasContent && isShow) ? <br /> : ''}
												</span>
											) : '';
										})}
										{!hasJingxi && (sellerEditFlag
											? <span className="r-pointer r-ml-12" onClick={ modifyMemo }><Icon type="bianji" /></span>
											: <span className="r-pointer r-ml-12" onClick={ modifyMemo }><Icon type="tianjiabeizhu" /></span>
										)}
									</div>
								</>
							)}
							{col.condition === "buyerMessage" && (
								<span className={ cs(s["tradeInfo-line"], "r-fs-16") } style={ { fontSize: "16px" } }>
									留言：
									{
										takeGoodsLabelInfos.map(packItem => {
											let hasContent = packItem.buyerMessage;
											let isShow = !isShowOne || (isShowOne && currentTakeGoodsLabelInfo.tid === packItem.tid);
											return hasContent && isShow ? (
												<>
													<span className="r-bold r-fs-16">{packItem.buyerMessage}</span>
													{hasContent && isShow ? <br /> : ''}
												</>
											) : '';
										})
									}
								</span>
							)}
						</React.Fragment>
					))
				}

				<div className={ s["view-config-contaniner"] }>
					<Button
						type="link"
						onClick={
							() => setScanPrintTradeInfoVisible(true)
						}
					>显示设置
					</Button>
				</div>
			</div>

			<div style={ { width: windowSize?.ww } }>
				{/* 列配置 */}
				<div className="r-flex r-jc-fe r-relative" style={ { top: '-10px' } }>
					<ColSortSetting />
				</div>

				<Table
					rowClassName={ (record, index) => `${record.isChecked ? 'ant-table-row-selected' : ''}` }
					columns={ getColumns }
					dataSource={ data }
					pagination={ false }
					scroll={ { x: windowSize?.ws, y: `calc(100vh - 300px)` } }
					className={ styleModule.scanPrintTable }
				/>


				<div className={ cs(s["tradeInfo-opt-container"]) }>
					{/* 兼容处理 防止出现备货数量大于总数量的情况 */}
					<span className="r-fs-14">总宝贝数：{Math.min(scanPrintTempInfo?.scannedGoodsCount < 0 ? 0 : scanPrintTempInfo?.scannedGoodsCount, scanPrintTempInfo?.goodsTotalCount)}/{scanPrintTempInfo?.goodsTotalCount}</span>
					<Button type="default" data-point={ Pointer.扫描打印_跳过_点击 } className="r-ml-24 r-mr-24" onClick={ skipTrade }>暂时跳过</Button>
					<Button
						type="primary"
						data-point={ Pointer.扫描打印_打印_手动打印点击 }
						onClick={ () => {
							timer.start(`手动打印`);
							beforePartPrint({ isForcePrint: false, isClick: true });
							timer.stop(`手动打印`, {
								type: logTypeEnum['小标签']
							});
						} }
					>打印
					</Button>
					<Button
						type="primary"
						// data-point={ Pointer.扫描打印_打印_手动打印点击 }
						onClick={ () => {
							timer.start(`强制补打`);
							beforePartPrint({ isForcePrint: true, isClick: true });
							timer.stop(`强制补打`, {
								type: logTypeEnum['小标签']
							});
						} }
						className="r-ml-24"
						loading={ forcePrintLoading }
					>强制补打
					</Button>

				</div>
			</div>

			{/* hack takeGoodsLabelInfos值不更新的问题 */}
			<div style={ { display: 'none' } }>
				{takeGoodsLabelInfos.map((item, index) => {
					return item.takeGoodsLabelInfos.map((info, jIndex) => (
						<div key={ `${index}-${jIndex}` }>{info.preparedNum}/{info.manualPrepare}</div>
					));
				})}
			</div>

			<Modal
				title="提示"
				okText="打印"
				closable={ false }
				visible={ isShowBeforePrintModal }
				footer={ [
					<Button
						type="primary"
						onClick={ () => {
							beforePartPrint({ isForcePrint: false, isNotShowBeforePrintModal: true });
							setIsShowBeforePrintModal(false);
							customLogPost(`有待发货的商品未勾选打印，继续打印`);
						} }
					>打印
					</Button>,
					<Button
						onClick={ () => {
							setIsShowBeforePrintModal(false);
							customLogPost(`有待发货的商品未勾选打印，取消打印`);
						} }
					>取消
					</Button>,
					<Checkbox
						className="r-ml-8"
						onChange={ e => {
							if (e.target.checked) {
								local.set('scanPrint.waitSendGoodsNotCheck', true);
							} else {
								local.remove('scanPrint.waitSendGoodsNotCheck');
							}
						} }
					>不再提示
					</Checkbox>
				] }
			>
				有待发货的商品未勾选打印，是否继续打印？
			</Modal>
			{isShowBatchModifyMemoModal ? <BatchModifyMemoModal fromPage="scanPrint" onChange={ changeInfo } /> : ''}

			{ scanPrintTradeInfoVisible && (
				<CustomScanPrintTradeInfoModal
					onOk={ handleScanPrintTradeInfoChange }
					onClose={ () => setScanPrintTradeInfoVisible(false) }
				/>
			) }
		</div>
	);
});

export default ScanPrintTradeInfo;
