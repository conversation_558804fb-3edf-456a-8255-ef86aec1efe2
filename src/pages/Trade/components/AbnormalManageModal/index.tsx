import { Button, Modal, Radio, Select, Transfer, Spin, Divider, Space, Tooltip } from "antd";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { observer } from "mobx-react";
import _ from "lodash";
import { useBoolean, useRequest } from "ahooks";
import { CopyOutlined, ExclamationCircleOutlined, QuestionCircleOutlined } from "@ant-design/icons";
import cs from 'classnames';
import { Link, useHistory } from "react-router-dom";
import { useAliveController } from "react-activation";
import message from '@/components/message';
import { tradeStore } from "@/stores";
import s from './index.module.scss';
import { AbnormalItemProps, abnormalManage, checkHasMultiAbnormal, handleAbnormalOrderChoice } from "./abnormalOrder";
import { TradeChoiceType, TradeOptEnum } from "@/utils/enum/trade";
import Icon from "@/components/Icon";
import { copyToPaste, splitFxgTid } from "@/utils";
import { local } from "@/libs/db";
import { PRINT_BATCH_ABNORMAL_ORDER_SET_IGNORE_KEY, PRINT_BATCH_ABNORMAL_TRADE_SYSSKUIDLIST } from "@/constants";
import event from '@/libs/event';
import { TradeOperateLogAddApi } from "@/apis/trade";
import useGetState from "@/utils/hooks/useGetState";
import { AbnormalFromType } from "@/pages/Trade/components/BottomCom/checkAbnormalUtil";

interface AbnormalManageModalProps {
	visible?: boolean;
	modalType?: string;
	data?:object;
	isPreShip?: boolean;
	onClose?:()=> void;
	onSubmitAfter?:(data?:[], hasRefundIds?:boolean)=> void;
}

export interface IAbnormalItem {
	key: string;
	desc: string;
	title?: string;
}

const typeText = {
	PRINT: '打印',
	SEND: '发货',
	PRESEND: '申请',
	[AbnormalFromType.波次生成]: '生成拣货波次',
};

// 打印、发货、申请 不可强制操作
const BREAK_ABNORMAL = {
	PRINT: [
		{ key: 'SCM_RELATION_UNBIND', desc: '供分销关系异常' },
		{ key: 'SCM_DISTRIBUTOR_STORE_UNBIND', desc: '分销订单已发起撤回/已被撤回' },
		{ key: 'SCM_TRADE_SHIP', desc: '分销订单非待发货' },
		{ key: 'SCM_TRADE_REFUND', desc: '分销订单产生售后' },
		{ key: 'mismatchingBreak', desc: '订单业务类型与模板不匹配' },
		// { key: 'onlineShip', desc: '线上已发货' },
		{ key: 'unSelect', desc: '订单未勾选任何宝贝', title: "请展开订单右侧详情勾选商品后重试" },
		{ key: 'isPending', desc: '订单被挂起' },
		{ key: 'appointmentArrival', desc: '承诺日达订单快递不匹配' },
		{ key: 'needSerialNumber', desc: '未填写商品识别码' },
		{ key: 'scmRelationAbnormal', desc: '分销关系异常' },
		{ key: 'scmOrderNotExist', desc: '分销订单不存在' },
		// { key: 'changeAddress', desc: '买家修改地址' },
		{ key: 'modifiedOnline', desc: '线上改商品异常' },
		{ key: 'unFxgPromiseLogistics', desc: '订单所选快递与指定快递不符' },
		{ key: 'refundBreak', desc: '退款中订单' },
		{ key: 'orderErrorStatus', desc: '订单状态异常' },
		// { key: 'alreadyRefundBreak', desc: '含已退款成功/已取消商品' }
		{ key: 'unStockBreak', desc: '库存不足订单' },
		{ key: 'unSync', desc: '店铺长时间未同步(12小时)', title: '订单对应店铺长时间未同步，订单状态可能不与平台不一致，点击【立即同步】该店铺后重新打印发货' },
		{ key: 'bic_order', desc: 'BIC质检订单' },
		{ key: 'abnormalSysGiftBreak', desc: '系统赠品异常', title: '存在子订单售后关闭，需复核当前系统赠品是否无误' },
		{ key: 'abnormalSysGiftCheck_14', desc: '订单正在执行自动策略', title: '订单此时正在执行自动策略（任务进度可至右上角任务中心查看），订单可能会产生变更，请稍后重试' },
		{ key: 'abnormalSysGiftCheck_15', desc: '列表加载商品数据与数据库商品数据不一致', title: '如执行赠品策略后可能会导致系统赠品添加或删除，请刷新列表后重试' },
		{ key: 'unAcceptOrder', desc: '得物订单商家未接单', title: '目前需前往店铺后台接单' },
		{ key: 'exchangeRefundAbnormal', desc: '换货手工单对应的售后单异常', },
		{ key: 'abnormalXsdPtpsBreak', desc: '小时达平台配送订单' },
		{ key: 'gfcf', desc: '官方仓发不可打印发货' },
		{ key: 'bizWaveAbnormal', desc: '波次拣货异常' }, 
		{ key: 'lowProfitBreak', desc: '订单预估利润较低' }
	],
	SEND: [
		{ key: 'SCM_RELATION_UNBIND', desc: '供分销关系异常' },
		{ key: 'SCM_DISTRIBUTOR_STORE_UNBIND', desc: '分销订单已发起撤回/已被撤回' },
		{ key: 'SCM_TRADE_SHIP', desc: '分销订单非待发货' },
		{ key: 'SCM_TRADE_REFUND', desc: '分销订单产生售后' },
		{ key: 'mismatchingBreak', desc: '订单业务类型与模板不匹配' },
		// { key: 'onlineShip', desc: '线上已发货' },
		{ key: 'unSid', desc: '订单未生成快递单号', title: "您选择的订单含有未打印的宝贝，请您仔细核对是否继续发货" },
		{ key: 'unSelect', desc: '订单未勾选任何宝贝' },
		{ key: 'isPending', desc: '订单挂起' },
		{ key: 'shipStatus', desc: '订单在当前页已操作过发货' },
		{ key: 'appointmentArrival', desc: '承诺日达订单快递不匹配' },
		{ key: 'needSerialNumber', desc: '未填写商品识别码' },
		{ key: 'scmRelationAbnormal', desc: '分销关系异常' },
		{ key: 'scmOrderNotExist', desc: '分销订单不存在' },
		// { key: 'unStock', desc: '库存不足订单' },
		// { key: 'changeAddress', desc: '买家修改地址' },
		{ key: 'splitTrade', desc: '订单勾选部分商品不支持自动发货' },
		{ key: 'modifiedOnline', desc: '线上改商品异常' },
		{ key: 'refundBreak', desc: '退款中订单' },
		{ key: 'unStockBreak', desc: '库存不足订单' },
		{ key: 'unSync', desc: '店铺长时间未同步(12小时)', title: '订单对应店铺长时间未同步，订单状态可能不与平台不一致，点击【立即同步】该店铺后重新打印发货' },
		{ key: 'unFxgPromiseLogistics', desc: '订单所选快递与指定快递不符' },
		{ key: 'alreadyRefundBreak', desc: '含已退款成功/已取消商品' },
		{ key: 'orderErrorStatus', desc: '订单状态异常' },
		{ key: 'bic_order', desc: 'BIC质检订单' },
		{ key: 'abnormalSysGiftBreak', desc: '系统赠品异常', title: '存在子订单售后关闭，需复核当前系统赠品是否无误' },
		{ key: 'abnormalSysGiftCheck_14', desc: '订单正在执行自动策略', title: '订单此时正在执行自动策略（任务进度可至右上角任务中心查看），订单可能会产生变更，请稍后重试' },
		{ key: 'abnormalSysGiftCheck_15', desc: '列表加载商品数据与数据库商品数据不一致', title: '如执行赠品策略后可能会导致系统赠品添加或删除，请刷新列表后重试' },
		{ key: 'unAcceptOrder', desc: '得物订单商家未接单', title: '目前需前往店铺后台接单' },
		{ key: 'exchangeRefundAbnormal', desc: '换货手工单对应的售后单异常', },
		{ key: 'abnormalXsdPtpsBreak', desc: '小时达平台配送订单' },
		{ key: 'gfcf', desc: '官方仓发不可打印发货' },
		{ key: 'aezy', desc: 'AE自营订单不可发货' },
		{ key: 'bizWaveAbnormal', desc: '波次拣货异常' }, // 添加波次拣货异常
		{ key: 'lowProfitBreak', desc: '订单预估利润较低' }
	],
	PRESEND: [
		{ key: 'SCM_RELATION_UNBIND', desc: '供分销关系异常' },
		{ key: 'SCM_DISTRIBUTOR_STORE_UNBIND', desc: '分销订单已发起撤回/已被撤回' },
		{ key: 'SCM_TRADE_SHIP', desc: '分销订单非待发货' },
		{ key: 'SCM_TRADE_REFUND', desc: '分销订单产生售后' },
		{ key: 'mismatchingBreak', desc: '订单业务类型与模板不匹配' },
		{ key: 'unSelect', desc: '订单未勾选任何宝贝', title: "请展开订单右侧详情勾选商品后重试" },
		{ key: 'isPending', desc: '订单被挂起' },
		// { key: 'changeAddress', desc: '买家修改地址' },
		{ key: 'appointmentArrival', desc: '承诺日达订单快递不匹配' },
		{ key: 'needSerialNumber', desc: '未填写商品识别码' },
		{ key: 'modifiedOnline', desc: '线上改商品异常' },
		{ key: 'refundBreak', desc: '退款中订单' },
		{ key: 'unStockBreak', desc: '库存不足订单' },
		{ key: 'unSync', desc: '店铺长时间未同步(12小时)', title: '订单对应店铺长时间未同步，订单状态可能不与平台不一致，点击【立即同步】该店铺后重新打印发货' },
		{ key: 'unFxgPromiseLogistics', desc: '订单所选快递与指定快递不符' },
		{ key: 'alreadyRefundBreak', desc: '含已退款成功/已取消商品' },
		{ key: 'orderErrorStatus', desc: '订单状态异常' },
		{ key: 'bic_order', desc: 'BIC质检订单' },
		{ key: 'abnormalSysGiftBreak', desc: '系统赠品异常', title: '存在子订单售后关闭，需复核当前系统赠品是否无误' },
		{ key: 'abnormalSysGiftCheck_14', desc: '订单正在执行自动策略', title: '订单此时正在执行自动策略（任务进度可至右上角任务中心查看），订单可能会产生变更，请稍后重试' },
		{ key: 'abnormalSysGiftCheck_15', desc: '列表加载商品数据与数据库商品数据不一致', title: '如执行赠品策略后可能会导致系统赠品添加或删除，请刷新列表后重试' },
		{ key: 'unAcceptOrder', desc: '得物订单商家未接单', title: '目前需前往店铺后台接单' },
		{ key: 'exchangeRefundAbnormal', desc: '换货手工单对应的售后单异常', },
		{ key: 'abnormalXsdPtpsBreak', desc: '小时达平台配送订单' },
		{ key: 'gfcf', desc: '官方仓发不可打印发货' },
		{ key: 'wave_no', desc: '已生成拣货波次' }, // 已生成拣货波次
		{ key: 'lowProfitBreak', desc: '订单预估利润较低' }
	],
	[AbnormalFromType.波次生成]: [
		// { key: 'SCM_RELATION_UNBIND', desc: '供分销关系异常' },
		// { key: 'SCM_DISTRIBUTOR_STORE_UNBIND', desc: '分销订单已发起撤回/已被撤回' },
		// { key: 'SCM_TRADE_SHIP', desc: '分销订单非待发货' },
		// { key: 'SCM_TRADE_REFUND', desc: '分销订单产生售后' },
		{ key: 'mismatchingBreak', desc: '订单业务类型与模板不匹配' },
		{ key: 'unSid', desc: '未获取单号', title: "您选择的订单含有未申请单号的宝贝，请先申请单号" }, // 未获取单号
		{ key: 'unSelect', desc: '订单未勾选任何宝贝' },
		{ key: 'isPending', desc: '订单挂起' },
		// { key: 'shipStatus', desc: '订单在当前页已操作过发货' },
		{ key: 'appointmentArrival', desc: '承诺日达订单快递不匹配' },
		{ key: 'needSerialNumber', desc: '未填写商品识别码' },
		// { key: 'scmRelationAbnormal', desc: '分销关系异常' },
		// { key: 'scmOrderNotExist', desc: '分销订单不存在' },
		{ key: 'splitTrade', desc: '订单勾选部分商品不支持自动发货' },
		// { key: 'modifiedOnline', desc: '线上改商品异常' },
		{ key: 'refundBreak', desc: '退款中订单' },
		{ key: 'unStockBreak', desc: '库存不足订单' },
		{ key: 'unSync', desc: '店铺长时间未同步(12小时)', title: '订单对应店铺长时间未同步，订单状态可能不与平台不一致，点击【立即同步】该店铺后重新打印发货' },
		{ key: 'unFxgPromiseLogistics', desc: '订单所选快递与指定快递不符' },
		{ key: 'alreadyRefundBreak', desc: '含已退款成功/已取消商品' },
		{ key: 'orderErrorStatus', desc: '订单状态异常' },
		{ key: 'bic_order', desc: 'BIC质检订单' },
		{ key: 'abnormalSysGiftBreak', desc: '系统赠品异常', title: '存在子订单售后关闭，需复核当前系统赠品是否无误' },
		{ key: 'abnormalSysGiftCheck_14', desc: '订单正在执行自动策略', title: '订单此时正在执行自动策略（任务进度可至右上角任务中心查看），订单可能会产生变更，请稍后重试' },
		{ key: 'abnormalSysGiftCheck_15', desc: '列表加载商品数据与数据库商品数据不一致', title: '如执行赠品策略后可能会导致系统赠品添加或删除，请刷新列表后重试' },
		{ key: 'unAcceptOrder', desc: '得物订单商家未接单', title: '目前需前往店铺后台接单' },
		{ key: 'exchangeRefundAbnormal', desc: '换货手工单对应的售后单异常', },
		{ key: 'abnormalXsdPtpsBreak', desc: '小时达平台配送订单' },
		{ key: 'gfcf', desc: '官方仓发不可打印发货' },
		{ key: 'aezy', desc: 'AE自营订单不可发货' },
		{ key: 'unBindGoodsBreak', desc: '商品未绑定本地货品' }, // 波次的商品未绑定本地货品不可强制
		{ key: 'wave_no', desc: '已生成拣货波次' }, // 已生成拣货波次
		{ key: 'unWarehouseSlotNameBreak', desc: '商品未设置货位' }, // 商品未设置货位
		{ key: 'scmOrderBreak', desc: '分销订单不支持生成波次' }, // 分销订单不支持波次
		{ key: 'lowProfitBreak', desc: '订单预估利润较低' }
	]
};

// 可强制操作
const IGNORE_ABNORMAL = [
	{ key: 'onlineShip', desc: '线上已发货' },
	{ key: 'abnormalOnlineRefunded', desc: '线上已退款' },
	{ key: 'changeAddress', desc: '改地址异常' },
	{ key: 'refund', desc: '退款中订单' },
	{ key: 'unStock', desc: '库存不足订单' },
	{ key: 'abnormalAddress', desc: '异常地址订单（双地址）' },
	{ key: 'unArrive', desc: '快递不可达订单' },
	{ key: 'unBindGoods', desc: '商品未绑定本地货品' },
	{ key: 'unPromiseLogistics', desc: '订单所选快递与指定快递不符' },
	{ key: 'addGoodsNoSend', desc: '有待发货的系统商品' },
	{ key: 'hasShipped', desc: '存在已发货订单商品' },
	{ key: 'hasPrinted', desc: '存在已打印订单商品' },
	{ key: 'mismatching', desc: '订单业务类型与模板不匹配' },
	{ key: 'abnormalSysGift', desc: '系统赠品异常' },
	{ key: 'exchangeRefund', desc: '换货手工单对应的售后单异常', },
	{ key: 'abnormalXsdPtps', desc: '小时达平台配送订单' },
	{ key: 'lowProfit', desc: '订单预估利润较低' }
];

// 打印发货异常拦截弹框
const AbnormalManageModal = observer((props:AbnormalManageModalProps) => {
	const { visible, modalType = "PRINT", data, onClose, onSubmitAfter, isPreShip = false } = props;
	console.log('%c [ 异常拦截数据 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', data);
	const history = useHistory();
	const [loading] = useBoolean(false);
	const [ignoreChoiceData, setIgnoreChoiceData, getIgnoreChoiceData] = useGetState<any[]>([]);
	const [isExceptionHandled, setIsExceptionHandled] = useState(false); // 是否全部强制
	const { refresh } = useAliveController();
	// 忽略异常里面有没有退款订单
	const [hasRefundIds, setHasRefundIds] = useState(false);
	const {
		tradeListStore: {
			checkedCount,
			handleChoiceChange,
			list,
			setTempAbnormalIgnoreKey,
			tempAbnormalIgnoreKey,
		},
	} = tradeStore;

	const updateIgnoreChoiceData = () => {
		if (tempAbnormalIgnoreKey.length) {
			setIgnoreChoiceData(_.cloneDeep(tempAbnormalIgnoreKey));
		}
	};

	// 强制按钮文字
	const btnText = useMemo(() => {
		if (modalType !== AbnormalFromType.波次生成) {
			return typeText[modalType];
		} else {
			return '生成';
		}
	}, [modalType]);

	useEffect(() => {
		console.log('datadatadatadatadata', data);
		event.on(PRINT_BATCH_ABNORMAL_ORDER_SET_IGNORE_KEY, updateIgnoreChoiceData);
		return () => {
			event.off(PRINT_BATCH_ABNORMAL_ORDER_SET_IGNORE_KEY, updateIgnoreChoiceData);
		};
	// eslint-disable-next-line react-hooks/exhaustive-deps
	}, []);

	const isIgnoreAllTrade = useMemo(() => {
		let result = true;
		IGNORE_ABNORMAL.forEach(ignoreItem => {
			if (data[modalType]?.[ignoreItem.key]?.num !== 0 && !ignoreChoiceData.includes(ignoreItem.key)) {
				result = false;
			}
		});
		return result;
	}, [data, ignoreChoiceData, modalType]);

	// 点击跳过
	const handleOk = _.debounce(() => {
		onCancel(true);
		onSubmitAfter([], hasRefundIds); 
		if (modalType === 'PRINT') {
			setTempAbnormalIgnoreKey(_.cloneDeep(ignoreChoiceData));
		}
	}, 500, {
		leading: true,
		trailing: false
	});

	// 点击强制打印、强制发货、强制申请、强制生成拣货波次
	const onIgnoreOrder = (key:string) => {
		if (ignoreChoiceData.includes(key)) {
			message.warning('当前异常已忽略！');
			return;
		}
		let togetherIds = data[modalType][key]?.togetherIds || [];
		let operateType = TradeOptEnum.强制打印;
		if (modalType === 'SEND') {
			operateType = TradeOptEnum.强制发货;
		} else if (modalType === 'PRESEND') {
			operateType = TradeOptEnum.强制申请;
		} else if (modalType === AbnormalFromType.波次生成) {
			operateType = TradeOptEnum.强制生成拣货波次;
		}
		let operateContent = `${IGNORE_ABNORMAL.find(item => item.key === key)?.desc || ''} 允许强制${typeText[modalType]}`;
		let params = togetherIds.map(item => ({
			operateType,
			operateContent,
			operateResult: 1,
			tids: item,
			platform: list.find(listItem => listItem.togetherId.indexOf(item) > -1)?.platform,
		}));
		TradeOperateLogAddApi(params); // 添加订单操作日志

		if (key === 'refund') {
			// 有退款中的订单
			setHasRefundIds(true);
		}
			
		const { breakTogetherIds } = handleAbnormalOrderChoice(data, modalType);
		let cancelChoiceOrders: string[] = [];
		togetherIds.forEach((item: string) => {
			if (!breakTogetherIds?.includes(item) && !item.split('|').some(i => breakTogetherIds?.includes(i))) {
				cancelChoiceOrders.push(item);
			}
		});

		if (cancelChoiceOrders.length === 0) {
			message.warning(`当前订单存在中断${typeText[modalType]}的异常 无法忽略～`);
		} else {
			let uniqueIds = checkHasMultiAbnormal(data, modalType, [...ignoreChoiceData, key], cancelChoiceOrders);
			if (uniqueIds?.length > 0) {
				handleChoiceChange({ type: TradeChoiceType.勾选指定订单, cancelChoiceOrders: uniqueIds, noUpdateSids: true });
			}
			setIgnoreChoiceData([...ignoreChoiceData, key]);
		}
	};

	// 判断是否渲染异常
	const checkHasAbnormal = (isBreak:boolean) => {
		let infos = data[modalType];
		let hasAbnormal = false;
		for (const key in infos) {
			if (isBreak) {
				// 中断异常
				if (abnormalManage[modalType]?.breakOrder.includes(key) && infos[key]?.num > 0) {
					hasAbnormal = true;
				}
			} else if (abnormalManage[modalType]?.ignoreOrder.includes(key) && infos[key]?.num > 0) {
				// 其他异常
				hasAbnormal = true;
			}
		}
		return hasAbnormal;
	};

	const renderTips = (data:any) => {
		const { title } = data;
		if (!title) {
			return null;
		}
		return (
			<Tooltip title={ () => <div>{title}</div> }>
				<QuestionCircleOutlined className="r-pl-4" />
			</Tooltip>
		);
	};

	const renderBreakOrderInfo = () => {
		if (!checkHasAbnormal(true) || !visible) {
			return null;
		}
		return (
			<div className={ cs(s['abnormal-part']) }>
				{
					BREAK_ABNORMAL[modalType].map((item: IAbnormalItem) => {
						console.log(' data[modalType]', item.key, data[modalType]);

						if (!data[modalType][item.key] || data[modalType][item.key]?.num === 0) {
							return null;
						}
						if (!isPreShip && item.key === 'splitTrade') {
							return null;
						}
						return (
							<div key={ item.key } className={ cs('r-flex', 'r-ai-c', 'r-lh-32') } >
								<div className={ cs(s['abnormal-item']) }>
									<span>{item.desc}</span>
									{renderCopyIcon(item)}
								</div>
							</div>
						);
					})
				}
			</div>
		);
	};

	const goStockInfoPage = (item: AbnormalItemProps) => {
		local.set(PRINT_BATCH_ABNORMAL_TRADE_SYSSKUIDLIST, item.sysSkuIds);
		// window.open('#/warehouse/stockInfo', '_blank');
		refresh('/warehouse/stockInfo');
		history.push('/warehouse/stockInfo');
	};

	const renderIgnoreOrderInfo = () => {
		if (!checkHasAbnormal(false) || !visible) {
			return null;
		}
		console.log('ignoreChoiceData', ignoreChoiceData, IGNORE_ABNORMAL);

		return (
			<div className={ cs(s['abnormal-part']) }>
				{
					IGNORE_ABNORMAL.map((item: IAbnormalItem) => {
						// console.log(256, data, modalType);
						if (!data[modalType][item.key] || data[modalType][item.key]?.num === 0) {
							return null;
						}

						return (
							<div key={ item.key }>
								<div className={ cs('r-flex', 'r-ai-c') }>
									<div className={ cs(s['abnormal-item']) } style={ { color: ignoreChoiceData.includes(item.key) ? 'rgba(0,0,0,0.25)' : '' } }>
										<span>{item.desc}</span>
										{renderCopyIcon(item)}
									</div>
									<div className="r-flex r-ai-c">
										{ignoreChoiceData.includes(item.key) ? '' : (
											<Button
												type="link"
												className="r-fs-12 r-pd-0 r-mr-20"
												onClick={ () => { onIgnoreOrder(item.key); } }
											>
												{ignoreChoiceData.includes(item.key) ? '' : `强制${btnText}`}
											</Button>
										)}
										{item.key === 'unStock'
											? (
												<div
													onClick={ () => { goStockInfoPage(data[modalType][item.key]); } }
													className="r-fs-12 g-clickable"
												> 查看库存不足商品{'>'}
												</div>
											) : null}
									</div>
								</div>
								{
									item.key === 'unStock' && data[modalType][item.key]?.info?.length ? (
										<div className={ s['stock-wrapper'] }>
											{
												data[modalType][item.key]?.info?.map((i:any) => {
													return (
														<p key={ i.sysSkuId }>
															【{i?.titleShort}{i.skuPropertiesName ? '+' : null}{i?.skuPropertiesName}】需要
															<span className={ s['fc-FF4D4F'] }>{i?.num}</span>，目前库存
															<span className={ s['fc-FF4D4F'] }>{i?.stockNum}</span>
														</p>
													);
												})
											}
										</div>
									) : null
								}
							</div>
						);
					})
				}

			</div>
		);
	};

	const onCancel = (isClickSkip?:boolean) => {
		onClose();
		resetModal();
		setTempAbnormalIgnoreKey([]);
	};

	const resetModal = () => {
		setIgnoreChoiceData([]);
		setIsExceptionHandled(false);
		setHasRefundIds(false); 
	};

	const copyAbnormalTids = (abnormalType?: string) => {
		let modalTypeData = data[modalType];
		let togetherIds = [];
		
		if (abnormalType) {
			togetherIds = modalTypeData[abnormalType]?.togetherIds || [];
			copyToPaste(splitFxgTid(togetherIds.join(',')));
		} else {
			// 复制所有异常单号
			for (const key in modalTypeData) {
				if (Object.prototype.hasOwnProperty.call(modalTypeData, key)) {
					const item = modalTypeData[key];
					// 在对应的类型的异常里面才复制
					if (item.togetherIds?.length 
						&& (abnormalManage[modalType]?.breakOrder.includes(key) 
						|| abnormalManage[modalType]?.ignoreOrder.includes(key))
						// 排除已经被强制操作的异常类型
						&& !ignoreChoiceData.includes(key)
					) {
						// 不再区分打印发货和未生成运单号unSid
						togetherIds.push(...item.togetherIds);
					}
				}
			}
			// 复制前去重
			copyToPaste(splitFxgTid([...new Set(togetherIds)].join(',')));
		}
	};

	const copyAbnormalSids = () => {
		let modalTypeData = data[modalType];
		let sids = [];
		for (const key in modalTypeData) {
			if (Object.prototype.hasOwnProperty.call(modalTypeData, key)) {
				const item = modalTypeData[key];
				if (item.sids?.length) {
					sids.push(...item.sids);
				}
			}
		}
		console.log("复制的：", [...new Set(sids)].join(','));
		copyToPaste([...new Set(sids)].join(','));
	};

	const renderCopyIcon = (item: IAbnormalItem) => (
		<div className="r-flex r-ai-c" style={ { width: 100 } }>
			<div style={ { width: 75 } }>{data[modalType][item.key]?.num} 单{renderTips(item)}</div>
			<Tooltip title="复制该异常类型下所有订单编号">
				<CopyOutlined onClick={ () => { copyAbnormalTids(item.key); } } className={ cs('r-fc-black-65', 'r-pointer') } />
			</Tooltip>
		</div>
	);

	// 统一的关闭回调
	const handleCancel = () => {
		if (modalType === AbnormalFromType.波次生成) {
			event.emit('wave.abnormal.result', { isBreak: true });
		} 
		onCancel?.();
	};

	return (
		<div>
			<Modal
				centered
				title={ (
					<div className="r-flex">
						{
							modalType !== AbnormalFromType.波次生成 ? (
								<div className="r-flex">
									<ExclamationCircleOutlined style={ { color: '#FD8204', fontSize: 22, marginRight: 4 } } />{typeText[modalType]}异常提醒
								</div>
							) : (
								<div className={ s['abnormal-title'] }>
									生成拣货波次提醒
								</div>
							)
						}
					</div>
				) }
				width={ 700 }
				visible={ visible }
				closable={ false }
				keyboard={ false }
				maskClosable={ false }
				destroyOnClose
				footer={ null }
				zIndex={ 999 }
				onCancel={ handleCancel }
			>
				<Spin spinning={ loading }>
					{
						modalType !== AbnormalFromType.波次生成 ? (
							<p>本次勾选{typeText[modalType]}（{data['total']}单）存在以下异常情况，系统将取消异常订单勾选继续{typeText[modalType]}，如需{typeText[modalType]}异常订单请点击【
								<span style={ { color: '#FD8204' } }>强制{typeText[modalType]}</span>】
							</p>
						) : (
							<p>本次勾选{typeText[modalType]}（{data['total']}单）生成拣货波次时存在以下异常，如需继续生成请点击【
								<span style={ { color: '#FD8204' } }>强制{btnText}</span>】
							</p>
						)
					}
					
					<div
						className={ cs('r-flex', 'r-mt-16', 'r-mb-12', 'r-fd-c', s['abnormal-trade-wrapper']) }
						style={ { minHeight: 222, background: 'rgba(242, 242, 242, 1)', maxHeight: 500, overflowY: 'auto' } }
					>
						{renderBreakOrderInfo()}
						{renderIgnoreOrderInfo()}
					</div>
					<span className="g-clickable r-mr-12" onClick={ () => { copyAbnormalTids(); } }>复制所有异常订单编号</span>
					{
						modalType === "SEND" ? <span className="g-clickable" onClick={ () => { copyAbnormalSids(); } }>复制全部异常订单发货快递单号</span> : null
					}

					<span style={ { color: '#FD8204', float: 'right' } } className="g-clickable" onClick={ () => { history.push('/settings/system?introName=openAbnormalCheck'); } }>调整异常提醒设置</span>
				</Spin>

				<div className={ cs('r-flex r-ai-c r-jc-sb r-mt-32') }>
					<div>过滤异常后勾选（{checkedCount}单）</div>
					<Space>
						{checkedCount === 0 && <Button type="primary" onClick={ handleCancel }>我知道了</Button>}
						{
							modalType !== AbnormalFromType.波次生成 ? (
								<>
									{(isExceptionHandled || checkedCount !== 0) && <Button type="primary" onClick={ handleOk }>{isIgnoreAllTrade ? '' : '跳过异常 '}{typeText[modalType]}正常订单</Button>}
									{(isExceptionHandled || checkedCount !== 0) && <Button onClick={ handleCancel }>取消{typeText[modalType]}</Button>}
								</>
							) : (
								<>
									{(isExceptionHandled || checkedCount !== 0) && <Button type="primary" onClick={ handleOk }>{isIgnoreAllTrade ? '' : '跳过异常，'}继续生成拣货波次</Button>}
									{(isExceptionHandled || checkedCount !== 0) && <Button onClick={ handleCancel }>取消</Button>}
								</>
							)
						}
					</Space>
				</div>
			</Modal>
		</div>
	);
});

export default AbnormalManageModal;
