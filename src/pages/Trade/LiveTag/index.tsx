import React, {
	useEffect,
	useMemo,
	useState
} from "react";
import { EventSourcePolyfill } from "event-source-polyfill";
import { useHistory } from "react-router-dom";
import { observer } from "mobx-react";
import {
	Layout,
	Radio,
	Card,
	Button,
	Checkbox,
	Select,
	Form,
	Modal,
	InputNumber,
	Popover,
	Tabs as AntTabs,
	Tooltip
} from "antd";
import { ExclamationCircleOutlined, QuestionCircleFilled } from '@ant-design/icons';
import { cloneDeep } from "lodash";
import { getToken } from "@/utils/token";
import Icon from "@/components/Icon";
import ShopMultiSelect from "@/components-biz/ShopListSelect/shopMultiSelect";
import { getMultiShops } from "@/components-biz/ShopListSelect/shopListUtils";
import AuthorMultiSelect from '@/components-biz/ShopListSelect/AuthorMultiSelect';
import InputSelect from "@/components/Input/InputSelect";
import {
	TradeLiveTagCreateTask<PERSON><PERSON>,
	TradeLiveTagCloseTaskApi,
	TradeLiveTagLastTaskApi,
	TradeLiveTagTradeStatusCountApi,
	TradePrintSetUpdatePrintSetApi 
} from "@/apis/trade";
import memoFn from '@/libs/memorizeFn';
import { PLAT_HAND, PLAT_SPH, PLAT_XHS, PLAT_FXG } from "@/constants";
import { tradeStore } from "@/stores";
import PrintCenter from "@/print/index";
import LiveTypeTabs from './components/LiveTypeTabs';
import message from "@/components/message";
import { BQ_PRINT_TYPE } from "../constants";
import { ORDER_FILTER_OPTIONS } from "./constants";
import { EnumLabelTypeTabs } from '../LiveLog/constants';
import {
	TRADE_LABEL_OPTIONS,
	TRADE_EXCEPTION_OPTIONS
} from "@/pages/Trade/components/SearchContainer/constants";
import userStore from "@/stores/user";
import Pointer from "@/utils/pointTrack/constants";
import sendPoint from "@/utils/pointTrack/sendPoint";
import { getFxgBuyerNick } from "@/components-biz/BuyerNickComp/fxg";
import tradeSetStore from "@/stores/trade/tradeSet";
import styles from "./index.module.scss";
import LivePrintModal from './components/LivePrintModal';
import FakerTradeSettingModal from './components/FakerTradeSettingModal';
import LiveLimitModal from './components/LiveLimitModal';
import { useLivePrintModal } from './components/LivePrintModal/useLivePrintModal';
import NormalLayout from "@/components-biz/layouts/NormalLayout";
import DyNickName from "@/assets/image/live/标签@2x.png";
import LowVersionControlModal from '@/components-biz/LowVersionControlModal';
import { PageNameControlEnum } from '@/components-biz/LowVersionControlModal/constant';

const initFormData = {
	liveTimeLength: 6,
	refundStatus: 0,
	liveType: 'shop',
	authorList: undefined,
	shopList: undefined,
	isOpenFakerTrade: false,
	isOpenBlackList: false,
	isOpenNewCustomer: false,
	isOpenRepeatTrade: false,
};

let countDownTimer = null;

let eventSource = null;

const LiveTag = observer(() => {
	const [showLivePrintModal, setShowLivePrintModal] = useState(false);
	const [showLivePrintResultModal, setShowLivePrintResultModal] = useState(false);
	const [form] = Form.useForm();
	const [modal, contextHolder] = Modal.useModal();
	const [formData, setFormData] = useState(initFormData);
	const [currentLiveInfo, setCurrentLiveInfo] = useState({} as any);
	const [isAgree, setIsAgree] = useState(false);
	const [statusCount, setStatusCount] = useState({} as any);
	const [lastLive, setLastLive] = useState({} as any);
	const [remainTime, setRemainTime] = useState('');
	const [liveType, setLiveType] = useState<'shop' | 'talent' | 'danmu'>('shop'); // 新增直播类型状态
	const [isShowFakerTradeSettingModal, setIsShowFakerTradeSettingModal] = useState(false); // 显示
	const [showLowVersionModal, setShowLowVersionModal] = useState(false); // 版本弹框
	const [showLiveLimitModal, setShowLiveLimitModal] = useState(false); // 直播功能限制弹框

	const { confirm } = Modal;
	const history = useHistory();
	const {
		tradeAdvanceSetObj,
		setTradeAdvanceSetObj,
		setSetting,
		zbdXbqTempList,
		setZbdXbqTempList,
		selectedZbdXbqTemp,
		setSelectedZbdXbqTemp,
		scanPrintStore: {
			printersList,
			defaultPrinter,
			setDefaultPrinter,
		},
	} = tradeStore;

	const { shopTokenCache, userInfo } = userStore;
	const {
		level,
		whiteListSetting,
	} = userInfo;
	const { 
		dyNickName, // 抖音昵称
		liveStream, // 直播功能
	} = JSON.parse(whiteListSetting || '{}');

	// 使用自定义hook管理订单数据
	const {
		liveOrderList,
		addOrderToList,
		updateOrderPrintStatus,
		clearOrderList,
	} = useLivePrintModal();

	const isLiving = useMemo(() => {
		return lastLive?.hasLivePrintTask && lastLive?.taskStatus === 1; // 执行状态0等待执行，1执行中，2成功，3失败4关闭
	}, [lastLive]);

	const getTempList = async() => {
		const data = await PrintCenter.getZbdTempList(BQ_PRINT_TYPE.直播标签);
		console.log(data.ModeListShows);
		setZbdXbqTempList(data.ModeListShows);
		if (data.ModeListShows[0]) {
			setSelectedZbdXbqTemp(data.ModeListShows[0]);
		}
		return data.ModeListShows;
	};
	useEffect(() => {
		console.log(defaultPrinter);
		form.setFieldsValue({
			selectedPrinter: defaultPrinter || printersList[0],
		});
	}, [printersList, defaultPrinter]);

	// 初始化高级设置
	useEffect(() => {
		const getAdvancedSet = async() => {
			let res = await memoFn.getAdvancedSet();
			if (res) {
				// 为新用户爆款打单配置默认值
				if (!res.printSetExpandDTO) {
					const { version } = await userStore.getUserInfo();
					let topItemShow = "";
					let topSkuShow = "";
					if (version == 2) {
						topItemShow = `{"itemPerspective":["21","24","22","23"]}`;
						topSkuShow = `{"itemPerspective":["21","22","23"]}`;
					} else {
						topItemShow = `{"itemPerspective":["1","2","3"],"sysPerspective":["11","12"]}`;
						topSkuShow = `{"itemPerspective":["2","1"],"sysPerspective":["11","12","13"]}`;
					}
					res.printSetExpandDTO = { topItemShow, topSkuShow };
					memoFn.updateAdvancedSet(res);
				}
				if (res?.groupPrintSetJsonString && typeof res.groupPrintSetJsonString === 'string') {
					try {
						res.groupPrintSetJsonString = JSON.parse(res.groupPrintSetJsonString);
						if (res?.groupPrintSetJsonString?.printOrderByHandOrder || res?.groupPrintSetJsonString?.handOrderMatchPlatform) {
							res.groupPrintSetJsonString.orderMatchSetting = [// 订单匹配规则
								{
									platform: PLAT_HAND,
									bindControlType: res.groupPrintSetJsonString.handOrderMatchPlatform
								},
								{
									platform: PLAT_SPH,
									bindControlType: res.groupPrintSetJsonString.handOrderMatchPlatform
								}
							];
							delete res.groupPrintSetJsonString.printOrderByHandOrder;
							delete res.groupPrintSetJsonString.handOrderMatchPlatform;
						}
						// 只默认去除小红书的
						res.groupPrintSetJsonString.orderMatchSetting = res?.groupPrintSetJsonString?.orderMatchSetting.filter(o => o.platform !== PLAT_XHS);
						memoFn.updateAdvancedSet(res);
					} catch (error) {
						console.error(error);
					}
				} else if (res?.groupPrintSetJsonString?.orderMatchSetting.length) {
					res.groupPrintSetJsonString.orderMatchSetting = res?.groupPrintSetJsonString?.orderMatchSetting.filter(o => o.platform !== PLAT_XHS);
					memoFn.updateAdvancedSet(res);
				}
				// setSetting(res);
				setTradeAdvanceSetObj(res);
			}
		};
		getAdvancedSet();
		tradeSetStore.getPrintContentSet();		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, []);

	const pageInit = async() => {
		const tempList = await getTempList();
		sendPoint(Pointer["直播打印-页面展现"]);
		TradeLiveTagLastTaskApi({ requestVersion: "v2" }).then((res) => {
			setLastLive(res || {});
			// 如果有直播打印任务,且执行中,则打开弹框
			if (res?.hasLivePrintTask && res?.taskStatus === 1) {
				setShowLivePrintModal(true);
				const { requestJson } = res;
				let initData = JSON.parse(requestJson);
				
				// 处理店铺信息
				initData.shopList = res.shopList.map(item => item.sellerId);
				initData.liveTimeLength = res.liveTimeLength;
				
				// 处理达人信息 - 也可以判断liveType
				if (res?.authorList?.length > 0) {
					// 如果有达人信息，设置直播类型为达人直播
					setLiveType('talent');
					// 设置达人选择的值，需要转换为AuthorMultiSelect组件期望的格式
					initData.authorList = { authorList: res.authorList };
				} else {
					// 如果没有达人信息，设置为店铺直播
					setLiveType('shop');
					initData.authorList = { authorList: [] };
				}
				
				// 处理打印模板
				const selectedZbdTempList = tempList.filter(item => item.Mode_ListShowId === initData.selectedZbdTemp);
				if (selectedZbdTempList[0]) {
					setSelectedZbdXbqTemp(selectedZbdTempList[0]);
				}
				
				// 设置表单值和数据
				form.setFieldsValue(initData);
				setFormData(initData);
				
				// 同时设置currentLiveInfo，确保弹框能正确显示直播信息
				setCurrentLiveInfo({
					...res,
					shopList: res.shopList || [],
					authorList: res.authorList || [],
					liveType: res.authorList?.length > 0 ? 2 : 1, // 1=店铺直播，2=达人直播
					liveNo: res.liveNo || `直播-${new Date().toLocaleString()}`, // 如果没有场次号，生成一个
				});
			} else {
				form.setFieldsValue(initFormData);
				setShowLivePrintModal(false);
				setShowLivePrintResultModal(false);
				setLiveType('shop'); // 重置为店铺直播
			}
		});
	};

	// 更新统计数据
	const updateStatusCount = () => {
		TradeLiveTagTradeStatusCountApi({
			liveTradePrintTaskId: isLiving ? lastLive.id : currentLiveInfo.id,
			requestVersion: "v2",
		}).then((res) => {
			// 如果任务状态不是执行中，则关闭弹框
			if (res.taskStatus !== 1) {
				closeLivePrintAfter();
			}
			setStatusCount(res);
		});
	};
	
	useEffect(() => {
		if (showLivePrintModal) {
			if (isLiving) {
				let timer = setInterval(() => {
					updateStatusCount();
				}, 5000);
				updateStatusCount();
				countdown(new Date(lastLive.livePrintTimeEnd).getTime());
				return () => {
					clearInterval(timer);
					timer = null;
				};
			} else {
				const sseUrl = process.env.APP_SSE_URL || "https://erpsse.kuaidizs.cn";
				const sseEnv = process.env.APP_SSE_ENV || userStore.userInfo.env;
				console.log("sseEnv", process.env.APP_SSE_ENV, userStore.userInfo.env);
				eventSource = new EventSourcePolyfill(
					`${sseUrl}/sse/liveBoxConn?token=${currentLiveInfo.uniqueBizCode || ""
					}&expireHours=${currentLiveInfo.liveTimeLength}&userId=${currentLiveInfo.userId
					}&kdzserptoken=${getToken() || ""}&env=${sseEnv}`
				);
				eventSource.onmessage = async(event) => {
					console.log(
						"message",
						event.data,
					);
					const { content = {}, sseBizType = "" } = JSON.parse(event.data);
					const isFakerTrade = content?.isFakerTrade || false;

					if (sseBizType === "LIVE_BOX") {
						// 非虚拟订单添加订单到BaseTable列表
						if (!isFakerTrade) {
							addOrderToList(content);
						}
						
						if (content?.platform?.toLocaleLowerCase() === PLAT_FXG && tradeAdvanceSetObj?.printSetExpandDTO?.showDyNickName) {
							const { ptTid, sellerId, sellerNick } = content as any;
							const token = shopTokenCache[sellerId];
							const orderId = ptTid?.replace(/A/g, '');
							const nickData = await getFxgBuyerNick({
								sellerId,
								token,
								sellerName: sellerNick,
								orderIds: [orderId],
							});
							content.base64Url = nickData?.[orderId] || '';
						}
						
						console.log('content', content);
						
						// 更新订单状态为正在打印
						if (!isFakerTrade) {
							updateOrderPrintStatus(content.tid, 0); // 0表示未打印，即将打印
						}
						
						// 执行打印
						try {
							if (isFakerTrade) {
								console.log('%c [ 虚拟订单打印 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', content);
							}
							await handlePrintAct([content]);
							// 打印成功，更新状态
							if (!isFakerTrade) {
								updateOrderPrintStatus(content.tid, 1); // 1表示已打印
							}
						} catch (error) {
							// 打印失败，更新状态
							const errorMsg = error.message || '打印失败';
							if (!isFakerTrade) {
								updateOrderPrintStatus(content.tid, 3, errorMsg); // 3表示异常
							}

							window.errorCollection?.customMessageUpload({
								type: `直播标签-直播打印: 【 直播自动打印失败 】`,
								data: {
									error: errorMsg,
									ptTid: content.ptTid,
									tid: content.tid,
								}
							});
						}
					}
				};
				eventSource.onopen = (event) => {
					console.log("onopen");
				};
				eventSource.onerror = (event) => {
					console.log("error", event);
				};
				let timer = setInterval(() => {
					updateStatusCount();
				}, 5000);
				updateStatusCount();
				countdown(new Date(currentLiveInfo.livePrintTimeEnd).getTime());
				window.addEventListener('beforeunload', () => {
					eventSource.close();
					clearInterval(timer);
					timer = null;
					clearInterval(countDownTimer);
					countDownTimer = null;
				});
				return () => {
					eventSource.close();
					clearInterval(timer);
					timer = null;
					clearInterval(countDownTimer);
					countDownTimer = null;
				};
			}
		} else {
			// 弹框关闭时清空订单列表
			clearOrderList();
			clearInterval(countDownTimer);
			countDownTimer = null;
		}
	}, [showLivePrintModal]);

	const countdown = (endTime) => {
		countDownTimer = setInterval(() => {
			let now = new Date().getTime();
			let timeRemaining = endTime - now;

			let days = Math.floor(timeRemaining / (1000 * 60 * 60 * 24));
			let hours = Math.floor((timeRemaining % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
			let minutes = Math.floor((timeRemaining % (1000 * 60 * 60)) / (1000 * 60));
			let seconds = Math.floor((timeRemaining % (1000 * 60)) / 1000);

			// console.log(hours + " 小时 " + minutes + " 分钟 " + seconds + " 秒 ");
			setRemainTime(`${hours >= 10 ? hours : '0' + hours}:${minutes >= 10 ? minutes : '0' + minutes}:${seconds >= 10 ? seconds : '0' + seconds}`);

			if (timeRemaining <= 0) {
				// 倒计时结束，可以执行其他操作
				clearInterval(countDownTimer);
				countDownTimer = null;
				closeLivePrint();
			}
		}, 1000);
	};

	const handleFinish = async(v) => {
		console.log("params:", v);
		const params: any = {};
		const { plats, plat_sellerIds } = v.platformInfo || {};
		let multiShops = await getMultiShops({ plats, plat_sellerIds });
		params.multiShops = multiShops;
		params.isPlatformEmptyQuery = !plats?.length && !plat_sellerIds?.length;
	};

	const closeLivePrint = async() => {
		sendPoint(Pointer.关闭自动打印);
		await TradeLiveTagCloseTaskApi({
			liveTradePrintTaskId: currentLiveInfo.id || lastLive.id,
			requestVersion: "v2",
		});
		closeLivePrintAfter();
	};

	const closeConfirm = () => {
		confirm({
			icon: <ExclamationCircleOutlined />,
			content: <div style={ { overflow: "hidden" } }>当前店铺存在自动打印进程，关闭该页面将导致自动打印中断</div>,
			okText: '确定关闭',
			centered: true,
			onOk() {
				closeLivePrint();
			},
			onCancel() {
			},
		});
	};

	const closeLivePrintAfter = async() => {
		const lastLiveData = await TradeLiveTagLastTaskApi({
			requestVersion: "v2",
		});
		const lastLiveCount = await TradeLiveTagTradeStatusCountApi({
			liveTradePrintTaskId: lastLiveData.id,
			requestVersion: "v2",
		});
		setStatusCount(lastLiveCount);
		setLastLive(lastLiveData);
		setShowLivePrintResultModal(true);
		setShowLivePrintModal(false);
		setRemainTime('');
		clearInterval(countDownTimer);
		countDownTimer = null;
	};

	const openLivePrint = async() => {
		// 添加直播功能限制判断
		if (liveStream !== 1) {
			setShowLiveLimitModal(true);
			return;
		}
		
		if (!isAgree) {
			message.warn("请先勾选接受免责声明");
			return;
		}
		const lastTask = await TradeLiveTagLastTaskApi({
			requestVersion: "v2",
		});
		if (lastTask?.hasLivePrintTask && lastTask?.taskStatus === 1) {
			message.warn("当前已有直播打印任务在进行，请勿重复开启");
			return;
		}
		await form.validateFields();
		if (!selectedZbdXbqTemp || !selectedZbdXbqTemp.Mode_ListShowId) {
			message.warn("请选择模板，若无模板，请点击编辑模板进行新增");
			return;
		}
		const formValues = form.getFieldsValue();
		console.log('表单数据', formValues, formData);

		const { plats, plat_sellerIds } = formValues.shopList || {};
		let multiShops = await getMultiShops({ plats, plat_sellerIds });
		
		// 处理达人信息 - 新增逻辑
		let authorList = [];
		if (liveType === 'talent' && formValues.authorList?.authorList?.length > 0) {
			authorList = formValues?.authorList?.authorList?.map(author => ({
				platform: author.platform,
				authorId: author.authorId || author.value,
				authorName: author.authorName || author.label,
			}));
		}

		if (liveType === 'talent') {
			if (!authorList?.length) {
				message.warn("请选择达人信息");
				return;
			}
		}
		if (!plats?.length || !plat_sellerIds?.length) {
			message.warn("请选择店铺信息");
			return;
		}
		
		sendPoint(Pointer.开启直播自动打印);
		const data = await TradeLiveTagCreateTaskApi({
			...formValues,
			liveType: liveType === 'talent' ? 2 : 1, // 1=店铺直播，2=达人直播
			userId: userInfo.userId,
			shopList: multiShops,
			authorList, // 新增达人信息
			selectedZbdTemp: selectedZbdXbqTemp.Mode_ListShowId,
			selectedPrinter: formValues.selectedPrinter,
			requestVersion: "v2",
			fakerTradeConfig: userStore.fakerTradeConfig || {}, // 虚拟订单配置
		});
		
		// 透传给打印，包含完整的直播信息
		setCurrentLiveInfo({
			...data,
			shopList: multiShops,
			authorList,
			liveType: liveType === 'talent' ? 2 : 1,
			liveNo: data.liveNo || `直播-${new Date().toLocaleString()}`, // 生成场次号
		});
		setShowLivePrintModal(true);
	};

	// 打印小标签
	const handlePrintAct = async(orderList?: any) => {
		let printList = [];
		printList = orderList.map((item) => {
			return {
				liveTradePrintTaskId: currentLiveInfo.id,
				...item,
			};
		});
		PrintCenter.batchPrintZbd({
			temp: selectedZbdXbqTemp,
			orderList: printList,
			printType: BQ_PRINT_TYPE.直播标签,
			printer: form.getFieldValue("selectedPrinter"),
			isLiveTag: true,
		});
	};

	/**
	 * 打开打印中心模板服务
	 * @param type
	 */
	const handleOpenPrintMainPanel = () => {
		PrintCenter.showTemplateMain({ printType: "zbd" });
	};
	/**
	 * 处理快递模板选择
	 * @param e
	 */
	const handleSelectTempChange = (e: any) => {
		let temp = zbdXbqTempList.find(
			(i) => i.Mode_ListShowId === e.target.value
		);
		setSelectedZbdXbqTemp(temp);
	};

	// 跳转到直播订单明细页面并打开对应tab
	const jumpToDetail = (type) => {
		const state = {
			liveTradePrintTaskIdList: lastLive.id,
			labelType: type === "1" ? EnumLabelTypeTabs["已打印"] : EnumLabelTypeTabs["未打印-不符合"]
		};
		
		setShowLivePrintResultModal(false);
		history.push({ pathname: '/liveLog', state });
	};

	// 打印模版
	const renderPrintComp = useMemo(() => {
		let _zbdXbqTempList = zbdXbqTempList || [];
		return (
			<Layout>
				<div className="r-flex r-bg-white">
					<div>
						<Form.Item
							required
							name="selectedPrinter"
							label="打印机"
						>
							<Select
								size="small"
								placeholder="请选择打印机"
								style={ { width: "200px" } }
								onChange={ (e) => { setDefaultPrinter(e); } }
							>
								{printersList.map((printer) => (
									<Select.Option
										key={ printer }
										value={ printer }
									>
										{printer}
									</Select.Option>
								))}
							</Select>
						</Form.Item>
					</div>
					<div className="r-ml-10 r-mr-40 r-flex">
						<div style={ { width: "80px" } }>标签模板：</div>
						<div className="r-c-666">
							<Radio.Group
								value={ selectedZbdXbqTemp.Mode_ListShowId }
								onChange={ handleSelectTempChange }
							>
								{_zbdXbqTempList.map((i) => (
									<Radio
										key={ i.Mode_ListShowId }
										value={ i.Mode_ListShowId }
									>
										{i.ExcodeName}
									</Radio>
								))}
							</Radio.Group>
						</div>
						<div
							className=""
							style={ {
								cursor: "pointer",
								color: "#1890FF",
								minWidth: "60px",
							} }
							onClick={ () => handleOpenPrintMainPanel() }
						>
							编辑模板
						</div>
					</div>
				</div>
			</Layout>
		);
	}, [zbdXbqTempList, printersList, selectedZbdXbqTemp]);

	const onFormValueChange = (changedValues: {}, allValues) => {
		console.log(allValues);
		setFormData(allValues);
	};

	// 直播异常选项
	const TRADE_EXCEPTION_OPTIONS_1 = (useMemo(() => {
		const hasWaveManagePermission = userStore.hasWaveManagePermission;
		if (!hasWaveManagePermission) {
			TRADE_EXCEPTION_OPTIONS.forEach(op => {
				op.options.forEach(item => {
					item.arr = item.arr.filter(i => i.value !== '波次拣货异常');
				});
			});
		}
		return [...TRADE_EXCEPTION_OPTIONS];
	}, [userStore.hasWaveManagePermission]));

	// 直播类型切换处理
	const handleLiveTypeChange = (activeKey: string) => {
		if (activeKey !== 'danmu') {
			setLiveType(activeKey as 'shop' | 'talent' | 'danmu');
			
			// 先重置字段，然后设置空值
			form.resetFields(['authorList', 'shopList']);
		
			// 延迟设置空值，确保重置生效
			setTimeout(() => {
				form.setFieldsValue({
					authorList: { authorList: [] },
					shopList: { plats: [], plat_sellerIds: [] },
				});
				
				// 同时重置 formData 状态
				setFormData(prev => ({
					...prev,
					authorList: { authorList: [] },
					shopList: { plats: [], plat_sellerIds: [] },
				}));
			}, 0);

			if (activeKey == 'talent') {
				userStore.getAuthorList(false);
			} else if (activeKey == 'shop') {
				userStore.getLiveShopList(true);
			}
		}
	};

	// 处理抖音昵称配置变更
	const handleDyNickNameChange = async(e) => {
		const checked = e.target.checked;
		
		try {
			// 更新 tradeAdvanceSetObj
			const updatedConfig = {
				...tradeAdvanceSetObj,
				printSetExpandDTO: {
					...tradeAdvanceSetObj?.printSetExpandDTO,
					showDyNickName: checked ? 1 : 0
				}
			};
			
			// 保存到服务器
			let params = cloneDeep(updatedConfig);
			console.log('%c [ 高级设置 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', params);
			// 保持与高级设置组件一致
			params.hideTrade && delete params.hideTrade;
			if (params?.groupPrintSetJsonString) {
				params.groupPrintSetJsonString = JSON.stringify(params.groupPrintSetJsonString);
			}
			
			await TradePrintSetUpdatePrintSetApi({ ...params });
			
			// 更新本地状态
			setTradeAdvanceSetObj(updatedConfig);
			memoFn.updateAdvancedSet(updatedConfig);
			setSetting(updatedConfig);
		} catch (error) {
			console.error('保存抖音昵称配置失败:', error);
		}
	};

	const disabledAuthorIds = useMemo(() => {
		return userStore.authorList?.filter(item => item.liveStatus === 1)?.map(item => `${item.platform}_${item.authorId}`) || [];
	}, [userStore.authorList]);

	// 根据选择的达人过滤可选的平台
	const filteredPlatformsForAuthors = useMemo(() => {
		if (!formData?.authorList?.authorList?.length) return [];
		
		const authorPlatforms = formData.authorList.authorList.map(author => author.platform);
		console.log('%c [ 可选的平台 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', [...new Set(authorPlatforms)]);
		return [...new Set(authorPlatforms)]; // 去重
	}, [formData?.authorList?.authorList]);

	// 根据选择的平台和店铺过滤可选的达人平台
	const filteredPlatformsForShops = useMemo(() => {
		if (!formData?.shopList?.plats?.length && !formData?.shopList?.plat_sellerIds?.length) return [];
		
		const selectedPlatforms = formData.shopList.plats || [];
		const selectedShopIds = formData.shopList.plat_sellerIds || [];
		
		// 从店铺ID中提取平台信息
		const platformsFromShopIds = selectedShopIds.map(shopId => {
			const [platform] = shopId.split('_');
			return platform;
		});
		
		// 合并平台列表并去重
		const allPlatforms = [...new Set([...selectedPlatforms, ...platformsFromShopIds])];
		return allPlatforms;
	}, [formData?.shopList?.plats, formData?.shopList?.plat_sellerIds]);

	// 达人直播不限制店铺
	const disabledShopIds = useMemo(() => {
		if (liveType == 'talent') {
			return [];
		} else {
			return userStore.liveShopList?.map(item => `${item.platform}_${item.sellerId}`) || [];
		}
	}, [userStore.liveShopList, liveType]);

	useEffect(() => {
		pageInit();
		userStore.getAuthorList(false);
		userStore.getLiveShopList(true);
		userStore.getFakerTradeConfig();
	}, []);

	return (
		<NormalLayout className={ styles['live-tag-warp'] }>
			<LiveTypeTabs
				activeKey={ liveType }
				onChange={ handleLiveTypeChange }
			/>

			<Form
				name="live-tag-search"
				size="small"
				form={ form }
				layout="inline"
				onFinish={ handleFinish }
				onValuesChange={ onFormValueChange }
			>
				<Layout>
					<div>
						<Card className={ styles['live-card'] } title="直播设置">
							{/* 达人直播时显示达人选择 */}
							{liveType === 'talent' && (
								<div className="r-flex r-ai-fs r-mb-16">
									<Form.Item
										name="authorList"
										label="选择达人"
										rules={ [
											{
												required: true, message: "请选择达人"
											},
											{
												validator(rule, value, callback) {
													if (value && !value?.authorList?.length) {
														return Promise.reject(new Error('请选择达人'));
													}
													return Promise.resolve();
												},
											}] }
									>
										<AuthorMultiSelect
											bgHighLight
											placeholder="请选择达人"
											dropdownStyle={ { maxHeight: '300px' } }
											dropdownOpenChange={ (open) => {
												// console.log('%c [ 达人选择器触发 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', open);
												if (open) {
													userStore.getAuthorList(true);
												}
											} }
											disabledAuthorIds={ disabledAuthorIds }
											filterPlatforms={ filteredPlatformsForShops } // 添加过滤选项，只显示对应平台的达人
										/>
									</Form.Item>
								</div>
							)}

							<div className="r-flex r-ai-fs">
								<div className="r-flex r-ai-fs">
									<Form.Item
										name="shopList"
										label="选择店铺"
										rules={ [
											{ required: true, message: "请选择店铺" },
											{
												validator(rule, value, callback) {
													if (value && !value?.plats?.length && !value?.plat_sellerIds?.length) {
														return Promise.reject(new Error('请选择店铺'));
													}
													return Promise.resolve();
												},
											}
										] }
									>
										<ShopMultiSelect 
											disabledShopIds={ disabledShopIds }
											disabledText="直播中"
											filterOptions={ liveType === 'talent' ? cloneDeep(filteredPlatformsForAuthors) : undefined } // 添加过滤选项，只显示对应平台的店铺
										/>
									</Form.Item>
								</div>
								<div className="r-ml-20 r-flex r-ai-fs">
									<Form.Item
										required
										label="直播时长:"
										name="liveTimeLength"
										rules={ [{ required: true, message: "请选择直播时长" }] }
									>
										<InputNumber
											max={ 12 }
											min={ 1 }
											precision={ 0 }
											style={ { width: "100px" } }
										/>
									</Form.Item>
									<div style={ { lineHeight: "24px" } }>小时，到达时间后关闭直播打印</div>
								</div>
							</div>
						</Card>
						<Card className={ styles['live-card'] } title="订单设置">
							<div className="r-flex r-ai-c">
								<div style={ { width: 100, textAlign: 'right' } }>订单拉取规则：</div>
								<div>全部付款订单</div>
							</div>
							
							<div className="r-flex r-mt-10">
								<div style={ { width: 100, textAlign: 'right' } }>过滤指定订单：</div>
								<div className="r-flex">
									<Form.Item
										required
										name="refundStatus"
										style={ { width: "228px" } }
									>
										<Select
											options={
												ORDER_FILTER_OPTIONS
											}
											size="small"
										/>
									</Form.Item>
									<Form.Item
										name="tradeLabelList"
										style={ { width: "328px" } }
									>
										<InputSelect
											bgHighLight
											optionsList={
												[TRADE_LABEL_OPTIONS[0]]
											}
											size="small"
										/>
									</Form.Item>
									<Form.Item
										name="tradeExceptionList"
										style={ { width: "328px" } }
									>
										<InputSelect
											bgHighLight
											optionsList={
												[TRADE_EXCEPTION_OPTIONS_1[0]]
											}
											size="small"
										/>
									</Form.Item>
								</div>
							</div>
							
							<div className="r-flex r-mt-10">
								<div style={ { width: 100, textAlign: 'right' } }>虚拟订单：</div>
								<div className="r-flex r-ai-c">
									<Form.Item
										name="isOpenFakerTrade"
										valuePropName="checked"
									>
										<Checkbox>
											直播过程中生成虚拟订单
										</Checkbox>
									</Form.Item>
									<div
										className="r-pointer"
										style={ {
											color: "#1890FF",
											minWidth: "60px",
										} }
										onClick={ () => { setIsShowFakerTradeSettingModal(true); } }
									>
										规则设置
									</div>
								</div>
							</div>

							<div className="r-flex r-mt-10">
								<div style={ { width: 100, textAlign: 'right' } }>买家标识：</div>
								<div className="r-flex r-ai-c">
									<Form.Item
										name="isOpenBlackList"
										valuePropName="checked"
									>
										<Checkbox>
											打印黑名单标识
										</Checkbox>
									</Form.Item>

									<div
										className="r-pointer"
										style={ {
											color: "#1890FF",
											minWidth: "60px",
										} }
										onClick={ () => { history.push('/settings/customer'); } }
									>
										黑名单设置
										<Tooltip title="说明：订单打印页面使用「标记黑名单」功能进行黑名单用户添加" >
											<span className="r-ml-4">
												<Icon type="wenhao-xian" className="r-c-999 r-pointer" />
											</span>
										</Tooltip>
									</div>

									<Form.Item
										name="isOpenNewCustomer"
										className="r-ml-20"
										valuePropName="checked"
									>
										<Checkbox>
											打印新人首单标识
										</Checkbox>
									</Form.Item>

									<Form.Item
										name="isOpenRepeatTrade"
										valuePropName="checked"
										className="r-ml-4"
									>
										<Checkbox>
											买家重复下单标识
										</Checkbox>
									</Form.Item>
								</div>
							</div>
						</Card>
						<Card
							className={ styles['live-card'] }
							title="打印设置"
						>
							<div className="r-flex r-ai-c r-mb-10">
								<div>抖音昵称:</div>
								<Checkbox 
									className="r-ml-10"
									disabled={ dyNickName != 1 && level == 2 }
									checked={ dyNickName == 1 ? tradeAdvanceSetObj?.printSetExpandDTO?.showDyNickName : false }
									onChange={ handleDyNickNameChange }
									onClick={ () => {
										if (level != 2 && dyNickName != 1) {
											setShowLowVersionModal(true);
										}
									} }
								>
									开启抖音昵称打印
								</Checkbox>
								<img src={ DyNickName } alt="" className="r-ml-12" style={ { width: '92px', height: '24px' } } />
							</div>
							{renderPrintComp}
						</Card>
						<Card className={ styles['live-card'] } title="免责声明">
							<div>
								<div
									className="r-pd-10 r-fs-12"
									style={ {
										border: "1px solid #ddd",
									} }
								>
									<p>
										开启直播自动打印功能表示您已同意并接受以下事实：
									</p>
									<p>
										1.您知晓“直播打印”功能为商家打印发货的一项辅助功能，会受到服务器稳定性、接口波动、电脑关机、浏览器关闭等因素影响，可能存在部分订单打印失败或发货失败的风险，无法完全替代手动打印及相关确认操作。
									</p>
									<p>
										2.您应当在“直播打印”进程结束后及时核对订单打印和发货的情况，【快递助手ERP】不承担因中断后您未及时确认订单打印和发货状态而导致的损失。
									</p>
								</div>
								<div className="r-mt-16">
									<Checkbox
										value={ isAgree }
										onChange={ (e) => setIsAgree(
											e.target.checked
										) }
									>

										<div style={ { userSelect: "none" } }>
											我已阅读并接受
											<span className="r-c-warning r-ml-8">开启直播自动打印后将按照上方设置规则自动打印符合条件的订单</span>
										</div>
									</Checkbox>
								</div>
								<div className="r-flex r-ai-c r-jc-c r-mt-10 r-pb-10">
									<Button
										type="primary"
										size="middle"
										onClick={ openLivePrint }
									>
										开启直播自动打印
									</Button>

								</div>
							</div>
						</Card>
					</div>
				</Layout>
			</Form>

			{/* 自动打印弹框 */}
			<LivePrintModal
				visible={ showLivePrintModal }
				currentLiveInfo={ currentLiveInfo }
				statusCount={ statusCount }
				remainTime={ remainTime }
				onClose={ closeConfirm }
				liveOrderList={ liveOrderList }
				onClearOrderList={ clearOrderList }
				isLiving={ isLiving }
				formData={ formData }
			/>

			{/* 功能限制弹框 */}
			<LiveLimitModal
				visible={ showLiveLimitModal }
				onClose={ () => setShowLiveLimitModal(false) }
			/>

			<Modal
				centered
				title="自动打印"
				visible={ showLivePrintResultModal }
				width="800px"
				footer={ null }
				destroyOnClose
				maskClosable={ false }
				onCancel={ () => { setShowLivePrintResultModal(false); } }
			>
				<div className="r-ta-c r-fs-20 r-c-warning">
					本次自动打印结束
				</div>
				<div className="r-flex r-jc-sb r-mt-20">
					<div>
						自动打印开始时间：
						{lastLive.livePrintTimeStart}
					</div>
					<div>
						结束时间：
						{lastLive.livePrintTimeActualEnd}
					</div>
					<div>
						共计：{lastLive.liveTimeStr}
					</div>
				</div>
				<div
					className="r-flex r-jc-c r-mt-20 r-ta-c r-pt-20 r-pl-40 r-pr-40"
					style={ { borderTop: "1px solid #eee" } }
				>
					<div style={ { padding: "0 50px" } }>
						<div className="r-c-success r-fs-20 r-pb-10 r-pointer" onClick={ () => jumpToDetail("1") }>
							{statusCount.alreadyPrintCount}
						</div>
						<div>标签已打印数量</div>
					</div>
					<div style={ { padding: "0 50px" } }>
						<div className="r-c-error r-fs-20 r-pb-10 r-pointer" onClick={ () => jumpToDetail("2") }>
							{statusCount.filterTradeNum}
						</div>
						<div>过滤订单数量</div>
					</div>
				</div>
				<div className="r-ta-r r-mt-30 r-pt-20" style={ { borderTop: "1px solid #eee" } }>
					<Button onClick={ () => { setShowLivePrintResultModal(false); } }>
						关闭
					</Button>
				</div>
			</Modal>

			{
				isShowFakerTradeSettingModal && (
					<FakerTradeSettingModal 
						visible={ isShowFakerTradeSettingModal }
						onClose={ () => { setIsShowFakerTradeSettingModal(false); } }
					/>		
				)
			}			

			{/* 低版本控制弹窗 */}
			{showLowVersionModal && (
				<LowVersionControlModal
					closable
					centered
					pageName={ PageNameControlEnum.抖音昵称打印 }
					onCancel={ () => {
						setShowLowVersionModal(false);
					} }
				/>
			)}
			
			{contextHolder}
		</NormalLayout>
	);
});

export default LiveTag;
