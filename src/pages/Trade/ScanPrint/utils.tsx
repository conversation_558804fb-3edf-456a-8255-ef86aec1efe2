import React from "react";
import { Modal } from "antd";
import { ExclamationCircleOutlined } from "@ant-design/icons";
import message from "@/components/message";
import { ILabelStatus } from "../interface";
import { LabelStatus, tradeStatusVoiceMap } from "./constants";
import voice订单已挂起 from '@/assets/mp3/订单已挂起.mp3';
import voice扫描成功 from '@/assets/mp3/扫描.mp3';
import voice多件 from '@/assets/mp3/多件.mp3';
import voice打印失败 from '@/assets/mp3/打印失败.mp3';
import { IData } from "./components/ScanPrintTradeInfo";
import { TradePrintContentSetRule } from "@/types/trade/tradeSet";
import tradeSetStore from "@/stores/trade/tradeSet";
import { genePrintContent } from "@/utils/trade/printContent";
import scanPrintStore from "@/stores/trade/scanPrint";
import { tradeStore } from "@/stores";
import { TradeTradeDetailGetApi } from "@/apis/trade";
import PrintCenterAPI from '@/print/index';
import { PLAT_ALI, PLAT_FXG, PLAT_TB, PRINT_MAP, PLAT_C2M, APPOINTMENT_ARRIVAL_PLAT, HL_PLAT, PLAT_OTHER, PLAT_DW, PLAT_HAND } from "@/constants";
import { templateType4PlatformTypeEnum } from "../constants";
import { calcPackTotalNum } from "../utils";
import { dealPlatAsHandPlat, transferOtherToHand } from "@/components-biz/ShopListSelect/shopListUtils";
import { getDyDefaultAddressApi, getTemplateInfoByShowIdApi } from "@/apis/printCenter";
import userStore from "@/stores/user";
import { pageLoading } from "@/components/PageLoading";
import timer, { logTypeEnum } from "@/libs/timer";
import memoFn from "@/libs/memorizeFn";
import { refluxOrderPrintSettingsEnum } from "@/types/trade/index";
import { sleep } from "@/utils/util";


export const getLabelStatusLabel = (labelStatus: ILabelStatus, needStyle = true) => {
	if (needStyle) {
		return <span className="r-c-error r-fw-700 r-fs-14">{LabelStatus[labelStatus]}</span>;
	} else {
		return <span>{LabelStatus[labelStatus]}</span>;
	}
};

export const focusLabelInput = () => {
	let el = document.querySelector('#labelIdInput') as HTMLInputElement;
	if (el) {
		el.disabled = false;
		el?.focus();
		el?.select();
	}
};

export const disabledLabelInput = () => {
	let el = document.querySelector('#labelIdInput') as HTMLInputElement;
	if (el) {
		el.disabled = true;
	}
};

export const domInputFocus = () => {
	let el = document.querySelector('#scanPrintCodeIdInput') as HTMLInputElement;
	if (el) {
		el.disabled = false;
		el?.focus();
		el?.select();
	} else {
		setTimeout(() => {
			domInputFocus();
		}, 100);
	}
};

const domInputBlur = () => {
	let el = document.querySelector('#scanPrintCodeIdInput') as HTMLInputElement;
	if (el) {
		el.disabled = true;
	}
};


let audio = new Audio('');
let tradeConfirmTipModal = null;

export const playAudio = async(src: string, timeout?: number) => {
	// 临时兼容方案 等待几百毫秒
	// 多件混扫分拣码提示 需要等分拣码播报完 再进行了其他语音提示
	const { scanPrintSetting } = scanPrintStore;
	// 多件订单混扫备齐模式
	const mixScanMode = !!scanPrintSetting?.scanSet?.mixScanMode;
	if (mixScanMode && timeout) {
		await new Promise((resolve, reject) => { setTimeout(resolve, timeout); });
	}
	console.log('audio', timeout);

	if (audio) {
		audio.src = src;
		const playPromise = audio.play();
		if (playPromise !== undefined) {
			playPromise.catch(e => {
				console.warn(e);
			});
		}
	} else {
		audio = new Audio(src);
		const playPromise = audio.play();
		if (playPromise !== undefined) {
			playPromise.catch(e => {
				console.warn(e);
			});
		}
	}
};

export const playAudioAsync = async(src: string, timeout?: number) => {
	// 临时兼容方案 等待几百毫秒
	// 多件混扫分拣码提示 需要等分拣码播报完 再进行了其他语音提示
	if (timeout) {
		await new Promise((resolve, reject) => { setTimeout(resolve, timeout); });
	}
	console.log('audio', timeout);

	if (audio) {
		audio.src = src;
		const playPromise = audio.play();
		if (playPromise !== undefined) {
			playPromise.catch(e => {
				console.warn(e);
			});
		}
	} else {
		audio = new Audio(src);
		const playPromise = audio.play();
		if (playPromise !== undefined) {
			playPromise.catch(e => {
				console.warn(e);
			});
		}
	}
};

/**
 * 提取单件、多件语音播放方法，以备其他地方用
 * @param scanSetting 扫描设置
 * @param isMultiple 是否是多件
 * @param isPlayNumAudio 是否播放数量提示语音
 * @param delayTime 延时
 * !! 开启混扫后，不进行多件/合单提示
 */
export const playNumberAudio = (scanSetting, isMultiple, isPlayNumAudio = true) => {
	const { scanSuccessVoiceClose, scanSuccessNumTips, mixScanMode } = scanSetting;
	if (isPlayNumAudio && scanSuccessNumTips && isMultiple && !mixScanMode) {
		if (!scanSuccessVoiceClose) {
			setTimeout(() => {
				playAudio(voice多件);
			}, 800);
		} else {
			playAudio(voice多件);
		}
	}
};
interface IScanPrintPlayAudio {
	info: any,
	pack: any,
	firstScan?:boolean,
	scanSuccessVoiceClose:number
}

export const scanPrintPlayAudio = ({ info, pack, firstScan = false, scanSuccessVoiceClose = 0 }:IScanPrintPlayAudio) => {
	let hasPlayed = false;
	if (pack.isPending && firstScan) {
		setTimeout(() => {
			playAudio(voice订单已挂起);
		}, 0);
		hasPlayed = true;
	} else if (['REFUND_SUCCESSED', 'REFUND_ING'].includes(info.refundStatus)) {
		setTimeout(() => {
			playAudio(tradeStatusVoiceMap[info.refundStatus]);
		}, 0);
		hasPlayed = true;
	} else if (['WAIT_BUYER_CONFIRM_GOODS', 'TRADE_CLOSED', 'TRADE_FINISHED'].includes(info.orderStatus)) {
		if (info.firstSend) {
			!scanSuccessVoiceClose && playAudio(voice扫描成功);
		} else {
			setTimeout(() => {
				playAudio(tradeStatusVoiceMap[info.orderStatus]);
			}, 0);
			hasPlayed = true;
		}
	}
	return hasPlayed;
};

const forcePrintFirstSend = () => {
	return new Promise((resolve, reject) => {
		Modal.confirm({
			centered: true,
			title: '系统提示',
			okText: '强制打印',
			cancelText: "取消",
			icon: <ExclamationCircleOutlined />,
			content: (
				<div>同一运单号下有未勾选订单，是否强制打印</div>
			),
			onOk() {
				resolve(true);
			},
			onCancel() {
				resolve(false);
			},
		});
	});
};

const tradeConfirmTip = ({ tipInfo, okText = '继续打印', title = '系统提示' }) => {
	return new Promise((resolve, reject) => {
		disabledLabelInput(); // 先确保唯一码输入框失焦

		const onScanning = () => {
			console.log('%c [ 扫描任意条形码即可取消打印-回车 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', '');

			domInputBlur();
			tradeConfirmTipModal?.destroy?.();

			resolve(false);
			focusLabelInput();
		};


		tradeConfirmTipModal?.destroy?.();

		tradeConfirmTipModal = Modal.confirm({
			centered: true,
			title,
			okText,
			cancelText: "取消",
			icon: <ExclamationCircleOutlined />,
			content: (
				<div>
					<div>{tipInfo}</div>
					<div className="r-mt-10">注意，扫描任意条形码即可取消打印。</div>
					<input
						style={ { opacity: 0 } }
						id="scanPrintCodeIdInput"
						autoComplete="off"
						// onChange={ () => onScanning() }
						onKeyDown={ (e) => {
							if (e.key === 'Enter') {
								e.preventDefault();
								onScanning();
							}
						} }
					/>
				</div>
			),
			onOk() {
				console.log('%c [ 触发继续打印 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', '触发继续打印');
				resolve(true);
			},
			onCancel() {
				console.log('%c [ 触发取消打印 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', '触发取消打印');
				resolve(false);
				focusLabelInput();
			},
			zIndex: 10000,
			autoFocusButton: null, // 指定自动获得焦点的按钮
			keyboard: false, // 不支持esc 关闭
			closable: false,
		});

		domInputFocus();
	});
};

export const makeTradeData4ScanPrint = async(list: IData[], historySids?:string[], isForcePrint?:boolean, ydNoObj?:any, isAutoPrint?:boolean, logReprint?:boolean) => {
	console.log('ydNoObjL:', ydNoObj);
	timer.start('快销组装打印数据');
	const { scanPrintFormSetting, getScanPrintSetting, setShipData, scanPrintTempInfo, packageInfoVos } = scanPrintStore;

	const { templateInfo, template, printer, senderInfo, tagPrinter } = scanPrintFormSetting;

	const { isMergePrint, setting } = tradeStore;

	const { userInfo } = userStore;

	const customLogPost = (dataType: string, data:any = {}) => {
		window.errorCollection?.customMessageUpload({
			type: `扫描打印【makeTradeData4ScanPrint: ${dataType}】`,
			data: {
				isMergePrint,
				scanPrintFormSetting,
				...data
			}
		});
	};

	console.log('scanPrintFormSetting:', scanPrintFormSetting, template);
	console.log('list:::', list);

	if (!templateInfo) {
		message.error('请先选择快递单模板');
		customLogPost('请先选择快递单模板');
		return;
	}

	if (!printer) {
		message.error('请先选择打印机');
		customLogPost('请先选择打印机');
		return;
	}

	if (!senderInfo) {
		message.error('请先选择发件人');
		customLogPost('请先选择发件人');
		return;
	}
	const advancedRes = await memoFn.getAdvancedSet();
	// 高级设置-1688回流明文设置-是否使用对应平台面单打印
	let isUsePlatTemp = advancedRes?.refluxOrderPrintSet == refluxOrderPrintSettingsEnum.使用对应平台面单打印;

	// * 平台与模板是否匹配
	if (!isMergePrint) {
		let kddType = template?.KddType;
		const platform = templateType4PlatformTypeEnum[kddType] || '';
		let tempPlatform = list[0].platform;
		const hlEncryptOrder = list[0]?.parent?.hlEncryptOrder;
		const hlPlatformType = list[0]?.parent?.hlPlatformType;

		// 1688回流订单 明文和密文处理逻辑：
		if (tempPlatform == PLAT_ALI && hlPlatformType) {
			// 密文、或者高级设置中设定为平台的明文(这两种只能用对应平台面单打印)
			if (hlEncryptOrder || (isUsePlatTemp && !hlEncryptOrder && hlPlatformType !== 'hl-sph')) {
				const hlPlat = HL_PLAT[hlPlatformType];
				tempPlatform = hlPlat === PLAT_OTHER ? PLAT_ALI : hlPlat;
			} else {
				tempPlatform = PLAT_TB;
			}
		} else if ([PLAT_ALI, PLAT_C2M].includes(tempPlatform)) {
			tempPlatform = PLAT_TB;
		}
		if (platform !== tempPlatform && !dealPlatAsHandPlat(tempPlatform, null, packageInfoVos[0])) {
			message.error('电子面单模版与订单平台不匹配');
			playAudio(voice打印失败);
			customLogPost('电子面单模版与订单平台不匹配');
			return;
		}
	} else if (!template?.userTemplateList.length) {
		message.warning('当前模板组暂无模板，请添加模板或更换模板组');
		playAudio(voice打印失败);
		customLogPost('当前模板组暂无模板，请添加模板或更换模板组');
		return;
	}
	// 1. "其他" 平台使用抖音电子面单打印:增加拦截，提示与当前模版校验保持一致
	if ((list?.[0]?.platform == PLAT_OTHER || list?.[0]?.platform == PLAT_HAND) && template.platform == PLAT_FXG) {
		playAudioAsync(voice打印失败, 500);
		message.error(`当前模板与勾选订单所属平台不一致，请重新选择模版。`);
		customLogPost(`当前模板与勾选订单所属平台不一致，请重新选择模版。`, { templateInfo, list });
		return;
	}
	const name = isMergePrint ? template?.exCode : template.ExCode;
	if (list.some(i => i?.parent?.appointmentArrival) && !APPOINTMENT_ARRIVAL_PLAT.includes(name)) {
		message.error('承诺日达订单平台限制必须使用顺丰或京东快递模版打印模板打印');
		playAudio(voice打印失败);
		customLogPost('承诺日达订单平台限制必须使用顺丰或京东快递模版打印模板打印', { name });
		return;
	}

	if (list.some(order => order.needSerialNumber && !order.productIdCode)) {
		message.error('订单下商品未填写商品识别码，根据平台发货要求，请前往订单页面填写商品识别码后打单发货。');
		customLogPost('订单下商品未填写商品识别码，根据平台发货要求，请前往订单页面填写商品识别码后打单发货', { list: list.filter(order => order.needSerialNumber && !order.productIdCode) });
		return;
	}

	if (list.some(i => i.refundStatus != "NOT_REFUND")) {
		const tids = list.filter(i => i.refundStatus != "NOT_REFUND").map(i => i.parentId);
		const tipInfo = <>【{tids.join(',')}】订单中<span className="r-c-error">存在退款宝贝</span>，请选择处理方式。</>;
		customLogPost('订单中存在退款宝贝,弹出弹框', { tids });
		let isRes = await tradeConfirmTip({ tipInfo });
		if (!isRes) {
			customLogPost('订单中存在退款宝贝,取消打印', { tids });
			return;
		}
		customLogPost('订单中存在退款宝贝,继续打印', { tids });
	}

	// hasSendAndNoSend 1 只有待发货 2 只有已发货 3 待发货和已发货
	let hasSendAndNoSend = 0;
	let ydNo = "";
	let isFirstSend = false;
	// * 申请单号时的订单编号 从底单通过运单号得到
	let applySidOrderIds = "";
	// 先发货订单 模板需与左侧选择的快递模板一致
	for (let item of list) {
		if (item.firstSend) {
			// 只需要判断同类型就好
			if (isMergePrint) {
				let hasTempType = template.userTemplateList.find((_temp:any) => _temp.exId == item.exId);
				if (!hasTempType) {
					message.warning('订单商品模版不一致，请选择相同模板订单商品进行手动打印');
					playAudio(voice打印失败);
					customLogPost('订单商品模版不一致，请选择相同模板订单商品进行手动打印', { });
					return;
				}
			} else if (template.Exid != item.exId) {
				console.log('template.Exid:', template.Exid, item.exId);
				message.warning('订单商品模版不一致，请选择相同模板订单商品进行手动打印');
				playAudio(voice打印失败);
				customLogPost('订单商品模版不一致，请选择相同模板订单商品进行手动打印', { });
				return;
			}

			if (ydNo && ydNo != item.ydNo) {
				message.warning('订单商品对应多个运单，请选择相同运单号进行手动打印');
				playAudio(voice打印失败);
				customLogPost('订单商品对应多个运单，请选择相同运单号进行手动打印', { ydNo, item });
				return;
			}
			isFirstSend = true;
			ydNo = item.ydNo;
			applySidOrderIds = item.applySidOrderIds;
		}
		if (hasSendAndNoSend != 3) {
			(item.orderStatus == "WAIT_SELLER_SEND_GOODS") && (hasSendAndNoSend = hasSendAndNoSend == 2 ? 3 : 1);
			(item.orderStatus == "WAIT_BUYER_CONFIRM_GOODS") && (hasSendAndNoSend = hasSendAndNoSend == 1 ? 3 : 2);
		}

		if (!item.firstSend && isForcePrint) {
			applySidOrderIds = item.applySidOrderIds;
		}
	}
	console.log('hasSendAndNoSend::', hasSendAndNoSend);
	let useNewYdNo = false;
	// 先发货中是否勾选了已发货与未发货
	if (hasSendAndNoSend === 3) {
		const tipProps = { tipInfo: '勾选商品包含已发货与未发货商品，是否使用新单号打印', title: '系统提示', okText: '强制打印' };
		customLogPost('勾选商品包含已发货与未发货商品，是否使用新单号打印,弹出弹框', { hasSendAndNoSend, list });
		let res = await tradeConfirmTip(tipProps);
		useNewYdNo = true;

		if (!res) {
			customLogPost('勾选商品包含已发货与未发货商品，取消使用新单号打印', { useNewYdNo });
			return;
		}
		customLogPost('勾选商品包含已发货与未发货商品，使用新单号继续打印', { useNewYdNo });
	}
	let needPrintTid = null; // 打印取号用的tid
	// 为false 表明先发货订单没出现确认弹窗提示，则走一致性校验拦截
	let showConfirmFlag = false;
	// 先发货拆单发货
	if (ydNo && !useNewYdNo && ydNoObj?.[ydNo]?.noSelected > 0) {
		showConfirmFlag = true;
		customLogPost('同一运单号下有未勾选订单，是否强制打印', { ydNo, useNewYdNo, ydNoObj });
		let res = await forcePrintFirstSend();
		if (!res) {
			customLogPost('同一运单号下有未勾选订单，取消打印', { ydNo });
			return;
		}
		customLogPost('同一运单号下有未勾选订单，强制打印', { ydNo });
		// !! 如果先发货手动打印触发,强制打印弹窗，则走强制补打弹窗逻辑，让客户自主选择是原单号还是新单号打印
		needPrintTid = applySidOrderIds;
		historySids = [ydNo];
		isForcePrint = true;
		// if (alreadySend === 0) needPrintTid = `${list[0].parent.tid}}`;
	} else if (isForcePrint && (!useNewYdNo || !ydNo)) {
		// !!这里加个判断逻辑，如果选择使用原单号，但是原单号为空的时候，needPrintTid为null，让打印组件自己算
		needPrintTid = historySids?.length ? applySidOrderIds : null;
	}
	console.log('needPrintTid===try', applySidOrderIds, isForcePrint, ydNo, useNewYdNo, ydNoObj, needPrintTid);

	// !! 先发货手打自动打印也走一致性校验逻辑
	if (isFirstSend && !isForcePrint && !showConfirmFlag) {
		console.log('先发货手动打印自动打印添加拦截');
		isAutoPrint = true;
	}

	let res;
	let data;

	if (list[0].platform === PLAT_FXG && !(packageInfoVos?.[0]?.trades?.[0]?.source == "HAND" && packageInfoVos?.[0]?.trades?.[0]?.tradeEncodeType != 2)) {
		let modeListShowId;
		let exId;
		if (isMergePrint) {
			let orderMatchPlatform = setting?.groupPrintSetJsonString?.orderMatchSetting || [];
			let orderKddType = PRINT_MAP[list[0].platform];
			if (dealPlatAsHandPlat(list[0].platform, null, packageInfoVos[0])) {
				let orderSettingType = orderMatchPlatform.find(o => o.platform === transferOtherToHand(list[0].platform, packageInfoVos[0]));
				orderKddType = orderSettingType.bindControlType;
			}
			if (orderKddType) {
				let tempType = template.userTemplateList.find((item:any) => item.expressType == orderKddType);
				modeListShowId = tempType?.userTemplateId;
				exId = tempType?.exId;
			}
		} else {
			modeListShowId = template.Mode_ListShowId;
			exId = template.Exid;
		}
		const requestArr = [
			TradeTradeDetailGetApi({
				tradeInfos: [{
					tids: [...new Set(list.map(item => item.parentId))],
					platform: list[0].platform,
					sellerId: list[0].sellerId,
				}]
			}), getDyDefaultAddressApi({
				modeListShowId,
				modeType: 3,
				exId,
			})
		];
		const tempCache = window.comp.Print.Data.kddTempInfos.find(o => o.ModeListShow.Mode_ListShowId == modeListShowId);
		if (!tempCache) {
			requestArr.push(getTemplateInfoByShowIdApi({
				action: 'GetTemplateInfoByShowId',
				exuserId: userInfo.userId,
				modeListShowId,
				modeType: 1,
			}));
		}
		res = await Promise.all(requestArr);
		(res || []).push(tempCache);
		data = res[0].data.list[0];
	} else {
		// * TradeTradeDetailGetApi
		res = await TradeTradeDetailGetApi({
			tradeInfos: [{
				// tids: togetherId,
				tids: [...new Set(list.map(item => item.parentId))],
				platform: list[0].platform,
				sellerId: list[0].sellerId,
			}]
		});

		data = res.data.list[0];
	}

	// BIC质检订单不支持操作
	if (data?.serviceTagList?.includes('bic_order')) {
		message.error('BIC质检订单不支持操作');
		textTransformAudioPlay('B I C 质检订单不支持操作');
		customLogPost('BIC质检订单不支持操作', { tids: data.tids });
		return;
	}

	// 得物品牌直发—普通履约、多仓发货标签的订单需匹配得物模版
	if (data.platform == PLAT_DW && (data?.serviceTagList?.includes('ordinaryOrder') || data?.serviceTagList?.includes('warehouseOrder')) && !["DWTYMB"].includes(name)) {
		message.error('不支持得物品牌直发订单处理，请选择【得物通用模板】使用官方电子面单打印');
		playAudio(voice打印失败);
		customLogPost('不支持得物品牌直发订单处理，请选择【得物通用模板】使用官方电子面单打印', { tids: data.tids });
		return;
	}

	console.log(res, data, '1111');


	// * transfrom data
	// if (list[0].firstSend && !useNewYdNo) {
	if (!useNewYdNo) {
		data.ydNo = list[0].ydNo || "";
		data.exId = list[0].exId || "";
		data.exName = list[0].exName || "";
		data.needPrintTid = needPrintTid ? [needPrintTid] : null;
	}
	// 模板组与订单不匹配校验
	if (isMergePrint) {
		let orderMatchPlatform = setting?.groupPrintSetJsonString?.orderMatchSetting || [];
		let tempPlatform = data.platform;
		const hlEncryptOrder = data?.hlEncryptOrder;
		const hlPlatformType = data?.hlPlatformType;
		// 1688回流订单 明文和密文处理逻辑：
		if (tempPlatform == PLAT_ALI && hlPlatformType) {
			// 密文、或者高级设置中设定为平台的明文(这两种只能用对应平台面单打印)
			if (hlEncryptOrder || (isUsePlatTemp && !hlEncryptOrder && hlPlatformType !== 'hl-sph')) {
				const hlPlat = HL_PLAT[hlPlatformType];
				tempPlatform = hlPlat === PLAT_OTHER ? PLAT_ALI : hlPlat;
			}
		}
		let orderKddType = PRINT_MAP[tempPlatform];
		if (dealPlatAsHandPlat(tempPlatform, null, packageInfoVos[0])) {
			let orderSettingType = orderMatchPlatform.find(o => o.platform === transferOtherToHand(tempPlatform, packageInfoVos[0]));
			orderKddType = orderSettingType.bindControlType;
		}
		if (orderKddType) {
			let tempType = template.userTemplateList.find((item:any) => {
				if ([13, 16].includes(Number(item.expressType))) {
					return orderKddType == 13;
				} else {
					return item.expressType == orderKddType;
				}
			});
			if (!tempType) {
				message.warning(`当前订单无法使用${template.groupName}打印，请更换模板组或添加模板`);
				playAudio(voice打印失败);
				customLogPost(`当前订单无法使用${template.groupName}打印，请更换模板组或添加模板`, { orderKddType, tempType, data });
				return;
			}
		}

	}
	let labelIds: string[] = [];
	// 通过勾选的商品来给order.isChecked赋值
	list.forEach(item => {
		for (let i = 0; i < data.trades.length; i++) {
			let orderIndex = data.trades[i].orders.findIndex(order => order.oid === item.oid);
			if (orderIndex > -1) {
				let orderItem = data.trades[i].orders[orderIndex];
				orderItem.isChecked = true;
				item.labelIds && labelIds.push(...item.labelIds);
			}
		}
	});
	// * 生成打印内容
	let printContent : TradePrintContentSetRule = null;
	let keyWords:string[] = null;
	let advancedSetRes;
	try {
		printContent = await tradeSetStore.getPrintContentSet();
		const filterWord = await tradeSetStore.getFilterWord();
		keyWords = filterWord;
		advancedSetRes = await memoFn.getAdvancedSet();
	} catch (error) {
		console.error('获取发货内容设置失败', error);
		customLogPost(`获取发货内容设置失败`, { error });
	}
	try {
		console.log(4, 'advancedSetRes: ', advancedSetRes);
		data.printContent = genePrintContent(data, printContent, keyWords, advancedSetRes);
	} catch (error) {
		console.error(error);
		customLogPost(`获取发货内容失败`, { error });
	}

	calcPackTotalNum(data); // 这里获取数量

	data.isChecked = true;
	data.labelIds = labelIds;

	setShipData(data);

	console.log(data, 'data');

	const sender = {
		[data.sellerId]: {
			info: {
				id: senderInfo.id,
				city: senderInfo.senderCity,
				district: senderInfo.senderDistrict,
				address: senderInfo.senderAddressDetail,
				name: senderInfo.senderName,
				phone: senderInfo.senderMobile || senderInfo.senderPhone,
				postCode: senderInfo.senderPostCode,
				province: senderInfo.senderProvince,
				sign: senderInfo.senderSign
			},
			taobaoNick: data.sellerNick
		}
	};
	timer.stop('快销组装打印数据', {
		type: logTypeEnum['小标签'],
	});
	pageLoading.loading(true);
	disabledLabelInput(); // 打印前失焦
	console.log('isAutoPrint && isFirstSend', isAutoPrint && isFirstSend);
	PrintCenterAPI.scanPrintKdd({
		fjrInfo: {
			isUseMore: false,
			isUseCommon: true,
			sender
		},
		// 只有先发货且自动打印的场景下才会进行原单号一致性强校验
		isMustUseOldCode: isAutoPrint && isFirstSend,
		orderList: [data],
		templateInfo: template,
		choosedPrinter: printer,
		choosedTagPrinter: tagPrinter,
		isMerge: isMergePrint,
		historySids, // 强制补打-原单号列表回显
		isForcePrint,
		templateInfoDetail: res?.[2] || '',
		defaultAddress: res?.[1] || '',
		logReprint, // 扫描打印先发货底单重打
		printWebSource: 1
	} as any);
};
// 是否为先发货 打印次数+商品状态+运单号
// export const isFirstSend = (info)=>{
// 	return info.orderStatus === "WAIT_BUYER_CONFIRM_GOODS" &&
// }

// 文字转语音通用方法
const createSpeechSynthesis = () => {
	let msgInteraction = null;

	return () => {
		if (('speechSynthesis' in window) && !msgInteraction) {
			// 创建一个新的SpeechSynthesisUtterance对象
			msgInteraction = new SpeechSynthesisUtterance("");
			// http://www.lingoes.net/en/translator/langcode.htm
			// zh zh-CN zh-HK zh-MO zh-SG zh-TW
			msgInteraction.lang = "zh-CN";
		}
		return msgInteraction;
	};
};

const createSpeechSynthesisInteraction = createSpeechSynthesis();

export const speechSynthesisInteraction = createSpeechSynthesisInteraction();

// 语音助手插件端口
const createWebSocket = () => {
	let sw = null;
	let isOpen = false;
	return () => {
		try {
			if (!sw) {
				sw = new WebSocket("ws://127.0.0.1:515351");
				sw.onopen = () => {
					isOpen = true;
				};
				return { sw, isOpen };
			}

		} catch (error) {
			console.log('语音助手插件端口连接失败');
			return { sw, isOpen };
		}
	};
};

export const createWebSocketInter = createWebSocket()();

// 发言方言问题处理
const transfromText = text => text.replace('-', '杠').replace('2', '二');

export const textTransformAudioPlay = async(text: string, timeout = 0, config = { volume: 100, rate: 1 }) => {
	// 临时兼容方案 等待几百毫秒
	// 多件混扫分拣码提示 需要等分拣码播报完 再进行了其他语音提示
	const { scanPrintSetting } = scanPrintStore;
	// 多件订单混扫备齐模式
	const mixScanMode = !!scanPrintSetting?.scanSet?.mixScanMode;
	if (mixScanMode && timeout) {
		await new Promise((resolve, reject) => { setTimeout(resolve, timeout); });
	}

	// 1. 基于Web Speech API
	// 检查浏览器是否支持Web Speech API
	if (!speechSynthesisInteraction && !createWebSocketInter) {
		console.error('该浏览器不支持语音转文字功能且没装语音控件');
		return;
	}

	if (speechSynthesisInteraction) {
		const { volume, rate } = config;
		// 发言方言问题处理

		speechSynthesisInteraction.volume = volume; // 音量
	    speechSynthesisInteraction.rate = rate; // 语速
	    speechSynthesisInteraction.text = transfromText(text); // 文字
	    speechSynthesis.speak(speechSynthesisInteraction); // 播放语音
		return;
	}
	console.error('浏览器不支持Web Speech API');
	// 加一些判断ws链接的逻辑 websock 通用方法
	// 2.判断是否链接融合控件 基于融合控件播报

	const { sw, isOpen } = createWebSocketInter;
	try {
		if (isOpen) {
			sw.send(JSON.stringify({
				"api": "play_text",
				"data": {
					text,
					"mode": "queue"
				}
			}));
		}
	} catch (error) {
		console.log('语音助手插件未安装');
	}

};
