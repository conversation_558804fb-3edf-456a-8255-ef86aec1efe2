import React, { useState, useEffect } from 'react';
import { Modal, Radio, Button, message } from 'antd';
import { observer } from 'mobx-react';
import userStore from '@/stores/user';
import { SettingSaveUserSettingApi } from '@/apis/user';
import s from './index.module.scss';

interface ZeroStockConfigModalProps {
	visible: boolean;
	onCancel: () => void;
}

const ZeroStockConfigModal: React.FC<ZeroStockConfigModalProps> = (props) => {
	const { visible, onCancel } = props;
	const [loading, setLoading] = useState(false);
	const [stockConfig, setStockConfig] = useState('standard'); // 'standard' | 'zero'
	const [userSetting, setUserSetting] = useState<any>({});

	useEffect(() => {
		if (visible) {
			userStore.getUserSetting().then((res: any) => {
				setUserSetting(res);
				// 从用户设置中获取当前配置，如果没有则默认为标配版
				const currentConfig = res?.extraSetting?.zeroStockConfig || 'standard';
				setStockConfig(currentConfig);
			});
		}
	}, [visible]);

	const handleSubmit = () => {
		setLoading(true);
		const newExtraSetting = {
			...userSetting.extraSetting,
			zeroStockConfig: stockConfig
		};
		
		SettingSaveUserSettingApi({
			...userSetting,
			extraSetting: newExtraSetting
		}).then(async(res) => {
			if (res) {
				message.success('保存成功');
				let currentSetting = {
					...userStore.userSetting,
					extraSetting: newExtraSetting
				};
				userStore.setUserSetting(currentSetting);
				onCancel();
			} else {
				message.error('保存失败');
			}
		}).finally(() => {
			setLoading(false);
		});
	};

	return (
		<Modal
			title="零库存配置"
			visible={visible}
			onCancel={onCancel}
			width={600}
			centered
			maskClosable={false}
			footer={[
				<Button key="cancel" onClick={onCancel}>
					取消
				</Button>,
				<Button key="submit" type="primary" loading={loading} onClick={handleSubmit}>
					确定
				</Button>
			]}
		>
			<div className={s.container}>
				<div className={s.section}>
					<h4 className={s.sectionTitle}>库存版本</h4>
					<div className={s.configOptions}>
						<div className={s.optionGroup}>
							<div className={s.optionHeader}>
								<Radio
									checked={stockConfig === 'standard'}
									onChange={() => setStockConfig('standard')}
								>
									标配版本
								</Radio>
							</div>
							<div className={s.optionContent}>
								<div className={s.optionItem}>
									<span className={s.optionNumber}>1</span>
									<span>我们将商品库存作为订单检测的依据</span>
								</div>
								<div className={s.optionItem}>
									<span className={s.optionNumber}>2</span>
									<span>我们将商品本地货品，与货品库存数据的价格作为比较</span>
								</div>
							</div>
						</div>
						
						<div className={s.optionGroup}>
							<div className={s.optionHeader}>
								<Radio
									checked={stockConfig === 'zero'}
									onChange={() => setStockConfig('zero')}
								>
									零库存版本
								</Radio>
							</div>
							<div className={s.optionContent}>
								<div className={s.optionItem}>
									<span className={s.optionNumber}>1</span>
									<span>我们将商品库存作为订单检测的依据</span>
								</div>
								<div className={s.optionItem}>
									<span className={s.optionNumber}>2</span>
									<span>我们将商品本地货品，与货品库存数据的价格作为比较</span>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</Modal>
	);
};

export default observer(ZeroStockConfigModal);