import * as printItem from '../printItem';

class PrintBhdXbq {
    constructor(hook = {}) {
        const {
            afterCheckComponentHook,
            afterChooseTempHook,
            afterChooseFjrHook,
            afterSavePrintMarkHook,
            afterSavePrintLogHook,
        } = hook || {};

        afterCheckComponentHook && (this.afterCheckComponentHook = afterCheckComponentHook);
        afterChooseTempHook && (this.afterChooseTempHook = afterChooseTempHook);
        afterChooseFjrHook && (this.afterChooseFjrHook = afterChooseFjrHook);
        afterSavePrintMarkHook && (this.afterSavePrintMarkHook = afterSavePrintMarkHook);
        afterSavePrintLogHook && (this.afterSavePrintLogHook = afterSavePrintLogHook);
    }

    // 具体流程实现
    async print(data) {
        console.log('打印备货单小标签 传给打印中心的数据');
        console.log({ data });
        let {
            tempId,
            orderList,
            scanPrintSetting,
			sortCodeSetting,
            hasView,
            selectedPrinter,
            printType,
            source,
            hotTempId, //爆款模板id
            hotOrderList = [],// 爆款列表
			isLiveTag,
			printStyle,
			isPrintSubGoods
        } = data;
        let newPrintType = printType
        if( printType === 'thd' ){
            let temps =comp.Print.Data.bqTemplateListthd
            tempId = temps.ModeListShowId
        }else if(['cgd','rkd','ckd','thqd'].includes(printType)){
            let temps = comp.Print.Data['bqTemplateList' + printType]
            tempId = await printItem.chooseTemplateXbqTempDialog(temps,printType);
            if (!tempId) {
                return false;
            }
        } else if(['dpd','tmd'].includes(printType)){
            if(printItem.isZeroStockVersion()) newPrintType = 'w'+printType
            let temps = Object.assign({},comp.Print.Data.bqTemplateListdpd)
            temps.ModeListShows = temps.ModeListShows.filter((item) => {
                return item.ExCode.toLowerCase() === newPrintType
            })
            tempId = await printItem.chooseTemplateXbqTempDialog(temps,printType);
            if (!tempId) {
                return false;
            }
        }else if(!tempId && printType !== 'bic') {
            let temps = printType === 'thd' ? comp.Print.Data.bqTemplateListthd : comp.Print.Data.bqTemplateListbq
            let selectTempId = await printItem.chooseTemplateXbqTempDialog(temps.ModeListShows,printType);
            if (!selectTempId) {
                return false;
            }
            // 提供出一个钩子，供外部选择是否要继续打印
            const result = this.afterChooseTempHook(selectTempId);
            if (result && result.isBreak === true) {
                return;
            }

        }
        this.params = data;
        // const printType = 'fhd';
        const userInfo = comp.Print.Data.userInfo;
        let total = orderList.length + hotOrderList.length
        let choosedPrinter = null
        let hotTemplateInfo = null
        let hotTemplateDetailInfo = null
        let xbqPrintNums = 1
        let dpdPrintNumType = null
		if (isPrintSubGoods) {
			let subTotal = 0
			orderList.forEach(it => {
				it.groupCombinationList.forEach(o => subTotal += o.num)
			})
			total = subTotal
		}
        // // lodop控件校验
        const installResult = printItem.checkLodopProgress();
        this.afterCheckComponentHook(installResult);
        if (!installResult.isSuccess) {
            return;
        }

        // 获取模板详情
        const templateDetailInfo = await printItem.getTempDetailInfo({
            printType,
            templateId: tempId,
        });

        const templateInfo = templateDetailInfo.ModeListShow;

        if(hotTempId){

             hotTemplateDetailInfo = await printItem.getTempDetailInfo({
                printType,
                templateId: hotTempId,
            });

            hotTemplateInfo = hotTemplateDetailInfo.ModeListShow;
        }
        // 获取本次打印使用的的发件人
        // let fjrInfo, modifyDefaultFjr;
        // if( !isSmallTag(templateInfo) ){
        //     ( { fjrInfo,modifyDefaultFjr } = await printItem.chooseFjr(fjrInfoMap,printType) );
        //     modifyDefaultFjr && modifyDefaultFjr.length && this.afterChooseFjrHook(modifyDefaultFjr);
        // }

        // 重打提醒
        // const hasPrinted = await printItem.checkOrderHasPrint(orderList, 'fhd');
        // await printItem.surePrintAgain({
        //     printType,
        //     templateInfo,
        //     hasPrinted,
        //     orderList,
        // });

        // 选择打印机
        if(!selectedPrinter){
            const {
                choosedPrinter:printer,
                printNums,
                isPreview,
                dpdPrintType
            } = await printItem.choosePrinter({
                orderList,
                // bqName: '小标签',
                // closeChoosePrinterNoti: choosePrinterNotiSet && printOp == 1,
                defaultPrinter: ((templateDetailInfo || {}).ModeTempPrintcfg || {}).DefaultPrinter,
                printType,
                templateInfo,
                templateDetailInfo,
                source,
				inputPrintNum: ['dpd', 'tmd'].includes(printType),
				displayPrintNum: ['cgd', 'rkd', 'ckd', 'thqd'].includes(printType),
                // orderNum: orderList.length,
                // isHasAutoShip: false,
                // shipAfterPrint: false,
                // isCloudPrint: false,
                // isHasPreview: ['dpd','tmd','wdpd','wtmd','ckd','rkd','cgd'].includes(printType),
                isHasPreview: true,
            });
            choosedPrinter = printer
            if(['dpd','tmd'].includes(printType)) {
                hasView = isPreview
                xbqPrintNums = printNums
            }
            hasView = isPreview
            dpdPrintNumType = dpdPrintType
        }else {
            // 前置选择打印机如果未传打印份数需要重置打印份数 默认1份
            comp.Print.setPrintCopies(1);
            choosedPrinter = selectedPrinter
        }

        if(source){
            const sourcePoint = {
                sdck:'采购_采购单_立即打印',
                cgrk:'采购_采购单_立即打印',
                dhrk:'库存_到货入库_立即打印',
            }
            Tatami.pub('otherPorint',sourcePoint[source])
        }
        //获取打印序号
        orderList = await printItem.dealWithPrintNum({
            orderList: orderList,
            printType: newPrintType,
        });
        //获取打印序号
        hotOrderList = hotOrderList.length && await printItem.dealWithPrintNum({
            orderList: hotOrderList,
            printType: printType,
        });
		if(isLiveTag){
			orderList = await printItem.livePrintNum({
				orderList: orderList,
			});
		}
        // const { fhdNoArr, lsn } = await printItem.getScanFhdId({
        //     orderList,
        //     total,
        //     templateDetailInfo,
        //     groupId,
        // });
        // // if(newSidNum){ newSidNum根据业务方返回的  pData.total - oldSidNum 是根据sortCodeList 有的 sid 个数了；
        // orderList = await printItem._dealWithSid({
        //     orderList,
        //     fhdNoArr,
        // });
        // // }
        // // 更新顺序
        // await printItem._updateFjhNo(fjhNoMap);
        let startSerialNo = 0;
        startSerialNo = await printItem._getSerialNo({ total, templateDetailInfo });
        let printTotalCount = Number(total*xbqPrintNums)
        if(dpdPrintNumType == 1 && source){
            printTotalCount = 0
            let keyObj = {
                sdck:'count',
                cgrk:'itemCount',
                dhrk:'instockCount',
            }
            orderList.forEach(element => {
                printTotalCount += Number(element[keyObj[source]] || 0)
            });
        }
        //打印进度弹窗
        const progress = new printItem.getPrintProgress({
            showType: 'mark',
            totalCount: printTotalCount
        });

        const extraType = 'xbq';
        // // 拼多多打印小标签标记

        progress.showType('progress');

        // 组装打印数据
        const printDatas = printItem.getFormatBhdXbqPrintData({
            orderList,
            startSerialNo,
            scanPrintSetting,
            printType:templateInfo.Modeid,
            dpdPrintNumType,
			printStyle,
			source,
			isPrintSubGoods
        });
        const hotPrintDatas = hotOrderList.length && printItem.getFormatBhdXbqPrintData({
            orderList:hotOrderList,
            startSerialNo,
            scanPrintSetting,
            printType:hotTemplateInfo.Modeid,
            dpdPrintNumType,
            source
        });

        // 店铺标识数据处理 业务方做完了
        // printDatas = await printIt em.useSelfShopSign(printDatas,userInfo)
        //打印
        this._doPrint({
            orderList,
            printDatas,
            progress,
            printType,
            choosedPrinter,
            templateInfo,
            templateDetailInfo,
            hotPrintDatas,
            hotTempId,
            hotTemplateDetailInfo,
            extraType,
            startSerialNo,
            hasView,
        });
    }

    _doPrint({
        orderList,
        printDatas,
        progress,
        printType,
        choosedPrinter,
        templateInfo,
        templateDetailInfo,
        hotPrintDatas = [],
        hotTempId,
        hotTemplateDetailInfo,
        extraType,
        hasView
    }) {
        const self = this;
        let printParams1 = null
        let printParams2 = null
        if(hotPrintDatas.length){
            printParams1 = {
                printDatas: hotPrintDatas,
                tempId: hotTempId,
                ptype: printType,
                printBoxIsShow: 0,
                selectedPrinter: choosedPrinter,
                templateSet: hotTemplateDetailInfo,
            };
            printParams2 = {
                printDatas: printDatas,
                tempId: templateInfo.Mode_ListShowId,
                ptype: printType,
                printBoxIsShow: 0,
                selectedPrinter: choosedPrinter,
                templateSet: templateDetailInfo,
                hasView
            };
        }else{
            printParams1 = {
                printDatas: printDatas,
                tempId: templateInfo.Mode_ListShowId,
                ptype: printType,
                printBoxIsShow: 0,
                selectedPrinter: choosedPrinter,
                templateSet: templateDetailInfo,
                hasView
            };
        }

        const p = new comp.Print();
        let isOver = false
        //打印回调methods获取
        const methodsCb = {
            printSynFunc: function (curCount) {
                progress.updateProgress({curCount});
            },
            printOkFunc: async function () {
                //底单日志存储
                progress.showType('log');
                // await printItem.saveFhdLog({
                //     orderList,
                //     fhdNoArr,
                //     lsn,
                //     extraType,
                // });
                // const results = await printItem.newMarkPrintStatus({
                //     orderList,
                //     printType: printType,
                //     templateInfo,
                //     printer: choosedPrinter,
                //     extraType,
                //     fhdNoArr,
                //     lsn,
                // }).catch(err=>{
                //     console.log(err);
                // });
                // console.log('saveKddLog 方法的返回值', results);
                if(hotPrintDatas.length && !isOver){
                    isOver = true
                    p.printTemplate(printParams2, methodsCb);

                }else{
                    progress.remove();
                    !hasView && self.afterSavePrintLogHook([...printDatas,...hotPrintDatas]);
                }


            },
        };

        const defaultPrinter = (templateDetailInfo.ModeTempPrintcfg || {}).DefaultPrinter;
        if (choosedPrinter != defaultPrinter && printType !=='bic') {
            p.setDefaultPrinter(printType, choosedPrinter, templateInfo.Mode_ListShowId, function () {
                p.printTemplate(printParams1, methodsCb);
            });
        } else {
            p.printTemplate(printParams1, methodsCb);
        }
    }

    // --------------钩子函数的初始化--------------------
    // 钩子函数
    /**
     * 检测控件安装结果后执行的钩子函数
     * type:'cainiao'|'lodop'
     * result:'update|noInstall'
     */
    afterCheckComponentHook({ type, result }) {
        console.log('afterCheckComponentHook:' + type + result);
    }

    // 选择模板后执行的钩子函数
    afterChooseTempHook(tempId) {
        console.log(tempId);
    }

    // 选择发件人后执行的钩子函数
    afterChooseFjrHook(modifyDefaultFjr) {
        // console.log('afterChooseFjrHook');
        console.log(modifyDefaultFjr);
    }

    // 打印标记完成后执行的钩子函数
    afterSavePrintMarkHook() {
    }

    //保存打印日志后的钩子函数
    afterSavePrintLogHook(){
        console.log('afterSavePrintLogHook',arguments);
    }
    // --------------钩子函数的初始化--------------------

}

export default PrintBhdXbq;
