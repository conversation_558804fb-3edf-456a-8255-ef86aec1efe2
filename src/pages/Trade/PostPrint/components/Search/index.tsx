import React, { useEffect, useState, useRef, useMemo, useCallback } from "react";
import { useHistory } from "react-router-dom";
import { Input, Form, Select, Button, Checkbox, Modal, Collapse, Radio, Dropdown, Menu, notification } from "antd";
import { observer } from "mobx-react";
import { debounce } from "lodash";
import dayjs from "dayjs";
import { EnterOutlined, SettingOutlined, CheckCircleOutlined, CloseCircleOutlined } from "@ant-design/icons";
import message from '@/components/message';
import SelectSender from "./components/SelectSender";
import SelectTemplate from "./components/SelectTemplate";
import SelectTagTemplate from "./components/SelectTagTemplate";
import { tradeStore } from "@/stores";
import scanPrintStore from "@/stores/trade/scanPrint";
import styles from "./index.module.scss";
import { TradeTradeDetailGetApi, getBatchSids, TradeBatchUpdatePendingStatusApi, TradeBatchUpdateMemoApi } from "@/apis/trade";
import { PrintRefundLabelExpressBill, disabledPostPrintLabelInput, focusPostPrintLabelInput, getUniqueKeyByOrderInfo } from "../../utils";
import { PRINT_MAP, flagAudio, flagName, PLAT_DW, PLAT_ALI, PLAT_JD, PLAT_OTHER, PLAT_FXG, PLAT_HAS_ADDRESS_TOWN, PLAT_HAND } from "@/constants";
import { getToken, sleep } from "@/utils/util";
import { genePrintContent } from "@/utils/trade/printContent";
import PrintResult from "./components/PrintResult";
import { local } from "@/libs/db";
import { CLEAR_PAGE_DATA, PRINT_SETTING, autoPrintInterceptTypeEnum, msgRemarksEnum, scanTypeEnum, scanTypeOptions, sellAttributeTypeEnum, KM } from "../../constants";
import userStore from "@/stores/user";
import SelectPrinter from "./components/SelectPrinter";
import tradeSetStore from "@/stores/trade/tradeSet";
import { isOrderMatchingTemplate } from "@/pages/Trade/components/BottomCom/checkAbnormalUtil";
import event from "@/libs/event";
import { pageLoading } from "@/components/PageLoading";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import SettingModal from "../SettingModal";
import { playAudioAsync, textTransformAudioPlay } from "@/pages/Trade/ScanPrint/utils";
import voice打印失败 from '@/assets/mp3/打印失败.mp3';
import voice有缺货 from '@/assets/mp3/有缺货.mp3';
import voice已发货 from '@/assets/mp3/已发货.mp3';
import voice已打印 from '@/assets/mp3/已打印.mp3';
import voice已退款 from '@/assets/mp3/已退款.mp3';
import voice卖家备注 from '@/assets/mp3/卖家备注.mp3';
import voice买家留言 from '@/assets/mp3/买家留言.mp3';
import { TradeStatus } from "@/utils/enum/trade";
import { TradePrintCheckApi } from "@/apis/trade/search";
import { TradeEditConfigForSubApi } from "@/apis/trade/postPrint";
import ShopMultiSelect from "@/components-biz/ShopListSelect/shopMultiSelect";
import { getMultiShops } from "@/components-biz/ShopListSelect/shopListUtils";
import { TradeQueryBgTradeForScanApi } from "@/apis/report/kddLog";
import { ItemSmartExpressGetListApi } from "@/apis/setting/smartExpress";
import { AbnormalType } from '@/pages/Trade/components/BottomCom/constants';
import { AbnormalType as TradeAbnormalType } from '@/components-biz/GenerateLabelWarningModal/constants';
import Icon from "@/components/Icon";
import MarkSettingModal from "./components/MarkSettingModal";
import { preCheckPrintLimit } from '@/utils/print/shopTemp';
import { createSocketHander } from "@/utils/websocket";
import PrintCenter from '@/print/index';
import { BQ_PRINT_TYPE } from "@/pages/Trade/constants";
import videoMonitorStore from "@/stores/trade/videoMonitor";

let scanUniqueCode = String(+new Date());
let tradeInfos = [];
let GLOBAL_DISABLED = false;
let checkTimer = null;

const Search = () => {
	const history = useHistory();
	const [form] = Form.useForm();
	const [settingModalVisible, setSettingModalVisible] = useState(false);
	const {
		isMergePrint,
		kddTempList,
		postPrintStore: {
			postPrintList,
			setPostPrintRes,
			getPostPrintTradeList,
			saveSettingConfig,
			getSettingConfig,
			settingConfig,
			setCurScanGoodInfo,
			allGoodOrderList,
			setAllGoodOrderList,
			allGoodOrderProgress,
			updateAllGoodOrderProgress,
			scanedCount,
			selectedRowKeys,
			setSelectedRowKeys,
			resetStoreNew,
			curOrderPrintSuccess,
			packWsHandler,
			setPackWsHandler,
			packWsHandlerSendFail,
			videoMonitorState,
			selectedShootingDevices,
			setSelectedShootingDevices,
			nvrDevices, setNvrDevices
		}
	} = tradeStore;
	const { 
		clearVideoMonitor,
		startVideoMonitor
	} = videoMonitorStore;
	const { printersList } = scanPrintStore;
	const { senderSetting, isShowZeroStockVersion, userInfo, systemSetting, getSystemSetting } = userStore;
	const { whiteListSetting } = userInfo || {};
	const whiteListSettingObj = JSON.parse(whiteListSetting || '{}');
	const { videoMonitor } = whiteListSettingObj || {}; // 视频监控开关
	const searchInput = useRef(null);
	const [onFocus, setOnFocus] = useState(false);
	const [scanType, setScanType] = useState("");
	const [sellAttributeTypeList, setSellAttributeTypeList] = useState("");
	const [firstSendKddLog, setFirstSendKddLog] = useState<any>({});
	const [printLoading, setPrintLoading] = useState(false);
	const [operateBtnsDefault, setOperateBtnsDefault] = useState({
		skipType: "onlySkip",
	});
	const [markSettingVisible, setMarkSettingVisible] = useState(false);
	const [smartExpressSwitch, setSmartExpressSwitch] = useState(false);
	const [printTagSet, setPrintTagSet] = useState({});
	const [lodopWarn, setLodopWarn] = useState(false);
	const customLogPost = (dataType: string, data:any = {}) => {
		window.errorCollection?.customMessageUpload({
			type: `后置打印: 【 ${dataType} 】`,
			data: {
				scanUniqueCode,
				settingConfig,
				postPrintList,
				isMergePrint,
				...data
			}
		});
	};
	const PRINT_SETTING_USER = `${PRINT_SETTING}_${userStore?.userInfo?.userId}`;
	const GLOBAL_CODE = `scanUniqueCode_${userStore?.userInfo?.userId}_${userStore?.userInfo?.subUserId || ''}`;

	useEffect(() => {
		PrintCenter.getBqTempList(BQ_PRINT_TYPE.吊牌);
		initConfig();
		userStore.getSystemSetting();
		ItemSmartExpressGetListApi({
			queryCondition: {
			}
		}).then(res => {
			if (res) {
				setSmartExpressSwitch(res?.smartExpressConfigVO?.openStatus || false);
			}
		});
		return () => {
			resetStoreNew();
		};
	}, []);

	useEffect(() => {
		try {
			if (!systemSetting) {
				userStore.getSystemSetting();
				return;
			}
			const printTagSet = JSON.parse(systemSetting?.printTagSet);
			setPrintTagSet(printTagSet);
			if (printTagSet?.tagTemplateSwitch === "1") {
				form.setFieldsValue({ tagTempId: printTagSet?.tagTemplate?.Mode_ListShowId });
				if (printTagSet?.tagTemplate?.defaultPrinter) {
					if (printersList.find(printer => printer == printTagSet?.tagTemplate?.defaultPrinter)) {
						form.setFieldsValue({ tagPrinter: printTagSet?.tagTemplate?.defaultPrinter });
					} else {
						form.setFieldsValue({ tagPrinter: printersList?.[0] });
					}
				} else {
					form.setFieldsValue({ tagPrinter: printersList?.[0] });
				}
			} else {
				form.setFieldsValue({ tagPrinter: printersList?.[0] });
			}
		} catch (error) {
			console.log(error);
		}
	}, [systemSetting]);

	useEffect(() => {
		if (settingConfig?.markScanPrintSet) {
			let defaultOperateBtns = { skipType: "" };
			const markScanPrintSet = settingConfig?.markScanPrintSet || {};
			if (markScanPrintSet.skipAndMark) {
				defaultOperateBtns.skipType = "skipAndMark";
			} else {
				defaultOperateBtns.skipType = "onlySkip";
			}
			setOperateBtnsDefault((prev => {
				return {
					...prev,
					...defaultOperateBtns,
				};
			}));
		}
		// if (settingConfig.isPrintItemTag && !lodopWarn) {
		// 	checkLodop();
		// }
	}, [settingConfig]);

	// const checkLodop = () => {
	// 	if (checkTimer) {
	// 		clearTimeout(checkTimer);
	// 		checkTimer = null;
	// 	}
	// 	checkTimer = setTimeout(() => {
	// 		if (window.printAPI?.checkLodopProgress) {
	// 			PrintCenter.checkLodopProgress(false);
	// 			setLodopWarn(true);
	// 		} else {
	// 			checkLodop();
	// 		}
	// 	}, 1000);
	// };

	const initConfig = async() => {
		const setting = await getSettingConfig();
		setScanType(setting.scanType);
		if (setting?.sellAttributeTypeList?.length === 0) {
			sellTypeChange(sellAttributeTypeEnum.单件);
			return;
		}
		setSellAttributeTypeList(setting?.sellAttributeTypeList?.length > 1 ? sellAttributeTypeEnum.全部 : setting?.sellAttributeTypeList?.join(','));
	};

	const scanTypeOptionsMemo = useMemo(() => {
		if (isShowZeroStockVersion) {
			return scanTypeOptions.filter(i => {
				return ![scanTypeEnum.SYS_OUTER_ID, scanTypeEnum.BAR_CODE, scanTypeEnum.ITEM_NO].includes(i.label);
			});
		}
		return scanTypeOptions;
	}, [isShowZeroStockVersion]);

	// 前端生成，并缓存在浏览器本地，默认30分钟有效，用户一直操作时，进行续期
	useEffect(() => {
		const code = local.get(GLOBAL_CODE);

		customLogPost('获取本地有效scanUniqueCode', { scanUniqueCode: code });
		if (code) {
			scanUniqueCode = code;
			console.log(code, 'scanUniqueCode');

		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [GLOBAL_CODE]);

	// 通过模板id获取模板信息
	const getTempInfoByTempId = (tempId) => {
		let templateInfo;
		if (isMergePrint) {
			templateInfo = kddTempList.find((i) => i.id == tempId);
		} else {
			templateInfo = kddTempList.find((i) => i.Mode_ListShowId == tempId);
		}
		return templateInfo;
	};
	/** --------------- 回显用户上次选择的打印选项 ------------------ */

	useEffect(() => {
		const cur = form.getFieldValue('printer');
		if (!cur) {
			const printSetting = local.get(PRINT_SETTING_USER) || {};
			const lastSelectedPrinter = printSetting.printer;
			const firstValue = printersList?.[0];
			let printer: any;
			if (lastSelectedPrinter) {
				printer = lastSelectedPrinter;
			} else if (firstValue) {
				printer = firstValue;
			}
			form.setFieldsValue({ printer });
		}
	}, [PRINT_SETTING_USER, form, printersList]);

	useEffect(() => {
		const cur = form.getFieldValue('template');
		if (!cur) {
			console.log("kddTempList", kddTempList);
			const printSetting = local.get(PRINT_SETTING_USER) || {};
			const lastSelectedTemplate = printSetting.template;
			const firstValue = isMergePrint ? kddTempList?.[0]?.id : kddTempList?.[0]?.Mode_ListShowId;
			let template: string;
			if (lastSelectedTemplate) {
				template = lastSelectedTemplate;
				const field = isMergePrint ? "id" : "Mode_ListShowId";
				if (!kddTempList.find(item => lastSelectedTemplate === item[field])) {
					template = firstValue;
					local.set(PRINT_SETTING_USER, {
						...printSetting,
						template: firstValue
					});
				}
			} else {
				template = firstValue;
				local.set(PRINT_SETTING_USER, {
					...printSetting,
					template: firstValue
				});
			}
			form.setFieldsValue({ template });
		}
	}, [kddTempList, isMergePrint, form, PRINT_SETTING_USER]);

	useEffect(() => {
		const cur = form.getFieldValue('sender');
		if (!cur) {
			const printSetting = local.get(PRINT_SETTING_USER) || {};
			const lastSelectedSender = printSetting.sender;
			const firstValue = senderSetting?.list?.[0]?.id;
			let sender: any;
			if (lastSelectedSender) {
				sender = lastSelectedSender;
			} else if (firstValue) {
				sender = firstValue;
			}
			form.setFieldsValue({ sender });

		}
	}, [senderSetting, form, PRINT_SETTING_USER]);

	/** ---------------------------------------------------------- */

	const scanTypeChange = (e) => {
		setScanType(e);
		saveSettingConfig({
			...settingConfig,
			scanType: e,
		});
	};

	const sellTypeChange = (e) => {
		setSellAttributeTypeList(e);
		TradeEditConfigForSubApi({
			id: settingConfig.id,
			sellAttributeTypeList: e.split(',')
		});
		saveSettingConfig({
			...settingConfig,
			sellAttributeTypeList: e.split(','),
		}, true);
	};

	const onTemplateChange = (value, info) => {
		form.setFieldsValue({ 'template': value });
		// 模板与发件人联动
		if (info.bindFjrId && info.bindFjrId != -1 && senderSetting?.list?.some(item => item.id == info.bindFjrId)) {
			form.setFieldsValue({ 'sender': info.bindFjrId });
		}
		// 把用户选择的模板保存到local中
		const printSetting = local.get(PRINT_SETTING_USER) || {};
		local.set(PRINT_SETTING_USER, {
			...printSetting,
			template: value
		});
	};

	const onSenderChange = (value) => {
		// 把用户选择的发件人保存到local中
		const printSetting = local.get(PRINT_SETTING_USER) || {};
		local.set(PRINT_SETTING_USER, {
			...printSetting,
			sender: value
		});
	};

	const onPrinterChange = (value) => {
		// 把用户选择的打印机保存到local中
		const printSetting = local.get(PRINT_SETTING_USER) || {};
		local.set(PRINT_SETTING_USER, {
			...printSetting,
			printer: value
		});
	};

	// 获取订单详情
	const getTradeDetail = async() => {
		let tradeList = [];
		await TradeTradeDetailGetApi({ tradeInfos }).then(res => {
			if (res?.data?.list?.length > 0) {
				tradeList = res?.data?.list;
			}
		}).catch(console.log);
		return tradeList;
	};
	/** ---------------------- 打印快递单相关逻辑 -------------------------- */
	// 组装查询历史单号的参数
	const getHistorySidsParams = (list) => {
		try {
			const templateInfo = getTempInfoByTempId(form.getFieldValue("template"));
			let params = {};
			const curTradeInfo = list[0];
			if (isMergePrint) {
				const curExpressType = PRINT_MAP[curTradeInfo.platform];
				const curTemplate = templateInfo?.userTemplateList?.find(template => template.expressType == curExpressType);
				params = {
					kdCode: curTemplate.exCode,
					kdType: curTemplate.expressType,
					exid: curTemplate.exId,
					userTemplateId: curTemplate.userTemplateId,
					trades: JSON.stringify([{
						tids: curTradeInfo.tids[0],
						togetherId: curTradeInfo.togetherId,
						sellerId: curTradeInfo.sellerId,
						platform: curTradeInfo.platform
					}])
				};
			} else {
				params = {
					kdCode: templateInfo?.ExCode,
					kdType: templateInfo?.KddType,
					exid: templateInfo?.Exid,
					userTemplateId: templateInfo?.Mode_ListShowId,
					trades: JSON.stringify([{
						tids: curTradeInfo.tids[0],
						togetherId: curTradeInfo.togetherId,
						sellerId: curTradeInfo.sellerId,
						platform: curTradeInfo.platform
					}])
				};
			}
			return params;
		} catch (e) {
			console.log(e);
		}
	};
	// 判断是否已经申请过单号
	const isAlreadyApplySid = async(list) => {
		let alreadyApplySids = [];
		const params = getHistorySidsParams(list);
		await getBatchSids(params).then(res => {
			if (res.data?.sids) {
				res.data.sids.forEach(item => {
					item.sids.forEach(sidItem => alreadyApplySids.push(sidItem.sid));
				});
			}
		}).catch(error => {
			customLogPost('isAlreadyApplySid: 判断是否已经申请过单号失败', { error });
		});
		return alreadyApplySids;

	};

	// 组装打印数据
	const fullPrintList = async(tradeDetailList, templateInfo) => {
		// const historySids = await isAlreadyApplySid(tradeDetailList);
		// theTradeDetail.isChecked = true; // 发货时需要
		let expressTemplate = {};
		if (isMergePrint) {
			expressTemplate = {
				exId: templateInfo.id,
				exCode: templateInfo.exCode,
				exName: templateInfo.groupName,
				kddType: templateInfo.expressType
			};
		} else {
			expressTemplate = {
				exId: templateInfo.Exid,
				exCode: templateInfo.ExCode,
				exName: templateInfo.ExcodeName,
				kddType: templateInfo.KddType
			};
		}
		const printContentSet = await tradeSetStore.getPrintContentSet();
		const keyWords = await tradeSetStore.getFilterWord();

		tradeDetailList.forEach(pack => {
			pack.isChecked = true; // 发货时需要
			pack.expressTemplate = expressTemplate; // 加上expressTemplate字段，因为发货的时候需要用到
			pack.trades.forEach(trade => {
				// trade.sids = alreadyApplySids;
				trade.orders.forEach(order => order.isChecked = selectedRowKeys.includes(getUniqueKeyByOrderInfo(order))); // 获取打印内容需要isChecked
			});
			const printContent = genePrintContent(pack, printContentSet, keyWords);
			pack.printContent = printContent;
		});
	};

	// 规则校验
	const checkPrintSettingRule = (tradeDetailList) => {
		const errorInfo = [
			{
				title: '订单已打印，请手动打印',
				rule: pack => pack.waybillPrintStatus != 'none',
				play: () => playAudioAsync(voice已打印, 500),
			},
			{
				title: '订单已发货，请手动打印',
				rule: pack => pack?.trades?.some(trade => trade?.orders?.some(order => [TradeStatus.等待买家确认收货, TradeStatus.卖家部分发货].includes(order.status) && !order.firstSend)),
				play: () => playAudioAsync(voice已发货, 500),
			},
			{
				title: '订单已退款，请手动打印',
				rule: pack => pack.hasRefund,
				play: () => playAudioAsync(voice已退款, 500),
			},
			{
				title: '订单缺货，请手动打印',
				rule: (pack) => {
					if (isShowZeroStockVersion || !settingConfig.autoPrintInterceptTypeList.includes(autoPrintInterceptTypeEnum.含缺货商品的不自动打印)) {
						return false;
					}
					return pack.noGoods;
				},
				play: () => playAudioAsync(voice有缺货, 500),
			},
			{
				title: '有留言备注，请手动打印',
				rule: (pack) => {
					if (settingConfig.autoPrintInterceptTypeList.includes(msgRemarksEnum.有备注)) {
						return pack.trades.some(trade => trade.sellerMemo);
					}
					if (settingConfig.autoPrintInterceptTypeList.includes(msgRemarksEnum.有留言)) {
						return pack.trades.some(trade => trade.buyerMessage);
					}
					return false;
				},
				play: (pack) => {
					if (settingConfig.autoPrintInterceptTypeList.includes(msgRemarksEnum.有备注) && pack.trades.some(trade => trade.sellerMemo)) {
						playAudioAsync(voice卖家备注, 500);
					}
					if (settingConfig.autoPrintInterceptTypeList.includes(msgRemarksEnum.有留言) && pack.trades.some(trade => trade.buyerMessage)) {
						playAudioAsync(voice买家留言, 500);
					}
				},
			},
			{
				title: '订单旗帜',
				rule: pack => {
					if (!settingConfig.autoPrintInterceptTypeList.includes(autoPrintInterceptTypeEnum.含以下旗帜不自动打印)) {
						return false;
					}
					return pack.trades.some(trade => settingConfig.sellerFlagList.includes(trade.sellerMemoFlag));
				},
				errorTextFn: pack => flagName[pack.trades?.[0]?.sellerMemoFlag] + '旗，请手动打印',
				play: (pack) => {
					playAudioAsync(flagAudio[pack.trades?.[0]?.sellerMemoFlag], 500);
				},
			},
			{
				title: 'BIC质检订单不允许操作',
				rule: pack => pack?.serviceTagList?.includes('bic_order'),
				play: () => textTransformAudioPlay('B I C 质检订单不允许操作'),
			},
			{
				title: '系统赠品异常，不允许操作',
				rule: pack => pack?.serviceTagList?.includes('sysGiftAbnormal'),
				play: () => textTransformAudioPlay('系统赠品异常'),
			}
		];
		let errArr = [];
		tradeDetailList.forEach(pack => {
			errorInfo.forEach(errorItem => {
				if (errorItem.rule(pack)) {
					errArr.push({
						text: errorItem.errorTextFn ? errorItem.errorTextFn(pack) : errorItem.title,
						play: () => errorItem?.play?.(pack)
					});
				}
			});
		});
		return errArr;
	};

	const scanFocus = () => {
		focusPostPrintLabelInput();
		autoFocus(true);
	};

	useEffect(() => {
		if (postPrintList?.length > 0 && scanedCount.total > 0 && scanedCount.current === scanedCount.total) {
			beforePrintExpress();
		}
	}, [scanedCount]);

	const beforePrintExpress = async() => {
		if (settingConfig.isAutoPrint) {
			// 校验规则
			const errList = checkPrintSettingRule(postPrintList);
			if (errList?.length) {
				let errItem = errList?.[0];
				setPostPrintRes({ showRes: true, errorText: errItem?.text });
				packWsHandlerSendFail(errItem?.text);
				errItem?.play();
				customLogPost(`打印快递单: 自动打印拦截`, { errList });
				return;
			}
			onPrintExpressBill(true);
			await sleep(500);
			textTransformAudioPlay("已备齐");
		} else {
			await sleep(500);
			textTransformAudioPlay("已备齐");
		}
	};

	const firstSendCheck = (tradeDetailList) => {
		let firstSendMode = 1; // 1:勾选都是未发货，2:勾选未发货与先发货，3:勾选都是先发货
		const firstSendArr = [];
		const notFirstSendArr = [];
		const ydNoArr = [];
		tradeDetailList.forEach(pack => {
			pack.trades.forEach(trade => {
				trade.orders.forEach(order => {
					if (order.isChecked) {
						if (order.firstSend) {
							firstSendArr.push(order);
						} else {
							notFirstSendArr.push(order);
						}
						if (order.ydNo && !ydNoArr.includes(order.ydNo)) {
							ydNoArr.push(order.ydNo);
						}
					}
				});
			});
		});
		if (firstSendArr?.length > 0 && notFirstSendArr?.length > 0) {
			firstSendMode = 2;
		} else if (firstSendArr?.length > 0 && notFirstSendArr?.length === 0) {
			firstSendMode = 3;
		}
		return {
			firstSendMode,
			firstSendArr,
			notFirstSendArr,
			ydNoArr
		};
	};

	const firstSendAllScanCheck = async(firstSendArr = [], tradeDetailList, firstSendkdd) => {
		const { orderIds } = firstSendkdd;
		const firstSendOrderArr = firstSendArr.map(item => `${item.tid}:${item.oid}`);
		console.log(firstSendOrderArr);
		if (firstSendArr?.length > 0) {
			let showTips = false;
			const orderIdArr = orderIds.split("|").map(item => item.split(":"));
			orderIdArr.forEach(item => {
				if (item[1]) {
					item[1].split(",").forEach(item1 => {
						if (!firstSendOrderArr.includes(`${item[0]}:${item1}`)) {
							showTips = true;
						}
					});
				} else {
					const tradeDetail = tradeDetailList[0]?.trades?.find(item1 => item1.tid === item[0]);
					tradeDetail?.orders?.forEach(item1 => {
						if (!firstSendOrderArr.includes(`${item1.tid}:${item1.oid}`)) {
							showTips = true;
						}
					});
				}
			});
			if (showTips) {
				return new Promise((resolve) => {
					Modal.confirm({
						title: "提示",
						content: "先发货订单有商品未备齐，是否继续打印",
						onOk() {
							customLogPost('打印快递单: 先发货订单有商品未备齐，强制打印', { tradeDetailList });
							resolve(true);
						}
					});
				});
			}
		}
	};

	const onPrintExpressBill = async(fromAutoPrint, isForcePrint = false, isClick = false) => {
		if (!fromAutoPrint) {
			sendPoint(Pointer.后置打印_打印快递单);
		}
		const isIpcModel = selectedShootingDevices?.length > 0; 
		const isVideoMonitor = videoMonitor && videoMonitorState;
		if (isClick) {
			clearVideoMonitor(isVideoMonitor, isIpcModel);
		}

		setPostPrintRes({ showRes: false });
		await preCheckPrintLimit();
		if (tradeInfos?.length < 1) {
			playAudioAsync(voice打印失败, 500);
			message.error("请先扫描");
			scanFocus();
			return;
		}
		if (selectedRowKeys?.length < 1) {
			message.error("未勾选商品");
			autoFocus(true);
			return;
		}
		setPrintLoading(true);
		try {
			const values = await form.validateFields(["printer", "template", "sender", "tagTempId", "tagPrinter"]);
			console.log('settingConfig', settingConfig);
			if (values.printer == values.tagPrinter && settingConfig.isPrintItemTag) {
				message.error("打印机和吊牌打印机不能相同");
				setPrintLoading(false);
				return;
			}
			// 获取订单详情
			const tradeDetailList = (fromAutoPrint && (["PT_TID", "BUYER_MESSAGE"].includes(settingConfig.scanType) || allGoodOrderList?.length === 1)) ? postPrintList : await getTradeDetail();
			if (!fromAutoPrint) {
				// 非自动打印校验
				let hasBic = false; // 有bic订单
				tradeDetailList?.forEach(pack => {
					if (pack?.serviceTagList?.includes('bic_order')) {
						hasBic = true;
					}
				});
				if (hasBic) {
					textTransformAudioPlay('B I C 质检订单不允许操作');
					setPostPrintRes({ showRes: true, errorText: 'BIC质检订单不允许操作' });
					packWsHandlerSendFail('BIC质检订单不允许操作');
					setPrintLoading(false);
					return;
				}
			}
			// 预占
			// 手动打印-前置校验
			if (!fromAutoPrint) {
				let hasSysGiftAbnormal = false; // 有bic订单
				tradeDetailList?.forEach(pack => {
					if (pack?.serviceTagList?.includes('sysGiftAbnormal')) {
						hasSysGiftAbnormal = true;
					}
				});
				if (hasSysGiftAbnormal) {
					setPostPrintRes({ showRes: true, errorText: '系统赠品异常，不允许操作' });
					textTransformAudioPlay('系统赠品异常');
					packWsHandlerSendFail('系统赠品异常，不允许操作');
					setPrintLoading(false);
					return;
				}
			}
			const trades = tradeDetailList?.flatMap(pack => pack.trades);
			console.log(trades);
			const checkRes = await TradePrintCheckApi({
				tidList: trades?.map(trade => trade.tid) || [],
				tradeCheckInfos: trades?.map(trade => ({
					sellerId: trade.sellerId,
					platform: trade.platform,
					source: trade.source,
					distributorUserId: trade.distributorUserId,
					tid: trade.tid,
					storageTime: trade.storageTime,
					ptTid: trade.ptTid,
					modified: trade.modified,
					scanUniqueCode,
					checkSource: 'SCAN_TRADE_PRINT',
					oidList: trade?.orders?.map(o => o.oid) || [], // 系统赠品判断
				})),
			});
			if (trades.some(trade => checkRes?.data?.scanAbnormalTradeInfoList?.includes(trade.tid))) {
				setPostPrintRes({ showRes: true, errorText: '当前订单已处理' });
				playAudioAsync(voice打印失败, 500);
				packWsHandlerSendFail('当前订单已处理');
				customLogPost('打印快递单: 当前订单已处理', { tradePrintCheck: checkRes });
				setPrintLoading(false);
				return;
			}

			// 接口异常拦截
			let hasAbnormal_14 = false;
			let hasAbnormal_15 = false;
			checkRes?.data?.abnormalTradeInfoList?.forEach((item: any) => {
				if (item.type == AbnormalType.订单正在执行自动策略) {
					hasAbnormal_14 = true;
				} else if (item.type == AbnormalType.列表加载商品数据与数据库商品数据不一致) {
					hasAbnormal_15 = true;
				}
			});
			if (hasAbnormal_14) {
				setPostPrintRes({ showRes: true, errorText: '订单正在执行自动策略' });
				playAudioAsync(voice打印失败, 500);
				packWsHandlerSendFail('订单正在执行自动策略');
				customLogPost('打印快递单: 订单正在执行自动策略', { tradePrintCheck: checkRes });
				setPrintLoading(false);
				return;
			}
			if (hasAbnormal_15) {
				setPostPrintRes({ showRes: true, errorText: '列表加载商品数据与数据库商品数据不一致' });
				playAudioAsync(voice打印失败, 500);
				packWsHandlerSendFail('列表加载商品数据与数据库商品数据不一致');
				customLogPost('打印快递单: 列表加载商品数据与数据库商品数据不一致', { tradePrintCheck: checkRes });
				setPrintLoading(false);
				return;
			}

			// 校验订单和平台是否匹配
			const templateInfo: { [k: string]: any } = getTempInfoByTempId(values.template);
			const name = isMergePrint ? templateInfo?.exCode : templateInfo.ExCode;

			// 得物品牌直发—普通履约、多仓发货标签的订单需匹配得物模版
			const notSupportedDeWu = tradeDetailList.filter(order => {
				return order.platform == PLAT_DW && (order?.serviceTagList?.includes('ordinaryOrder') || order?.serviceTagList?.includes('warehouseOrder'));
			});
			if (notSupportedDeWu?.length && !["DWTYMB"].includes(name)) {
				playAudioAsync(voice打印失败, 500);
				packWsHandlerSendFail('不支持得物品牌直发订单处理，请选择【得物通用模板】使用官方电子面单打印');
				message.error("不支持得物品牌直发订单处理，请选择【得物通用模板】使用官方电子面单打印");
				customLogPost('打印快递单: 不支持得物品牌直发订单处理，请选择【得物通用模板】使用官方电子面单打印', { notSupportedDeWu: notSupportedDeWu?.map(d => d.togetherId) });
				setPrintLoading(false);
				return;
			}

			//
			const notMatchingTemplateOrderList = isOrderMatchingTemplate(tradeDetailList, templateInfo);
			console.log('notMatchingTemplateOrderList', notMatchingTemplateOrderList);
			if (notMatchingTemplateOrderList?.length) {
				playAudioAsync(voice打印失败, 500);
				packWsHandlerSendFail('当前模板和所选订单所属平台不一致，请重新选择模板');
				message.error("当前模板和所选订单所属平台不一致，请重新选择模板");
				customLogPost('打印快递单: 当前模板和所选订单所属平台不一致，请重新选择模板', { notMatchingTemplateOrderList: notMatchingTemplateOrderList?.map(d => d.togetherId) });
				setPrintLoading(false);
				return;
			}
			console.log('tradeDetailList_templateInfo', tradeDetailList, templateInfo);
			if (tradeDetailList?.length) {
				const platform = tradeDetailList[0].platform;
				// 1. "其他" 平台使用抖音电子面单打印:增加拦截，提示与当前模版校验保持一致
				if ((platform == PLAT_OTHER || platform == PLAT_HAND) && templateInfo.platform == PLAT_FXG) {
					playAudioAsync(voice打印失败, 500);
					packWsHandlerSendFail('当前模板与勾选订单所属平台不一致，请重新选择模版。');
					message.error(`当前模板与勾选订单所属平台不一致，请重新选择模版。`);
					customLogPost(`打印快递单: 【${templateInfo.ExcodeName}】与以下订单所属平台不一致，请选择订单对应平台的模板打印。`, { templateInfo, tradeDetailList });
					setPrintLoading(false);
					return;
				}
			}

			if (tradeDetailList.length > 0) {
				// 在tradeDetailList中组装打印数据
				await fullPrintList(tradeDetailList, templateInfo);
				// if (isForcePrint) {
				// console.log(tradeDetailList);
				// }
				let checkResult: any = firstSendCheck(tradeDetailList);
				if (fromAutoPrint && checkResult.firstSendMode === 2) {
					setPostPrintRes({ showRes: true, errorText: '自动打印失败：勾选商品与订单先发货快递单号所含商品不一致，请手动点击打印' });
					packWsHandlerSendFail('自动打印失败：勾选商品与订单先发货快递单号所含商品不一致，请手动点击打印');
					playAudioAsync(voice打印失败, 500);
					customLogPost('自动打印失败：勾选商品与订单先发货快递单号所含商品不一致，请手动点击打印', checkResult.firstSendArr);
					setPrintLoading(false);
					return;
				}
				if (checkResult.ydNoArr?.length > 1) {
					setPostPrintRes({ showRes: true, errorText: `${fromAutoPrint ? "自动" : ""}打印失败：勾选商品存在多个发货快递单号，请前往订单打印处理` });
					packWsHandlerSendFail(`${fromAutoPrint ? "自动" : ""}打印失败：勾选商品存在多个发货快递单号，请前往订单打印处理`);
					playAudioAsync(voice打印失败, 500);
					customLogPost(`${fromAutoPrint ? "自动" : ""}打印失败：勾选商品存在多个发货快递单号，请前往订单打印处理`, checkResult.firstSendArr);
					setPrintLoading(false);
					return;
				}
				let firstSendkdd = {} as any;
				if (checkResult.firstSendArr?.length > 0) {
					const bgTradeInfo = await TradeQueryBgTradeForScanApi({
						queryType: 1,
						startTime: dayjs().subtract(3, "M").startOf('d').format("YYYY-MM-DD HH:mm:ss"),
						endTime: dayjs().endOf('d').format("YYYY-MM-DD HH:mm:ss"),
						pageNo: 1,
						pageSize: 10,
						ydNoList: checkResult.ydNoArr
					}).catch(e => console.log(e));
					setPrintLoading(false);
					firstSendkdd = bgTradeInfo?.data?.list?.[0] || {};
					await firstSendAllScanCheck(checkResult.firstSendArr, tradeDetailList, firstSendkdd);
					// 先发货订单判断打印模板和发货模板是否一致
					if (isMergePrint) {
						let hasTempType = templateInfo?.userTemplateList.find((_temp: any) => _temp.exId == firstSendkdd?.exId);
						if (!hasTempType) {
							setPostPrintRes({ showRes: true, errorText: `${fromAutoPrint ? "自动" : ""}打印失败：商品发货快递与打印快递模板不一致，请设置为相同模板后进行打印发货` });
							packWsHandlerSendFail(`${fromAutoPrint ? "自动" : ""}打印失败：商品发货快递与打印快递模板不一致，请设置为相同模板后进行打印发货`);
							playAudioAsync(voice打印失败);
							customLogPost('商品发货快递与打印快递模板不一致，请设置为相同模板后进行打印发货', {});
							return;
						}
					} else if (templateInfo.Exid != firstSendkdd?.exId) {
						setPostPrintRes({ showRes: true, errorText: `${fromAutoPrint ? "自动" : ""}打印失败：商品发货快递与打印快递模板不一致，请设置为相同模板后进行打印发货` });
						packWsHandlerSendFail(`${fromAutoPrint ? "自动" : ""}打印失败：商品发货快递与打印快递模板不一致，请设置为相同模板后进行打印发货`);
						playAudioAsync(voice打印失败);
						customLogPost('商品发货快递与打印快递模板不一致，请设置为相同模板后进行打印发货', {});
						return;
					}
					if (checkResult.firstSendMode === 3) {
						tradeDetailList[0].ydNo = firstSendkdd.ydNo || "";
						tradeDetailList[0].exId = firstSendkdd.exId || "";
						tradeDetailList[0].exName = firstSendkdd.exName || "";
						tradeDetailList[0].needPrintTid = firstSendkdd.orderIds ? [firstSendkdd.orderIds] : null;
					}
				} else {
					const bgTradeInfo = await TradeQueryBgTradeForScanApi({
						queryType: 1,
						startTime: dayjs().subtract(3, "M").startOf('d').format("YYYY-MM-DD HH:mm:ss"),
						endTime: dayjs().endOf('d').format("YYYY-MM-DD HH:mm:ss"),
						pageNo: 1,
						pageSize: 10,
						tidList: tradeDetailList[0].tids
					}).catch(e => console.log(e));
					setPrintLoading(false);
					const kddInfo = bgTradeInfo?.data?.list?.[0] || {};
					if (kddInfo.exNumber && checkResult.notFirstSendArr?.some(item => kddInfo.orderIds.includes(`${item.tid}:${item.oid}`))) {
						tradeDetailList[0].ydNo = kddInfo.exNumber || "";
						tradeDetailList[0].exId = kddInfo.exId || "";
						tradeDetailList[0].exName = kddInfo.exName || "";
					}
				}
				pageLoading.loading(true);
				PrintRefundLabelExpressBill({
					isMergePrint,
					packList: tradeDetailList,
					senderId: values.sender,
					printer: values.printer,
					tagPrinter: values.tagPrinter,
					templateInfo: getTempInfoByTempId(values.template),
					tagTempId: values.tagTempId,
					historySids: checkResult.ydNoArr.length > 0 ? checkResult.ydNoArr : null,
					logReprint: [2, 3].includes(checkResult.firstSendMode),
					firstSendMode: checkResult.firstSendMode,
					isForcePrint,
					isMustUseOldCode: false,
				});
			} else {
				customLogPost('打印快递单: 获取订单详情为空', { tradeDetailList });
				packWsHandlerSendFail('获取订单详情为空');
			}
		} catch (error) {
			setPrintLoading(false);
			customLogPost('onPrintExpressBill: error', { error });
		}
		setPrintLoading(false);
		scanFocus();
	};

	const onSubmit = debounce(async() => {
		console.log(postPrintList.length);
		if (!curOrderPrintSuccess && postPrintList?.length > 0 && !["PT_TID", "BUYER_MESSAGE"].includes(settingConfig.scanType)) {
			goodsUniqueCodeOnSubmit();
			return;
		}
		local.set(GLOBAL_CODE, scanUniqueCode, 30 * 60 * 1000);
		customLogPost('扫描', { scanUniqueCode });
		sendPoint(Pointer.后置打印_点击查询按钮);
		setPostPrintRes({ showRes: false });
		const values = await form.validateFields(["labelId", "shopMultiSelect"]);
		console.log(values);
		const { plats, plat_sellerIds } = values.shopMultiSelect || {
			plats: [],
			plat_sellerIds: []
		};
		const multiShopS = await getMultiShops({ plats, plat_sellerIds });
		const params = {
			scanContent: values.labelId,
			scanUniqueCode,
			multiShopS
		};
		disabledPostPrintLabelInput();
		const list = await getPostPrintTradeList(params);

		if (list?.length < 1) {
			form.resetFields(['labelId']);
			focusPostPrintLabelInput();
			autoFocus(true);
			return;
		}
		const trades = list.flatMap(pack => pack.trades);
		if (trades?.some(trade => trade.isSnapshotAbnormal)) {
			Modal.warning({
				title: '提示',
				centered: true,
				content: '订单信息与商品信息不一致，请核对后打印'
			});
			packWsHandlerSendFail('订单信息与商品信息不一致，请核对后打印');
			form.resetFields(['labelId']);
			scanFocus();
			return;
		}

		// 在这里添加监控开启逻辑
		if (list?.length > 0) {
			// 判断是否需要发送视频监控消息
			const isIpcModel = selectedShootingDevices?.length > 0; 
			const isVideoMonitor = videoMonitor && videoMonitorState;
			clearVideoMonitor(isVideoMonitor, isIpcModel);
			const outSid = values?.labelId?.trim();
			const texts = [`${scanTypeOptionsMemo.find(v => v.value === scanType).label}：${outSid}`];
			const isKd = scanTypeOptionsMemo.find(v => v.value === scanType)?.label == '快递单号';
			const isDd = scanTypeOptionsMemo.find(v => v.value === scanType)?.label == '订单编号';
			const data = {
				outSid,
				orderList: list,
				isIpcModel,
				isVideoMonitor,
				nvrDevices,
				selectedShootingDevices,
				texts,
				isKd,
				isDd
			};
			console.log(data, scanTypeOptionsMemo, scanType, '0000000000000');
			// 开启监控
			startVideoMonitor(data, 2);
		}

		tradeInfos = list?.map(pack => ({
			tids: pack.isMerge && pack.tids[0] ? pack.tids[0].split('|') : pack.tids,
			sellerId: pack.sellerId,
			platform: pack.platform,
		}));
		let orderList = [];
		list.forEach(pack => {
			pack.trades.forEach(trade => {
				orderList = orderList.concat(trade.orders);
			});
		});
		setAllGoodOrderList(orderList);
		console.log(allGoodOrderProgress);
		console.log("list111", list);

		// if (tradeInfos.length && settingConfig.isAutoPrint) {
		// 	await onPrintExpressBill(true);
		// }
	}, 500, { leading: true, trailing: false });

	const setTemplateSenderPrinter = (template: any, templateInfo: any, type: string) => {
		const printSetting = local.get(PRINT_SETTING_USER) || {};
		const lastSelectedPrinter = printSetting.printer;
		const lastSelectedSender = printSetting.sender;
		const printInfo: any = {
			template
		};
		printInfo.sender = templateInfo.bindFjrId && senderSetting?.list?.some(item => item.id == templateInfo.bindFjrId) ? templateInfo.bindFjrId : (lastSelectedSender || senderSetting?.list?.[0]?.id);
		printInfo.printer = templateInfo.defaultPrinter && printersList.includes(templateInfo.defaultPrinter) ? templateInfo.defaultPrinter : (lastSelectedPrinter || printersList?.[0]);
		customLogPost('切换模板、发件人、打印机', { printInfo, templateInfo, printSetting, type });
		form.setFieldsValue(printInfo);
	};

	const resetTemplate = () => {
		const printSetting = local.get(PRINT_SETTING_USER) || {};
		const lastSelectedTemplate = printSetting.template;
		let templateInfo: any = {};
		if (isMergePrint) {
			templateInfo = kddTempList.find(item => item?.id === lastSelectedTemplate);
		} else {
			templateInfo = kddTempList.find(item => item?.Mode_ListShowId === +lastSelectedTemplate);
		}
		if (templateInfo) {
			setTemplateSenderPrinter(lastSelectedTemplate, templateInfo, "自动切换默认模板");
		} else {
			customLogPost('未命中--自动切换默认模板', {
				lastSelectedTemplate,
				templateInfo,
			});
		}
	};

	const setSmartExpressTemplate = (pack) => {
		let templateInfo: any = {};
		let template = "";
		const { smartExpressTemplate, packSmartExpressTemplate, isMerge } = pack;
		if (smartExpressTemplate || packSmartExpressTemplate) {
			const smartInfo = JSON.parse(isMerge ? packSmartExpressTemplate : smartExpressTemplate);
			const { printTemplateId, isManual, printTemplateName, hasCleared } = smartInfo;
			console.log('%c [ 智选快递 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', smartInfo, printTemplateId);

			// 如果智选快递模板未删除
			if (hasCleared !== 'true') {
				if (isMergePrint) {
					templateInfo = kddTempList.find(item => item?.userTemplateList?.some(temp => temp.userTemplateId === printTemplateId));
					template = templateInfo?.id;
				} else {
					templateInfo = kddTempList.find(item => item?.Mode_ListShowId === +printTemplateId);
					template = templateInfo?.Mode_ListShowId;
				}
				if (template) {
					setTemplateSenderPrinter(template, templateInfo, "智选快递快递自动切换模板");
					console.log('%c [ 命中--智选快递 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', '命中--');
					customLogPost('模板命中-智选快递', {
						templateInfo,
						template,
						smartInfo,
						printTemplateId
					});
				}
			} else {
				resetTemplate();
			}
		} else {
			resetTemplate();
		}
	};

	const switchTemplate = async(labelId, list, orderList) => {
		if (settingConfig.expressMatchTypeList.includes("FIRST_SEND_AUTO_TEMPLATE") && list[0].firstSend) {
			const bgTradeInfo = await TradeQueryBgTradeForScanApi({
				queryType: 1,
				startTime: dayjs().subtract(3, "M").startOf('d').format("YYYY-MM-DD HH:mm:ss"),
				endTime: dayjs().endOf('d').format("YYYY-MM-DD HH:mm:ss"),
				pageNo: 1,
				pageSize: 10,
				ydNoList: list[0].waybillNoSet
			}).catch(e => console.log(e));
			const firstSendkdd = bgTradeInfo?.data?.list?.[0] || {};
			console.log(firstSendkdd);
			setFirstSendKddLog(firstSendkdd);
			let template = '';
			let templateInfo: any = {};
			if (isMergePrint) {
				templateInfo = kddTempList.find(item => item.userTemplateList?.some(temp => temp.exId === firstSendkdd.exId));
				template = templateInfo?.id;
			} else {
				templateInfo = kddTempList.find(item => item.Exid === +firstSendkdd.exId && item.ExcodeName === firstSendkdd.exName);
				template = templateInfo?.Mode_ListShowId;
			}
			if (template) {
				setTemplateSenderPrinter(template, templateInfo, "先发货自动切换模板");
			}
		} else if (settingConfig.expressMatchTypeList.includes("NO_SEND_HAS_SIDS_TEMPLATE")) {
			let tidList = [];
			if (settingConfig.scanType === "BUYER_MESSAGE" || settingConfig.scanType === "PT_TID") {
				tidList = list?.[0]?.tids;
			} else {
				const curScanBarCode = KM[settingConfig.scanType];
				tidList = [orderList.find(order => order[curScanBarCode] == labelId?.trim())?.tid];
			}
			const bgTradeInfo = await TradeQueryBgTradeForScanApi({
				queryType: 1,
				startTime: dayjs().subtract(3, "M").startOf('d').format("YYYY-MM-DD HH:mm:ss"),
				endTime: dayjs().endOf('d').format("YYYY-MM-DD HH:mm:ss"),
				pageNo: 1,
				pageSize: 10,
				tidList,
			}).catch(e => console.log(e));
			const hasKddInfo = bgTradeInfo?.data?.list?.[0] || null;
			if (hasKddInfo) {
				let templateInfo: any = {};
				let template = '';
				if (isMergePrint) {
					templateInfo = kddTempList.find(item => item.userTemplateList?.some(temp => temp.exId === hasKddInfo.exId));
					template = templateInfo?.id;
				} else {
					templateInfo = kddTempList.find(item => item.Exid === +hasKddInfo.exId && item.ExcodeName === hasKddInfo.exName);
					template = templateInfo?.Mode_ListShowId;
				}
				if (template) {
					setTemplateSenderPrinter(template, templateInfo, "已申请单号未发货自动切换模板");
				} else {
					customLogPost('未命中--已申请单号未发货自动切换模板', {
						hasKddInfo,
						templateInfo,
						template,
					});
				}
			} else if (settingConfig.expressMatchTypeList.includes("NO_SIDS_SMART_EXPRESS_TEMPLATE") && smartExpressSwitch) {
				setSmartExpressTemplate(list?.[0]);
			} else {
				resetTemplate();
			}
		} else if (settingConfig.expressMatchTypeList.includes("NO_SIDS_SMART_EXPRESS_TEMPLATE") && smartExpressSwitch) {
			setSmartExpressTemplate(list?.[0]);
		} else {
			resetTemplate();
		}
	};

	const autoCheckAll = async() => {
		await switchTemplate(form.getFieldValue('labelId'), postPrintList, allGoodOrderList);
		allGoodOrderList.forEach(order => {
			updateAllGoodOrderProgress(order, order.num);
		});
		form.resetFields(['labelId']);
		focusPostPrintLabelInput();
		autoFocus(true);
	};

	useEffect(() => {
		if (postPrintList?.length > 0 && allGoodOrderList?.length > 0) {
			if (!["PT_TID", "BUYER_MESSAGE"].includes(settingConfig.scanType)) {
				goodsUniqueCodeOnSubmit();
			} else {
				autoCheckAll();
			}
		}
	}, [postPrintList, allGoodOrderList]);

	const goodsUniqueCodeOnSubmit = async() => {
		const values = await form.validateFields(["labelId"]);
		const curScanBarCode = KM[settingConfig.scanType];
		let errorMsg = "";
		let curScanGoodOrders = [];
		let isMatchFlag = false;
		console.log(allGoodOrderList);
		allGoodOrderList.forEach(order => {
			const isMatch = order[curScanBarCode] == values.labelId?.trim();
			if (isMatch) {
				isMatchFlag = true;
				if (allGoodOrderProgress[order.oid].current < allGoodOrderProgress[order.oid].total) {
					curScanGoodOrders.push(order);
				}
			}
		});
		form.resetFields(['labelId']);
		focusPostPrintLabelInput();
		autoFocus(true);
		if (!curScanGoodOrders.length) {
			// message.error("未匹配到货品");
			// setErrorMsg('未匹配到货品');
			if (isMatchFlag) {
				textTransformAudioPlay('重复');
			} else {
				textTransformAudioPlay('商品错误');
			}
			return;
		} else {
			await switchTemplate(values.labelId, postPrintList, allGoodOrderList);
			setCurScanGoodInfo(curScanGoodOrders[0]);
			updateAllGoodOrderProgress(curScanGoodOrders[0]);
		}
	};

	const scanSetting = (e) => {
		e.stopPropagation();
		sendPoint(Pointer.后置打印_扫描设置);
		setSettingModalVisible(true);
	};

	const validatorTemplate = (_, value) => {
		if (!value) {
			return Promise.reject(new Error("请选择快递模板"));
		} else if (isMergePrint) {
			const templateInfo = kddTempList.find((i) => i.id == value);
			if (templateInfo?.userTemplateList.length < 1) {
				return Promise.reject(new Error("当前模板组暂无模板，请添加模板或更换模板组"));
			}
		}
		return Promise.resolve();
	};

	const onChangeAutoSend = (e) => {
		const isChecked = e.target.checked;
		saveSettingConfig({
			...settingConfig,
			isAutoSend: isChecked
		});
	};

	const clearPageData = () => {
		form.setFieldsValue({
			labelId: undefined
		});
	};

	const settingModalOnCancel = () => {
		setSettingModalVisible(false);
	};

	const settingModalOnOk = () => {
		setSettingModalVisible(false);
	};

	useEffect(() => {
		event.on(CLEAR_PAGE_DATA, clearPageData);
		const socketHandler: any = {};
		createSocketHander(socketHandler, {
			// url: "ws://localhost:8080",
			url: "ws://127.0.0.1:8010/SmartPack/Examine",
			onOpen: () => {
				notification.open({
					message: "打包机连接成功",
					description: "打包机连接成功",
					icon: <CheckCircleOutlined />,
					style: {
						color: "#f00"
					}
				});
				customLogPost('打包机连接成功');
				setPackWsHandler(socketHandler);
				console.log(event, 'onOpen');
			},
			onMessage: (event) => {
				customLogPost('打包机消息', event);
				console.log(event, 'event');
			},
			onClose: () => {
				notification.open({
					message: "打包机连接关闭",
					description: "打包机连接关闭",
					icon: <CloseCircleOutlined />,
					style: {
						color: "#f00"
					}
				});
				customLogPost('打包机连接关闭');
				console.log('onClose');
			},
			onError: (event) => {
				notification.open({
					message: "打包机连接失败",
					description: "打包机连接失败",
					icon: <CloseCircleOutlined />,
					style: {
						color: "#f00"
					}
				});
				customLogPost('打包机连接失败');
				console.log(event, 'onError');
			}
		});
		return () => {
			event.off(CLEAR_PAGE_DATA, clearPageData);
			socketHandler?.close();
			setPackWsHandler(null);
		};
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, []);

	const handleKeyPress = async(event) => {
		console.log(event, 'event', event.key === 'Enter');
		if (event.key === 'Enter') {
			if (GLOBAL_DISABLED) return;
			GLOBAL_DISABLED = true;
			event?.preventDefault?.();
			try {
				await onSubmit();
			} catch (error) {
				console.log(error);
				autoFocus(false);
			}
			GLOBAL_DISABLED = false;
		}
	};

	const autoFocus = (clear) => {
		if (clear) {
			form.resetFields(['labelId']);
		}
		let timer = null;
		timer = setTimeout(() => {
			searchInput?.current?.focus();
			searchInput?.current?.select();
			clearTimeout(timer);
			timer = null;
		}, 100);
	};

	const handlePass = (flag = false) => {
		if (postPrintList?.length < 1) {
			message.error("请先匹配订单");
			return;
		}
		const isIpcModel = selectedShootingDevices?.length > 0; 
		const isVideoMonitor = videoMonitor && videoMonitorState;
		clearVideoMonitor(isVideoMonitor, isIpcModel);
		if (flag) {
			const { markScanPrintSet: { skipAndMarkSet = {} } } = settingConfig;
			if (skipAndMarkSet.autoPending) {
				const tids = [];
				postPrintList?.forEach(pack => {
					tids.push(...pack.togetherId.split('|'));
				});
				TradeBatchUpdatePendingStatusApi({
					isPending: 1,
					tids,
				}).then(res => {
					console.log(res);
					message.success("跳过并标记成功");
					resetStoreNew();
				});
			} else if (skipAndMarkSet.autoSellerFlag) {
				const params = [];
				postPrintList?.forEach(pack => {
					pack?.trades.forEach(trade => {
						params.push({
							tid: trade.tid,
							memo: !trade.sellerMemo && [PLAT_ALI, PLAT_JD].includes(pack.platform) ? `【${userInfo?.userName}】【${dayjs().format('MM-DD HH:mm:ss')}】` : trade.sellerMemo,
							ptTid: trade.ptTid,
							sellerFlag: Number(skipAndMarkSet.sellerFlagList?.[0]),
							platform: pack.platform,
							sellerId: pack.sellerId,
							saleUserId: pack.distributorUserId,
							source: pack.source || "",
							afterSalesFlag: pack.afterSalesFlag || false
						});
					});
				});
				TradeBatchUpdateMemoApi(params).then(res => {
					console.log(res);
					message.success("跳过并标记成功");
					resetStoreNew();
				});
			}
		} else {
			resetStoreNew();
		}
	};

	const handleSetBtnsDefault = (fieldName, value) => {
		setOperateBtnsDefault(prev => {
			return {
				...prev,
				[fieldName]: value
			};
		});
		saveSettingConfig({
			...settingConfig,
			markScanPrintSet: {
				...settingConfig.markScanPrintSet,
				onlySkip: value === "onlySkip",
				skipAndMark: value === "skipAndMark"
			}
		});
	};

	const OrderPassBtns = useCallback(() => {
		return (
			<Dropdown.Button
				size="large"
				onClick={ () => {
					if (operateBtnsDefault?.skipType === "skipAndMark") {
						handlePass(true);
					} else {
						handlePass(false);
					}
				} }
				overlay={ (
					<Menu>
						<Menu.Item onClick={ () => setMarkSettingVisible(true) }>
							<SettingOutlined className="r-mr-8 r-c-666" /><span className="r-c-primary">标记设置</span>
						</Menu.Item>
						<Menu.Item key="cancelClose" onClick={ () => handlePass(true) }>
							<div className="r-flex r-jc-sb r-ai-c">
								<span className="r-mr-8">跳过并标记</span>
								{
									operateBtnsDefault?.skipType === "skipAndMark"
										? (
											<Icon
												className="r-c-warning"
												type="guding"
												onClick={ (e) => {
													e.stopPropagation();
												} }
											/>
										)
										: (
											<Icon
												className="r-c-999"
												type="weiguding"
												onClick={ (e) => {
													e.stopPropagation();
													handleSetBtnsDefault("skipType", "skipAndMark");
												} }
											/>
										)
								}
							</div>
						</Menu.Item>
						<Menu.Item
							key="cancelClose"
							onClick={ () => handlePass(false) }
						>
							<div className="r-flex r-jc-sb r-ai-c">
								<span className="r-mr-8">仅跳过</span>
								{
									operateBtnsDefault?.skipType === "onlySkip"
										? (
											<Icon
												className="r-c-warning"
												type="guding"
												onClick={ (e) => {
													e.stopPropagation();
												} }
											/>
										)
										: (
											<Icon
												className="r-c-999"
												type="weiguding"
												onClick={ (e) => {
													e.stopPropagation();
													handleSetBtnsDefault("skipType", "onlySkip");
												} }
											/>
										)
								}
							</div>
						</Menu.Item>
					</Menu>
				) }
			>
				<div className="r-fs-14" style={ { width: "5em" } }>{operateBtnsDefault?.skipType === "skipAndMark" ? "跳过并标记" : "仅跳过"}</div>
			</Dropdown.Button>
		);

	}, [operateBtnsDefault, postPrintList, settingConfig]);

	return (
		<div>
			<Form
				form={ form }
				className={ styles.form }
			>
				<div>
					<div className="r-flex">
						<div>
							<Select value={ scanType } options={ scanTypeOptionsMemo } onChange={ (e) => { scanTypeChange(e); } } style={ { marginRight: "-1px" } } />
						</div>
						<Form.Item
							name="labelId"
							className="unique-code"
							style={ { width: "100%" } }
							rules={ [{
								required: true,
								message: "请输入"
							}] }
						>
							<Input
								placeholder={ onFocus ? `` : "英文输入法下扫描" }
								// onPressEnter={ onSubmit }
								ref={ searchInput }
								id="labelIdPostPrintInput"
								onKeyPress={ handleKeyPress }
								onFocus={ (e) => { setOnFocus(true); } }
								onBlur={ (e) => { setTimeout(() => setOnFocus(false), 100); } }
								suffix={ onFocus ? <div className="r-pointer r-bold" onClick={ onSubmit }><EnterOutlined style={ { color: '#ff5900' } } /></div> : null }
							/>
						</Form.Item>
					</div>
					<div className="r-ta-r">
						<Button onClick={ () => { form.resetFields(['labelId']); } }>重置</Button>
						<Button type="primary" className="r-ml-10" onClick={ () => { handleKeyPress({ key: "Enter" }); } }>查询</Button>
					</div>
				</div>

				{/* 设置 */}
				<div className="r-mt-10" style={ { background: "#f5f5f5" } }>
					<Collapse className={ styles["setting-collapse"] } defaultActiveKey={ ['1', '2', '3', '4'] } ghost expandIconPosition="right">
						<Collapse.Panel header={ <div className={ styles["header-border"] }>设置匹配情况</div> } key="1">
							<Form.Item
								name="shopMultiSelect"
							>
								<ShopMultiSelect isHasHandPlat />
							</Form.Item>
							<div style={ { marginBottom: 5 } }>
								匹配订单商品数：
							</div>
							<Form.Item
								style={ { marginBottom: 0 } }
								className="r-flex r-ai-c"
							>
								<Radio.Group value={ sellAttributeTypeList } onChange={ e => sellTypeChange(e.target.value) }>
									<Radio value={ sellAttributeTypeEnum.单件 }>单件</Radio>
									<Radio value={ sellAttributeTypeEnum.多件 }>多件</Radio>
									<Radio value={ sellAttributeTypeEnum.全部 }>全部</Radio>
								</Radio.Group>
							</Form.Item>
						</Collapse.Panel>
						<Collapse.Panel header={ <div className={ styles["header-border"] }>设置快递单模板</div> } key="2">
							<Form.Item
								required
								name="template"
								// validateTrigger={ validateTrigger }
								rules={ [{
									validator: validatorTemplate
								}] }
							>
								<SelectTemplate onChange={ onTemplateChange } />

							</Form.Item>
							<Form.Item
								name="printer"
								// rules={ [{
								// 	required: true,
								// 	message: "请选择打印机"
								// }] }
							>
								<SelectPrinter onChange={ onPrinterChange } />

							</Form.Item>

							<Form.Item
								name="sender"
								rules={ [{
									required: true,
									message: "请选择发件人"
								}] }
								style={ { marginBottom: 0 } }
							>
								<SelectSender onChange={ onSenderChange } />

							</Form.Item>
						</Collapse.Panel>
						{
							settingConfig.isPrintItemTag && (
								<Collapse.Panel header={ <div className={ styles["header-border"] }>设置吊牌模板</div> } key="3">
									<Form.Item
										name="tagPrinter"
										rules={ [{
											required: true,
											message: "请选择吊牌打印机"
										}] }
									>
										<SelectPrinter />
									</Form.Item>
									<div className="r-flex r-ai-c">
										<Form.Item
											name="tagTempId"
											label="吊牌模板"
											required={ false }
											// validateTrigger={ validateTrigger }
											rules={ [{
												required: true,
												message: "请选择吊牌模板"
											}] }
											style={ { marginBottom: 0, flex: 1, marginRight: 8 } }
										>
											<SelectTagTemplate
												disabled={ printTagSet?.tagTemplateSwitch == "1" }
												onChange={ (v, info) => {
													if (printersList.find(printer => printer == info?.defaultPrinter)) {
														form.setFieldsValue({ tagPrinter: info?.defaultPrinter });
													}
											 } }
											/>
										</Form.Item>
										<SettingOutlined onClick={ () => { history.push('/settings/system?introName=printTagSet'); } } />
									</div>
									<Form.Item
										style={ { marginBottom: 0 } }
										label="打印份数"
									>
										<div style={ { height: 32, lineHeight: '32px' } }>
											{
												printTagSet?.printTagNumSwitch == "1" ? (
													<div>每个商品打印 <span style={ { color: "#FD8204" } }>{printTagSet?.printTagNum}</span> 份</div>
												) : '与商品数量一致'
											}
										</div>
									</Form.Item>
								</Collapse.Panel>
							)
						}
						<Collapse.Panel header={ <div className={ styles["header-border"] }>高级设置</div> } collapsible="disabled" showArrow={ false } extra={ <div className="ant-btn-link r-pointer" style={ { wordBreak: 'keep-all' } } onClick={ scanSetting } >扫描设置</div> } key="4">
							<div className="r-flex r-jc-sb">
								<Checkbox checked={ settingConfig?.isAutoSend } onChange={ onChangeAutoSend }>打印后自动发货</Checkbox>
							</div>
						</Collapse.Panel>
					</Collapse>
				</div>
				<div className={ styles.mainBtn }>
					<Button className="r-mr-8" type="primary" loading={ printLoading } onClick={ () => onPrintExpressBill(false, true, true) } style={ { width: '100%', height: '40px' } }>打印快递单</Button>
					{/* <Button className="r-ml-10" style={ { height: '40px' } } onClick={ handlePass }>跳过</Button> */}
					{
						OrderPassBtns()
					}
				</div>
			</Form>
			<div className="r-mt-20">
				<PrintResult />
			</div>
			{
				settingModalVisible && <SettingModal onCancel={ settingModalOnCancel } onOk={ settingModalOnOk } customLogPost={ customLogPost } />
			}
			<MarkSettingModal
				visible={ markSettingVisible }
				onOk={ val => { saveSettingConfig(val); setMarkSettingVisible(false); } }
				onCancel={ () => setMarkSettingVisible(false) }
				settingConfig={ settingConfig }
			/>
		</div>
	);
};
export default observer(Search);
