import { cloneDeep } from 'lodash';
import { IPackage } from '../../interface';
import { getOrderAllotStock, getOrderNotAllotStock } from '../BottomCom/utils';
import { tradeStore } from "@/stores";
import userStore from '@/stores/user';
import { getWarehouseVersion } from '@/pages/AfterSale/TradeList/utils';
import { IKddTemp, IKddTempGroup } from '@/types/schemas/report';
import { PLAT_FXG, PLAT_SPH } from '@/constants';
import { AbnormalFromType } from "@/pages/Trade/components/BottomCom/checkAbnormalUtil";
import { dealPlatAsHandPlat, isSourceScm, transferOtherToHand } from "@/components-biz/ShopListSelect/shopListUtils";

export interface AbnormalItemProps {
	sysSkuIds?: any[],
	num?:number,
	togetherIds?: any[],
	info?:any[],
	sids?: any[]
}

// 打印发货异常项配置
const abnormalManage = {
	PRINT: {
		// 直接中断的情况
		breakOrder: ['mismatchingBreak', 'modifiedOnline', 'unSelect', 'isPending', 'scmOrderNotExist', 'scmRelationAbnormal', 'appointmentArrival', 'needSerialNumber', 'refundBreak', 'unStockBreak', 'lowProfitBreak',
			'unSync', 'unFxgPromiseLogistics', 'SCM_RELATION_UNBIND', 'SCM_DISTRIBUTOR_STORE_UNBIND', 'SCM_TRADE_SHIP', 'SCM_TRADE_REFUND', 'orderErrorStatus', 'bic_order', 'abnormalSysGiftBreak',
			'abnormalSysGiftCheck_14', 'abnormalSysGiftCheck_15', 'unAcceptOrder', 'exchangeRefundAbnormal', 'abnormalXsdPtpsBreak', 'gfcf', 'bizWaveAbnormal'],
		// 可以操作忽略的情况
		ignoreOrder: ['mismatching', 'addGoodsNoSend', 'refund', 'onlineShip', 'abnormalOnlineRefunded', 'abnormalAddress', 'unArrive', 'unStock', 'lowProfit', 'unBindGoods', 'unPromiseLogistics', 'hasShipped', 'hasPrinted', 'changeAddress', 'abnormalSysGift', 'exchangeRefund', 'abnormalXsdPtps'],
	},
	SEND: {
		// 直接中断的情况
		breakOrder: ['mismatchingBreak', 'modifiedOnline', 'unSid', 'unSelect', 'isPending', 'shipStatus', 'splitTrade', 'scmOrderNotExist', 'scmRelationAbnormal', 'appointmentArrival', 'needSerialNumber', 'refundBreak', 'unStockBreak', 'lowProfitBreak', 'unSync', 'alreadyRefundBreak',
			'unFxgPromiseLogistics', 'SCM_RELATION_UNBIND', 'SCM_DISTRIBUTOR_STORE_UNBIND', 'SCM_TRADE_SHIP', 'SCM_TRADE_REFUND', 'orderErrorStatus', 'bic_order', 'abnormalSysGiftBreak', 'abnormalSysGiftCheck_14', 'abnormalSysGiftCheck_15', 'unAcceptOrder', 'exchangeRefundAbnormal', 'abnormalXsdPtpsBreak', 'gfcf', 'aezy', 'bizWaveAbnormal'],
		// 可以操作忽略的情况
		ignoreOrder: ['mismatching', 'addGoodsNoSend', 'onlineShip', 'abnormalOnlineRefunded', 'refund', 'abnormalAddress', 'unStock', 'lowProfit', 'unBindGoods', 'unArrive', 'unPromiseLogistics', 'hasShipped', 'changeAddress', 'abnormalSysGift', 'exchangeRefund', 'abnormalXsdPtps'],
	},
	PRESEND: {
		// 直接中断的情况
		breakOrder: ['mismatchingBreak', 'modifiedOnline', 'unSelect', 'isPending', 'appointmentArrival', 'needSerialNumber', 'refundBreak', 'unStockBreak', 'lowProfitBreak', 'unSync', 'alreadyRefundBreak', 'unFxgPromiseLogistics', 'SCM_RELATION_UNBIND', 'SCM_DISTRIBUTOR_STORE_UNBIND', 'SCM_TRADE_SHIP', 'SCM_TRADE_REFUND', 'orderErrorStatus', 'bic_order', 'abnormalSysGiftBreak',
			'abnormalSysGiftCheck_14', 'abnormalSysGiftCheck_15', 'unAcceptOrder', 'exchangeRefundAbnormal', 'abnormalXsdPtpsBreak', 'gfcf', 'wave_no' 
		],
		// 可以操作忽略的情况
		ignoreOrder: ['mismatching', 'addGoodsNoSend', 'refund', 'abnormalAddress', 'unArrive', 'unStock', 'lowProfit', 'unBindGoods', 'unPromiseLogistics', 'changeAddress', 'abnormalSysGift', 'exchangeRefund', 'abnormalXsdPtps'],
	},
	[AbnormalFromType.波次生成]: {
		// 直接中断的情况
		breakOrder: ['mismatchingBreak', 'unSid', 'unSelect', 'isPending', 'splitTrade', 'scmOrderNotExist', 'scmRelationAbnormal', 'appointmentArrival', 'needSerialNumber', 'refundBreak', 'unStockBreak', 'lowProfitBreak', 'unSync', 'alreadyRefundBreak',
			'unFxgPromiseLogistics', 'SCM_RELATION_UNBIND', 'SCM_DISTRIBUTOR_STORE_UNBIND', 'SCM_TRADE_SHIP', 'SCM_TRADE_REFUND', 'orderErrorStatus', 'bic_order', 'abnormalSysGiftBreak', 'abnormalSysGiftCheck_14', 'abnormalSysGiftCheck_15', 'unAcceptOrder', 'exchangeRefundAbnormal', 'abnormalXsdPtpsBreak', 'gfcf', 'aezy',
			'unBindGoodsBreak', 'wave_no', 'unWarehouseSlotNameBreak', 'scmOrderBreak' 
		],
		// 可以操作忽略的情况
		ignoreOrder: ['mismatching', 'onlineShip', 'refund', 'abnormalAddress', 'unArrive', 'unPromiseLogistics', 'hasShipped', 'abnormalSysGift', 'exchangeRefund', 'abnormalXsdPtps', 'lowProfit'],
	},
	total: 0,
};

export const SID_STATUS = ['打印后生成', '无需运单号', '获取失败', ''];

const getHasAlreadyRefundGoods = (trade) => {
	try {
		trade?.forEach((item) => {
			item?.orders?.forEach((order) => {
				if (order?.isChecked && ['TRADE_CLOSED', 'TRADE_CLOSED_BY_TAOBAO', 'REFUND_SUCCESSED'].includes(order?.refundStatus)) {
					throw new Error("true");
				}
			});
		});
	} catch (error) {
		if (error.message === "true") {
			return true;
		}
	}
	return false;
};

// 获取异常信息
const getAbnormalInfo = async(params: {orderList: IPackage[], type: string, isNoLogistics: boolean, isPreShip: boolean, afterPrint: boolean, templateInfo: IKddTemp | IKddTempGroup;}) => {
	const { orderList, type = 'PRINT', isNoLogistics = false, isPreShip = false, afterPrint = false, templateInfo } = params;
	let { selectedTemp, selectedTempGroup, isMergePrint } = tradeStore;
	let template = templateInfo || (isMergePrint ? selectedTempGroup : selectedTemp);
	const { userSetting } = userStore;
	const { extraSetting = {} } = userSetting;
	const {
		abnormalItemUnbindCheckNew, // 商品未绑定本地货品
		abnormalRefundCheckNew, // 含退款商品的订单
		abnormalAddressCheckNew, // 异常地址
		abnormalExpressArriveCheckNew, // 快递不可达订单检测
		abnormalStockWarnCheckNew, // 库存不足订单
		abnormalSysGiftCheckNew, // 系统赠品异常
		abnormalTradeTemplateCheckNew, // 订单业务类型与模板不匹配
		abnormalXsdPtpsCheck, // 小时达平台配送订单
	} = extraSetting;

	console.log('%c [ 异常提醒配置 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', extraSetting);

	let canReachList = [0, 2, 3, 5];
	let hasAbnormal = false;
	// 订单所属店铺长期未同步订单
	let unSync:AbnormalItemProps = { num: 0, togetherIds: [], sids: [] };
	// 未选择宝贝订单
	let unSelect:AbnormalItemProps = { num: 0, togetherIds: [], sids: [] };
	// BIC质检订单
	let bic_order: AbnormalItemProps = { num: 0, togetherIds: [] };
	// 得物商家未接单订单
	let unAcceptOrder:AbnormalItemProps = { num: 0, togetherIds: [] };
	// 挂起订单
	let isPending :AbnormalItemProps = { num: 0, togetherIds: [], sids: [] };
	// 退款订单
	let refund:AbnormalItemProps = { num: 0, togetherIds: [], sids: [] };
	// 退款且不能强制打印
	let refundBreak:AbnormalItemProps = { num: 0, togetherIds: [], sids: [] };
	// 异常地址订单
	let abnormalAddress:AbnormalItemProps = { num: 0, togetherIds: [], sids: [] };
	// 不可达订单
	let unArrive:AbnormalItemProps = { num: 0, togetherIds: [], sids: [] };
	// 库存不足订单
	let unStock: AbnormalItemProps = { num: 0, togetherIds: [], info: [], sids: [] };
	// 库存不足订单且不可强制打印
	let unStockBreak: AbnormalItemProps = { num: 0, togetherIds: [], info: [], sids: [] };
	// 低毛利润订单
	let lowProfit: AbnormalItemProps = { num: 0, togetherIds: [], info: [], sids: [] };
	// 低毛利润订单且不可强制打印
	let lowProfitBreak: AbnormalItemProps = { num: 0, togetherIds: [], info: [], sids: [] };
	// 商品未绑定本地货品
	let unBindGoods:AbnormalItemProps = { num: 0, togetherIds: [], sids: [] };
	let unBindGoodsBreak:AbnormalItemProps = { num: 0, togetherIds: [], sids: [] };
	// 未生成运单号的订单
	let unSid:AbnormalItemProps = { num: 0, togetherIds: [] };
	// 已操作过发货的订单
	let shipStatus:AbnormalItemProps = { num: 0, togetherIds: [], sids: [] };
	// 订单所选快递与指定快递不符-可强制打印
	let unPromiseLogistics:AbnormalItemProps = { num: 0, togetherIds: [], sids: [] };
	// 订单所选快递与指定快递不符fxg--不可强制打印
	let unFxgPromiseLogistics:AbnormalItemProps = { num: 0, togetherIds: [], sids: [] };
	// 拆单发货  自动发货不支持
	let splitTrade: AbnormalItemProps = { num: 0, togetherIds: [], sids: [] };
	// 订单业务类型与模板不匹配
	const mismatching: AbnormalItemProps = { num: 0, togetherIds: [] };
	const mismatchingBreak: AbnormalItemProps = { num: 0, togetherIds: [] };
	// 如果是线上已发货
	const onlineShip: AbnormalItemProps = { num: 0, togetherIds: [] };
	// 如果是线上已退款
	const abnormalOnlineRefunded: AbnormalItemProps = { num: 0, togetherIds: [] };
	// 已退款成功/已取消商品
	const alreadyRefundBreak:AbnormalItemProps = { num: 0, togetherIds: [], sids: [] };
	// 系统赠品异常
	let abnormalSysGift:AbnormalItemProps = { num: 0, togetherIds: [] };
	let abnormalSysGiftBreak:AbnormalItemProps = { num: 0, togetherIds: [] };

	// 小时达平台配送订单
	let abnormalXsdPtps:AbnormalItemProps = { num: 0, togetherIds: [] };
	let abnormalXsdPtpsBreak:AbnormalItemProps = { num: 0, togetherIds: [] }; // 不可强制

	// 官方仓发
	let gfcf: AbnormalItemProps = { num: 0, togetherIds: [] };
	// AE自营订单
	let aezy: AbnormalItemProps = { num: 0, togetherIds: [] };
	// 波次拣货
	let wave_no: AbnormalItemProps = { num: 0, togetherIds: [] };
	// 波次拣货异常
	let bizWaveAbnormal: AbnormalItemProps = { num: 0, togetherIds: [] };
	let unWarehouseSlotNameBreak: AbnormalItemProps = { num: 0, togetherIds: [] };
	let scmOrderBreak: AbnormalItemProps = { num: 0, togetherIds: [] }; // 分销订单不支持生成波次


	const ExCode = isMergePrint ? selectedTempGroup.exCode : selectedTemp.ExCode;
	const ExId = isMergePrint ? String(selectedTempGroup.id) : String(selectedTemp.Exid);
	console.log('orderList', ExCode, ExId, orderList);
	for (const item of orderList) {
		let sid = null;
		// 如果是加价发顺丰或是顺丰包邮但是快递模板不是顺丰
		if ((item.hasSfExpressService || item.freeSF) && ((ExId !== "-901" && ExCode !== 'SF') || (ExId === "-901" && !item?.sidsExNames[0]?.includes("顺丰")))) {
			if (abnormalTradeTemplateCheckNew?.supportForceAction) {
				mismatching.num += 1;
				mismatching.togetherIds.push(item.togetherId);
			} else {
				mismatchingBreak.num += 1;
				mismatchingBreak.togetherIds.push(item.togetherId);
			}
		}
		// 订单所属店铺长期未同步订单
		if (item.rdsLastSyncTimeout) {
			unSync.num += 1;
			unSync.togetherIds.push(item.togetherId);
		}
		// 如果是线上已发货
		if (item.onlineShip) {
			onlineShip.num += 1;
			onlineShip.togetherIds.push(item.togetherId);
		}
		console.log('xxxxx', onlineShip);

		if (!(SID_STATUS.includes(item.sids?.[0]))) {
			sid = item.sids?.[0];
		}
		// 商品未绑定本地货品不可强制 abnormalItemUnbindCheckNew.waveBeforeCheck
		if (item.noGoodsLink && type == AbnormalFromType.波次生成) {
			unBindGoodsBreak['num'] += 1;
			unBindGoodsBreak['togetherIds'].push(item.togetherId);
		}
		if (item.noGoodsLink && abnormalItemUnbindCheckNew.printBeforeCheck) {
			unBindGoods['num'] += 1;
			unBindGoods['togetherIds'].push(item.togetherId);
			sid && unBindGoods['sids'].push(sid);
		}
		if (item.isPending) {
			isPending['num'] += 1;
			isPending['togetherIds'].push(item.togetherId);
			sid && unBindGoods['sids'].push(sid);
		}
		if (item.abnormalAddress && abnormalAddressCheckNew?.printBeforeCheck && item.platform !== PLAT_SPH) {
			abnormalAddress['num'] += 1;
			abnormalAddress['togetherIds'].push(item.togetherId);
			sid && unBindGoods['sids'].push(sid);
		}
		// 系统配置：快递不可达订单检测
		if (canReachList.includes(item._canReach) && abnormalExpressArriveCheckNew?.printBeforeCheck) {
			unArrive['num'] += 1;
			unArrive['togetherIds'].push(item.togetherId);
			sid && unBindGoods['sids'].push(sid);
		}
		// 允许强制打印 含退款商品的订单
		if (item.hasRefund && abnormalRefundCheckNew?.printBeforeCheck && abnormalRefundCheckNew?.supportForceAction) {
			refund['num'] += 1;
			refund['togetherIds'].push(item.togetherId);
			sid && unBindGoods['sids'].push(sid);
		}
		// 不允许强制打印 含退款商品的订单
		if (item.hasRefund && abnormalRefundCheckNew?.printBeforeCheck && !abnormalRefundCheckNew?.supportForceAction) {
			refundBreak['num'] += 1;
			refundBreak['togetherIds'].push(item.togetherId);
			sid && unBindGoods['sids'].push(sid);
		}

		// 已退款成功/已取消商品
		if (getHasAlreadyRefundGoods(item?.trades)) {
			alreadyRefundBreak['num'] += 1;
			alreadyRefundBreak['togetherIds'].push(item.togetherId);
			sid && unBindGoods['sids'].push(sid);
		}
		if (SID_STATUS.includes(item.sids?.[0]) && template.ExCode !== 'NONE' && !isNoLogistics) {
			unSid['num'] += 1;
			unSid['togetherIds'].push(item.togetherId);
		}
		if (item.isSplitSend && isPreShip) {
			splitTrade['num'] += 1;
			splitTrade['togetherIds'].push(item.togetherId);
			sid && unBindGoods['sids'].push(sid);
		}
		// BIC质检订单
		if (item?.serviceTagList?.includes('bic_order')) {
			bic_order['num'] += 1;
			bic_order['togetherIds'].push(item.togetherId);
		}

		// 官方仓发订单
		if (item?.serviceTagList?.includes('gfcf')) {
			gfcf['num'] += 1;
			gfcf['togetherIds'].push(item.togetherId);
		}

		// AE自营订单
		if (item?.serviceTagList?.includes('aezy')) {
			aezy['num'] += 1;
			aezy['togetherIds'].push(item.togetherId);
		}

		// 如果要从子订单拿异常数据，建议都放这里，减少循环计算
		item?.trades?.forEach(trade => {
			trade?.orders?.forEach(order => {
				// 系统赠品且异常 sysGiftStatus == 2-异常
				if (order?.isSysGift && order?.sysGiftStatus == 2 && abnormalSysGiftCheckNew?.printBeforeCheck) {
					// printBeforeCheck sendBeforeCheck 是一起的，判断一个就行
					// supportForceAction 是否允许强制操作
					if (abnormalSysGiftCheckNew?.supportForceAction && !abnormalSysGift['togetherIds']?.includes(item.togetherId)) {
						abnormalSysGift['num'] += 1;
						abnormalSysGift['togetherIds'].push(item.togetherId);
					} else if (!abnormalSysGiftCheckNew?.supportForceAction && !abnormalSysGiftBreak['togetherIds']?.includes(item.togetherId)) {
						abnormalSysGiftBreak['num'] += 1;
						abnormalSysGiftBreak['togetherIds'].push(item.togetherId);
					}
				}
				// 如果是线上已退款
				if (order.abnormalOnlineRefunded) {
					abnormalOnlineRefunded.num += 1;
					abnormalOnlineRefunded.togetherIds.push(item.togetherId);
				}
				// 需要判断子订单的放下面

				// 需要判断勾选的商品
				if (order.isChecked) {
					// 已生成拣货波次
					if (order.waveNo && !wave_no['togetherIds']?.includes(item.togetherId)) {
						wave_no.num += 1;
						wave_no.togetherIds.push(item.togetherId);
					}

					// 商品未设置货位
					if (!order?.warehouseSlotName && !unWarehouseSlotNameBreak['togetherIds']?.includes(item.togetherId)) {
						unWarehouseSlotNameBreak['num'] += 1;
						unWarehouseSlotNameBreak['togetherIds'].push(item.togetherId);
					}
				}
			});
		});

		// 得物订单商家未接单
		if (item?.serviceTagList?.includes('acceptOrderFlagNo')) {
			unAcceptOrder['num'] += 1;
			unAcceptOrder['togetherIds'].push(item.togetherId);
		}

		// 小时达平台配送订单
		if (item?.serviceTagList?.includes('xsd_ptps')) {
			if (abnormalXsdPtpsCheck?.supportForceAction) {
				abnormalXsdPtps['num'] += 1;
				abnormalXsdPtps['togetherIds'].push(item.togetherId);
			} else {
				abnormalXsdPtpsBreak['num'] += 1;
				abnormalXsdPtpsBreak['togetherIds'].push(item.togetherId);
			}
		}
		// 波次拣货异常
		if (item?.serviceTagList?.includes('bizWaveAbnormal')) {
			bizWaveAbnormal.num += 1;
			bizWaveAbnormal.togetherIds.push(item.togetherId);
		}

		// 分销订单不支持波次
		if (isSourceScm(item)) {
			scmOrderBreak.num += 1;
			scmOrderBreak.togetherIds.push(item.togetherId);
		}
	}

	// 获取未勾选任何宝贝订单
	unSelect = getUnSelectOrder(orderList);

	// 检查库存不足的订单
	let isStockVersion = await getWarehouseVersion();
	// 无库存版本中断异常检测
	if (isStockVersion) {
		let stockInfo;
		if (userStore?.isStockAllocationVersion) {
			stockInfo = await getOrderAllotStock(orderList); // 库存版且开启扣减库存，订单占用库存方式为占用库存
		} else {
			stockInfo = await getOrderNotAllotStock(orderList); // 检查库存版 不占库存
		}
		if (stockInfo?.num > 0) {
			console.log('abnormalStockWarnCheckNew', abnormalStockWarnCheckNew);

			if (
				(abnormalStockWarnCheckNew?.printBeforeCheck && type == 'PRINT')
				|| (abnormalStockWarnCheckNew?.sendBeforeCheck && ["SEND", "PRESEND"].includes(type))
			) {
				if (abnormalStockWarnCheckNew?.supportForceAction) {
					unStock = stockInfo;
				} else {
					unStockBreak = stockInfo;
				}
			} else if (type == AbnormalFromType.波次生成) { // 库存不足订单不可强制 abnormalStockWarnCheckNew?.waveBeforeCheck
				unStockBreak = stockInfo;
			}
		}
	}

	unPromiseLogistics = getUnPromiseLogisticsOrder(orderList.filter(i => ![PLAT_FXG].includes(i.platform)), template);
	unFxgPromiseLogistics = getUnPromiseLogisticsOrder(orderList.filter(i => [PLAT_FXG].includes(i.platform)), template);

	let abnormalInfos: any = {};
	console.log('unSyncunSyncunSync', unSync);

	abnormalInfos[type] = {
		unSync,
		unSelect,
		isPending,
		refund,
		refundBreak,
		abnormalAddress,
		unArrive,
		unStock,
		unStockBreak,
		lowProfit,
		lowProfitBreak,
		unBindGoods,
		unSid,
		shipStatus,
		unPromiseLogistics,
		unFxgPromiseLogistics,
		splitTrade,
		mismatching,
		mismatchingBreak,
		onlineShip,
		abnormalOnlineRefunded,
		alreadyRefundBreak,
		bic_order,
		abnormalSysGift,
		abnormalSysGiftBreak,
		unAcceptOrder,
		abnormalXsdPtps,
		abnormalXsdPtpsBreak,
		gfcf,
		aezy,
		unBindGoodsBreak,
		wave_no,
		unWarehouseSlotNameBreak,
		bizWaveAbnormal, // 添加波次拣货异常
	};

	for (const key in abnormalInfos[type]) {
		if (abnormalManage[type]?.breakOrder.includes(key) || abnormalManage[type]?.ignoreOrder.includes(key)) {
			if (abnormalInfos[type][key]?.num > 0) {
				hasAbnormal = true;
			}
		}
	}

	abnormalInfos['total'] = orderList.length;

	return { abnormalInfos, hasAbnormal };
};

// 获取未勾选任何宝贝订单
const getUnSelectOrder = (orderList: IPackage[]) => {
	let unSelectNum = 0;
	let togetherIds:any = [];
	for (const item of orderList) {
		let isNotCheckedOrder = true;
		item.trades?.forEach((trade) => {
			const checkOrders = trade.orders.filter((order) => order.isChecked);
			if (checkOrders && checkOrders.length) {
				isNotCheckedOrder = false;
			}
		});

		if (isNotCheckedOrder) {
			togetherIds.push(item.togetherId);
			unSelectNum += 1;
		}
	}
	return { num: unSelectNum, togetherIds };
};


// 处理异常订单的勾选
const handleAbnormalOrderChoice = (abnormalInfos: any, type:string) => {
	// 所有异常订单
	let allTogetherIds = [];
	// 中断的异常订单
	let breakTogetherIds = [];
	// 可忽略的异常订单
	let ignoreTogetherIds = [];
	let obj = abnormalInfos[type];
	const {
		tradeListStore: {
			tempAbnormalIgnoreKey
		}
	} = tradeStore;
	for (const key in obj) {
		// * 把异常订单（包含中断流程、忽略流程）push进allTogetherIds
		// * 发货情况下 如果tempAbnormalIgnoreKey包含了当前key的话不push
		if ((obj[key] && (abnormalManage[type].breakOrder?.includes(key) || abnormalManage[type].ignoreOrder?.includes(key)))) {
			if (type === 'SEND' && !tempAbnormalIgnoreKey.includes(key)) {
				allTogetherIds.push(...obj[key].togetherIds);
			} else if (type === 'PRINT') {
				allTogetherIds.push(...obj[key].togetherIds);
			} else if (type === 'PRESEND') {
				allTogetherIds.push(...obj[key].togetherIds);
			} else if (type === AbnormalFromType.波次生成) {
				allTogetherIds.push(...obj[key].togetherIds);
			} 
			if (abnormalManage[type]?.breakOrder.includes(key)) {
				breakTogetherIds.push(...obj[key].togetherIds);
			}
		}
	}
	allTogetherIds = Array.from(new Set(allTogetherIds));
	breakTogetherIds = Array.from(new Set(breakTogetherIds));

	return {
		allTogetherIds,
		breakTogetherIds
	};

};

// 判断是否同一个订单 存在多个待忽略异常
const checkHasMultiAbnormal = (abnormalInfos: any, type:string, ignoreList:any[], ids:any[]) => {
	let obj = abnormalInfos[type];
	let uniqueIds = cloneDeep(ids);
	for (const key in obj) {
		if (obj[key] && abnormalManage[type].ignoreOrder?.includes(key) && !ignoreList?.includes(key)) {
			// 判断其他可忽略异常情况是否存在相同togetherId
			uniqueIds =	uniqueIds.filter((id:any) => !obj[key]?.togetherIds.includes(id));

		}
	}
	return uniqueIds;
};

// 订单所选快递与指定快递不符
const getUnPromiseLogisticsOrder = (list: IPackage[], template: any) => {

	let unSelectNum = 0;
	let togetherIds:any = [];
	let sids:any = [];
	const { isMergePrint } = tradeStore;
	list.forEach(order => {
		if (order.promiseLogisticsList?.length > 0) {
			let sid = null;
			if (!(SID_STATUS.includes(order.sids?.[0]))) {
				sid = order.sids?.[0];
			}
			let isPromiseLogistics = false;
			if (isMergePrint) {
				isPromiseLogistics = !order.promiseLogisticsList.every((p: any) => (template.userTemplateList?.find((item:any) => item.exCode == p.exCode)));
			} else {
				isPromiseLogistics = order.promiseLogisticsList.every((p: any) => p.exCode !== template.ExCode);
			}

			isPromiseLogistics && (unSelectNum += 1);
			isPromiseLogistics && (togetherIds.push(order.togetherId));
			isPromiseLogistics && (sids.push(order.sid));
		}
	});
	console.log('getUnPromiseLogisticsOrder', list, template, unSelectNum, togetherIds);

	return { num: unSelectNum, togetherIds, sids };
};


export {
	getAbnormalInfo,
	getUnSelectOrder,
	handleAbnormalOrderChoice,
	abnormalManage,
	checkHasMultiAbnormal,
	getUnPromiseLogisticsOrder
};


