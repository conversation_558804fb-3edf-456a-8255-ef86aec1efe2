import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import antdDayjs from 'antd-dayjs-vite-plugin';
import inject from '@rollup/plugin-inject';
import { viteStaticCopy } from 'vite-plugin-static-copy';
import * as fs from 'fs';

const path = require('path');
const htmlTemplatePlugin = require('./config/vite-plugins/html-template-plugin.js');

const packageJson = require('./package.json');


export default defineConfig(({ command, mode }) => {
	const env = loadEnv(mode, process.cwd(), '');

	// 获取构建相关的环境变量
	const { APP_BUILD_TYPE = "", APP_BUILD_ENV = "", APP_BUILD_PATH = "" } = process.env;
	const buildByOss = APP_BUILD_TYPE === "oss";

	// 版本号逻辑
	let version = packageJson.version;
	if (APP_BUILD_ENV === 'erpg') {
		version = packageJson.grayVersion;
	}

	// 构建路径配置
	const buildPrefix = `/${APP_BUILD_ENV}/${version}`;
	const publicPathCdn = `${APP_BUILD_PATH}${buildPrefix}/`;

	// 用来本地环境开启vite https
	let httpsOptions:any = false; // 默认http
	if (env.VITE_HTTPS === 'true') {
		httpsOptions = {
			key: fs.readFileSync('/Users/<USER>/kuaidizs.cn.key'), // https证书统一地址
			cert: fs.readFileSync('/Users/<USER>/kuaidizs.cn.pem'),
		};
	}

	// 保留原来vite代理形式
	let serverObject:any = {
		host: '0.0.0.0', // 开启局域网访问
		proxy: null, // proxy
		open: false, // 自动打开浏览器
	};
	if (env.VITE_HOST === 'localhost') {
		serverObject = {
			host: true,
			proxy: {
				"/api": {
					target: "http://erptest.kuaidizs.cn/",
					changeOrigin: true,
					secure: false,
					rewrite: (path) => path.replace(/^\/api/, ""),
				},
				"/print/center": {
					target: "http://erptest.kuaidizs.cn/",
					changeOrigin: true,
					secure: false,
					rewrite: (path) => {
						return path.replace('', "");
					},
				},
				"/item": {
					target: "http://erptest.kuaidizs.cn/",
					changeOrigin: true,
					secure: false,
					rewrite: (path) => {
						return path;
					},
				},
				// 添加对erppre.kuaidizs.cn的代理
				// "/erppre-api": {
				// 	target: "https://erppre.kuaidizs.cn/",
				// 	changeOrigin: true,
				// 	secure: false,
				// 	rewrite: (path) => path.replace(/^\/erppre-api/, ""),
				// },
			},
			open: true,
		};
	}

	return {
		base: buildByOss ? publicPathCdn : '/',
		build: {
			outDir: buildByOss ? `dist${buildPrefix}` : 'dist',
			sourcemap: false,
			minify: 'esbuild',
			chunkSizeWarningLimit: 1000,
			rollupOptions: {
				input: {
					main: buildByOss ? './index-oss.html' : './index.html',
				},
				output: {
					// 统一使用 . 分隔符，与 webpack 保持一致
					chunkFileNames: 'js/[name].[hash].js',
					entryFileNames: 'js/[name].[hash].js',
					assetFileNames: (assetInfo) => {
						if (assetInfo.name?.endsWith('.css')) {
							return 'css/[name].[hash][extname]';
						}
						return 'assets/[name].[hash][extname]';
					},
					manualChunks: {
						vendor: ['react', 'react-dom'],
						antd: ['antd'],
						lodash: ['lodash'],
						echarts: ['echarts'],
					},
				},
			},
		},
		envPrefix: ['APP_'],
		// 控制 public 目录的处理
		publicDir: buildByOss ? false : 'public',  // OSS 模式不自动复制 public 目录
		plugins: [
			react({
				babel: {
					plugins: [
						["@babel/plugin-proposal-decorators", { legacy: true }],
						["@babel/plugin-proposal-class-properties", { loose: true }],
						// 自定义 CSS Modules 插件，自动为 CSS 导入添加 ?modules 查询参数
						"./config/plugins/babelPluginAutoCssModules.js"
					],
				},
			}),
			antdDayjs(),
			inject({
				'jQuery': 'jquery',
				'$': 'jquery',
				'window.jQuery': 'jquery',
			}),
			// 复制静态资源，对应 webpack 的 CopyWebpackPlugin
			viteStaticCopy({
				targets: [
					// 静态资源始终复制到 dist 根目录（与 webpack 保持一致）
					{
						src: 'order',
						dest: buildByOss ? '../..' : '.'  // OSS: dist/erp/1.4.8 -> dist/, 普通: dist/ -> dist/
					},
					{
						src: 'static',
						dest: buildByOss ? '../..' : '.'  // OSS: dist/erp/1.4.8 -> dist/, 普通: dist/ -> dist/
					},
					// OSS 模式下复制 favicon 到构建目录
					...(buildByOss ? [
						{
							src: 'public/favicon.png',
							dest: '.'
						},
						{
							src: 'public/favicon.ico',
							dest: '.'
						}
					] : [])
				]
			}),
			// HTML 模板变量注入插件
			htmlTemplatePlugin({
				env,
				version,
				buildByOss
			})

		],
		server: {
			host: serverObject['host'], // 开启局域网访问
			port: 8088,
			open: serverObject['open'], // 自动打开浏览器
			https: httpsOptions, // 是否开启https
			historyApiFallback: true,
			proxy: serverObject['proxy'], // 根据VITE_HOST判断是否走代理
		},
		resolve: {
			// 配置路径别名
			alias: {
				'@': path.resolve(__dirname, './src')
			},

		},
		css: {
			modules: {
				// 启用 CSS Modules
				localsConvention: 'camelCase',
				generateScopedName: '[local]--[hash:base64:5]', // 与 webpack 配置保持一致
			},
			preprocessorOptions: {
				scss: {
					api: 'modern-compiler',
					silenceDeprecations: ['legacy-js-api']
				},
				less: {
					javascriptEnabled: true, // 注意，这一句是在less对象中，写在外边不起作用
					modifyVars: { // 在这里进行主题的修改，参考官方配置属性
						'primary-color': '#FD8204',
						'link-color': '#FD8204',
						'form-item-margin-bottom': '16px',
						'border-radius-base': '2px'
					},
				}
			}
		},
		define: {
			__APP_ENV__: env.APP_ENV,
			// 处理 process.env 环境变量，抛出所有环境变量
			'process.env': Object.keys(env)
				.reduce((processEnv, key) => {
					processEnv[key] = JSON.stringify(env[key]);
					return processEnv;
				}, {})
		},
		assetsInclude: "",
		esbuild: {
			logOverride: { 'this-is-undefined-in-esm': 'silent' }
		},
		optimizeDeps: {
			exclude: ['./large-screen', 'react-dom/client']
		},
	};
});
