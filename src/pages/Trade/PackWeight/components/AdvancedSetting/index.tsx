import React, { useEffect, useState } from "react";
import { Checkbox, Form, InputNumber, Modal, Radio, Space } from "antd";
import { observer } from "mobx-react";
import { useForm } from "antd/es/form/Form";
import styles from "./index.module.scss";
import { TradePrintSetUpdatePrintSetApi } from "@/apis/trade";
import memoFn from "@/libs/memorizeFn";
import userStore from "@/stores/user";
import { weightUnit } from "@/pages/Index/Settings/System/constants";
import { accMul } from "@/utils/util";
import packWeightStore from "@/stores/trade/packWeight";
import WeightInput from '@/components/Input/InputNumber/WeightInput';
import VideoMonitorControl from "@/pages/AfterSale/ScanRegister/components/AftersaleScan/ScanSetModal/components/VideoMonitorControl";

const formRender = (i) => {
	const { children, ...ret } = i;
	return (
		<Form.Item
			key={ ret.name }
			{ ...ret }
		>
			{children}
		</Form.Item>
	);
};

const AdvancedSettingModal = () => {
	const {
		advancedSettingModalVisible,
		setAdvancedSettingModalVisible,
		getAdvancedSettingConfig,
		updateAdvancedSettingConfig,
		videoMonitorState,
		setVideoMonitorState,
	} = packWeightStore;
	const [form] = useForm();
	const [loading, setLoading] = useState(false);
	const [weightType, setWeightType] = useState("");
	const [isWeightCheck, setIsWeightCheck] = useState(false);
	const isKg = true;
	// const isKg = userStore.userSetting?.weightUnit == weightUnit.显示kg;
	const inputWeightProps = isKg ? {
		formatter: (value: any) => (value === '' ? value : +(value / 1000).toFixed(3)),
		parser: (value: any) => (value === '' ? value : accMul(value, 1000)),
	} : {
		precision: 0
	};
	const inputRateProps = {
		formatter: (value: any) => (value === '' ? value : Number(value).toFixed(2)),
	};
	const onCancel = () => {
		setAdvancedSettingModalVisible(false);
	};
	const onOk = async() => {
		setLoading(true);
		const globalConfig = await memoFn.getAdvancedSet();
		const weightParams = form.getFieldsValue();
		weightParams.weightCheckType = weightType;
		weightParams.videoMonitor = videoMonitorState ? 1 : 0;
		try {
			await TradePrintSetUpdatePrintSetApi({
				id: globalConfig.id,
				weighingAndShippingConfig: weightParams
			});
			setAdvancedSettingModalVisible(false);
			updateAdvancedSettingConfig(weightParams);
			setVideoMonitorState(!!weightParams.videoMonitor);
		} catch (error) {
			console.log('error: ', error);
		}
		setLoading(false);
	};

	const handleConfigInfo = async() => {
		const configInfo = await getAdvancedSettingConfig().catch();
		console.log('configInfo: ', configInfo);
		form.setFieldsValue(configInfo);
		setIsWeightCheck(configInfo.isWeightCheck);
		setWeightType(configInfo.weightCheckType || "weightNum");
		setVideoMonitorState(!!configInfo?.videoMonitor);
	};

	const weightCheckChange = (e) => {
		setIsWeightCheck(e.target.checked);
	};

	useEffect(() => {
		if (advancedSettingModalVisible) {
			handleConfigInfo();
		}
	}, [advancedSettingModalVisible]);
	useEffect(() => {
		handleConfigInfo();
	}, []);

	const formList = [
		{
			name: "isWeightOnly",
			label: "发货设置",
			valuePropName: 'checked',
			children: (
				<Checkbox >
					仅称重不发货
				</Checkbox>
			)
		},
		{
			name: "isWeightCheck",
			label: "重量校验",
			valuePropName: 'checked',
			children: (
				<Checkbox onChange={ (e) => weightCheckChange(e) }>
					支持重量校验
				</Checkbox>
			)
		},
		{
			name: "opt",
			wrapperCol: { offset: 4 },
			children: (
				<>
					<Space align="center">
						<Form.Item><Radio disabled={ !isWeightCheck } checked={ weightType === 'weightNum' } onChange={ () => { setWeightType("weightNum"); } } />预计重量浮动数值范围 上浮</Form.Item>
						<Form.Item name="widthUpNum">
							<WeightInput width={ 80 } disabled={ !isWeightCheck } isKg={ isKg } placeholder=" " />
						</Form.Item>
						<Form.Item>{isKg ? 'kg' : 'g'}，下浮</Form.Item>
						<Form.Item name="widthDownNum">
							<WeightInput width={ 80 } disabled={ !isWeightCheck } isKg={ isKg } placeholder=" " />
						</Form.Item>
						<Form.Item>{isKg ? 'kg' : 'g'}</Form.Item>
					</Space>
					<Space align="center">
						<Form.Item><Radio disabled={ !isWeightCheck } checked={ weightType === 'weightRate' } onChange={ () => { setWeightType("weightRate"); } } />预计重量浮动比例范围 上浮</Form.Item>
						<Form.Item name="widthUpRate">
							<InputNumber
								min={ 0 }
								max={ 100 }
								{ ...inputRateProps }
								style={ { width: '80px' } }
								disabled={ !isWeightCheck }
							/>
						</Form.Item>
						<Form.Item>%，下浮</Form.Item>
						<Form.Item name="widthDownRate">
							<InputNumber
								min={ 0 }
								max={ 100 }
								{ ...inputRateProps }
								style={ { width: '80px' } }
								disabled={ !isWeightCheck }
							/>
						</Form.Item>
						<Form.Item>%</Form.Item>
					</Space>
				</>
			),
		}
	];

	return (
		<Modal
			visible={ advancedSettingModalVisible }
			width={ 640 }
			title="高级设置"
			confirmLoading={ loading }
			onOk={ onOk }
			className={ styles.modal }
			onCancel={ onCancel }
		>
			<Form
				form={ form }
				labelCol={ { span: 4 } }
			>
				{
					formList?.map(formRender)
				}
			</Form>
			<VideoMonitorControl
				videoMonitorState={ videoMonitorState }
				onVideoMonitorChange={ setVideoMonitorState }
				name="称重视频监控："
			/>
		</Modal>
	);

};
export default observer(AdvancedSettingModal);
