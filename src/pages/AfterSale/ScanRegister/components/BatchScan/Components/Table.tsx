/* eslint-disable jsx-a11y/control-has-associated-label */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState, useMemo, useCallback, useRef } from "react";
import { Button, Checkbox, Dropdown, Menu, Modal, Popover, Table, Tooltip, Alert, Image } from "antd";
import { observer } from "mobx-react";
import _, { cloneDeep } from "lodash";
import { CopyOutlined, DeleteOutlined, ExclamationCircleOutlined, FormOutlined, RedoOutlined, SettingOutlined } from "@ant-design/icons";
import dayjs from "dayjs";
import cs from 'classnames';
import s from '../index.module.scss';
import { AfterSaleType, BatchScanIsMatch } from "../../../constants";
import { RefundSystemTypeEnum, getPlatformTradeLink, getPlatformDetailLink, refundItemTypeEnum } from "@/pages/AfterSale/TradeList/utils";
import PlatformIcon from '@/pages/Warehouse/StockSync/HandStockSync/components/PlatformIcon';
import message from '@/components/message';
import ImageComp from "@/components/Image";
import EditModal from "./EditModal";
import userStore from "@/stores/user";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import BuyerNickComp from "@/components-biz/BuyerNickComp";
import Icon from "@/components/Icon";
import BatchModifyMemoModal from "@/pages/Trade/components/BatchModifyMemoModal";
import { tradeStore } from "@/stores";
import { getTradeFlag, getTradeFlagTag } from "@/pages/Trade/utils";
import { copyToPaste } from "@/utils";
import { PLAT_HAND, PLAT_KS, PLAT_SCMHAND } from "@/constants";
import { inventoryDeduct } from "@/pages/Index/Settings/System/constants";
import OfflineMemoModal, { EnumFromPage, IBatchOfflineMemoModalProps } from "@/components-biz/OfflineMemoModal";
import { BatchUpdateLocalNoteApi, GetRefundGlobalConfigListApi, UpdateRefundGlobalConfigApi } from "@/apis/aftersale/trade";
import { AftersaleGlobalConfig, BatchExportType, OfflineMemoEnum, listItemName, PRODUCT_CONTENT_KEY_ENUM, DEFAULT_PRODUCT_CONTENT_SETTING, PRODUCT_CONTENT_ENUM, REVERSE_MAPPING, UpdateTypeEnum } from "@/pages/AfterSale/TradeList/constants";
import BaseTable from "@/components/SearchTable/BaseTable";
import BatchExportModal, { exportTypeEnum } from "@/pages/AfterSale/TradeList/components/BatchExportModal";
import { downloadCenter } from "@/pages/Index/DownloadCenter/utils";
import { ModulesFunctionEnum } from "@/types/schemas/setting/download";
import { sortList, MissedType } from "./contants";
import { useStores } from "@/stores/tool";
import PrintCenter from '@/print/index';
import { GenerateRefundLabelApi, BatchIsPendingApi } from "@/apis/aftersale/scanRegister";
import useGetState from '@/utils/hooks/useGetState';
import { avoidRepeatReq } from "@/utils/util";
import RefundItemType from '@/pages/AfterSale/components/RefundItemType';
import SearchTable from '@/components/SearchTableVirtual';
import { LabelPrintSourceEnum } from '@/constants/labelPush';
import { FieldsPermissionEnum } from "@/utils/permissionCheck/fieldsPermissionCheck";
import { isSourceScm, isAfterSaleSourceScm, getShopName } from "@/components-biz/ShopListSelect/shopListUtils";
import GoodsContentSetting from '@/pages/AfterSale/TradeList/components/GoodsContentSetting';
import { ColSetPlaceEnum } from '@/components/SearchTable/BaseCheckGroupTable';
import QuickCheckRows from '@/pages/AfterSale/TradeList/components/QuickCheckRows';
import { afterRefundStatusEnum } from '@/pages/AfterSale/ExpressIntercept/utils';
import BatchModifyLocalMemoPicModal from '@/pages/AfterSale/TradeList/components/BatchModifyLocalMemoPicModal';
import { AFTERSALE_REQUEST_SOURCE } from '@/pages/AfterSale/constants';
import VideoMonitorControl from "../../AftersaleScan/ScanSetModal/components/VideoMonitorControl";
import SelectShootingDeviceModal from "@/components-biz/SelectShootingDeviceModal";
import { local } from "@/libs/db";
import videoMonitorStore from "@/stores/trade/videoMonitor";

export enum AfterSaleBizEnum {
	售后扫描 = "refundScanListColumn",
	售后订单 = "refundListShowColumnSet",
	商品信息配置 = 'refundBatchScanItemContentSetting'
}
const SELECTED_SHOOTING_DEVICES_CACHE_KEY = 'SELECTED_SHOOTING_DEVICES_BATCH_SCAN';

const BatchScanTable = (props) => {
	const {
		dataSource: oldSource,
		pagination,
		onDelete,
		onRefresh,
		onReceive,
		onChange,
		onDeleteSingle,
		onEdited,
		lastSearchParams,
		className,
		missedList = [],
		missedType = '',
		statistics = {},
		onClearMissedList,
	} = props;
	const { setModifyMemoPackage, setIsShowBatchModifyMemoModal, modifyMemoPackage,
		batchScanVideoMonitorState,
		setBatchScanVideoMonitorState,
		batchScanselectedShootingDevices,
		setbatchScanSelectedShootingDevices,
		batchScannvrDevices,
		setbatchScanNvrDevices
	 } = tradeStore;
	 const { 
		checkKdzsPrintComponent,
		connectWs,
		disconnectWs,
		videoMonitor
	} = videoMonitorStore;
	const batchModifyLocalMemoPicModalRef = useRef(null);
	const [selectedRows, setSelectedRows] = useState([]);
	const [selectedRowKeys, setSelectedRowKeys] = useState([]);
	const [deletingList, setDeletingList] = useState([]);
	const [editRowData, setEditRowData] = useState<{[K:string]:any}>({});
	const [editModalVisible, setEditModalVisible] = useState(false);
	const [editMemoModalVisible, setEditMemoModalVisible] = useState(false);
	const [offlineMemoData, setOfflineMemoData] = useState<IBatchOfflineMemoModalProps["data"]>({ visible: false });
	const [userSystemSetting, setUserSystemSetting] = useState({});
	const { isShowZeroStockVersion } = userStore;
	const [batchExportModalVisible, setBatchExportModalVisible] = useState(false);
	const [conditionList, setConditionList] = useState([]);
	const [printLoading, setPrintLoading] = useState(false);
	const [userInfo, setUserInfo, getUserInfo] = useGetState({});
	const [upStockConfig, setUpStockConfig] = useState(true);
	const [confirmReceiveLoading, setConfirmReceiveLoading] = useState(false);
	const [batchRegisterAutoCreateExchangeOrder, setBatchRegisterAutoCreateExchangeOrder] = useState(false); // 批量登记确认收货自动创建换货手工单配置
	const [productContentList, setProductContentList] = useState<string[]>(DEFAULT_PRODUCT_CONTENT_SETTING);
	const [lastSelectedKey, setLastSelectedKey] = useState(null); // 记录上一次选中的行
	const [isShiftPressed, setIsShiftPressed] = useState(false);
	const [scanSettingModalVisible, setScanSettingModalVisible] = useState(false);
	const [isShowChooseModel, setIsShowChooseModel] = useState(false); // 是否显示选择拍摄设备
	const [ishow, setIsShow] = useState(false); // 新增状态
	
	// 校验并更新缓存的设备信息
	const validateAndUpdateCachedDevices = async() => {
		try {
		// 获取缓存的设备信息
			const cachedDevices = local.getByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY) || [];
		
			if (cachedDevices.length === 0) {
				return;
			}

			// 获取最新的设备列表
			const currentDeviceList = await videoMonitorStore.getDeviceCameraList();
			// 过滤掉禁用的设备
			const enabledDevices = currentDeviceList.filter(item => item.status === 1);

			// 校验缓存中的设备是否还存在
			const validDevices = cachedDevices.filter((cachedDevice: any) => enabledDevices.find((device: any) => device.id === cachedDevice.id));

			// 校验是否属于同一个NVR
			if (validDevices.length > 0) {
				const nvrIds = validDevices.map((device: any) => device.deviceId);
				const uniqueNvrIds = Array.from(new Set(nvrIds));
				if (uniqueNvrIds.length > 1) {
				// 只保留第一个NVR下的设备
					const firstNvrId = nvrIds[0];
					const sameNvrDevices = validDevices.filter((device: any) => device.deviceId === firstNvrId);
					local.setByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY, sameNvrDevices);
					setbatchScanSelectedShootingDevices(sameNvrDevices);
				} else {
					local.setByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY, validDevices);
					setbatchScanSelectedShootingDevices(validDevices);
				}
			} else {
				local.setByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY, []);
				setbatchScanSelectedShootingDevices([]);
			}

			// 获取nvr设备列表
			const nvrDevices = await videoMonitorStore.getDeviceList();
			setbatchScanNvrDevices(nvrDevices);
		} catch (error) {
			console.error('校验缓存设备失败:', error);
			// 清除无效缓存
			local.removeByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY);
			setbatchScanSelectedShootingDevices([]);
			setbatchScanNvrDevices([]);
		}
	};
	const handleReconnect = async() => {
		console.log('重新连接');
		await validateAndUpdateCachedDevices();
		await checkKdzsPrintComponent();
		connectWs();
	};

	useEffect(() => {
		if (batchScanVideoMonitorState && videoMonitor) {
			handleReconnect();
		} else {
			disconnectWs(); // 断开ERP聚合控件连接
		}
	}, [batchScanVideoMonitorState, videoMonitor]);
	   // 监听状态变化，更新 ishow
	useEffect(() => {
		console.log(videoMonitor, batchScanVideoMonitorState, 'videoMonitor, videoMonitorState');
		// 确保 userInfo 已经加载完成
		if (userInfo?.userId) {
			const newIshow = videoMonitor == 1 && batchScanVideoMonitorState;
			setIsShow(newIshow);
		}
	}, [videoMonitor, batchScanVideoMonitorState, userInfo?.userId]);
	// 处理选择拍摄设备确认
	const handleSelectShootingDeviceConfirm = (selectedDevices: any[]) => {
		// 缓存选中的设备
		local.setByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY, selectedDevices);
		setbatchScanSelectedShootingDevices(selectedDevices);
		setIsShowChooseModel(false);
	};

	// 处理选择拍摄设备取消
	const handleSelectShootingDeviceCancel = () => {
		setIsShowChooseModel(false);
	};

	// 获取选中的设备名称显示文本
	const getSelectedDevicesText = () => {
		if (batchScanselectedShootingDevices.length === 0) {
			return "请选择拍摄设备";
		} else {
			return batchScanselectedShootingDevices.map(item => item.cameraName).join(",");
		}
	};
	const resizeId = `scanRegister_width_${userStore?.userInfo?.userId}`;

	const customLogPost = (dataType: string, data:any = {}) => {
		window.errorCollection?.customMessageUpload({
			type: `售后扫描登记-批量登记: 【 ${dataType} 】`,
			data: {
				selectedRowKeys,
				...data
			}
		});
	};

	// 计算下序号
	const dataSource = useMemo(() => {
		const { pageSize = 10, current = 1 } = pagination || {};
		const baseNumber = pageSize * (current - 1);
		return oldSource.map((item, index) => {
			const curSerialNumber = parseInt(baseNumber + index + 1, 10);
			return {
				...item,
				rowId: item.id,
				serialNumber: curSerialNumber
			};
		});
	}, [pagination?.current, pagination?.pageSize, oldSource]);

	// 清空勾选
	const clearSelectedRows = () => {
		setSelectedRows([]);
		setSelectedRowKeys([]);
	};

	// 校验是否包含挂起
	const checkIsPending = (record?: any) => {
		let pending = false;
		if (record) {
			if (record?.isPending === 1) {
				pending = true;
			}
		} else if (selectedRows?.filter(row => row?.isPending === 1)?.length) {
			pending = true;
		}

		if (pending) {
			message.error("请先取消挂起包裹后处理");
		}
		return pending;
	};

	// 判断无主件有没有添加商品
	const getFilterNoTrade = () => {
		return selectedRows?.filter(row => row?.buildType == 3 && !row?.refundScanRegistItemRecordDTOList?.length) || [];
	};

	// 确认收货
	const _onReceive = () => {
		sendPoint(Pointer["售后_批量扫描登记_确认收货"]);
		if (selectedRows?.length < 1) {
			if (selectedRowKeys.length < 1) {
				Modal.confirm({
					title: '系统提醒',
					width: 500,
					centered: true,
					icon: <ExclamationCircleOutlined />,
					content: (
						<div>没有选择要操作的退货包裹，请勾选后再确认收货</div>
					),
					cancelButtonProps: { hidden: true }
				});
			}
			return;
		}
		if (checkIsPending()) {
			return;
		}
		const filterNoTrades = getFilterNoTrade();
		// const filterNoTrades = []; // test
		setConfirmReceiveLoading(true);
		try {
			Modal.confirm({
				title: '系统提醒',
				width: 400,
				centered: true,
				icon: <ExclamationCircleOutlined />,
				content: (
					<div>是否要<span className="r-bold">{selectedRows.length}</span>
						个包裹批量收货?
					</div>
				),
				onOk: () => {
					const idList = selectedRows.filter(item => !filterNoTrades.find(row => row?.id == item?.id))?.map(row => row.id);
					const isInventoryDeductOpen = userSystemSetting?.inventoryDeduct === inventoryDeduct.需要扣减库存;

					// 如果匹配到是售后单，并且存在异常，需要校验是否添加过本地货品
					const exceptionSerialNums = []; // 收集有异常的订单序号
					selectedRows.forEach(row => {
						const { buildType, exceptionType, refundScanRegistItemRecordDTOList = [] } = row;
						// exceptionType 0无异常 1异常 2忽略异常
						// buildType 1售后单 2销售单 3手动创建
						// buildType = 1，判断外层的exceptionType == 1，如果没有添加货品，弹出提示
						// buildType = 2，取明细集合refundScanRegistItemRecordDTOList里的exceptionType判断
						// buildType = 3
						if (
							(!isShowZeroStockVersion && buildType == 1 && exceptionType == 1)
							 || (!isShowZeroStockVersion && buildType == 2 && refundScanRegistItemRecordDTOList?.some(itme => itme?.exceptionType == 1))
							  || (buildType == 3 && !refundScanRegistItemRecordDTOList.length)) {
							// const isAddProducts = refundScanRegistItemRecordDTOList?.some(item => item.refundItemSource != 1 || item.sysRefundItemSource != 1);
							// if (!isAddProducts) {
							exceptionSerialNums.push(`序号${row.serialNumber}`);
							console.log('%c [ 有异常 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', row);
							// }
						}
					});
					// 零库存板不做关联校验
					// 库存版未开启库存扣减，不做关联校验
					if (
						(isShowZeroStockVersion &&	exceptionSerialNums.length > 0)
						|| (!isShowZeroStockVersion &&	exceptionSerialNums.length > 0 && isInventoryDeductOpen)
					) {
						const serialNumsText = exceptionSerialNums.join('，');
						Modal.confirm({
							centered: true,
							title: '系统提醒',
							content: `退货包裹【${serialNumsText}】未添加退货商品，请添加${isShowZeroStockVersion ? '商品' : '系统货品'}后确认收货`,
							okText: '关闭',
							cancelText: '强制收货',
							onCancel() {
								customLogPost('售后订单未关联本地货品,忽略异常,强制收货', { exceptionSerialNums });
								onReceive({ idList, upStockConfig, batchRegisterAutoCreateExchangeOrder, filterNoTrades, ignoreException: true }, onReceiveSuccess, onReceiveFail); // 忽略异常，强制收货
							},
							onOk() {
								setConfirmReceiveLoading(false);
								customLogPost('售后订单未关联本地货品，取消继续收货', { exceptionSerialNums });
							}
						});

					} else {
						onReceive({ idList, upStockConfig, batchRegisterAutoCreateExchangeOrder, filterNoTrades, ignoreException: false }, onReceiveSuccess, onReceiveFail);
					}
				},
				onCancel: () => {
					setConfirmReceiveLoading(false);
				},
			});
		} catch (error) {
			setConfirmReceiveLoading(false);
		}
	};

	const onReceiveFail = () => {
		setConfirmReceiveLoading(false);
	};

	const onReceiveSuccess = () => {
		setConfirmReceiveLoading(false);
		clearSelectedRows();
	};

	// 开始删除订单
	const deleteAftersaleOrders = () => {
		const idList = selectedRows.map(row => row.id);
		onDelete(idList, clearSelectedRows);
	};

	// 批量删除
	const _onDelete = () => {
		sendPoint(Pointer["售后_批量扫描登记_批量删除"]);
		if (selectedRows?.length < 1) {
			message.error("请先选择要删除的退货包裹");
			return;
		}
		if (checkIsPending()) {
			return;
		}
		Modal.confirm({
			title: "系统提醒",
			content: "确定要删除所选的退货包裹吗？",
			onOk: deleteAftersaleOrders
		});
	};

	// 刷新匹配结果
	const _onRefresh = () => {
		sendPoint(Pointer["售后_批量扫描登记_刷新匹配结果"]);
		if (selectedRows?.length < 1) {
			message.error("请先选择要刷新的退货包裹");
			return;
		}
		const idList = selectedRows.map(row => row.id);
		onRefresh(idList, clearSelectedRows);
	};

	// 批量备注
	const batchMemo = () => {
		if (!selectedRows.length) {
			message.error('请选择退货包裹后再操作');
			return;
		}
		// 判断是否有异常,如果是库存板，并且开启了扣库存，并且exceptionType==1就是异常
		const hasAbnormal = selectedRows.some((record) => {
			const hasException = record.exceptionType == 1;
			const isInventoryDeductOpen = userSystemSetting?.inventoryDeduct === inventoryDeduct.需要扣减库存;
			return hasException && !isShowZeroStockVersion && isInventoryDeductOpen;
		});
		if (hasAbnormal) {
			message.error('请先处理异常');
			return;
		}
		const hasNoTradeMess = selectedRows.some(record => record.isMatch === BatchScanIsMatch.未匹配);
		if (hasNoTradeMess) {
			message.error('无主件不可进行备注');
			return;
		}
		openModifyMemo(selectedRows);
	};

	// 单个删除
	const _onDeleteSingle = (record) => {
		if (checkIsPending(record)) {
			return;
		}
		Modal.confirm({
			centered: true,
			title: "系统提醒",
			content: "确定要删除所选的退货包裹吗？",
			onOk: () => {
				let _deletingList = cloneDeep(deletingList);
				_deletingList.push(record.id);
				setDeletingList(_deletingList);
				onDeleteSingle([record.id], () => {
					_deletingList = _deletingList.filter(id => id !== record.id);
					setDeletingList(_deletingList);
				});
			}
		});
	};

	// 编辑
	const onEdit = (record) => {
		sendPoint(Pointer["售后_批量扫描登记_编辑"]);
		setEditModalVisible(true);
		setEditRowData(record);
	};

	//
	const onEditModalOk = () => {
		setEditModalVisible(false);
		onEdited && onEdited();
	};

	const onModifiedMemoOk = () => {
		setEditMemoModalVisible(false);
		// 修改完备注以后，调用刷新匹配结果的接口，再调用查询接口
		const selectedRowKeys = selectedRows.map(row => row.id);
		onRefresh(selectedRowKeys);
		setSelectedRows([]);
		setSelectedRowKeys([]);
	};

	const openModifyMemo = (list) => {
		setSelectedRows(list);
		const packList = list.map((item) => ({
			trades: [{
				sellerMemo: item.sellerMemo,
				sellerMemoFlag: item.sellerFlag,
				tid: item.tid,
				ptTid: item?.ptTid || '',
			}],
			platform: item.platform,
			source: item.source,
			togetherId: item.tid,
			sellerId: item.sellerId,
			refundStatus: item.refundStatus || "",
			isDistributorUserPushRefund: item?.isDistributorUserPushRefund, // 分销商的售后单
			afterSalesFlag: true,
		}));
		setModifyMemoPackage(packList);
		setEditMemoModalVisible(true);
	};

	const modifyMemo = (item) => {
		const isInventoryDeductOpen = userSystemSetting?.inventoryDeduct === inventoryDeduct.需要扣减库存;
		if (item.isMatch === BatchScanIsMatch.未匹配) {
			message.info('无主件不可进行备注');
			return;
		}
		// 前端做兼容：只有库存版，并且开启了扣库存功能，并且是异常件，才提示
		if (!isShowZeroStockVersion && isInventoryDeductOpen && item.exceptionType == 1) {
			Modal.warning({
				title: '系统提示',
				content: '该订单已被锁定，不能进行该操作，请先处理异常',
			});
		} else {
			openModifyMemo([item]);
		}
	};

	/** ------------- 表格渲染列相关逻辑 -------------- */
	const renderSerialNumber = (_, __, index) => {
		// const { pageSize = 10, current = 1 } = pagination || {};
		// const baseNumber = pageSize * (current - 1);
		// const curSerialNumber = parseInt(baseNumber + index + 1, 10);
		return <span>{__?.serialNumber}</span>;
	};

	const renderExpressInfo = (_, record) => {
		const { companyName, sid } = record;
		return (
			<div>
				<div className="r-fs-12 r-ta-l">{companyName}</div>
				<div className="r-flex r-word-break">
					<span className="kdzs-link-text">{sid}
						<span hidden={ !sid }>
							<CopyOutlined
								onClick={ (e) => {
									e.stopPropagation();
									copyToPaste(record.sid);
								} }
								className="r-fc-black-65 r-pointer r-ml-4 r-mr-5"
							/>
						</span>
					</span>
				</div>


			</div>
		);
	};

	const renderScanRes = (_, record) => {
		const { isMatch } = record;
		return (
			<div style={ { color: isMatch === BatchScanIsMatch.未匹配 ? "#f00" : "#000" } }>{BatchScanIsMatch[isMatch]}</div>
		);
	};

	const renderMatchRes = (_, record) => {
		const { refundSystemType, afterSaleType } = record;
		const { isMatch } = record;
		return (
			isMatch === BatchScanIsMatch.未匹配 ? <span style={ { color: "#f00" } }>无主件</span>
				: (
					<div>
						<div>{AfterSaleType[afterSaleType]}</div>
						{
							RefundSystemTypeEnum[refundSystemType]
								? (<div style={ { color: "rgba(0,0,0,0.65)" } }>({RefundSystemTypeEnum[refundSystemType]})</div>)
								: ""
						}

					</div>
				)
		);
	};

	const renderShopInfo = (_, record) => {
		const { source, platform, sellerNick } = record;
		return (
			<div>
				<div className="r-flex r-ai-c">
					<PlatformIcon platform={ platform } />
					<span className="r-mr-4 r-word-break">
						{
							!userStore.hasFieldsPermission(FieldsPermissionEnum.供应商店铺信息) && isAfterSaleSourceScm(record)
								? '****'
								: getShopName({
									plat: platform,
									sellerNick,
								})
						}
					</span>
				</div>
				<div className="r-mt-6" onClick={ handleStopClick }>
					<div className="r-fc-black-65 r-wb-bw">
						<BuyerNickComp ptTid={ record?.ptTid || '' } tid={ record.tid } encryptuid={ record.buyerOpenUid } platform={ record.platform } buyerNick={ record.buyerNick } sellerId={ record?.sellerId } />
					</div>
				</div>
			</div>
		);
	};

	const renderTid = (_, record) => {
		return (
			<div className={ s['trade-info-container'] }>
				<div className={ s['trade-info-item'] }>
					<div>订单：</div>
					{
						!record.ptTid ? "--"
							: (
								<div onClick={ handleStopClick }>
									<a
										className="r-fc-1890FF"
										href={ getPlatformTradeLink(record['platform'], record).trade }
										target="_blank"
										rel="noreferrer"
									>{record.ptTid}
									</a>
									<CopyOutlined onClick={ () => { copyToPaste(record.ptTid); } } className="r-fc-black-65 r-pointer r-ml-4 r-mr-5" />
								</div>
							)
					}

				</div>
				<div className={ s['trade-info-item'] }>
					<div>售后：</div>
					{
						!record.refundId ? "--" : (
							<div onClick={ handleStopClick }>
								<a
									className="r-fc-1890FF"
									href={ getPlatformTradeLink(record['platform'], record).refund }
									target="_blank"
									rel="noreferrer"
								>{record.refundId}
								</a>
								<CopyOutlined onClick={ () => { copyToPaste(record.refundId); } } className="r-fc-black-65 r-pointer r-ml-4 r-mr-5" />
							</div>
						)
					}


				</div>
			</div>

		);

	};

	const renderRefundReason = (_, record) => {
		return (
			<div>
				<div>
					<span>售后原因：</span>
					<span>{record.refundReason}</span>
				</div>
				<div>
					<span>售后说明：</span>
					<span>{record.desc}</span>
				</div>
			</div>
		);
	};

	const renderGoodsInfo = (_, record) => {
		/**
		 * 如果匹配到了订单，并且属于异常，则不展示商品信息 ：exceptionType === 1并且没有添加过商品/货品
		 * 如果匹配到了订单，并且忽略过异常，则展示商品信息 ：exceptionType === 2
		 * 如果匹配到了订单，并且解决过异常，则展示手动添加的商品/货品信息 exceptionType === 1并且添加过商品/货品
		 * 判断是否添加过商品/货品：refundItemSource/sysRefundItemSource(0-平台快照 1-手动添加)
		 */

		// TODO[author:yuren]: 需要判断修改展示顺序ui
		const imgSize = productContentList.includes(PRODUCT_CONTENT_ENUM.小图) ? '300px'
			: productContentList.includes(PRODUCT_CONTENT_ENUM.中图) ? '500px'
				: productContentList.includes(PRODUCT_CONTENT_ENUM.大图) ? '800px' : '500px';
		const showImg = productContentList.includes(PRODUCT_CONTENT_ENUM.货品图片) || productContentList.includes(PRODUCT_CONTENT_ENUM.商品图片);
		const showPicUrl = productContentList.includes(PRODUCT_CONTENT_ENUM.货品图片);
		const showPicPath = productContentList.includes(PRODUCT_CONTENT_ENUM.商品图片);
		const showTitle = productContentList.includes(PRODUCT_CONTENT_ENUM.商品标题);
		const showSysItemAlias = productContentList.includes(PRODUCT_CONTENT_ENUM.货品简称);
		const showItemOuterId = productContentList.includes(PRODUCT_CONTENT_ENUM.商家编码);
		const showOuterId = productContentList.includes(PRODUCT_CONTENT_ENUM.货品编码);

		const showSkuProperties = productContentList.includes(PRODUCT_CONTENT_ENUM.规格名称);
		const showSysSkuAlias = productContentList.includes(PRODUCT_CONTENT_ENUM.规格别名);
		const showItemOuterSkuId = productContentList.includes(PRODUCT_CONTENT_ENUM.规格编码);
		const showOuterSkuId = productContentList.includes(PRODUCT_CONTENT_ENUM.货品规格编码);

		const showSupplierName = productContentList.includes(PRODUCT_CONTENT_ENUM.供应商);
		const showMarket = productContentList.includes(PRODUCT_CONTENT_ENUM.市场);
		const showStall = productContentList.includes(PRODUCT_CONTENT_ENUM.档口);

		const hasException = record.exceptionType == 1;
		const isExceptionResolved = hasException && record?.refundScanRegistItemRecordDTOList?.some(item => item.refundItemSource == 1 || item.sysRefundItemSource == 1);
		const content = [];
		record?.refundScanRegistItemRecordDTOList.forEach(goodItem => {

			/**
			 * 如果异常未解决或者已解决过异常，但是商品来源非手动添加，则不展示
			 * 如果用户是库存版本，但是未开启扣库存功能，不需要隐藏商品
			 */
			const isInventoryDeductOpen = userSystemSetting?.inventoryDeduct === inventoryDeduct.需要扣减库存;
			const needHideItem = (hasException && !isExceptionResolved) || (isExceptionResolved && (goodItem.refundItemSource != 1 || goodItem.sysRefundItemSource != 1));
			if (needHideItem && !isShowZeroStockVersion && isInventoryDeductOpen) {
				return;
			}
			const getLinkHref = getPlatformDetailLink(record.platform, goodItem.numIid);
			content.push(
				<div className="r-flex r-w-full r-ai-c">
					<RefundItemType data={ goodItem } />

					<div className={ `${s["goods-info-container"]} goods-info-container r-flex-1 r-ml-4` }>
						<div
							className="img-container"
							hidden={ !showImg }
						>
							<Popover
								// overlayClassName={ s['popover'] }
								placement="right"
								content={ (
									<>
										{
											(!isShowZeroStockVersion && showPicUrl) && <ImageComp style={ { width: imgSize, height: imgSize } } src={ goodItem.sysPicUrl || '' } />
										}{
											showPicPath && <ImageComp style={ { width: imgSize, height: imgSize } } src={ goodItem.picUrl || '' } />
										}
									</>
								) }
							>
								<a
								// eslint-disable-next-line no-script-url
									href={ getLinkHref || 'javascript:void(0)' }
									target={ getLinkHref ? '_blank' : '_self' }
									rel="noopener noreferrer"
									className="r-flex r-ai-c"
								>
									{
										(!isShowZeroStockVersion && showPicUrl) && <ImageComp src={ goodItem.sysPicUrl || '' } />
									}{
										showPicPath && <ImageComp src={ goodItem.picUrl || '' } />
									}
								</a>
							</Popover>
							{/* <ImageComp src={ goodItem.sysPicUrl || goodItem.picUrl } /> */}
						</div>
						<div className="info-container">
							<div className="title">
								<div className="title-content" >
									<Tooltip title={ goodItem.title || "" }>
										<div className="r-pointer" hidden={ !showTitle }>{ goodItem.title || ""}</div>
									</Tooltip>
									<Tooltip title={ goodItem.sysItemAlias || '' }>
										<div className="r-pointer" hidden={ !showSysItemAlias }>{ goodItem.sysItemAlias || ""}</div>
									</Tooltip>

									<div>
										<span className="r-pointer" hidden={ !showItemOuterId }>{ goodItem.outerId || ""}</span>
										<span className="r-pointer" hidden={ !showOuterId }>{ goodItem.sysOutId || ""}</span>

										<span className="r-pointer" hidden={ !showSupplierName }>{ goodItem.supplierName || ""}</span>
										<span className="r-pointer" hidden={ !showMarket }>{ goodItem.market || ""}</span>
										<span className="r-pointer" hidden={ !showStall }>{ goodItem.stall || ""}</span>
									</div>

								</div>
								<div className="icon-content">
									{goodItem.isCombination == 1 ? (
										<Tooltip title="组合货品">
											<span className="r-warehouse-combined">组</span>
										</Tooltip>
									) : ''}
								</div>
							</div>
							<div className="extra">
								{/* <Tooltip title={ goodItem.sysSkuName || goodItem.skuName }>

								</Tooltip> */}
								{/* <span className="extra-content r-c-gray">{isShowZeroStockVersion ? goodItem.skuName || goodItem.sysSkuName : goodItem.sysSkuName || goodItem.skuName}</span> */}
								<span className="r-c-gray" style={ { marginLeft: showSkuProperties ? '5px' : 0 } } hidden={ !showSkuProperties }>{goodItem.skuName} </span>
								<span className="r-c-gray" style={ { marginLeft: showSysSkuAlias ? '5px' : 0 } } hidden={ !showSysSkuAlias }>{goodItem.sysSkuAlias} </span>
								<span className="r-c-gray" style={ { marginLeft: showItemOuterSkuId ? '5px' : 0 } } hidden={ !showItemOuterSkuId }>{goodItem.outerSkuId}</span>
								{!isShowZeroStockVersion && <span className="r-c-gray" style={ { marginLeft: showOuterSkuId ? '5px' : 0 } } hidden={ !showOuterSkuId }>{goodItem.sysOutSkuId}</span>}
							</div>
						</div>
					</div>
				</div>

			);
		});
		return content;

	};

	const renderReceivedNum = (_, record) => {
		const hasException = record.exceptionType == 1;
		const isExceptionResolved = hasException && record?.refundScanRegistItemRecordDTOList?.some(item => item.refundItemSource == 1 || item.sysRefundItemSource == 1);
		const content = record?.refundScanRegistItemRecordDTOList.map((goodItem, index) => {
			const needHideItem = (hasException && !isExceptionResolved) || (isExceptionResolved && (goodItem.refundItemSource != 1 || goodItem.sysRefundItemSource != 1));
			if (needHideItem && !isShowZeroStockVersion) {
				return;
			}
			return (
				<div key={ `renderReceivedNum_${index}` } className={ `${s["received-num-container"]} received-num-container` }>
					{goodItem.hasRefundNum || 0}
				</div>
			);
		});
		return content;
	};

	const renderApplyRefundNum = (_, record) => {
		const hasException = record.exceptionType == 1;
		const isExceptionResolved = hasException && record?.refundScanRegistItemRecordDTOList?.some(item => item.refundItemSource == 1 || item.sysRefundItemSource == 1);
		const content = record?.refundScanRegistItemRecordDTOList.map((goodItem, index) => {
			const needHideItem = (hasException && !isExceptionResolved) || (isExceptionResolved && (goodItem.refundItemSource != 1 || goodItem.sysRefundItemSource != 1));
			if (needHideItem && !isShowZeroStockVersion) {
				return;
			}
			const currentReceivedNum = (goodItem.applyRefundNum || 0) - (goodItem.hasRefundNum || 0);
			return (
				<div key={ `renderApplyRefundNum_${index}` } className={ `${s["received-num-container"]} received-num-container` }>
					{currentReceivedNum < 0 ? 0 : currentReceivedNum}
				</div>
			);
		});
		return content;
	};
	const modifyOfflineMemo = (item) => {
		setOfflineMemoData({ visible: true, list: [item] });
	};

	const renderMemo = (_, record) => {
		if (record.isMatch === BatchScanIsMatch.未匹配) {
			return "";
		}
		// 添加判断是否为京喜订单的逻辑
		const hasJingxi = record.refundTagList?.includes('jxTrade');
		return (
			<div>
				<span>{getTradeFlag(0, null, record.sellerFlag)}</span>
				{getTradeFlagTag(record.sellerFlag, record?.sellerFlagTag)}
				<span className="r-fc-black-65">{record['sellerMemo']}</span>
				{(!record.isDistributorUserPushRefund && !hasJingxi) && (
					<span className="r-as-c r-ml-2 r-fc-1890FF">
						{record['sellerMemo']
							? <span className="r-pointer" onClick={ (e) => { e.stopPropagation(); modifyMemo(record); } }><Icon type="bianji" /></span>
							: <span className="r-pointer" onClick={ (e) => { e.stopPropagation(); modifyMemo(record); } }><Icon type="tianjiabeizhu" /></span>}
					</span>
				)}
			</div>
		);
	};

	const renderApplyRefundNumCount = (text, record) => {
		if (record.isMatch === BatchScanIsMatch.未匹配) {
			return "";
		}
		return (
			<div>
				{text || ""}
			</div>
		);
	};

	const renderRefundAmount = (text, record) => {
		// console.log('record', record);

		return (
			<div>
				<div>{text || "0.00"}</div>
				{
					record.platform == PLAT_KS && Number(record.freight)
						? <div>退货运费：{record.freight}</div>
						: null
				}
			</div>
		);
	};

	const renderOfflineMemo = (_, record) => {
		return (
			<div>
				<span className={ cs('r-fc-black-65') }>{record[listItemName.线下备注]}</span>
				<span className="r-as-c r-ml-2 r-fc-1890FF">
					{record[listItemName.线下备注]
						? <span className="r-pointer" onClick={ (e) => { e.stopPropagation(); modifyOfflineMemo(record); } }><Icon type="bianji" /></span>
						: <span className="r-pointer" onClick={ (e) => { e.stopPropagation(); modifyOfflineMemo(record); } }><Icon type="tianjiabeizhu" /></span>}
				</span>
			</div>
		);
	};



	const renderOpt = (_, record) => {
		return (
			<div className="r-flex r-ai-c r-jc-c">
				<div
					className={ s.edit }
					onClick={ (e) => {
						e.stopPropagation();
						onEdit(record);
					} }
				>
					<FormOutlined />&nbsp;编辑
				</div>
				<div className="r-ml-10">
					{
						deletingList.includes(record.id)
							? (
								<span
									className={ `${s.delete} ${s.deleting}` }
								><RedoOutlined spin />
									&nbsp;删除
								</span>
							) : (
								<span
									className={ s.delete }
									onClick={ (e) => {
										e.stopPropagation();
										_onDeleteSingle(record);
									} }
								>
									<DeleteOutlined />&nbsp;删除
								</span>
							)
					}
				</div>
			</div>
		);
	};

	const GoodsContentSettingOnOk = (list) => {
		console.log(15, list);
		setProductContentList(list);
		// 转化下value
		const paramsVal = list.map(x => PRODUCT_CONTENT_KEY_ENUM[x]);
		const value = paramsVal ? paramsVal.join(',') : '';
		// 调接口存储
		UpdateRefundGlobalConfigApi([{ biz: AfterSaleBizEnum.商品信息配置, value }]);
		console.log(value, '--------23123123');
	};

	const renderGoodsContentColHeader = useMemo(() => (
		<div className="r-flex r-ai-c">
			{/* 售后扫描登记 */}
			<div>商品信息</div>
			<span className="r-pointer r-ml-4">
				<GoodsContentSetting onOk={ GoodsContentSettingOnOk } productContentList={ productContentList }>
					<SettingOutlined />
				</GoodsContentSetting>
			</span>
		</div>
	), [productContentList]);

	const Select = () => {
		const allKeys = props.dataSource.map((record) => record.id); // 所有行的主键
		const allSelected = selectedRowKeys.length === allKeys.length; // 判断是否全选
		const isIndeterminate = selectedRowKeys.length > 0 && selectedRowKeys.length < allKeys.length; // 半选状态

		return (
			<Checkbox
				indeterminate={ isIndeterminate } // 控制半选状态
				checked={ allSelected } // 控制全选状态
				onChange={ (e) => {
					const isChecked = e.target.checked;

					if (isChecked) {
						// 全选
						setSelectedRowKeys(allKeys);
						setSelectedRows(props.dataSource);
					} else {
						// 清空选中
						setSelectedRowKeys([]);
						setSelectedRows([]);
					}
				} }
			/>
		);
	};

	const handleStopClick = (event) => {
		event.stopPropagation();
	};

	// 添加修改线下备注图片的函数
	const modifyOfflineMemoPic = (item) => {
		// 添加 ref 检查
		if (batchModifyLocalMemoPicModalRef.current) {
			batchModifyLocalMemoPicModalRef.current.open({
			  refundId: item.refundId,
			  refundScanRecordId: item.id,
			  sid: item.sid,
			  localMemoPic: Array.isArray(item.localContentPicList)
			   ? item.localContentPicList.join(',')
			    : item.localContentPicList || '',
			  requestSource: AFTERSALE_REQUEST_SOURCE.批量登记,
			}, () => onRefresh([item.id]));
		  } else {
			console.error('BatchModifyLocalMemoPicModal ref is not initialized');
		  }
	};

	const columns: any[] = [
		{
			width: 50,
			noDrag: true,
			lock: true,
			align: 'center',
			title: <Select />,
			dataIndex: 'check',
			colFixed: 'left',
			render: (checked, record) => {
				return (
					<div style={ { display: 'flex', alignItems: 'center' } }>
						{record?.isPending === 1 && <Icon type="suo_mian" size={ 16 } />}
						<span className={ record?.isPending ? 'r-ml-2' : '' }>
							<Checkbox
								checked={ selectedRowKeys.includes(record.id) } // 控制行选中状态
							/>
						</span>
					</div>
				);
			},
		},
		{
			dataIndex: "serialNumber",
			colFixed: 'left',
			title: "序号",
			align: "center",
			width: 50,
			noDrag: true,
			render: renderSerialNumber,
			lock: true,
		}, {
			dataIndex: "expressInfo",
			title: "快递信息",
			width: 160,
			render: renderExpressInfo
		}, {
			dataIndex: "scanRes",
			title: "扫描结果",
			align: "center",
			width: 80,
			render: renderScanRes
		}, {
			dataIndex: "matchRes",
			title: "匹配结果",
			align: "center",
			width: 80,
			render: renderMatchRes
		}, {
			dataIndex: "goodsInfo",
			title: renderGoodsContentColHeader,
			sortSet: { name: '商品信息' },
			width: 300,
			render: renderGoodsInfo
		}, {
			dataIndex: "applyRefundNumCount",
			title: "申请数量",
			width: 300,
			render: renderApplyRefundNumCount
		}, {
			dataIndex: "refundAmount",
			title: "退款金额",
			width: 300,
			render: renderRefundAmount
		},
		{
			dataIndex: "payment",
			title: "原订单实付金额",
			width: 100,
			sortSet: {
				ischecked: false
			},
			render: (text, record, index) => {
				return (
					<div className={ cs('r-fc-black-65') }>
						<p>{record?.payment || 0}元</p>
						<p>{`(含运费：${record.postFee || 0}元)`}</p>
					</div>
				);
			}
		},
		{
			dataIndex: "hasRefundNum",
			title: "已收数量",
			width: 80,
			render: renderReceivedNum
		}, {
			dataIndex: "applyRefundNum",
			title: "本次收货数量(合计)",
			width: 120,
			render: renderApplyRefundNum
		}, {
			dataIndex: "shopInfo",
			title: "店铺/买家昵称",
			width: 200,
			render: renderShopInfo
		}, {
			dataIndex: "tid",
			title: "订单/售后单号",
			render: renderTid,
			width: 260,
		},
		{
			dataIndex: "applyPayTime",
			title: "付款/售后申请时间",
			width: 200,
			sortSet: {
				ischecked: false
			},
			render: (text, record, index) => {
				return (
					<div>
						<div className="r-flex r-ai-c r-c-black85">
							<span>订单付款：</span>
							<span>{record['payTime'] ? dayjs(record.payTime).format("YYYY-MM-DD HH:mm:ss") : '-'}</span>
						</div>
						<div className="r-flex r-ai-c r-c-black85">
							<span>售后申请：</span>
							<span>{record['refundCreatedTime'] ? dayjs(record.refundCreatedTime).format("YYYY-MM-DD HH:mm:ss") : '-'}</span>
						</div>
					</div>
				);
			}
		},
		{
			dataIndex: "refundStatus",
			title: "平台退款状态",
			width: 100,
			sortSet: {
				ischecked: false
			},
			render: (text, record, index) => {
				return (
					<div className={ cs('r-fc-black-85') }>
						{record['refundStatus'] ? afterRefundStatusEnum[record['refundStatus']] : '-'}
					</div>
				);
			},
		},
		{
			dataIndex: "refundReason",
			title: "售后原因",
			width: 200,
			render: renderRefundReason,
		}, {
			dataIndex: "sellerMemo",
			title: "旗帜/卖家备注",
			render: renderMemo,
			width: 120,
			minWidth: 120,
		}, {
			dataIndex: OfflineMemoEnum.线下备注内容,
			title: "线下备注",
			render: renderOfflineMemo,
			width: 80,
		},
		{
			title: '线下备注图片',
			key: 'localMemoPic',
			dataIndex: 'localMemoPic',
			width: 140,
			minWidth: 140,
			render: (text, record, index) => {
				const hasImgList = record.localContentPicList && record.localContentPicList.length > 0;
				return (
					<>
						<div className="r-flex r-fw-w r-mt-4">
							<p>
								{hasImgList ? (
									<div className="r-flex r-ai-c">
										{/* 修改为循环展示所有图片，从左到右排列，超出边界才换行 */}
										<div className="r-flex r-ai-c r-fw-w" style={ { maxWidth: '300px', display: 'flex', flexDirection: 'row' } }>
											{record.localContentPicList.map((imgUrl, imgIndex) => (
												<Popover
													key={ imgIndex }
													placement="right" 
													content={ <Image src={ imgUrl } style={ { width: '300px' } } /> }
												>
													<div style={ { display: 'inline-block', marginRight: '4px', marginBottom: '4px' } }>
														<Image 
															src={ imgUrl } 
															width={ 40 } 
															height={ 40 } 
															style={ { objectFit: 'cover' } }
														/>
													</div>
												</Popover>
											))}
										</div>
										<span className="r-as-c r-ml-2 r-fc-1890FF">
											<span className="r-pointer" onClick={ (e) => { e.stopPropagation(); modifyOfflineMemoPic(record); } }><Icon type="bianji" /></span>
										</span>
									</div>
								) : (
									<span className="r-as-c r-fc-1890FF">
										<span className="r-pointer" onClick={ (e) => { e.stopPropagation(); modifyOfflineMemoPic(record); } }><Icon type="tianjiabeizhu" /></span>
									</span>
								)}
							</p>
						</div>
					</>
				);
			}
		},
		{
			dataIndex: "gmtCreate",
			title: "扫描时间",
			width: 150,
			render: text => dayjs(text).format("YYYY-MM-DD HH:mm:ss")
		}, {
			dataIndex: "operatorNick",
			title: "操作人",
			width: 100,
			render: _ => _
		}, {
			dataIndex: "operate",
			colFixed: 'right',
			className: s["column-opt"],
			title: "操作",
			width: 110,
			noDrag: true,
			render: renderOpt
		}];

	columns.forEach(i => {
		if (!i.hasOwnProperty('getCellProps')) i['getCellProps'] = () => ({ style: { verticalAlign: 'middle', padding: '8px' } });
	});

	const rowSelection:any = {
		fixed: true,
		columnWidth: 50,
		selectedRowKeys,
		selectKey: 'id',
		renderCell: (checked, record, index, originNode) => {
			// 新的虚拟列表不支持自定义选择栏的渲染
			return (
				<div style={ { display: 'flex', alignItems: 'center' } }>
					{record?.isPending === 1 && (
						<Icon type="suo_mian" size={ 16 } />
					)}
					<span className={ !record?.isPending ? 'r-ml-16' : '' }>{originNode}  </span>
				</div>
			);
		},
		onChange: (selectedRowKeys: React.Key[], selectedRows) => {
			// onChange 和要全选的时候过滤挂起功能冲突，所以不适用
			// console.log('%c [ onChange ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', selectedRowKeys, selectedRows);
		},
		onSelect: (record, selected, selectedRows, nativeEvent) => {
			// console.log('%c [ onSelect ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', record, selected, selectedRows);
			setSelectedRows(selectedRows || []);
			setSelectedRowKeys(selectedRows?.map(row => row?.id) || []);
		},
		onSelectAll: (selected, selectedRows, changeRows) => {
			// console.log('%c [ onSelectAll ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', selected, selectedRows, changeRows);
			// let filteredRows = selectedRows?.filter(row => !row?.isPending) || [];

			let filteredRows;
			if (selected) {
				 filteredRows = [...selectedRows] || [];
			} else {
				 filteredRows = selectedRows?.filter(row => !row?.isPending) || [];
			}

			const filterSelectedRowKeys = filteredRows?.map(row => row.id) || [];
			const isSelectedKeyIncluded = filterSelectedRowKeys?.some(key => selectedRowKeys?.includes(key));
			console.log(selected, selectedRows, filterSelectedRowKeys, isSelectedKeyIncluded);
			if (!isSelectedKeyIncluded) {
				setSelectedRows(filteredRows);
				setSelectedRowKeys(filterSelectedRowKeys);
			} else {
				clearSelectedRows();
			}
		},
	};

	const _onChange = (pagination, filters, sorter, { action }) => {
		console.log(606, pagination, props);
		onChange && onChange(pagination, filters, sorter, { action });
		if (action == 'paginate') {
			setSelectedRows([]);
			setSelectedRowKeys([]);
		}
	};

	// 批量线下备注
	const onBatchModifyMemo = () => {
		if (!selectedRows.length) {
			message.error('请选择退货包裹后操作');
			return;
		}
		setOfflineMemoData({ visible: true, list: selectedRows });
	};

	// 编辑线下备注
	const handleOfflineMemoOk = ({ list }) => {
		setOfflineMemoData((prev) => {
			return {
				...prev,
				loading: true
			};
		});
		const newList = list.map((item) => ({
			...item,
			updateType: UpdateTypeEnum.线下备注,
			requestSource: AFTERSALE_REQUEST_SOURCE.批量登记,
		}));
		BatchUpdateLocalNoteApi(newList).then((res) => {
			console.log(res);
			setOfflineMemoData({ visible: false, loading: false });
			const selectedRowKeys = list.map(row => row.id);
			onRefresh(selectedRowKeys);
			clearSelectedRows();
			message.success("编辑成功");
		}).catch(() => {
			setOfflineMemoData(prev => ({ ...prev, loading: false }));
		});
	};

	const handleOfflineMemoCancel = () => {
		setOfflineMemoData({ visible: false });
	};

	// ===批量打印小标签相关逻辑start===
	// 获取勾选的行信息 为何这样做 是为了无论选择都按列表顺序来
	const getSelectedRows = () => {
		const selectedRows = [];
		const dataSourceTemp = dataSource || [];
		const selectedRowKeysTemp = selectedRowKeys || [];
		// console.log('getSelectedRows', dataSourceTemp, selectedRowKeys);
		dataSourceTemp.forEach(item => {
			if (selectedRowKeysTemp.includes(item.id)) {
				selectedRows.push(item);
			}
		});
		return selectedRows;
	};

	useEffect(() => {
		if (selectedRowKeys.length > 0) {
			const selectedRows = getSelectedRows();
			setSelectedRows(selectedRows);
		}
	}, [selectedRowKeys, dataSource]);

	const initData = async() => {
		userStore.getUserInfo().then(async res => {
			setUserInfo(res);
		});

		avoidRepeatReq(GetRefundGlobalConfigListApi, {
			bizEnumList: [
				AftersaleGlobalConfig.批量登记自动入库配置,
				AftersaleGlobalConfig.批量登记确认收货自动创建换货手工单配置,
				AftersaleGlobalConfig.批量登记扫描设置
			]
		}).then(res => {
			res.forEach(item => {
				if (item.biz == AftersaleGlobalConfig.批量登记自动入库配置) {
					setUpStockConfig(item?.value ? item?.value == '1' : true);
				}

				if (item.biz == AftersaleGlobalConfig.批量登记确认收货自动创建换货手工单配置) {
					setBatchRegisterAutoCreateExchangeOrder(item?.value ? item?.value == '1' : false);
				}

				if (item.biz == AftersaleGlobalConfig.批量登记扫描设置) {
					setBatchScanVideoMonitorState(item?.value ? (JSON.parse(item.value)?.videoMonitoring) == '1' : false);
				}
			});
		});
	};

	useEffect(() => {
		initData();

	}, []);

	const getGenerateRefundLabelParams = () => {
		const userInfo = getUserInfo();
		const generateRefundLabelInfoList = [];
		const selectedRows = getSelectedRows();
		console.log('selectedRowsselectedRows11', selectedRows);

		selectedRows.forEach(row => {
			row?.refundScanRegistItemRecordDTOList?.forEach(goodItem => {
				// 过滤换货和补发的订单
				if ([refundItemTypeEnum.换货, refundItemTypeEnum.补发].includes(goodItem?.refundItemType || goodItem?.sysRefundItemType)) {
					return;
				}
				const rowData = goodItem;
				const currentReceivedNum = (goodItem.applyRefundNum || 0) - (goodItem.hasRefundNum || 0);
				const receiveNum = currentReceivedNum < 0 ? 0 : currentReceivedNum;
				// refundScanRegistItemRecordDTOList 缺少 sysSkuOuterId
				let item = {
					...rowData,
					title: rowData.title,
					picUrl: rowData.picUrl,
					tid: row.tid,
					oid: rowData.oid,
					ptTid: row?.ptTid || '',
					ptOid: row?.ptOid || '',
					goodsNum: receiveNum,
					localContent: rowData.localContent, // 线下备注
					numIid: rowData.numIid,
					skuId: rowData.skuId,
					outerId: rowData.outerId,
					skuOuterId: rowData.outerSkuId,
					skuName: rowData.skuName,
					sysItemId: rowData.sysItemId,
					sysSkuId: rowData.sysSkuId,
					sysItemAlias: rowData.sysItemAlias,
					sysOuterId: rowData.sysOuterId,
					sysSkuPicUrl: rowData.sysPicUrl,
					sysSkuName: rowData.sysSkuName,
					sysSkuAlias: rowData.sysSkuAlias,
					sysSkuOuterId: rowData.sysOutSkuId,
					storageId: row?.storageId,
					supplierid: rowData.supplierId, // 这里后端是小写
					supplierName: rowData.supplierName,
					sellerId: row?.sellerId || '',
					sellerNick: row?.sellerNick || '',
					platform: row?.platform || '',
					source: row?.source || '',
					refundId: row?.refundId || rowData?.refundId || '',
					// refundWaybillCode: row?.companyCode || '',
					refundWaybillNo: row?.sid || '',
					refundWaybillName: row?.companyName || '',
					sellerMemo: row?.sellerMemo || rowData.sellerMemo || ''
				};
				console.log("getGenerateRefundLabelParams",item);
				generateRefundLabelInfoList.push(item);
			});
		});
		return {
			userId: userInfo?.userId,
			generateRefundLabelInfoList,
		};

	};

	const generateRefundLabel = async() => {
		let refundLabelData = {};
		const params = getGenerateRefundLabelParams();

		// 只有补货且单据中没有商品时，才会出现全部数据都过滤掉的情况
		if (!params.generateRefundLabelInfoList?.length) {
			message.error('本次收货数量有值才可打印');
			return Promise.reject(new Error('没有可生成退货小标签的数据'));
		}

		console.log('%c [ params ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', params);
		setPrintLoading(true);

		try {
			const res = await GenerateRefundLabelApi(params);
			if (res.takeGoodsLabelDTOS) {
				// 同一个商品会根据数量生成多个标签，这里用skuId或者sysSkuId作为key的方式，把所有的标签存起来
				res.takeGoodsLabelDTOS.forEach(labelItem => {
					const key = `${labelItem.tid}:${labelItem.sysSkuId || labelItem.skuId}`;
					if (refundLabelData[key]) {
						refundLabelData[key].push(labelItem);
					} else {
						refundLabelData[key] = [labelItem];
					}
				});
			}
		} catch (error) {
			console.error("Error fetching refund labels:", error);
			customLogPost('获取退货唯一码失败', { error, params });
			return Promise.reject(new Error(error?.errorMessage || '生成唯一码出错'));
		} finally {
			setPrintLoading(false);
		}
		return refundLabelData;
	};

	const handlePrintCenterOrderList = (refundLabelData) => {
		const orderList = [];
		const selectedRows = getSelectedRows();
		console.log('selectedRowsselectedRowsselectedRows', selectedRows);

		selectedRows.forEach(row => {
			row?.refundScanRegistItemRecordDTOList?.forEach(goodItem => {
				// 过滤换货和补发的订单
				if ([refundItemTypeEnum.换货, refundItemTypeEnum.补发].includes(goodItem?.refundItemType || goodItem?.sysRefundItemType)) {
					return;
				}
				const curRowOrderList = [];
				// const rowData = row.originData;
				// refundScanRegistItemRecordDTOList 缺少 sysSkuOuterId
				const rowData = {
					platform: row.platform,
					tid: row.tid,
					oid: goodItem.oid,
					sellerId: row.sellerId,
					sysItemId: goodItem.sysItemId,
					sysSkuId: goodItem.sysSkuId,
					sysPicUrl: goodItem.sysPicUrl,
					sysSkuName: goodItem.sysSkuName,
					sysItemAlias: goodItem.sysItemAlias,
					// 下面两个字段都像 货品规格编码
					sysOuterSkuId: goodItem.sysOutSkuId,
					outerSkuId: goodItem.outerSkuId,
					source: row.source,
					// 感觉未用字段
					sellerAbbreviation: goodItem.sellerAbbreviation,
					refundId: row.refundId,
					localContent: row?.localContent || '', // 线下备注
					numIid: goodItem.numIid,
					skuId: goodItem.skuId,
					picUrl: goodItem.picUrl,
					title: goodItem.title,
					skuName: goodItem.skuName,
					itemAlias: goodItem.itemAlias,
					outerId: goodItem.outerId,
					sysRefundItemRecordId: goodItem.sysRefundItemRecordId,
					itemRefundItemRecordId: goodItem.itemRefundItemRecordId,
					refundItemSource: goodItem.refundItemSource,
					refundItemType: goodItem.refundItemType,
					sysItemName: goodItem.sysItemName,
					sysOuterId: goodItem.sysOuterId,
					sysSkuAlias: goodItem.sysSkuAlias,
					sysItemNo: goodItem.sysItemNo,
					market: goodItem.market,
					stall: goodItem.stall,
					supplierName: goodItem.supplierName,
					supplierId: goodItem.supplierId,
					isCombination: goodItem.isCombination,
					companyName: goodItem.companyName,
					sid: row.sid,
					costPrice: goodItem.costPrice,
					sellerNick: row.sellerNick,
					storageId: row?.storageId,
					sellerMemo: row?.sellerMemo || goodItem?.sellerMemo || ""
				};
				// console.log('rowData', rowData);
				const key = `${rowData.tid}:${rowData.sysSkuId || rowData.skuId}`;
				const curRefundLabelList = refundLabelData[key];
				// 后端没返回到数据不处理
				if (!curRefundLabelList) return;
				// 代表本次收货数量
				const receiveNum = goodItem.applyRefundNum;
				[...new Array(receiveNum)].forEach((_, index) => {
					const item = {
						...rowData,
						sellerAbbreviation: curRefundLabelList[index]?.sellerAbbreviation,
						refundLabelId: curRefundLabelList[index]?.labelId
					};
					curRowOrderList.push(item);
				});
				// console.log('curRowOrderList', curRowOrderList);
				orderList.push(...curRowOrderList);
			});

		});
		return orderList;
	};

	const getPrintCenterOrderList = (refundLabelData) => {
		let success = true;
		const orderList = handlePrintCenterOrderList(refundLabelData);
		if (orderList.length === 0) {
			message.error("本次收货数量有值才可打印");
			success = false;
			customLogPost('获取打印退货标签数据失败', { refundLabelData: orderList?.map(item => {
				return {
					tid: item?.tid,
					refund: item?.refundId,
					skuId: item?.skuId,
					refundLabelId: item?.refundLabelId,
					labelId: item?.labelId,
					refundItemType: item?.refundItemType,
				};
			}) });
		}
		return ({
			success,
			orderList
		});
	};

	// 批量打印退款标签按钮
	const handleBatchPrintRefundLabel = async() => {
		// console.log('handleBatchPrintRefundLabel', selectedRows);
		if (!selectedRows.length) {
			message.error('请选择退货包裹后操作');
			return;
		}
		if (checkIsPending()) {
			return;
		}

		let refundLabelData = {};
		try {
			// 打印之前生成退货标签唯一码
			refundLabelData = await generateRefundLabel();
			console.log('refundLabelData', refundLabelData);
		} catch (error) {
			console.log('%c [ 打印之前生成退货标签唯一码出错 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', error);
			return;
		}

		const { success, orderList } = getPrintCenterOrderList(refundLabelData);
		console.log('getPrintCenterOrderList', success, orderList);
		// orderList 少一个printNum
		if (success) {
			PrintCenter.printThdXbq({
				orderList,
				takeGoodsLabelPrintSource: LabelPrintSourceEnum.售后批量登记退款标签,
			} as any);
			customLogPost('批量打印退货标签', { orderList: orderList?.map(item => {
				return {
					tid: item?.tid,
					refund: item?.refundId,
					skuId: item?.skuId,
					refundLabelId: item?.refundLabelId,
					labelId: item?.labelId,
					refundItemType: item?.refundItemType,
				};
			}) });
		}
	};

	// ===批量打印退款标签逻辑end===

	const handleExportScanRes = () => {
		setBatchExportModalVisible(true);
	};

	const onBatchExportCancel = () => {
		setBatchExportModalVisible(false);
	};

	const onBatchExportOk = async(config) => {
		// 如果选择的是导出勾选订单，需要判断是否勾选了订单
		let requestParams = {
			latitude: exportTypeEnum.售后扫描,
		};
		const { type } = config;
		if (type === BatchExportType.导出勾选订单) {
			if (!selectedRows?.length) {
				message.error("请先选择需要导出的数据");
				return;
			} else {
				requestParams["idList"] = selectedRows.map(i => i.id);
			}

		} else if (type === BatchExportType.导出查询结果) {
			requestParams = {
				...requestParams,
				...lastSearchParams
			};
		}
		setBatchExportModalVisible(false);
		await downloadCenter({
			requestParams,
			fileName: `售后扫描记录_`,
			module: ModulesFunctionEnum.售后扫描记录
		});

	};

	const BatchMemoBtns = () => {
		return (
			<Dropdown.Button
				onClick={ batchMemo }
				getPopupContainer={ () => document.body }
				overlayStyle={ { width: "118px", } }
				className="r-mr-8"
				overlay={ (
					<Menu onClick={ ({ key }) => onBatchModifyMemo() }>
						<Menu.Item key="cancelClose">
							批量线下备注
						</Menu.Item>
					</Menu>
				) }
			>批量备注
			</Dropdown.Button>
		);
	};

	const getPendingList = (isPending: number) => {
		let ids = selectedRows?.filter(item => item?.isPending === isPending)?.map(row => {
			return row?.id;
		});
		return ids || [];
	};

	// 批量挂起
	const batchPending = async() => {
		if (!selectedRows.length) {
			message.error('请选择退货包裹后操作');
			return;
		}
		if (selectedRows?.every(item => item?.isPending)) {
			message.warning('选中退货包裹已是挂起状态');
			return;
		}
		let params = getPendingList(0);
		await BatchIsPendingApi({
			idList: params,
			isPending: 1,
		});
		message.success('批量挂起成功');
		onRefresh?.(params, clearSelectedRows);
	};

	// 批量取消挂起
	const batchCancelPending = async() => {
		if (!selectedRows.length) {
			message.error('请选择退货包裹后操作');
			return;
		}
		if (selectedRows?.every(item => !item?.isPending)) {
			message.warning('选中退货包裹不是挂起状态，无需取消挂起');
			return;
		}
		let params = getPendingList(1);
		await BatchIsPendingApi({
			idList: params,
			isPending: 0,
		});
		message.success('批量取消挂起成功');
		onRefresh?.(params, clearSelectedRows);
	};

	const getBatchPendingBtns = () => {
		return (
			<Dropdown.Button
				onClick={ () => batchPending() }
				getPopupContainer={ () => document.body }
				overlayStyle={ { width: "118px" } }
				className="r-mr-8"
				overlay={ (
					<Menu onClick={ () => batchCancelPending() }>
						<Menu.Item key="cancelClose">
							批量取消挂起
						</Menu.Item>
					</Menu>
				) }
			>批量挂起
			</Dropdown.Button>
		);
	};

	useEffect(() => {
		setUserSystemSetting(userStore.systemSetting);
	}, [userStore.systemSetting]);

	useEffect(() => {
		getSearchConditionConfig();
	}, [isShowZeroStockVersion]);

	const getSearchConditionConfig = async() => {
		let list = [];
		const _enum = [AfterSaleBizEnum.商品信息配置, AfterSaleBizEnum.售后扫描];
		try {
			let res = await GetRefundGlobalConfigListApi({ bizEnumList: [..._enum] });
			const _list = res.filter((item) => item.biz === AfterSaleBizEnum.售后扫描);
			const shopInfoShow = res.filter((item) => item.biz === AfterSaleBizEnum.商品信息配置);
			if (shopInfoShow.length > 0) {				// 转化
				const valueArr = shopInfoShow[0].value ? shopInfoShow[0].value?.split(',').filter(value => value).map(x => REVERSE_MAPPING[x]) : [];
				setProductContentList(valueArr);
			} else if (!isShowZeroStockVersion) {
				setProductContentList([PRODUCT_CONTENT_ENUM.货品图片, PRODUCT_CONTENT_ENUM.货品规格编码, PRODUCT_CONTENT_ENUM.商品标题, PRODUCT_CONTENT_ENUM.规格名称]);
			} else {
				setProductContentList([PRODUCT_CONTENT_ENUM.商品图片, PRODUCT_CONTENT_ENUM.商品标题, PRODUCT_CONTENT_ENUM.规格名称]);
			}
			if (_list?.length) {
				list = JSON.parse(_list[0]?.value);
				console.log(list, '_list_list_list');
				// 遍历对象数组，查找并替换字段
				list.forEach(item => {
					if (item.name === "售后商品") {
						item.name = "商品信息";
					}
				});
				list = setSerialNumber(list);
			} else {
				list = sortList;
			}
		} catch (error) {
			console.log(error);
		}
		const tableColumnWidthConfig = {};
		list.forEach(s => { tableColumnWidthConfig[s.key] = s.width; });
		localStorage.setItem(`RESIZE_${resizeId}`, JSON.stringify(tableColumnWidthConfig));
		console.log('%c [ 批量登记列配置 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', list);
		setConditionList([...list]);
	};

	const setSerialNumber = (list) => {
		const _serialIndex = list.findIndex((item) => (item.key == "serialNumber"));
		if (_serialIndex > -1) {
			const _serialItem = list.splice(_serialIndex, 1)[0];
			_serialItem.index = -1;
			_serialItem.isedit = false;
			_serialItem.dragDisabled = false;
			list.unshift(_serialItem);
		}
		return list;
	};

	const handleReset = (w, s) => {
		handleSortChange(s);
	};
	const handleSortChange = (list) => {
		list = setSerialNumber(list);

		// console.log('%c [ list ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', list);

		UpdateRefundGlobalConfigApi([{
			biz: AfterSaleBizEnum.售后扫描,
			value: JSON.stringify(list)
		}]).then(() => {
			setConditionList([...list]);
			message.success(`保存查询条件设置成功`);
		});
	};

	const onChangeUpStockConfig = async(e) => {
		let checked = e?.target?.checked;
		setUpStockConfig(checked);
		await UpdateRefundGlobalConfigApi([{
			biz: AftersaleGlobalConfig.批量登记自动入库配置,
			value: checked ? '1' : '0'
		}]);
	};

	const onExchangeConfigChange = async(e) => {
		let checked = e?.target?.checked;
		setBatchRegisterAutoCreateExchangeOrder(checked);
		await UpdateRefundGlobalConfigApi([{
			biz: AftersaleGlobalConfig.批量登记确认收货自动创建换货手工单配置,
			value: checked ? '1' : '0'
		}]);
	};

	

	// 处理扫描设置点击
	const handleScanSettingClick = () => {
		setScanSettingModalVisible(true);
	};

	// 关闭扫描设置弹窗
	const handleScanSettingClose = () => {
		setScanSettingModalVisible(false);
	};

	// 确认扫描设置
	const handleScanSettingOk = () => {
	// 这里可以添加保存设置的逻辑
		console.log('保存扫描设置');
		saveScanSettingConfig({
			videoMonitoring: batchScanVideoMonitorState ? '1' : '0'
		});
		setScanSettingModalVisible(false);
	};
		// 保存扫描设置配置
	const saveScanSettingConfig = async(config) => {
		try {
			await UpdateRefundGlobalConfigApi([{
				biz: AftersaleGlobalConfig.批量登记扫描设置,
				value: JSON.stringify(config)
			}]);
			message.success('保存成功');
			// 重新获取配置
		} catch (error) {
			console.error('保存扫描设置配置失败:', error);
			message.error('保存失败');
		}
	};
	// 批量功能按钮区域
	const expandContext = (
		<div className="r-flex">
			<div className="r-flex r-ai-c">
				<Button type="primary" className="r-mr-8" onClick={ _onReceive } loading={ confirmReceiveLoading }>确认收货</Button>
				<Button className="r-mr-8" onClick={ _onDelete }>批量删除</Button>
				<Button onClick={ _onRefresh } className="r-mr-8">刷新匹配结果</Button>
				{BatchMemoBtns()}
				{getBatchPendingBtns()}
				<Button onClick={ handleBatchPrintRefundLabel } loading={ printLoading } className="r-mr-8">批量打印退款标签</Button>
				<Button onClick={ handleExportScanRes } className="r-mr-8">导出扫描记录</Button>
				{
					userStore.isShowZeroStockVersion ? null : (
						<Checkbox
							checked={ upStockConfig }
							onChange={ onChangeUpStockConfig }
						>
							确认收货后自动入库
						</Checkbox>
					)
				}
				<div className="r-ml-8">
					<Checkbox
						checked={ batchRegisterAutoCreateExchangeOrder }
						onChange={ onExchangeConfigChange }
					>
						确认收货后自动创建换货手工单
					</Checkbox>
				</div>

			</div>
			<div style={ { position: 'absolute', right: 16 } }>
				<Button onClick={ handleScanSettingClick }> 
					<SettingOutlined style={ { marginLeft: 0 } } />
					扫描设置
				</Button>
			</div>
		</div>
	);
	
	const onResizeChange = (config = []) => {

		const list = conditionList.map((column) => {
			return { ...column, width: config[column.key] };
		});
		UpdateRefundGlobalConfigApi([{
			biz: AfterSaleBizEnum.售后扫描,
			value: JSON.stringify(list)
		}]);
	};


	const copyMissedList = () => {
		copyToPaste(missedList?.join(","));
	};
	const clearErrorList = () => {
		onClearMissedList?.();
	};

	// 表格上方区域
	const tableExtra = useMemo(() => {
		const messageContent = (
			<div className="r-fs-14 r-bold">
				以下<span className="r-bold r-c-error">&nbsp;{missedList?.length}&nbsp;</span>个{MissedType?.[missedType] || '快递单号'}未查询到，请确认信息是否填写正确
			</div>
		);
		const description = missedList?.map((i, index) => {
			return (
				<span className="r-c-error r-bold">
					{i}{index + 1 === missedList.length ? "" : ","}&nbsp;
				</span>
			);
		}) || '';
		if (missedList?.length) {
			return (
				<div className="r-mb-8">
					<Alert
						className={ s["no-match-alert"] }
						style={ { padding: 16 } }
						message=""
						description={ (
							<div className="r-flex r-fd-c">
								{
									missedList?.length > 0 && (
										<div>
											{messageContent}
											<div>
												{description}
												<span className="kdzs-link-text" onClick={ copyMissedList }>
													复制
												</span>
											</div>
										</div>
									)
								}
							</div>
						) }
						onClose={ () => clearErrorList() }
						type="warning"
						showIcon
						closable
					/>
				</div>
			);
		}
		return '';
	}, [missedList, missedType]);

	// 表格上方列配置区域
	const tableExtraFn = (sortNode) => {
		// 即快递单号数、涉及的订单数、售后单数，删重后的结果
		const { refundCount = '', sidDistinctCount = '', tidDistinctCount = '' } = statistics || {};
		let sidNum = sidDistinctCount || 0;
		let tradeNum = tidDistinctCount || 0;
		let retundNum = refundCount || 0;

		if (!sidNum && !tradeNum && !retundNum) {
			return (
				<div className="r-flex r-ai-c r-jc-fe r-mb-8">{
					ishow ? (
						<Button 
							type="primary" 
							className="r-ml-16" 
							style={ { backgroundColor: "#1890ff", borderColor: "#1890ff" } }
							onClick={ () => {
								setIsShowChooseModel(true);
							} }
						>
							{getSelectedDevicesText()}
						</Button>
					) : null
				}
				</div>
			);
		}
		return (
			<>
				<div className="r-flex r-ai-c r-jc-fe r-mb-8">
					<div className="r-flex-1">
						共
						<span className="trade-status-error">{sidNum}</span>
						个快递单号（涉及
						<span className="trade-status-error">{tradeNum}</span>
						笔订单，
						<span className="trade-status-error">{retundNum}</span>
						笔售后单）
					</div>
					{
						ishow ? (
							<Button 
								type="primary" 
								className="r-ml-16" 
								style={ { backgroundColor: "#1890ff", borderColor: "#1890ff" } }
								onClick={ () => {
									setIsShowChooseModel(true);
								} }
							>
								{getSelectedDevicesText()}
							</Button>
						) : null
					}
					<div className="r-ml-10">{sortNode}</div>
				</div>
			</>
		);
	};

	// 快速勾选点击
	const moreChoiceOper = (type: string) => {
		console.log(type);

		let quickSelectedRows:any[] = [];
		switch (type) {
			case '全选':
				quickSelectedRows = dataSource?.map(item => item);
				break;
			case '反选':
				quickSelectedRows = dataSource?.filter(item => !selectedRowKeys?.includes(item.id));
				break;
			case '取消勾选':
				quickSelectedRows = [];
				break;
			case '勾选非挂起数据':
				quickSelectedRows = dataSource?.filter(item => item?.isPending !== 1);
				break;
			default:
				break;
		}

		setSelectedRowKeys(quickSelectedRows?.map(item => item.id) || []);
		setSelectedRows(quickSelectedRows);
	};

	// 分页栏快速勾选
	const getSelectedContent = useCallback(() => {
		let count = selectedRowKeys?.length || 0;
		let refundAmountCount = 0;
		dataSource?.forEach(row => {
			if (selectedRowKeys?.includes(row.id) && row.refundAmount) {
				refundAmountCount += (Number?.(row.refundAmount) || 0);
			}
		});
		return (
			<div className={ s.selectedContent }>
				<div className={ s.selectedContentQuickCheck }>
					<QuickCheckRows onTabClick={ (type) => { moreChoiceOper(type); } } type="batchScan" />

					<div className={ s.selectedCount }>
						<Icon style={ { fontSize: 24, color: '#FAAD14' } } type="tishi-fill" />

						<div>本页已勾选 <span className="r-c-FF4D4F">{count}</span> 条数据，共计 <span className="r-c-FF4D4F">{refundAmountCount.toFixed(2)}</span> 元</div>
					</div>
				</div>
			</div>
		);
	}, [selectedRowKeys, dataSource, ishow, isShowChooseModel]);

	// 行点击新增shift多选功能
	const handleRowClick = (e, record, index) => {
		console.log('%c [ 点击行 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', index + 1);
		// 按住 Shift 键多选
		let _selectedRowKeys = [...selectedRowKeys];

		if (isShiftPressed && lastSelectedKey !== null) {
			// 如果上一次是选中状态，点击后选中所有行
			const lastIndex = dataSource.findIndex((row) => row.id === lastSelectedKey);
			const currentIndex = dataSource.findIndex((row) => row.id === record.id);

			if (lastIndex !== -1 && currentIndex !== -1) {
				const [start, end] = [lastIndex, currentIndex].sort((a, b) => a - b);
				const rangeKeys = dataSource.slice(start, end + 1).map((row) => row.id);

				if (_selectedRowKeys.includes(lastSelectedKey)) {
					// 上次是选中状态，选中中间的所有行
					_selectedRowKeys = Array.from(new Set([..._selectedRowKeys, ...rangeKeys]));
				} else {
					// 上次未选中，取消选中中间的所有行
					_selectedRowKeys = _selectedRowKeys.filter((key) => !rangeKeys.includes(key));
				}
			}
		} else {
			// 处理单击行的选中/取消选中
			// eslint-disable-next-line no-lonely-if
			if (!_selectedRowKeys.includes(record.id)) {
				_selectedRowKeys.push(record.id);
			} else {
				_selectedRowKeys = _selectedRowKeys.filter((key) => key !== record.id);
			}
		}

		setSelectedRowKeys(_selectedRowKeys);
		setSelectedRows(dataSource.filter((row) => _selectedRowKeys.includes(row.id)));
		setLastSelectedKey(record.id); // 记录本次点击的行

		// let _selectedRowKeys = [...selectedRowKeys];
		// if (!_selectedRowKeys.includes(record.id)) {
		// 	_selectedRowKeys.push(record.id);
		// } else {
		// 	_selectedRowKeys = _selectedRowKeys?.filter(key => key !== record.id);
		// }
		// setSelectedRowKeys(_selectedRowKeys);
		// setSelectedRows(dataSource?.filter(row => _selectedRowKeys?.includes(row.id)));
	};

	// 监听 Shift 键状态
	useEffect(() => {
		const handleKeyDown = (e) => {
			if (e.key === "Shift") setIsShiftPressed(true);
		};
		const handleKeyUp = (e) => {
			if (e.key === "Shift") setIsShiftPressed(false);
		};

		window.addEventListener("keydown", handleKeyDown);
		window.addEventListener("keyup", handleKeyUp);

		return () => {
			window.removeEventListener("keydown", handleKeyDown);
			window.removeEventListener("keyup", handleKeyUp);
		};
	}, []);

	return (
		<div className={ s.batchScanTable }>
			{
				conditionList?.length
					? (
						<>
							<SearchTable
								pageSizeId="AfterSale_BatchScanTable"
								tableExtra={ tableExtra }
								baseTableConfig={ {
									tableExtraFn,
									pagination: {
										...pagination,
										showSizeChanger: true,
										pageSizeOptions: [10, 20, 50, 100, 200],
									},
									showSelectedCount: getSelectedContent(),
									columns,
									rowKey: "rowId",
									dataSource,
									expandContext,
									expandContextStyle: { height: '72px', display: 'flex', alignItems: 'center', overflowX: 'unset' },
									headerColSet: {
										inPlace: ColSetPlaceEnum.表格上方,
										sortId: `scanRegister_sort_${userStore?.userInfo?.userId}`,
										resizeId,
										onResizeChange,
										initList: conditionList,
										onReset: (w, s) => {
											handleReset(w, s);
										},
										onSortChange: (list) => {
											handleSortChange(list);
										},
										useDrawer: true
									},
									isStickyHeader: false, // 是否吸顶innerTableChange
									stickyTop: 162,
									onRow: (record, index) => ({
										onClick: (e) => handleRowClick(e, record, index)
									}),
									// rowSelection: {
									// 	type: 'checkbox',
									// 	...rowSelection,
									// 	...{
									// 		selectedRowKeys
									// 	}
									// },
									rowClassName: (record) => {
										if (record?.isPending === 1) {
											return 'r-row-locked';
										}
										if (selectedRowKeys.includes(record.id)) {
											return 'r-row-checked';
										}
										return 'r-row-default';
									}
								} }
								onChange={ ({ pageNo, pageSize }) => {
									_onChange({ current: pageNo, pageSize }, undefined, undefined, { action: 'paginate' });
								} }
								paginationStyle={ { bottom: 0, originBottom: 50 } }
							/>
						</>
					)
					: null
			}
			{
				editModalVisible
					? (
						<EditModal
							data={ editRowData }
							onOk={ onEditModalOk }
							onCancel={ () => setEditModalVisible(false) }
						/>
					) : ""
			}
			{editMemoModalVisible ? (
				<BatchModifyMemoModal
					onOk={ onModifiedMemoOk }
					onCancel={ () => setEditMemoModalVisible(false) }
				/>
			) : ''}

			{/* 线下备注 */}
			<OfflineMemoModal onOk={ handleOfflineMemoOk } onCancel={ handleOfflineMemoCancel } data={ offlineMemoData } fromPage={ EnumFromPage.售后扫描登记 } />

			{
				batchExportModalVisible && <BatchExportModal onCancel={ onBatchExportCancel } onOk={ onBatchExportOk } latitude={ exportTypeEnum.售后扫描 } />
			}

			<BatchModifyLocalMemoPicModal ref={ batchModifyLocalMemoPicModalRef } />
			{/* 扫描设置弹窗 */}
			<Modal
				title="扫描设置"
				visible={ scanSettingModalVisible }
				onOk={ handleScanSettingOk }
				onCancel={ handleScanSettingClose }
				width={ 500 }
				destroyOnClose
			>
				<VideoMonitorControl
					videoMonitorState={ batchScanVideoMonitorState }
					onVideoMonitorChange={ setBatchScanVideoMonitorState }
					name="视频监控："
				/>
			</Modal>
						
			{/* 选择拍摄设备弹框 */}
			<SelectShootingDeviceModal
				visible={ isShowChooseModel }
				onCancel={ handleSelectShootingDeviceCancel }
				onConfirm={ handleSelectShootingDeviceConfirm }
				defaultSelectedDevices={ batchScanselectedShootingDevices }
			/>
		</div>
	);
};

export default observer(BatchScanTable);
