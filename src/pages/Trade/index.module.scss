.tradeInfoContainer {
	position: relative;
	padding : 0 10px 72px;
}

.colset-opt-container {
	position        : absolute;
	z-index         : 1001;
	padding         : 10px;
	background-color: #fff;
	border-radius   : 4px;
	bottom          : 0;
	left            : 0;
	width           : 100%;
	border-top      : 1px solid #eee;

	:global {
		.ant-btn {
			margin: 0 5px;
		}
	}
}

.bottom-container {
	position  : fixed;
	bottom    : 0;
	left      : 0;
	width     : 100%;
	height    : 70px;
	background: #ffffff;
	box-shadow: 0px -2px 16px 0px rgba(0, 0, 0, 0.1);
	z-index   : 998;

	&.showTransparent {
		background: rgba(0, 0, 0, 0.5);
		color     : #fff;
	}

	.bottom-opt-container {
		display        : flex;
		align-items    : center;
		justify-content: center;
		width          : 100%;
		height         : 70px;
	}

	.bottom-check-container {
		position   : absolute;
		top        : 50%;
		left       : 65px;
		transform  : translateY(-50%);
		display    : flex;
		align-items: center;
	}
}

.batchPrintBottom {
	.showTransparentPagination {
		:global {
			.ant-pagination-simple-pager {
				color: #fff;

				input {
					color: rgba(0, 0, 0, 0.85);
				}
			}

			.anticon {
				color: #fff;
			}
		}
	}
}


.quick-choice-container,
.operate-container {
	border    : 1px solid #e4e7ed;
	box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.06);
	background: #ffffff;

	.quick-choice-item,
	.operate-item {
		display    : flex;
		align-items: center;
		padding    : 0 20px;
		height     : 32px;
		width      : 180px;
		box-sizing : border-box;
	}
}

.operate-item-pop {
	:global {
		.ant-popover-placement-right {
			padding-left: 0;
		}

		.ant-popover-placement-top {
			padding-bottom: 0;
		}

		.ant-popover-arrow {
			display: none !important;
		}

		.ant-popover-inner-content {
			padding: 0;
		}
	}

	&:hover {
		:global {
			.anticon-right {
				color: #fd8204 !important;
			}
		}
	}
}

.operate-item-group {
	width     : 160px;
	background: rgba(255, 255, 255, 0);

	.active {
		color           : #fd8204 !important;
		background-color: #f5f5f5;
	}

	.isChildren {
		display    : flex;
		align-items: center;
		width      : 160px;
		padding    : 0 24px;
		height     : 32px;
		box-sizing : border-box;

		&:hover {
			color           : #fd8204 !important;
			background-color: #f5f5f5;
		}
	}
}

.operate-container {
	border    : 1px solid transparent;
	max-height: 580px;
	overflow-y: auto;
	box-shadow: unset;
	margin    : 8px 0;
}

.operate-container-w {
	border: 1px solid #e4e7ed;

}

.temp-action {
	display       : inline-block;
	vertical-align: -2px;

	>span {
		display   : block;
		float     : left;
		margin    : 0 10px 0 0;
		width     : 13px;
		height    : 13px;
		cursor    : pointer;
		background: url(https://static.kuaidizs.cn/resources/img/print/group.png) -102px -63px;
	}

	>.temp-subtract {
		background-position: -138px -63px;
	}
}

.express-reach-wrap {
	// position: absolute;
	// left: -13px;
}

.add_group {
	font-size      : 14px;
	color          : #1890ff;
	text-decoration: underline;
	cursor         : pointer;
}

.temp_item {
	height: 27px;
}

.cnwd_show_wrap {
	display       : inline-block;
	vertical-align: -5px;
}

.cnwd_show,
.cnwd_num {
	display      : inline-block;
	font-size    : 12px;
	color        : #365064;
	height       : 20px;
	overflow     : hidden;
	text-overflow: ellipsis;
	white-space  : nowrap;
}

.cnwd_show {
	width: 70px;
}

.cnwd_num {}

.smart-express-company {
	cursor      : pointer;
	margin-right: 24px;

	&.active {
		color: #fd8204;
	}
}

.consolidate-popover {
	:global {
		.ant-popover-title {
			padding: 0;
		}

		.ant-popover-inner-content {
			width  : 460px;
			padding: 0;

			.content-item {
				margin-bottom: 8px;

				div {

					.title-icon,
					.description-icon {
						width: 20px;
						color: #09b24c;
					}
				}

				.title-content {
					flex       : 1;
					font-size  : 14px;
					color      : #333;
					font-weight: 600;
				}

				.description-content {
					flex     : 1;
					font-size: 12px;
					color    : #666;
				}
			}
		}
	}
}

.consolidate-title {
	padding: 5px 16px 4px;
}

.consolidate-container {
	padding: 12px 16px;
}

.foot-container {
	color      : #666;
	font-size  : 12px;
	border-top : 1px solid #ddd;
	padding-top: 8px;
}

.step {
	width          : 420px;
	height         : 60px;
	background     : rgb(***********);
	border-radius  : 2px;
	display        : flex;
	align-items    : center;
	justify-content: center;

	:global {
		.step-item {
			display        : flex;
			align-items    : center;
			justify-content: center;
			height         : 100%;

			.step-info {
				display       : flex;
				flex-direction: column;
				align-items   : center;
			}

			.step-icon {
				margin: 0px 8px;
			}
		}
	}
}

.scrollLine {
	position: fixed;
	bottom  : 70px;
	height  : 15px;
	left    : 0;
	right   : 0;
	width   : 100%;
	overflow: auto;
	z-index : 999;
}

.templateToggleWarpper {
	position     : absolute !important;
	bottom       : -20px;
	left         : 50%;
	transform    : translateX(-50%);
	border-radius: 50% !important;
	border       : none !important;
}

.templateListCon {
	display: flex;
	height : auto;

	&.templateToggleStatus {
		max-height: 45px !important;
		overflow  : auto;
	}
}

.no-match-alert {
	padding: 10px !important;
}


.image-container {
	border-radius: 6px;
}

.image-container:hover {
	box-shadow: 0 0 7px #dadadacc;
}

.qusStyle {
	margin-left: 5px;
	color      : #FFA940 !important;
	/* 修改为你想要的颜色 */
}

.colset-container {
	position     : relative;
	z-index      : 1001;
	// width: fit-content;
	// max-height: calc(100vh - 300px);
	// overflow-y: auto;
	user-select  : none;

	:global {
		.colsetItem {
			display        : flex;
			align-items    : center;
			justify-content: space-between;
			height         : 32px;
			padding        : 4px 8px;
			margin         : 0 -8px;
			font-size      : 14px !important;
			border         : 1px solid transparent;

			.colset-label {
				margin      : 0 8px;
				margin-right: auto;
				user-select : none;
			}
		}

		.ant-table-thead {
			display: none;
		}

		.ant-table-cell {
			border: none;
		}
	}
}

.row-dragging {
	z-index        : 1052;
	display        : flex !important;
	align-items    : center !important;
	justify-content: space-between !important;
	height         : auto !important;
	padding        : 4px 8px;
	margin         : 0 -8px;
	background     : #fff;
	border         : 1px solid #f0f0f0;
	box-shadow     : 0px 2px 6px rgba(0, 0, 0, 0.15);

	.colset-label {
		margin      : 0 8px !important;
		margin-right: auto !important;
	}

	:global {
		.anticon-menu {
			color: #FD8204 !important;
		}
	}
}

.search-setting-drawer {
	:global {
		.ant-drawer-header-title {
			flex-direction: row-reverse;
		}

		.ant-drawer-body {
			padding-bottom: 60px;
		}

		.ant-drawer-close {
			margin-right: 0;
		}
	}
}

.columnHeight {
	width        : 32px;
	height       : 32px;
	border-radius: 2px;
	border       : 1px solid #D9D9D9;
	margin-right : 8px;
	margin-left  : -4px;
	cursor       : pointer;
}