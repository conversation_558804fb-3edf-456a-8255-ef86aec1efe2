import React, { useState, useEffect } from 'react';
import { Form, Radio, Input, Select, Button } from 'antd';
import { PlusOutlined, ReloadOutlined } from '@ant-design/icons';
import TextArea from 'antd/lib/input/TextArea';
import s from './index.module.scss';
import { getUserExpressTemplate, getUserShipAddress } from '@/apis/user'; // 导入获取运费模板的API
import { PLAT_ALI, PLAT_FXG, PLAT_KS, PLAT_PDD, PLAT_SPH, PLAT_TB, PLAT_TM } from '@/constants';

interface AfterSalesAdressProps {
  form: any;
  onChange?: (values: { freightTemplate: string }) => void;
  platform?: string; // 平台参数
  productId?: string; // 商品ID参数
  isAli?: boolean;
}

const AfterSalesAdress: React.FC<AfterSalesAdressProps> = ({ 
	form, 
	onChange,
	platform,
	productId,
	isAli
}) => {
	const [templateOptions, setTemplateOptions] = useState<Array<{id?: string, name?: string}>>([]);
	const [loading, setLoading] = useState(false);
	const [dropdownOpen, setDropdownOpen] = useState(false);
	const [selectedId, setSelectedId] = useState<string | undefined>(undefined);

	// 获取运费模板列表
	const fetchExpressTemplates = async() => {
		setLoading(true);
		try {
			const data = {
				shopId: productId,
				platform,
				pageSize: 100,
				pageNo: 1
			};
			const response = await getUserShipAddress(data);
			if (response.list && Array.isArray(response.list)) {
				const newList = response.list.map(item => {
					return {
						id: item.shippingAddressId,
						name: (item.shippingAddress || '').replace(/\^+/g, ' '),
						sendGoodsContactor: item.sendGoodsContactor || '',
						phone: item.phone || '',
					};
				});
				console.log(newList, 'newListnewListnewList');
				setTemplateOptions(newList);
				setSelectedId(form.getFieldValue('refundAddressId'));
			} 
		} catch (error) {
			// 接口失败时设置默认选项
			console.log(error, 'error');
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		// 获取运费模板列表
		fetchExpressTemplates();
	}, [platform, productId]);


	// 处理添加按钮点击事件
	const handleAddClick = () => {
		// 根据平台打开不同的URL
		let url = '';
    
		if (platform) {
			switch (platform.toLowerCase()) {
				case PLAT_SPH:
					url = `https://store.weixin.qq.com/shop/address/expressAddressMgmt`;
					break;
				default:
					console.warn('未知平台:', platform);
					return;
			}
			// 在新标签页中打开URL
			window.open(url, '_blank');
		} else {
			console.log(productId, 'productId');
		}
	};

	return (
		<div className={ s.AfterSalesAdressContainer }>
			<Form.Item label="售后地址：" className={ s.serviceFormItem }>
				<div className={ s.serviceFormControl }>
					<Form.Item name="refundAddressId" noStyle>
						<Select 
							style={ { width: 406 } } 
							className={ s.serviceSelect }
							loading={ loading }
							placeholder="请选择"
							open={ dropdownOpen }
							onDropdownVisibleChange={ setDropdownOpen }
							value={ selectedId }
							dropdownRender={ (menu) => (
								<div style={ { maxHeight: '240px', overflow: 'auto' } }>
									{templateOptions.map(template => {
										const name = template.sendGoodsContactor || '';
										const phone = template.phone || '';
										const address = template.name || '';
										const isSelected = selectedId === template.id;
										
										return (
											<div
												key={ template.id }
												className={ `ant-select-item ant-select-item-option ${isSelected ? 'ant-select-item-option-selected' : ''}` }
												onClick={ () => {
													form.setFieldsValue({ refundAddressId: template.id });
													setSelectedId(template.id);
													setDropdownOpen(false);
												} }
											>
												<div>
													<div style={ { color: '#333' } }>
														{name} {phone}
													</div>
													<div style={ { fontSize: '12px', color: '#999', marginTop: '2px' } }>
														{address}
													</div>
												</div>
											</div>
										);
									})}
								</div>
							) }
						>
							{templateOptions.map(template => {
								const address = template.name || '';
								return (
									<Select.Option key={ template.id } value={ template.id }>
										{address}
									</Select.Option>
								);
							})}
						</Select>
					</Form.Item>
				
					<Button 
						type="link" 
						className={ s.serviceAddBtn }
						onClick={ fetchExpressTemplates }
					>
						<ReloadOutlined style={ { marginTop: '2px' } } />
					</Button>
					<Button 
						type="link" 
						className={ s.serviceAddBtn }
						onClick={ handleAddClick }
					>
						<PlusOutlined style={ { marginTop: '2px' } } /> 添加
					</Button>
				</div>
			</Form.Item>
			<Form.Item
				label="售后说明："
				name="refundExplain"
				className={ s.serviceFormItem }
				labelCol={ {
					style: { alignSelf: 'flex-start', paddingTop: '0px' }
				} }
			>
				<TextArea 
					style={ { width: '900px' } } 
					placeholder="请输入售后说明" 
					autoSize={ { minRows: 3, maxRows: 3 } } 
					maxLength={ 200 }
				/>
			</Form.Item>
		</div>
	);
};

export default AfterSalesAdress;
