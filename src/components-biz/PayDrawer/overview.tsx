/*
 * @Author: 唐荣洋 <EMAIL>
 * @Date: 2025-08-26 17:37:11
 * @Description:
 */

import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Ta<PERSON>, Tooltip } from 'antd';
import dayjs from 'dayjs';
import { DISPLAY_MAP, PAY_CYCLE, PAY_TYPE } from './interface';
import s from './index.module.less';
import Flex from '@/components/Flex';
import { OrderRecordGetCurrentValueAddedStateApi, OrderRecordGetValueAddedOrderAmountApi } from '@/apis/trade';
import sendPoint from '@/utils/pointTrack/sendPoint';

interface OverviewProps {
    type: PAY_TYPE
    onPay: (_params: any) => void;
	userInfo: any;
	activeVersion?: any;
	activeRange?: any;
}
export default function Overview(props: OverviewProps) {
	const { type, userInfo, onPay } = props;
	const typeMap = DISPLAY_MAP[type];
	const { version, cycle } = typeMap;
	const [activeVersion, setActiveVersion] = useState(props.activeVersion ?? version[0].value);
	const [activeRange, setActiveRange] = useState(props.activeRange ?? cycle[0].value);
	const [tips, setTips] = useState('');
	const [currentPrice, setCurrentPrice] = useState(0);
	const getTime = (value: PAY_CYCLE) => {
		if (value == PAY_CYCLE.EXPIRE_TIME) {
			return dayjs(userInfo.expireTime).format('YYYY-MM-DD HH:mm:ss');
		}
		if (value == PAY_CYCLE.ONE_YEAR) {
			return dayjs().add(365, 'day').format('YYYY-MM-DD 23:59:59');
		}
	};
	useEffect(() => {
		OrderRecordGetCurrentValueAddedStateApi({ type }).then((res: any) => {
			if (res) {
				setTips(res);
			}
		});
	}, [type]);

	useEffect(() => {
		OrderRecordGetValueAddedOrderAmountApi({
			version: activeVersion,
			cycle: activeRange,
		}).then((res: any) => {
			setCurrentPrice(res);
		});
	}, [type, activeVersion, activeRange, userInfo.expireTime]);
	return (
		<div className={ s.overView }>
			<Flex>
				<img className={ s.icon } src={ typeMap.packIcon } alt="" />
				<div style={ { flex: 1 } }>
					<div className={ s.title }>{typeMap.title}</div>
					<div className={ s.subTitle }>{typeMap.subTitle}</div>
					<div className={ s.priceWrap }>
						<div>价格</div>
						<div>
							<span className={ s.price }>¥</span>
							<span className={ s.priceValue }>
								{(currentPrice / 100).toFixed(2)}
							</span>
						</div>
					</div>
					<div className={ s.versionWrap }>
						<div className={ s.versionTitle }>版本</div>
						{version.map((item) => (
							<div key={ item.title } className={ activeVersion == item.value ? s.versionActive : s.version } onClick={ () => setActiveVersion(item.value) }>{item.title}</div>
						))}
						{
							[PAY_TYPE.最大店铺数, PAY_TYPE.物流预警单量].includes(type)
								? <Button disabled ghost>更多数量联系销售</Button>
								: null
						}
					</div>
					<div className={ s.versionWrap }>
						<div className={ s.versionTitle }>周期</div>
						{cycle.map((item) => (
							<Tooltip placement="bottomLeft" title={ `有效期至：${getTime(item.value)}` } key={ item.title }>
								<div className={ activeRange == item.value ? s.versionActive : s.version } onClick={ () => setActiveRange(item.value) }>{item.title}</div>
							</Tooltip>
						))}
					</div>
					<Flex alignItems="center" style={ { marginTop: '24px' } }>
						<Button
							className={ s.button }
							type="primary"
							onClick={ () => {
								sendPoint(typeMap.clickPoint);
								onPay({
									type,
									version: activeVersion,
									cycle: activeRange,
									versionName: version.find(item => item.value == activeVersion)?.title,
									cycleName: cycle.find(item => item.value == activeRange)?.title,
									typeName: typeMap.title,
								});
							} }
						>立即订购
						</Button>
						<span style={ { color: 'color: rgba(0, 0, 0, 0.65)', marginLeft: '16px' } }>当前状态：{tips}</span>
					</Flex>
				</div>
			</Flex>
			<Tabs defaultActiveKey="1">
				<Tabs.TabPane tab="详情介绍" key="1">
					<img className={ s.detailIcon } src={ typeMap.icon } alt="" />
				</Tabs.TabPane>
			</Tabs>
		</div>
	);
}
