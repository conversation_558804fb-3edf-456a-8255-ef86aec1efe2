import * as printItem from './index';
import { chooseItem, showPrinterChooseModal } from '../../b-components';
import { API, Tpl } from '../../typings';
import {KDD_TYPE_NAME,ORDER_REFLUX_TYPE} from '../../common/constant'

/**
 * 选择模板方法
 */
async function chooseTemplateDialog(printType: 'kdd' | 'fhd' = 'kdd',temp = null,orderList) {
    const templateListObj: API.R.ITemplateList<Tpl.Kdd.ITemplate> = await printItem.getTemplateList(
        printType,
    );
    const tempList = temp || templateListObj.ModeListShows
    const title = printType === 'kdd' ? '请选择快递模板' : '请选择发货单模板';
    let isShowAll = null
    let tempObj = {}
    if(printType === 'kdd'){
		const showAllArr = ['hand', 'other', 'dw', 'ktt']
        const platByKddType = {
            tb: 3,
            tm: 3,
            jd: 5,
            pdd: 7,
            fxg: 8,
            ali: 3,
            c2m: 3,
            ksxd: 9,
            xhs: 13,
            sph: 14,
            yz: 15,
            xhsNew: 16,
            dw: 17,
        }
        let order  = orderList[0] || {}
        let firstType = platByKddType[order.platform]
        // 回流订单需要不同展示
        // 密文订单展示下游平台的
        // 明文订单根据高级设置展示1688的活性下游平台模板
        // 先确认是否回流订单
        // 此处拿第一个订单做参考（如果非回流订单，就是列表中包含回流订单，因业务前置校验，回流订单使用的模板类型也肯定跟第一个的平台类型一致）
        if(order.hlPlatformType){
            // 获取高级设置使用类型  1 菜鸟，2 回流平台
            let isUseCainiao =  window.erpData?.advancedSetting?.refluxOrderPrintSet == '1'
            let hlType = order.hlPlatformType.replace('hl-','')
            if((order.hlEncryptOrder || !isUseCainiao) && ORDER_REFLUX_TYPE[hlType]){
                firstType = ORDER_REFLUX_TYPE[hlType]
            }
        }
		isShowAll = orderList?.find(it => (showAllArr.includes(it.platform) || (it.platform !== orderList[0].platform) || (it?.platform === 'fxg' && it?.source === 'HAND' && it?.trades[0]?.tradeEncodeType === 1)))


		// isShowAll需要过滤视频号、得物、有赞模板
		let filterTemp = [15, 17, 14]
		// 抖音明文手工单需要过滤抖音的展示其他的
		// if (isShowAll) {
		// 	let hasFxgOrder = orderList?.find(item => item?.platform === 'fxg' && (item?.source !== 'HAND' || item?.trades[0]?.tradeEncodeType === 2))
		// 	// 展示全部默认不展示抖音，如果存在抖音的订单模板才展示抖音
		// 	if (hasFxgOrder) filterTemp = [15, 17, 14]
		// }
        tempList.filter(it=>{
            if(isShowAll){
				return it.Exid > 0 && !filterTemp.includes(it.KddType)
            }else{
				return firstType == 13 ? [13,16].includes(it.KddType) : it.KddType == firstType

            }
        }).map(o=>{
            let tempName = KDD_TYPE_NAME[o.KddType] || ''
            if(tempObj[tempName]){
                tempObj[tempName].push({
                    name: o.ExcodeName,
                    id: o.Mode_ListShowId,
                })
            }else{
                tempObj[tempName] = [{
                    name: o.ExcodeName,
                    id: o.Mode_ListShowId,
                }]
            }
        })
    }

    const tplIdx = await chooseItem({
        className: 'choose-tpl',
        title,
        isMerge:false,
        classification:printType === 'kdd',
        items: tempList.map((tpl, idx) => ({
            name: tpl.ExcodeName,
            id: tpl.Mode_ListShowId,
        })),
        classItem:tempObj
    });

    const selectTpl = tempList.find(o => o.Mode_ListShowId == +tplIdx);

    return selectTpl;
}

/**
 * 选择模板组方法
 */
 async function chooseTemplateGroupDialog(temp = null) {
    const templateListObj = temp || await printItem.getTemplateList(
        'kdd',
    );

    const title =  '请选择快递模板组'
    const tplIdx = await chooseItem({
        className: 'choose-tpl',
        title,
        items: templateListObj.map((tpl, idx) => ({
            name: tpl.groupName,
            id: idx.toString(),
        })),
    });

    const selectTpl = templateListObj[+tplIdx];

    return selectTpl;
}
// 指定快递选择模板
async function chooseTemplateByTplIdDialog(config: any) {
	try {
		const templateListObj = await printItem.getTemplateList(
			'kdd',
		);
		const printList = await printItem.getPrinterList();
		const result = await showPrinterChooseModal({
			...config,
			templateList: templateListObj?.ModeListShows || [],
			printList: printList?.printers || [],
			defaultPrint: printList?.dPrinter || '',
		});

		return result; // 返回结果而不是undefined
	} catch (error) {
		console.error('选择打印模板时出错:', error);
		throw error; // 重新抛出错误以便上层处理
	}
}
/**
 * 选择备货单小标签模板方法
 */
 async function chooseTemplateXbqTempDialog(temps:any,printType?:string) {
    const titleObj = {
        thqd:'退货清单'
    }
    const {ModeListShows=[],ModeListShowId} =temps
    const title =  `请选择${titleObj[printType] || '小标签'}模板`
    let newTemps = []
    if(printType === 'thqd'){
        ModeListShows.forEach(it => {
            if(ModeListShowId == it.Mode_ListShowId){
                newTemps.unshift(it)
            }else{
                newTemps.push(it)
            }
        });
    }else{
        newTemps = ModeListShows
    }
    const tplIdx = await chooseItem({
        className: 'choose-tpl',
        title,
        items: newTemps.map((tpl, idx) => ({
            name: tpl.ExcodeName,
            id: tpl.Mode_ListShowId.toString(),
        })),
    });

    const selectTpl = tplIdx

    return selectTpl;
}
export {
    chooseTemplateDialog,
    chooseTemplateGroupDialog,
	chooseTemplateXbqTempDialog,
	chooseTemplateByTplIdDialog
};
