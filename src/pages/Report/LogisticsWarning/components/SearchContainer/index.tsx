import React, { useState } from "react";
import { Form, Select, Button } from "antd";
import dayjs from "dayjs";
import { isNumber } from "lodash";
import ShopMultiSelect from "@/components-biz/ShopListSelect/shopMultiSelect";
import InputMulti from "@/components/Input/InputMulti";
import { PACKAGE_STATUS, EXCEPTION_TYPE, STATUS_TYPE, REFUND_TYPE, EVENT_BUS } from "../../contants";
import s from '../../index.module.scss';
import { KddTempListSelect, KddExCodeSelect, KddExNameSelect } from "@/pages/Report/components/kddTempSelect";
import { TradeLogisticsGetLogisticsListRequest } from "@/types/schemas/report/logisticsWarning";
import { getMultiShops } from "@/components-biz/ShopListSelect/shopListUtils";
import Pointer from "@/utils/pointTrack/constants";
import QuicklySearch from "./quicklySearch";
import event from "@/libs/event";
import useGetState from "@/utils/hooks/useGetState";

export interface ISearchContainerProps {
	onSearch: Function;
	onReset: Function;
	isSearch: boolean;
	logisticsConfig:{}
}

const SearchContainer = (props: ISearchContainerProps) => {
	const { onSearch, isSearch, onReset, logisticsConfig } = props;
	const [form] = Form.useForm();
	const [failStatusList, setFailStatusList] = useState([]);
	const [logisticsType, setLogisticsType, getLogisticsType] = useGetState<number|string|null>();
	// const [searchParams, setSearchParams] = useState<Partial<TradeLogisticsGetLogisticsListRequest>>({});

	const getParams = async(values) => {
		let params: Partial<TradeLogisticsGetLogisticsListRequest> = {
			abnormalStatus: values.abnormalStatus || '',
			expressPackageStatusList: values.expressPackageStatusList || '',
			tid: values.tid || '',
			// ptTid: values.ptTid || '',
			ydNo: values.ydNo || '',
			includeFailStatus: values.includeFailStatus,
			failStatusList: values.failStatusList,
			refundStatusType: values.refundStatusType,
			logisticsType: getLogisticsType(),
			dealStatusList: isNumber(values.dealStatusList) ? [values.dealStatusList] : null,
			kdCodeList: values.kdCodeList,
			kdNameList: values.kdNameList,
			// sendTimeStart: values.sendTime ? values.sendTime[0]?.format("YYYY-MM-DD HH:mm:ss") : '',
			// sendTimeEnd: values.sendTime ? values.sendTime[1]?.format("YYYY-MM-DD HH:mm:ss") : '',
		};
		if (values.failStatusList?.includes("all")) {
			params.failStatusList = EXCEPTION_TYPE.map(i => i.value);
		}
		if (params.logisticsType === "all") {
			params.logisticsType = null;
		}
		// if (values.exId) {
		// 	if (values.exId.includes('')) {
		// 		params.exId = '';
		// 	} else {
		// 		params.exId = values.exId.join(',');
		// 	}
		// } else {
		// 	params.exId = '';
		// }
		// if (values.shopInfo) {
		const { plats, plat_sellerIds } = values.shopInfo || {};
		let multiShops = await getMultiShops({ plats, plat_sellerIds });
		multiShops && (params['multiShopS'] = multiShops);
		// }
		return params;
	};
	const failStatusListOnChange = (values) => {
		let failStatusList = values;
		if (values.includes("all")) {
			failStatusList = ["all"];
		}
		setFailStatusList(failStatusList);
		form.setFieldsValue({ failStatusList });

	};

	const quicklySearchOnChange = (v) => {
		setLogisticsType(v);
	};

	const quicklySearchOnClick = (v) => {
		// setLogisticsType(v);
		setTimeout(async() => {
			const values = form.getFieldsValue();
			const params = await getParams(values);
			onSearch(params);
		});
	};

	const onFinish = async(values: any) => {
		const params = await getParams(values);
		const selectShop = values.shopInfo?.plat_sellerIds?.length || values.shopInfo?.plats?.length;
		params.includeDistribution = !selectShop;
		console.log('Success:', values, params);
		onSearch(params);
	};
	const _onReset = () => {
		form.resetFields();
		event.emit(EVENT_BUS.SET_QUICKLY_SEARCH_KET, null);
		setLogisticsType(null);
		onReset?.();
	};
	return (
		<Form
			form={ form }
			name="logisticsWarning"
			labelCol={ { style: { width: 80, marginBottom: 8 } } }
			size="middle"
			className={ s['logistics-warning-form'] }
			onFinish={ onFinish }
			initialValues={ {
				includeFailStatus: true,
				sendTime: [dayjs().subtract(1, 'M').startOf('day'), dayjs().endOf('day')],
			} }
		>
			{/* <Form.Item
				label="发货时间"
				name="sendTime"
			>
				<KdzsDateRangePicker1
					style={ { width: 170, marginBottom: 8 } }
					datePickerKey={ DatePickerKey.logistics_warning }
					cacheQuickChoose
					useServeTime
				/>
			</Form.Item> */}
			<Form.Item label="预警包裹" className={ s["form-inline-wrap"] } >
				<QuicklySearch
					onClick={ quicklySearchOnClick }
					logisticsConfig={ logisticsConfig }
					onChange={ quicklySearchOnChange }
				/>
			</Form.Item>
			<Form.Item label="查询条件" className={ s["form-inline-wrap"] } >
				{/* 选择店铺 */}
				<Form.Item name="shopInfo" className={ s["form-inline-item"] } style={ { width: '348px' } }>
					<ShopMultiSelect
						isHasHandPlat
						style={ { width: '170px' } }
						size="small"
					/>
				</Form.Item>

				<Form.Item name="kdNameList" className={ s["form-inline-item"] } style={ { width: '170px' } }>
					<KddExNameSelect />
				</Form.Item>

				<Form.Item name="kdCodeList" className={ s["form-inline-item"] } style={ { width: '170px' } }>
					<KddExCodeSelect />
				</Form.Item>

				<Form.Item name="expressPackageStatusList" className={ s["form-inline-item"] } style={ { width: '170px' } }>
					<Select
						placeholder="请选择包裹状态"
						size="small"
						mode="multiple"
					>
						{PACKAGE_STATUS.map(item => (
							<Select.Option key={ item.value } value={ item.value } label={ item.label }>
								{item.label}
							</Select.Option>
						))}
					</Select>
				</Form.Item>

				{/* <Form.Item name="abnormalStatus" className={ s["form-inline-item"] } style={ { width: '170px' } }>
					<Select
						placeholder="异常类型"
						size="small"
					>
						{EXCEPTION_TYPE.map(item => (
							<Select.Option key={ item.value } value={ item.value } label={ item.label }>
								{item.label}
							</Select.Option>
						))}
					</Select>
				</Form.Item> */}
				<div className={ s["deal-status-wrap"] }>
					<Form.Item name="includeFailStatus" className={ s["form-inline-item"] } style={ { width: '170px' } }>
						<Select
							placeholder="异常状态"
							size="small"
							className={ s["deal-status-includes"] }
						>
							<Select.Option key={ 1 } value>异常状态（包含）</Select.Option>
							<Select.Option key={ 1 } value={ false }>异常状态（不包含）</Select.Option>
						</Select>
					</Form.Item>
					<Form.Item name="failStatusList" className={ s["form-inline-item"] } style={ { width: '170px' } }>
						<Select
							placeholder="全部"
							size="small"
							mode="multiple"
							className={ s["deal-status-value"] }
							maxTagCount="responsive"
							onChange={ failStatusListOnChange }
						>
							<Select.Option key="all" value="all">全部</Select.Option>
							{EXCEPTION_TYPE.map(item => (
								<Select.Option disabled={ failStatusList.includes("all") } key={ item.value } value={ item.value } label={ item.label }>
									{item.label}
								</Select.Option>
							))}
						</Select>
					</Form.Item>
				</div>
				<Form.Item name="refundStatusType" className={ s["form-inline-item"] } style={ { width: '170px' } }>
					<Select
						placeholder="退款状态"
						size="small"
					>
						{REFUND_TYPE.map(item => (
							<Select.Option key={ item.value } value={ item.value } label={ item.label }>
								{item.label}
							</Select.Option>
						))}
					</Select>
				</Form.Item>
				<Form.Item name="dealStatusList" className={ s["form-inline-item"] } style={ { width: '170px' } }>
					<Select
						placeholder="处理状态"
						size="small"
					>
						{STATUS_TYPE.map(item => (
							<Select.Option key={ item.value } value={ item.value } label={ item.label }>
								{item.label}
							</Select.Option>
						))}
					</Select>
				</Form.Item>

				{/* <Form.Item name="tid" className={ s["form-inline-item"] } style={ { width: '170px' } } >
					<InputMulti maxInputNum={ 200 } placeholder="系统单号" style={ { width: '100%}' } } size="small" />
				</Form.Item>

				<Form.Item name="ptTid" className={ s["form-inline-item"] } style={ { width: '170px' } } >
					<InputMulti maxInputNum={ 200 } placeholder="订单编号" style={ { width: '100%}' } } size="small" />
				</Form.Item> */}

				<Form.Item name="tid" className={ s["form-inline-item"] } style={ { width: '170px' } } >
					<InputMulti maxInputNum={ 200 } placeholder="订单编号" style={ { width: '100%}' } } size="small" />
				</Form.Item>

				<Form.Item name="ydNo" className={ s["form-inline-item"] } style={ { width: '170px' } } >
					<InputMulti maxInputNum={ 200 } placeholder="快递单号" style={ { width: '100%}' } } size="small" />
				</Form.Item>

				<Button className="r-mr-10" htmlType="submit" size="small" type="primary" loading={ isSearch } data-point={ Pointer.物流预警_查询 }>查询</Button>
				<Button size="small" onClick={ _onReset }>重置</Button>

				{/* <Button className="r-ml-6" onClick={ () => { logisticsExport(); } } loading={ exportLoading } size="small" >导出</Button> */}
			</Form.Item>

		</Form>
	);
};

export default SearchContainer;
