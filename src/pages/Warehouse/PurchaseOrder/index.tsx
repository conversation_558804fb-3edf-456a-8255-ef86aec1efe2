import React, { useRef, useState, useEffect, useCallback } from 'react';
import { Button, Space, Modal, Dropdown, Menu, Popover } from 'antd';
import { DownOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { useForm } from 'antd/es/form/Form';
import { ColumnsType } from 'antd/lib/table';
import { observer } from 'mobx-react';
import { useRequest } from 'ahooks';
import dayjs, { Dayjs } from 'dayjs';
import cs from 'classnames';
import _ from 'lodash';
import Icon from '@/components/Icon';
import { SearchTableRefProps } from '@/components/SearchTable/SearchTable';
import { FormItemConfig } from '@/components/SearchTable/FormWidthRow';
import SearchTable from '@/components/SearchTable';
import DatePicker from '@/components/DatePicker';
import EnumSelect from '@/components/Select/EnumSelect';
import WarpText from '@/components-biz/WarpText';
import { GreenButton, BlueButton } from '@/components/Botton';
import { PurchaseListResponse } from "@/types/schemas/warehouse/PurchaseOrder";
import { useStores } from '@/stores/tool';
import { PurchaseListApi, PurchaseDelApi, StockPurchaseExportApi, StockPurchaseBatchGetApi } from '@/apis/warehouse/PurchaseOrder';
import PurchaseOrderDialog from './components/PurchaseOrderDialog';
import NormalLayout from '@/components-biz/layouts/NormalLayout';
import Input from '@/components/Input/InputSearch';
import Pointer from "@/utils/pointTrack/constants";
import sendPoint from "@/utils/pointTrack/sendPoint";
import { getParaName } from "@/utils";
import PrintCenter from '@/print/index';
import message from '@/components/message';
import BatchImportModal from './components/BatchImportModal';
import SupplierSelect from '@/components-biz/SupplierSelect';
import { PrintType } from '../System/Archives';
import { DPPrintSource } from '@/utils/enum/productManage';
import { BQ_PRINT_TYPE } from '@/pages/Trade/constants';
import ExportDropdown, { ExportType } from "@/components/ExportDropdown";
import { downloadCenter } from "@/pages/Index/DownloadCenter/utils";
import { ModulesFunctionEnum } from "@/types/schemas/setting/download";
import s from './index.module.scss';

const { confirm } = Modal;
export enum PutQueryType {
	未入库 = 0,
	部分入库,
	已完成,
	已作废,
}
interface SearchProps {
	date: [Dayjs, Dayjs],
	endCreateTime: string,
	startCreateTime: string,
	inStockStatus: number,
	supplierId: string,
	purchaseName: string,
	// 分页信息自动维护添加
	pageNo: number,
	pageSize: number
}
const PurchaseOrder: React.FC = (props: any) => {
	const [form] = useForm();
	const ref = useRef<SearchTableRefProps>(); // 获取表单对象
	const warehouseStore = useStores('WarehouseStore'); // mobx仓库
	const [addVisible, setAddVisible] = useState(false);
	const [status, setStatus] = useState('add');
	const [purchaseData, setPurchaseData] = useState<object | undefined>();
	const { stockWarnSelectSysSkuIdList } = warehouseStore;
	const [selectPro, setSelectPro] = useState([]);
	const [selectedRows, setSelectedRows] = useState<PurchaseListResponse["data"]["list"]>([]);
	const [selectProItemCount, setSelectProItemCount] = useState(null);
	const [searchParams, setSearchParams] = useState({}); // 当前查询条件
	const [dataSource, setDataSource] = useState([]);

	const { runAsync: batchGetApi, loading } = useRequest(StockPurchaseBatchGetApi, {
		manual: true
	});

	const rowSelection = {
		onChange: (selectedRowKeys: React.Key[], selectedRows) => {
			setSelectedRows(selectedRows);
			console.log(selectedRowKeys, selectedRows, '1');
		},
	};

	// 顶部查询表单
	const FormFieldListV1: FormItemConfig[] = [
		{
			name: 'date',
			children: <DatePicker.RangePicker
				size="small"
				disabledDate={ (current: Dayjs) => {
					if (current > dayjs().subtract(6, 'month') && current < dayjs()) {
						return false;
					} else {
						return true;
					}
				} }
				className={ cs('r-w-full') }
			/>,
		},
		{
			name: 'purchaseNo',
			children: <Input className={ cs('r-l-preWrap') } placeholder="采购单号" />
		},
		{
			name: 'supplierId',
			label: "",
			children: (
				<SupplierSelect
					size="small"
					value="supplierId"
					allowClear
					callBackProp="id"
				/>
			)
		},
		{
			name: 'purchaseStatusList',
			children: <EnumSelect
				placeholder="入库状态"
				enum={ PutQueryType }
				showArrow
				style={ { width: 182 } }
				size="small"
				mode="multiple"
				maxTagCount={ 1 }
			/>
		},
		{
			name: 'purchaseName',
			children: <Input className={ cs('r-l-preWrap') } placeholder="采购单名称" />
		},
		{
			name: 'sysItemAlias',
			children: <Input className={ cs('r-l-preWrap') } placeholder="货品简称" />
		},
		{
			name: 'sysSkuOuterId',
			children: <Input className={ cs('r-l-preWrap') } placeholder="货品规格编码" />
		},
		{
			name: 'memo',
			children: <Input className={ cs('r-l-preWrap') } placeholder="备注" />
		}
	];

	// 采购单表格定义
	const columns: ColumnsType<unknown> = [
		{
			width: 30,
			align: 'center',
			key: 'index',
			render: (text, record: PurchaseListResponse["data"]["list"][0], index) => {
				return index + 1;
			}
		},
		{
			key: 'purchaseNo',
			title: '采购单号',
			width: 140,
			dataIndex: 'purchaseNo',
			render: (text, row) => {
				return <WarpText>{text}</WarpText>;
			}
		},
		{
			key: 'purchaseName',
			title: '采购单名称',
			width: 140,
			dataIndex: 'purchaseName',
			render: (text, row) => {
				return <WarpText>{text}</WarpText>;
			}
		},
		{
			title: '供应商',
			width: 100,
			dataIndex: 'supplierName',
			key: 'supplierName',
			render: (text, row) => {
				return <WarpText>{text}</WarpText>;
			}
		},
		{
			title: '入库状态',
			dataIndex: 'purchaseStatus',
			key: 'purchaseStatus',
			width: 80,
			render: (obj, record: PurchaseListResponse["data"]["list"][0], index) => {
				return PutQueryType[obj];
			},
		},
		{
			title: '采购数量',
			width: 80,
			dataIndex: 'purchaseCount',
			key: 'purchaseCount'
		},
		{
			title: '采购金额',
			width: 80,
			dataIndex: 'purchaseTotalAmount',
			key: 'purchaseTotalAmount',
			render: (res) => {
				if (res.includes('***')) {
					return (
						<Popover content={ <div>无该数据查看权限</div> }>  
							{res} 
						</Popover>
					);  
				} else {
					return <div>{res}</div>;
				}
				
			},
		},
		{
			title: '运费',
			width: 80,
			dataIndex: 'carriage',
			key: 'carriage'
		},
		{
			title: '其他金额',
			width: 80,
			dataIndex: 'otherCost',
			key: 'otherCost'
		},
		{
			title: '备注',
			width: 80,
			dataIndex: 'memo',
			key: 'memo'
		},
		{
			title: '下单时间',
			width: 140,
			dataIndex: 'createTime',
			key: 'createTime',
			render: (text, record: PurchaseListResponse["data"]["list"][0], index) => {
				return dayjs(text).format('YYYY-MM-DD HH:mm:ss');
			}
		},
		{
			title: '创建人',
			width: 100,
			dataIndex: 'createUser',
			key: 'createUser'
		},
		{
			title: '操作',
			align: 'center',
			dataIndex: 'option',
			key: 'option',
			width: 240,
			render: (text, record: PurchaseListResponse["data"]["list"][0], index) => {
				const { id, purchaseStatus, purchaseNo = "" } = record;
				const type = { 0: 'unPut', 1: 'partPut', 2: 'allPut' }[purchaseStatus];
				return ((
					<Space>
						<GreenButton
							size="small"
							type="primary"
							data-point={ Pointer[purchaseStatus === 2 ? '采购_采购单_修改成本' : `采购_采购单_编辑`] }
							onClick={ () => { openDialog(type, record); } }
							style={ { width: 82, whiteSpace: 'pre' } }
							disabled={ purchaseStatus == 3 }
						>
							编      辑
						</GreenButton>
						<BlueButton
							size="small"
							type="primary"
							data-point={ Pointer['采购_采购单_再次下单'] }
							onClick={ () => { openDialog('copy', record); } }
						>
							再次下单
						</BlueButton>
						<Button
							size="small"
							type="primary"
							data-point={ Pointer['采购_采购单_导出'] }
							onClick={ () => {
								exportExcel(id, purchaseNo);
							} }
						>导出
						</Button>
					</Space>
				));
			},
		}
	];


	// 返回值重构
	const responseAdapter = (info: any) => {
		setDataSource(info.list || []);
		return {
			total: info.total,
			list: info.list
		};
	};

	const openDialog = (type: string, purchaseData?: object) => {
		setStatus(type);
		setPurchaseData(purchaseData);
		setAddVisible(true);
	};

	const batchDelete = async() => {
		if (selectedRows.length == 0) {
			message.warn('请先选择');
			return;
		}
		confirm({
			centered: true,
			title: '您确定要作废该采购单吗？',
			icon: <ExclamationCircleOutlined />,
			content: '作废入库单后无法撤回，入库单作废后已入库的库存会返还。',
			onOk: async() => {
				await PurchaseDelApi({ idList: selectedRows.map(i => i.id) });
				setSelectedRows([]);
				message.success('批量作废成功');
				ref.current.submit();
			}
		});

	};

	const batchGetDetail = async() => {
		const res = await batchGetApi({
			idList: selectedRows.map(i => i.id),
		});
		let printData = _.cloneDeep(selectedRows);

		console.time('printData');
		printData.forEach((item, index) => {
			let temp = res.find(detail => detail.id === item.id);
			if (temp) {
				// 这里不处理sysItemInfo
				const purchaseItemDTOS = item?.purchaseItemDTOS?.map(d => {
					let p = temp.purchaseItemDTOS.find(i => i.sysItemId == d.sysItemId && i.sysSkuId == d.sysSkuId) || {};
					return {
						...d,
						...p,
					};
				});
				printData[index] = {
					...item,
					...temp,
					purchaseItemDTOS
				};
			}
		});
		console.timeEnd('printData');
		return printData;
	};

	const onPrintPuchaseOrder = async() => {
		sendPoint(Pointer.采购单_打印采购单);
		// console.log(selectedRows, 'selectedRows');
		if (!selectedRows.length) {
			message.warn('请选择要打印的采购单');
			return;
		}
		message.loading({
			content: '正在加载中',
			key: 'batchPrintCGD',
			duration: 0,
		});
		try {
			const printData = await batchGetDetail();
			console.log(printData, 'printData');
			message.destroy('batchPrintCGD');
			PrintCenter.batchPrintPurchaseOrder({
				orderList: printData,
				printType: 'cgd'
			});
		} catch (error) {
			message.destroy('batchPrintCGD');
		}
	};

	const exportExcel = async(id: number, name: string) => {
		// StockPurchaseExportApi({ id }).then((res: any) => {
		// 	let blob = new Blob([res]);
		// 	const aLink = document.createElement('a');
		// 	aLink.download = `${name}-${dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss')}.xlsx`;
		// 	// aLink.download = `采购单号-${dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss')}.xlsx`;
		// 	aLink.href = window.URL.createObjectURL(blob);
		// 	aLink.click();
		// }).catch((error) => { });
		// 修改为使用下载中心
		await downloadCenter({
			requestParams: { id },
			fileName: `采购单`,
			module: ModulesFunctionEnum.下载中心_采购单
		});
	};

	const onPrintGoodsDpOrTm = async(type) => {
		if (!selectedRows.length) {
			message.warn('请先选择采购单');
			return;
		}
		message.loading('正在加载中');
		try {
			const printData = await batchGetDetail();
			console.log(printData, 'onPrintGoodsDp');
			message.destroy();
			let printList = [];
			printData.forEach((item) => {
				const { supplierName = "",
					stall = "",
					market = "",
					storageAddrProvince = "",
					storageAddrCity = "",
					storageAddrDistrict = "",
					storageAddr = "",
				} = item;
				item.purchaseItemDTOS?.forEach((purchaseItem) => {
					const {
						itemNo = "",
						barCode = "",
						skuOuterId = "",
						outerId = "",
						brandName = "",
						classifyName = "",
					} = purchaseItem;

					// 这里要用到更多的货品信息
					const itemInfo = purchaseItem?.sysItemInfo ? JSON.parse(purchaseItem.sysItemInfo) : {};
					let sysItem:any = {
						...itemInfo,
						...purchaseItem,
					};

					// 支持自定义属性
					sysItem?.customAttributesList?.forEach((item) => {
						if (item.key && item.value) {
							sysItem[item.key] = item.value;
						}
					});

					sysItem.classifyText = classifyName;
					sysItem.supplierName = supplierName;
					sysItem.stall = stall;
					sysItem.market = market;
					sysItem.brandName = brandName;
					// 货品编码
					sysItem.outerId = outerId;
					// 供应商地址
					sysItem.supplierAddress = `${storageAddrProvince}${storageAddrCity}${storageAddrDistrict}${storageAddr}`;
					// 货品条形码 货品条形码；（如没有配置货号，不生成条形码）
					sysItem.sysBarCode = barCode ? `${barCode}` : "";
					// 货号条形码 货号；（如没有配置货号，不生成条形码）
					sysItem.itemNoBarCode = itemNo ? `${itemNo}` : "";
					// 货品规格编码条形码 货品规格编码
					sysItem.sysSkuBarCode = `${skuOuterId}`;
					printList.push(sysItem);
				});
			});
			console.log('printList::', printList, type);
			PrintCenter.batchPrintGoodsBarCodeOrTag({
				orderList: printList,
				printType: type,
				source: DPPrintSource.采购入库,
			});
		} catch (error) {
			message.destroy();
		}

	};

	const handleExportSelect = async(key: string) => {
		console.log("用户选择了导出类型:", key);

		let requestParams = {
			
		};
		
		switch (key) {
			case ExportType.导出选中数据:
				if (!selectedRows?.length) {
					message.error("请先选择需要导出的数据");
					return;
				} else {
					requestParams = {
						...requestParams,
						idList: selectedRows.map(i => i.id),
						needPic: false
					};
				}
				
				break;
			case ExportType.导出查询结果:
				requestParams = {
					...requestParams,
					...searchParams,
					needPic: false
				};
				break;
			case ExportType['导出选中数据(带图片)']:
				if (!selectedRows?.length) {
					message.error("请先选择需要导出的数据");
					return;
				} else {
					requestParams = {
						...requestParams,
						idList: selectedRows.map(i => i.id),
						needPic: true
					};
				}
				break;
			case ExportType['导出当前页(带图片)']:
				requestParams = {
					...requestParams,
					...searchParams,
					needPic: true
				};
				
				break;
			default:
				break;
		}
		
		await downloadCenter({
			requestParams,
			fileName: `采购单_`,
			module: ModulesFunctionEnum.采购单,
		});
	};

	const expandContext = (
		<div className="r-flex">
			<Button type="primary" onClick={ () => { openDialog('add'); } } >
				新建采购订单
			</Button>
			<BatchImportModal type="采购单" />
			<Dropdown
				overlay={ (
					<Menu>
						<Menu.Item
							key="onPrintPuchaseOrder"
							onClick={ onPrintPuchaseOrder }
						>
							打印采购单
						</Menu.Item>
						<Menu.Item
							key="onPrintGoodsDp"
							onClick={ () => { sendPoint(Pointer.采购_采购单_打印商品吊牌); onPrintGoodsDpOrTm(PrintType.吊牌); } }
						>
							打印商品吊牌
						</Menu.Item>
						<Menu.Item
							key="onPrintGoodsTm"
							onClick={ () => { sendPoint(Pointer.采购_采购单_打印商品条码); onPrintGoodsDpOrTm(PrintType.条码); } }
						>
							打印商品条码
						</Menu.Item>
					</Menu>
				) }
				placement="bottomLeft"
			>
				<Button className="r-ml-8">
					批量打印<DownOutlined />
				</Button>
			</Dropdown>

			<Button className="r-ml-8" onClick={ batchDelete } >
				批量作废
			</Button>

			<Button
				className="r-pointer r-ml-8"
				data-point={ Pointer.采购单_打印设置 }
				onClick={ () => { PrintCenter.showTemplateMain({ printType: 'cgd' }); } }
			>采购单打印设置
			</Button>

			<span className="r-ml-8">
				<ExportDropdown onSelect={ handleExportSelect } />
			</span>

		</div>
	);
	const fetchListRequest = (info: SearchProps) => {
		if (info.date) {
			info.startCreateTime = dayjs(info.date[0]).format('YYYY-MM-DD 00:00:00');
			info.endCreateTime = dayjs(info.date[1]).format('YYYY-MM-DD 23:59:59');
		}

		setSearchParams(info);

		return PurchaseListApi({ ...info });
	};

	useEffect(() => {
		PrintCenter.getBqTempList(BQ_PRINT_TYPE.采购单);
		PrintCenter.getBqTempList(BQ_PRINT_TYPE.吊牌);
	}, []);
	useEffect(() => {
		let paramsObj = getParaName(window.location.hash);
		const todo = new URL(location.href).searchParams.get('todo') ?? '';
		// 点击「备货单-“生成采购单”跳转到「采购单」的业务逻辑
		if (todo === 'generatePurchaseOrder') {
			openDialog('generate');
		}
	}, [window.location.hash]);

	useEffect(() => {
		if (stockWarnSelectSysSkuIdList?.length) {
			let sysSkuIds = [];
			let itemCountMap = {};
			stockWarnSelectSysSkuIdList.forEach(item => {
				sysSkuIds.push(item.sysSkuId);
				itemCountMap[item.sysSkuId] = item.itemCount;
			});
			sysSkuIds = stockWarnSelectSysSkuIdList.map((item) => item.sysSkuId);
			console.log("itemCountMap:", itemCountMap);
			setAddVisible(true);
			setSelectPro(sysSkuIds);
			setSelectProItemCount(itemCountMap);
		}
	}, [stockWarnSelectSysSkuIdList]);
	
	const closePurchaseOrderDialog = () => {
		setAddVisible(false);
		setSelectPro([]);
		warehouseStore.setSelectPro([]);
	};

	const getSelectedContent = useCallback(() => {
		let count = selectedRows?.length || 0;
		let totalPriceCount = 0;
		selectedRows?.forEach(row => {
			totalPriceCount += (Number?.(row.purchaseTotalAmount) || 0);
		});
		return (
			<div className={ s.selectedContent }>
				<div className={ s.selectedContentQuickCheck }>
					<div className={ s.selectedCount }>
						<Icon style={ { fontSize: 24, color: '#FAAD14' } } type="tishi-fill" />
						<div>本页已勾选 <span className="r-c-FF4D4F">{count}</span> 条数据，共计 <span className="r-c-FF4D4F">{totalPriceCount.toFixed(2)}</span> 元</div>
					</div>
				</div>
			</div>
		);
	}, [selectedRows, dataSource]);

	return (
		<NormalLayout>
			<SearchTable<PurchaseListResponse["data"]["list"][0]>
				pageSizeId="purchaseOrderTable"
				ref={ ref } // 引用
				form={ form } //
				fetchData={ fetchListRequest } // 接口请求
				responseAdapter={ responseAdapter } // 返回值适配
				searchBtnPoint={ Pointer['采购_采购单_查询'] }
				searchBtnText="查询"
				resetBtnText="重置"
				showSearch
				rowFormConfig={ { // 表单配置
					formList: FormFieldListV1,
					defaultParams: {
						date: [dayjs().subtract(30, 'day'), dayjs()]
					}, // 查询表单设置初始值
					colProps: { // 表单列配置
						// span: 4
					},
					formItemProps: {},
				} }
				baseTableConfig={ { // 表格基础设置
					noGap: true,
					innerTableStyle: { paddingTop: 0 },
					rowKey: 'id',
					columns, // 列配置
					scroll: {},
					pagination: false,
					paginationWrapStyle: { position: 'fixed', bottom: 0, width: '100%' },
					expandContext, // 自定义区域
					expandContextStyle: { marginBottom: 0 },
					rowSelection: {
						selectedRowKeys: selectedRows.map(i => i.id),
						type: 'checkbox',
						...rowSelection,
					},
					showSelectedCount: getSelectedContent(),
				} }
			/>
			{addVisible && (
				<PurchaseOrderDialog
					selectPro={ selectPro }
					selectProItemCount={ selectProItemCount }
					status={ status }
					purchaseData={ purchaseData }
					isModalVisible={ addVisible }
					afterCancel={ () => { closePurchaseOrderDialog(); } }
					afterConfirm={ () => { closePurchaseOrderDialog(); ref.current.submit(); } }
				/>
			)}
		</NormalLayout>

	);
};
export default observer(PurchaseOrder);
