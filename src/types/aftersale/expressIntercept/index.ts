/**
 * ReqSelectLogisticsInterceptWithPageDTO :ReqSelectLogisticsInterceptWithPageDTO
 */
export interface SelectLogisticsInterceptListRequest {
  /**
   * 用户id
   */
  userId?: number;
  /**
   * 用户版本
   */
  userVersion?: number;
  /**
   * 售后申请时间开始
   */
  refundApplyTimeStart?: string;
  /**
   * 售后申请时间结束
   */
  refundApplyTimeEnd?: string;
  /**
   * 申请拦截时间开始
   */
  interceptApplyTimeStart?: string;
  /**
   * 申请拦截时间结束
   */
  interceptApplyTimeEnd?: string;
  /**
   * 拦截状态多选，枚举：RefundLogisticsInterceptStatusEnum ,Integer
   */
  interceptStatusList?: number[];
  /**
   * 拦截超时时间枚举
   */
  timeOutSearchEnum?: {
    [k: string]: any;
  };
  /**
   * 拦截超时时间
   */
  interceptOutTime?: string;
  /**
   * 退款状态
   */
  refundStatus?: number;
  /**
   * 退款id
   */
  refundId?: string;
  /**
   * String
   */
  refundIdList?: string[];
  /**
   * 拦截申请人
   */
  interceptUserName?: string;
  /**
   * 快递公司code
   */
  logisticsCompanyCode?: string;
  /**
   * 发货单号,隔开
   */
  invoiceNo?: string;
  /**
   * String
   */
  invoiceNoList?: string[];
  /**
   * 多平台店铺查询 ,MultiShopDTO
   */
  multiShopS?: {
    sellerId?: number;
    platform?: string;
    [k: string]: any;
  }[];
  /**
   * 售后拦截tab展示栏
   */
  interceptTabShowType?: {
    [k: string]: any;
  };
  interceptTabShowTypeStr?: string;
  createTimeStart?: string;
  createTimeEnd?: string;
  pageNo?: number;
  pageSize?: number;
  [k: string]: any;
}

/**
 * ResponseBody<WebPageResponse<RspSelectLogisticsInterceptWithPageDTO>> :ResponseBody
 */
export interface SelectLogisticsInterceptListResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * WebPageResponse
   */
  data?: {
    /**
     * 第几页
     */
    pageNo?: number;
    /**
     * 一页多少条
     */
    pageSize?: number;
    /**
     * 总页数
     */
    pageCount?: number;
    /**
     * 总数
     */
    total?: number;
    /**
     * 列表 ,T
     */
    list?: {
      /**
       * id
       */
      id?: number;
      /**
       * 用户ID
       */
      userId?: number;
      /**
       * 平台类型
       */
      platform?: string;
      /**
       * 店铺ID
       */
      sellerId?: string;
      /**
       * 店铺名称
       */
      sellerNick?: string;
      /**
       * 发货单号
       */
      invoiceNo?: string;
      /**
       * 发货快递公司code
       */
      invoiceLogisticsCode?: string;
      /**
       * 发货快递公司name
       */
      invoiceLogisticsName?: string;
      /**
       * 发货运单号主物流状态
       */
      invoiceStatus?: string;
      /**
       * 发货运单号子物流状态
       */
      invoiceSubStatus?: string;
      /**
       * 发货物流详情
       */
      invoiceLogisticsDetail?: string;
      /**
       * 快递拦截状态
       */
      interceptStatus?: number;
      /**
       * 快递拦截状态中文
       */
      interceptStatusDesc?: string;
      /**
       * 拦截申请时间
       */
      interceptApplyTime?: string;
      /**
       * 拦截申请人
       */
      interceptApplyPeople?: string;
      /**
       * 售后申请时间
       */
      refundCreated?: string;
      /**
       * 退款状态
       */
      refundStatus?: number;
      /**
       * 退款状态说明
       */
      refundStatusDesc?: string;
      /**
       * 售后编码
       */
      refundId?: string;
      /**
       * 订单编号
       */
      tid?: string;
      /**
       * 添加时间
       */
      gmtCreate?: string;
      [k: string]: any;
    }[];
    /**
     * 提示条件类型1-订单编号2-售后单号3-发货运单号4-退货运单号
     */
    tipQueryBackType?: number;
    /**
     * 返回提示未查询到的条件 ,String
     */
    missedList?: string[];
    [k: string]: any;
  };
  [k: string]: any;
}


/**
 * ReqRefundLogisticsInterceptLabCountDTO :ReqRefundLogisticsInterceptLabCountDTO
 */
export interface SelectLogisticsInterceptTabCountRequest {
  /**
   * 用户id
   */
  userId?: number;
  /**
   * 开始时间
   */
  startTime?: string;
  /**
   * 结束时间
   */
  endTime?: string;
  /**
   * 多平台店铺查询 ,MultiShopDTO
   */
  multiShopS?: {
    sellerId?: number;
    platform?: string;
    [k: string]: any;
  }[];
  [k: string]: any;
}


/**
 * ResponseBody<RspSelectLogisticsInterceptLabCountDTO> :ResponseBody
 */
export interface SelectLogisticsInterceptTabCountResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * RspSelectLogisticsInterceptLabCountDTO
   */
  data?: {
    /**
     * 待拦截
     */
    waitInterceptNum?: string;
    /**
     * 已发起拦截
     */
    alreadyInterceptNum?: string;
    /**
     * 拦截成功
     */
    interceptSuccessNum?: string;
    [k: string]: any;
  };
  [k: string]: any;
}

/**
 * ReqAddRefundLogisticsInterceptDTO :ReqAddRefundLogisticsInterceptDTO
 */
export interface AddLogisticsInterceptRequest {
  /**
   * 售后单号
   */
  refundId?: string;
  /**
   * 快递单号
   */
  invoiceNo?: string;
  /**
   * 发货快递公司code
   */
  invoiceLogisticsCode?: string;
  /**
   * 发货快递公司name
   */
  invoiceLogisticsName?: string;
  /**
   * 拦截状态
   */
  interceptStatus?: number;
  [k: string]: any;
}


/**
 * ResponseBody<Integer> :ResponseBody
 */
export interface AddLogisticsInterceptResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * data
   */
  data?: number;
  [k: string]: any;
}

/**
 * ReqLogisticsInterceptMarkParamsDTO :ReqLogisticsInterceptMarkParamsDTO
 */
export interface MarkLogisticsInterceptStatusRequest {
  /**
   * ids ,Long
   */
  ids?: number[];
  /**
   * 处理方式1-标记申请拦截2-取消标记申请拦截3-标记拦截成功4-取消标记拦截成功
   */
  handleWay?: number;
  [k: string]: any;
}

/**
 * ResponseBody<BatchCloseRefundResDto> :ResponseBody
 */
export interface MarkLogisticsInterceptStatusResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * BatchCloseRefundResDto
   */
  data?: {
    /**
     * 成功数
     */
    successNum?: number;
    /**
     * 失败数
     */
    failedNum?: number;
    /**
     * 失败结果集合 ,BatchResult
     */
    batchResults?: {
      operationId?: string;
      success?: boolean;
      errorCode?: number;
      errorMessage?: string;
      [k: string]: any;
    }[];
    [k: string]: any;
  };
  [k: string]: any;
}

/**
 * ReqLogisticsInterceptMarkParamsDTO :ReqLogisticsInterceptMarkParamsDTO
 */
export interface BatchDelLogisticsInterceptRequest {
  /**
   * ids ,Long
   */
  ids?: number[];
  /**
   * 处理方式1-标记申请拦截2-取消标记申请拦截3-标记拦截成功4-取消标记拦截成功
   */
  handleWay?: number;
  [k: string]: any;
}

/**
 * ResponseBody<Integer> :ResponseBody
 */
export interface BatchDelLogisticsInterceptResponse {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * data
   */
  data?: number;
  [k: string]: any;
}

/**
 * ReqSelectLogisticsInterceptWithPageDTO :ReqSelectLogisticsInterceptWithPageDTO
 */
export interface SelectLogisticsInterceptListV2Request {
  /**
   * 用户id
   */
  userId?: number;
  /**
   * 用户版本
   */
  userVersion?: number;
  /**
   * 售后申请时间开始
   */
  refundApplyTimeStart?: string;
  /**
   * 售后申请时间结束
   */
  refundApplyTimeEnd?: string;
  /**
   * 申请拦截时间开始
   */
  interceptApplyTimeStart?: string;
  /**
   * 申请拦截时间结束
   */
  interceptApplyTimeEnd?: string;
  /**
   * 添加时间开始
   */
  gmtCreateTimeStart?: string;
  /**
   * 添加时间结束
   */
  gmtCreateTimeEnd?: string;
  /**
   * 拦截状态多选，枚举：RefundLogisticsInterceptStatusEnum ,Integer
   */
  interceptStatusList?: number[];
  /**
   * 拦截超时时间枚举
   */
  timeOutSearchEnum?: {
    [k: string]: any;
  };
  /**
   * 拦截超时时间
   */
  interceptOutTime?: string;
  /**
   * 退款状态
   */
  refundStatus?: number;
  /**
   * 退款id
   */
  refundId?: string;
  /**
   * String
   */
  refundIdList?: string[];
  /**
   * 拦截申请人
   */
  interceptUserName?: string;
  /**
   * 快递公司code
   */
  logisticsCompanyCode?: string;
  /**
   * 快递公司code集合 ,String
   */
  logisticsCompanyCodeList?: string[];
  /**
   * 发货单号,隔开
   */
  invoiceNo?: string;
  /**
   * String
   */
  invoiceNoList?: string[];
  /**
   * 多平台店铺查询 ,MultiShopDTO
   */
  multiShopS?: {
    sellerId?: number;
    platform?: string;
    [k: string]: any;
  }[];
  /**
   * 页面是否实际勾选平台店铺查询0-否1-是
   */
  shopIsActualSelect?: number;
  isQueryHandCreate?: boolean;
  /**
   * 售后拦截tab展示栏
   */
  interceptTabShowType?: {
    [k: string]: any;
  };
  interceptTabShowTypeStr?: string;
  createTimeStart?: string;
  createTimeEnd?: string;
  /**
   * NO_STATUS(0,"无状态"),,WAIT_ACCEPT(1,"待揽收"),,ACCEPT(2,"已揽收"),,TRANSPORT(3,"运输中"),,DELIVERING(4,"派送中"),,FAILED(5,"包裹异常"),,REJECT(6,"拒签"),,AGENT_SIGN(7,"已代签收"),,SIGN(8,"已签收")
   */
  invoiceStatus?: string;
  /**
   * 1有买家留言2有卖家备注3有买家留言+有卖家备注4没有留言且没有备注5红6黄7绿8蓝色9紫色10有留言或备注11无留言12无备注13无留言或无备注0-灰旗,橙色-14、浅蓝-15、浅粉-16、深绿-17、桃红-18
   */
  flagValue?: string;
  hasGrayFlag?: boolean;
  /**
   * 1,2_1表示包含红黄旗从自定义备注接口找0表示不包含1表示包含
   */
  flagSelValue?: string;
  /**
   * 旗帜,1：红2：黄3：绿4：蓝5：紫橙色-14、浅蓝-15、浅粉-16、深绿-17、桃红-18,传多个,分割
   */
  sellerFlag?: string;
  /**
   * 0表示不包含1表示包含和sellerFlag联立用
   */
  sellerFlagContains?: number;
  /**
   * 留言内容(多个用,隔开)
   */
  buyerMessage?: string;
  /**
   * 留言备注内容(多个用,隔开)
   */
  sellerMemo?: string;
  /**
   * 买家留言列表 ,String
   */
  buyerMessageList?: string[];
  /**
   * 卖家备注列表 ,String
   */
  sellerMemoList?: string[];
  /**
   * 旗帜,1：红2：黄3：绿4：蓝5：紫
   */
  sellerFlagFinalQuery?: string;
  /**
   * 是否需要查询线下备注
   */
  needFindLocalContent?: boolean;
  /**
   * 线下备注
   */
  localContent?: string;
  /**
   * 导出版本：v2
   */
  exportVersion?: string;
  /**
   * 页码
   */
  pageNo?: number;
  /**
   * 每页大小
   */
  pageSize?: number;
  [k: string]: any;
}

/**
 * ResponseBody<WebPageResponse<LogisticsInterceptRecordDTO>> :ResponseBody
 */
export interface SelectLogisticsInterceptListV2Response {
  success?: boolean;
  errorCode?: number;
  errorMessage?: string;
  /**
   * WebPageResponse
   */
  data?: {
    /**
     * 第几页
     */
    pageNo?: number;
    /**
     * 一页多少条
     */
    pageSize?: number;
    /**
     * 总页数
     */
    pageCount?: number;
    /**
     * 总数
     */
    total?: number;
    /**
     * 列表 ,T
     */
    list?: {
      /**
       * 发货单号
       */
      invoiceNo?: string;
      /**
       * 发货快递公司code
       */
      invoiceLogisticsCode?: string;
      /**
       * 发货快递公司name
       */
      invoiceLogisticsName?: string;
      /**
       * 发货运单号主物流状态
       */
      invoiceStatus?: string;
      /**
       * 拦截申请时间
       */
      interceptApplyTime?: string;
      /**
       * 拦截申请人
       */
      interceptApplyPeople?: string;
      /**
       * 详情列表 ,RspSelectLogisticsInterceptWithPageDTO
       */
      detailList?: {
        /**
         * id
         */
        id?: number;
        /**
         * 用户ID
         */
        userId?: number;
        /**
         * 平台类型
         */
        platform?: string;
        /**
         * 店铺ID
         */
        sellerId?: string;
        /**
         * 店铺名称
         */
        sellerNick?: string;
        /**
         * 发货单号
         */
        invoiceNo?: string;
        /**
         * 发货快递公司code
         */
        invoiceLogisticsCode?: string;
        /**
         * 发货快递公司name
         */
        invoiceLogisticsName?: string;
        /**
         * 发货运单号主物流状态
         */
        invoiceStatus?: string;
        /**
         * 发货运单号子物流状态
         */
        invoiceSubStatus?: string;
        /**
         * 发货物流详情
         */
        invoiceLogisticsDetail?: string;
        /**
         * 最新物流信息时间
         */
        invoiceLastTime?: string;
        /**
         * 快递拦截状态
         */
        interceptStatus?: number;
        /**
         * 快递拦截状态中文
         */
        interceptStatusDesc?: string;
        /**
         * 拦截申请时间
         */
        interceptApplyTime?: string;
        /**
         * 拦截申请人
         */
        interceptApplyPeople?: string;
        /**
         * 售后申请时间
         */
        refundCreated?: string;
        /**
         * 退款状态
         */
        refundStatus?: number;
        /**
         * 退款状态说明
         */
        refundStatusDesc?: string;
        /**
         * 售后编码
         */
        refundId?: string;
        /**
         * 订单编号
         */
        tid?: string;
        /**
         * 平台订单编号
         */
        ptTid?: string;
        /**
         * 添加时间
         */
        gmtCreate?: string;
        /**
         * 平台自动拦截标识0-无1-平台自动拦截
         */
        logisticsInterceptMark?: number;
        /**
         * 拦截费用出资方
         */
        interceptInvestorStr?: string;
        /**
         * 快递模版id
         */
        exId?: number;
        /**
         * 快递模版code
         */
        exCode?: string;
        /**
         * 快递模版名称
         */
        exName?: string;
        /**
         * 卖家旗帜
         */
        sellerFlag?: string;
        /**
         * 卖家备注
         */
        sellerMemo?: string;
        /**
         * 插旗标签
         */
        sellerFlagTag?: string;
        /**
         * 线下备注
         */
        localContent?: string;
        /**
         * 买家留言
         */
        buyerMessage?: string;
        /**
         * 线下备注图片 ,String
         */
        localContentPicList?: string[];
        /**
         * 售后标签 ,String
         */
        refundTagList?: string[];
        /**
         * 售后金额
         */
        refundFee?: string;
        /**
         * 退货运费金额
         */
        freight?: string;
        /**
         * 是否无主件
         */
        isNoTradeMess?: boolean;
        /**
         * 分销推送
         */
        distributorPushFlag?: boolean;
        /**
         * 售后单来源
         */
        afterSaleSource?: string;
        [k: string]: any;
      }[];
      [k: string]: any;
    }[];
    isSuccess?: boolean;
    errorMsg?: string;
    [k: string]: any;
  };
  [k: string]: any;
}

