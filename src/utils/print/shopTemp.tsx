
import React from "react";
import ReactDOM from "react-dom";
import message from "antd/lib/message";
import { <PERSON><PERSON>, Modal } from "antd";
import { WarningOutlined } from "@ant-design/icons";
import { PLAT_SPH } from "@/constants";
import Icon from "@/components/Icon";
import { templateType4PlatformTypeEnum } from "@/pages/Trade/constants";
import { preCheckTradeLimitApi } from "@/apis/trade";
import { pageLoading } from '@/components/PageLoading';
import sendPoint from "../pointTrack/sendPoint";
import Pointer from "../pointTrack/constants";
import AdditionalPurchaseDrawer from "@/components-biz/PayDrawer/AdditionalPurchaseDrawer";
import { showPayDrawer } from "@/components-biz/PayDrawer";
import { PAY_TYPE } from "@/components-biz/PayDrawer/interface";
import Flex from "@/components/Flex";
import { session } from "@/libs/db";

interface IShopParams {
	platform: string,
	sellerId: string,
	[k: string]: any
}

// sphUserTemplateSyn  同步模板
export const updateShopTemp = async(shops: IShopParams[]) => {
	const sphShops = shops?.filter(i => i.platform == PLAT_SPH);
	if (sphShops.length) {
		const params = sphShops.map(i => ({
			kddType: templateType4PlatformTypeEnum.sph,
			sellerId: shops[0]?.sellerId
		}));
		const res = await window.printAPI?.sphUserTemplateSyn(params);
		console.log('res: ', res);
		return res;
	}
};

// sphUserTemplateCheck  校验模板
export const checkShopTemp = async(temp) => {
	if (temp && temp.KddType == templateType4PlatformTypeEnum.sph) {
		console.log('temp: ', temp);
		console.log('temp check params', temp.ExCode, temp.platformUserTemplate, temp.sellerId);
		const res = await window.printAPI?.sphUserTemplateCheck(temp.ExCode, temp.platformUserTemplate, temp.sellerId);
		console.log('res: ', res);
		if (!res) {
			message.warn('平台模版已被删除，请刷新后再试');
			return Promise.reject();
		}
	}
};

const showTips = ({ isSeeDetail, tradeLimitNum, totalConsumeNum, openLimit, isLimit }) => {
	let insertDiv = document.createElement('div');
	insertDiv.id = 'limitDomId';
	const userInfo = session.get('erp_userInfo');
	console.log('userInfo', userInfo);
	document.body.appendChild(insertDiv);
	ReactDOM.render(
		<Modal
			title={ isSeeDetail && !isLimit ? "单量统计" : "单量不足提醒" }
			visible
			onCancel={ () => {
				document.body.removeChild(insertDiv);
			} }
			onOk={ () => {
				document.body.removeChild(insertDiv);
			} }
		>
			<div>
				<div className="r-flex r-ta-c r-pd-16" style={ { background: "#f0f0f0" } }>
					<div style={ { flex: 1 } }>
						<div className="r-c-999">当前账号订单数量上限</div>
						<div className="r-fs-24">{(+tradeLimitNum)?.toLocaleString()}</div>
					</div>
					<div style={ { flex: 1 } }>
						{
							isSeeDetail && !isLimit ? (
								<div>当前剩余</div>
							) : (
								<div style={ { color: "#f00" } }><WarningOutlined className="r-mr-4" style={ { color: "ff0" } } />已超出</div>
							)
						}
						<div className="r-fs-24">{
							isSeeDetail && !isLimit ? (
								<div>{(tradeLimitNum - totalConsumeNum)?.toLocaleString()}</div>
							) : (
								<div style={ { color: "#f00" } }>{(totalConsumeNum - tradeLimitNum)?.toLocaleString()}</div>
							)
						}
						</div>
					</div>
				</div>
				<div className="r-flex r-mt-16 r-pd-16" style={ { border: "1px solid #f0f0f0", alignItems: "center", justifyContent: 'space-between' } }>
					<Flex>
						<div className=" r-mr-16"><Icon type="lianxikefu" className="r-c-666" style={ { fontSize: "24px" } } /></div>
						<div>
							<div className="r-fs-16 r-fw-b r-c-black">联系销售</div>
							<div className="r-c-999 r-mt-8">如需提高更多单量上限，请联系销售或直接购买</div>
						</div>
					</Flex>
					{
						[1, 2, 3].includes(userInfo?.level)
							? <Button type="link" style={ { padding: 0 } } onClick={ () => showPayDrawer({ type: PAY_TYPE.打印订单数 }) }>前往订购</Button>
							: null
					}
				</div>
			</div>
		</Modal>,
		document.getElementById("limitDomId")
	);
};

export const preCheckPrintLimit = async(isSeeDetail = false) => {
	const { tradeLimitNum, totalConsumeNum, openLimit, isLimit } = await preCheckTradeLimitApi({});
	if (isSeeDetail) {
		showTips({ isSeeDetail, tradeLimitNum, totalConsumeNum, openLimit, isLimit });
	} else if (openLimit && isLimit) {
		sendPoint(Pointer.弹窗_单量不足提醒);
		showTips({ isSeeDetail, tradeLimitNum, totalConsumeNum, openLimit, isLimit });
		pageLoading.destroy();
		return Promise.reject();
	}
};
