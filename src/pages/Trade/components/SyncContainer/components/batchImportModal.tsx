import { <PERSON><PERSON>, <PERSON><PERSON>, Too<PERSON><PERSON> } from "antd";
import Upload, { UploadChangeParam } from "antd/lib/upload";
import React, { useEffect, useState } from "react";
import { observer } from "mobx-react";
import { getToken } from "@/utils/token";
import message from "@/components/message";
import userStore from "@/stores/user";
import ProgressModal from "@/components-biz/ProgressModal";
import { ItemAsyncGetProgressApi } from "@/apis/warehouse/system";
import ShopSingleSelect from "@/components-biz/ShopListSelect/shopSingleSelect";
import { PLAT_HAND, PLAT_OTHER, PLAT_FXG } from "@/constants";
import Pointer from "@/utils/pointTrack/constants";
import sendPoint from "@/utils/pointTrack/sendPoint";
import { platform } from "@/types/schemas/common";
import { FunctionPermissionEnum } from '@/utils/permissionCheck/functionPermissionCheck';

export interface IBatchImportModalProps {
    onClose: () => void;
}

const BatchImportModal = (props: IBatchImportModalProps) => {
	const { onClose } = props;
	const [loading, setLoading] = useState(false);
	const [selectShop, setSelectShop] = useState<{plat:platform, sellerNick:string, shopId:string}>(null);
	const [loopObj, setLoopObj] = useState({
		asyncCode: '',
		isStart: false,
	});
	const { isShowZeroStockVersion, hasFunctionPermission } = userStore;
	const uploadProps = {
		accept: '.xls,.xlsx',
		onChange: (info: UploadChangeParam) => {
			console.log(info, 'info');
			setLoading(true);
			if (info.file.status !== 'uploading') {
				console.log(info.file, info.fileList);
			}
			if (info.file.status === 'done') {
				const { data, success, errorMessage } = info.file.response;
				if (!success) {
					setLoading(false);
					Modal.error({
						closable: true,
						centered: true,
						width: 600,
						content: <div style={ { maxHeight: 400, overflowY: 'auto' } } dangerouslySetInnerHTML={ { __html: errorMessage || '导入手工单异常' } } />
					});
				} else {
					setLoading(false);
					// * 异步完成
					setLoopObj({
						isStart: true,
						asyncCode: data.cacheKey
					});
				}
			} else if (info.file.status === 'error') {
				setLoading(false);
				message.error('导入手工单异常');
			}
		},
		data: (file:any) => {
			if (file?.name && !/含店铺EXCEL模板/.test(file?.name)) {
				return {
					'sellerId': selectShop?.shopId,
					'sellerNick': selectShop?.sellerNick,
					'platform': selectShop?.plat
				};
			}
			return {};
		},
		action: location.origin + '/trade/handTrade/importExcel',
		itemRender: (originNode:any, file:any, currFileList: any) => (
			<span className="r-ml-10">{file.name}</span>
		),
		withCredentials: true,
		maxCount: 1,
		headers: {
			'qnquerystring': getToken() || '',
		},
		beforeUpload: (file: any) => {
			const { plat, sellerNick, shopId } = selectShop || {};

			// 含店铺EXCEL模板 不校验店铺
			if (file?.name && !/含店铺EXCEL模板/.test(file?.name)) {
				if (!selectShop) {
					message.error("请选择店铺");
					return Upload.LIST_IGNORE;
				}

				if (!plat || !sellerNick || !shopId) {
					message.error("请选择店铺");
					return Upload.LIST_IGNORE;
				}
			}

			if (file?.name && !/.[xls|xlsx]$/.test(file?.name)) {
				message.warning('上传文件格式不正确！');
				return false;
			}
			const isLt5M = 	file.size / 1024 / 1024 > 2;
			if (isLt5M) {
				message.warning('文件大小不能超过2MB');
				return Upload.LIST_IGNORE;
			}

			if (plat) {
				sendPointAboutSelectPlat(plat);
			}
		}
	};

	const shopChange = (shop) => {
		const { plat, sellerNick, shopId } = shop;
		setSelectShop({ plat, sellerNick, shopId });
	};

	const sendPointAboutSelectPlat = (plat:platform) => {
		switch (plat) {
			case PLAT_HAND:
				sendPoint(Pointer.订单_批量导入手工单_无店铺保存);
				break;
			case PLAT_OTHER:
				sendPoint(Pointer.订单_批量导入手工单_虚拟店铺保存);
				break;
			default:
				sendPoint(Pointer.订单_批量导入手工单_线上店铺保存);
				break;
		}
	};

	return (
		<>
			<Modal title="" width={ 600 } footer={ null } visible onCancel={ onClose } centered maskClosable={ false }>
				<>
					<div className="r-flex r-mb-12 r-mt-12">
						<div style={ { minWidth: "6em", marginTop: "6px" } }><span className="r-c-error">*</span>选择店铺：</div>
						<div>
							<ShopSingleSelect onChange={ shopChange } style={ { width: 174 } } isHideAllShop isDisableExpire isDisableStop isHasHandPlat />
							{
								selectShop?.plat == PLAT_FXG && (
									<div className="" style={ { color: '#f00' } }>
										提示：明文地址手工单使用抖音面单取号时填写地址不生效，实际地址将以关联平台订单为准
									</div>
								)
							}
						</div>
					</div>

					<div className="r-flex r-ai-c">
						<div style={ { minWidth: "6em", marginTop: "6px" } }>选择文件：</div>
						<Upload { ...uploadProps } className="r-flex r-ai-c">
							<Button loading={ loading } type="primary">选择文件</Button>
						</Upload>
					</div>

					<div className="r-flex r-mt-16 r-mb-16">
						<Button
							style={ { marginLeft: '-16px' } }
							onClick={ () => {
								window.open(`/static/download/普通EXCEL模板${isShowZeroStockVersion ? '_零库存版' : ''}.xls`, '_blank');
							} }
							className=""
							type="link"
						>普通excel模板
						</Button>
						{ !hasFunctionPermission(FunctionPermissionEnum.发件人信息编辑) 
							&& (
								<Tooltip title="无手工单发件人编辑权限">
									<Button
										onClick={ () => { 
											window.open(`/static/download/含发件人EXCEL模板${isShowZeroStockVersion ? '_零库存版' : ''}.xls`, '_blank'); 
										} }
										type="link"
										disabled={ !hasFunctionPermission(FunctionPermissionEnum.发件人信息编辑) }
									>含发件人excel模板
							
									</Button>
								</Tooltip>
							)}
						{ hasFunctionPermission(FunctionPermissionEnum.发件人信息编辑) && (
							<Button
								onClick={ () => { 
									window.open(`/static/download/含发件人EXCEL模板${isShowZeroStockVersion ? '_零库存版' : ''}.xls`, '_blank'); 
								} }
								type="link"
							>含发件人excel模板
							
							</Button>
						)}
						{/* <Button
							onClick={ () => {
								window.open(`/static/download/含店铺EXCEL模板${isShowZeroStockVersion ? '_零库存版' : ''}.xls`, '_blank');
							} }
							type="link"
						>含店铺excel模板
						</Button> */}
					</div>

					<div>
						◆ 上传Excel支持xls、xlsx格式，最大支持5MB <br />
						{!isShowZeroStockVersion ? (
							<>
								◆ 导入订单中的商品以 <span className="r-c-error">本地货品档案的货品规格编码</span> 为确定商品的唯一依据 <br />
							</>
						) : ''}
						{`◆ 自定义模板必须选择以下几列（收件人，手机/固话，地址${!isShowZeroStockVersion ? "，货品规格编码）" : ""}`} <br />
						◆ 一个订单下多商品以订单号作为唯一标识，如发生同订单号多条记录信息不一致情况，默认取相同订单号中首条数据的信息（如发件人、地址等）<br />
						◆ 免责说明：由于省市区地址复杂，批量导入地址后，请您务必校验地址省市区是否正确，快递助手不承担因个人未校验导致物流配送错误的问题。 <br />
						{!hasFunctionPermission(FunctionPermissionEnum.发件人信息编辑) ? (
							<>
								◆ 账号无手工单发件人编辑权限时导入发件人信息不生效 <br />
							</>
						) : ''}
					</div>
				</>
			</Modal>

			<ProgressModal
				loopObj={ {
					isShowErrModal: true,
					...loopObj,
					api: ItemAsyncGetProgressApi,
				} }
				modalProps={ {
					title: '进度查询'
				} }
				onClose={ () => {
					setLoopObj(prev => ({
						...prev,
						isStart: false,
					}));
				} }
				onSuccess={ (res) => {
					message.success(`成功导入${res?.result?.successNum || 0}条数据`);
					setLoopObj(prev => ({
						...prev,
						isStart: false,
					}));
					setTimeout(() => {
						onClose();
					}, 2000);
				} }
			/>
		</>
	);
};

export default observer(BatchImportModal);
