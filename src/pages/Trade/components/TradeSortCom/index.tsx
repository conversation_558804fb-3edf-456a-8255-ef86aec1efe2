
import { Divider, Popover, <PERSON>, <PERSON>lt<PERSON>, But<PERSON> } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import { observer } from 'mobx-react';
import { CaretDownOutlined, CaretUpOutlined, PlusOutlined } from '@ant-design/icons';
import cs from 'classnames';
import { getQueryTradeOrderConfigApi, updateQueryTradeOrderConfigApi } from '@/apis/trade';
import { tradeStore } from "@/stores";
import sendPoint from '@/utils/pointTrack/sendPoint';
import s from './index.module.scss';
import { CUSTOM_RULE_ARR, CUSTOM_RULE_NAME, SortItemEnum, TYPE_ENUM, hotKeyList, sortInfo } from './utils';
import event from '@/libs/event';
import Pointer from '@/utils/pointTrack/constants';
import SortModal from './SortModal';
import CustomModal from './CustomModal';
import { QueryTradeOrderDTO } from '@/types/trade/index';


const { Option } = Select;

const TradeSortCom = (props: any) => {
	const { size } = props;
	const { setTradeOrderSortType, setTradeOrderSortTypeList } = tradeStore;
	const [isDesc, setIsDesc] = useState(false); // 是否倒顺序
	const [selected, setSelected] = useState('');
	const [sortModalVisible, setSortModalVisible] = useState(false);
	const [dataSource, setDataSource] = useState([]);
	const [customModalVisible, setCustomModalVisible] = useState(false);
	const [editItem, setEditItem] = useState<QueryTradeOrderDTO>();

	const {
		tradeListStore: {
			setIsShowPrintStyleSetting,
		},
	} = tradeStore;

	const getInit = async() => {
		const res = await getQueryTradeOrderConfigApi();
		setTradeOrderSortTypeList(res?.queryTradeOrderList);
		setIsDesc(res?.isDesc);
		setSelected(res?.selectedOrderKey);
		setTradeOrderSortType(res?.selectedOrderKey);
		setDataSource(res?.queryTradeOrderList);
		console.log(res);
	};
	useEffect(() => {
		getInit();
	}, []);

	const canSwitchAscDesc = useMemo(() => {
		return dataSource.find(i => i.key == selected)?.canSwitchAscDesc;
	}, [dataSource, selected]);

	const handleChange = (v) => {
		console.log(v, sortInfo[v], 'item.point');
		sendPoint(sortInfo[v]?.point); 
		setSelected(v);
		let curDesc = false;
		setIsDesc(curDesc);
		saveConfig({ selectedOrderKey: v, isDesc: curDesc });
	};

	const getInfoByValue = (value) => {
		if (value.type == TYPE_ENUM.自定义) {
			const customList = [];
			CUSTOM_RULE_ARR.forEach(item => {
				const hasValue = value[CUSTOM_RULE_NAME(item)];
				customList.push(hasValue);
			});
			return (
				<div className={ s.infoPopContainer }>
					<div className={ s.infoTitle }>{value.name}</div>
					<div className={ s.title } >规则：</div>
					<div className={ s.ruleContent }>订单将根据优先级顺序进行排序，当多笔订单符合同一优先级时，默认按下一优先级排序，以此类推；</div>
					<div className={ s.title } >当前优先级：</div>
					<div className={ s.ruleContent }>
						{customList.map((i, index) => {
							return <div>优先级-{index + 1}：{SortItemEnum[i]}</div>;
						})}
					</div>

				</div>
			);
		} else {
			const item = sortInfo[value.key];
			if (item) {
				return (
					<div 
						className={ s.infoPopContainer }
					>
						<div className={ s.infoTitle }>{item.title}</div>
						<div className={ s.title } >规则：</div>
						<div className={ s.ruleContent }>{item.rule || '暂无说明'}</div>
						<div className={ s.title } >示例：</div>
						<div className={ s.ruleContent }>{item.example || '暂无说明'}</div>
						<div className={ s.title } >适用场景：</div>
						<div className={ s.ruleContent }>{item.scene || '暂无说明'}</div>
					</div>
				);
			}
		}
		return '';
	};

	const changeRank = () => {
		sendPoint(Pointer.排序设置_点击_修改排序位置);
		setSortModalVisible(true);
	};

	const changeCustom = () => {
		sendPoint(Pointer.排序设置_点击_确定新增);
		setEditItem({});
		setCustomModalVisible(true);
	};

	const bottomNode = (
		<>
			<div className="r-flex r-jc-sb" style={ { padding: '0 12px 6px' } }>
				<div className="r-click" onClick={ changeCustom }><PlusOutlined />新增自定义排序   </div>
				<div className="r-click" onClick={ changeRank }>修改排序位置   </div>
			</div>
		</>
	);

	const handleArrow = async() => {
		if (!canSwitchAscDesc) {
			return;
		}
		const nextIsDesc = !isDesc;
		setIsDesc(nextIsDesc);
		const params = {
			isDesc: nextIsDesc,
		};
		saveConfig(params);

	};

	const saveConfig = async(params) => {
		params = {
			selectedOrderKey: selected,
			isDesc,
			...params
		};
		await updateQueryTradeOrderConfigApi(params);
		setTradeOrderSortType(params.selectedOrderKey);
		event.emit('printBatch.reSearch');

	};

	const sortOk = async(dataSource) => {
		const p = {
			selectedOrderKey: selected,
			isDesc,
			queryTradeOrderList: dataSource
		};
		await updateQueryTradeOrderConfigApi(p);
		setTradeOrderSortType(selected);
		setDataSource(dataSource);

	};
	const customOk = async() => {
		await getInit();
		if (selected == editItem.key) {
			event.emit('printBatch.reSearch');
		}
	};

	const editCustom = (e, item) => {
		e.stopPropagation();
		setCustomModalVisible(true);
		setEditItem(item);
	};

	return (
		<>
			<div className={ s.tradeSort }>
				<Button
					type="link"
					onClick={ () => { setIsShowPrintStyleSetting(true); } }
				>
					打印内容设置
				</Button>

				<Select
					style={ { minWidth: 240 } }
					listHeight={ 450 }
					dropdownMatchSelectWidth={ false }
					onChange={ handleChange }
					optionLabelProp="label"
					placement="bottomLeft"
					value={ selected }
					dropdownStyle={ { width: '380px' } }
					dropdownRender={
						(menu) => {
							return (
								<div className={ s.showSelect }>
									{menu}
									<Divider style={ { margin: '8px 0' } } />
									{bottomNode}
								</div>
							);
						}
					}
				>
					{dataSource.map(i => {
						return (
							<Option
								value={ i.key }
								label={ (
									<Popover placement="left" zIndex={ 1000 } content={ getInfoByValue(i) } trigger="hover">
										{i.type == TYPE_ENUM.自定义 ? <span className="r-c-warning">自定义：</span> : ''}
										{i.name}
									</Popover>
								) }
							>
								<Popover placement="left" zIndex={ 10000 } content={ getInfoByValue(i) } trigger="hover">
									<div className="r-flex r-ai-c">

										{i.type == TYPE_ENUM.自定义 ? <span className="r-c-warning">自定义：</span> : ''}
										<span className={ i.type == TYPE_ENUM.自定义 ? s.customNameStyle : '' }>{i.name}</span>
										{hotKeyList.includes(i.key) ? <span className="r-c-warning r-ml-5">推荐</span> : ''}

										{i.type == TYPE_ENUM.自定义 ? <span className="r-click r-ml-10 customEdit" onClick={ (e) => editCustom(e, i) } >编辑</span> : ''}
									</div>
								</Popover>
							</Option>
						);
					})}
				</Select>
				<div
					onClick={ handleArrow }
					className={ cs(s.arrow, isDesc ? s.isDesc : s.isUp, canSwitchAscDesc ? '' : s.descDisable) }
				>
					<CaretUpOutlined className={ s.up } />
					<CaretDownOutlined className={ s.down } />
				</div>
				<SortModal onOk={ sortOk } dataSource={ dataSource } visible={ sortModalVisible } onCancel={ () => { setSortModalVisible(false); } } />
				<CustomModal onRefresh={ customOk } editItem={ editItem } visible={ customModalVisible } onCancel={ () => { setCustomModalVisible(false); } } />
			</div>
		</>
	);
};

export default observer(TradeSortCom);
