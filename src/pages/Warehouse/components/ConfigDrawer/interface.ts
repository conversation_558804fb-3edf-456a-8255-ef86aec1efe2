import { PLAT_ALI, PLAT_FXG, PLAT_KS, PLAT_PDD, PLAT_SPH, PLAT_TB, PLAT_TM } from "@/constants";
import { platform } from "@/types/schemas/common";

/**
 * BatchPuhuoSettingDTO :BatchPuhuoSettingDTO
 */
export interface ItemDistributeUpdatePlatformPuhuoSettingRequest {
    /**
     * 选择的多店列表 ,PuhuoSettingShopDTO
     */
    shopList?: {
      /**
       * 店铺id
       */
      shopId?: number;
      /**
       * 平台
       */
      platform?: string;
      /**
       * 设置模板id
       */
      freightTemplate?: string;
      [k: string]: any;
    }[];
    /**
     * 是否选择当前模板
     */
    selected?: number;
    /**
     * 设置模板id
     */
    id?: number;
    /**
     * 设置模板名称
     */
    templateName?: string;
    /**
     * 货源平台1688PPW
     */
    fromPlatform?: string;
    /**
     * 平台
     */
    platform: string;
    /**
     * 店铺id
     */
    shopId: string;
    /**
     * 店铺名
     */
    shopName: string;
    /**
     * 平台用户唯一标识
     */
    userId?: string;
    /**
     * 是否开启图片空间自动清理
     */
    pictureClean: boolean;
    /**
     * 类目匹配方式,1智能匹配（默认）,2手动匹配,3指定类目铺货
     */
    matchType?: number;
    /**
     * 平台类目id,<p>,这里类目是是多级拼接，1级,2级,3级
     */
    platformCid?: string;
    /**
     * 平台类目名称,<p>,这里类目是是多级拼接，1级>2级>3级
     */
    platformCidName?: string;
    /**
     * 其他配置信息json格式,{,"autoDelivery":1,,"autoFill":1,,"autoWhiteBackground":1,,"deliveryTime":1,,"freightTemplate":0,,"groupPrice":{,"removeDecimalPoint":1,,"removeMethod":0,},,"inventorySettings":1,,"payTenForOneFake":1,,"preSale":0,,"promiseToShip":0,,"sellingPriceSetting":{,"markup":0,,"percentage":0,,"removeDecimalPoint":1,,"removeMethod":0,},,"sevenDay":1,,"shelfSetting":1,,"singlePrice":{,"markup":0,,"removeDecimalPoint":1,,"removeMethod":0,},,"underlinedPrice":{,"markup":0,,"removeDecimalPoint":1,,"removeMethod":0,},,<p>,} ,ShopSettingBo
     */
    extraSetting: {
      platform?: string;
      /**
       * 平台：全平台,<p>,上架设置1：直接上架2：存放仓库
       */
      shelfSetting?: number;
      /**
       * 平台：抖音即时零售,上架门店选择直接上架时对应的门店配置 ,ShelfStoresSetting
       */
      shelfStores?: {
        /**
         * 0:所有门店，1：部分选中门店
         */
        type?: number;
        /**
         * type为1时，这里是选择的门店集合 ,ShelfStores
         */
        shelfStoresList?: {
          /**
           * 门店id
           */
          storeId?: number;
          /**
           * 门店名称
           */
          name?: string;
          [k: string]: any;
        }[];
        [k: string]: any;
      };
      /**
       * 平台：抖音即时零售,商品商家编码
       */
      goodsMerchantCode?: string;
      /**
       * 平台：抖音即时零售,<p>,重量设置 ,WeightInfo
       */
      weightInfo?: {
        /**
         * 重量值
         */
        value?: string;
        /**
         * 单位
         */
        unit?: string;
        [k: string]: any;
      };
      /**
       * 平台：淘宝、抖音、快手、微店、微信小商店,<p>,商品售价设置 ,SellingPriceSetting
       */
      sellingPriceSetting?: {
        /**
         * 价格类型0：代发价，1：批发价默认代发价
         */
        priceType?: number;
        /**
         * 百比例
         */
        percentage?: number;
        /**
         * 加价
         */
        markup?: string;
        /**
         * 是否去掉小数点1=直接去掉0=不去掉（保留角分）2=固定小数
         */
        removeDecimalPoint?: number;
        /**
         * 固定小数点后的内容
         */
        fixedPointPrice?: string;
        /**
         * 冲突字段基于removeDecimalPoint是直接去掉的情况下,0=四舍五入1=直接去掉,废弃
         */
        removeMethod?: number;
        /**
         * 冲突类型,<p>,售价类型：0-分销价1-利润率
         */
        sellingPriceType?: number;
        [k: string]: any;
      };
      /**
       * 平台：除拼多多都有,<p>,追加运费设置 ,AppendFreightSetting
       */
      appendFreightSetting?: {
        /**
         * 追加运费开关0:关闭1:开启,新用户默认关闭
         */
        flag?: number;
        /**
         * 省
         */
        province?: string;
        /**
         * 运费占比
         */
        freightRate?: number;
        /**
         * 追加金额
         */
        appendAmount?: number;
        [k: string]: any;
      };
      /**
       * 平台：淘宝、天猫,<p>,商家编码设置 ,MerchantCode
       */
      merchantCode?: {
        /**
         * 商家开关0:关闭1:开启
         */
        flag?: number;
        /**
         * 商家编码
         */
        code?: string;
        [k: string]: any;
      };
      /**
       * 平台：全平台,<p>,库存计数：1：使用商品来源2：统一修改库存
       */
      inventoryNumSettings?: number;
      /**
       * 抖音店长：sku库存低于这个值设置库存为0
       */
      lowerStockToZero?: number;
      /**
       * 抖音店长：sku库存低于这个值sku设置为下线
       */
      lowerStockOffline?: number;
      /**
       * 抖音店长：来源库存小于0时将库存设置为这个数
       */
      fromStockZeroSetTo?: number;
      /**
       * 两件折扣->满两件打折默认95,示例：95（代表商品上已设置9.5折的折扣值）
       */
      twoPiecesDiscount?: number;
      /**
       * 闲鱼发货地区id
       */
      districtId?: number;
      /**
       * 闲鱼发货地区名称
       */
      districtNames?: string;
      /**
       * 闲鱼上货店铺列表 ,String
       */
      publishUserName?: string[];
      /**
       * 闲鱼物流费用:单位元
       */
      logisticFee?: string;
      /**
       * 平台：全平台,<p>,统一修改库存数量
       */
      inventoryNums?: number;
      /**
       * 平台：全平台,<p>,标题设置 ,TitleSettings
       */
      titleSettings?: {
        /**
         * 标题前缀
         */
        pre?: string;
        /**
         * 标题后缀
         */
        end?: string;
        /**
         * 替换关键词 ,ReplaceStr
         */
        replaces?: {
          /**
           * 替换前
           */
          before?: string;
          /**
           * 替换之后
           */
          after?: string;
          [k: string]: any;
        }[];
        /**
         * 1：超长删最前，2：超长删最后
         */
        moreThanDel?: number;
        [k: string]: any;
      };
      /**
       * 平台：全平台,<p>,规格设置 ,SkipSettings
       */
      skipSettings?: {
        /**
         * 跳过重复铺货true是false否
         */
        isSkipRepeatDistrib?: boolean;
        [k: string]: any;
      };
      /**
       * 跳过设置-需保存到Oss内容 ,SkipSettingsOss
       */
      skipSettingsOss?: {
        /**
         * 标题删除关键词","隔开
         */
        titleDel?: string;
        /**
         * 标题跳过关键词","隔开
         */
        titleSkip?: string;
        /**
         * SKU删除关键词","隔开
         */
        skuDel?: string;
        /**
         * SKU跳过关键词","隔开
         */
        skuSkip?: string;
        /**
         * 代发价低于多少价格跳过单位分
         */
        priceLowSkip?: string;
        /**
         * 代发价高于多少价格跳过单位分
         */
        priceHighSkip?: string;
        /**
         * SKU低于多少价格跳过单位分
         */
        skuPriceLowSkip?: string;
        /**
         * SKU高于多少价格跳过单位分
         */
        skuPriceHighSkip?: string;
        [k: string]: any;
      };
      /**
       * 平台：全平台,<p>,SKU规格名称 ,SkuSpecificationName
       */
      skuSpecificationName?: {
        /**
         * 删除关键字
         */
        del?: string;
        /**
         * 标题前缀
         */
        pre?: string;
        /**
         * 标题后缀
         */
        end?: string;
        /**
         * 替换关键词 ,ReplaceStr
         */
        replaces?: {
          /**
           * 替换前
           */
          before?: string;
          /**
           * 替换之后
           */
          after?: string;
          [k: string]: any;
        }[];
        [k: string]: any;
      };
      /**
       * 平台：全平台,<p>,主图设置：是否删除主图0:不删除1：删除
       */
      isDeletePicture?: number;
      /**
       * 平台：全平台,<p>,主图设置：删除主图第几张
       */
      deletePictureNum?: number;
      /**
       * 平台：淘宝,<p>,运费模版类型1-复制货源运费模板2-使用本店运费模板默认2
       */
      freightTemplateType?: number;
      /**
       * 平台：全平台,<p>,运费模板默认-1
       */
      freightTemplate?: string;
      /**
       * 平台：全平台,<p>,物流模板默认-1
       */
      logisticTemplate?: string;
      /**
       * 商品配送方式,4-快递发货;8-同城配送;16-到店自提可多选多选,分割
       */
      itemDeliveryList?: string;
      /**
       * 平台：拼多多、快手,<p>,发货时间,<p>,快手拼多多用承诺发货默认-1,单位：秒，取值86400,172800,259200,-1代表发货时间不承诺前端传了24,48,72
       */
      promiseToShip?: number;
      /**
       * 是否预售默认0-现货发货，1-预售发货，2-阶梯发货，默认0
       */
      preSale?: number;
      /**
       * 平台：抖店、拼多多、快手,<p>,七天无理由,<p>,抖店是否支持七天无理由退货0不支持，1支持，2支持（拆封后不支持）,<p>,快手类目退款规则列表：1：支持7天无理由退货，4：不支持7天无理由退货，5：支持7天无理由退货(拆封后不支持)，6：支持7天无理由退货(激活后不支持)，7：支持7天无理由退货(安装后不支持)，8：支持7天无理由退货(定制类不支持)，9：支持7天无理由退货(使用后不支持)，10：支持7天无理由退货(开窗后不支持)，11：随时退过期退，12：过期自动退，13：不可退
       */
      sevenDay?: number;
      /**
       * 平台：快手,服务承诺 ,ServicePromise
       */
      servicePromise?: {
        /**
         * 平台：快手,坏了包退0未选中，1选中默认
         */
        freshRotRefund?: number;
        /**
         * 平台：快手,破损包退0未选中，1选中默认
         */
        brokenRefund?: number;
        /**
         * 平台：快手,过敏包退0未选中，1选中默认
         */
        allergyRefund?: number;
        [k: string]: any;
      };
      /**
       * 平台：淘宝,<p>,自定义类目id逗号分割1,2,3
       */
      sellerCatIds?: string;
      /**
       * 平台：淘宝,<p>,发货时间,<p>,淘宝时间类型：0：48小时内发货2：大于48小时发货3：24小时发货,默认为0
       */
      deliveryTimeType?: number;
      /**
       * 平台：淘宝,<p>,发货时间天数例如3，deliveryTimeType为2时生效,<p>
       */
      tbDeliveryTime?: number;
      /**
       * 品牌自定义映射关系 ,BrandCustomGroup
       */
      brandCustomMapping?: {
        /**
         * SimpleCategory
         */
        brandCategory?: {
          fullCid?: string;
          fullCname?: string;
          [k: string]: any;
        };
        /**
         * SimpleBrand
         */
        simpleBrand?: {
          brandId?: string;
          brandName?: string;
          [k: string]: any;
        };
        [k: string]: any;
      }[];
      /**
       * 平台：淘宝抖音,<p>,主图视频上传开关1上传0不上传
       */
      imageVideoUpload?: number;
      /**
       * 平台：全平台,<p>,详情外链及其内容过滤1打开0关闭
       */
      remove1688OutLink?: number;
      /**
       * 平台:全平台,<p>,白底图自动生成1打开0关闭
       */
      generateWhitePic?: number;
      /**
       * 平台:全平台,<p>,生成第几张白底图
       */
      generateWhitePicNum?: number;
      /**
       * 平台：全平台,<p>,详情描述：删除详情图,<p>,删除详情图类型1:删除详情图前后几张2：删除详情图第几张3：两个都选
       */
      deleteDetailPicType?: number;
      /**
       * 平台：全平台,<p>,详情描述：删除详情图前几张,<p>,删除详情图张数：配合deleteDetailPicType使用,多张用";"隔开
       */
      deleteDetailPicNumPre?: string;
      /**
       * 平台：全平台,<p>,详情描述：删除详情图后几张,<p>,删除详情图张数：配合deleteDetailPicType使用,多张用";"隔开
       */
      deleteDetailPicNumEnd?: string;
      /**
       * 平台：全平台,<p>,详情描述：指定位置删除详情图,<p>,删除详情图张数：配合deleteDetailPicType使用,多张用";"隔开
       */
      deleteDetailPicNum?: string;
      /**
       * 平台：全平台,<p>,详情页前添加详情图 ,String
       */
      addDetailPicPre?: string[];
      /**
       * 平台：全平台,<p>,详情页前添加文字转图片
       */
      addDetailPreText?: string;
      /**
       * 平台：全平台,<p>,详情页前添加文字转图片的图片
       */
      addDetailPreTextPic?: string;
      /**
       * 平台：全平台,<p>,详情页后添加详情图 ,String
       */
      addDetailPicEnd?: string[];
      /**
       * 平台：淘宝,<p>,图片空间：节省图片空间
       */
      zipImage?: boolean;
      /**
       * 平台：淘宝,<p>,图片空间：节省图片空间，图片大小，单位kb张
       */
      zipImageSize?: number;
      /**
       * 平台：全平台,<p>,详情页后添加文字转图片
       */
      addDetailEndText?: string;
      /**
       * 平台：全平台,<p>,详情页后添加文字转图片的图片
       */
      addDetailEndTextPic?: string;
      /**
       * 0:第一张主图，1：第二张主图，2：第三张主图，3：第四张主图，4：第五张主图，999：末尾主图，-1：白底图 ,Integer
       */
      addMainPicToDetail?: number[];
      /**
       * 0：主图添加到详情页前，1：主图添加到详情页后，2：主图添加详情页随机位置
       */
      addMainPicToDetailPosition?: number;
      /**
       * 平台：全平台,<p>,支持违禁词过滤1支持0不支持
       */
      filterForbiddenWord?: number;
      /**
       * 平台：京东,<p>,品牌id
       */
      brandId?: number;
      /**
       * 平台：京东,<p>,品牌名称
       */
      brandName?: string;
      /**
       * 平台：抖店,<p>,必须三包服务:1：保修期内提供维修（寄修）2：延长售后服务有效期
       */
      mustThreeGuaranteesType?: number;
      /**
       * 平台：抖店,<p>,必须三包服务天数：,<p>,单位：天数
       */
      mustThreeGuaranteesDays?: number;
      /**
       * 平台：抖店,<p>,可选三包服务:0:不包含三包1：保修期内提供维修（寄修）2：延长售后服务有效期
       */
      notMustThreeGuaranteesType?: number;
      /**
       * 平台：抖店,<p>,可选三包服务天数,<p>,单位：天数
       */
      notMustThreeGuaranteesDays?: number;
      /**
       * 基础品牌设置 ,CommonBrandSetting
       */
      commonBrandSetting?: {
        /**
         * 本店品牌必填时货源有品牌，则本店品牌设置配置项
         */
        mustOnFromItemExistConf?: number;
        /**
         * 本店品牌必填时货源无品牌，则本店品牌设置配置项
         */
        mustOnFromItemWithoutConf?: number;
        /**
         * 本店品牌非必填时则本店品牌设置配置项
         */
        noMustOnItemConf?: number;
        [k: string]: any;
      };
      /**
       * 抖音-支付方式：0：货到付款1：在线支付2：在线支付货到付款
       */
      payMethod?: number;
      /**
       * 抖音每次限购数量
       */
      perCumulativePurchase?: number;
      /**
       * 抖音单用户累计限购数量
       */
      cumulativePurchase?: number;
      /**
       * 抖音每次至少购买
       */
      perLeastPurchase?: number;
      /**
       * 发货模式：0：现货发货1：全款预售发货2：使用商品来源
       */
      deliveryMethod?: number;
      /**
       * 预售时间：
       */
      preSaleTime?: string;
      /**
       * 拼多多团购价 ,GroupPrice
       */
      groupPrice?: {
        /**
         * 价格类型0：代发价，1：批发价默认代发价
         */
        priceType?: number;
        /**
         * 是否去掉小数点1=去掉0=不去掉2=固定小数
         */
        removeDecimalPoint?: number;
        /**
         * 0=四舍五入1=直接去掉
         */
        removeMethod?: number;
        /**
         * 固定小数点后的内容
         */
        fixedPointPrice?: string;
        [k: string]: any;
      };
      /**
       * 拼多多单买; ,SinglePrice
       */
      singlePrice?: {
        /**
         * 加价
         */
        markup?: string;
        /**
         * 是否去掉小数点1=去掉0=不去掉
         */
        removeDecimalPoint?: number;
        /**
         * 0=四舍五入1=直接去掉
         */
        removeMethod?: number;
        [k: string]: any;
      };
      /**
       * 自动发货1=自动发货0=不自动
       */
      autoDelivery?: number;
      /**
       * 平台：全平台,<p>,白底图：自动生成白底图1=自动0=不自动（弃用）
       */
      autoWhiteBackground?: number;
      /**
       * 平台：拼多多,<p>,假一罚十,<p>,加1赔101=开启0=不开启
       */
      payTenForOneFake?: number;
      /**
       * 平台：拼多多,<p>,警告提醒,<p>,是否忽略异常0忽略1不忽略默认1不忽略
       */
      ignoreEditWarn?: number;
      /**
       * 过滤二维码1=开启0=不开启默认开启
       */
      qrcodeFilter?: number;
      /**
       * 抖音商品划线价 ,UnderlinedPrice
       */
      underlinedPrice?: {
        /**
         * 划线价比例
         */
        percentage?: number;
        /**
         * 加价
         */
        markup?: string;
        /**
         * 是否去掉小数点1=去掉0=不去掉
         */
        removeDecimalPoint?: number;
        /**
         * 0=四舍五入1=直接去掉
         */
        removeMethod?: number;
        [k: string]: any;
      };
      /**
       * 平台：淘宝、快手,<p>,减库存类型：1-拍下减库存2-付款减库存
       */
      inventorySettings?: number;
      /**
       * 平台：抖店,<p>,客服电话
       */
      mobile?: string;
      /**
       * 平台：小红书1688抖店,发货时间-1=不承诺999=当天发货2=48h两天、3=72h3天、5=5天、7=7天、10=10天、15=15天1=1天次日发货
       */
      deliveryTime?: number;
      /**
       * 预售结束后发货：单位天
       */
      afterSaleDeliveryTime?: number;
      /**
       * 支付完成后发货：单位天
       */
      afterPayDeliveryTime?: number;
      /**
       * 爆单设置 ,ExplosiveOrderSetting
       */
      explosiveOrderSetting?: {
        /**
         * 1开启0关闭
         */
        open?: number;
        /**
         * 1=智能匹配0=手动选择
         */
        matchCategory?: number;
        /**
         * 类目id
         */
        categoryId?: string;
        /**
         * 类目名称
         */
        categoryName?: string;
        /**
         * 服务保障 ,Integer
         */
        serviceGuarantee?: number[];
        /**
         * 起始价格
         */
        startPrice?: string;
        /**
         * 结束价格
         */
        endPrice?: string;
        /**
         * 上货量
         */
        distribNum?: number;
        /**
         * 标志1=开启0=关闭
         */
        offShelfFlag?: number;
        /**
         * 动销设置
         */
        offShelfDay?: number;
        /**
         * 动销操作：1删除，2下架
         */
        offShelfAct?: number;
        [k: string]: any;
      };
      /**
       * 智能匹配自定义类目映射关系组 ,SimpleCategoryGroup
       */
      smartMatchCustomMapping?: {
        /**
         * SimpleCategory
         */
        fromSimpleCategory?: {
          fullCid?: string;
          fullCname?: string;
          [k: string]: any;
        };
        /**
         * SimpleCategory
         */
        toSimpleCategory?: {
          fullCid?: string;
          fullCname?: string;
          [k: string]: any;
        };
        [k: string]: any;
      }[];
      /**
       * 货源同步开关
       */
      itemSourceSync?: boolean;
      /**
       * 货源库存同步配置 ,StockSyncConfig
       */
      stockSyncConfig?: {
        /**
         * 标志1=开启0=关闭
         */
        flag?: number;
        /**
         * 选项1,2,3对应单选按钮1,2,3
         */
        stockSyncOption?: number;
        /**
         * stockSyncOption为1时，是否开启低于多少时不修改库存选项
         */
        stockModifyOption?: number;
        /**
         * stockSyncOption为1时，低于多少时不修改库存
         */
        stockModifyMinNum?: number;
        /**
         * 货源库存低于100时提醒
         */
        stockWarnMinNum?: number;
        [k: string]: any;
      };
      /**
       * 货源价格同步配置 ,PriceSyncConfig
       */
      priceSyncConfig?: {
        /**
         * 标志1=开启0=关闭
         */
        flag?: number;
        /**
         * 货源涨价配置，1-仅提醒
         */
        priceUpOption?: number;
        /**
         * 货源降价配置，1-仅提醒
         */
        priceDownOption?: number;
        [k: string]: any;
      };
      /**
       * 货源状态同步配置 ,StatusSyncConfig
       */
      statusSyncConfig?: {
        /**
         * 标志1=开启0=关闭
         */
        flag?: number;
        /**
         * 货源sku失效配置，1-仅提醒
         */
        skuInvalidOption?: number;
        /**
         * 货源sku上架配置，1-仅提醒
         */
        skuUpOption?: number;
        /**
         * 货源sku下架配置，1-仅提醒
         */
        skuDownOption?: number;
        [k: string]: any;
      };
      /**
       * 平台：拼多多,主图视频是否上传到多多视频，也叫视频详解,<p>,先不给默认值，防止序列化到其他平台去
       */
      uploadDuoduoVideo?: boolean;
      [k: string]: any;
    };
    /**
     * scenceType==2：多店同步业务场景下，关联店铺的运费模版设置 ,MultipleSyncInfoSettingBo
     */
    multipleSyncInfo?: {
      /**
       * FreightTemplate
       */
      multipleFreightTemplateRelation?: {
        /**
         * 平台：全平台,<p>,运费模板默认0
         */
        freightTemplateId?: string;
        /**
         * 关联的店铺id
         */
        toShopId?: string;
        [k: string]: any;
      }[];
      [k: string]: any;
    };
    /**
     * 跳过复制设置项 ,SkipCopyInfoSettingBo
     */
    skipCopyInfo?: {
      /**
       * 设置了需要跳过的类目 ,SimpleCategory
       */
      skipCopyCategorySetting?: {
        fullCid?: string;
        fullCname?: string;
        [k: string]: any;
      }[];
      [k: string]: any;
    };
    /**
     * 自动下单
     */
    autoOrder?: boolean;
    /**
     * 自动备注
     */
    autoMemo?: boolean;
    /**
     * 自动售后
     */
    autoRefund?: boolean;
    /**
     * 是否默认模板
     */
    isDefault?: number;
    /**
     * 更新时间
     */
    modified?: string;
    shopType?: string;
    /**
     * 场景区分：1：铺货业务，2：多店同步业务
     */
    scenceType?: number;
    /**
     * 查询订单设置
     */
    queryOrderSetting?: boolean;
    [k: string]: any;
  }
  
// 筛选店铺，并按指定顺序排序，同时过滤掉已关闭的店铺 -这是to
export const itemMigrationplatform :platform[] = [PLAT_TB, PLAT_TM, PLAT_FXG, PLAT_PDD, PLAT_ALI, PLAT_KS, PLAT_SPH];
// 筛选店铺，并按指定顺序排序，同时过滤掉已关闭的店铺 -这是from
export const choiceItemMigrationplatform :platform[] = [PLAT_FXG, PLAT_PDD, PLAT_ALI, PLAT_KS, PLAT_SPH];
// 这是统一匹配里面 类目需要分段获取的种类，查询需要走后端
export const CategorySelectList = [PLAT_TB, PLAT_ALI, PLAT_KS];
// 商品类目没有自动匹配的
export const CategoryNoAutoSelectList = [PLAT_TB, PLAT_ALI, PLAT_SPH];
export const sevenDayList = [
	{
		value: 1,
		label: '支持7天无理由退货'
	},
	{
		value: 4,
		label: '不支持7天无理由退货'
	},
	{
		value: 5,
		label: '支持7天无理由退货（拆封后不支持）'
	},
	{
		value: 6,
		label: '支持7天无理由退货（激活后不支持）'
	},
	{
		value: 7,
		label: '支持7天无理由退货（安装后不支持）'
	},
	{
		value: 8,
		label: '支持7天无理由退货（定制类不支持）'
	},
	{
		value: 9,
		label: '支持7天无理由退货（使用后不支持）'
	}
];
export const sevenDayOtherList = [
	{
		value: 10,
		label: '支持7天无理由退货(开窗后不支持)'
	},
	{
		value: 11,
		label: '随时退过期退'
	},
	{
		value: 12,
		label: '过期自动退'
	},
	{
		value: 13,
		label: '不可退'
	}
];
export const sphSevenDayList = [
	{
		value: 1,
		label: '支持7天无理由'
	},
	{
		value: 4,
		label: '不支持7天无理由'
	},
	{
		value: 8,
		label: '支持7天无理由（定制商品除外）'
	},
	{
		value: 9,
		label: '支持7天无理由（使用后不支持）'
	}
];