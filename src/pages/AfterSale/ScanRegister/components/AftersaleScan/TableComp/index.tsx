import React, { useEffect, useImperativeHandle, useState, useMemo, useRef } from "react";
import { Table, Form, Input, Button, Modal, Checkbox, Popover, Tooltip } from "antd";
import { PlusCircleFilled, MinusCircleFilled, CopyOutlined, QuestionCircleFilled, SettingOutlined, LoadingOutlined } from '@ant-design/icons';
import cs from 'classnames';
import { observer } from 'mobx-react';
import { cloneDeep, isUndefined } from 'lodash';
import message from "@/components/message";
import { AftersaleScanTableDataSourceItem } from '@/types/aftersale/scanRegister/index';
import Image from "@/components/Image";
import PlatformIcon from '@/pages/Warehouse/StockSync/HandStockSync/components/PlatformIcon';
import ProductListModal from "@/components-biz/Product/List/Model";
import SelectPrinterAndTemplateModal from "@/components-biz/SelectPrinterAndTemplateModal";
import { EVENT_BUS, AFTERSALE_REQUEST_SOURCE } from '@/pages/AfterSale/constants';
import ProductOnlineListModal from "@/components-biz/Product/OnlineList/Model";
import { GenerateRefundLabelApi, TradeGetOrderCountGroupByDetailsApi } from "@/apis/aftersale/scanRegister";
import userStore from "@/stores/user";
import videoMonitorStore from "@/stores/trade/videoMonitor";
import PrintCenter from '@/print/index';
import event from '@/libs/event';
import styles from './index.module.scss';
import { copyToPaste, integer, splitFxgTid } from '@/utils';
import useGetState from '@/utils/hooks/useGetState';
import { getPlatformTradeLink, getPlatformDetailLink, afterSaleTypeText, RefundItemTypeEnum, refundItemTypeEnum } from "@/pages/AfterSale/TradeList/utils";
import { TradeDictQueryDictApi, TradeDictInsertDictApi } from '@/apis/trade/search';
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import Icon from "@/components/Icon";
import BatchModifyMemoModal from "@/pages/Trade/components/BatchModifyMemoModal";
import { tradeStore } from "@/stores";
import { TradeBatchUpdateMemoRequest } from "@/types/trade/index";
import scanPrintStore from "@/stores/trade/scanPrint";
import { avoidRepeatReq } from "@/utils/util";
import { PLAT_ALI, PLAT_HAND, PLAT_KS, PLAT_OTHER, PLAT_TB, allFlagArr, flagAudio, PLAT_SCMHAND, DEFAULT_IMG } from "@/constants";
import { getTradeFlag, getTradeFlagTag } from "@/pages/Trade/utils";
import BuyerNickComp from "@/components-biz/BuyerNickComp";
import ZeroStockOnlineListModal from "@/components-biz/Product/ZeroStockOnlineList/Modal";
import { inventoryDeduct } from "@/pages/Index/Settings/System/constants";
import { playAudio } from "@/pages/Trade/ScanPrint/utils";
import 买家留言 from '@/assets/mp3/买家留言.mp3';
import 卖家备注 from '@/assets/mp3/卖家备注.mp3';
import 未匹配售后单 from '@/assets/mp3/未匹配售后单.mp3';
import 售后换货 from '@/assets/mp3/售后换货.mp3';
import voice2 from "@/assets/mp3/匹配2件.mp3";
import voice3 from "@/assets/mp3/匹配3件.mp3";
import voice4 from "@/assets/mp3/匹配4件.mp3";
import voice5 from "@/assets/mp3/匹配5件.mp3";
import voiceMoreThan5 from "@/assets/mp3/超过5件.mp3";
import { OfflineMemoEnum, UpdateTypeEnum, listItemName } from "@/pages/AfterSale/TradeList/constants";
import OfflineMemoModal, { EnumFromPage, IBatchOfflineMemoModalProps } from "@/components-biz/OfflineMemoModal";
import { BatchUpdateLocalNoteApi, GetRefundGlobalConfigListApi, UpdateRefundGlobalConfigApi } from "@/apis/aftersale/trade";
import RefundItemType from '@/pages/AfterSale/components/RefundItemType';
import { BQ_PRINT_TYPE } from "@/pages/Trade/constants";
import { LabelPrintSourceEnum } from '@/constants/labelPush';
import { FieldsPermissionEnum } from "@/utils/permissionCheck/fieldsPermissionCheck";
import { isSourceScm, isAfterSaleSourceScm } from "@/components-biz/ShopListSelect/shopListUtils";
import GoodsContentSetting from "@/pages/AfterSale/TradeList/components/GoodsContentSetting";
import { DEFAULT_PRODUCT_CONTENT_SETTING_SP, DEFAULT_PRODUCT_CONTENT_SETTING_ZERO_SP, IMG_LIST_HP, IMG_LIST_SP, LOCAL_AFTERSALE_SCANING_SETTING, PRODUCT_CONTENT_ENUM_HP, PRODUCT_CONTENT_ENUM_SP, PRODUCT_SETTING_OBJECT_HP, PRODUCT_SETTING_OBJECT_IMG_SIZE_HP, PRODUCT_SETTING_OBJECT_IMG_SIZE_SP, PRODUCT_SETTING_OBJECT_SP, PRODUCT_SETTING_ZERO_OBJECT_SP } from "./constants";
import TradeInfoModal from '../TradeInfoModal';
import BatchModifyLocalMemoPicModal from '@/pages/AfterSale/TradeList/components/BatchModifyLocalMemoPicModal';
import { tokens } from '@/utils/request';

const ORDER_ROW_START_KEY = 'order';
const GOOD_ROW_START_KEY = 'good';

/**
 * 本组件可优化点
 * 1.table的dataSource处理之前应当有一个公用的数据模型，应当根据这个数据模型来自动生成个dataSource
 * 而不是每次添加商品或者货品的时候操作dataSource
 * 2.勾选逻辑需要优化成对勾选的key进行监听，然后再实现checkbox的全选或者部分选，
 * 而不是每次通过onRowSelected来做这件事，不然回显勾选的时候执执行onRowSelected方法会导致页面性能很差
 *
 */
const TableComp = (props) => {
	const { configInfo, searchInstance, cRef, sortColumnList } = props;
	const thePrinterList = scanPrintStore.printersList;
	const { selectPrinterAndTemplateModalVisible, setSelectPrinterAndTemplateModalVisible } = tradeStore;
	const theThdXbqTempList = tradeStore.thdXbqTempList;

	const [form] = Form.useForm();
	const batchModifyLocalMemoPicModalRef = useRef(null);
	const columnsRef = useRef([]); // 初始值设为空数组
	const [version, setVersion, getVersion] = useGetState(1);// 1是库存 2 0库存
	const [buildType, setBuildType, getBuildType] = useGetState(3);
	const [dataSource, setDataSource, getDataSource] = useGetState<AftersaleScanTableDataSourceItem[]>([]);
	const [applyRefundNumCount, setApplyRefundNumCount] = useState(0);
	const [alreadyRefundNumCount, setAlreadyRefundNumCount] = useState(0);
	const [receiveNumFill, setReceiveNumFill, getReceiveNumFill] = useGetState(false);
	const [memoFlagConfig, setMemoFlagConfig, getMemoFlagConfig] = useGetState("");
	const [hasPlayedVoice, setHasPlayedVoice, getHasPlayedVoice] = useGetState(false);
	const [receiveNumCount, setReceiveNumCount] = useState(0);
	const [autoPrintSwitchState, setAutoPrintSwitchState, getAutoPrintSwitchState] = useGetState(false);
	const [printer, setDefaultPrinter, getDefaultPrinter] = useGetState('');
	const [template, setDefaultTemplate, getDefaultTemplate] = useGetState('');
	const [selectedRowKeys, setSelectedRowKeys, getSelectedRowKeys] = useGetState<React.Key[]>([]);
	const [printerList, setPrinterList, getPrinterList] = useGetState(thePrinterList);
	const [thdXbqTempList, setThdXbqTempList, getThdXbqTempList] = useGetState(theThdXbqTempList);
	const [refundScanConfirmRes, setRefundScanConfirmRes, getRefundScanConfirmRes] = useGetState();
	const [isFormValidateFieldsFn, setIsFormValidateFieldsFn, getIsFormValidateFieldsFn] = useGetState(false);
	const [printLoading, setPrintLoading] = useState(false);
	const [userInfo, setUserInfo, getUserInfo] = useGetState({});
	const [matchPlatform, setMatchPlatform, getMatchPlatform] = useGetState("");
	const [matchShopInfo, setMatchShopInfo, getMatchShopInfo] = useGetState({});
	const [orderFormData, setOrderFormData, getOrderFormData] = useGetState<any>({});
	const [shopList, setShopList, getShopList] = useGetState([]);
	const [offlineMemoData, setOfflineMemoData] = useState<IBatchOfflineMemoModalProps["data"]>({ visible: false });
	const [tradeInfoModalShow, setTradeInfoModalShow] = useState<{[k: string]: any, visible: boolean}>({ visible: false }); // 待发货订单详情
	const [productContentList, setProductContentList] = useState<string[]>([]);

	const [modifyMemoIndex, setModifyMemoIndex] = useState(-1);
	const { setModifyMemoPackage, setIsShowBatchModifyMemoModal } = tradeStore;

	const { isShowZeroStockVersion } = userStore;

	const customLogPost = (dataType: string, data:any = {}) => {
		window.errorCollection?.customMessageUpload({
			type: `售后扫描登记-售后扫描: 【 ${dataType} 】`,
			data: {
				configInfo, // confirmNumFillUpDefault, autoPrintLabels, defaultPrinter, defaultReturnLabelTemplate, memoFlagConfig
				selectedRowKeys,
				...data
			}
		});
	};

	const sharedOnCell = (row, index) => {
		if (row.rowType === 'order') {
			return {
				colSpan: 0
			};
		}
		return {};
	};
	useEffect(() => {
		// BQ_PRINT_TYPE
		PrintCenter.getBqTempList(BQ_PRINT_TYPE.退货单);
	}, []);

	// 添加修改线下备注图片的函数
	const modifyOfflineMemoPic = (item, recordIndex) => {
		// 添加 ref 检查
		console.log(item.localContentPicList);
		if (batchModifyLocalMemoPicModalRef.current) {
			batchModifyLocalMemoPicModalRef.current.open({
				refundId: item.refundId,
				refundScanRecordId: item.refundScanRecordId || item.scanRecordId,
				sid: item.sid,
				localMemoPic: Array.isArray(item.localContentPicList) ? item.localContentPicList.join(',') : item.localContentPicList || '',
				requestSource: AFTERSALE_REQUEST_SOURCE.单个扫描,
			}, (localContentPic) => {
				setDataSource(prev => {
					prev[recordIndex].localContentPicList = (Array.isArray(localContentPic) ? localContentPic : localContentPic.split(',')).filter(Boolean);
					return [...prev];
				});
			});
		  } else {
			console.error('BatchModifyLocalMemoPicModal ref is not initialized');
		  }
	};

	const onSearch = (searching) => {
		if (searching) {
			setDataSource([]);
			setSelectedRowKeys([]);
			setAlreadyRefundNumCount(0);
			setApplyRefundNumCount(0);
			setReceiveNumCount(0);
			form.resetFields();
		}
	};
	// 处理底部展示的各类统计数量
	const handleCountNum = () => {
		let applyRefundNumCount = 0;
		let alreadyRefundNumCount = 0;
		let receiveNumCount = 0;
		dataSource.forEach(item => {
			if (item.rowType === 'good' && selectedRowKeys.includes(item.key)) {
				applyRefundNumCount += Number(item.applyRefundNum || 0);
				alreadyRefundNumCount += Number(item.hasRefundNum || 0);
				receiveNumCount += Number(form.getFieldValue(`receiveNum_${item.key}`));
			}
		});
		setApplyRefundNumCount(applyRefundNumCount);
		setAlreadyRefundNumCount(alreadyRefundNumCount);
		setReceiveNumCount(receiveNumCount);
		return { applyRefundNumCount, alreadyRefundNumCount, receiveNumCount };
	};

	// 获取勾选的行信息
	const getSelectedRows = () => {
		const selectedRows = [];
		const dataSourceTemp = getDataSource() || [];
		const selectedRowKeysTemp = getSelectedRowKeys() || [];

		// itemIdList 不维护了，找到主商品的备注给子商品
		dataSourceTemp.forEach(item => {
			if (selectedRowKeysTemp.includes(item.key)) {
				if (item?.rowType === 'order') {
					selectedRows.push(item);
				} else {
					// 找到当前的主商品
					const curOrderRowData = dataSourceTemp.find(i => i.key === item.belongOrderRowKey) || {};
					selectedRows.push({
						...item,
						buyerMessage: curOrderRowData?.buyerMessage || '',
						sellerMemo: curOrderRowData?.sellerMemo || '',
					});
				}
			}
		});
		return selectedRows;
	};

	// 获取详细地址
	const getReceiverAddress = (orderInfo) => {
		const { receiverState = "", receiverCity = "", receiverDistrict = "", receiverAddress = "", receiverName = "", receiverMobile = "" } = orderInfo;
		return (
			<span>
				{`${receiverState}${receiverCity}${receiverDistrict}${receiverAddress}`}&nbsp;&nbsp;
				{`(${receiverName} ${receiverMobile})`}
			</span>
		);

	};
	// 获取换货详细地址
	const getExchangeReceiverAddress = (orderInfo) => {
		const { exchangeReceiverProvince = "", exchangeReceiverCity = "", exchangeReceiverTown = "", exchangeReceiverAddressMask = "", } = orderInfo;
		return (
			<span>
				{`${exchangeReceiverProvince}${exchangeReceiverCity}${exchangeReceiverTown}${exchangeReceiverAddressMask}`}
			</span>
		);
	};
	// 当选择行的时候
	const onRowSelected = (selected, record) => {
		const { rowType, key, belongOrderRowKey, relateGoodsRowKeys = [] } = record;
		let selectedRowKeysTemp = cloneDeep(getSelectedRowKeys());
		if (selected) {
			selectedRowKeysTemp.push(key);
			// 如果勾选的是订单，需要全选订单下的商品行
			if (rowType === 'order') {
				selectedRowKeysTemp = selectedRowKeysTemp.concat(relateGoodsRowKeys);
			} else if (!selectedRowKeysTemp.includes(belongOrderRowKey)) {
				// 否则如果勾选的是商品行，需要把订单行也勾选上
				selectedRowKeysTemp.push(belongOrderRowKey);
			}
		} else if (rowType === 'order') {
			// 如果取消勾选的是订单，需要取消订单下的商品行勾选
			selectedRowKeysTemp = selectedRowKeysTemp.filter(i => i !== key && !relateGoodsRowKeys.includes(i));
		} else {
			selectedRowKeysTemp = selectedRowKeysTemp.filter(i => i !== key);
			// 如果取消勾选的是订单下的最后一个勾选的商品，那订单也要取消勾选
			const curOrderRowData = dataSource.find(i => i.key === belongOrderRowKey) || {};
			const curOrderSelectedGoodsRowKeys = selectedRowKeysTemp.filter(i => curOrderRowData?.relateGoodsRowKeys?.includes(i));
			if (curOrderSelectedGoodsRowKeys?.length === 0) {
				selectedRowKeysTemp = selectedRowKeysTemp.filter(i => i !== belongOrderRowKey);
			}
		}

		setSelectedRowKeys(selectedRowKeysTemp);
	};

	const getReceiveNum = (item) => {
		/**
		 * 如果用户开启了收货数量填充开关，或者手动添加了商品或者货品
		 * 则默认本次收货数量等于申请数量减去已收数量
		 * 否则本次收货数量等于0
		 */
		const applyRefundNum = item.applyRefundNum || 1;
		const hasRefundNum = item.hasRefundNum || 0;
		if (getReceiveNumFill() || ["sys", "item"].includes(item.addType)) {
			return (applyRefundNum - hasRefundNum < 0 ? 0 : applyRefundNum - hasRefundNum);
		}
		return 0;
	};


	// 处理dataSource数据
	const handleDataSource = async(data = []) => {

		// 保存一份匹配到的售后订单的店铺信息，平台信息，因为手动添加的商品或者货品有时候需要用到
		let matchPlatform = "";
		let matchShopInfo = {
			sellerId: "",
			sellerNick: "",
			sellerAbbreviation: "",
			source: ""
		};
		const res = data[0] || {};
		matchPlatform = res.platform;
		matchShopInfo = {
			sellerId: res.sellerId,
			sellerNick: res.sellerNick,
			sellerAbbreviation: res.sellerAbbreviation,
			source: res.source
		};
		setMatchPlatform(matchPlatform);
		setMatchShopInfo(matchShopInfo);
		/**
     * 如果匹配到售后单或者销售单，table是需要展示订单信息的，但是如果没有匹配到订单，
     * 用户需要手动添加商品，这个时候table是不需要展示订单信息的，只需要展示商品/货品信息即可
     */
		const newDataSource = [];
		let selectedRowKeysTemp = cloneDeep(getSelectedRowKeys());
		let applyRefundNums = 0; // 申请数量总和，排查换货商品

		 // 先收集所有需要查询的 skuId
		 const allSkuIds = [];
		 const allSysSkuIds = [];
		 data.forEach(orderItem => {
			 orderItem.refundItemRecordInfos?.forEach(item => {
				if (!isShowZeroStockVersion && item.sysSkuId) {
					allSysSkuIds.push(item.sysSkuId);
				} else {
					allSkuIds.push(item.skuId);
				}
			 });
		 });

		data.forEach((orderItem, orderItemIndex) => {
			const theOrderAndGoodsRows = [];
			const orderRowKey = `${ORDER_ROW_START_KEY}_${orderItemIndex}_${orderItem.tid}_${orderItem.refundId}`;
			const goodsRowKeys = [];
			const itemIdList = [];
			const skuIdList = [];
			const refundItemTypeList = [];
			let ordersRowKeys = [];
			// 处理商品/货品信息
			orderItem.refundItemRecordInfos?.forEach((item, itemIndex) => {
				let needPush = true;
				/**
				 * 如果是仅退款的售后单，不需要展示平台同步过来的商品/货品
				 */
				// if (orderItem.afterSaleType === afterSaleTypeText.仅退款) {
				// 	needPush = item.refundItemSource !== 0 || item.sysRefundItemSource !== 0;
				// }
				/**
				 * 如果是换货的售后单，仅展示商品类型为退货的商品
				 * 扫描单号匹配到换货订单时，展示换货商品，并标识货品类型，换货商品默认不勾选
				 */
				// if (orderItem.afterSaleType === afterSaleTypeText.换货) {
				// 	needPush = item.refundItemType == RefundItemTypeEnum.退货 || item.sysRefundItemType == RefundItemTypeEnum.退货;
				// }
				// if (!needPush) {
				// 	return;
				// }
				const goodRowKey = `${GOOD_ROW_START_KEY}_${itemIndex}_${orderRowKey}`;
				const getShowType = () => {
					let showType = "";
					if (item.numIid) {
						showType = "item";
					}
					if (item.sysItemId) {
						showType = "sys";
					}
					if (item.numIid && item.sysItemId) {
						showType = "all";
					}
					return showType;
				};
				itemIdList.push(item.numIid, item.sysItemId);
				skuIdList.push(item.skuId, item.sysSkuId);

				let refundItemTypeStr1 = `${item.refundItemType}_${item.numIid}_${item.skuId}`;
				let refundItemTypeStr2 = `${item.sysRefundItemType}_${item.sysItemId}_${item.sysSkuId}`;
				if (!refundItemTypeList.includes(refundItemTypeStr1) || !refundItemTypeList.includes(refundItemTypeStr2)) {
					refundItemTypeList.push(refundItemTypeStr1);
					refundItemTypeList.push(refundItemTypeStr2);
				}

				const theReceiveNum = getReceiveNum(item);
				const goodRowItem = {
					stall: item.stall,
					market: item.market,
					supplierName: item.supplierName,
					key: goodRowKey,
					buildType: orderItem.buildType,
					belongOrderRowKey: orderRowKey,
					rowType: "good",
					showType: getShowType(),
					serialNum: itemIndex + 1,
					picUrl: item.picUrl,
					title: item.title,
					freight: orderItem.freight,
					skuName: item.skuName,
					outerId: item.outerId,
					outerSkuId: item.outerSkuId,
					sysItemAlias: item.sysItemAlias,
					sysOuterSkuId: item.sysOuterSkuId,
					sysSkuAlias: item.sysSkuAlias,
					sysSkuName: item.sysSkuName,
					platform: orderItem.platform,
					endRow: orderItem.refundItemRecordInfos.length - itemIndex === 1,
					refundAmount: orderItem.refundAmount || 0,
					applyRefundNum: item.applyRefundNum || 0,
					hasRefundNum: item.hasRefundNum || 0,
					numIid: item.numIid,
					oid: item.orderId,
					originData: {
						salableItemDistributableStock: item?.salableItemDistributableStock, // 可配货库存库存数据
						existWaitSendTrade: false, // 待发货订单数量
						backOrderLoading: true, // 是否正在请求待发货订单
						serialNum: orderItemIndex + 1,
						exceptionType: item.exceptionType,
						platform: orderItem.platform,
						tid: orderItem.tid,
						ptTid: orderItem.ptTid,
						ptOid: orderItem.ptOid,
						oid: item.orderId,
						sellerNick: orderItem.sellerNick,
						sellerId: orderItem.sellerId,
						sellerAbbreviation: orderItem.sellerAbbreviation,
						refundId: orderItem.refundId, // 售后单号
						localContent: orderItem?.localContent || '', // 线下备注
						numIid: item.numIid,
						skuId: item.skuId,
						picUrl: item.picUrl,
						title: item.title,
						skuName: item.skuName,
						itemAlias: item.itemAlias,
						outerId: item.outerId,
						outerSkuId: item.outerSkuId,
						sysRefundItemRecordId: item.sysRefundItemRecordId,
						itemRefundItemRecordId: item.itemRefundItemRecordId,
						refundItemSource: item.refundItemSource,
						refundItemType: item.refundItemType,
						sysRefundItemType: item.sysRefundItemType,
						sysItemId: item.sysItemId,
						sysSkuId: item.sysSkuId,
						sysPicUrl: item.sysPicUrl,
						sysItemName: item.sysItemName,
						sysSkuName: item.sysSkuName,
						sysItemAlias: item.sysItemAlias,
						sysOuterId: item.sysOuterId,
						sysOuterSkuId: item.sysOuterSkuId,
						sysSkuAlias: item.sysSkuAlias,
						sysItemNo: item.sysItemNo,
						market: item.market,
						stall: item.stall,
						supplierName: item.supplierName,
						supplierId: item.supplierId,
						isCombination: item.isCombination,
						companyName: orderItem.companyName,
						sid: orderItem.sid,
						source: orderItem.source,
						costPrice: item.costPrice,
						isDistributorUserPushRefund: orderItem?.isDistributorUserPushRefund, // 是否分销商的售后单
						storageId: orderItem?.storageId || '-1',
					},
					buyerMessage: orderItem.buyerMessage,
					sellerMemo: orderItem.sellerMemo,
				};

				/**
				 * 如果用收货数量大于0的时候，默认给用户勾选上当前商品，换货的除外
				 */
				if (theReceiveNum > 0 && ![refundItemTypeEnum.换货].includes(goodRowItem?.originData?.refundItemType || goodRowItem?.originData?.sysRefundItemType)) {
					ordersRowKeys.push(goodRowKey);
				}

				if (![refundItemTypeEnum.换货, refundItemTypeEnum.补发].includes(goodRowItem?.originData?.refundItemType || goodRowItem?.originData?.sysRefundItemType)) {
					applyRefundNums += Number(item?.applyRefundNum || 0);
				}

				// 下面这个代码严重的性能问题，引以为戒
				// setTimeout(() => {
				// 	if (theReceiveNum > 0 && goodRowItem.originData.refundItemType !== refundItemTypeEnum.换货) {
				// 		onRowSelected(true, goodRowItem);
				// 	}
				// }, itemIndex);

				goodsRowKeys.push(goodRowKey);
				theOrderAndGoodsRows.push(goodRowItem);
			});
			// 处理订单信息
			const orderRowInfo = {
				...orderItem,
				key: orderRowKey,
				startRow: true,
				rowType: "order",
				serialNum: orderItemIndex + 1,
				buildType: orderItem.buildType,
				exception: orderItem.exceptionType === 1,
				platform: orderItem.platform,
				sellerNick: orderItem.sellerNick,
				sellerId: orderItem.sellerId,
				tid: orderItem.tid,
				ptTid: orderItem.ptTid,
				refundId: orderItem.refundId,
				refundReason: orderItem.refundReason,
				buyerNick: orderItem.buyerNick,
				receiverName: orderItem.receiverName,
				receiverAddress: getReceiverAddress(orderItem),
				exchangeReceiverAddressMask: getExchangeReceiverAddress(orderItem), // 换货地址
				sellerMemo: orderItem.sellerMemo,
				sellerFlag: orderItem.sellerFlag,
				sellerFlagTag: orderItem?.sellerFlagTag,
				relateGoodsRowKeys: goodsRowKeys,
				skuIdList: skuIdList.filter(Boolean), // 当前订单下的skuId
				itemIdList: itemIdList.filter(Boolean), // 当前订单下的itemId
				refundItemTypeList, // 区分不同的售后类型
				desc: orderItem.desc,
				originData: orderItem,
				localContent: orderItem[OfflineMemoEnum.线下备注内容],
				sid: orderItem.sid,
				id: orderItem.id,
				isDistributorUserPushRefund: orderItem?.isDistributorUserPushRefund, // 是否分销商的售后单
				afterSaleSource: orderItem?.afterSaleSource, // 新的售后来源字段
				buyerMessage: orderItem.buyerMessage,
			};
			// 如果商品全部勾选，需要订单也勾选
			selectedRowKeysTemp.push(...ordersRowKeys);
			if (ordersRowKeys?.length > 0 && ordersRowKeys?.length == goodsRowKeys?.length) {
				selectedRowKeysTemp.push(orderRowKey);
			}


			theOrderAndGoodsRows.unshift(orderRowInfo);
			newDataSource.push(...theOrderAndGoodsRows);
		});
		setDataSource(newDataSource);
		setSelectedRowKeys(selectedRowKeysTemp);

		if (applyRefundNums > 0 && !getHasPlayedVoice()) { // 已申请的数量大于0
			handleAudio(applyRefundNums);
		}


		// 这里只能通过columnsRef来获取最新值 。。。
		if (newDataSource?.length && columnsRef.current?.find(i => i.dataIndex === 'existWaitSendTrade')) {
			updateTradeOrderData(newDataSource, allSkuIds, allSysSkuIds);
		}

	};


	// 更新待发货订单数据
	const updateTradeOrderData = async(currentDataSource, allSkuIds, allSysSkuIds) => {
		try {
			const { orderData, sysOrderData } = await getTradeOrderData(allSkuIds, allSysSkuIds);
			// 更新dataSource中的existWaitSendTrade
			const updatedDataSource = currentDataSource.map(item => {
				if (item.rowType == 'order') {
					return item;
				}

				const orderInfo = !isShowZeroStockVersion && item?.originData?.sysSkuId
					? sysOrderData.find(order => order.sysSkuId === item?.originData?.sysSkuId)
					: orderData.find(order => order.skuId === item?.originData?.skuId);

				return {
					...item,
					originData: {
						...item.originData,
						existWaitSendTrade: !!(orderInfo?.total),
						backOrderLoading: false,
					}
				};
			});

			setDataSource(updatedDataSource);
		} catch (error) {
			console.log('%c [ error ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', error);
			// 如果是取消请求，直接返回空数据
			if (error?.includes('取消待发货订单数据请求')) {
				console.log('%c [ 取消待发货订单数据请求 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;');
				return;
			}
			const updatedDataSource = currentDataSource?.map(item => {
				if (item.rowType == 'order') {
					return item;
				}
				return {
					...item,
					originData: {
						...item.originData,
						backOrderLoading: false,
					}
				};
			});
			setDataSource(updatedDataSource);
		}
	};

	// 检查并更新待发货订单数据的辅助函数
	const checkAndUpdateTradeOrderData = (dataSourceToCheck) => {
		// 检查是否需要显示待发货订单列
		if (columnsRef.current?.find(i => i.dataIndex === 'existWaitSendTrade')) {
			const orderList = dataSourceToCheck?.filter(item => item.rowType === 'good') || [];
			const needList = orderList?.filter(item => isUndefined(item.originData?.existWaitSendTrade) || item.originData?.backOrderLoading) || [];
			const skuIdList = needList?.filter(item => item.originData?.skuId && !item.originData?.sysSkuId).map(item => item.originData?.skuId) || [];
			const sysSkuIdList = needList?.filter(item => item.originData?.sysSkuId).map(item => item.originData?.sysSkuId) || [];
			
			console.log('%c [ needList ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', 'needList', needList, 'skuIdList', skuIdList, 'sysSkuIdList', sysSkuIdList);
			if (skuIdList?.length || sysSkuIdList?.length) {
				if (tokens.has('getTradeOrderData1')) {
					tokens.get('getTradeOrderData1').cancel('取消请求：添加商品取消待发货订单数据请求');
					tokens.delete('getTradeOrderData1');
					console.log('%c [ 添加商品时候，取消之前的待发货订单数据请求 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', 'getTradeOrderData1');
				}
				if (tokens.has('getTradeOrderData2')) {
					tokens.get('getTradeOrderData2').cancel('取消请求：添加商品取消待发货订单数据请求');
					tokens.delete('getTradeOrderData2');
					console.log('%c [ 添加商品时候，取消之前的待发货订单数据请求 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', 'getTradeOrderData2');
				}
				updateTradeOrderData(dataSourceToCheck, skuIdList, sysSkuIdList);
			}
		}
	};

	// 获取待发货订单数据
	const getTradeOrderData = async(skuIds: string[] = [], sysSkuIds: string[] = []) => {
		let orderData = [];
		let sysOrderData = [];
    
		// 分别请求普通商品和系统商品的待发货订单数据
		const requests = [];
		if (skuIds.length) {
			requests.push(
				TradeGetOrderCountGroupByDetailsApi({
					skuIdList: skuIds,
					sysSkuIdList: [],
				}, 'getTradeOrderData1').then(res => {
					orderData = res || [];
				})
			);
		}
		if (sysSkuIds.length) {
			requests.push(
				TradeGetOrderCountGroupByDetailsApi({
					skuIdList: [],
					sysSkuIdList: sysSkuIds,
				}, 'getTradeOrderData2').then(res => {
					sysOrderData = res || [];
				})
			);
		}
		// 等待所有请求完成
		if (requests.length) {
			await Promise.all(requests);
		}

		return {
			orderData, // skuIdList参数请求的结果
			sysOrderData // sysSkuIdList参数请求的结果
		};
	};

	// 多件语音提示
	const handleAudio = (itemNum) => {
		if (itemNum > 5) {
			playAudio(voiceMoreThan5);
			setHasPlayedVoice(true);
		} else {
			switch (itemNum) {
				case 2:
					playAudio(voice2);
					setHasPlayedVoice(true);
					break;
				case 3:
					playAudio(voice3);
					setHasPlayedVoice(true);
					break;
				case 4:
					playAudio(voice4);
					setHasPlayedVoice(true);
					break;
				case 5:
					playAudio(voice5);
					setHasPlayedVoice(true);
					break;
				default:
					// 单件售后单不需要提示
					break;
			}
		}
	};

	// 语音提示顺序：【售后换货】＞ 留言备注提示＞商品数量
	const playVoiceByScene = ({ tradeInfo, audio }:{tradeInfo?:any, audio?:any}) => {
		if (!getHasPlayedVoice()) {
			if (audio) {
				playAudio(audio);
				setHasPlayedVoice(true);
				return;
			}
			const playRefundItemType = tradeInfo.refundItemRecordInfos.some(item => item.refundItemType == refundItemTypeEnum.换货); // 有售后换货订单
			const playMessageVoice = tradeInfo.buyerMessage && getMemoFlagConfig()?.includes("message");
			const playMemoVoice = tradeInfo.sellerMemo && getMemoFlagConfig()?.includes("memo");
			const playFlagVoice = flagAudio[tradeInfo.sellerFlag] && getMemoFlagConfig()?.includes("flag");
			if (playRefundItemType) {
				playAudio(售后换货);
				setHasPlayedVoice(true);
				return;
			}
			if (playMessageVoice) {
				playAudio(买家留言);
				setHasPlayedVoice(true);
				return;
			}
			if (playMemoVoice) {
				playAudio(卖家备注);
				setHasPlayedVoice(true);
				return;
			}
			if (playFlagVoice) {
				playAudio(flagAudio[tradeInfo.sellerFlag]);
				setHasPlayedVoice(true);
				return;
			}
		}
	};

	const handleResult = async(res, other = { buildType: "", scanRecordId: "" }) => {
		setHasPlayedVoice(false);
		if (res && res.length > 0) {

			res.forEach(tradeInfo => {
				playVoiceByScene({ tradeInfo });
				// 不再由前端过滤 子账号没有店铺权限且非无主件的售后单，由后端过滤
			});

			handleDataSource(res);
			setBuildType(res[0].buildType);
		} else {
			playVoiceByScene({ audio: 未匹配售后单 });
			setBuildType(3);
			handleDataSource([{
				buildType: other.buildType,
				scanRecordId: other.scanRecordId
			}]);
		}
	};


	// 确认收货时触发
	const onConfirmReceive = () => {
		console.timeEnd('EVENT_BUS.CONFIRM_RECEIVE');
		if (!getDataSource().length) {
			customLogPost('没有收货数据', {});
			return;
		}
		
		const selectedRows = getSelectedRows();
		// console.log('%c [ selectedRows ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', selectedRows);
		// 校验是否勾选订单
		if (selectedRows.length < 1) {
			message.warning({
				content: '请勾选商品后再确认收货'
			});
			
			customLogPost('请勾选商品后再确认收货', {});
			return;
		}
		let hasIgnoreException = false;
		// 如果匹配到是售后单，并且存在异常，需要校验是否添加过本地货品
		const exceptionSerialNums = []; // 收集有异常的订单序号
		const exceptionSerials = [];
		const selectedSellIdList = []; // 所有勾选的店铺
		const selectedPlatformList = []; // 所有勾选的平台
		selectedRows.forEach(row => {
			const { buildType, originData } = row;

			if (row.rowType === 'order') {
				let serialNumText = `序号${row.serialNum}`;
				/**
				 * 判断勾选的订单是否忽略了异常的时候，需要注意存在两种忽略：
				 * 1.一种是售后扫描登记页面弹窗的时候点击了忽略
				 * 2.另一种是在售后订单页面处理异常的时候点击了忽略，然后又来售后扫描登记进行操作
				 *  */
				if (row?.originData?.exceptionType === 2) { // 对于第2种，其实是需要忽略异常
					hasIgnoreException = true;
				}
				selectedSellIdList.push(row.sellerId);
				selectedPlatformList.push(row.platform);
				// exceptionType 0无异常 1异常 2忽略异常
				// buildType 1售后单 2销售单 3手动创建
				// buildType = 1，判断外层的exceptionType == 1，如果没有添加货品，弹出提示
				// buildType = 2，取明细集合refundItemRecordInfos里的exceptionType判断
				if (buildType == 1 && row.exception) {
					const isAddProducts = selectedRows.some(_row => {
						const hasAddProduct = _row.belongOrderRowKey === row.key && ['sys'].includes(_row.showType);
						return hasAddProduct;
					});
					if (!isAddProducts && !exceptionSerialNums?.includes(serialNumText)) {
						exceptionSerialNums.push(serialNumText);
						exceptionSerials.push({ buildType, oid: row.oid });
						console.log('%c [ 未添加货品 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', buildType, row.serialNum);
					}
				}
			} else if (row.rowType === 'good') {
				if (buildType == 2 && originData?.exceptionType == 1 && !['sys'].includes(row.showType)) {
					let serialNumText = `序号${originData.serialNum}`;
					if (!exceptionSerialNums.includes(serialNumText)) {
						exceptionSerialNums.push(serialNumText);
						exceptionSerials.push({ buildType, oid: row.oid });
					}
				}
			}
		});
		// 校验是否选了多个平台
		if ([...new Set(selectedPlatformList)].length > 1) {
			message.warning('只能对同一平台的订单进行收货！');

			customLogPost('只能对同一平台的订单进行收货！', { selectedPlatformList });
			return;
		}
		// 校验是否选了多个店铺
		if ([...new Set(selectedSellIdList)].length > 1) {
			message.warning('只能对同一店铺的订单进行收货！');
			
			customLogPost('只能对同一店铺的订单进行收货！', { selectedSellIdList });
			return;
		}

		// 校验表单是否正确填写
		setIsFormValidateFieldsFn(true);
		const { searchType, searchValue } = searchInstance?.current?.searchForm?.getFieldsValue();

		form.validateFields().then(res => {
			const isInventoryDeductOpen = userStore.systemSetting?.inventoryDeduct === inventoryDeduct.需要扣减库存;
			// 零库存板不做关联校验
			// 库存版未开启库存扣减，不做关联校验
			if (exceptionSerialNums.length > 0 && getVersion() !== 2 && isInventoryDeductOpen) {
				const serialNumsText = exceptionSerialNums.join('，');
				Modal.confirm({
					title: '系统提醒',
					content: `售后订单【${serialNumsText}】未关联本地货品，请添加本地货品并勾选后重新确认收货`,
					okText: '关闭',
					cancelText: '强制收货',
					onCancel() {
						customLogPost('售后订单未关联本地货品,忽略异常,强制收货', { exceptionSerialNums, exceptionSerials, searchType, searchValue });
						console.time('EVENT_BUS.CONFIRM_RECEIVE_VALIDATE_SUCCESS');
						event.emit(EVENT_BUS.CONFIRM_RECEIVE_VALIDATE_SUCCESS, res, selectedRows, true); // 忽略异常，强制收货
					},
					onOk() {
						customLogPost('售后订单未关联本地货品，取消继续收货', { exceptionSerialNums, exceptionSerials, searchType, searchValue });
					}
				});
				return;
			}
			console.time('EVENT_BUS.CONFIRM_RECEIVE_VALIDATE_SUCCESS');
			event.emit(EVENT_BUS.CONFIRM_RECEIVE_VALIDATE_SUCCESS, res, selectedRows, hasIgnoreException);
		}).catch((error) => {
			customLogPost('表格校验失败', { error, searchType, searchValue });
		}).finally(() => setIsFormValidateFieldsFn(false));

	};

	const modifyMemo = (record, index) => {
		setModifyMemoIndex(index);

		setModifyMemoPackage([{
			trades: [{
				sellerMemo: record.sellerMemo,
				sellerMemoFlag: record.sellerFlag,
				tid: record.tid,
				ptTid: record?.ptTid || '',
			}],
			source: record.source,
			platform: record.platform,
			togetherId: record.tid,
			sellerId: record.sellerId,
			refundStatus: record.refundStatus || "",
			afterSalesFlag: true,
		}]);
	};

	const modifyOfflineMemo = (item, index) => {
		setOfflineMemoData({ visible: true, list: [item], index });
	};

	// 编辑线下备注
	const handleOfflineMemoOk = ({ list }) => {
		setOfflineMemoData((prev) => {
			return {
				...prev,
				loading: true
			};
		});
		const newList = list.map((item) => ({
			...item,
			updateType: UpdateTypeEnum.线下备注,
			requestSource: AFTERSALE_REQUEST_SOURCE.单个扫描
		}));
		BatchUpdateLocalNoteApi(newList).then((res) => {
			setDataSource(prev => {
				prev[offlineMemoData?.index].localContent = list[0]?.localContent;
				const key = prev[offlineMemoData?.index].key;
				prev.forEach((item, index) => {
					if (item.belongOrderRowKey == key) {
						prev[index].originData.localContent = list[0]?.localContent;
					}
				});
				console.log('resres', prev[offlineMemoData?.index], prev);
				return [...prev];
			});
			setOfflineMemoData({ visible: false, loading: false });
			message.success("编辑成功");
		}).catch(() => {
			setOfflineMemoData(prev => ({ ...prev, loading: false }));
		});
	};

	const handleOfflineMemoCancel = () => {
		setOfflineMemoData({ visible: false });
	};

	// 渲染订单信息row
	const getOrderInfoContent = (record, index) => {
		return (
			<div className={ cs("r-flex", styles['order-info-container']) }>
				<div className="r-flex r-ai-c">序号{record.serialNum}</div>
				<div className="r-flex r-ai-c r-jc-c r-fs-14 r-bold" style={ { width: 60, color: "#f00" } }>
					{record.originData?.afterSaleType && afterSaleTypeText[record.originData?.afterSaleType]}
				</div>
				<div className="r-flex r-fd-c r-ml-10 r-flex-1">
					<div className="r-flex ">
						<div className="item shop">
							<div className="label">店铺：</div>
							<div className="value">
								<PlatformIcon platform={ record.platform } />
								{!userStore.hasFieldsPermission(FieldsPermissionEnum.供应商店铺信息) && isAfterSaleSourceScm(record) ? '****' : record.sellerNick}
							</div>
						</div>
						<div className="item buyer-nick">
							<div className="label">买家昵称：</div>
							<div className="value" style={ { marginTop: [PLAT_TB, PLAT_ALI].includes(record.platform) ? '-5px' : 'unset' } }>
								<BuyerNickComp ptTid={ record?.ptTid || '' } tid={ record.tid } encryptuid={ record.originData?.buyerOpenUid } platform={ record.platform } buyerNick={ record.buyerNick } sellerId={ record?.sellerId } />
							</div>
						</div>
						<div className="item receiver">
							<div className="label">收货人：</div>
							<div className="value">{record.receiverName}</div>
						</div>
						<div className="item buyer-address">
							<div className="label ">买家地址：</div>
							<div className="value">{record.receiverAddress}</div>
						</div>
						<div className="item buyer-address">
							<div className="label ">换货地址：</div>
							<div className="value">{record.exchangeReceiverAddressMask}</div>
						</div>
					</div>
					<div className="r-flex ">
						<div className="item refund-id">
							<div className="label">售后单号：</div>
							<div className="value">
								<a
									className={ cs('r-fc-1890FF') }
									href={ getPlatformTradeLink(record['platform'], record).refund }
									target="_blank"
									rel="noreferrer"
								>{record.refundId}
								</a>
								<CopyOutlined onClick={ () => { copyToPaste(record.refundId); } } className={ cs('r-fc-black-65', 'r-pointer', 'r-ml-4', 'r-mr-5') } />
								<span className="r-c-error r-fw-500">{record.originData?.refundStatusDesc}</span>
							</div>
						</div>

						<div className="item order-id" style={ { whiteSpace: 'pre-wrap' } }>
							<div className="label">系统单号：</div>
							<div className="value">
								<a
									className={ cs('r-fc-1890FF') }
									href={ getPlatformTradeLink(record['platform'], record).trade }
									target="_blank"
									rel="noreferrer"
								>{record.tid}
								</a>
								<CopyOutlined onClick={ () => { copyToPaste(splitFxgTid(record.tid)); } } className={ cs('r-fc-black-65', 'r-pointer', 'r-ml-4', 'r-mr-5') } />
							</div>
						</div>

						<div className="item order-id" style={ { whiteSpace: 'pre-wrap' } }>
							<div className="label">订单编号：</div>
							<div className="value">
								<a
									className={ cs('r-fc-1890FF') }
									href={ getPlatformTradeLink(record['platform'], record).trade }
									target="_blank"
									rel="noreferrer"
								>{record.ptTid}
								</a>
								<CopyOutlined onClick={ () => { copyToPaste(splitFxgTid(record?.ptTid || '')); } } className={ cs('r-fc-black-65', 'r-pointer', 'r-ml-4', 'r-mr-5') } />
								<span className="r-c-error r-fw-500">{record.originData?.orderStatusDesc}</span>
							</div>
						</div>

						<div className="item order-id" style={ { whiteSpace: 'pre-wrap' } }>
							<div className="label">原订单实付金额：</div>
							<div className="value">
								<span>{record?.originData?.payment || 0}元</span>
								<span>{`(含运费：${record?.originData?.postFee || 0}元)`}</span>
							</div>
						</div>
					</div>
					<div className="r-flex r-mb-4">
						<div className="item refund-reason">
							<div className="label">售后原因：</div>
							<div className="value">
								{record.refundReason}
							</div>
						</div >
						<div className="item refund-reason">
							<div className="label">售后说明：</div>
							<div className="value">
								{record.desc}
							</div>
						</div >
						<div className="item order-memo">
							<div className="label">订单备注：</div>
							{getTradeFlag(0, null, record.sellerFlag)}
							{getTradeFlagTag(record?.sellerFlag, record?.sellerFlagTag, { whiteSpace: 'nowrap', height: 18 })}
							<div className="r-ml-5" style={ { wordBreak: 'break-all' } }>{record.sellerMemo}</div>
							{!record.isDistributorUserPushRefund && !(record?.buildType === 3 || (record?.isNoTradeMess === true && record?.buildType === 1)) && (
								<div className="r-as-c r-ml-10">
									{!record.refundTagList?.includes('jxTrade') && (
										<>
											{record.sellerMemo
												? <span className="r-pointer" onClick={ () => { modifyMemo(record, index); } }><Icon type="bianji" /></span>
												: <span className="r-pointer" onClick={ () => { modifyMemo(record, index); } }><Icon type="tianjiabeizhu" /></span>}
										</>
									)}
								</div>
							)}
						</div>
						<div className="item order-memo">
							<div className="label">线下备注：</div>
							<span className={ cs('r-fc-black-65') }>{record[listItemName.线下备注]}</span>
							<span className="r-as-c r-ml-2">
								{record[listItemName.线下备注]
									? <span className="r-pointer" onClick={ () => { modifyOfflineMemo(record, index); } }><Icon type="bianji" /></span>
									: <span className="r-pointer" onClick={ () => { modifyOfflineMemo(record, index); } }><Icon type="tianjiabeizhu" /></span>}
							</span>
						</div>
					</div>
					<div className="r-flex r-ai-c">
						<div className="label">线下备注图片：</div>
						<div className="r-flex r-fw-w">
							<p>
								{record.localContentPicList && record.localContentPicList.length ? (
									<div className="r-flex r-ai-c">
										{/* 修改为循环展示所有图片，从左到右排列，超出边界才换行 */}
										<div className="r-flex r-ai-c r-fw-w" style={ { maxWidth: '300px', display: 'flex', flexDirection: 'row' } }>
											{record.localContentPicList.map((imgUrl, imgIndex) => (
												<Popover
													key={ imgIndex }
													placement="right"
													content={ <Image src={ imgUrl } style={ { width: '300px' } } /> }
												>
													<div style={ { display: 'inline-block', marginRight: '4px', marginBottom: '4px' } }>
														<Image
															src={ imgUrl }
															width={ 40 }
															height={ 40 }
															style={ { objectFit: 'cover' } }
														/>
													</div>
												</Popover>
											))}
										</div>
										<span className="r-as-c r-ml-2 r-fc-1890FF">
											<span className="r-pointer" onClick={ (e) => { e.stopPropagation(); modifyOfflineMemoPic(record, index); } }><Icon type="bianji" /></span>
										</span>
									</div>
								) : (
									<span className="r-as-c r-fc-1890FF">
										<span className="r-pointer" onClick={ (e) => { e.stopPropagation(); modifyOfflineMemoPic(record, index); } }><Icon type="tianjiabeizhu" /></span>
									</span>
								)}
							</p>
						</div>
					</div>
				</div>
			</div>
		);

	};

	// 渲染商品信息列
	const getGoodsInfoNode = (_, row) => {
		const imgSize = productContentList.includes(PRODUCT_CONTENT_ENUM_SP.小图) ? '300px'
			: productContentList.includes(PRODUCT_CONTENT_ENUM_SP.中图) ? '500px'
				: productContentList.includes(PRODUCT_CONTENT_ENUM_SP.大图) ? '800px' : '500px';
		const showTitle = productContentList.includes(PRODUCT_CONTENT_ENUM_SP.商品标题);
		const showSysItemAlias = productContentList.includes(PRODUCT_CONTENT_ENUM_SP.简称);
		const showOuterId = productContentList.includes(PRODUCT_CONTENT_ENUM_SP.商家编码);
		const showSkuProperties = productContentList.includes(PRODUCT_CONTENT_ENUM_SP.规格名称);
		const showSysSkuAlias = productContentList.includes(PRODUCT_CONTENT_ENUM_SP.规格别名);
		const showOuterSkuId = productContentList.includes(PRODUCT_CONTENT_ENUM_SP.规格编码);
		const showSupplierName = productContentList.includes(PRODUCT_CONTENT_ENUM_SP.供应商) && version === 2;
		const showMarket = productContentList.includes(PRODUCT_CONTENT_ENUM_SP.市场) && version === 2;
		const showStall = productContentList.includes(PRODUCT_CONTENT_ENUM_SP.档口) && version === 2;

		const showPicUrl = productContentList.includes(PRODUCT_CONTENT_ENUM_SP.商品图片);

		// 当用户添加商品或者匹配的订单包含商品时才渲染商品列
		// console.log('%c [ showType ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', row.showType, row);
		const validateShowType = ['all', 'item'].includes(row.showType);
		const getLinkHref = getPlatformDetailLink(row.platform, row.numIid);
		if (validateShowType) {
			row = { ...row?.originData || {}, ...row };
			return (
				<div className={ styles['goods-info-container'] }>
					<RefundItemType data={ row.originData } type="simple" />

					{showPicUrl && (
						<div className="img-container">
							<Popover
								overlayClassName={ styles['popover'] }
								placement="right"
								content={ <Image style={ { width: imgSize, height: imgSize } } src={ row.picUrl || DEFAULT_IMG } /> }
							>
								<a
								// eslint-disable-next-line no-script-url
									href={ getLinkHref || 'javascript:void(0)' }
									target={ getLinkHref ? '_blank' : '_self' }
									rel="noopener noreferrer"
									className={ cs("r-flex", "r-ai-c") }
								>
									<Image src={ row.picUrl || DEFAULT_IMG } />
								</a>
							</Popover>
						</div>
					)}
					<div className="goods-content">
						<div>
							<div className="goods-name">
								{showTitle && <span>{row.title || ''}</span>}
								{showSysItemAlias && <span>{row.sysItemAlias || ''}</span>}
								{showOuterId && <span>{row.outerId ? `（${row.outerId}）` : ""}</span>}
							</div>
						</div>
						<div>
							<div className="goods-specification">
								{showSkuProperties && <span>{row.skuName || ''}</span>}
								{showSysSkuAlias && <span>{row.sysSkuAlias || ''}</span>}
								{showOuterSkuId && <span>{row.outerSkuId ? `（${row.outerSkuId}）` : ""}</span>}
								{showSupplierName && <span>{row.supplierName || ''}</span>}
								{showMarket && <span>{row.market || ''}</span>}
								{showStall && <span>{row.stall || ''}</span>}
							</div>

						</div>
					</div>
				</div>
			);
		} else {
			return (
				<div className="r-flex">
					<RefundItemType data={ row.originData } type="simple" />
				</div>
			);
		}
	};

	// 渲染货品信息列
	const getProductInfoNode = (_, row) => {
		// 当用户添加货品或者匹配的订单包含货品时才渲染货品列
		const validateShowType = ['all', 'sys'].includes(row.showType);

		const showSysItemAlias = productContentList.includes(PRODUCT_CONTENT_ENUM_HP.货品简称);
		const showOuterId = productContentList.includes(PRODUCT_CONTENT_ENUM_HP.货品编码);
		const showSkuProperties = productContentList.includes(PRODUCT_CONTENT_ENUM_HP.规格名称);
		const showOuterSkuId = productContentList.includes(PRODUCT_CONTENT_ENUM_HP.货品规格编码);
		const showSupplierName = productContentList.includes(PRODUCT_CONTENT_ENUM_HP.供应商);
		const showMarket = productContentList.includes(PRODUCT_CONTENT_ENUM_HP.市场);
		const showStall = productContentList.includes(PRODUCT_CONTENT_ENUM_HP.档口);

		const imgSize = productContentList.includes(PRODUCT_CONTENT_ENUM_HP.小图) ? '300px'
			: productContentList.includes(PRODUCT_CONTENT_ENUM_HP.中图) ? '500px'
				: productContentList.includes(PRODUCT_CONTENT_ENUM_HP.大图) ? '800px' : '500px';
		const showPicUrl = productContentList.includes(PRODUCT_CONTENT_ENUM_HP.货品图片);
		const getLinkHref = getPlatformDetailLink(row.platform, row.numIid);

		if (validateShowType) {
			row = { ...row?.originData || {}, ...row };
			return (
				<div className={ styles["product-container"] } >

					<div className="img-container" style={ { marginRight: '10px', paddingTop: '10px' } }>
						{showPicUrl && (
							<Popover
								overlayClassName={ styles['popover'] }
								placement="right"
								content={ <Image style={ { width: imgSize, height: imgSize } } src={ row.sysPicUrl || DEFAULT_IMG } /> }
							>
								<a
								// eslint-disable-next-line no-script-url
									href={ getLinkHref || 'javascript:void(0)' }
									target={ getLinkHref ? '_blank' : '_self' }
									rel="noopener noreferrer"
									className={ cs("r-flex", "r-ai-c") }
								>
									<Image width={ 80 } height={ 80 } src={ row.sysPicUrl || DEFAULT_IMG } />
								</a>
							</Popover>
						)}
					</div>

					<div>
						<div className="product-name">
							<span>{showSysItemAlias ? row.sysItemAlias : ''}</span>
							{showOuterId && <span>{row.sysOuterId ? `（${row.sysOuterId}）` : ""}</span>}
						</div>
						<div className="product-specification">
							{showSkuProperties && <span>{row.sysSkuAlias || row.sysSkuName}</span>}
							{showOuterSkuId && <span>{row.sysOuterSkuId ? `（${row.sysOuterSkuId}）` : ""}</span>}
							{showSupplierName && <span>{row.supplierName || ''}</span>}
							{showMarket && <span>{row.market || ''}</span>}
							{showStall && <span>{row.stall || ''}</span>}
						</div>
					</div>
				</div>
			);
		}

		return '';
	};

	// 渲染售后金额列
	const getAftersaleMountNode = (_, row) => {
		// 匹配到售后订单时，不允许修改售后金额
		const { buildType, key, freight, refundAmount, platform } = row;
		return (
			<div>
				<Form.Item name={ `refundFee_${key}` } initialValue={ refundAmount }>
					<Input disabled={ buildType === 1 } />
				</Form.Item>
				{
					platform == PLAT_KS && Number(freight)
						? (
							<div>
								退货运费:{freight}
							</div>
						)
						: null
				}
			</div>
		);

	};
	// 渲染申请数量列
	const getApplyRefundNumNode = (_, row) => {
		return <div>{row.applyRefundNum || 0}</div>;
	};

	const receiveNumOnChange = (value, row) => {
		/**
		 * 这里需要区分触发自定义校验的是手动触发，还是执行form.validateFields触发的。
		 * 不然确认收货时，form触发校验如果onRowSelected为false的时候，别的地方会拿不到勾选的数据
		 */
		if (getIsFormValidateFieldsFn()) {
			return;
		}

		// 当收货数量大于0时，勾选当前行，否则取消勾选
		if (value > 0) {
			onRowSelected(true, row);
		} else {
			onRowSelected(false, row);
		}
	};

	// 校验收货数量值
	const validatorReceiveNumValue = (value, row) => {
		const { applyRefundNum, hasRefundNum, key } = row;
		receiveNumOnChange(value, row);
		// 0≤ 收货数量最 ≤申请数量-已收/已退数量
		const maxReceiveNum = applyRefundNum - hasRefundNum;
		// if (value > maxReceiveNum && selectedRowKeys.includes(key)) {
		// 	return Promise.reject(new Error(`收货数量不能超过申请数量-已收/已退数量`));
		// }
		return Promise.resolve();
	};

	// 当收货数量增加时
	const onClickReceiveNumPlus = (formName, row) => {
		sendPoint(Pointer.售后_售后扫描登记_实收数量);
		const curFormItemValue = form.getFieldValue(formName);
		const value = curFormItemValue + 1;
		form.setFieldsValue({
			[formName]: value
		});
		receiveNumOnChange(value, row);
		handleCountNum();

	};

	// 当收货数量减去时
	const onClickReceiveNumMinus = (formName, row) => {
		sendPoint(Pointer.售后_售后扫描登记_实收数量);
		const curFormItemValue = form.getFieldValue(formName);
		if (curFormItemValue > 0) {
			const value = curFormItemValue - 1;
			form.setFieldsValue({
				[formName]: value
			});
			receiveNumOnChange(value, row);
			handleCountNum();
		}
	};

	// 渲染本次收货数量列
	const getReceiveNumNode = (_, row) => {
		const formName = `receiveNum_${row.key}`;
		return (
			<div className={ styles['receive-num-container'] }>
				<div className="r-pointer">
					<MinusCircleFilled style={ { color: "#999" } } onClick={ () => onClickReceiveNumMinus(formName, row) } />
				</div>
				<div className="r-m-lr-4">
					<Form.Item
						name={ formName }
						initialValue={ getReceiveNum(row) }
						rules={ [{
							validator: (_, value) => validatorReceiveNumValue(value, row)
						}] }
					>
						<Input />
					</Form.Item>
				</div>
				<div className="r-pointer">
					<PlusCircleFilled style={ { color: "#999" } } onClick={ () => onClickReceiveNumPlus(formName, row) } />
				</div>
			</div>
		);
	};

	// 获取店铺是全选还是部分选择
	const getIndeterminate = (record) => {
		const { rowType, relateGoodsRowKeys = [], key } = record;
		if (rowType === 'order') {
			const orderHasSelectedAllGoods = relateGoodsRowKeys.every(i => {
				return selectedRowKeys.includes(i);
			});
			const orderHasChecked = selectedRowKeys.includes(key);
			return orderHasChecked && !orderHasSelectedAllGoods;
		}
		return false;

	};

	const rowSelection = {
		selectedRowKeys,
		onSelectAll(selected, selectedRows, changeRows) {
			if (selected) {
				const allRowKeys = dataSource.map(i => i.key);
				setSelectedRowKeys(allRowKeys);
			} else {
				setSelectedRowKeys([]);
			}
		},
		renderCell: (selected, record, index) => {
			return (
				<Checkbox
					checked={ selectedRowKeys.includes(record.key) }
					indeterminate={ getIndeterminate(record) }
					onChange={ (e) => onRowSelected(e.target.checked, record) }
				/>
			);
		}
	};

	// 自定义row的className
	const rowClassName = (record, index) => {
		let defaultClassName = [record.rowType];
		if (record.endRow) {
			defaultClassName.push('row-end');
		}
		if (record.startRow) {
			defaultClassName.push('row-start');
		}
		return defaultClassName.join(' ');
	};

	// 当点击添加商品时 (零库存)
	const onAddGooods = (selectedRows) => {
		if (selectedRows.length > 0) {
			const dataSourceTemp = cloneDeep(dataSource);
			const allRefundItemTypeList = dataSourceTemp.map(i => `${RefundItemTypeEnum.退货}_${i.itemId}_${i.skuId}`);
			// buildType 1匹配到售后单2-匹配到销售单3-手动创建
			if (getBuildType() !== 3) {
				// 1.找出所有勾选的订单，往订单下添加商品
				selectedRowKeys.forEach(rowKey => {
					if (rowKey.startsWith('order')) {
						let theOrderLastGoodsRowIndex = 0;
						let curOrderRow:AftersaleScanTableDataSourceItem = {};
						let curOrderOriginRelateGoodsRowKeys = [];
						dataSourceTemp.forEach((dataItem, index) => {
							// 1.找出勾选订单下的最后一行的商品行及其index
							if (dataItem.belongOrderRowKey === rowKey && dataItem.endRow) {
								theOrderLastGoodsRowIndex = index;
							}
							// 2.找出当前勾选的订单信息行
							if (dataItem.key === rowKey) {
								curOrderRow = dataItem;
								curOrderOriginRelateGoodsRowKeys = cloneDeep(dataItem.relateGoodsRowKeys);
							}
						});
						// 3.找出当前订单下的商品的skuId或者itemId，如果已经存在勾选的商品，则不需要重新添加
						const { key, relateGoodsRowKeys = [], buildType, refundItemTypeList } = curOrderRow;
						const validSelectedRows = selectedRows.filter(item => {
							const itemInfo = item.platformItemSkuList[0];
							let refundItemTypeStr = `${RefundItemTypeEnum.退货}_${item.numIid}_${itemInfo.skuId}`;
							if (!refundItemTypeList?.includes(refundItemTypeStr)) {
								refundItemTypeList.push(refundItemTypeStr);
								return true;
							}
							return false;
						});

						// 4.将勾选的商品处理成table的dataSource
						const theSelectedDataSource = [];
						validSelectedRows.forEach((item, itemIndex) => {
							const itemInfo = item?.platformItemSkuList?.[0] || {};
							const relationSystemItem = itemInfo?.relationSystemItemList?.[0] || {};
							const goodRowKey = `${GOOD_ROW_START_KEY}_${curOrderOriginRelateGoodsRowKeys.length + itemIndex}_${key}`;
							const goodRowItem = {
								key: goodRowKey,
								buildType,
								belongOrderRowKey: key,
								rowType: "good",
								showType: "item",
								serialNum: curOrderOriginRelateGoodsRowKeys.length + itemIndex + 1,
								title: item.title,
								skuName: itemInfo.skuName,
								picUrl: itemInfo.picUrl,
								sysPicUrl: null,
								sysItemAlias: null,
								sysSkuName: null,
								endRow: selectedRows.length - itemIndex === 1,
								refundAmount: item.price || 0,
								applyRefundNum: 1,
								hasRefundNum: 0,
								addType: 'item',
								skuId: itemInfo.skuId, // 这个id给删除商品时重新计算key使用，重复添加商品时也会用到
								itemId: item.numIid, // 这个id和skuId用于区分sku，重复添加商品时用到
								storageId: itemInfo?.storageId || '-1',
								originData: {
									// salableItemDistributableStock: itemInfo?.salableItemDistributableStock ?? relationSystemItem?.salableItemDistributableStock, // 可配货库存数量
									backOrderLoading: true, // 是否正在请求待发货订单
									serialNum: curOrderRow?.serialNum,
									refundItemType: RefundItemTypeEnum.退货,
									sysRefundItemType: RefundItemTypeEnum.退货,
									tid: curOrderRow.originData.tid,
									ptTid: curOrderRow.originData.ptTid,
									ptOid: curOrderRow.originData?.ptOid || '',
									platform: item.platform,
									sellerId: item.sellerId,
									sellerNick: item.sellerNick,
									refundId: curOrderRow?.originData?.refundId, // 退款编号
									localContent: curOrderRow?.originData?.localContent || '', // 线下备注
									numIid: item.numIid,
									skuId: itemInfo.skuId,
									picUrl: itemInfo.picUrl,
									title: item.title,
									skuName: itemInfo.skuName,
									itemAlias: item.title,
									outerId: item.outerId,
									costPrice: relationSystemItem?.costPrice,
									outerSkuId: itemInfo.skuOuterId,
									sysRefundItemRecordId: null,
									itemRefundItemRecordId: null,
									sysItemId: relationSystemItem?.sysItemId,
									sysSkuId: relationSystemItem?.sysSkuId,
									sysPicUrl: relationSystemItem?.sysPicUrl,
									sysItemName: relationSystemItem?.sysItemName,
									sysSkuName: relationSystemItem?.sysSkuName,
									sysItemAlias: relationSystemItem?.sysItemAlias,
									sysOuterId: relationSystemItem?.outerId,
									sysOuterSkuId: relationSystemItem?.skuOuterId,
									sysSkuAlias: relationSystemItem?.sysSkuAlias,
									sysItemNo: relationSystemItem?.itemNo,
									isCombination: relationSystemItem?.isCombination,
									market: relationSystemItem?.market,
									stall: relationSystemItem?.stall,
									supplierName: relationSystemItem?.supplierName,
									supplierId: relationSystemItem?.supplierId,
									isDistributorUserPushRefund: curOrderRow?.originData?.isDistributorUserPushRefund,
									storageId: itemInfo?.storageId || '-1',
								}
							};
							setTimeout(() => {
								onRowSelected(true, goodRowItem);
							}, itemIndex);
							relateGoodsRowKeys.push(goodRowKey);
							theSelectedDataSource.push(goodRowItem);
						});
						// 4.处理当前订单的relateGoodsRowKeys，把添加的新行的row加进去
						dataSourceTemp.forEach(i => {
							if (i.key === key) {
								i.relateGoodsRowKeys = relateGoodsRowKeys;
							}
						});
						if (validSelectedRows.length > 0) {
							// 5.把原本的最后一行的endRow置为false
							dataSourceTemp[theOrderLastGoodsRowIndex]['endRow'] = false;
							dataSourceTemp.splice(theOrderLastGoodsRowIndex + 1, 0, ...theSelectedDataSource);
							setDataSource(dataSourceTemp);

							// 延迟执行，确保 dataSource 已更新
							setTimeout(() => {
								checkAndUpdateTradeOrderData(dataSourceTemp);
							}, 0);
						}

					}
				});
			} else {
				const curLastRowIndex = dataSource.length;
				// 过滤掉已经添加过的商品
				const validSelectedRows = selectedRows.filter(item => {
					const itemInfo = item.platformItemSkuList[0];

					let refundItemTypeStr = `${RefundItemTypeEnum.退货}_${item.numIid}_${itemInfo.skuId}`;
					if (!allRefundItemTypeList.includes(refundItemTypeStr)) {
						return true;
					}
					return false;
				});
				// 1.先把要添加的商品处理成dataSourceItem
				validSelectedRows.forEach((item, itemIndex) => {
					const itemInfo = item?.platformItemSkuList?.[0] || {};
					const relationSystemItem = itemInfo?.relationSystemItemList?.[0] || {};
					const goodRowKey = `${GOOD_ROW_START_KEY}_${curLastRowIndex + itemIndex}_${itemInfo.skuId}`;
					const goodRowItem = {
						key: goodRowKey,
						buildType: 3,
						belongOrderRowKey: null,
						rowType: "good",
						showType: "item",
						serialNum: curLastRowIndex + itemIndex + 1,
						title: item.title,
						skuName: itemInfo.skuName,
						picUrl: itemInfo.picUrl,
						sysPicUrl: null,
						sysItemAlias: null,
						sysSkuName: null,
						endRow: selectedRows.length - itemIndex === 1,
						refundAmount: item.price || 0,
						applyRefundNum: 1,
						hasRefundNum: 0,
						addType: 'item',
						skuId: itemInfo.skuId, // 这个id给删除商品时重新计算key使用，重复添加商品时也会用到
						itemId: item.numIid, // 这个id和skuId用于区分sku，重复添加商品时用到
						storageId: itemInfo?.storageId || '-1',
						originData: {
							// salableItemDistributableStock: itemInfo?.salableItemDistributableStock ?? relationSystemItem?.salableItemDistributableStock, // 可配货库存数量
							backOrderLoading: true, // 是否正在请求待发货订单
							refundItemType: RefundItemTypeEnum.退货,
							sysRefundItemType: RefundItemTypeEnum.退货,
							oid: null,
							platform: item.platform,
							sellerId: item.sellerId,
							sellerNick: item.sellerNick,
							numIid: item.numIid,
							skuId: itemInfo.skuId,
							picUrl: itemInfo.picUrl,
							title: item.title,
							skuName: itemInfo.skuName,
							itemAlias: item.itemAlias,
							outerId: item.outerId,
							outerSkuId: itemInfo.skuOuterId,
							costPrice: relationSystemItem?.costPrice,
							sysRefundItemRecordId: null,
							itemRefundItemRecordId: null,
							sysItemId: relationSystemItem?.sysItemId,
							sysSkuId: relationSystemItem?.sysSkuId,
							sysPicUrl: relationSystemItem?.picUrl,
							sysItemName: relationSystemItem?.sysItemName,
							sysSkuName: relationSystemItem?.sysSkuName,
							sysItemAlias: relationSystemItem?.sysItemAlias,
							sysOuterId: relationSystemItem?.outerId,
							sysOuterSkuId: relationSystemItem?.skuOuterId,
							sysItemNo: relationSystemItem?.itemNo,
							sysSkuAlias: relationSystemItem?.sysSkuAlias,
							isCombination: relationSystemItem?.isCombination,
							market: relationSystemItem?.market,
							stall: relationSystemItem?.stall,
							supplierName: relationSystemItem?.supplierName,
							supplierId: relationSystemItem?.supplierId,
							isDistributorUserPushRefund: false,
							storageId: itemInfo?.storageId || '-1',
						}
					};
					setTimeout(() => {
						onRowSelected(true, goodRowItem);
					}, itemIndex);
					dataSourceTemp.push(goodRowItem);
				});
				// 2.重新计算dataSource的starRow和endRow
				dataSourceTemp.forEach((item, index) => {
					item.startRow = index === 1;
					item.serialNum = index <= 1 ? 1 : index;
					item.endRow = dataSourceTemp.length - 1 === index;
				});
				setDataSource(dataSourceTemp);

				// 延迟执行，确保 dataSource 已更新
				setTimeout(() => {
					checkAndUpdateTradeOrderData(dataSourceTemp);
				}, 0);
			}

		}
	};

	// 当点击添加换货商品时 (零库存)
	const onAddGooods2 = (selectedRows) => {
		if (selectedRows.length > 0) {
			const dataSourceTemp = cloneDeep(dataSource);
			// buildType 1匹配到售后单2-匹配到销售单3-手动创建
			if (getBuildType() === 1) {
				// 1.找出所有勾选的订单，往订单下添加商品
				selectedRowKeys.forEach(rowKey => {
					if (rowKey.startsWith('order')) {
						let theOrderLastGoodsRowIndex = 0;
						let curOrderRow:AftersaleScanTableDataSourceItem = {};
						let curOrderOriginRelateGoodsRowKeys = [];
						dataSourceTemp.forEach((dataItem, index) => {
							// 1.找出勾选订单下的最后一行的商品行及其index
							if (dataItem.belongOrderRowKey === rowKey && dataItem.endRow) {
								theOrderLastGoodsRowIndex = index;
							}
							// 2.找出当前勾选的订单信息行
							if (dataItem.key === rowKey) {
								curOrderRow = dataItem;
								curOrderOriginRelateGoodsRowKeys = cloneDeep(dataItem.relateGoodsRowKeys);
							}
						});
						// 3.找出当前订单下的商品的skuId或者itemId，如果已经存在勾选的商品，则不需要重新添加
						const { key, relateGoodsRowKeys = [], buildType, refundItemTypeList } = curOrderRow;

						// 判断当前订单是否满足添加换货条件 buildType == 1 && afterSaleType == 3 || 5
						if (![afterSaleTypeText.换货, afterSaleTypeText.补发货品].includes(curOrderRow.originData?.afterSaleType)) {
							return;
						}

						const validSelectedRows = selectedRows.filter(item => {
							const itemInfo = item.platformItemSkuList[0];

							let refundItemTypeStr = `${RefundItemTypeEnum.换货}_${item.numIid}_${itemInfo.skuId}`;
							if (!refundItemTypeList?.includes(refundItemTypeStr)) {
								refundItemTypeList.push(refundItemTypeStr);
								return true;
							}
							return false;
						});

						// 4.将勾选的商品处理成table的dataSource
						const theSelectedDataSource = [];
						validSelectedRows.forEach((item, itemIndex) => {
							const itemInfo = item?.platformItemSkuList?.[0] || {};
							const relationSystemItem = itemInfo?.relationSystemItemList?.[0] || {};
							const goodRowKey = `${GOOD_ROW_START_KEY}_${curOrderOriginRelateGoodsRowKeys.length + itemIndex}_${key}`;
							const goodRowItem = {
								key: goodRowKey,
								buildType,
								belongOrderRowKey: key,
								rowType: "good",
								showType: "item",
								serialNum: curOrderOriginRelateGoodsRowKeys.length + itemIndex + 1,
								title: item.title,
								skuName: itemInfo.skuName,
								picUrl: itemInfo.picUrl,
								sysPicUrl: null,
								sysItemAlias: null,
								sysSkuName: null,
								endRow: selectedRows.length - itemIndex === 1,
								refundAmount: item.price || 0,
								applyRefundNum: 1,
								hasRefundNum: 0,
								addType: 'item',
								skuId: itemInfo.skuId, // 这个id给删除商品时重新计算key使用，重复添加商品时也会用到
								itemId: item.numIid, // 这个id和skuId用于区分sku，重复添加商品时用到
								storageId: itemInfo?.storageId || '-1',
								originData: {
									// salableItemDistributableStock: itemInfo?.salableItemDistributableStock ?? relationSystemItem?.salableItemDistributableStock, // 可配货库存数量
									backOrderLoading: true, // 是否正在请求待发货订单
									serialNum: curOrderRow?.serialNum,
									refundItemType: RefundItemTypeEnum.换货,
									sysRefundItemType: RefundItemTypeEnum.换货,
									tid: curOrderRow.originData.tid,
									platform: item.platform,
									sellerId: item.sellerId,
									sellerNick: item.sellerNick,
									refundId: curOrderRow?.originData?.refundId, // 退款编号
									localContent: curOrderRow?.originData?.localContent || '', // 线下备注
									numIid: item.numIid,
									skuId: itemInfo.skuId,
									picUrl: itemInfo.picUrl,
									title: item.title,
									skuName: itemInfo.skuName,
									itemAlias: item.title,
									outerId: item.outerId,
									costPrice: relationSystemItem?.costPrice,
									outerSkuId: itemInfo.skuOuterId,
									sysRefundItemRecordId: null,
									itemRefundItemRecordId: null,
									sysItemId: relationSystemItem?.sysItemId,
									sysSkuId: relationSystemItem?.sysSkuId,
									sysPicUrl: relationSystemItem?.sysPicUrl,
									sysItemName: relationSystemItem?.sysItemName,
									sysSkuName: relationSystemItem?.sysSkuName,
									sysItemAlias: relationSystemItem?.sysItemAlias,
									sysOuterId: relationSystemItem?.outerId,
									sysOuterSkuId: relationSystemItem?.skuOuterId,
									sysSkuAlias: relationSystemItem?.sysSkuAlias,
									sysItemNo: relationSystemItem?.itemNo,
									isCombination: relationSystemItem?.isCombination,
									market: relationSystemItem?.market,
									stall: relationSystemItem?.stall,
									supplierName: relationSystemItem?.supplierName,
									supplierId: relationSystemItem?.supplierId,
									isDistributorUserPushRefund: curOrderRow?.originData?.isDistributorUserPushRefund,
									storageId: itemInfo?.storageId || '-1',
								}
							};
							setTimeout(() => {
								onRowSelected(true, goodRowItem);
							}, itemIndex);
							relateGoodsRowKeys.push(goodRowKey);
							theSelectedDataSource.push(goodRowItem);
						});
						// 4.处理当前订单的relateGoodsRowKeys，把添加的新行的row加进去
						dataSourceTemp.forEach(i => {
							if (i.key === key) {
								i.relateGoodsRowKeys = relateGoodsRowKeys;
							}
						});
						if (validSelectedRows.length > 0) {
							// 5.把原本的最后一行的endRow置为false
							dataSourceTemp[theOrderLastGoodsRowIndex]['endRow'] = false;
							dataSourceTemp.splice(theOrderLastGoodsRowIndex + 1, 0, ...theSelectedDataSource);
							setDataSource(dataSourceTemp);

							// 延迟执行，确保 dataSource 已更新
							setTimeout(() => {
								checkAndUpdateTradeOrderData(dataSourceTemp);
							}, 0);
						}

					}
				});
			}
		}
	};

	// 当点击添加货品时 （库存版）
	const onAddProducts = (selectedRows) => {
		if (selectedRows.length > 0) {
			console.log('selectedRowsselectedRows111', selectedRows, getBuildType());
			const dataSourceTemp = cloneDeep(dataSource);
			if (getBuildType() !== 3) {
				// 1.找出所有勾选的订单，往订单下添加商品
				selectedRowKeys.forEach((rowKey:string) => {
					if (rowKey.startsWith('order')) {
						let theOrderLastGoodsRowIndex = 0;
						let curOrderRow:AftersaleScanTableDataSourceItem = {};
						let curOrderOriginRelateGoodsRowKeys = [];
						dataSourceTemp.forEach((dataItem, index) => {
							// 1.找出勾选订单下的最后一行的商品行及其index
							if (dataItem.belongOrderRowKey === rowKey && dataItem.endRow) {
								theOrderLastGoodsRowIndex = index;
							}
							// 2.找出当前勾选的订单信息行
							if (dataItem.key === rowKey) {
								curOrderRow = dataItem;
								curOrderOriginRelateGoodsRowKeys = cloneDeep(dataItem.relateGoodsRowKeys);
							}
						});
						// 3.找出当前订单下的货品的skuId或者itemId，如果已经存在勾选的货品，则不需要重新添加
						const { key, relateGoodsRowKeys = [], buildType, refundItemTypeList } = curOrderRow;
						const validSelectedRows = selectedRows.filter(item => {
							const skuInfo = item.sysSkuList[0];

							let refundItemTypeStr = `${RefundItemTypeEnum.退货}_${skuInfo.sysItemId}_${skuInfo.sysSkuId}`;
							if (!refundItemTypeList?.includes(refundItemTypeStr)) {
								refundItemTypeList.push(refundItemTypeStr);
								return true;
							}
							return false;
						});
						// 4.将勾选的商品处理成table的dataSource

						const theSelectedDataSource = [];
						validSelectedRows.forEach((item, itemIndex) => {
							const skuInfo = item.sysSkuList[0];
							const goodRowKey = `${GOOD_ROW_START_KEY}_${curOrderOriginRelateGoodsRowKeys.length + itemIndex}_${key}`;
							const goodRowItem = {
								key: goodRowKey,
								buildType,
								belongOrderRowKey: key,
								rowType: "good",
								showType: "sys",
								serialNum: curOrderOriginRelateGoodsRowKeys.length + itemIndex + 1,
								title: '',
								skuName: '',
								picUrl: '',
								sysPicUrl: skuInfo.picUrl,
								sysItemAlias: skuInfo.sysItemAlias,
								sysSkuName: skuInfo.sysSkuName,
								endRow: selectedRows.length - itemIndex === 1,
								refundAmount: skuInfo.price || 0,
								storageId: skuInfo?.storageId || '-1',
								applyRefundNum: 1,
								hasRefundNum: 0,
								addType: 'sys',
								skuId: skuInfo.sysSkuId, // 这个id给删除货品品时重新计算key使用，重复添加货品时也会用到
								itemId: skuInfo.sysItemId, // 这个id和skuId用于区分sku，重复添加货品时用到
								originData: {
									salableItemDistributableStock: skuInfo?.salableItemDistributableStock, // 可配货库存数量
									backOrderLoading: true, // 是否正在请求待发货订单
									serialNum: curOrderRow?.serialNum,
									refundItemType: RefundItemTypeEnum.退货,
									sysRefundItemType: RefundItemTypeEnum.退货,
									tid: curOrderRow.originData.tid,
									ptTid: curOrderRow.originData.ptTid,
									ptOid: curOrderRow.originData?.ptOid || '',
									storageId: skuInfo?.storageId || '-1',
									numIid: null,
									skuId: null,
									picUrl: null,
									title: null,
									skuName: null,
									itemAlias: null,
									outerId: null,
									outerSkuId: null,
									sysRefundItemRecordId: null,
									itemRefundItemRecordId: null,
									costPrice: skuInfo.costPrice,
									sysItemId: skuInfo.sysItemId,
									sysSkuId: skuInfo.sysSkuId,
									sysPicUrl: skuInfo.picUrl,
									sysItemName: skuInfo.sysItemName,
									sysSkuName: skuInfo.sysSkuName,
									sysItemAlias: skuInfo.sysItemAlias,
									sysOuterId: skuInfo.outerId,
									sysOuterSkuId: skuInfo.skuOuterId,
									sysItemNo: item.sysItemNo,
									isCombination: skuInfo.isCombination,
									sysSkuAlias: skuInfo.sysSkuAlias,
									market: skuInfo.market,
									stall: skuInfo.stall,
									supplierName: skuInfo.supplierName,
									supplierId: skuInfo.supplierId,
									refundId: curOrderRow.originData.refundId,
									sellerNick: curOrderRow.originData.sellerNick,
									sellerId: curOrderRow.originData.sellerId,
									localContent: curOrderRow?.originData?.localContent || '', // 线下备注
									isDistributorUserPushRefund: curOrderRow?.originData?.isDistributorUserPushRefund,
								}
							};
							setTimeout(() => {
								onRowSelected(true, goodRowItem);
							}, itemIndex);
							relateGoodsRowKeys.push(goodRowKey);
							theSelectedDataSource.push(goodRowItem);
						});
						// 3.处理当前订单的relateGoodsRowKeys，把添加的新行的row加进去
						dataSourceTemp.forEach(i => {
							if (i.key === key) {
								i.relateGoodsRowKeys = relateGoodsRowKeys;
							}
						});
						if (validSelectedRows.length > 0) {
							// 5.把原本的最后一行的endRow置为false
							dataSourceTemp[theOrderLastGoodsRowIndex]['endRow'] = false;
							dataSourceTemp.splice(theOrderLastGoodsRowIndex + 1, 0, ...theSelectedDataSource);
							setDataSource(dataSourceTemp);

							// 延迟执行，确保 dataSource 已更新
							setTimeout(() => {
								checkAndUpdateTradeOrderData(dataSourceTemp);
							}, 0);
						}

					}
				});
			} else {
				const curLastRowIndex = dataSource.length;
				const allRefundItemTypeList = dataSourceTemp.map(i => `${RefundItemTypeEnum.退货}_${i.itemId}_${i.skuId}`);
				// 过滤掉已经添加过的货品
				const validSelectedRows = selectedRows.filter(item => {
					const skuInfo = item.sysSkuList[0];

					let refundItemTypeStr = `${RefundItemTypeEnum.退货}_${skuInfo.sysItemId}_${skuInfo.sysSkuId}`;
					if (!allRefundItemTypeList.includes(refundItemTypeStr)) {
						return true;
					}
					return false;
				});
				console.log('selectedRowsselectedRows2222', selectedRows, getBuildType());
				// 1.先把要添加的货品处理成dataSourceItem
				validSelectedRows.forEach((item, itemIndex) => {
					const skuInfo = item.sysSkuList[0];
					const goodRowKey = `${GOOD_ROW_START_KEY}_${curLastRowIndex + itemIndex}_${skuInfo.sysSkuId}`;
					const goodRowItem = {
						key: goodRowKey,
						buildType: 3,
						belongOrderRowKey: null,
						rowType: "good",
						showType: "sys",
						serialNum: curLastRowIndex + itemIndex + 1,
						title: '',
						skuName: '',
						picUrl: '',
						sysPicUrl: skuInfo.picUrl,
						sysItemAlias: skuInfo.sysItemAlias,
						sysSkuName: skuInfo.sysSkuName,
						endRow: selectedRows.length - itemIndex === 1,
						refundAmount: item.price || 0,
						applyRefundNum: 1,
						hasRefundNum: 0,
						storageId: skuInfo.storageId || '-1',
						addType: 'item',
						skuId: skuInfo.sysSkuId, // 这个id给删除货品品时重新计算key使用，重复添加货品时也会用到
						itemId: skuInfo.sysItemId, // 这个id和skuId用于区分sku，重复添加货品时用到
						originData: {
							salableItemDistributableStock: skuInfo?.salableItemDistributableStock, // 可配货库存数量
							backOrderLoading: true, // 是否正在请求待发货订单
							refundItemType: RefundItemTypeEnum.退货,
							sysRefundItemType: RefundItemTypeEnum.退货,
							oid: null,
							numIid: null,
							skuId: null,
							picUrl: null,
							title: null,
							skuName: null,
							itemAlias: null,
							outerId: null,
							outerSkuId: null,
							sysRefundItemRecordId: null,
							itemRefundItemRecordId: null,
							costPrice: skuInfo.costPrice,
							storageId: skuInfo.storageId || '-1',
							sysItemId: skuInfo.sysItemId,
							sysSkuId: skuInfo.sysSkuId,
							sysPicUrl: skuInfo.picUrl,
							sysItemName: skuInfo.sysItemName,
							sysSkuName: skuInfo.sysSkuName,
							sysItemAlias: skuInfo.sysItemAlias,
							sysOuterId: skuInfo.outerId,
							sysOuterSkuId: skuInfo.skuOuterId,
							sysItemNo: item.sysItemNo,
							sysSkuAlias: skuInfo.sysSkuAlias,
							isCombination: skuInfo.isCombination,
							market: skuInfo.market,
							stall: skuInfo.stall,
							supplierName: skuInfo.supplierName,
							isDistributorUserPushRefund: false,
						}
					};
					setTimeout(() => {
						onRowSelected(true, goodRowItem);
					}, itemIndex);

					dataSourceTemp.push(goodRowItem);
				});
				// 2.重新计算dataSource的starRow和endRow
				dataSourceTemp.forEach((item, index) => {
					item.startRow = index === 1;
					item.serialNum = index <= 1 ? 1 : index;
					item.endRow = dataSourceTemp.length - 1 === index;
				});
				setDataSource(dataSourceTemp);

				// 延迟执行，确保 dataSource 已更新
				setTimeout(() => {
					checkAndUpdateTradeOrderData(dataSourceTemp);
				}, 0);
			}

		} else {
			message.warning('请先勾选售后订单');
		}
	};

	// 当点击添加换货货品时（库存版）
	const onAddProducts2 = (selectedRows) => {
		if (selectedRows.length > 0) {
			const dataSourceTemp = cloneDeep(dataSource);
			if (getBuildType() === 1) {
				// 1.找出所有勾选的订单，往订单下添加商品
				selectedRowKeys.forEach((rowKey:string) => {
					if (rowKey.startsWith('order')) {
						let theOrderLastGoodsRowIndex = 0;
						let curOrderRow:AftersaleScanTableDataSourceItem = {};
						let curOrderOriginRelateGoodsRowKeys = [];
						dataSourceTemp.forEach((dataItem, index) => {
							// 1.找出勾选订单下的最后一行的商品行及其index
							if (dataItem.belongOrderRowKey === rowKey && dataItem.endRow) {
								theOrderLastGoodsRowIndex = index;
							}
							// 2.找出当前勾选的订单信息行
							if (dataItem.key === rowKey) {
								curOrderRow = dataItem;
								curOrderOriginRelateGoodsRowKeys = cloneDeep(dataItem.relateGoodsRowKeys);
							}
						});
						// 3.找出当前订单下的货品的skuId或者itemId，如果已经存在勾选的货品，则不需要重新添加
						const { key, relateGoodsRowKeys = [], buildType, refundItemTypeList } = curOrderRow;

						// 判断当前订单是否满足添加换货条件 buildType == 1 && afterSaleType == 3 || 5
						if (![afterSaleTypeText.换货, afterSaleTypeText.补发货品].includes(curOrderRow.originData?.afterSaleType)) {
							return;
						}

						const validSelectedRows = selectedRows.filter(item => {
							const skuInfo = item.sysSkuList[0];

							let refundItemTypeStr = `${RefundItemTypeEnum.换货}_${skuInfo.sysItemId}_${skuInfo.sysSkuId}`;
							if (!refundItemTypeList?.includes(refundItemTypeStr)) {
								refundItemTypeList.push(refundItemTypeStr);
								return true;
							}
							return false;
						});
						// 4.将勾选的商品处理成table的dataSource

						const theSelectedDataSource = [];
						validSelectedRows.forEach((item, itemIndex) => {
							const skuInfo = item.sysSkuList[0];
							const goodRowKey = `${GOOD_ROW_START_KEY}_${curOrderOriginRelateGoodsRowKeys.length + itemIndex}_${key}`;
							const goodRowItem = {
								key: goodRowKey,
								buildType,
								belongOrderRowKey: key,
								rowType: "good",
								showType: "sys",
								serialNum: curOrderOriginRelateGoodsRowKeys.length + itemIndex + 1,
								title: '',
								skuName: '',
								picUrl: '',
								sysPicUrl: skuInfo.picUrl,
								sysItemAlias: skuInfo.sysItemAlias,
								sysSkuName: skuInfo.sysSkuName,
								endRow: selectedRows.length - itemIndex === 1,
								refundAmount: skuInfo.price || 0,
								applyRefundNum: 1,
								hasRefundNum: 0,
								addType: 'sys',
								skuId: skuInfo.sysSkuId, // 这个id给删除货品品时重新计算key使用，重复添加货品时也会用到
								itemId: skuInfo.sysItemId, // 这个id和skuId用于区分sku，重复添加货品时用到
								storageId: skuInfo?.storageId || '-1',
								originData: {
									salableItemDistributableStock: skuInfo?.salableItemDistributableStock, // 可配货库存数量
									backOrderLoading: true, // 是否正在请求待发货订单
									serialNum: curOrderRow?.serialNum,
									refundItemType: RefundItemTypeEnum.换货,
									sysRefundItemType: RefundItemTypeEnum.换货,
									tid: curOrderRow.originData.tid,
									numIid: null,
									skuId: null,
									picUrl: null,
									title: null,
									skuName: null,
									itemAlias: null,
									outerId: null,
									outerSkuId: null,
									sysRefundItemRecordId: null,
									itemRefundItemRecordId: null,
									costPrice: skuInfo.costPrice,
									sysItemId: skuInfo.sysItemId,
									sysSkuId: skuInfo.sysSkuId,
									sysPicUrl: skuInfo.picUrl,
									sysItemName: skuInfo.sysItemName,
									sysSkuName: skuInfo.sysSkuName,
									sysItemAlias: skuInfo.sysItemAlias,
									sysOuterId: skuInfo.outerId,
									sysOuterSkuId: skuInfo.skuOuterId,
									sysItemNo: item.sysItemNo,
									isCombination: skuInfo.isCombination,
									sysSkuAlias: skuInfo.sysSkuAlias,
									market: skuInfo.market,
									stall: skuInfo.stall,
									supplierName: skuInfo.supplierName,
									supplierId: skuInfo.supplierId,
									refundId: curOrderRow.originData.refundId,
									sellerNick: curOrderRow.originData.sellerNick,
									sellerId: curOrderRow.originData.sellerId,
									localContent: curOrderRow?.originData?.localContent || '', // 线下备注
									isDistributorUserPushRefund: curOrderRow?.originData?.isDistributorUserPushRefund,
									storageId: skuInfo?.storageId || '-1',
								}
							};
							setTimeout(() => {
								onRowSelected(true, goodRowItem);
							}, itemIndex);
							relateGoodsRowKeys.push(goodRowKey);
							theSelectedDataSource.push(goodRowItem);
						});
						// 3.处理当前订单的relateGoodsRowKeys，把添加的新行的row加进去
						dataSourceTemp.forEach(i => {
							if (i.key === key) {
								i.relateGoodsRowKeys = relateGoodsRowKeys;
							}
						});
						if (validSelectedRows.length > 0) {
							// 5.把原本的最后一行的endRow置为false
							dataSourceTemp[theOrderLastGoodsRowIndex]['endRow'] = false;
							dataSourceTemp.splice(theOrderLastGoodsRowIndex + 1, 0, ...theSelectedDataSource);
							setDataSource(dataSourceTemp);

							// 延迟执行，确保 dataSource 已更新
							setTimeout(() => {
								checkAndUpdateTradeOrderData(dataSourceTemp);
							}, 0);
						}

					}
				});
			}
		} else {
			message.warning('请先勾选售后订单');
		}
	};

	const shopInfoContentSettingOnOk = (list) => {
		// const list:any = [...new Set(v.concat(productContentList))];
		console.log(list, '999999999999');
		// ... existing code ...
		const newList = Object.keys(DEFAULT_PRODUCT_CONTENT_SETTING_ZERO_SP).reduce((acc, key) => {
			acc[key] = (list || []).includes(key);
			return acc;
		}, {});

		setProductContentList(list);

		// 调接口存储
		UpdateRefundGlobalConfigApi([{ biz: LOCAL_AFTERSALE_SCANING_SETTING, value: JSON.stringify(newList) }]);
	};

	const renderGoodsContentColHeader = useMemo(() => (
		<div className="r-flex r-ai-c">
			<div>商品信息</div>
			<span className="r-pointer r-ml-4">
				<GoodsContentSetting
					onOk={ shopInfoContentSettingOnOk }
					imgSettingsList={ PRODUCT_SETTING_OBJECT_IMG_SIZE_SP }
					imgSettingsdefaut={ PRODUCT_CONTENT_ENUM_SP.中图 }
					imgAllList={ IMG_LIST_SP }
					productSettingsList={ version === 1 ? PRODUCT_SETTING_OBJECT_SP : PRODUCT_SETTING_ZERO_OBJECT_SP }
					productContentList={ productContentList }
				>
					<SettingOutlined />
				</GoodsContentSetting>
			</span>
		</div>
	), [productContentList, version]);

	const renderGoodsHpContentColHeader = useMemo(() => (
		<div className="r-flex r-ai-c">
			<div>货品信息</div>
			<span className="r-pointer r-ml-4">
				<GoodsContentSetting
					onOk={ shopInfoContentSettingOnOk }
					imgSettingsList={ PRODUCT_SETTING_OBJECT_IMG_SIZE_HP }
					imgSettingsdefaut={ PRODUCT_CONTENT_ENUM_HP.中图 }
					imgAllList={ IMG_LIST_HP }
					productSettingsList={ PRODUCT_SETTING_OBJECT_HP }
					productContentList={ productContentList }
					titleName="货品信息设置"
				>
					<SettingOutlined />
				</GoodsContentSetting>
			</span>
		</div>
	), [productContentList]);

	// 显示待发货订单详情
	const handleShowOrderInfo = (row) => {
		console.log('%c [ row ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', row);
		const {
			sysSkuId,
			skuId,
		} = row?.originData || {};

		let obj:any = {
			visible: true,
			sysSkuId: '',
			skuId: '',
		};
		// 有货品传货品没有传商品
		if (sysSkuId) {
			obj.sysSkuId = sysSkuId;
		} else if (skuId) {
			obj.skuId = skuId;
		}
		setTradeInfoModalShow(obj);
	};

	// 待发货订单渲染: 注意后端这里不给具体数值
	const getOrdersToBeShippedNode = (_, record, index) => {
		const { existWaitSendTrade = false, backOrderLoading = false } = record?.originData || {};
		
		if (backOrderLoading) {
			return <LoadingOutlined className="r-fs-14 r-c-primary" style={ { marginLeft: '4px' } } />;
		}
		return (
			<div>
				{
					existWaitSendTrade ? (
						<span className="r-c-primary r-pointer r-fs-14 r-bold" onClick={ () => handleShowOrderInfo(record) }>有</span>
					) : (
						<span className="r-fs-14 r-bold r-c-333">无</span>
					)
				}
			</div>
		);
	};

	const defaultColumn = useMemo(() => {
		return [
			{
				dataIndex: 'index',
				title: '序号',
				width: 70,
				onCell: row => {
					if (row.rowType === 'order') {
						return { colSpan: 9 };
					}
				},
				render: (_, row, index) => {
					if (row.rowType === 'order') {
						return getOrderInfoContent(row, index);
					}
					return row.serialNum;
				}
			},
			{
				dataIndex: 'goodsInfo',
				title: renderGoodsContentColHeader,
				render: getGoodsInfoNode,
				onCell: sharedOnCell,
				className: 'flex-grow',
			},
			version === 1 ? {
				dataIndex: 'productInfo',
				title: renderGoodsHpContentColHeader,
				render: getProductInfoNode,
				onCell: sharedOnCell,
				className: 'flex-grow',
			} : {},
			{
				dataIndex: 'mount',
				width: 150,
				title: '售后金额',
				onCell: sharedOnCell,
				className: 'td-refund-mount',
				render: getAftersaleMountNode,
			},
			{
				dataIndex: 'applyRefundNum',
				title: '申请数量',
				width: 110,
				render: getApplyRefundNumNode,
				className: 'td-apply-refund-num',
				onCell: sharedOnCell,
			},
			{
				dataIndex: 'hasRefundNum',
				title: '已处理', // 已收/已退数量
				width: 110,
				className: 'td-has-refund-num',
				onCell: sharedOnCell,
			},
			{
				dataIndex: 'receiveNum',
				title: (
					<div className="r-flex r-ai-c">
						<div>本次处理数量</div>
						<Tooltip title={ <>数量不为 0 时将自<br />动勾选售后商品</> }>
							<QuestionCircleFilled className="r-ml-4 r-pointer" />
						</Tooltip>
					</div>),
				width: 150,
				onCell: sharedOnCell,
				className: 'td-receive-num',
				render: getReceiveNumNode,
			},
			{
				dataIndex: 'existWaitSendTrade',
				title: (
					<div className="r-flex r-ai-c">
						<div>待发货订单</div>
						<Tooltip
							title={ (
								<div>
									<div>系统默认的匹配订单规则为：</div>
									<div>· 下单时间为近30天（包含当日）</div>
									<div>· 对应品的子单状态为待发货</div>
								</div>
							) }
							autoAdjustOverflow
						>
							<QuestionCircleFilled className="r-ml-4 r-pointer" />
						</Tooltip>
					</div>
				),
				width: 120,
				onCell: sharedOnCell,
				render: getOrdersToBeShippedNode,
			},
			!isShowZeroStockVersion ? {
				dataIndex: 'salableItemDistributableStock',
				title: (
					<div className="r-flex r-ai-c">
						<div>可配货库存</div>
						<Tooltip title="仓内目前所剩库存或所需库存" autoAdjustOverflow>
							<QuestionCircleFilled className="r-ml-4 r-pointer" />
						</Tooltip>
					</div>
				),
				width: 130,
				onCell: sharedOnCell,
				render: (_, record, index) => {
					return <div className="r-fs-14">{record?.originData?.salableItemDistributableStock}</div>;
				},
			} : {}
		];
	}, [isShowZeroStockVersion, version, renderGoodsContentColHeader, renderGoodsHpContentColHeader]);

	const columns = useMemo(() => {
		let columnMap = {};
		defaultColumn.filter(d => d?.title).forEach(item => {
			columnMap[item.dataIndex] = item;
		});
		return sortColumnList.filter(item => item.ischecked).map(item => columnMap[item.key]).filter(Boolean);
	}, [sortColumnList, defaultColumn]); // 添加所有依赖项

	// 添加 useEffect 来更新 ref
	useEffect(() => {
		columnsRef.current = columns;
	}, [columns]);
	console.log('dataSourcedataSource', dataSource);
	const getAddGoodsContent = () => {
		if (version === 1) { // 库存版
			return (
				<ProductListModal
					type="checkbox"
					showSave
					onOk={ onAddProducts }
				>
					<Button
						type="primary"
						onClick={
							(e) => {
								sendPoint(Pointer.售后_售后扫描登记_添加商品);
								if (!selectedRowKeys.length && dataSource.length > 1) {
									message.warning('请先勾选售后订单');
									e.stopPropagation();
									return;
								}
							}
						}
					>
						添加商品
					</Button>
				</ProductListModal>
			);
		} else if (version === 2) { // 无库存版

			return (
				<ZeroStockOnlineListModal
					type="checkbox"
					showColumnKeys={ ['itemNo', 'barCode'] }
					onOk={ onAddGooods }
				>
					<Button
						type="primary"
						onClick={
							(e) => {
								sendPoint(Pointer.售后_售后扫描登记_添加商品);
								if (!selectedRowKeys.length && dataSource.length > 1) {
									message.warning('请先勾选售后订单');
									e.stopPropagation();
									return;
								}
							}
						}
					>
						添加商品
					</Button>
				</ZeroStockOnlineListModal>
			);

		}
	};

	const isShowAddExchangeBtn = useMemo(() => {
		let isHasExchangeGoods = !!dataSource.find(item => selectedRowKeys.includes(item.key) && [afterSaleTypeText.换货, afterSaleTypeText.补发货品].includes(item.originData?.afterSaleType));
		return isHasExchangeGoods;
	}, [selectedRowKeys, dataSource]);

	const handleCheckExchangeGoods = () => {
		if (!selectedRowKeys.length) {
			message.warning('请先勾选售后订单');
			return;
		}
		if (!isShowAddExchangeBtn) {
			message.warning('仅换货/补发售后单可进行添加');
			return;
		}
	};

	// 添加换货商品
	const getAddExchangeGoodsContent = () => {
		if (isShowAddExchangeBtn) {
			if (version === 1) { // 库存版
				return (
					<ProductListModal
						type="checkbox"
						showSave
						onOk={ (goods) => onAddProducts2(goods) }
					><Button type="primary">添加换货商品</Button>
					</ProductListModal>
				);
			} else if (version === 2) { // 无库存版
				return (
					<ZeroStockOnlineListModal
						type="checkbox"
						showColumnKeys={ ['itemNo', 'barCode'] }
						onOk={ (goods) => onAddGooods2(goods) }
					>	<Button type="primary">添加换货商品</Button>
					</ZeroStockOnlineListModal>
				);

			}
		} else {
			return (
				<div onClick={ handleCheckExchangeGoods } className={ styles.disAddExchangeGoods }>添加换货商品</div>
			);
		}
	};
	const onDelete = () => {
		if (!selectedRowKeys.length) {
			message.warning('请先勾选售后订单');
			return;
		}
		const dataSourceTemp = [];
		sendPoint(Pointer.售后_售后扫描登记_删除商品);
		dataSource.forEach(rowItem => {
			const { key, rowType } = rowItem;
			const theOrderGoodsRows = [];
			if (getBuildType() !== 3) {
				if (rowType === 'order') {
					rowItem.relateGoodsRowKeys = [];
					rowItem.itemIdList = [];
					rowItem.skuIdList = [];
					rowItem.refundItemTypeList = [];
					// 获取订单下未勾选的商品行,并重新计算序号和key
					const theNoSelectedGoodsRows = dataSource.filter(i => {
						return i.belongOrderRowKey === key && !selectedRowKeys.includes(i.key);
					});
					theNoSelectedGoodsRows.forEach((i, index) => {
						const goodRowKey = `${GOOD_ROW_START_KEY}_${index}_${key}`;
						const newGoodInfo = {
							...i,
							key: goodRowKey,
							serialNum: index + 1,
							endRow: theNoSelectedGoodsRows.length - 1 === index
						};
						// 删除所选行以后，订单中的relateGoodsRowKeys也要同步更新
						rowItem.relateGoodsRowKeys.push(goodRowKey);
						// 删除所选行以后，订单中的skuIdList和itemIdList也要同步更新
						if (['sys', 'item'].includes(i.addType)) { // 如果是添加的商品或货品
							rowItem.itemIdList.push(i.itemId);
							rowItem.skuIdList.push(i.skuId);

							let refundItemTypeStr = `${i.refundItemType}_${i.itemId}_${i.skuId}`;
							rowItem.refundItemTypeList.push(refundItemTypeStr);
						}
						theOrderGoodsRows.push(newGoodInfo);
					});
					// 没有关联的商品行，说明当前订单下的所有商品都要删除，那订单行也删除，没必要展示了
					if (rowItem.relateGoodsRowKeys.length === 0) {
						rowItem.endRow = true;
						dataSourceTemp.push(...theOrderGoodsRows);
					} else {
						dataSourceTemp.push(rowItem, ...theOrderGoodsRows);
					}
				}
			} else if (!selectedRowKeys.includes(key)) {
				dataSourceTemp.push(rowItem);
			}
		});
		// 未匹配到任何数据然后删除商品或者货品时重新计算starRow，endRow和serialNum
		if (getBuildType() === 3) {
			dataSourceTemp.forEach((item, index) => {
				const goodRowKey = `${GOOD_ROW_START_KEY}_${index}_${item.skuId}`;
				item.key = goodRowKey;
				item.startRow = index === 1;
				item.serialNum = index <= 1 ? 1 : index;
				item.endRow = dataSourceTemp.length - 1 === index;
			});
		}
		console.log(dataSourceTemp);
		setDataSource(dataSourceTemp);
		setSelectedRowKeys([]);
	};


	const clearReceiveSuccessTrade = () => {
		/**
		 * 清空收货成功的订单，如果是手动单或者自建的单子，全部清空
		 */
		console.time('clearReceiveSuccessTrade');
		const refundScanConfirmRes = getRefundScanConfirmRes();
		const successRefundIdList = [];
		const successTidList = [];
		if (refundScanConfirmRes) {
			const { buildType, batchResults = [] } = refundScanConfirmRes;
			if (buildType === 'HAND_CREATE' || getBuildType() === 3) {
				const isSuccess = batchResults[0].success;
				if (isSuccess) {
					setDataSource([]);
				}
			} else {
				batchResults.forEach(i => {
					// 把收货成功的退款订单id或者销售订单id保存下来
					if (i.success) {
						if (buildType === 'MATCH_ORDER') {
							successTidList.push(i.operationId);
						}
						if (buildType === 'MATCH_REFUND') {
							successRefundIdList.push(i.operationId);
						}
					}
				});
				// 把收货成功的订单以及订单下的商品行全部过滤掉
				let needOrderRowKey = '';
				let orderSerialNum = 0;
				const dataSourceTemp = getDataSource().filter(item => {
					if (buildType === 'MATCH_ORDER') {
						if (item.rowType === 'order') {
							if (successTidList.includes(item.tid)) {
								needOrderRowKey = item.key;
								return false;
							} else { // 需要重新计算序号
								orderSerialNum += 1;
								item.serialNum = orderSerialNum;
								return true;
							}

						}
						if (item.belongOrderRowKey === needOrderRowKey) {
							return false;
						}
						return true;
					} else if (buildType === 'MATCH_REFUND') {
						if (item.rowType === 'order') {
							if (successRefundIdList.includes(item.refundId)) {
								needOrderRowKey = item.key;
								return false;
							} else { // 需要重新计算序号
								orderSerialNum += 1;
								item.serialNum = orderSerialNum;
								return true;
							}
						}
						if (item.belongOrderRowKey === needOrderRowKey) {
							return false;
						}
						return true;
					}
					return false;
				});
				setDataSource(dataSourceTemp);
				console.log('%c [ 清理数据 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', dataSourceTemp);
			}
		}
		console.timeEnd('clearReceiveSuccessTrade');
		console.time('EVENT_BUS.CONFIRM_RECEIVE_SUCCESS_FINALLY');
		event.emit(EVENT_BUS.CONFIRM_RECEIVE_SUCCESS_FINALLY);
		setSelectedRowKeys([]);
	};

	// 组装打印数据 orderList
	const handlePrintCenterOrderList = (refundLabelData) => {
		console.log('%c [ 组装打印数据 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', refundLabelData);
		const selectedRows = getSelectedRows();
		const orderList = [];

		selectedRows.forEach(row => {
			if (row.rowType === 'good') {
				const curRowOrderList = [];
				const rowData = row.originData;
				let newKey = `${rowData.refundId}_${rowData.sysSkuId || rowData.skuId}`;
				const curRefundLabelList = refundLabelData[newKey];

				// 过滤换货和补发的订单
				if ([refundItemTypeEnum.换货, refundItemTypeEnum.补发].includes(rowData?.refundItemType || rowData?.sysRefundItemType)) {
					return;
				}
				const receiveNum = Number(form.getFieldValue(`receiveNum_${row.key}`));
				// 拆开数量
				[...new Array(receiveNum)].forEach((_, index) => {
					const item = {
						...rowData,
						sellerAbbreviation: curRefundLabelList[index]?.sellerAbbreviation,
						refundLabelId: curRefundLabelList[index]?.labelId,
						buyerMessage: curRefundLabelList[index]?.buyerMessage,
						sellerMemo: curRefundLabelList[index]?.sellerMemo,
						warehouseSlotName: curRefundLabelList[index]?.warehouseSlotName,
						warehouseSlotId: curRefundLabelList[index]?.warehouseSlotId,
					};
					curRowOrderList.push(item);
				});
				orderList.push(...curRowOrderList);
			}
		});
		console.log('%c [ 组装打印数据 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', orderList);
		return orderList;
	};

	const getPrintCenterOrderList = (refundLabelData) => {
		let success = true;
		const orderList = handlePrintCenterOrderList(refundLabelData);
		if (orderList.length === 0) {
			message.warning("请填写本次收货数量后再次打印");
			success = false;
			customLogPost('获取打印退货标签数据失败', { refundLabelData: orderList?.map(item => {
				return {
					tid: item?.tid,
					refund: item?.refundId,
					skuId: item?.skuId,
					refundLabelId: item?.refundLabelId,
					refundItemType: item?.refundItemType,
				};
			}) });
		}
		return ({
			success,
			orderList
		});
	};

	const getGenerateRefundLabelParams = () => {
		const userInfo = getUserInfo();
		const generateRefundLabelInfoList = [];
		const selectedRows = getSelectedRows();
		console.log("selectedRows::", selectedRows);
		selectedRows.forEach(row => {
			if (row.rowType === 'good') {
				const rowData = row.originData;
				// 过滤换货和补发的订单
				if ([refundItemTypeEnum.换货, refundItemTypeEnum.补发].includes(rowData?.refundItemType || rowData?.sysRefundItemType)) {
					return;
				}
				const receiveNum = Number(form.getFieldValue(`receiveNum_${row.key}`));
				let item = {
					title: rowData.title,
					picUrl: rowData.picUrl,
					tid: rowData.tid,
					oid: rowData.oid,
					ptTid: rowData?.ptTid || '',
					ptOid: rowData?.ptOid || '',
					goodsNum: receiveNum,
					refundId: rowData.refundId,
					localContent: rowData.localContent,
					numIid: rowData.numIid,
					skuId: rowData.skuId,
					outerId: rowData.outerId,
					skuOuterId: rowData.outerSkuId,
					skuName: rowData.skuName,
					platform: rowData.platform,
					costPrice: rowData.costPrice,
					sysItemId: rowData.sysItemId,
					isCombination: rowData.isCombination,
					sysItemNo: rowData.sysItemNo,
					sysSkuId: rowData.sysSkuId,
					sysItemAlias: rowData.sysItemAlias,
					sysItemName: rowData.sysItemName,
					sysOuterId: rowData.sysOuterId,
					sysSkuPicUrl: rowData.sysPicUrl,
					sysSkuName: rowData.sysSkuName,
					sysSkuAlias: rowData.sysSkuAlias,
					sysSkuOuterId: rowData.sysOuterSkuId,
					market: rowData.market,
					stall: rowData.stall,
					supplierid: rowData.supplierid,
					supplierName: rowData.supplierName,
					sellerNick: rowData.sellerNick,
					sellerId: rowData.sellerId,
					sellerAbbreviation: rowData.sellerAbbreviation,
					source: rowData.source,
					refundItemType: rowData?.refundItemType,
					sysRefundItemType: rowData?.sysRefundItemType,
					storageId: rowData?.storageId || '-1', // 找不到传-1
					buyerMessage: row?.buyerMessage || rowData?.buyerMessage,
					sellerMemo: row?.sellerMemo || rowData?.sellerMemo
					// refundWaybillNo: rowData.sid,
					// refundWaybillCode: rowData.companyCode,
					// refundWaybillName: rowData.companyName
				};
				try {
					if (searchInstance?.current) {
						const { getExpressCompanyList, getShopList } = searchInstance.current;
						const { companyCode, platform, sellerId, sid } = getOrderFormData();
						const companyObj = getExpressCompanyList().find(i => i.exCode === companyCode) || {};
						const sellerObj = getShopList().find(i => i.sellerId === sellerId) || {};
						item = {
							...item,
							platform: getMatchPlatform(),
							sellerId: getMatchShopInfo()?.sellerId,
							sellerNick: getMatchShopInfo()?.sellerNick,
							source: getMatchShopInfo()?.source,
							sellerAbbreviation: getMatchShopInfo()?.sellerAbbreviation,
							refundWaybillCode: companyCode,
							refundWaybillNo: sid,
							refundWaybillName: companyObj.exName,
						};
						// 如果是手动创建的售后单,以下参数需要从searchComponent的表单中获取
						if (getBuildType() === 3) {
							item = {
								...item,
								source: PLAT_HAND.toUpperCase(),
								platform,
								sellerId: sellerObj.sellerId,
								sellerNick: sellerObj.sellerNick,
								sellerAbbreviation: sellerObj.sellerAbbreviation,
							};
						}
					}
				} catch (e) {
					console.log(e);
				}

				generateRefundLabelInfoList.push(item);
			}

		});

		return {
			userId: userInfo?.userId,
			generateRefundLabelInfoList,
		};

	};

	// 获取退货唯一码
	const generateRefundLabel = async() => {
		let refundLabelData = {};
		const params = getGenerateRefundLabelParams();
		console.log('%c [ params ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', params);
		setPrintLoading(true);

		try {
			const res = await GenerateRefundLabelApi(params);
			if (res.takeGoodsLabelDTOS) {
				// 同一个商品会根据数量生成多个标签，这里用skuId或者sysSkuId作为key的方式，把所有的标签存起来
				res.takeGoodsLabelDTOS.forEach(labelItem => {
					let newKey = `${labelItem.refundId}_${labelItem.sysSkuId || labelItem.skuId}`; // sysSkuId可能相同，所以加上refundId
					if (refundLabelData[newKey]) {
						refundLabelData[newKey].push(labelItem);
					} else {
						refundLabelData[newKey] = [labelItem];
					}
				});
				
			}
		} catch (error) {
			console.error("Error fetching refund labels:", error);
			customLogPost('获取退货唯一码失败', { error, params });
		} finally {
			setPrintLoading(false);
		}

		return refundLabelData;
	};

	// 打印退货标签
	const onClickPrintLabel = async() => {
		sendPoint(Pointer.售后_售后扫描登记_打印退货标签);
		const selectedRows = getSelectedRows();
		if (selectedRows.length < 1) {
			message.warning({
				content: '请先勾选售后订单'
			});
			return;
		}
		// 打印之前生成退货标签唯一码
		const refundLabelData = await generateRefundLabel();
		const { success, orderList } = getPrintCenterOrderList(refundLabelData);
		if (success) {
			customLogPost('手动打印退货标签', { orderList: orderList?.map(item => {
				return {
					sid: item?.sid,
					tid: item?.tid,
					title: item?.title,
					companyName: item?.companyName,
					sellerAbbreviation: item?.sellerAbbreviation,
					refundLabelId: item?.refundLabelId,
					buyerMessage: item?.buyerMessage,
					sellerMemo: item?.sellerMemo,
					refundId: item?.refundId,
					skuId: item?.skuId,
					skuName: item?.skuName,
					outerSkuId: item?.outerSkuId,
					outerId: item?.outerId,
					itemAlias: item?.itemAlias,
					sysSkuId: item?.sysSkuId,
					sysSkuName: item?.sysSkuName,
					sysOuterSkuId: item?.sysOuterSkuId,
					sysOuterId: item?.sysOuterId,
					sysSkuAlias: item?.sysSkuAlias,
					sysItemAlias: item?.sysItemAlias,
					sysRefundItemRecordId: item?.sysRefundItemRecordId,
					sysRefundItemType: item?.sysRefundItemType,
				};
			}) });

			PrintCenter.printThdXbq({
				orderList,
				takeGoodsLabelPrintSource: LabelPrintSourceEnum.售后扫描退货单小标签,
			} as any);
		}
	};

	// 展示打印模板
	const onClickPrintTemp = () => {
		sendPoint(Pointer.售后_售后扫描登记_退货标签模板设置);
		PrintCenter.showTemplateMain({ printType: 'thd' });
	};

	// 自动执行打印标签
	const autoPrintLabel = async() => {
		// 自动打印标签之前，先判断之前设置的模板和打印机是否存在，不存在的话，弹窗走手动打印
		const validDefaultPrinter = getPrinterList().some(i => i == getDefaultPrinter());
		const validDefaultTemplate = getThdXbqTempList().some(i => i.Mode_ListShowId == getDefaultTemplate());
		if (validDefaultPrinter && validDefaultTemplate) {
			const refundLabelData = await generateRefundLabel();
			const { success, orderList } = getPrintCenterOrderList(refundLabelData);
			if (success) {
				customLogPost('自动打印退货标签', { orderList: orderList?.map(item => {
					return {
						sid: item?.sid,
						tid: item?.tid,
						title: item?.title,
						companyName: item?.companyName,
						sellerAbbreviation: item?.sellerAbbreviation,
						refundLabelId: item?.refundLabelId,
						buyerMessage: item?.buyerMessage,
						sellerMemo: item?.sellerMemo,
						refundId: item?.refundId,
						skuId: item?.skuId,
						skuName: item?.skuName,
						outerSkuId: item?.outerSkuId,
						outerId: item?.outerId,
						itemAlias: item?.itemAlias,
						sysSkuId: item?.sysSkuId,
						sysSkuName: item?.sysSkuName,
						sysOuterSkuId: item?.sysOuterSkuId,
						sysOuterId: item?.sysOuterId,
						sysSkuAlias: item?.sysSkuAlias,
						sysItemAlias: item?.sysItemAlias,
						sysRefundItemRecordId: item?.sysRefundItemRecordId,
						sysRefundItemType: item?.sysRefundItemType,
					};
				}) });

				PrintCenter.printThdXbq({
					printer: getDefaultPrinter(),
					tempId: getDefaultTemplate(),
					review: false,
					orderList,
					takeGoodsLabelPrintSource: LabelPrintSourceEnum.售后扫描退货单小标签,
				});
			}

			clearReceiveSuccessTrade();
		} else {
			setSelectPrinterAndTemplateModalVisible(true);
			customLogPost('自动打印退货标签失败，选择模板和打印机');
		}
	};

	const onReceiveSuccess = (refundScanConfirmRes) => {
		console.timeEnd('EVENT_BUS.RECEIVE_SUCCESS');
		try {
			setRefundScanConfirmRes(refundScanConfirmRes);
			// 如果开启了自动打印标签，则执行打印操作
			if (getAutoPrintSwitchState()) {
				autoPrintLabel();
			} else {
				clearReceiveSuccessTrade();
			}
		} catch (error) {
			customLogPost('收货成功后执行遇到错误', { error, batchResults: refundScanConfirmRes?.batchResults });
			throw new Error('onReceiveSuccess error');
		}

	};
	// 处理用户配置开关
	const handleConfigInfo = (value) => {
		const { confirmNumFillUpDefault, autoPrintLabels, defaultPrinter, defaultReturnLabelTemplate, memoFlagConfig } = value;
		setMemoFlagConfig(memoFlagConfig);
		setReceiveNumFill(confirmNumFillUpDefault === 1);
		setAutoPrintSwitchState(autoPrintLabels === 1);
		setDefaultPrinter(defaultPrinter);
		setDefaultTemplate(defaultReturnLabelTemplate);
	};


	// 获取用户开关配置
	const getConfigInfo = () => {
		avoidRepeatReq(TradeDictQueryDictApi, {
			userDictEnum: 'AFTERSALE_SCAN_SET'
		}).then(res => {
			if (res.value) {
				try {
					const value = JSON.parse(res.value);
					handleConfigInfo(value);
				} catch (e) { console.log(e); }
			}
		});
	};

	const onScanSetModalClose = () => {
	};

	const printerAndTemplateModalOnOk = async(value) => {
		const refundLabelData = await generateRefundLabel();
		const { success, orderList } = getPrintCenterOrderList(refundLabelData);
		if (success) {
			PrintCenter.printThdXbq({
				printer: value.printer,
				tempId: value.template,
				review: false,
				orderList,
				takeGoodsLabelPrintSource: LabelPrintSourceEnum.售后扫描退货单小标签,
			});
			customLogPost('自动打印退货标签失败,选择模板后打印退货标签', { orderList: orderList?.map(item => item?.refundLabelId) });
		}

		// 把用户选择的打印机和模板更新到设置中去
		TradeDictQueryDictApi({
			userDictEnum: 'AFTERSALE_SCAN_SET'
		}).then(res => {
			if (res.value) {
				try {
					const queryDictValue = JSON.parse(res.value);
					queryDictValue["defaultPrinter"] = value.printer;
					queryDictValue["defaultReturnLabelTemplate"] = value.template;
					const params = {
						userDictEnum: 'AFTERSALE_SCAN_SET',
						value: JSON.stringify(queryDictValue)
					};
					TradeDictInsertDictApi(params);
				} catch (e) { console.log(e); }
			}
		});
		clearReceiveSuccessTrade();
	};

	const onUpdateOrderFormData = (orderFormData) => {
		setOrderFormData(orderFormData);
	};
	const initData = async() => {
		userStore.getUserInfo().then(async res => {
			setVersion(res.version);
			setUserInfo(res);
		});
		userStore.getSystemSetting();
		const shopList = await userStore.getShopList();
		setShopList(shopList);
	};

	useImperativeHandle(cRef, () => {
		return {
			_getDataSource: () => getDataSource(),
		};
	}, [dataSource]);

	useEffect(() => {
		handleConfigInfo(configInfo);
	}, [configInfo]);

	useEffect(() => {
		setPrinterList(thePrinterList);
	}, [thePrinterList]);

	useEffect(() => {
		setThdXbqTempList(theThdXbqTempList);
	}, [theThdXbqTempList]);

	useEffect(() => {
		handleCountNum();
		// PrintCenter.getPrinterList();
	}, [selectedRowKeys]);

	useEffect(() => {
		initData();
		getSearchConditionConfig(); // 获取商品信息货品信息配置
		event.on(EVENT_BUS.SEARCH_FORM_ONSEARCH, onSearch);
		event.on(EVENT_BUS.SCAN_REQUEST_RESULT, handleResult);
		event.on(EVENT_BUS.CONFIRM_RECEIVE, onConfirmReceive);
		event.on(EVENT_BUS.RECEIVE_SUCCESS, onReceiveSuccess);
		event.on(EVENT_BUS.SCAN_SET_MODAL_CLOSED, onScanSetModalClose);
		event.on(EVENT_BUS.UPDATE_ORDER_FORM_DATA, onUpdateOrderFormData);
		return () => {
			event.off(EVENT_BUS.SEARCH_FORM_ONSEARCH, onSearch);
			event.off(EVENT_BUS.SCAN_REQUEST_RESULT, handleResult);
			event.off(EVENT_BUS.CONFIRM_RECEIVE, onConfirmReceive);
			event.off(EVENT_BUS.RECEIVE_SUCCESS, onReceiveSuccess);
			event.off(EVENT_BUS.SCAN_SET_MODAL_CLOSED, onScanSetModalClose);
			event.off(EVENT_BUS.UPDATE_ORDER_FORM_DATA, onUpdateOrderFormData);
		};
	}, []);


	const onFormValuesChange = (changeValue, allValues) => {
		for (let key in changeValue) {
			if (key.startsWith('receiveNum')) {
				handleCountNum();
				// 判断收货数量只能是正整数
				form.setFieldsValue({
					[key]: integer(changeValue[key]) || 0
				});
			}
		}
	};

	const onUpdateModifyMemoOk = (data: TradeBatchUpdateMemoRequest) => {
		// 旗帜标签的回显
		setIsShowBatchModifyMemoModal(false);
		setDataSource(prev => {
			prev[modifyMemoIndex].sellerMemo = data[0]?.memo ?? prev[modifyMemoIndex].sellerMemo;
			prev[modifyMemoIndex].sellerFlag = data[0]?.sellerFlag ?? prev[modifyMemoIndex].sellerFlag;
			prev[modifyMemoIndex].sellerFlagTag = data[0]?.sellerFlagTag || '';
			return [...prev];
		});
		setModifyMemoIndex(-1);
	};

	const getSearchConditionConfig = async() => {
		GetRefundGlobalConfigListApi({
			bizEnumList: [LOCAL_AFTERSALE_SCANING_SETTING]
		}).then(res => {
			const targetConfig = res?.find(v => v.biz === LOCAL_AFTERSALE_SCANING_SETTING);
			if (!targetConfig) {
				// 新用户初始值
				setProductContentList(Object.keys(DEFAULT_PRODUCT_CONTENT_SETTING_ZERO_SP)
					.filter(key => DEFAULT_PRODUCT_CONTENT_SETTING_ZERO_SP[key]));
				return;
			}
			try {
				// 合并配置逻辑优化
				const parsedValue = targetConfig.value ? JSON.parse(targetConfig.value) : {};
				const defaultKeys = Object.keys(DEFAULT_PRODUCT_CONTENT_SETTING_ZERO_SP);

				// 当存在新配置时进行合并
				if (Object.keys(parsedValue).length < defaultKeys.length) {
					const mergedConfig = defaultKeys.reduce((acc, key) => {
						// 保留已有配置，新增字段默认DEFAULT_PRODUCT_CONTENT_SETTING_ZERO_SP
						acc[key] = Object.prototype.hasOwnProperty.call(parsedValue, key)
							? parsedValue[key]
							: DEFAULT_PRODUCT_CONTENT_SETTING_ZERO_SP[key];
						return acc;
					}, {});

					// 合并到原始配置对象
					Object.assign(parsedValue, mergedConfig);
				}

				// 过滤出启用的配置项
				const enabledKeys = Object.keys(parsedValue).filter(key => parsedValue[key]);
				setProductContentList(enabledKeys);
			} catch {
				// 合并配置逻辑优化
				const parsedValue = Object.keys(DEFAULT_PRODUCT_CONTENT_SETTING_ZERO_SP).reduce((acc, key) => {
					acc[key] = (targetConfig.value || []).includes(key);
					return acc;
				}, {});
				const defaultKeys = Object.keys(DEFAULT_PRODUCT_CONTENT_SETTING_ZERO_SP);

				// 当存在新配置时进行合并
				if (Object.keys(parsedValue).length < defaultKeys.length) {
					const mergedConfig = defaultKeys.reduce((acc, key) => {
						// 保留已有配置，新增字段默认启用
						acc[key] = Object.prototype.hasOwnProperty.call(parsedValue, key)
							? parsedValue[key]
							: DEFAULT_PRODUCT_CONTENT_SETTING_ZERO_SP[key];
						return acc;
					}, {});

					// 合并到原始配置对象
					Object.assign(parsedValue, mergedConfig);
				}
				// 过滤出启用的配置项
				const enabledKeys = Object.keys(parsedValue).filter(key => parsedValue[key]);
				setProductContentList(enabledKeys);
			}

		});
	};

	return (
		<div>
			<Form form={ form } onValuesChange={ onFormValuesChange }>
				<Table
					rowKey="key"
					pagination={ false }
					// loading={ loading }
					className={ styles.table }
					rowClassName={ rowClassName }
					rowSelection={ rowSelection }
					columns={ columns }
					dataSource={ dataSource }
				/>
			</Form>

			<div className={ styles["footer-container"] }>
				{/* 统计区域 */}
				<div className="count-container">
					<div className="count-item">
						<span>申请总商品数</span>
						<span>{applyRefundNumCount}</span>
					</div>
					<div className="count-item">
						<span>已退数量</span>
						<span>{alreadyRefundNumCount}</span>
					</div>
					<div className="count-item">
						<span>本次数量</span>
						<span>{receiveNumCount}</span>
					</div>
				</div>

				{/* 按钮操作区域 */}
				<div className="operate-container">
					{getAddGoodsContent()}
					<div className="r-ml-16">
						{getAddExchangeGoodsContent()}
					</div>

					<Button className="r-m-lr-16" onClick={ onDelete }>删除商品</Button>
					<Button onClick={ onClickPrintLabel } loading={ printLoading }>打印退货标签</Button>
					<div className="r-pointer r-c-primary r-ml-16" onClick={ onClickPrintTemp }>退货标签模板</div>
				</div>
			</div>
			{
				selectPrinterAndTemplateModalVisible
				&& (
					<SelectPrinterAndTemplateModal
						defaultPrinter={ getDefaultPrinter() }
						defaultTemplate={ getDefaultTemplate() }
						templateName="thdXbqTempList"
						onOkSuccess={ printerAndTemplateModalOnOk }
					/>
				)
			}

			{/* 线下备注 */}
			<OfflineMemoModal onOk={ handleOfflineMemoOk } onCancel={ handleOfflineMemoCancel } data={ offlineMemoData } fromPage={ EnumFromPage.售后扫描 } />
			{modifyMemoIndex !== -1 ? <BatchModifyMemoModal onOk={ onUpdateModifyMemoOk } onCancel={ () => { setModifyMemoIndex(-1); } } /> : ''}
			<BatchModifyLocalMemoPicModal ref={ batchModifyLocalMemoPicModalRef } />
			{
				tradeInfoModalShow.visible && (
					<TradeInfoModal
						handleCancel={ () => setTradeInfoModalShow({ visible: false }) }
						sysSkuId={ tradeInfoModalShow.sysSkuId }
						skuId={ tradeInfoModalShow.skuId }
					/>
				)
			}
		</div>
	);
};

export default observer(TableComp);
