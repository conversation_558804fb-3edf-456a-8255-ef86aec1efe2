import md5 from 'md5';
import _ from 'lodash';
import { getToken as utilsGetToken } from "@/utils/token";
import userStore from "@/stores/user";
import { local } from '@/libs/db';
import { USER_TYPE, isViteDevMode } from '@/constants';

export const getUserInfoFromHref = (searchParams: string) => {
	if (searchParams) {
		let searchArr = searchParams.split('&');
		searchArr.forEach(item => {
			const [key, value] = item.split('=');
			localStorage.setItem(key, value);
		});
	}
};

export const getToken = () => {
	return localStorage.getItem('kdzsMallToken') || '';
};

export const getUserId = () => {
	return localStorage.getItem('userId') || '';
};

export const getUserInfo = () => {
	return JSON.parse(sessionStorage.getItem('userInfo') || '{}');
};

export const unique = (arr) => {
	return [...(new Set(arr))];
};

/**
 * 获取订购链接
 * @param userId 用户Id
 * @returns
 */
export const getPayOrderURL = (userId:string|number) => {
	const kdzsErpToken = utilsGetToken();
	return `${location.origin}/orderRecord/forward?userId=${userId}&kdzserptoken=${kdzsErpToken}`;
	// return `${location.origin}/index.html?#/payGuide`;
};

export const getMd5Str = (str: string = '') => md5(str);

export const randomHex = (n: number = 12) => (Math.random() * 16).toString(16).substring(2, n + 2);

export function getCookie(name: string) {
	let arr; let reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");

	arr = document.cookie.match(reg);
	return arr ? decodeURIComponent(arr[2]) : null;
}

export const getMaxVal = (val1: number | string, val2: number | string) => {
	val1 = +val1;
	val2 = +val2;
	return (val1 > val2 ? val1 : val2);
};
export const setAdParams = (customRefererUri?:string) => {
	return Promise.resolve(
		userStore.getUserInfo().then((userInfo) => {
			if (!userInfo) return;
			const { expireTime, userName, userId, version, subUserId, level, remainingTime } = userInfo;
			const adConfig = {
				appKey: 'kdzsEPR', // appkey （必传）
				userId: userId || subUserId, // 主账号id （必传）
				userNick: userName, // 主账号nick（必传）
				system: 'web', // 系统名（必传）（手机端为：Andriod,iOS，千年PC端小程序传值“qapc”,web端传“web”）
				// endDay: 90, // 到期天数
				level, // 版本
				endDay: remainingTime,
				shopType: '', // 店铺类型
				shopCid: '', // 店铺行业（有就传，没有传空）
				shopLevel: '', // 店铺信誉等级（有就传，没有传空）
				refererUri: customRefererUri || location.href, // [bool|string]  调用页面的域名。  为true则自动获取地址栏?前的内容。  也可以自己传url。  不传则是当前域名。
				// refererUri: customRefererUri || location.href.replace("192.168.59.246:8088", "erp.kuaidizs.cn"), // [bool|string]  调用页面的域名。  为true则自动获取地址栏?前的内容。  也可以自己传url。  不传则是当前域名。
				// customKeyValue: {
				// http://middle-ad-admin.kuaidizs.cn
				// http://middle-ad-web.kuaidizs.cn
				// 	key1: '文字1',
				// 	key2: '文字2',
				// }
			};
			if (process.env.NODE_ENV === 'development') {
				window.Ads(adConfig);
			} else {
				window.Ads.getUserInfo(adConfig);
			}
		})
	);
};
export const calcDomPostion = (parnetDom: any, domWidth: number = 300, domHeight: number = 300) => {
	let winHeight = 0;
	let winWidth = 0;
	if (window.innerWidth) {
		winWidth = window.innerWidth;
	} else if ((document.body) && (document.body.clientWidth)) {
		winWidth = document.body.clientWidth;
	}
	if (window.innerHeight) {
		winHeight = window.innerHeight;
	} else if ((document.body) && (document.body.clientHeight)) {
		winHeight = document.body.clientHeight;
	}
	// 边界判断
	const pageY = (winHeight - parnetDom.clientY - domHeight) < 0 ? parnetDom.clientY + ((winHeight - parnetDom.clientY - domHeight)) : parnetDom.clientY - 20;
	const pageX = (winWidth - parnetDom.clientX - domWidth) < 0 ? parnetDom.clientX + (winWidth - parnetDom.clientX - domWidth) : parnetDom.clientX - 80;
	return {
		x: pageX + 'px',
		y: pageY + 'px'
	};
};

// 调换数组内两位素位置
export const swapArray = (arr:any[], index1:number, index2:number) => {
	arr[index1] = arr.splice(index2, 1, arr[index1])[0];
	return arr;
};

export const sleep = (time: number) => {
	return new Promise((resolve) => {
		setTimeout(resolve, time);
	});
};

interface IRepeatReqItem {
	funcName: string;
	isRequset: boolean;
	result: any;
}

let repeatReqArr: IRepeatReqItem[] = [];
// 避免重复请求
export const avoidRepeatReq = async(func: Function, params: any) => {
	let curReq = repeatReqArr.find(item => item.funcName === func.name);
	if (curReq && curReq.isRequset) {
		while (curReq.isRequset) {
			// eslint-disable-next-line no-await-in-loop
			await sleep(200);
		}
		return curReq.result;
	} else {
		let temp: IRepeatReqItem = {
			funcName: func.name,
			isRequset: true,
			result: null,
		};
		repeatReqArr.push(temp);
		try {
			const res = await func(params);
			temp.result = res;
			temp.isRequset = false;
			repeatReqArr = repeatReqArr.filter(item => item.funcName !== func.name);
			return res;
		} catch (error) {
			temp.result = error;
			temp.isRequset = false;
			repeatReqArr = repeatReqArr.filter(item => item.funcName !== func.name);
			return error;
		}
	}
};
// 点击Table行的时候判断是不是有效点击（点击时没有内容选区并且点击的元素是tr || td）
export const isValidTableRowClick = (e) => {
	const selectObj = window.getSelection();
	const clickElisTdOrTrNode = ['TD', 'TR'].includes(e?.target?.nodeName);
	const hasSelectedText = !!selectObj.toString();
	return clickElisTdOrTrNode && !hasSelectedText;
};

// 登录以后根据不同版本和类型跳转到不同页面
export const loginRedirect = (res) => {
	const md5UserId = getMd5Str(`${res.userId}_${res?.subUserId}`);
	
	// 检查是否有保存的重定向路径
	const redirectPath = sessionStorage.getItem('redirectAfterLogin');
	if (redirectPath) {
		sessionStorage.removeItem('redirectAfterLogin');
		location.href = `?userId=${md5UserId}#${redirectPath}`;
		return;
	}

	if (!(res?.version === 1 || res?.version === 2) || !res?.popupSetting?.initStock) {
		location.href = `?userId=${md5UserId}#/initialize`;
	} else if (res?.userType == USER_TYPE.免费分销商) {
		location.href = `?userId=${md5UserId}#/distribution/MySupplier`;
	} else if (res?.userType == USER_TYPE.免费版供应商用户) {
		location.href = `?userId=${md5UserId}#/distribution/myDistributor`;
	} else {
		local.set('syncTradeFlag', true);
		location.href = `?userId=${md5UserId}#/`;
	}
};

// 位置碰撞检测，用于计算鼠标点击位置距离视窗的横纵坐标，如果小于弹出层的宽度X或者小于弹出层的高度Y，就返回正确的位置值
export const detectMousePosition = (event, X, Y) => {
	const x = event.clientX;
	const y = event.clientY;

	const deltaX = Math.min(x, window.innerWidth - x);
	const deltaY = Math.min(y, window.innerHeight - y);

	let position = '';
	if (deltaX < X || deltaY < Y) {
		if (deltaX < X) {
			position += x <= window.innerWidth / 2 ? 'Right' : 'Left';
		} else {
			position += 'Center';
		}
		if (deltaY < Y) {
			position = y <= window.innerHeight / 2 ? 'bottom' + position : 'top' + position;
		}
		if (position === 'bottomRight') {
			position = 'bottomLeft';
		} else if (position === 'topRight') {
			position = 'topLeft';
		}
		return position;
	} else {
		return "bottomCenter";
	}
};



export const getNavigatorConnectionDownLink = () => {
	return window.navigator?.connection?.downlink || -1;
};

export const accMul = (num1, num2) => {
	return (num1 * num2).toFixed(2);
};

export const capitalizeFirstLetter = (str: string) => {
	return str.charAt(0).toUpperCase() + str.slice(1);
};

export const changeItemData = ({ itemObj, data, keys }) => {
	const _data = _.cloneDeep(data);
	const index = _data.findIndex((item, index) => {
		let isFind = true;
		keys.forEach(key => {
			if (item[key] !== itemObj[key]) {
				isFind = false;
			}
		});
		return isFind;
	});
	if (index > -1) {
		_data[index] = {
			..._data[index],
			...itemObj
		};
	}
	return _data;
};

export const switchProtocolToHTTPS = () => {
	if (location.protocol === 'http:' && !isViteDevMode()) {
		location.protocol = 'https:';
	}
};

export const getTopContainerHeight = () => {
	// 计算下吸顶的距离
	const topContainer = document.querySelector(`.header-menu`);
	const height = topContainer?.getBoundingClientRect()?.bottom || 92;
	return height;
};

// 计算一个通用的弹框中的表格的滚动高度，防止大小屏幕样式不太兼容问题
export const getModalTableScrollHeight = () => {
	const width = window.innerWidth;
	let scrollY = 400;
	// 根据常见屏幕宽度设置 scrollY
	if (width <= 1024) {
		scrollY = 120;
	} else if (width > 1024 && width <= 1366) {
		scrollY = 220;
	} else if (width > 1366 && width <= 1600) {
		scrollY = 320;
	} else if (width > 1600 && width <= 1920) {
		scrollY = 420;
	} else if (width > 1920 && width <= 2560) {
		scrollY = 600;
	} else if (width > 2560 && width <= 3840) {
		scrollY = 700;
	} else {
		scrollY = 800;
	}
	return scrollY; // 根据需要在不同场景下适当增减
};
// 判断是否在范围内，value 传入[a,b],range [min,max]
export const isInRange = (value = [], range = []) => {
	const [A, B] = value;
	const a = A !== undefined ? Number(A) : undefined;
	const b = B !== undefined ? Number(B) : undefined;
	const [min, max] = range;

	// 如果没有提供 min 和 max，返回 true
	if (min === undefined && max === undefined) {
		return true;
	}

	if (isNaN(a) || isNaN(b)) {
		return false;
	}

	// 如果没有提供 a 和 b，返回 false
	if (a === undefined || b === undefined) {
		return false;
	}

	// 检查 b 是否大于 a
	if (b < a) {
		return false;
	}

	// 处理 a 和 b 的默认值
	const lowerBound = a !== undefined ? a : -Infinity; // 如果 a 没有提供，设为负无穷
	const upperBound = b !== undefined ? b : Infinity; // 如果 b 没有提供，设为正无穷

	// 判断范围
	const isMinValid = min === undefined || (lowerBound >= min && upperBound >= min); // lowerBound 和 upperBound 都要大于等于 min
	const isMaxValid = max === undefined || (lowerBound <= max && upperBound <= max); // lowerBound 和 upperBound 都要小于等于 max

	return isMinValid && isMaxValid;
};

export const escapeRegExp = (string) => {
	return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // $& 表示整个匹配的字符串
};


// 排序方法， isToNumber转成数字比较 isAscending 升序还是降序
export const sorterFun = (key:string, isToNumber = false, isAscending = true) => {
	return (a, b) => {
		if (a[key] === undefined || a[key] === null) return 1;
		if (b[key] === undefined || b[key] === null) return -1;

		// 需要知道是字符串
		if (isToNumber) {
			a[key] = Number(a[key]);
			b[key] = Number(b[key]);
		}

		// 处理数字和字符串的比较
		if (typeof a[key] === 'number' && typeof b[key] === 'number') {
			return (a[key] - b[key]) * (isAscending ? 1 : -1); // 数字比较
		}
		
		const comparisonResult = a?.[key]?.localeCompare(b?.[key]); // 字符串比较
		return isAscending ? comparisonResult : -comparisonResult; // 根据升序或降序返回结果
	};
};

export const isWindows = /win32|win64|windows|wow64/i.test(navigator.userAgent);
export const isMac = /macintosh|mac os x/i.test(navigator.userAgent);

// 移除零宽字符和方向控制字符
export const removeZeroWidthChars = (str: string) => {
	if (!str) return '';
	
	// 使用Unicode码点范围，避免直接写入控制字符
	return String(str)
		.replace(/[\u200B-\u200D]/g, '') // 零宽空格、非连字符、连字符
		.replace(/[\u202A-\u202E]/g, '') // 方向控制字符
		.replace(/[\u2066-\u2069]/g, '') // 方向隔离字符
		.replace(/\uFEFF/g, ''); // 字节顺序标记
};