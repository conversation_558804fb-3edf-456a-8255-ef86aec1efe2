/*
 * @Author: try <EMAIL>
 * @Date: 2024-04-09 17:11:29
 * @Description:
 */

import cs from 'classnames';
import { observer } from 'mobx-react';
import React from 'react';
import userStore from '@/stores/user';
import message from '@/components/message';

export enum FunctionPermissionEnum {
	爆款查询 = "TOP_SELLING",
	预设条件管理 = "PRE_CONDITION_MANAGEMENT",
	手工录入订单 = "HAND_ORDER",
	批量导入订单 = "IMPORT_ORDER",
	打印快递单 = "PRINT_KDD",
	打印发货单 = "PRINT_FHD",
	发货 = "BATCH_SEND",
	自动发货 = "AUTO_SEND",
	验单发货 = "VERIFY_ORDER_SHIP",
	重新发货 = "RESEND_TRADE",
	批量复制 = "BATCH_COPY",
	批量回收单号="BATCH_RECYCLE",
	批量导出 = "BATCH_EXPORT",
	发件人信息编辑 = "EDIT_SENDER_INFO",

	/**
	 * 货品功能权限
	 */

	从线上店铺导入 = "SYS_ITEM_IMPORT_FROM_ONLINE",
	手工创建 = "SYS_ITEM_HAND_CREATE",
	导入货品资料 = "SYS_ITEM_IMPORT",
	导出货品资料 = "SYS_ITEM_EXPORT",
	货品批量修改 = "SYS_ITEM_BATCH_UPDATE",
	货品打印 = "SYS_ITEM_PRINT",
	货品库存同步 = "SYS_ITEM_STOCK_SYNC",
	货品打印设置 = "SYS_ITEM_PRINT_SET",
	货品修改增加分类 = "SYS_ITEM_ADD_CLASSIFY",
	货品编辑 = "SYS_ITEM_UPDATE",
	货品克隆 = "SYS_ITEM_CLONE",
	货品删除 = "SYS_ITEM_DEL",
	批量填充平台信息 = "SYS_ITEM_EDIT_FROM_ONLINE",
	货位管理 = "WAREHOUSE_SLOT_MANAGE_PERMISSION",
	Excel导入货品 = "SYS_ITEM_IMPORT",
	导出 = "SYS_ITEM_EXPORT",


	/**
	 * 组合功能权限
	 */
	将指定货品转为组合货品 = "GROUP_TRANSFORM_GROUP",
	新建组合 = "GROUP_CREATE",
	导入组合货品 = "GROUP_IMPORT",
	导出组合货品 = "GROUP_EXPORT",
	组合批量修改 = "GROUP_BATCH_UPDATE",
	组合货品打印 = "GROUP_PRINT",
	组合货品库存同步 = "GROUP_STOCK_SYNC",
	组合货品打印设置 = "GROUP_PRINT_SET",
	组合货品修改增加分类 = "GROUP_ADD_CLASSIFY",
	组合货品编辑 = "GROUP_UPDATE",
	组合货品克隆 = "GROUP_CLONE",
	组合货品删除 = "GROUP_DEL",
	组合批量填充平台信息='GROUP_EDIT_FROM_ONLINE',

	/**
	 * 商品功能权限
	 */

	下载平台商品 = "ITEM_DOWNLOAD_FROM_ONLINE",
	导出平台商品 = "ITEM_EXPORT",
	导入平台商品 = "ITEM_IMPORT",
	平台批量更新编码 = "ITEM_BATCH_UPDATE_OUTERID",
	复制批量复制 = "ITEM_BATCH_CLONE",
	解除批量解除 = "ITEM_BATCH_REMOVE",
	删除批量删除 = "ITEM_BATCH_DEL",
	确认批量确认 = "ITEM_BATCH_CONFIRM",
	商品添加 = "ITEM_ADD",
	商品更换 = "ITEM_REPLACE",
	手动合并 = "ITEM_RECOMMEND_ITEM_MERGE_HAND",
	同款推荐合并 = "ITEM_RECOMMEND_ITEM_MERGE",

	/**
	 * 库存功能权限
	 */
	导入库存 = "STOCK_IMPORT",
	导出库存 = "STOCK_EXPORT",
	修改实际总库存 = "STOCK_UPDATE_ACTUAL_STOCK",

	波次管理 = 'waveManagement'
}

const FunctionPermissionCheck = ({ functionPermission, children, noFitWidth = true }: {
	functionPermission: FunctionPermissionEnum,
	children: any,
	noFitWidth?: boolean;
}) => {
	const { userInfo: { userName, subUserId }, hasFunctionPermission } = userStore;

	if (!hasFunctionPermission(functionPermission) && subUserId) {
		return (
			<div className={ cs("g-nopermission-opt", noFitWidth ? '' : 'g-width-fit') } onClick={ () => { message.warning(`该账号无操作权限，请联系主账号（${userName}）`); } }>
				<div style={ { pointerEvents: "none" } }>
					{children}
				</div>
			</div>
		);
	} else {
		return <>{children}</>;
	}
};

export default observer(FunctionPermissionCheck);
