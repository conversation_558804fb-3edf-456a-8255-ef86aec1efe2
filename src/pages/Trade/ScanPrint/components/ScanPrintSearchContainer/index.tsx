import React, { useEffect, useMemo, useRef, useState, useCallback } from "react";
import cs from 'classnames';
import { Button, Form, Input, Modal, Popover, Row, Select } from "antd";
import { useSetState, useToggle } from "ahooks";
import { intersection, debounce } from "lodash";
import Checkbox, { CheckboxChangeEvent } from "antd/lib/checkbox";
import { observer } from "mobx-react";
import { useHistory } from "react-router-dom";
import { CloseOutlined, SettingOutlined, SettingTwoTone, ExclamationCircleOutlined } from "@ant-design/icons";
import message from '@/components/message';
import Icon from '@/components/Icon';
import s from '../../index.module.scss';
import { getAllPlats, getAllShopMap, getPlatAndShops, getShopsByPlat } from "@/components-biz/ShopListSelect/shopListUtils";
import { IShopInfo } from "@/types/schemas/user";
import { getShopStatus, PLAT_HAND, PLAT_MAP, PLAT_OTHER, PLAT_SPH, PLAT_XHS, TEMPLATE_ZMJ, PLAT_ALI, PLAT_TB, PLAT_C2M, PLAT_KTT, PLAT_YZ, PLAT_DW } from "@/constants";
import { platform } from "@/types/schemas/common";
import CustomizeSmallEmpty from "@/components/CustomizeRenderEmpty/CustomizeSmallEmpty";
import { ShopMultiSelectValue } from "@/components-biz/ShopListSelect/shopMultiSelect";
import scanPrintStore from "@/stores/trade/scanPrint";
import { IScanPrintFormSetting } from "@/pages/Trade/interface";
import { tradeStore } from "@/stores";
import userStore from "@/stores/user";
import event from "@/libs/event";
import { getTradeStatusLabel } from "@/pages/Trade/utils";
import { local } from "@/libs/db";
import { templateType4PlatformTypeEnum } from "@/pages/Trade/constants";
import sendPoint from "@/utils/pointTrack/sendPoint";
import Pointer from "@/utils/pointTrack/constants";
import { ItemTakeGoodsLabelConfigEditApi } from "@/apis/trade/takeGoodsLabel";
import { IKddTemp } from "@/types/schemas/report";
import { TagNumEnum, TemplateEnum, SET_CURRENT_SCAN_DATA_INFO, UPDATE_FORM_VALUES, showShortNickLocal, updateLabelInfo } from "../../constants";
import useGetState from "@/utils/hooks/useGetState";
import { TradeQueryBgTradeApi } from "@/apis/report/kddLog";
import { checkShopTemp } from "@/utils/print/shopTemp";
import { sleep } from "@/utils/util";
import SelectShootingDeviceModal from "@/components-biz/SelectShootingDeviceModal";
import videoMonitorStore from "@/stores/trade/videoMonitor";
import { focusLabelInput } from "../../utils";

const SELECTED_SHOOTING_DEVICES_CACHE_KEY = 'SELECTED_SHOOTING_DEVICES_SCAN_PRINT';

export interface IScanPrintSearchContainerProps {
	value?: ShopMultiSelectValue;
	onChange?: (value: IScanPrintFormSetting) => void;
	onFinish?: (value: IScanPrintFormSetting) => void;
	loading?: boolean;
}

const formStyle = {
	labelCol: { span: 8 },
	wrapperCol: { span: 16 },
};

let isEditSetting = false;

const filterTemp = (item) => {
	return item.dictStatus !== TEMPLATE_ZMJ && item.KddType != 2;
};

const { Option } = Select;
const ScanPrintSearchContainer = (props: IScanPrintSearchContainerProps) => {
	const { value = {}, onChange, onFinish, loading } = props;
	const { plats = [], plat_sellerIds = [] } = value;
	const [shopValue, setShopValue, getShopValue] = useGetState<string[]>(plat_sellerIds);
	const [platValue, setPlatValue] = useState<platform[]>(plats);
	const [checkAllPlat, setCheckAllPlat] = useToggle();
	const [checkAllShop, setCheckAllShop] = useToggle();
	const shopValueRef = useRef<string[]>();
	const allShopMapRef = useRef<{ [K:string] : IShopInfo}>();
	const popupContainerRef = useRef();
	const [printerList, setPrinterList] = useState([]);
	const [currentTemp, setCurrentTemp] = useState(null);
	const [showShortNick, setShowShortNick] = useState<boolean>(false);
	const [isShowKddType2Modal, setIsShowKddType2Modal] = useState<boolean>(false); // 网点面单提示弹框
	const [isShowKddType2Tip, setIsShowKddType2Tip] = useState<boolean>(false); // 网点面单提示文字
	const [senderSettingVisible, setSenderSettingVisible] = useState<boolean>(false);
	const [useShopBindSender, setUseShopBindSender, getUseShopBindSender] = useGetState<boolean>(false);
	const [scanDataInfo, setScanDataInfo, getScanDataInfo] = useGetState({});
	const [tagTemplateText, setTagTemplateText] = useState<string | JSX.Element>("");
	const [tagPrintNumText, setTagPrintNumText] = useState<string | JSX.Element>("");

	const history = useHistory();
	const {
		setIsShowScanPrintSettingModal,
		scanPrintSetting,
		setScanPrintSetting,
		scanPrintTempInfo,
		printersList,
		defaultPrinter,
		getScanPrintSetting,
		skipMergePrintModal,
		takeGoodsLabelInfos,
		packageInfoVos,
		videoMonitorState,
		setVideoMonitorState,
		selectedShootingDevices,
		setSelectedShootingDevices,
		nvrDevices,
		setNvrDevices,
	} = scanPrintStore;

	const {
		isMergePrint,
		kddTempList,
		expressTemplateList,
		smartExpressSwitch,
		smartExpressList,
		getSmartExpressSwitch,
	} = tradeStore;
	const { senderSetting } = userStore;
	const [form] = Form.useForm();

	const [shopInfo, setShopInfo] = useSetState<{
		platList: platform[]
		shopList: IShopInfo[]
	}>({
		platList: [],
		shopList: []
	});
	const { userInfo } = userStore;

	const { 
		checkKdzsPrintComponent,
		connectWs,
		disconnectWs,
		videoMonitor,
		isSocketConnected
	} = videoMonitorStore;
	const [isShowChooseModel, setIsShowChooseModel] = useState(false); // 是否显示选择拍摄设备

	// 校验并更新缓存的设备信息
	const validateAndUpdateCachedDevices = async() => {
		try {
			// 获取缓存的设备信息
			const cachedDevices = local.getByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY) || [];
			
			if (cachedDevices.length === 0) {
				return;
			}
	
			// 获取最新的设备列表
			const currentDeviceList = await videoMonitorStore.getDeviceCameraList();
			// 过滤掉禁用的设备
			const enabledDevices = currentDeviceList.filter(item => item.status === 1);
	
			// 校验缓存中的设备是否还存在
			const validDevices = cachedDevices.filter((cachedDevice: any) => enabledDevices.find((device: any) => device.id === cachedDevice.id));
	
			// 校验是否属于同一个NVR
			if (validDevices.length > 0) {
				const nvrIds = validDevices.map((device: any) => device.deviceId);
				const uniqueNvrIds = Array.from(new Set(nvrIds));
				if (uniqueNvrIds.length > 1) {
					// 只保留第一个NVR下的设备
					const firstNvrId = nvrIds[0];
					const sameNvrDevices = validDevices.filter((device: any) => device.deviceId === firstNvrId);
					local.setByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY, sameNvrDevices);
					setSelectedShootingDevices(sameNvrDevices);
				} else {
					local.setByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY, validDevices);
					setSelectedShootingDevices(validDevices);
				}
			} else {
				local.setByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY, []);
				setSelectedShootingDevices([]);
			}
	
			// 获取nvr设备列表
			const nvrDevices = await videoMonitorStore.getDeviceList();
			setNvrDevices(nvrDevices);
		} catch (error) {
			console.error('校验缓存设备失败:', error);
			// 清除无效缓存
			local.removeByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY);
			setSelectedShootingDevices([]);
			setNvrDevices([]);
		}
	};
	const [ishow, setIsShow] = useState(false); // 新增状态
	const handleReconnect = async() => {
		console.log('重新连接');
		await validateAndUpdateCachedDevices();
		await checkKdzsPrintComponent();
		connectWs();
	};
	useEffect(() => {
		const init = async() => {
			// await	getScanPrintSetting();
			if (videoMonitorState && videoMonitor) {
				handleReconnect();
			}
		};
		init();
		return () => {
			disconnectWs();
		};
	}, [videoMonitor, userInfo?.userId]);
	useEffect(() => {
		if (videoMonitorState && videoMonitor) {
			handleReconnect();
		} else {
			disconnectWs(); // 断开ERP聚合控件连接
		}
	}, [videoMonitorState, videoMonitor]);
		   // 监听状态变化，更新 ishow
	useEffect(() => {
		console.log(videoMonitor, videoMonitorState, 'videoMonitor, videoMonitorState');
		// 确保 userInfo 已经加载完成
		if (userInfo?.userId) {
			const newIshow = videoMonitor == 1 && videoMonitorState;
			setIsShow(newIshow);
		}
	}, [videoMonitor, videoMonitorState, userInfo?.userId]);
	// 处理选择拍摄设备确认
	const handleSelectShootingDeviceConfirm = (selectedDevices: any[]) => {
		// 缓存选中的设备
		local.setByUserId(SELECTED_SHOOTING_DEVICES_CACHE_KEY, selectedDevices);
		setSelectedShootingDevices(selectedDevices);
		setIsShowChooseModel(false);
	};
	
	// 处理选择拍摄设备取消
	const handleSelectShootingDeviceCancel = () => {
		setIsShowChooseModel(false);
	};
	
	// 获取选中的设备名称显示文本
	const getSelectedDevicesText = () => {
		if (selectedShootingDevices.length === 0) {
			return "请选择拍摄设备";
		} else {
			return selectedShootingDevices.map(item => item.cameraName).join(",");
		}
	};

	const customLogPost = (dataType: string, data:any = {}) => {
		window.errorCollection?.customMessageUpload({
			type: `扫描打印【${dataType}】`,
			data: {
				isMergePrint,
				...data
			}
		});
	};
		
	useMemo(async() => {
		allShopMapRef.current = await getAllShopMap({ hasHand: true });
	}, []);
	const [scanPrintSettingSearch, setScanPrintSettingSearch] = useState(scanPrintSetting);

	// 这里去重下
	const smartExpressObj = useMemo(() => {
		const obj = {};
		expressTemplateList?.forEach(i => {
			obj[i.Mode_ListShowId] = i;
		});
		return obj;
	}, [expressTemplateList]);

	// 修改打印后自动发货
	const changeAutoSend = (res) => {
		return new Promise((resolve, reject) => {
			if (!!res.autoSend !== !!scanPrintSetting.scanSet.autoSend) {
				isEditSetting = true;
	
				Modal.confirm({
					title: '系统提醒',
					content: (
						<div>
							是否确定
							<span className="r-c-error">{res.autoSend ? '勾选' : '取消勾选'}</span>
							打印后自动发货？
						</div>
					),
					okText: '确 定',
					cancelText: '取 消',
					closable: false,
					maskClosable: false,
					onCancel() {
						isEditSetting = false;
						form.setFieldsValue({
							'autoSend': !res.autoSend
						});
						resolve(true);
					},
					async onOk() {
						await ItemTakeGoodsLabelConfigEditApi({
							scanSet: {
								...scanPrintSetting.scanSet,
								autoSend: res.autoSend ? 1 : 0,
							}
						});
			
						scanPrintSetting.scanSet.autoSend = res.autoSend ? 1 : 0;
						isEditSetting = false;

						resolve(true);
					}
				});
			} else {
				resolve(true);
			}
		});
	};

	// 显示网点面单弹框
	const showConfirm = () => {
		let isHide = local.get('scanPrint.isNotShowKddType2Modal') ?? false;
		if (!isHide) {
			setIsShowKddType2Modal(true);
		}
	};

	const mergePrintModal = () => {
		Modal.confirm({
			title: '提示',
			content: '请前往订单处理页面开启融合打印后，再选择多平台店铺',
			okText: '前往订单页面',
			onOk: () => {
				history.push('/trade');
			}
		});
	};

	const checkMultiplePlatform = (shopValue: string[]) => {
		let arr = shopValue.map(item => {
			return item.split('_')[0];
		});
		console.log(arr, 'checkMultiplePlatform');
		return [...new Set(arr)].length > 1;
	};

	const handleChange = async() => {
		try {
			const res = await form.validateFields();
			console.log('%c [ 111 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', res);
			if (isEditSetting) {
				message.info('操作太快了');
				form.setFieldsValue({
					'autoSend': !res.autoSend
				});
				return;
			}

			// 等待二次确认弹框操作结果,不管取消还是确认都继续执行后面程序
			await changeAutoSend(res);
			// console.log('%c [ 继续执行 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', '');

			if (res.templateInfo !== currentTemp && !isMergePrint) {
				let temp = kddTempList.find(item => item.Mode_ListShowId === res.templateInfo);
				await checkShopTemp(temp);
			}
			
			if (res.templateInfo && res.templateInfo !== currentTemp) {
				console.log('%c [ 222 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', currentTemp);
				setCurrentTemp(res.templateInfo);
				return;
			}
			
			if (res.templateInfo && !isMergePrint) {
				let temp = kddTempList.find(item => item.Mode_ListShowId === res.templateInfo);
				if (temp?.dictStatus === TEMPLATE_ZMJ) {
					message.info({ content: '该快递模板已开启子母件，暂不支持扫描打印', key: 'TEMPLATE_ZMJ_MSG' });
					return;
				}
				if (temp?.KddType == 2) {
					setIsShowKddType2Tip(true);
					showConfirm();
				} else {
					setIsShowKddType2Tip(false);
				}
			}

			onChange?.({
				...res,
				shopValue,
				platValue,
			});
		} catch (error) {
			isEditSetting = false;
			
			if (error?.values) {
				onChange?.({
					...(error?.values || {}),
					shopValue,
					platValue,
				});
			}
			
			console.log('%c [ handleChange error]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', error);
		}
	};

	const handleSenderSetting = () => {
		getScanPrintSetting().then(res => {
			if (res?.scanSet) {
				setUseShopBindSender(res.scanSet.useShopBindSender == 1);
				form.setFieldsValue({
					autoSend: res.scanSet?.autoSend,
				});
				handleChange();
			}
		});
	};

	useMemo(() => {
		shopValueRef.current = shopValue ? [...shopValue] : [];
	}, [shopValue]);

	const handlePrintTagSetting = () => {
		userStore.getSystemSetting().then(res => {
			const printTagSet = res.printTagSet || "{}";
			try {
				const printTagSetObj = JSON.parse(printTagSet);
				const { printTagNumSwitch, tagTemplateSwitch, printTagNum, tagTemplate } = printTagSetObj;
				if (printTagNumSwitch == TagNumEnum["与商品数量一致"]) {
					setTagPrintNumText("与商品数量一致");
				} else if (printTagNumSwitch == TagNumEnum["设置固定数量"]) {
					const domContent = <span>每个商品打印<span style={ { color: "#1890ff" } }>{printTagNum}</span>份</span>;
					setTagPrintNumText(domContent);
				}
				if (tagTemplateSwitch == TemplateEnum["无"]) {
					const domContent = <span className="r-c-error">未设置模板</span>;
					setTagTemplateText(domContent);
				} else if (tagTemplateSwitch == TemplateEnum["设置默认模板"]) {
					setTagTemplateText(tagTemplate?.ExcodeName);
				}
			} catch (e) { console.log(e); }
		});
	};

	const changePlats = (plats:platform[]) => {
		getShopsByPlat({ plats, hasHand: true }).then((newShopList) => {
			setShopInfo({
				shopList: newShopList,
			});

			// 更新选中的店铺， 如果删除某个平台，需要删除该平台已选中的店铺
			const newShopValue = intersection(shopValueRef.current, newShopList.map(item => `${item.platform}_${item.sellerId}`));
			setShopValue(newShopValue);
		});
	};

	const handleChangePlat = (val:platform[]) => {
		// 由于用户未开启融合打印时，平台和模板需要有联动，所以，选了多个平台时，必须开启融合打印功能，才支持多个平台的快递单打印
		if (!isMergePrint && val.length > 1) {
			mergePrintModal();
			return;
		}
		setPlatValue(val);
		changePlats(val);
	};

	const handleSelectAllPlat = (e: CheckboxChangeEvent) => {
		let newValue:platform[];
		const isChecked = e.target.checked;
		if (isChecked) {
			newValue = [...shopInfo.platList];
		} else {
			newValue = [];
		}

		if (!isMergePrint && newValue.length > 1) {
			mergePrintModal();
			return;
		}
		setPlatValue(newValue);
		changePlats(newValue);
	};

	const handleSelectAllShop = (e: CheckboxChangeEvent) => {
		let newValue:string[];
		const isChecked = e.target.checked;
		if (isChecked) {
			newValue = shopInfo.shopList.map(item => `${item.platform}_${item.sellerId}`);
		} else {
			newValue = [];
		}
		if (!isMergePrint && checkMultiplePlatform(newValue)) {
			mergePrintModal();
			return;
		}
		setShopValue(newValue);
	};

	const createKddTemplateModal = () => {
		Modal.confirm({
			title: '提示',
			content: '当前平台未找到对应的快递模板，请创建后再使用扫描打印功能',
			okText: '前往订单页面',
			onOk: () => {
				history.push('/trade');
			}
		});
	};

	const getKddTemListByPlatform = useMemo(() => {
		// * 非融合打印下 平台/模板联动
		if (!isMergePrint) {
			let newKddTempList = kddTempList;
			if (platValue.length && !platValue.includes(PLAT_HAND) && !platValue.includes(PLAT_SPH) && !platValue.includes(PLAT_OTHER) && !platValue.includes(PLAT_XHS) && !platValue.includes(PLAT_KTT)) {
				newKddTempList = kddTempList?.filter(item => {
					if (platValue.includes(PLAT_ALI) || platValue.includes(PLAT_TB) || platValue.includes(PLAT_C2M)) {
						return	[PLAT_TB].includes(templateType4PlatformTypeEnum[item.KddType] as platform);
					}
					return	platValue.includes(templateType4PlatformTypeEnum[item.KddType] as platform);
				});
				form.setFieldsValue({
					templateInfo: newKddTempList?.filter(item => filterTemp(item))?.[0]?.Mode_ListShowId,
				});
			} else if (platValue.length && platValue.includes(PLAT_KTT)) {
				// 快团团模板判断：快团团不能使用视频号、有赞、得物模板
				newKddTempList = kddTempList?.filter(item => {
					return	![PLAT_SPH, PLAT_YZ, PLAT_DW].includes(templateType4PlatformTypeEnum[item.KddType] as platform);
				});
				form.setFieldsValue({
					templateInfo: newKddTempList?.filter(item => filterTemp(item))?.[0]?.Mode_ListShowId,
				});
			} else if (platValue.length) {
				form.setFieldsValue({
					templateInfo: kddTempList.filter(item => filterTemp(item))?.[0]?.Mode_ListShowId,
				});
			}
			// 对应的平台没有模板。提示用户去创建模板
			if (platValue.length && !newKddTempList.length) {
				createKddTemplateModal();
			}
			return newKddTempList;
		}
		return kddTempList;
	}, [kddTempList, platValue, isMergePrint]);

	const getSenderByShop = (selectedShop = []) => {
		try {
			if (getUseShopBindSender()) {
				// 先取扫描结果中的的店铺，再取手动下拉中的店铺
				const curScanDataInfo = getScanDataInfo();
				let curSellerId = curScanDataInfo?.sellerId;
				if (selectedShop?.length === 1) {
					curSellerId = selectedShop[0].split("_")?.[1];
				}
				const allShop = Object.values(allShopMapRef.current || {});
				const theShopInfo = allShop.find(shopItem => shopItem.sellerId == curSellerId);
				const theShopBindSenderId = theShopInfo?.senderSetting?.[0];
				if (theShopBindSenderId) {
					return theShopBindSenderId;
				}
				return "";
			}
			return "";
		} catch (e) {
			console.log(e);
		}
	};

	const handleChangeShop = (val: string[]) => {
		sendPoint(Pointer.扫描打印_选择店铺_点击);
		if (!isMergePrint && checkMultiplePlatform(val)) {
			mergePrintModal();
			return;
		}
		setShopValue(val);
		if (useShopBindSender) {
			// 判断是否开启"使用店铺绑定发件人",开启的话，使用店铺绑定的发件人
			const currentTempInfo = isMergePrint ? kddTempList.find(o => o.id == currentTemp) : getKddTemListByPlatform?.find(o => currentTemp == o.Mode_ListShowId);
			const bindFjr = currentTempInfo?.bindFjrId;
			// 先判断选择的模板有没有绑定发件人
			if (!bindFjr && bindFjr != -1) {
				const shopSender = getSenderByShop(val);
				if (shopSender) {
					form.setFieldsValue({
						sender: shopSender
					});
					handleChange();
				}
			}
		}
	};

	const handleFinish = useCallback((val: any) => {
		val = {
			...val,
			platValue,
			shopValue
		};
		if (!val.id) {
			message.error('请先输入或扫描唯一码');
			return;
		}
		if (scanPrintSettingSearch.scanSet?.stockpileAfterScan) {
			val.stockpileAfterScan = scanPrintSettingSearch.scanSet?.stockpileAfterScan;
			onFinish?.(val);
		} else {
			onFinish?.(val);
		}
		focusLabelInput(); // 查询完又选中输入框
	}, [scanPrintSettingSearch, platValue, shopValue, onFinish]);

	// onPressEnter
	const onPressEnter = useCallback((event) => {
		event.preventDefault(); // 阻止默认的表单提交行为
		if (!loading) {
			const values = form.getFieldsValue();
			console.log('Enter finish', values);
			handleFinish(values);
		}
	}, [form, loading, handleFinish]);

	// 唯一码回车、查询按钮点击
	const handlePressEnter = debounce(onPressEnter, 200);

	const getKddTempList = () => {
		const {
			kddTempList,
		} = tradeStore;
		const {
			scanPrintSetting,
			scanPrintFormSetting
		} = scanPrintStore;
		const platValue = scanPrintFormSetting?.platValue;
		if (platValue.length && !platValue.includes(PLAT_HAND) && !platValue.includes(PLAT_SPH) && !platValue.includes(PLAT_OTHER)) {
			let res = kddTempList?.filter(item => platValue.includes(templateType4PlatformTypeEnum[item.KddType] as platform));
			return res;
		}
		return kddTempList;
	};
	// 切换发件人
	const setSender = (sender?:any) => {
		const { senderSetting } = userStore;
		/**
		 * 发件人获取顺序：
		 * 1.优先取模板绑定的发件人
		 * 2.如果开启了”使用店铺绑定发件人“，就使用店铺绑定发件人
		 * 3.取用户上次选择的发件人，存在了local中
		 * 4.取系统设置中的默认发件人
		 * 5.取系统设置中的发件人列表第一个
		 * 4和5直接取senderSetting?.list[0]就行，因为如果设置了默认发件人，一定会置顶
		 */
		const curTemplateId = form.getFieldValue("templateInfo");
		const currentTempInfo = isMergePrint ? kddTempList.find(o => o.id == curTemplateId) : getKddTemListByPlatform?.find(o => o.Mode_ListShowId == curTemplateId);
		const templateSender = currentTempInfo?.bindFjrId; // 模板发件人
		let shopSender = ""; // 店铺绑定的发件人
		if (getUseShopBindSender()) {
			shopSender = getSenderByShop();
		}
		const lastSelectedSender = local.get('scanPrintSearchSetting')?.sender; // 上次选择的发件人
		const systemSender = senderSetting?.list[0].id; // 系统中的发件人
		let sendList = [templateSender, shopSender, lastSelectedSender, systemSender];
		if (senderSetting?.list?.length) {
			for (let id of sendList) {
				if (id && senderSetting.list.find(item => item.id === id) && id != -1) {
					form.setFieldsValue({
						sender: id,
					});
					handleChange();
					return;
				}
			}
		}
	};

	const updateFormValues = (templateInfo) => {
		if (templateInfo) {
			form.setFieldsValue({
				templateInfo: templateInfo.templateId
			});
			setSender(templateInfo.bindFjrId);
			handleChange();
		}
	};

	const setCurrentScanDataInfo = (info) => {
		setScanDataInfo(info);
	};
	
	const changeModeListShowId = async(info) => {
		try {
			const {
				kddTempList,
			} = tradeStore;
			const {
				scanPrintSetting,
				scanPrintFormSetting,
			} = scanPrintStore;
			const { scanChangeExIdForFirstSend, scanChangeExIdForGetYd, scanChangeExIdForNotGetYd } = scanPrintSetting?.scanSet || {};
			
			const setTemplateInfo = (exInfo, type?:string) => {
				console.log('exInfo::', exInfo);

				let exId = '';
				let exName = '';
				
				if (type === 'labelInfo') {
					const { firstSend = false } = exInfo;

					// 判断先发货
					if (firstSend && scanChangeExIdForFirstSend) {
						exId = exInfo?.exId;
						exName = exInfo?.exName;

						if (exId && exName) {
							console.log('%c [ 命中--先发货 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', '');
							customLogPost('模板命中-先发货', {
								exId,
								exName,
								firstSend,
								scanChangeExIdForFirstSend
							});
						}
					} else if (scanChangeExIdForNotGetYd && smartExpressSwitch) { // 如果开了智选快递、且勾选了未申请单号订单自动切换为智选快递推荐的模版
						// 先找到对应的pack
						if (packageInfoVos?.[0]) {
							const pack = packageInfoVos?.[0];
							const { smartExpressTemplate, packSmartExpressTemplate, isMerge } = pack;
							if (smartExpressTemplate || packSmartExpressTemplate) {
								const smartInfo = JSON.parse(isMerge ? packSmartExpressTemplate : smartExpressTemplate);
								const { printTemplateId, isManual, printTemplateName, hasCleared } = smartInfo;
								const smartExpressInfo = smartExpressObj[printTemplateId];
								console.log('%c [ 智选快递 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', smartInfo, smartExpressInfo, smartExpressObj);
								
								// 如果智选快递模板未删除
								if (hasCleared !== 'true') {
									exId = smartExpressInfo?.Exid || printTemplateId;
									exName = smartExpressInfo?.ExcodeName || printTemplateName;

									if (exId && exName) {
										console.log('%c [ 命中--智选快递 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', '命中--');
										customLogPost('模板命中-智选快递', {
											exId,
											exName,
											smartInfo,
											smartExpressInfo
										});
									}
								}
							}
						}
					}
				} else {
					exId = exInfo?.exId;
					exName = exInfo?.exName;

					if (exId && exName) {
						console.log('%c [ 命中--默认模板 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', '命中--默认模板');
						customLogPost('模板命中-默认模板', {
							exId,
							exName,
							type
						});
					}
				}

				if (!exId || !exName) {
					return false;
				}
				
				let _item = null;
				let _id = null;
				if (!isMergePrint) {
					// 如果没有同一模版 选择同一公司模版
					_item = getKddTempList()?.find((item:any) => (item.Exid == exId && item.ExcodeName == exName && filterTemp(item)));
					if (!_item) {
						_item = getKddTempList()?.find((item:any) => (item.Exid == exId && filterTemp(item)));
					}
					_id = _item?.Mode_ListShowId;
				} else {
					console.log('%c [ kddTempList ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', kddTempList);

					// 先找模板组里面的模板id有没有对得上的
					_item = kddTempList.find((item) => (item.groupName == exName && item.userTemplateList?.find((userTempItem) => (userTempItem.exId == exId))));
					if (!_item) {
						_item = kddTempList.find((item) => (item.userTemplateList?.find((userTempItem) => (userTempItem.exId == exId))));
					}

					if (type === 'defaultTemplate' && !_item) {
						// 找不到直接匹配模版组id
						_item = kddTempList.find((item) => (item.groupName == exName && item.exCompanyId == exId));
						if (!_item) {
							_item = kddTempList.find((item) => item.exCompanyId == exId);
						}
						console.log('%c [ 找不到直接找默认模版组 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', _item);
					}
					
					_id = _item?.id;
				}
				console.log("_item:::", _item);
				if (_item) {
					form.setFieldsValue({
						templateInfo: _id
					});
					setSender(_item.bindFjrId);
					handleChange();
					return true;
				}
				return false;
			};
			// 先发货、先申请单号、智选快递判断
			if (!setTemplateInfo(info, 'labelInfo')) {
				let _defaultExInfo = scanPrintSetting?.scanSet?.defaultTemplate;
				// 前面找不到，找默认模板
				if (!(_defaultExInfo && setTemplateInfo(_defaultExInfo, 'defaultTemplate'))) {
					console.log('%c [ 命中--没有默认模板，取第一个模板 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', '命中--没有默认模板，取第一个模板');
					
					// 找不到默认模板，取第一个模板数据
					if (!isMergePrint) {
						let temp = getKddTempList()?.filter(item => item.dictStatus !== TEMPLATE_ZMJ && item.KddType != 2);
						form.setFieldsValue({
							templateInfo: temp[0]?.Mode_ListShowId,
						});
						setSender(temp[0]?.bindFjrId);
						customLogPost('模板命中-使用第一个可用模板', {
							templateId: temp?.[0]?.Mode_ListShowId,
							bindFjrId: temp?.[0]?.bindFjrId
						});
					} else {
						form.setFieldsValue({
							templateInfo: kddTempList[0]?.id,
						});
						setSender(kddTempList[0]?.bindFjrId);
						customLogPost('模板命中-使用第一个可用模板', {
							templateId: kddTempList?.[0]?.id,
							bindFjrId: kddTempList?.[0]?.bindFjrId
						});
					}
				}
			}
			handleChange();
		} catch (error) {
			console.log('changeModeListShowId error:', error);
		}
	};

	const setDefaultExId = (item: IKddTemp) => {
		console.log('setDefaultExId:', item, scanPrintSettingSearch);
		if (item.dictStatus === TEMPLATE_ZMJ) {
			message.info({ content: '该快递模板已开启子母件，暂不支持扫描打印', key: 'TEMPLATE_ZMJ_MSG' });
			return;
		}
		const _scanSet = scanPrintSettingSearch.scanSet;
		if (!isMergePrint) {
			const { Exid, ExcodeName, bindFjrId } = item;
			_scanSet.defaultTemplate = {
				exId: Exid,
				exName: ExcodeName
			};
			setSender(bindFjrId);
		} else {
			const { exCompanyId, groupName, bindFjrId } = item;
			_scanSet.defaultTemplate = {
				exId: exCompanyId,
				exName: groupName
			};
			setSender(bindFjrId);
		}
		handleChange();
		const _scanPrintSetting = {
			...scanPrintSettingSearch,
			scanSet: JSON.stringify(_scanSet),
		};

		ItemTakeGoodsLabelConfigEditApi(_scanPrintSetting).then(res => {
			setScanPrintSetting({
				...scanPrintSettingSearch,
				scanSet: _scanSet,
			});
		});
	};

	const showShortNickOnChange = () => {
		const val = !showShortNick;
		setShowShortNick(val);
		local.set(showShortNickLocal, val);
		event.emit(showShortNickLocal, val);
	};

	const senderSettingOnOk = () => {
		const _scanSet = {
			...scanPrintSettingSearch.scanSet,
			useShopBindSender: getUseShopBindSender() ? 1 : 0
		};
		const _scanPrintSetting = {
			...scanPrintSettingSearch,
			scanSet: JSON.stringify(_scanSet),
		};

		ItemTakeGoodsLabelConfigEditApi(_scanPrintSetting).then(res => {
			setSenderSettingVisible(false);
			setScanPrintSetting({
				...scanPrintSettingSearch,
				scanSet: _scanSet,
			});
		});
	};

	const senderSettingOnCancel = () => {
		setSenderSettingVisible(false);
		getScanPrintSetting().then(res => {
			if (res?.scanSet) {
				setUseShopBindSender(res.scanSet.useShopBindSender == 1);
			}
		});
	};

	const senderSettingContainer = (
		<Popover
			placement="rightTop"
			visible={ senderSettingVisible }
			getPopupContainer={ () => popupContainerRef.current as HTMLDivElement }
			content={ (
				<div>
					<div className={ s["setting-body"] }>
						<div className="r-flex r-jc-sb">
							<span className="r-bold r-fs-16 ">发件人设置</span>
							<span className="r-pointer" onClick={ senderSettingOnCancel }><CloseOutlined /></span>
						</div>
						<div className="r-mt-10">
							<Checkbox checked={ getUseShopBindSender() } onChange={ (e) => setUseShopBindSender(e.target.checked) }>使用店铺绑定发件人</Checkbox>
						</div>
						<div className="r-mt-10" style={ { color: "#fd8305" } }>
							发件人取值优先级：
							<p>
								模板绑定发件人&nbsp;&gt;&nbsp;店铺绑定发件人
							</p>
						</div>
						<div className="r-ta-c r-mt-20">
							<Button type="primary" onClick={ senderSettingOnOk }>确定</Button>
							<Button className="r-ml-10" onClick={ senderSettingOnCancel }>取消</Button>
						</div>
					</div>
				</div>
			) }

		>
			{
				useShopBindSender ? <SettingTwoTone onClick={ () => setSenderSettingVisible(true) } /> : <SettingOutlined onClick={ () => setSenderSettingVisible(true) } />
			}
			<div className={ s["custom-mask"] } hidden={ !senderSettingVisible } />
		</Popover>
	);

	const renderOptionDisabeld = (template: IKddTemp) => {
		return template.dictStatus === TEMPLATE_ZMJ;
	};

	const renderOptionTitle = (template: IKddTemp) => {
		let title = '';
		template.dictStatus === TEMPLATE_ZMJ ? title += '该快递模板已开启子母件，暂不支持扫描打印;' : '';
		return title;
	};

	useEffect(() => {
		// 是否开启了”扫描时自动切换标签关联快递模板“
		const { scanChangeExIdForFirstSend, scanChangeExIdForGetYd, scanChangeExIdForNotGetYd } = scanPrintSetting?.scanSet || {};
		const scanAutoLinkTemp = scanChangeExIdForFirstSend || scanChangeExIdForGetYd || scanChangeExIdForNotGetYd;
		if (takeGoodsLabelInfos && !scanAutoLinkTemp) {
			takeGoodsLabelInfos.forEach(pack => {
				pack.takeGoodsLabelInfos.forEach(labelItem => {
					if (labelItem.currentScan) {
						setScanDataInfo(labelItem);
					}
				});
			});
			setTimeout(() => {
				setSender();
				handleChange();
			});
		}
	}, [takeGoodsLabelInfos, scanPrintSetting?.scanSet]);

	useEffect(() => {
		userStore.getUserInfo().then(async res => {
			const { userId } = res;
			const params = {
				userId,
				pageNo: 1,
				pageSize: 300,
			};
			userStore.getSenderSetting(params);

			getSmartExpressSwitch(); // 获取智选快递开关
		});
		form.setFieldsValue({
			scanNumSetting: local.get('scanPrintSearchSetting')?.scanNumSetting,
			// autoSend: local.get('scanPrintSearchSetting')?.autoSend,
			// printer: local.get('scanPrintSearchSetting')?.printer,
			tagPrinter: local.get('scanPrintSearchSetting')?.tagPrinter,
			// templateInfo: local.get('scanPrintSearchSetting')?.templateInfo,
		});
		handleChange();

		const _showShortNick = local.get(showShortNickLocal);
		setShowShortNick(_showShortNick);
		handleSenderSetting();
	}, []);

	useEffect(() => {
		const isPrintTag = scanPrintSetting?.scanSet?.isPrintTag;
		// 如果需要打印吊牌，那就获取吊牌配置
		if (isPrintTag) {
			handlePrintTagSetting();
		}
		const { scanChangeExIdForFirstSend, scanChangeExIdForGetYd, scanChangeExIdForNotGetYd } = scanPrintSetting?.scanSet || {};
		const allowChangeTemplate = scanChangeExIdForFirstSend || scanChangeExIdForGetYd || scanChangeExIdForNotGetYd;
		if (allowChangeTemplate) {
			event.on('scanPrint.changeModeListShowId', changeModeListShowId);
			event.on(UPDATE_FORM_VALUES, updateFormValues);
			event.on(SET_CURRENT_SCAN_DATA_INFO, setCurrentScanDataInfo);
			return () => {
				event.off('scanPrint.changeModeListShowId', changeModeListShowId);
				event.off(UPDATE_FORM_VALUES, updateFormValues);
				event.off(SET_CURRENT_SCAN_DATA_INFO, setCurrentScanDataInfo);
			};
		}
	}, [scanPrintSetting, smartExpressSwitch, smartExpressObj, packageInfoVos]);

	useEffect(() => {
		if (printersList.length) {
			setPrinterList(printersList);
			const lastSelectedPrinter = local.get('scanPrintSearchSetting')?.printer;
			// 只有打印机还存在于列表中，才去设置默认打印机，可以避免误导用户
			if (printersList.includes(lastSelectedPrinter)) {
				form.setFieldsValue({
					printer: lastSelectedPrinter,
				});
				handleChange();
			}
		}
	}, [printersList]);

	useEffect(() => {
		if (!scanPrintSetting) return;
		let bindFjrId = null;
		let canSetDefault = false;
		const { scanChangeExIdForFirstSend, scanChangeExIdForGetYd, scanChangeExIdForNotGetYd } = scanPrintSetting?.scanSet || {};
		const allowChangeTemplate = scanChangeExIdForFirstSend || scanChangeExIdForGetYd || scanChangeExIdForNotGetYd;

		const { scanChangeExIdForFirstSend: scanChangeExIdForFirstSend2, scanChangeExIdForGetYd: scanChangeExIdForGetYd2, scanChangeExIdForNotGetYd: scanChangeExIdForNotGetYd2 } = scanPrintSettingSearch?.scanSet || {};
		const allowChangeTemplate2 = scanChangeExIdForFirstSend2 || scanChangeExIdForGetYd2 || scanChangeExIdForNotGetYd2;
		// 是否可以设置默认模板
		if (allowChangeTemplate && !allowChangeTemplate2) {
			canSetDefault = true;
		}
		scanPrintSetting && setScanPrintSettingSearch(scanPrintSetting);
		if (kddTempList.length && (!form.getFieldValue('templateInfo') || canSetDefault)) {
			let templateId: number;
			if (!isMergePrint) {
				let temp = null;
				// 如果有设置默认 优先取默认
				if (allowChangeTemplate) {
					const { exName, exId } = scanPrintSetting?.scanSet?.defaultTemplate || {};
					if (exName && exId) {
						temp = kddTempList.find((item) => (item.Exid == exId && item.ExcodeName == exName));
						bindFjrId = temp?.bindFjrId;
					}
				}
				if (!temp) {
					temp = kddTempList.find(item => item.Mode_ListShowId === local.get('scanPrintSearchSetting')?.templateInfo);
				}
				templateId = temp ? temp.Mode_ListShowId : kddTempList[0].Mode_ListShowId;
			} else {
				let temp = null;
				if (allowChangeTemplate) {
					const { exName, exId } = scanPrintSetting?.scanSet?.defaultTemplate || {};
					if (exName && exId) {
						temp = kddTempList.find((item) => (item.exCompanyId == exId && item.groupName == exName));
						bindFjrId = temp?.bindFjrId;
					}
				}
				if (!temp) {
					temp = kddTempList.find(item => item.id === local.get('scanPrintSearchSetting')?.templateInfo);
				}
				templateId = temp ? temp.id : kddTempList[0].id;
			}
			form.setFieldsValue({
				templateInfo: templateId,
			});
		} else if (!kddTempList.length && !form.getFieldValue('templateInfo')) {
			form.setFieldsValue({
				templateInfo: '',
			});
		}
		// 将设置发件人写在了设置完快递单模版后 是因为快递单发件人受模版变化而变化
		if (senderSetting?.list?.length && (!form.getFieldValue('sender') || canSetDefault) && form.getFieldValue('templateInfo')) {
			setSender(bindFjrId);
		}
		if (printersList?.length && !form.getFieldValue('printer')) {
			let printer = local.get('scanPrintSearchSetting')?.printer;
			if (!printersList.includes(printer)) {
				printer = printersList[0];
			}
			form.setFieldsValue({
				printer,
			});
		}

		handleChange();

	}, [kddTempList, senderSetting, isMergePrint, printersList, scanPrintSetting?.scanSet, scanPrintSettingSearch?.scanSet]);

	useEffect(() => {
		const init = async() => {
			let platList = await getAllPlats(true);
			let shopList = await getShopsByPlat({ hasHand: true });
			setShopInfo({
				platList,
				shopList
			});
		};
		init();
	}, [setShopInfo]);

	useEffect(() => {
		if (platValue?.length && platValue?.length === shopInfo?.platList?.length) {
			setCheckAllPlat.setRight();
		} else {
			setCheckAllPlat.setLeft();
		}
	}, [platValue?.length, setCheckAllPlat, shopInfo?.platList?.length]);

	useEffect(() => {
		if (shopValue?.length && shopValue?.length === shopInfo?.shopList?.length) {
			setCheckAllShop.setRight();
		} else {
			setCheckAllShop.setLeft();
		}
	}, [setCheckAllShop, shopInfo?.shopList?.length, shopValue?.length]);

	useEffect(() => {
		handleChange();
	}, [shopValue, platValue]);

	useEffect(() => {
		let currentTempInfo = isMergePrint ? kddTempList.find(o => o.id == currentTemp) : getKddTemListByPlatform?.find(o => currentTemp == o.Mode_ListShowId);
		console.log('%c [ 333 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', currentTemp, currentTempInfo);

		let bindFjr = currentTempInfo?.bindFjrId;
		let defaultPrint = currentTempInfo?.defaultPrinter;
		
		// 模板绑定发件人的时候，取模板绑定的发件人
		if (defaultPrint) {
			let isCurrentDevice = printerList.find(o => o === defaultPrint);
			if (!isCurrentDevice) defaultPrint = defaultPrinter;
			if (printersList.includes(defaultPrint)) {
				form.setFieldsValue({
					printer: defaultPrint
				});
			}
		}
		if (bindFjr && bindFjr != -1) {
			form.setFieldsValue({
				sender: bindFjr
			});
		} else if (getUseShopBindSender()) {
			// 判断是否开启"使用店铺绑定发件人",开启的话，使用店铺绑定的发件人
			const shopSender = getSenderByShop();
			if (shopSender && shopSender != -1) {
				form.setFieldsValue({
					sender: shopSender
				});
			}
		}
		handleChange();
	}, [currentTemp, printersList, kddTempList, isMergePrint, printerList, getKddTemListByPlatform]);

	return (
		<div className={ cs(s['scanPrintSearchContainer']) }>
			<Form
				// onFinish={ handleFinish }
				onChange={ handleChange }
				form={ form }
				{ ...formStyle }
				size="small"
			>
				<Form.Item label="平台">
					<Select
						mode="multiple"
						placeholder="选择平台"
						optionLabelProp="label"
						className={ cs(s['select-plat'], 'r-w-full') }
						value={ platValue }
						onChange={ handleChangePlat }
						tagRender={ (props) => {
							if (checkAllPlat) {
								if (platValue?.[0] && platValue?.[0] === props.value) {
									return <span className="kdzs-select-selection-item">所有平台</span>;
								} else {
									return <></>;
								}
							} else if (platValue.length > 1) {
								if (platValue?.[0] && platValue?.[0] === props.value) {
									return <span className="kdzs-select-selection-item">多平台</span>;
								} else {
									return <></>;
								}
							} else {
								return (
									<span className="kdzs-select-selection-item" title={ PLAT_MAP[props.value] }>{PLAT_MAP[props.value]}</span>
								);
							}
						} }
						dropdownRender={ menu => (
							<div className={ s['select-dropdown-wrap'] }>
								{
									shopInfo?.platList?.length ? (
										<Row className={ s['custom-select-item'] }>
											<Checkbox
												value="allPlat"
												className="r-w-full"
												onChange={ handleSelectAllPlat }
												checked={ checkAllPlat }
											>所有平台
											</Checkbox>
										</Row>
									) : <CustomizeSmallEmpty />
								}
								<Checkbox.Group className="r-w-full" value={ platValue } onChange={ handleChangePlat }>
									{shopInfo.platList.map(platform => (
										<Row key={ platform } className={ s['custom-select-item'] }>
											<Checkbox
												value={ platform }
												className="r-w-full"
											>  {PLAT_MAP[platform]}
											</Checkbox>
										</Row>
									))}
								</Checkbox.Group>
							</div>
						) }
					/>
				</Form.Item>

				<Form.Item label="店铺">
					<Select
						mode="multiple"
						placeholder="选择店铺"
						className={ cs('r-w-full') }
						optionLabelProp="label"
						value={ shopValue }
						onChange={ handleChangeShop }
						// dropdownClassName="kdzs-select-dropdown"
						tagRender={ (props) => {
							if (checkAllShop) {
								if (shopValue?.[0] && shopValue?.[0] === props.value) {
									return <span className="kdzs-select-selection-item" >所有店铺</span>;
								} else {
									return <></>;
								}
							} else if (shopValue.length > 1) {
								if (shopValue?.[0] && shopValue?.[0] === props.value) {
									return <span className="kdzs-select-selection-item" >多店铺</span>;
								} else {
									return <></>;
								}
							} else {
								let value = null;
								if (showShortNick) {
									value = allShopMapRef?.current && allShopMapRef.current[props.value]?.sellerAbbreviation;
								} else {
									value = allShopMapRef?.current && allShopMapRef.current[props.value]?.sellerNick;
								}
								return (
									<span title={ value } className="kdzs-select-selection-item" > {value}</span>
								);
							}
						} }
						dropdownRender={ menu => (
							<div className={ s['select-dropdown-wrap'] }>
								{shopInfo?.shopList.length ? (
									<Row className={ `${s['custom-select-item']} r-flex r-ai-c r-jc-sb` }>
										<Checkbox
											value="allPlat"
											onChange={ handleSelectAllShop }
											checked={ checkAllShop }
										>所有店铺
										</Checkbox>
										<p className="r-fs-12 r-c-primary" onClick={ showShortNickOnChange }>
											{ showShortNick ? '显示店铺名称' : '显示店铺简称'}
											<Icon className="r-ml-4" size={ 12 } type="qiehuan" />
										</p>
									</Row>
								) : <CustomizeSmallEmpty />}
								<Checkbox.Group className="r-w-full" value={ shopValue } onChange={ handleChangeShop }>
									{shopInfo.shopList.map(shop => (
										<React.Fragment key={ `${shop.platform}_${shop.sellerId}` }>
											<Row className={ s['custom-select-item'] }>
												<Checkbox
													value={ `${shop.platform}_${shop.sellerId}` }
													className="r-w-full"
												> { showShortNick ? shop.sellerAbbreviation : shop.sellerNick}
												</Checkbox>
											</Row>
											{
												getShopStatus(shop).statusText && (
													<div className={ s['error-info'] }>
														<a
															href={ getShopStatus(shop).href }
															target={ getShopStatus(shop).target }
															className={ s[`warning-text-${shop._status}`] }
															style={ { paddingLeft: '35px' } }
															rel="noreferrer"
														>
															{getShopStatus(shop).statusText}
														</a>
													</div>
												)
											}
										</React.Fragment>
									))}
								</Checkbox.Group>
							</div>
						) }
					/>
				</Form.Item>

				<Form.Item label="扫描选项" name="scanNumSetting">
					<Checkbox.Group data-point={ Pointer["扫描打印_单件/多件_点击"] } >
						<Checkbox value={ 1 } >单件</Checkbox>
						<Checkbox value={ 2 } >多件</Checkbox>
					</Checkbox.Group>
				</Form.Item>

				<Form.Item label={ isMergePrint ? "快递模板" : "快递单模板" } name="templateInfo">
					<Select dropdownMatchSelectWidth={ false } onChange={ handleChange }>
						{
							!isMergePrint ? (
								getKddTemListByPlatform?.filter(item => +item.Exid !== -900 && +item.Exid !== -901).map(i => (
									<Option
										title={ renderOptionTitle(i) }
										disabled={ renderOptionDisabeld(i) }
										key={ i.Mode_ListShowId }
										value={ i.Mode_ListShowId }
										className={ cs(s["temp_item"]) }
									>
										<span>{i.ExcodeName}{i.wd?.branchName && `（${i.wd?.quantity}）`}</span>
										{
											(scanPrintSettingSearch?.scanSet?.scanChangeExIdForFirstSend || scanPrintSettingSearch?.scanSet?.scanChangeExIdForGetYd || scanPrintSettingSearch?.scanSet?.scanChangeExIdForNotGetYd)
												? i.ExcodeName == scanPrintSettingSearch?.scanSet?.defaultTemplate?.exName
												&& i.Exid == scanPrintSettingSearch?.scanSet?.defaultTemplate?.exId
													? (<span className={ s["default_text"] }>默认</span>) : (<span className={ s["set_default"] } onClick={ () => { setDefaultExId(i); } }>设为默认</span>)
												: null
										}

									</Option>
								))
							) : (
								kddTempList?.filter(item => item.id != -901 && item.id != -900).map(i => (
									<Option key={ i.id } value={ i.id } className={ cs(s["temp_item"]) }>
										<span>{i.groupName}</span>
										{
											(scanPrintSettingSearch?.scanSet?.scanChangeExIdForFirstSend || scanPrintSettingSearch?.scanSet?.scanChangeExIdForGetYd || scanPrintSettingSearch?.scanSet?.scanChangeExIdForNotGetYd)
												? i.groupName == scanPrintSettingSearch?.scanSet?.defaultTemplate?.exName
												&& i.exCompanyId == scanPrintSettingSearch?.scanSet?.defaultTemplate?.exId
													? (<span className={ s["default_text"] }>默认</span>) : (<span className={ s["set_default"] } onClick={ () => { setDefaultExId(i); } }>设为默认</span>)
												: null
										}
									</Option>
								))
							)
						}
					</Select>
				</Form.Item>
				
				{isShowKddType2Tip && (
					<Form.Item wrapperCol={ { span: 20, offset: 4 } }>
						<div className="r-c-error" style={ { textAlign: 'right' } }>网点面单无法打印平台加密订单</div>
					</Form.Item>
				)}

				<Form.Item label="打印机" name="printer">
					<Select onChange={ handleChange }>
						{printerList.map(printer => (
							<Option key={ printer } value={ printer }>{printer}</Option>
						))}
					</Select>
				</Form.Item>
				<div hidden={ !scanPrintSetting?.scanSet?.isPrintTag }>
					<Form.Item label="吊牌打印机" name="tagPrinter">
						<Select onChange={ handleChange }>
							{printerList.map(printer => (
								<Option key={ printer } value={ printer }>{printer}</Option>
							))}
						</Select>
					</Form.Item>
					<div>
						<Form.Item label="吊牌模板" className="form-item" wrapperCol={ { span: 14 } } >
							<div >
								<span className="r-mr-6">{tagTemplateText}</span>
								<SettingTwoTone onClick={ () => { history.push('/settings/system?introName=printTagSet'); } } />
							</div>
						</Form.Item>
					</div>
					<div className="r-mb-16">
						<div style={ { marginLeft: -13 } }>
							<span>吊牌打印数量<span style={ { position: "relative", top: -1.5, margin: "0 4px 0 2px" } }>:</span> </span>
							<span>{tagPrintNumText}</span>
						</div>
					</div>
				</div>
				<div className={ s["sender-container"] }>
					<Form.Item label="发件人" name="sender" className="form-item" wrapperCol={ { span: 14 } }>
						<Select value={ senderSetting?.list[0]?.id } dropdownMatchSelectWidth={ false } onChange={ handleChange } disabled={ useShopBindSender }>
							{senderSetting?.list?.map(i => (
								<Option key={ i.id } value={ i.id }>{i.senderName}</Option>
							))}
						</Select>
					</Form.Item>
					<div className={ s["sender-setting"] } ref={ popupContainerRef }>
						{senderSettingContainer}
					</div>
				</div>

				<Form.Item label="唯一码" name="id">
					<Input id="labelIdInput" autoFocus placeholder="唯一码" onPressEnter={ handlePressEnter } />
				</Form.Item>

				<Form.Item label=" " colon={ false }>
					<Button data-point={ Pointer["扫描打印_查询_点击"] } type="primary" loading={ loading } onClick={ handlePressEnter }>查询</Button>
					<Button data-point={ Pointer["扫描打印_扫描设置_点击"] } type="link" onClick={ () => { setIsShowScanPrintSettingModal(true); } }>扫描设置</Button>
				</Form.Item>

				<Form.Item label=" " colon={ false } name="autoSend" valuePropName="checked">
					<Checkbox data-point={ Pointer["扫描打印_自动发货_勾选"] }>打印后自动发货</Checkbox>
				</Form.Item>
				{
					ishow ? (
						<Button 
							type="primary" 
							style={ { backgroundColor: "#1890ff", borderColor: "#1890ff", marginLeft: 85 } }
							onClick={ () => {
								setIsShowChooseModel(true);
							} }
						>
							{getSelectedDevicesText()}
						</Button>
					) : null
				}
			</Form>
			
			{/* 选择拍摄设备弹框 */}
			<SelectShootingDeviceModal
				visible={ isShowChooseModel }
				onCancel={ handleSelectShootingDeviceCancel }
				onConfirm={ handleSelectShootingDeviceConfirm }
				defaultSelectedDevices={ selectedShootingDevices }
			/>
			<div className={ cs(s["scan-order-status"]) }>
				{/* <div>
						{getLabelStatusLabel(scanPrintTempInfo.currentScanOrderInfo?.labelStatus)}
					</div> */}
				<div>
					{getTradeStatusLabel(scanPrintTempInfo.currentScanOrderInfo?.orderStatus)}
				</div>
			</div>

			<div className={ cs(s["scan-info"], s[scanPrintTempInfo.scanInfoClassName]) }>
				{scanPrintTempInfo.scanInfoMsg}
			</div>

			{/* 多件订单混扫备齐模式-标签显示 */}
			<div className={ cs(s["mixScanMode"]) }>
				{scanPrintTempInfo.mixScanModeMsg}
			</div>
			
			<Modal
				title={ <div className="r-flex r-ai-c" style={ { fontSize: 20 } }><ExclamationCircleOutlined style={ { color: '#FD8204', fontSize: 30, marginRight: 14 } } />网点电子面单模版</div> }
				closable={ false }
				visible={ isShowKddType2Modal }
				centered
				width={ 490 }
				footer={ (
					<div className="r-flex r-ai-c r-jc-c r-relative">
						<Button 
							type="primary" 
							onClick={ () => { 
								setIsShowKddType2Modal(false); 
							} }
						>
							知道了
						</Button>
						<Checkbox
							className="r-absolute"
							style={ { right: 10, bottom: 0 } }
							onChange={ e => {
								if (e.target.checked) {
									local.set('scanPrint.isNotShowKddType2Modal', true);
								} else {
									local.remove('scanPrint.isNotShowKddType2Modal');
								}
							} }
						>不再提示
						</Checkbox>
					</div>
				) }
			>
				<div className="r-pl-40 r-fs-16">网点电子面单无法打印加密的平台订单，仅支持打印手工订单。</div>
			</Modal>
		</div>
	);
};

export default observer(ScanPrintSearchContainer);
