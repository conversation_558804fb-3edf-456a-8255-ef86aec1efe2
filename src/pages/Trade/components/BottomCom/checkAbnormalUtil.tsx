import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "antd";
import _ from "lodash";
import Decimal from "decimal.js";
import NiceModal, { useModal } from "@ebay/nice-modal-react";
import distributionStore from "@/stores/distribution";
import { TradePrintCheckApi } from "@/apis/trade/search";
import { spArr } from "../../utils";
import { PLAT_PDD, PLAT_TB, PLAT_SPH, PLAT_XHS, PLAT_YZ, PRINT_MAP, PLAT_FXG, PLAT_KS, PLAT_ALI, PLAT_C2M, TEMP_MAP, APPOINTMENT_ARRIVAL_PLAT, PLAT_JD, PLAT_DW, PLAT_KTT } from "@/constants";
import { AbnormalType, SysExchangeTypeEnum } from "./constants";
import userStore from "@/stores/user";
import { PrintTypeEnum, TradeChoiceType, TradeStatus } from "@/utils/enum/trade";
import { ABNORMAL_MODAL_BUTTON_PROPS, ConsolidateTypeEnum, templateType4PlatformTypeEnum } from "../../constants";
import c from './index.module.scss';
import { pageLoading } from "@/components/PageLoading";
import { IPackage, ISubTrade } from "../../interface";
import { tradeStore } from "@/stores";
import { openAbnormalCheck } from "@/pages/Index/Settings/System/constants";
import { dealPlatAsHandPlat, isSourceScm, transferOtherToHand } from "@/components-biz/ShopListSelect/shopListUtils";
import { TradeCheckInfoItem } from "@/types/trade/index";

interface AbnormalOrderProps {
	addGoodsNoSend?: string,
	modifiedOnline?: string,
	address?: string,
	onlineShip?: string,
	afterSale?: string,
	scmOrderNotExist?: string,
	scmRelationAbnormal?: string,
	hasShippied?: string;
	list: Array<string>,
	tradeObjMap: Map<string, IPackage>
}

export const enum AbnormalFromType {
	打印 = 'PRINT',
	发货 = 'SEND',
	预发货 = 'PRESEND',
	波次生成 = 'WAVE',
}

export const enum checkAbnormalType {
	未填写商品识别码 = 'needSerialNumber',
	承诺日达 ='appointmentArrival',
	地址变更 = 'address',
	产生售后 = 'afterSale',
	线上改商品 = 'modifiedOnline',
	有待发货的系统商品 = 'addGoodsNoSend',
	分销订单不存在 = 'scmOrderNotExist',
	分销关系异常 = 'scmRelationAbnormal',
	存在已发货订单商品 = 'hasShipped',
	存在已打印订单商品 = 'hasPrinted',
	线上已发货 = 'onlineShip',
	线上已退款 = 'abnormalOnlineRefunded',
	供分销关系解绑 = 'SCM_RELATION_UNBIND',
	分销店铺解绑 = 'SCM_DISTRIBUTOR_STORE_UNBIND',
	分销订单发货 = 'SCM_TRADE_SHIP',
	分销订单退款 = 'SCM_TRADE_REFUND',
	订单状态异常 = 'orderErrorStatus',
	订单正在执行自动策略 = 'abnormalSysGiftCheck_14',
	列表加载商品数据与数据库商品数据不一致 = 'abnormalSysGiftCheck_15',
	换货手工单对应的售后单异常中断 = 'exchangeRefundAbnormal',
	换货手工单对应的售后单异常 = 'exchangeRefund',
	波次拣货异常 = 'bizWaveAbnormal',
	订单预估利润较低 = 'lowProfit',
}

// 判断是否分销商关系异常
const handleScmRelationAbnormal = async(abnormalOrderObj, selectedPack, tradeObjMap) => {
	// 检查是否有分销订单
	const hasScmOrders = selectedPack.some(packItem => isSourceScm(packItem));

	// 只有当存在分销订单时才执行后续逻辑
	if (hasScmOrders && (userStore.isSupplierAccount || userStore.isFreeSupplierAccount)) {
		let activeDistributorUserId = await distributionStore.getActiveDistributorUserIdList();
		const abnormalTids = [];
		selectedPack.forEach(packItem => {
			if (isSourceScm(packItem) && !activeDistributorUserId?.includes(packItem.distributorUserId)) {
				packItem.trades.forEach(trade => {
					abnormalTids.push(trade.tid);
					tradeObjMap.set(trade.tid, { ...tradeObjMap.get(trade.tid), selected: false });
				});
			}
		});
		const dedupedAbnormalTids = [...new Set(abnormalTids)];
		abnormalOrderObj[checkAbnormalType.分销关系异常] = dedupedAbnormalTids.join(",");
		abnormalOrderObj.list.push(...dedupedAbnormalTids);
	}
};

// 承诺日达
const handleAppointmentArrival = (abnormalOrderObj, selectedPack, tradeObjMap) => {
	let temp = tradeStore.isMergePrint ? tradeStore.selectedTempGroup.exCode : tradeStore.selectedTemp.ExCode;
	if (APPOINTMENT_ARRIVAL_PLAT.includes(temp)) {
		return;
	}
	const abnormalTids = [];
	selectedPack.forEach(pack => {
		pack.trades.forEach(trade => {
			if (trade.appointmentArrival) {
				abnormalTids.push(trade.tid);
				tradeObjMap.set(trade.tid, { ...tradeObjMap.get(trade.tid), selected: false });
			}
		});
	});
	const dedupedAbnormalTids = [...new Set(abnormalTids)];
	abnormalOrderObj["appointmentArrival"] = dedupedAbnormalTids.join(",");
	abnormalOrderObj.list.push(...dedupedAbnormalTids);
};

// 商品识别码
const handleNeedSerialNumber = (abnormalOrderObj, selectedPack, tradeObjMap) => {
	const abnormalTids = [];
	selectedPack.forEach(pack => {
		pack.trades.forEach(trade => {
			trade.orders.forEach(order => {
				if (order.needSerialNumber && !order.productIdCode && order.isChecked) {
					abnormalTids.push(trade.tid);
					tradeObjMap.set(trade.tid, { ...tradeObjMap.get(trade.tid), selected: false });
				}
			});

		});
	});
	const dedupedAbnormalTids = [...new Set(abnormalTids)];
	abnormalOrderObj["needSerialNumber"] = dedupedAbnormalTids.join(",");
	abnormalOrderObj.list.push(...dedupedAbnormalTids);
};
// 线上已发货
const hasOnlineShip = (abnormalOrderObj, selectedPack, tradeObjMap) => {
	const abnormalTids = [];
	console.log('selectedPack', selectedPack);
	selectedPack.forEach(pack => {
		pack.trades.forEach(trade => {
			if (trade.onlineShip) {
				console.log('trade22', trade);
				abnormalTids.push(trade.tid);
				tradeObjMap.set(trade.tid, { ...tradeObjMap.get(trade.tid), selected: false });
			}
		});
	});
	const dedupedAbnormalTids = [...new Set(abnormalTids)];
	abnormalOrderObj["onlineShip"] = dedupedAbnormalTids.join(",");
	abnormalOrderObj.list.push(...dedupedAbnormalTids);
};
// 线上已退款
const hasOnlineRefunded = (abnormalOrderObj, selectedPack, tradeObjMap) => {
	const abnormalTids = [];
	selectedPack.forEach(pack => {
		pack.trades.forEach(trade => {
			trade.orders.forEach(order => {
				if (order.abnormalOnlineRefunded) {
					abnormalTids.push(trade.tid);
					tradeObjMap.set(trade.tid, { ...tradeObjMap.get(trade.tid), selected: false });
				}
			});
		});
	});
	const dedupedAbnormalTids = [...new Set(abnormalTids)];
	abnormalOrderObj["abnormalOnlineRefunded"] = dedupedAbnormalTids.join(",");
	abnormalOrderObj.list.push(...dedupedAbnormalTids);
};
const handleNoSendAddGoods = (abnormalOrderObj, selectedPack, tradeObjMap) => {
	// 未勾选的商品数据中不存在待发货的平台商品但存在待发货的系统商品
	const abnormalTids = [];
	selectedPack.forEach(packItem => {
		packItem.trades.forEach(trade => {
			let hasNoSendAddGoods = false; // 存在未勾选且未发货的系统商品
			let hasNoSendGoods = false; // 存在未勾选且未发货的平台商品
			trade?.orders?.forEach(order => {
				// 判断未勾选的商品数据中不存在待发货的平台商品但存在待发货的系统商品
				if (!order?.isChecked && order.status === "WAIT_SELLER_SEND_GOODS") {
					if ([SysExchangeTypeEnum.新增的商品, SysExchangeTypeEnum.新增的货品].includes(order.sysExchangeType)) {
						hasNoSendAddGoods = true;
					} else {
						hasNoSendGoods = true;
					}
				}
			});

			if (hasNoSendAddGoods && !hasNoSendGoods) {
				abnormalTids.push(trade.tid);
				tradeObjMap.set(trade.tid, { ...tradeObjMap.get(trade.tid), selected: false });
			}
		});
	});
	const dedupedAbnormalTids = [...new Set(abnormalTids)];
	abnormalOrderObj["addGoodsNoSend"] = dedupedAbnormalTids.join(",");
	abnormalOrderObj.list.push(...dedupedAbnormalTids);
};

// 处理波次拣货异常
// const handleBizWaveAbnormal = (abnormalOrderObj, list, tradeObjMap) => {
// 	for (const item of list) {
// 		// 波次拣货异常
// 		if (item?.serviceTagList?.includes('bizWaveAbnormal')) {
// 			bizWaveAbnormal.num += 1;
// 			bizWaveAbnormal.togetherIds.push(item.togetherId);
// 		}
// 	}
// };

// 地址异常比较
const checkAddressDiffer = (newItem:any, oldItem:any) => {
	const { province, city, county, town } = newItem;
	const { receiverState, receiverCity, receiverDistrict, receiverTown } = oldItem;
	// 后端接口数据返回为空时，例如省市区为空则不报错，仅标记：改地址标记
	if (!province || !city || !county) {
		return false;
	}
	if (
		province !== receiverState
		|| city !== receiverCity
		|| county !== receiverDistrict
		|| newItem.idxEncodeReceiverAddress !== oldItem.idxEncodeReceiverAddress
		|| (newItem.idxEncodeReceiverMobile !== oldItem.idxEncodeReceiverMobile && oldItem?.consolidateType != ConsolidateTypeEnum['新疆集运'] && oldItem.platform !== PLAT_PDD)
		|| newItem.idxEncodeReceiverName !== oldItem.idxEncodeReceiverName
	) {
		return true;
	}
	return false;
};

// 将获得的异常订单与现在的列表中的订单进行对比 看是否不同 不同的进行展示
const tradeInfoCheck = (item:any, tradeObjMap:any, abnormalKeys: checkAbnormalType[]) => {
	console.log('tradeObjMap: ', tradeObjMap, tradeObjMap.get(item.tid), abnormalKeys);
	const { extraSetting = {} } = userStore?.userSetting || {};
	const {
		abnormalExchangeAfterSalesTradeCheck
	} = extraSetting;
	let _trade = tradeObjMap.get(item.tid);
	// 只有待发货和部分发货的订单才进行判断
	if (!["WAIT_SELLER_SEND_GOODS", "SELLER_CONSIGNED_PART"].includes(_trade?.status) && !_trade?.firstSend) {
		return false;
	}
	if (abnormalKeys.includes(checkAbnormalType.订单状态异常)) {
		if (item.type == AbnormalType.订单状态异常) {
			for (let orderItem of _trade.orders) {
				// !! 部分平台订单关闭的时候，是不返回子单id的，这里需要做下兼容处理
				if (((item.oid && orderItem.oid === item.oid) || (!item.oid && orderItem.tid === item.tid)) && ['TRADE_FINISHED', 'TRADE_CLOSED'].includes(item.tradeStatus)) {
					return { abnormalStatus: checkAbnormalType.订单状态异常 };
				}
			}
		}
		// if (item.type == AbnormalType.存在已发货订单商品) {
		// 	for (let orderItem of _trade.orders) {
		// 		// !! 部分平台订单已发货的时候，是不返回子单id的，这里需要做下兼容处理，同时部分平台子单可以部分发货
		// 		if (((item.oid && orderItem.oid === item.oid) || (!item.oid && orderItem.tid === item.tid)) && ['WAIT_BUYER_CONFIRM_GOODS', 'SELLER_CONSIGNED_PART'].includes(item.tradeStatus)) {
		// 			return { abnormalStatus: checkAbnormalType.订单状态异常 };
		// 		}
		// 	}
		// }
	}

	if (abnormalKeys.includes(checkAbnormalType.供分销关系解绑)) {
		if (item.type == AbnormalType.供分销关系解绑) {
			return { abnormalStatus: checkAbnormalType.供分销关系解绑 };
		}
	}

	if (abnormalKeys.includes(checkAbnormalType.分销店铺解绑)) {
		if (item.type == AbnormalType.分销店铺解绑) {
			return { abnormalStatus: checkAbnormalType.分销店铺解绑 };
		}
	}

	if (abnormalKeys.includes(checkAbnormalType.分销订单发货)) {
		// 分销先发货订单未放行
		if (item.type == AbnormalType.分销订单发货 && !_trade.firstSend) {
			return { abnormalStatus: checkAbnormalType.分销订单发货 };
		}
	}

	if (abnormalKeys.includes(checkAbnormalType.分销订单退款)) {
		if (item.type == AbnormalType.分销订单退款) {
			return { abnormalStatus: checkAbnormalType.分销订单退款 };
		}
	}

	if (abnormalKeys.includes(checkAbnormalType.地址变更)) {
		// 目前淘宝和拼多多只有地址异常 抖音有售后和地址异常
		// abnormalStatus address afterSale 地址变更
		// 淘宝、视频号 type ==1 校验oaid
		if ([PLAT_TB, PLAT_SPH].includes(item.platform) && (item.type === 1 || item.type === 4)) {
			if (_trade.caid !== item.oaid) return { abnormalStatus: checkAbnormalType.地址变更 };
		}
		// 1688
		if (item.platform === PLAT_ALI && (item.type === 1 || item.type === 4)) {
			if (_trade.caid?.split('-')?.[0] !== item.oaid?.split('-')?.[0]) return { abnormalStatus: checkAbnormalType.地址变更 };
		}
		// 抖店 type==1 校验 省市区 详细地址 收件人昵称 收件人手机号 type 4 商家后台改地址
		if (item.platform === PLAT_FXG && (item.type === 1 || item.type === 4)) {
			if (
				(item.oaid && _trade.caid !== item.oaid)
				|| (!item.oaid && checkAddressDiffer(item, _trade))
			) {
				return { abnormalStatus: checkAbnormalType.地址变更 };
			}
		}
		// 拼多多、快手小店、小红书、有赞、京东 校验 省市区 详细地址 收件人昵称 收件人手机号
		if ([PLAT_PDD, PLAT_KS, PLAT_XHS, PLAT_YZ, PLAT_JD].includes(item.platform) && (item.type === 1 || item.type === 4)) {
			if (checkAddressDiffer(item, _trade)) { return { abnormalStatus: checkAbnormalType.地址变更 }; }
		}
	}

	if (abnormalKeys.includes(checkAbnormalType.产生售后)) {
		// fxg type ==3 校验 退款状态 子订单纬度 抖音，淘宝，拼多多，阿里，快手、小红书,sph，快团团先不加
		if (([PLAT_TB, PLAT_FXG, PLAT_PDD, PLAT_KS, PLAT_ALI, PLAT_XHS, PLAT_SPH].includes(item.platform)) && item.type == AbnormalType.退款处理) {
			for (let orderItem of _trade.orders) {
				// !! 部分平台订单有退款的时候，是不返回子单id的，这里需要做下兼容处理
				if ((orderItem.status === "WAIT_SELLER_SEND_GOODS" || orderItem.firstSend)// 检查订单商品的状态是"等待卖家发货"或者是首次发货的订单
					&& ((item.oid && orderItem.oid === item.oid) || (!item.oid && orderItem.tid === item.tid)) // 匹配当前订单商品和异常信息中的订单商品是否是同一个
					&& (orderItem.refundStatus !== item.refundStatus)// 检查当前订单商品的退款状态与从接口获取的最新退款状态是否不一致
				) {
					// 如果获取到异常订单的退款状态与目前退款状态不一致 就是异常订单 订单中如果是已发货的订单 将不进行校验
					return { abnormalStatus: checkAbnormalType.产生售后 };
				}
			}
		}
	}

	if (abnormalKeys.includes(checkAbnormalType.线上改商品)) {
		// 抖店线上改商品如果存在异常需要拦截
		if ([PLAT_FXG, PLAT_TB].includes(item.platform) && item.type == AbnormalType.线上改商品) {
			// 如果线上已经改了sku了，但是系统中的sku没有更新需要拦截
			for (let orderItem of _trade.orders) {
				// 只需要判断当前商品是线上改商品的就行，如果是系统改了商品，就算线上也改了，也不做拦截
				const needValid = orderItem.sysExchangeType == SysExchangeTypeEnum.线上改商品 || orderItem.sysExchangeType == SysExchangeTypeEnum.默认;
				// * 后端返回无法和订单查询的子订单顺序一致 需先判断 oid 一致，在判断 skuId 是否一致
				if (needValid && orderItem.oid == item.oid && orderItem.skuId !== item.modifyInfo) {
					return { abnormalStatus: checkAbnormalType.线上改商品 };
				}
			}
		}
	}

	if (abnormalKeys.includes(checkAbnormalType.分销订单不存在)) {
		// 分销订单不存在 拦截
		if (item.type == AbnormalType.分销订单不存在) {
			return { abnormalStatus: checkAbnormalType.分销订单不存在 };
		}
	}

	if (abnormalKeys.includes(checkAbnormalType.订单正在执行自动策略)) {
		// 订单正在执行自动策略 拦截
		if (item.type == AbnormalType.订单正在执行自动策略) {
			return { abnormalStatus: checkAbnormalType.订单正在执行自动策略 };
		}
	}

	if (abnormalKeys.includes(checkAbnormalType.列表加载商品数据与数据库商品数据不一致)) {
		// 列表加载商品数据与数据库商品数据不一致 拦截
		if (item.type == AbnormalType.列表加载商品数据与数据库商品数据不一致) {
			return { abnormalStatus: checkAbnormalType.列表加载商品数据与数据库商品数据不一致 };
		}
	}
	if (abnormalKeys.includes(checkAbnormalType.换货手工单对应的售后单异常)) {
		// 换货手工单对应的售后单异常 拦截
		if (item.type == AbnormalType.换货手工单对应的售后单异常) {
			if (abnormalExchangeAfterSalesTradeCheck?.supportForceAction) {
				return { abnormalStatus: checkAbnormalType.换货手工单对应的售后单异常 };
			}
			return { abnormalStatus: checkAbnormalType.换货手工单对应的售后单异常中断 };
		}
	}
	return false;
};


const isLowProfit = (order: IPackage, item: IPackage) => {
	// 新增的商品和货品以及赠品不进行毛利润检测，
	if ([SysExchangeTypeEnum.新增的商品, SysExchangeTypeEnum.新增的货品].includes(order.sysExchangeType) || order.isGift) {
		return false;
	}
	let isLowProfit = false;
	const { paidAmountConfig, estimatedGrossProfitConfig } = userStore.userSetting.lowProfitTradeConfig || {};
	// 使用 Decimal.js 进行精确计算：实付金额 = 总金额 / 数量
	const payment = new Decimal(order.payment || 0).dividedBy(new Decimal(order.num || 1));
	console.log('毛利润检测', JSON.parse(JSON.stringify(userStore.userSetting.lowProfitTradeConfig)), order, item);
	const PRICE_MAP = { COST_PRICE: 'costPrice', 'TAG_PRICE': 'tagPrice', 'SELLING_PRICE': 'price' };
	// 商品实付低于xx 价格
	if (paidAmountConfig?.enabled) {
		const price = PRICE_MAP[paidAmountConfig.basePriceType] || 0;
		const itemPrice = new Decimal(item[price] || 0);
		isLowProfit = payment.lessThan(itemPrice);
		console.log('毛利润检测_商品实付', payment.toNumber(), itemPrice.toNumber(), isLowProfit);
		if (isLowProfit) {
			return true;
		}
	}
	// 订单预估毛利低于xx元
	if (estimatedGrossProfitConfig?.enabled) {
		const { enableSubtractCustomAmount, customAmount, enableBasePrice, basePriceType } = estimatedGrossProfitConfig?.calculationParams;
		const price = enableBasePrice ? (item[PRICE_MAP[basePriceType]] || 0) : 0;
		const customPrice = enableSubtractCustomAmount ? (customAmount || 0) : 0;
		// 使用 Decimal.js 进行精确计算：毛利润 = 实付金额 - 基础价格 - 自定义费用
		const basePrice = new Decimal(price);
		const customCost = new Decimal(customPrice);
		const grossProfit = payment.minus(basePrice).minus(customCost);
		const threshold = new Decimal(estimatedGrossProfitConfig.threshold || 0);
		isLowProfit = grossProfit.lessThan(threshold);
		console.log('毛利润检测_预估毛利', payment.toNumber(), basePrice.toNumber(), customCost.toNumber(), grossProfit.toNumber(), threshold.toNumber(), isLowProfit);
		if (isLowProfit) {
			return true;
		}
	}
	return isLowProfit;
};
const customLogPost = (dataType: string, data:any = {}) => {
	window.errorCollection?.customMessageUpload({
		type: `打印、发货、波次生成前校验,【 ${dataType} 】`,
		data
	});
};
//  校验看是否有异常订单 （从接口获取异常）
export const checkAbnormalOrders = async(list: IPackage[], type = AbnormalFromType.打印, printType?: PrintTypeEnum) => {
	// console.log(type);
	const isPrint = type == AbnormalFromType.打印;
	console.log('校验看是否有异常订单', list, type, printType);
	// const { extraSetting = {} } = await userStore.getUserSetting();
	// const {
	// 	abnormalExchangeAfterSalesTradeCheck
	// } = extraSetting;
	// console.log('userSetting222', JSON.parse(JSON.stringify(extraSetting)));

	// eslint-disable-next-line no-async-promise-executor
	return new Promise(async(resolve, reject) => {
		let arr:Array<TradeCheckInfoItem> = [];
		let tradeObjMap = new Map<string, ISubTrade>();
		// 循环获得所有要打印的订单 并将要打印的订单放到一个对象中 方便后续获得异常订单直接用来对比
		for (let item of list) {
			for (let tradeItem of item.trades) {
				// 只有待发货才进入
				if ([TradeStatus.等待卖家发货, TradeStatus.卖家部分发货].includes(tradeItem.status as TradeStatus) || tradeItem.firstSend) {
					const tradeCheckInfo: TradeCheckInfoItem = {
						sellerId: tradeItem.sellerId,
						platform: tradeItem.platform,
						source: tradeItem.source,
						distributorUserId: item.distributorUserId,
						tid: tradeItem.tid,
						storageTime: tradeItem.storageTime,
						ptTid: tradeItem.ptTid,
						modified: tradeItem.modified,
						refundId: tradeItem?.orders?.map(item => item.refundId).filter(Boolean)?.[0],
						oidList: tradeItem?.orders?.map(o => o.oid) || [], // 系统赠品判断
					};
					arr.push(tradeCheckInfo);
					tradeObjMap.set(tradeItem.tid, { ...tradeItem, togetherId: item.togetherId, selected: true });
				}
			}
		}

		let splitArr: Array<any> = spArr(arr, 200);
		const userSetting = await userStore.getUserSetting();
		const { printBeforeCheck, sendBeforeCheck, waveBeforeCheck } = userSetting?.extraSetting?.abnormalLowProfitTradeConfigCheck || {};
		// 每次请求 最多能查200个tid
		const isNeedCheckLowProfit = (printType != PrintTypeEnum.发货单)
			&& (((type == AbnormalFromType.打印 || type == AbnormalFromType.预发货) && printBeforeCheck) || ((type == AbnormalFromType.发货) && sendBeforeCheck) || ((type == AbnormalFromType.波次生成) && waveBeforeCheck));
		const promises = splitArr.map(function(item) {
			let params: any = {
				tidList: item.map((i: TradeCheckInfoItem) => i.tid),
				tradeCheckInfos: item,
			};
			if (isNeedCheckLowProfit) {
				params.abnormalCheckTypeList = ['LOW_PROFIT_TRADE_CHECK'];
			}
			return TradePrintCheckApi(params);
		});
		let abnormalOrderObj: AbnormalOrderProps = {
			list: [],
			tradeObjMap
		};
		// 异常类型
		let abnormalKeys = [];

		if (isPrint) {
			// 打印
			abnormalKeys = [
				checkAbnormalType.供分销关系解绑,
				checkAbnormalType.分销店铺解绑,
				checkAbnormalType.分销订单发货,
				checkAbnormalType.分销订单退款,
				// checkAbnormalType.未填写商品识别码,
				checkAbnormalType.承诺日达,
				checkAbnormalType.地址变更,
				checkAbnormalType.产生售后,
				checkAbnormalType.线上改商品,
				checkAbnormalType.分销订单不存在,
				checkAbnormalType.分销关系异常,
				checkAbnormalType.存在已发货订单商品,
				checkAbnormalType.存在已打印订单商品,
				checkAbnormalType.线上已发货,
				checkAbnormalType.线上已退款,
				checkAbnormalType.订单状态异常,
				checkAbnormalType.订单正在执行自动策略,
				checkAbnormalType.列表加载商品数据与数据库商品数据不一致,
				checkAbnormalType.有待发货的系统商品, // 默认就是开启，所以无需判断开关
				checkAbnormalType.换货手工单对应的售后单异常,
				checkAbnormalType.换货手工单对应的售后单异常中断,
				checkAbnormalType.订单预估利润较低
			];
		} else {
			abnormalKeys = [
				checkAbnormalType.供分销关系解绑,
				checkAbnormalType.分销店铺解绑,
				checkAbnormalType.分销订单发货,
				checkAbnormalType.分销订单退款,
				checkAbnormalType.未填写商品识别码,
				checkAbnormalType.承诺日达,
				checkAbnormalType.分销订单不存在,
				checkAbnormalType.分销关系异常,
				checkAbnormalType.线上已发货,
				checkAbnormalType.线上已退款,
				checkAbnormalType.订单状态异常,
				checkAbnormalType.产生售后,
				checkAbnormalType.订单正在执行自动策略,
				checkAbnormalType.列表加载商品数据与数据库商品数据不一致,
				checkAbnormalType.换货手工单对应的售后单异常,
				checkAbnormalType.换货手工单对应的售后单异常中断,
				checkAbnormalType.订单预估利润较低
			];

			if (type !== AbnormalFromType.预发货) {
				abnormalKeys.push(checkAbnormalType.存在已发货订单商品);
			}
			// 发货，如果开启异常检测的情况，检测 有待发货的系统商品
			if (userStore?.userSetting?.openAbnormalCheck === openAbnormalCheck.开启) {
				abnormalKeys.push(checkAbnormalType.有待发货的系统商品);
			}
		}

		/**
			 * 新订单校验逻辑
			 * 以下几个校验项均走配置
			 * printBeforeCheck
			 * sendBeforeCheck
			 * supportForceAction
			 */
		// const { abnormalItemUnbindCheck, ...rest } = extraSetting || {};
		// Object.keys(rest).forEach(key => {
		// const value = rest[key];
		// 线上已发货
		// if (key === 'abnormalOutOfSystemShipCheckNew') {
		// 	const isCheck = isPrint ? value.printBeforeCheck : value.sendBeforeCheck;
		// 	isCheck
		// 		? abnormalKeys.push(checkAbnormalType.线上已发货)
		// 		: abnormalKeys = abnormalKeys.filter(s => s != checkAbnormalType.线上已发货);
		// }
		// });
		console.log('restrest222', abnormalKeys);

		// { title: '线上已发货', value: 'abnormalOutOfSystemShipCheckNew', ...extraSetting['abnormalOutOfSystemShipCheckNew'] || {} },
		// { title: '商品未绑定本地货品', value: 'abnormalItemUnbindCheckNew', ...extraSetting['abnormalItemUnbindCheckNew'] || {} },
		// { title: '含退款商品的订单', value: 'abnormalRefundCheckNew', ...extraSetting['abnormalRefundCheckNew'] || {} },
		// { title: '异常地址(双地址)', value: 'abnormalAddressCheckNew', ...extraSetting['abnormalAddressCheckNew'] || {} },
		// { title: '快递不可达订单检测', value: 'abnormalExpressArriveCheckNew', ...extraSetting['abnormalExpressArriveCheckNew'] || {} },
		// { title: '库存不足订单(缺货)', value: 'abnormalStockWarnCheckNew', ...extraSetting['abnormalStockWarnCheckNew'] || {} }

		const ignoreKeyList = [checkAbnormalType.地址变更];
		abnormalKeys.forEach(key => abnormalOrderObj[key] = '');

		// 判断是否存在线上已发货订单
		if (abnormalKeys.includes(checkAbnormalType.线上已发货)) {
			hasOnlineShip(abnormalOrderObj, list, tradeObjMap);
		}
		// 判断是否存在线上已退款订单
		if (abnormalKeys.includes(checkAbnormalType.线上已退款)) {
			hasOnlineRefunded(abnormalOrderObj, list, tradeObjMap);
		}
		// 判断是否存在未填写商品识别码订单
		if (abnormalKeys.includes(checkAbnormalType.未填写商品识别码)) {
			handleNeedSerialNumber(abnormalOrderObj, list, tradeObjMap);
		}

		// 判断是否存在承诺日达订单
		if (abnormalKeys.includes(checkAbnormalType.承诺日达)) {
			handleAppointmentArrival(abnormalOrderObj, list, tradeObjMap);
		}

		// 判断是否存在未勾选的商品数据中不存在待发货的平台商品但存在待发货的系统商品
		if (abnormalKeys.includes(checkAbnormalType.有待发货的系统商品)) {
			handleNoSendAddGoods(abnormalOrderObj, list, tradeObjMap);
		}
		// 判断是否分销商关系异常
		if (abnormalKeys.includes(checkAbnormalType.分销关系异常)) {
			await handleScmRelationAbnormal(abnormalOrderObj, list, tradeObjMap);
		}

		// 判断是否存在波次拣货异常
		// if (abnormalKeys.includes(checkAbnormalType.波次拣货异常)) {
		// 	handleBizWaveAbnormal(abnormalOrderObj, list, tradeObjMap);
		// }
		console.log('userSetting', userSetting);
		customLogPost(type, {
			isNeedCheckLowProfit,
			abnormalLowProfitTradeConfigCheck: userSetting?.extraSetting?.abnormalLowProfitTradeConfigCheck,
			lowProfitTradeConfig: userSetting?.lowProfitTradeConfig
		});
		Promise.all(promises).then(function(resArr) {
			console.log('%c [ resArr ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', resArr);
			resArr.forEach((res) => {
				// res.data.abnormalTradeInfoList.push({ "platform": "fxg", "tid": "6922576230350984957A", "type": 4, oaid: '222' });
				// if (res && res.data && res.data.abnormalTradeInfoList && res.data.abnormalTradeInfoList.length > 0) {
				window.errorCollection?.customMessageUpload({ type: '打印发货接口检测', data: res.data });
				res.data.abnormalTradeInfoList.forEach((item: any) => {
					if (item && item !== 'null') {
						let isAbnormal = tradeInfoCheck(item, tradeObjMap, abnormalKeys);
						console.log('isAbnormal', isAbnormal);
						// abnormalTradeInfoList可能会返回同一个订单号的多条改地址数据
						if (isAbnormal && isAbnormal.abnormalStatus && !abnormalOrderObj[isAbnormal.abnormalStatus].includes(item.tid)) {
							abnormalOrderObj[isAbnormal.abnormalStatus] += ((abnormalOrderObj[isAbnormal.abnormalStatus] ? "," : "") + item.tid);
							abnormalOrderObj.list.push(item.tid);
							// 将异常的订单的select置为false
							// 在忽略列表中，select 还是true
							const nextSelected = ignoreKeyList.includes(isAbnormal.abnormalStatus);
							tradeObjMap.set(item.tid, { ...tradeObjMap.get(item.tid), selected: nextSelected });
						}
					}
				});
				// }
				if (abnormalKeys.includes(checkAbnormalType.存在已发货订单商品) || abnormalKeys.includes(checkAbnormalType.存在已打印订单商品) || (isNeedCheckLowProfit && abnormalKeys.includes(checkAbnormalType.订单预估利润较低))) {
					const { supportForceAction } = userStore.userSetting?.extraSetting?.abnormalLowProfitTradeConfigCheck || {};

					res?.data?.tradeInfoStatusList?.forEach(item => {
						if (item && item !== 'null') {
							let _trade = tradeObjMap.get(item.tid);
							let _order = _trade.orders.find(o => o.oid === item.oid && o.isChecked) || {};
							/** 订单低毛利润判断 */
							if (isNeedCheckLowProfit && isLowProfit(_order, item)) {
								abnormalOrderObj[checkAbnormalType.订单预估利润较低] += ((abnormalOrderObj[checkAbnormalType.订单预估利润较低] ? "," : "") + item.tid);
								abnormalOrderObj.list.push(item.tid);
								tradeObjMap.set(item.tid, {
									..._trade,
									selected: false,
								});
							}
							console.log('_order_order', _order, abnormalOrderObj, tradeObjMap, !supportForceAction);
							if (_order.status === TradeStatus.等待卖家发货 && item.status === TradeStatus.等待买家确认收货) {
							// if ((_order.status === TradeStatus.等待卖家发货) && ((_order.platform == 'tb' && [TradeStatus.等待买家确认收货, TradeStatus.卖家部分发货].includes(item.status)) || (_order.platform !== 'tb' && item.status === TradeStatus.等待买家确认收货))) {
								if (abnormalOrderObj[checkAbnormalType.存在已发货订单商品]?.indexOf(item.tid) === -1) {
									abnormalOrderObj[checkAbnormalType.存在已发货订单商品] += ((abnormalOrderObj[checkAbnormalType.存在已发货订单商品] ? "," : "") + item.tid);
								}
								abnormalOrderObj.list.push(item.tid);
								tradeObjMap.set(item.tid, {
									..._trade,
									selected: false,
								});
								// * 暂时不需要 后续可能会补上
								// // * 未开启异常检测时把传入的list的order 打印和发货状态更新
								// let allListOfOrder = list.find(i => i.togetherId == _trade.togetherId)?.trades?.find(trade => trade.tid == item.tid)?.orders?.find(order => order.oid == item.oid);
								// allListOfOrder.latestOrderPrintStatus = item.orderPrintStatus;
								// allListOfOrder.latestStatus = item.status;

								// // * 开启异常检测时把store中的list的order 打印和发货状态更新
								// allListOfOrder = allList.find(i => i.togetherId == _trade.togetherId)?.trades?.find(trade => trade.tid == item.tid)?.orders?.find(order => order.oid == item.oid);
								// allListOfOrder.latestOrderPrintStatus = item.orderPrintStatus;
								// allListOfOrder.latestStatus = item.status;
							} else if (+_trade.isPrintKdd == 0 && +item.orderPrintStatus == 1 && _order.oid) {
								// !! 添加一层判断，只有abnormalKeys包含『存在已打印订单商品』时候才执行
								console.log('只有abnormalKeys包含『存在已打印订单商品』时候才执行', _trade, item, _order);
								if (abnormalKeys.includes(checkAbnormalType.存在已打印订单商品) && abnormalOrderObj[checkAbnormalType.存在已打印订单商品]?.indexOf(item.tid) === -1) {
									abnormalOrderObj[checkAbnormalType.存在已打印订单商品] += ((abnormalOrderObj[checkAbnormalType.存在已打印订单商品] ? "," : "") + item.tid);
								}
								abnormalOrderObj.list.push(item.tid);
								tradeObjMap.set(item.tid, {
									..._trade,
									selected: false,
								});
							}
						}
					});
				}
			});
			if (abnormalKeys.some(key => abnormalOrderObj[key])) {
				abnormalOrderObj.list = _.uniq(abnormalOrderObj.list);
				resolve(abnormalOrderObj);
			} else {
				resolve(null);
			}
		}).catch((reason) => {
			reject(reason);
			window.errorCollection?.customMessageUpload({
				type: `校验看是否有异常订单: checkAbnormalOrders TradePrintCheckApi error`,
				data: {
					reason,
					abnormalOrderObj,
					isPrint
				}
			});
		});
	});
};

// 强制， 地址变更
const nextIgnoreModal = (abnormalTradeInfoObj: AbnormalOrderProps, fromType = AbnormalFromType.打印) => {
	const isPrint = fromType == AbnormalFromType.打印;
	return new Promise((resolve, reject) => {
		if (!abnormalTradeInfoObj.address) {
			resolve(true);
			return;
		}
		Modal.confirm({
			centered: true,
			title: '异常提醒',
			content: (
				<>
					<div>
						<p>以下订单信息发生变更，建议重新查询后{isPrint ? '打印' : '发货'}</p>
					</div>
					<div className={ c["change-address-container"] }>
						{
							abnormalTradeInfoObj.address && (
								<div>
									<p>地址变更：</p>
									<p>{abnormalTradeInfoObj.address}</p>
								</div>
							)
						}
					</div>
				</>
			),
			okText: `强制${isPrint ? '打印' : '发货'}`,
			cancelText: '取消',
			onOk: () => {
				resolve(true);
				window.errorCollection?.customMessageUpload({
					type: `地址变更，强制${isPrint ? '打印' : '发货'}`,
					data: {
						address: abnormalTradeInfoObj.address,
						isPrint,
						fromType
					}
				});
			},
			onCancel: () => {
				resolve(false);
				window.errorCollection?.customMessageUpload({
					type: `地址变更，取消${isPrint ? '打印' : '发货'}`,
					data: {
						address: abnormalTradeInfoObj.address,
						isPrint,
						fromType
					}
				});
			}
		});
		window.errorCollection?.customMessageUpload({
			type: `地址变更，异常提醒: nextIgnoreModal`,
			data: {
				address: abnormalTradeInfoObj.address,
				isPrint,
				fromType
			}
		});
	});
};

// 跳过，分销订单不存在，分销关系异常，
const nextBreakModal = (abnormalTradeInfoObj: AbnormalOrderProps, fromType = AbnormalFromType.打印) => {
	const isPrint = fromType == AbnormalFromType.打印;
	return new Promise((resolve, reject) => {
		if (!abnormalTradeInfoObj.scmOrderNotExist && !abnormalTradeInfoObj.scmRelationAbnormal) {
			resolve(true);
			return;
		}
		Modal.confirm({
			centered: true,
			title: '异常提醒',
			content: (
				<>
					<div>
						<p>以下订单信息发生变更，建议重新查询后{isPrint ? '打印' : '发货'}</p>
					</div>
					<div className={ c["change-address-container"] }>
						{
							abnormalTradeInfoObj.scmOrderNotExist && (
								<div>
									<p>分销订单不存在：</p>
									<p>{abnormalTradeInfoObj.scmOrderNotExist}</p>
								</div>
							)
						}
						{
							abnormalTradeInfoObj.scmRelationAbnormal && (
								<div>
									<p>分销关系异常：</p>
									<p>{abnormalTradeInfoObj.scmRelationAbnormal}</p>
								</div>
							)
						}
					</div>
				</>
			),
			okText: `跳过异常继续${isPrint ? '打印' : '发货'}`,
			cancelText: '取消',
			onOk: async() => {
				resolve(true);
				window.errorCollection?.customMessageUpload({
					type: `分销订单不存在，分销关系异常: 跳过异常继续`,
					data: {
						scmOrderNotExist: abnormalTradeInfoObj?.scmOrderNotExist,
						scmRelationAbnormal: abnormalTradeInfoObj?.scmRelationAbnormal,
						fromType,
						isPrint
					}
				});
			},
			onCancel: () => {
				resolve(false);
				window.errorCollection?.customMessageUpload({
					type: `分销订单不存在，分销关系异常: 取消`,
					data: {
						scmOrderNotExist: abnormalTradeInfoObj?.scmOrderNotExist,
						scmRelationAbnormal: abnormalTradeInfoObj?.scmRelationAbnormal,
						fromType,
						isPrint
					}
				});
			}
		});
		window.errorCollection?.customMessageUpload({
			type: `分销订单不存在，分销关系异常: nextBreakModal`,
			data: {
				scmOrderNotExist: abnormalTradeInfoObj?.scmOrderNotExist,
				scmRelationAbnormal: abnormalTradeInfoObj?.scmRelationAbnormal,
				fromType,
				isPrint
			}
		});

	});
};


// 未开启异常检测校验 （感觉无用）
export const abnormalModalShow = (abnormalTradeInfoObj: AbnormalOrderProps, fromType = AbnormalFromType.打印) => {
	const isPrint = fromType == AbnormalFromType.打印;
	console.log('abnormalTradeInfoObj', abnormalTradeInfoObj);

	const customLogPost = (dataType: string, data:any = {}) => {
		window.errorCollection?.customMessageUpload({
			type: `打印前校验,abnormalModalShow: 【 ${dataType} 】`,
			data: {
				isPrint,
				abnormalTradeInfoObj,
				fromType,
				...data
			}
		});
	};
	// eslint-disable-next-line react-hooks/rules-of-hooks
	// eslint-disable-next-line no-async-promise-executor
	return new Promise(async(resolve, reject) => {
		// 判断是否存在产生售后的订单商品
		if (abnormalTradeInfoObj[checkAbnormalType.产生售后]) {
			console.log('xxxxx');
			NiceModal.show(NiceModal.create(() => {
				const modal = useModal();
				return (
					<Modal
						title="系统提醒"
						visible={ modal.visible }
						onCancel={ modal.hide }
						centered
						afterClose={ modal.remove }
						bodyStyle={ { padding: "20px 24px" } }
						footer={ (
							<div>
								<Button onClick={ () => {
									modal.hide();
									customLogPost('取消', {
										type: checkAbnormalType.产生售后
									});
								} }
								>取消
								</Button>
								<Button
									onClick={ () => {
										modal.hide();
										resolve({ allowCancelTrade: false });
										customLogPost(`强制${isPrint ? '打印' : '发货'}`, {
											type: checkAbnormalType.产生售后
										});
									} }
									type="primary"
									className="r-ml-8"
								>强制{isPrint ? '打印' : '发货'}
								</Button>
								<Button
									onClick={ () => {
										modal.hide();
										resolve(true);
										customLogPost(`跳过异常,继续${isPrint ? '打印' : '发货'}`, {
											type: checkAbnormalType.产生售后
										});
									} }
									type="primary"
								>跳过异常,继续{isPrint ? '打印' : '发货'}
								</Button>
							</div>
						) }
					>
						<div style={ { maxHeight: 600, overflowY: 'auto' } }>
							<p>
								以下订单含<span className="r-c-warning" >【产生售后】</span>商品不可{isPrint ? '打印' : '发货'}，请取消该订单后继续{isPrint ? '打印' : '发货'}
							</p>
							<p>订单号：{abnormalTradeInfoObj[checkAbnormalType.产生售后]}</p>
						</div>
					</Modal>
				);
			}));
			customLogPost(`产生售后,弹出异常弹框`, {
				type: checkAbnormalType.产生售后
			});
			return;
		}
		// 判断是否存在线上已发货的订单商品
		if (abnormalTradeInfoObj[checkAbnormalType.线上已发货]) {
			console.log('xxxxx');
			NiceModal.show(NiceModal.create(() => {
				const modal = useModal();
				return (
					<Modal
						title="系统提醒"
						visible={ modal.visible }
						onCancel={ modal.hide }
						centered
						afterClose={ modal.remove }
						bodyStyle={ { padding: "20px 24px" } }
						footer={ (
							<div>
								<Button onClick={ () => {
									modal.hide();
									customLogPost('取消', {
										type: checkAbnormalType.线上已发货
									});
								} }
								>取消
								</Button>
								<Button
									onClick={ () => {
										modal.hide();
										resolve({ allowCancelTrade: false });
										customLogPost(`强制${isPrint ? '打印' : '发货'}`, {
											type: checkAbnormalType.线上已发货
										});
									} }
									type="primary"
									className="r-ml-8"
								>强制{isPrint ? '打印' : '发货'}
								</Button>
								<Button
									onClick={ () => {
										modal.hide();
										resolve(true);
										customLogPost(`跳过异常,继续${isPrint ? '打印' : '发货'}`, {
											type: checkAbnormalType.线上已发货
										});
									} }
									type="primary"
								>跳过异常,继续{isPrint ? '打印' : '发货'}
								</Button>
							</div>
						) }
					>
						<div style={ { maxHeight: 600, overflowY: 'auto' } }>
							<p>
								以下订单含<span className="r-c-warning" >【线上已发货】</span>商品不可{isPrint ? '打印' : '发货'}，请取消该订单后继续{isPrint ? '打印' : '发货'}
							</p>
							<p>订单号：{abnormalTradeInfoObj[checkAbnormalType.线上已发货]}</p>
						</div>
					</Modal>
				);
			}));
			customLogPost(`线上已发货,弹出异常弹框`, {
				type: checkAbnormalType.线上已发货
			});
			return;
		}
		// 判断是否存在已发货的订单商品
		if (abnormalTradeInfoObj[checkAbnormalType.存在已发货订单商品]) {
			const content = abnormalTradeInfoObj?.list?.map(tid => <p>{tid}</p>);
			Modal.confirm({
				centered: true,
				title: '系统提醒',
				bodyStyle: { padding: "20px 24px" },
				content: (
					<div style={ { maxHeight: 600, overflowY: 'auto' } }>
						<p style={ { color: "#777" } }>您选择的订单含有
							<span style={ { color: "#f00" } }>已发货</span>
							的订单商品，请您仔细核对是否继续{isPrint ? '打印' : '发货'}？
						</p>
						<div className="r-mt-10">{content}</div>
					</div>
				),
				okText: `继续${isPrint ? '打印' : '发货'}`,
				cancelText: '取消',
				onOk: () => {
					resolve({
						allowCancelTrade: false
					});
					customLogPost(`继续${isPrint ? '打印' : '发货'}`, {
						type: checkAbnormalType.存在已发货订单商品
					});
				},
				onCancel: () => {
					resolve(true);
					customLogPost(`取消${isPrint ? '打印' : '发货'}`, {
						type: checkAbnormalType.存在已发货订单商品
					});
				}
			});
			customLogPost(`存在已发货订单商品,弹出异常弹框`, {
				type: checkAbnormalType.存在已发货订单商品
			});
			return;
		}
		if (abnormalTradeInfoObj[checkAbnormalType.存在已打印订单商品]) {
			const content = abnormalTradeInfoObj?.list?.map(tid => <p>{tid}</p>);
			Modal.confirm({
				centered: true,
				title: '系统提醒',
				bodyStyle: { padding: "20px 24px" },
				content: (
					<div style={ { maxHeight: 600, overflowY: 'auto' } }>
						<p style={ { color: "#777" } }>您选择的订单含有
							<span style={ { color: "#f00" } }>已打印</span>
							的订单商品，请您仔细核对是否继续{isPrint ? '打印' : '发货'}？
						</p>
						<div className="r-mt-10">{content}</div>
					</div>
				),
				okText: `继续${isPrint ? '打印' : '发货'}`,
				cancelText: '取消',
				onOk: () => {
					resolve({
						allowCancelTrade: false
					});
					customLogPost(`继续${isPrint ? '打印' : '发货'}`, {
						type: checkAbnormalType.存在已打印订单商品
					});
				},
				onCancel: () => {
					resolve(true);
					customLogPost(`取消${isPrint ? '打印' : '发货'}`, {
						type: checkAbnormalType.存在已打印订单商品
					});
				}
			});
			customLogPost(`存在已打印订单商品,弹出异常弹框`, {
				type: checkAbnormalType.存在已打印订单商品
			});
			return;
		}
		// 判断是否存在待发货的新增商品
		if (abnormalTradeInfoObj.addGoodsNoSend) {
			const content = abnormalTradeInfoObj?.list?.map(tid => <p>{tid}</p>);
			Modal.confirm({
				centered: true,
				title: '系统提醒',
				bodyStyle: { padding: "20px 24px" },
				content: (
					<div style={ { maxHeight: 600, overflowY: 'auto' } }>
						<p style={ { color: "#777" } }>所选订单存在【
							<span style={ { color: "#f00" } }>有待发货的系统商品</span>
							】的订单，是否继续{isPrint ? '打印' : '发货'}？
						</p>
						<div className="r-mt-10">{content}</div>
					</div>
				),
				okText: `继续${isPrint ? '打印' : '发货'}`,
				cancelText: '取消',
				onOk: () => {
					resolve({
						allowCancelTrade: false
					});
					customLogPost(`继续`, {
						type: checkAbnormalType.有待发货的系统商品
					});
				},
				onCancel: () => {
					resolve(true);
					customLogPost(`取消`, {
						type: checkAbnormalType.有待发货的系统商品
					});
				}
			});
			customLogPost(`有待发货的系统商品,弹出异常弹框`, {
				type: checkAbnormalType.有待发货的系统商品
			});
			return;
		}
		// 校验抖店订单是否有存在线上改商品异常的情况
		if (abnormalTradeInfoObj.modifiedOnline) {
			const content = abnormalTradeInfoObj?.list?.map(tid => <p>{tid}</p>);

			Modal.confirm({
				centered: true,
				title: '订单打印异常提醒',
				bodyStyle: { padding: "20px 24px" },
				content: (
					<div style={ { maxHeight: 600, overflowY: 'auto' } }>
						<p style={ { color: "#777" } }>以下订单存在
							<span style={ { color: "#f00" } }>【线上改商品】</span>
							异常，请重新查询或同步订单后进行打印
						</p>
						<div className="r-mt-10">{content}</div>

					</div>
				),
				...ABNORMAL_MODAL_BUTTON_PROPS,
			});
			customLogPost(`线上改商品,弹出异常弹框`, {
				type: checkAbnormalType.线上改商品
			});
			return;
		}
		if (abnormalTradeInfoObj[checkAbnormalType.承诺日达]) {
			console.log('abnormalTradeInfoObj: ', abnormalTradeInfoObj);

			let name = tradeStore.isMergePrint ? tradeStore.selectedTempGroup.groupName : tradeStore.selectedTemp.ExcodeName;
			Modal.confirm({
				centered: true,
				title: '系统提醒',
				bodyStyle: { padding: "20px 24px" },
				content: (
					<div className="r-c-warning" style={ { maxHeight: 600, overflowY: 'auto' } }>
						<p>
							【{name}】与以下抖音订单不匹配，承诺日达订单平台限制必须使用
							{isPrint ? '顺丰或京东快递模版打印模板打印' : '顺丰或京东快递发货'}。
						</p>
						<p>订单号：{abnormalTradeInfoObj[checkAbnormalType.承诺日达]}</p>
					</div>
				),
				okText: `跳过异常继续${isPrint ? '打印' : '发货'}`,
				cancelText: '取消',
				onOk: () => {
					resolve(true);
					customLogPost(`跳过异常继续`, {
						type: checkAbnormalType.承诺日达,
						name
					});
				},
				onCancel: () => {
					resolve(false);
					customLogPost(`取消`, {
						type: checkAbnormalType.承诺日达,
						name
					});
				}
			});
			customLogPost(`承诺日达,弹出异常弹框`, {
				type: checkAbnormalType.承诺日达,
				name
			});
			return;
		}
		if (abnormalTradeInfoObj[checkAbnormalType.未填写商品识别码]) {

			Modal.confirm({
				centered: true,
				title: '系统提醒',
				bodyStyle: { padding: "20px 24px" },
				content: (
					<div className="r-c-warning" style={ { maxHeight: 600, overflowY: 'auto' } }>
						<p>
							以下订单下商品未填写商品识别码，根据平台发货要求，请先填写商品识别码后打单发货。
						</p>
						<p>订单号：{abnormalTradeInfoObj[checkAbnormalType.未填写商品识别码]}</p>
					</div>
				),
				okText: `跳过异常继续${isPrint ? '打印' : '发货'}`,
				cancelText: '取消',
				onOk: () => {
					resolve(true);
					customLogPost(`跳过异常继续`, {
						type: checkAbnormalType.未填写商品识别码,
					});
				},
				onCancel: () => {
					resolve(false);
					customLogPost(`取消`, {
						type: checkAbnormalType.未填写商品识别码,
					});
				}
			});
			customLogPost(`未填写商品识别码,弹出异常弹框`, {
				type: checkAbnormalType.未填写商品识别码,
			});
			return;
		}

		// 可以跳过的后继续打印
		const breakRes = await nextBreakModal(abnormalTradeInfoObj, fromType);
		dealCancelOrderChoice(abnormalTradeInfoObj, breakRes);
		if (!breakRes) {
			resolve(false);
			return;
		}
		// 可以强制打印
		const ignoreRes = await nextIgnoreModal(abnormalTradeInfoObj, fromType);
		resolve(ignoreRes);
	});
};

// 拿到忽略的订单
const getCancelChoiceOrder = (abnormalTradeInfoObj: AbnormalOrderProps) => {
	let cancelChoiceOrders:Array<string> = [];
	for (let item of abnormalTradeInfoObj.tradeObjMap.values()) {
		!item.selected && item.togetherId && cancelChoiceOrders.push(item.togetherId);
	}
	return cancelChoiceOrders;
};

// 根据异常结果，取消订单选择
export const dealCancelOrderChoice = (abnormalTradeInfoObj: AbnormalOrderProps, modalRes) => {
	const { tradeListStore: {
		handleChoiceChange
	},
	} = tradeStore;
	let cancelChoiceOrders:Array<string> = getCancelChoiceOrder(abnormalTradeInfoObj);
	// modalRes之前的类型是boolean，现在可能是object
	if (_.isPlainObject(modalRes)) {
		if (modalRes?.allowCancelTrade) {
			handleChoiceChange({
				type: TradeChoiceType.取消勾选指定订单,
				cancelChoiceOrders
			});
		}
	} else {
		handleChoiceChange({
			type: TradeChoiceType.取消勾选指定订单,
			cancelChoiceOrders
		});
	}
	return cancelChoiceOrders;
};
// 接口的异常校验， 分销关系异常，分销关系
export const checkDistributionToFilter = async(list: IPackage[], type) => {
	try {
		pageLoading.loading();
		const abnormalTradeInfoObj:any = await checkAbnormalOrders(list, type);
		pageLoading.destroy();
		if (abnormalTradeInfoObj) {
			const modalRes = await abnormalModalShow(abnormalTradeInfoObj, AbnormalFromType.发货);
			console.log('将获得的列表进行校验==>checkDistributionToFilter', abnormalTradeInfoObj, list);
			const cancelOrder = dealCancelOrderChoice(abnormalTradeInfoObj, modalRes);
			if (modalRes === true) {
				return list.filter(i => !cancelOrder.includes(i.togetherId));
			}
		}
		return list;
	} catch (e) {
		console.log('e: ', e);
	}
	pageLoading.destroy();
	return list;
};

// 判断所选平台和模板是否匹配

export const isOrderMatchingTemplate = (orderList: IPackage[], template:{[k:string]:any}) => {
	const {
		isMergePrint,
		setting,
	} = tradeStore;
	let notSupportedOrders: any = [];
	console.log('isMergePrintisMergePrint', isMergePrint, template);
	if (isMergePrint && !template.userTemplateList.length) {
		notSupportedOrders = [...orderList];
	} else if (isMergePrint) {
		let orderMatchSetting = setting?.groupPrintSetJsonString?.orderMatchSetting;
		notSupportedOrders = orderList.filter((order: IPackage) => {
			let orderKddType = PRINT_MAP[order.platform];
			if (dealPlatAsHandPlat(order.platform, null, order)) {
				// let itemPlatform = order.platform;
				// if (itemPlatform === 'other') itemPlatform = 'hand';
				let orderSettingType = orderMatchSetting.find(it => it.platform === transferOtherToHand(order.platform, order));
				orderKddType = orderSettingType.bindControlType;
			}
			let tempType = template.userTemplateList.find((item:any) => {
				if ([13, 16].includes(Number(item.expressType))) {
					return orderKddType == 13;
				} else {
					return item.expressType == orderKddType;
				}
			});
			return !tempType && ![PLAT_DW].includes(order?.platform);
		});
	} else {
		notSupportedOrders = orderList.filter((order: IPackage) => {
			let platform = [PLAT_ALI, PLAT_C2M].includes(order.platform) ? PLAT_TB : order.platform;

			// 快团团模板判断，不能选视屏号、有赞、得物模板
			if (order.platform == PLAT_KTT) {
				return [templateType4PlatformTypeEnum['sph'], templateType4PlatformTypeEnum['yz'], templateType4PlatformTypeEnum['dw']].includes(template.KddType);
			}
			// 1. 抖音明文手工单使用非抖音电子面单打印限制放开
			console.log('orderorder', order);
			if (order.platform == PLAT_FXG && order.source == 'HADN' && !order?.serviceTagList?.includes('ciphertextHandTrade') && template?.platform != PLAT_FXG) {
				return [];
			}
			// 其他不是等同于手工单的订单，且没有对应平台模板的需要拦截
			return !dealPlatAsHandPlat(order.platform, null, order) && platform != TEMP_MAP[template.KddType];
		});
	}
	return notSupportedOrders;
};
