/*
 * @Author: 唐荣洋 <EMAIL>
 * @Date: 2025-08-19 11:00:42
 * @Description:
 */
import React, { useState, useEffect } from 'react';
import { Modal, Form, Button, message, Tooltip, Checkbox, Select, InputNumber, Alert } from 'antd';
import { observer } from 'mobx-react';
import userStore from '@/stores/user';
import { SettingSaveUserSettingApi } from '@/apis/user';
import s from './index.module.scss';
import Icon from '@/components/Icon';
import Flex from '@/components/Flex';

interface LowProfitConfigModalProps {
	visible: boolean;
	onCancel: () => void;
	onOk: () => void;
}

const LowProfitConfigModal: React.FC<LowProfitConfigModalProps> = (props) => {
	const { visible, onOk, onCancel } = props;
	const [loading, setLoading] = useState(false);
	const [userSetting, setUserSetting] = useState({});
	const [form] = Form.useForm();
	const { isShowZeroStockVersion, userInfo } = userStore;
	// 判断是否为子账号
	const isSubAccount = !!userInfo?.subUserId;
	useEffect(() => {
		if (visible) {
			userStore.getUserSetting().then((res: any) => {
				setUserSetting(res);
				// 从用户设置中获取当前配置
				const config = res?.lowProfitTradeConfig || {};
				config.paidAmountConfig = config.paidAmountConfig || {
					basePriceType: 'COST_PRICE',
					enabled: false
				};
				config.estimatedGrossProfitConfig = config.estimatedGrossProfitConfig || {
					enabled: false,
					calculationParams: {
						basePriceType: 'COST_PRICE',
						enableBasePrice: false,
						enableSubtractCustomAmount: false
					}
				};
				form.setFieldsValue({
					...config,
				});
			});
		}
	}, [visible, form]);

	const handleSubmit = () => {
		form.validateFields().then((values) => {
			setLoading(true);
			SettingSaveUserSettingApi({
				...userSetting,
				lowProfitTradeConfig: values
			}).then(async(res) => {
				if (res) {
					message.success('保存成功');
					let currentSetting = {
						...userStore.userSetting,
						lowProfitTradeConfig: values
					};
					userStore.setUserSetting(currentSetting);
					onOk();
					onCancel();
				} else {
					message.error('保存失败');
				}
			}).finally(() => {
				setLoading(false);
			});
		});
	};

	return (
		<Modal
			title="低利润订单配置"
			visible={ visible }
			onCancel={ onCancel }
			width={ 600 }
			centered
			maskClosable={ false }
			footer={ (
				<Flex justifyContent="space-between" alignItems="center">
					<div className={ s.note }>
						说明：若公式计算结果小于0，则按0计
					</div>
					<div>
						<Button key="cancel" onClick={ onCancel }>
							取消
						</Button>
						<Button key="submit" type="primary" loading={ loading } onClick={ handleSubmit } disabled={ isSubAccount }>
							确定
						</Button>
					</div>
				</Flex>
			) }
		>
			<div>
				<Alert
					style={ { padding: '4px', marginBottom: '12px' } }
					description={ (
						<Flex>说明：
							<Flex direction="column">
								<div>1、根据取{isShowZeroStockVersion ? '平台商品管理' : "货品档案"}价格实时计算，仅支持主账号设置</div>
								<div>2、若公式计算结果小于0，则按0计</div>
							</Flex>
						</Flex>
					) }
				/>
				<Form form={ form } layout="vertical">
					{/* 商品实付配置 */}
					<div className={ s.configItem } >
						<Form.Item name={ ["paidAmountConfig", "enabled"] } valuePropName="checked" style={ { margin: 0 } }>
							<Checkbox disabled={ isSubAccount }>商品实付低于</Checkbox>
						</Form.Item>
						<Form.Item name={ ["paidAmountConfig", "basePriceType"] } style={ { margin: 0 } }>
							<Select style={ { width: 100 } } size="small" disabled={ isSubAccount }>
								<Select.Option value="COST_PRICE">成本价</Select.Option>
								{isShowZeroStockVersion ? null : <Select.Option value="SELLING_PRICE">售价</Select.Option>}
								<Select.Option value="TAG_PRICE">吊牌价</Select.Option>
							</Select>
						</Form.Item>
						<span>，进行异常拦截</span>
						<Tooltip
							title={ <div>若订单商品的单件成本分摊低于商品资料中设置的价格，进行异常提醒</div> }
							overlayStyle={ { maxWidth: 400 } }
						>
							<span style={ { color: "#666", marginLeft: 8 } }>
								<Icon type="wenhao-xian" size={ 16 } />
							</span>
						</Tooltip>
					</div>
					{/* 订单预估毛利配置 */}
					<div className={ s.configItem }>
						<Form.Item name={ ["estimatedGrossProfitConfig", "enabled"] } valuePropName="checked" style={ { margin: 0 } }>
							<Checkbox disabled={ isSubAccount }>订单预估毛利低于</Checkbox>
						</Form.Item>
						<Form.Item name={ ["estimatedGrossProfitConfig", "threshold"] } style={ { margin: 0 } }>
							<InputNumber
								size="small"
								style={ { width: 80 } }
								min={ 0 }
								precision={ 2 }
								placeholder="0.00"
								disabled={ isSubAccount }
							/>
						</Form.Item>
						<span>元，进行异常提醒</span>
					</div>

					{/* 计算公式配置 */}
					<div className={ s.section } style={ { marginLeft: '24px' } }>
						<div className={ s.profitFormula }>
							<span>毛利 = 实付金额 - </span>
							<Form.Item name={ ["estimatedGrossProfitConfig", "calculationParams", "enableBasePrice"] } valuePropName="checked" style={ { margin: 0 } }>
								<Checkbox disabled={ isSubAccount } />
							</Form.Item>
							<Form.Item name={ ["estimatedGrossProfitConfig", "calculationParams", "basePriceType"] } style={ { margin: 0 } }>
								<Select style={ { width: 100 } } size="small" disabled={ isSubAccount }>
									<Select.Option value="COST_PRICE">成本价</Select.Option>
									{isShowZeroStockVersion ? null : <Select.Option value="SELLING_PRICE">售价</Select.Option>}
									<Select.Option value="TAG_PRICE">吊牌价</Select.Option>
								</Select>
							</Form.Item>
							<span> - </span>
							<Form.Item name={ ["estimatedGrossProfitConfig", "calculationParams", "enableSubtractCustomAmount"] } valuePropName="checked" style={ { margin: 0 } }>
								<Checkbox disabled={ isSubAccount }>自定义金额</Checkbox>
							</Form.Item>
							<Form.Item name={ ["estimatedGrossProfitConfig", "calculationParams", "customAmount"] } style={ { margin: 0 } }>
								<InputNumber
									size="small"
									style={ { width: 80 } }
									precision={ 2 }
									placeholder="0.00"
									disabled={ isSubAccount }
								/>
							</Form.Item>
							<span>元</span>
						</div>
					</div>
				</Form>
			</div>
		</Modal>
	);
};

export default observer(LowProfitConfigModal);
