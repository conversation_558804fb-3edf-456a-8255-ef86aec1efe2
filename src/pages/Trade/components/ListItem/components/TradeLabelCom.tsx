
import React, { useMemo, useState } from "react";
import { observer } from "mobx-react";
import { Popover, Tooltip, Button } from "antd";
import { groupBy } from "lodash";
import { IPackage } from "@/pages/Trade/interface";
import { ConsolidateType, FxgConsolidateType, PRINT_CONTENT_MAX_LENGTH, PRINT_CONTENT_MAX_ROW, serviceTagTypeMap } from "@/pages/Trade/constants";
import { KsConsolidateType, consolidateContent, JdConsolidateType } from "@/pages/Trade/utils";
import { IgnoreStatus, JDOrderShipType, JDStoreOrderType, SendType } from "@/utils/enum/trade";
import { SecretPhoneTip } from "./SecretPhoneTip";
import { HL_LABEL_PLAT, PLAT_FXG, PLAT_TB } from "@/constants";
import { copyToPaste } from "@/utils";
import './tradeLabelCom.scss';

// 订单标签
const TradeLabelCom = (props: { pack: IPackage, colWidth: string | number }) => {
	const { colWidth, pack } = props;
	const [multiPackYdList, setMultiPackYdList] = useState({});
	let promiseLogisticsList = [];
	pack.trades.forEach(trade => {
		trade.promiseLogisticsList.forEach(item => {
			if (!promiseLogisticsList.includes(item.company)) {
				promiseLogisticsList.push(item.company);
			}
		});
	});

	const multiPackYdCount = useMemo(() => {
		const count = pack.trades.length == 1 ? pack.trades[0]?.multiPackYdCount : pack.multiPackYdCount;
		if (count > 1) {
			const multiPackYdList = [];
			pack?.trades?.forEach(trade => {
				trade?.orders?.forEach(order => {
					if (order.ydNoSet.length > 0) {
						order.ydNoSet?.forEach(item => {
							const [ydName, ydNo] = item?.split(':') || [];
							if (ydNo) {
								multiPackYdList.push({ ydNo, ydName });
							}
						});
					}
				});
			});
			const groupByYdName = groupBy(multiPackYdList, 'ydName');
			Object.keys(groupByYdName).forEach(key => {
				groupByYdName[key] = [...new Set(groupByYdName[key].map(item => item.ydNo))];
			});
			console.log(multiPackYdList, groupBy(multiPackYdList, 'ydName'), groupByYdName, 'multiPackYdList');
			setMultiPackYdList(groupByYdName);
		}
		return count;
	}, [pack.multiPackYdCount, pack.trades]);


	const ignoreType = pack.trades[0]?.ignoreType;

	const delaySendTimeTitleNode = () => (
		<div style={ { color: 'rgba(0, 0,0,.65)' } }>
			<div style={ { fontSize: '16px', color: '#000' } }>受客观因素影响，该订单<span style={ { color: '#FD8204' } }>发货时间可延迟</span></div>
			<div>1.受疫情等客观因素影响，快递可能无法正常配送；若该订单发货时间延迟，平台不做处罚； </div>
			<div>2.当该订单收货区域解除上述影响时，解除当日订单将重启承诺发货时间倒计时，请您密切关注变化，按照承诺发货时间发货，否则将受平台处罚；</div>
		</div>
	);
	const priorityDeliveryItem = pack._reachItem?.logisticsRecommendedDeliveryVo?.orderPrivilegeInfoList?.find(i => i.label == '优先发货');

	const filteredTrades = pack?.trades?.filter(trade => trade.isAuthorOrder) || []; // 抖音达人订单
	const hasTag = (tag:string) => pack.trades.some(trade => trade.serviceTagList.some(i => i == tag));
	return (
		<div style={ { width: colWidth } }>
			{pack.hasRefund ? <Tooltip title="此订单下有退款"><span className="trade-error-outlined">有退款</span></Tooltip> : ''}
			{pack.urge ? <span className="trade-error-outlined">催发货</span> : ''}
			{pack.isPreSale ? <span className="trade-error-outlined">含预售商品</span> : ''}
			{pack.orderTagList.find(item => item.name === 'region_black_delay_shipping')?.value ? (
				<Tooltip
					color="#fff"
					placement="bottom"
					overlayInnerStyle={ { width: 500 } }
					title={ delaySendTimeTitleNode }
				>
					<span className="trade-error-outlined">发货时效可延迟</span>
				</Tooltip>
			) : ''}
			{pack.promiseDeliveryTime ? (
				<Tooltip
					title={
						priorityDeliveryItem?.reason
					}
				>
					<span className="trade-error-outlined">优先发货</span>
				</Tooltip>
			) : ''}

			{multiPackYdCount > 1 ? (
				<Popover
					title="多运单号发货"
					content={ (
						<div onClick={ (e) => e.stopPropagation() }>
							<div>
								{Object.keys(multiPackYdList).map(key => (
									<div key={ key } className="r-flex">
										<div>{key}：</div>
										<div style={ { maxWidth: 300, wordWrap: 'break-word' } }>{multiPackYdList[key]?.join(',')}</div>
									</div>
								))}
							</div>
							<div className="r-ta-r">
								<Button
									onClick={
										() => {
											let str = '';
											Object.keys(multiPackYdList).forEach(key => {
												str += `${key}:${multiPackYdList[key]?.join(',')}\n`;
											});
											copyToPaste(str, "复制成功", true);
										}
									}
									type="link"
									style={ { padding: 0 } }
								>复制全部单号
								</Button>
							</div>
						</div>
					) }
					trigger="hover"
					placement="bottomLeft"
				>
					<span><span title="多运单号发货" className="trade-warn-filled">多包裹:{multiPackYdCount}</span></span>
				</Popover>
			) : null}

			{pack.trades.some(trade => trade.isSplit) ? <span className="trade-warn-filled">拆单</span> : ''}
			{/* {pack.onlineShip ? <span className="trade-warn-filled">线上已发货</span> : ''} */}
			{pack.trades.some(trade => trade.changeAdderFlag) ? <span className="trade-warn-filled">改地址</span> : ''}
			{pack.isPartShipped ? <span className="trade-warn-filled">部分发货</span> : ''}
			{pack.isPending ? <span className="trade-warn-filled">有挂起</span> : ''}
			{pack.isPreShip ? (<span className="trade-warn-filled">自动发货</span>) : ''}
			{pack.isMerge && pack.mergeTradeCount > 1 ? (
				<Tooltip title={ `合并${pack.mergeTradeCount}个订单` }>
					<span className="trade-warn-filled">合:{pack.mergeTradeCount}</span>
				</Tooltip>
			) : ''}
			{pack.isSplitSend ? <span className="trade-warn-filled">进行拆单发货</span> : ''}
			{pack.labelstatus ? <span className="trade-warn-filled">已生成商品标签</span> : ''}
			{pack.hasDeliveryOnDoor ? <span className="trade-warn-filled">送货上门</span> : ''}
			{pack.directMailActivity
				? (
					<Tooltip title="针对该订单的运费，平台会为您补贴广告红包，具体请至拼多多商家后台订单详情页查看。">
						<span className="trade-gray-filled">直邮活动</span>
					</Tooltip>
				)
				: ''}
			{pack.firstSend ? (
				<Tooltip title="先发货订单，已在系统中申请单号发货但未打印快递单">
					<span className="trade-warn-filled">先发货</span>
				</Tooltip>
			) : ''}
			{[IgnoreStatus.自动标记, IgnoreStatus.手动标记].includes(ignoreType) ? (
				<Tooltip title={ `此订单为空包订单(${IgnoreStatus[ignoreType]})` }>
					<span className="trade-warn-filled">空包订单</span>
				</Tooltip>
			) : ''}

			{pack.trades.some(trade => trade.serviceTagList.some(i => i == 'full_order_ship')) ? (
				<span className="trade-gray-filled">整单发货</span>
			) : ''}

			{pack.trades.some(trade => trade.serviceTagList.some(i => i == 'ciphertextHandTrade')) ? (
				<span className="trade-warn-filled">密文手工单</span>
			) : ''}

			{pack.isSysExchanged ? (
				<Tooltip title="此订单下有更换商品">
					<span className="trade-gray-filled">系统改商品</span>
				</Tooltip>
			) : ''}
			{pack.isOnlineExchanged ? (
				<Tooltip title="此订单下有更换商品">
					<span className="trade-gray-filled">线上改商品</span>
				</Tooltip>
			) : ''}
			{pack.isTotalPostFee ? <span className="trade-gray-filled">含运费</span> : ''}

			{/* 虚拟号 */}
			{pack.secret_no_expire_time ? (
				<SecretPhoneTip pack={ pack } />
			) : ''}
			{/* 京仓/云仓 */}
			{pack.jdStoreOrderType ? <span className="trade-gray-filled">{JDStoreOrderType[pack.jdStoreOrderType]}</span> : ''}
			{/* 京配/京配自提 */}
			{pack.jdOrderShipType ? <span className="trade-gray-filled">{JDOrderShipType[pack.jdOrderShipType]}</span> : ''}
			{/* 香港集运、新疆集运 */}
			{ConsolidateType[pack.consolidateType] ? consolidateContent(pack.consolidateType, 'list') : ''}
			{pack.hasTown ? <span className="trade-gray-filled">乡镇</span> : ''}
			{pack.hasSfExpressService ? <span className="trade-gray-filled">加运费发顺丰</span> : ''}
			{pack.hasGift ? <span className="trade-gray-filled">含平台赠品</span> : ''}
			{
				promiseLogisticsList.map((item: any) => {
					const promiseLogisticsAmountArr = pack.trades.map(trade => trade.promiseLogisticsAmount).filter(i => i);
					let promiseLogisticsLabel;
					if (promiseLogisticsAmountArr?.length) {
						promiseLogisticsLabel = `指定快递费用：${promiseLogisticsAmountArr.map(i => i + '元').join("，")}`;
					}
					return (
						<Tooltip title={ promiseLogisticsLabel }>
							<div className="trade-gray-filled">指定:{item}</div>
						</Tooltip>
					);
				})
			}
			{pack.wrapperDescription ? <div className="trade-gray-filled">指定包材</div> : ''}
			{pack.duoduoWholesale ? <div className="trade-gray-filled">多多批发</div> : ''}
			{pack.shipHold ? (
				<Tooltip title="该订单因发货地疫情影响，暂不支持发货，当疫情限制解除时，订单将重启承诺发货时间倒计时并支持发货">
					<div className="trade-gray-filled">暂停发货</div>
				</Tooltip>
			) : ''}
			{pack.freeSF ? (
				<Tooltip title={ pack.platform == PLAT_FXG ? '商家已选择本商品承诺顺丰包邮。为保障消费者体验，本商品请选择顺丰速运or顺丰快运发货，否则会扣除10元货款赔付给消费者' : '' }>
					<div className="trade-gray-filled">顺丰包邮</div>
				</Tooltip>
			) : ''}
			{pack.printContentOverflow ? (
				<Tooltip title={ `发货内容字数超出${PRINT_CONTENT_MAX_LENGTH}上限` }>
					<span className="trade-error-outlined">发货内容过长</span>
				</Tooltip>
			) : ''}

			{pack.printContentOverflowRow && !pack.printContentOverflow ? (
				<Tooltip title={ `发货内容行数超出${PRINT_CONTENT_MAX_ROW}行上限` }>
					<span className="trade-error-outlined">发货内容过长</span>
				</Tooltip>
			) : ''}

			{pack.sendType === SendType.重新发货 ? <div className="trade-warn-filled">重新发货</div> : ''}

			{/* 区分于 ConsolidateType  */}
			{FxgConsolidateType[pack.consolidateType] ? (
				<Tooltip title={ FxgConsolidateType[pack.consolidateType] }>
					<div className="trade-gray-filled">物流转运/集运</div>
				</Tooltip>
			) : ''}

			{KsConsolidateType[pack.consolidateType] ? (
				<Popover content={ KsConsolidateType[pack.consolidateType] }>
					<div className="trade-gray-filled">物流转运/集运</div>
				</Popover>
			) : ''}

			{JdConsolidateType[pack.consolidateType] ? (
				<Popover content={ JdConsolidateType[pack.consolidateType] }>
					<div className="trade-gray-filled">物流转运/集运</div>
				</Popover>
			) : ''}
			{pack.consolidateType == 'tb_gfjy' ? (
				<div className="trade-gray-filled">物流转运/集运</div>
			) : ''}

			{ pack.isFlashBuyingProducts ? <div className="trade-gray-filled">闪电购商品</div> : ''}
			{ pack.isSmallStoreSelfSelling ? <div className="trade-gray-filled">小店自卖</div> : ''}
			{ pack.isSelectedAlliance ? <div className="trade-gray-filled">精选联盟</div> : ''}
			{ pack.isCod ? <div className="trade-gray-filled">货到付款</div> : ''}
			{filteredTrades.length ? <div className="trade-gray-filled">达人订单</div> : ''}

			{ pack._brandDeliver ? <Tooltip title="支持得物平台"><div className="trade-gray-filled">品牌直发</div> </Tooltip> : ''}
			{ pack._ordinaryOrder ? <Tooltip title="支持得物平台"><div className="trade-gray-filled">普通履约</div></Tooltip> : ''}
			{ pack._promiseLogistics ? <Tooltip title="支持得物平台"><div className="trade-gray-filled">商家指定物流</div></Tooltip> : ''}
			{ pack._warehouseOrder ? <Tooltip title="支持得物平台"><div className="trade-gray-filled">多仓发货</div></Tooltip> : ''}
			{ hasTag('clearanceOrder') ? <div className="trade-gray-filled">清仓订单</div> : ''}

			{pack.trades.some(trade => trade.serviceTagList?.includes('jingxi')) ? (
				<Tooltip title="京喜订单">
					<span
						className="trade-warn-filled"
					>京喜
					</span>
				</Tooltip>
			) : ''}

			{/* 最新的标签都放这里 */}
			{
				Object.keys(serviceTagTypeMap).map(key => {
					if (pack.serviceTagList.includes(key)) {
						const tagDom = (
							<div
								className={ serviceTagTypeMap?.[key]?.class || "trade-gray-filled" }
							>{serviceTagTypeMap?.[key]?.tagName || serviceTagTypeMap?.[key]?.name}
							</div>
						);
						if (pack.platform === PLAT_TB && key === "presentOrder") {
							return (
								<Tooltip title="由于存在付款人和收货人不一致的订单类型，在收货人确认收货地址前，暂时无法获取相关订单，如有问题请前往千牛后台-订单管理处理">
									{tagDom}
								</Tooltip>
							);
						} else if (serviceTagTypeMap?.[key]?.tip) {
							return (
								<Tooltip title={ serviceTagTypeMap?.[key]?.tip }>
									{tagDom}
								</Tooltip>
							);
						} else {
							return tagDom;
						}
					} else {
						return '';
					}
				})
			}

			{/* 1688回流订单标签 */}
			{HL_LABEL_PLAT[pack.hlPlatformType] ? <div className="trade-warn-filled">{HL_LABEL_PLAT[pack.hlPlatformType]}</div> : '' }
			{HL_LABEL_PLAT[pack.hlPlatformType] ? (
				<Tooltip title={ pack.hlEncryptOrder ? '仅支持使用对应回流平台电子面单打印' : '支持菜鸟电子面单以及回流对应平台电子面单打印' }>
					<span className="trade-warn-filled">{pack.hlEncryptOrder ? '1688密文订单' : '1688明文订单'}</span>
				</Tooltip>
			) : ''}

			{ pack.appointmentArrival ? (
				<Popover
					content={ (
						<div className="r-fs-12 r-lh-20">
							<div className="r-fs-14 r-mb-5">承诺日达订单仅支持顺丰/京东快递打印发货</div>
							<div>承诺时间：   {pack.receiptDate}</div>
							<div>发货仓编号：{pack.outWarehouseId}</div>
							<div>发货仓名称：{pack.warehouseName}</div>
						</div>
					) }
					trigger="hover"
				>
					<span><div className="trade-warn-filled">承诺日达</div></span>
				</Popover>
			) : ''}

			{ pack.aliEntryServiceInfo ? (
				<div className="trade-warn-filled">定制服务</div>
			) : ''}
			{pack.needSerialNumber ? (
				<Tooltip title="由于平台要求，该类目下发货必须填写识别码（SN码、IMEI号、ICCID码）">
					<div className="trade-warn-filled">商品识别码</div>
				</Tooltip>
			) : ''}
			 {/* 添加波次号标签 */}
			 {pack.waveNoList && pack.waveNoList.length > 0 ? (
				// <Popover
				// 	content={ (
				// 		<div>
				// 			<div style={ { fontWeight: 'bold', marginBottom: '5px' } }>波次号列表</div>
				// 			{pack.waveNoList.map((waveNo, index) => (
				// 				<div key={ index }>{waveNo}</div>
				// 			))}
				// 		</div>
				// 	) }
				// 	trigger="hover"
				// 	placement="bottomLeft"
				// >
				<span className="trade-warn-filled">已生成拣货波次</span>
				// </Popover>
			) : null}
		</div>
	);
};

export default observer(TradeLabelCom);
