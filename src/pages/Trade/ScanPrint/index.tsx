import React, { useEffect, useCallback } from "react";
import cs from "classnames";
import { Button } from "antd";
import { useHistory } from "react-router-dom";
import { observer } from "mobx-react";
import { useRequest } from "ahooks";
import _ from "lodash";
import s from './index.module.scss';
import ScanPrintSearchContainer from "./components/ScanPrintSearchContainer";
import ScanPrintTradeInfo from "./components/ScanPrintTradeInfo";
import scanPrintEmpty from '@/assets/image/trade/scanPrintEmpty.png';
import ScanPrintSetting from "./components/ScanPrintSetting";
import scanPrintStore from "@/stores/trade/scanPrint";
import { ItemTakeGoodsLabelSelectTradeInfoByLabelIdApi } from "@/apis/trade/takeGoodsLabel";
import { IScanPrintFormSetting } from "../interface";
import { focusLabelInput, playAudio } from "./utils";
import voice标签错误 from '@/assets/mp3/标签错误.mp3';
import voice权限不足 from '@/assets/mp3/权限不足.mp3';
import voice重复扫描 from '@/assets/mp3/重复扫描.mp3';
import MergePrintModal from "./components/MergePrintDetectModal";
import tradeSetStore from "@/stores/trade/tradeSet";
import timer, { logTypeEnum } from "@/libs/timer";
import { getTimerTraceId } from "@/utils";
import PrintCenter from '@/print/index';
import { BQ_PRINT_TYPE } from "../constants";
import videoMonitorStore from "@/stores/trade/videoMonitor";

export interface IScanPrintProps {}

let newLabelId = ''; // 记录上一次扫描唯一码
let mixScanModeArray = [];

const ScanPrint = (props: IScanPrintProps) => {
	const history = useHistory();
	const {
		setScanPrintFormSetting,
		isShowScanPrintSettingModal,
		getScanPrintSetting,
		takeGoodsLabelInfos,
		scanPrintFormSetting,
		setSearchScanPrintFormSetting,
		updateTakeGoodsLabelInfo,
		clearScanPrintInfo,
		scanPrintTempInfo,
		prevLabelId,
		setPrevLabelId,
		scanPrintSetting,
		getColumnList,
		selectedShootingDevices,
		videoMonitorState,
		nvrDevices,
	} = scanPrintStore;
	const { 
		clearVideoMonitor,
		startVideoMonitor,
		videoMonitor,
		connectWs,
		checkKdzsPrintComponent,
		disconnectWs,
	} = videoMonitorStore;

	const { runAsync, loading } = useRequest(ItemTakeGoodsLabelSelectTradeInfoByLabelIdApi, {
		manual: true,
	});

	useEffect(() => {
		// 暂时跳过这里也清空下
		if (!prevLabelId) {
			newLabelId = '';
			mixScanModeArray = [];
		}
	}, [prevLabelId]);

	useEffect(() => {
		getColumnList();
	}, []);

	useEffect(() => {
		getScanPrintSetting();
	}, [getScanPrintSetting]);

	useEffect(() => {
		tradeSetStore.getFilterWord();
		tradeSetStore.getPrintContentSet();
		return () => {
			clearScanPrintInfo();
			setPrevLabelId("");
			newLabelId = "";
			mixScanModeArray = [];
		};
	}, []);

	useEffect(() => {
		if (scanPrintSetting?.scanSet?.isPrintTag) {
			PrintCenter.getBqTempList(BQ_PRINT_TYPE.吊牌);
		}
	}, [scanPrintSetting?.scanSet?.isPrintTag]);
	
	// 查询改为useCallback了，依赖handleFinish
	const handleFinish = useCallback(async(val:any) => {
		console.log('handleFinish', val);
		console.log('prevLabelId', prevLabelId, newLabelId, mixScanModeArray);
		const mixScanMode = !!scanPrintSetting?.scanSet?.mixScanMode;
		val.id = val.id.trim();
		// 判断是否需要发送视频监控消息
		const isIpcModel = selectedShootingDevices?.length > 0; 
		const isVideoMonitor = videoMonitor && videoMonitorState;
		clearVideoMonitor(isVideoMonitor, isIpcModel);
		// 上一步扫的和下一步一样，不管是不是混扫，都算重复扫描
		if (prevLabelId === val.id || newLabelId === val.id || (mixScanMode && mixScanModeArray?.includes(val.id))) {
			scanPrintTempInfo.scanInfoMsg = "重复扫描订单";
			playAudio(voice重复扫描);
			console.log('xxxxxx111', 'voice重复扫描');
			focusLabelInput();
			return;
		}
		
		newLabelId = val.id;
		if (mixScanMode) {
			mixScanModeArray.push(val.id);
			if (mixScanModeArray.length > 10) {
				mixScanModeArray.shift(); 
			}
		}

		timer.start(`小标签扫描`);
		let traceId = await getTimerTraceId();
		window.scanPrintTraceId = traceId;
		console.log('TraceId:', traceId);
		setPrevLabelId(val.id);
		let _scanPrintFormSetting = _.cloneDeep(scanPrintFormSetting);
		// 多件订单混扫备齐模式
		runAsync({
			labelId: val.id,
			mixScanMode,
			stockpileAfterScan: val.stockpileAfterScan
		}).then(async res => {
			timer.stop(`小标签扫描`, {
				type: logTypeEnum['小标签'],
				desc: `${val.id}`
			});
			setSearchScanPrintFormSetting(_scanPrintFormSetting || val);
			updateTakeGoodsLabelInfo(res).then(() => {
				console.log('123123123', scanPrintTempInfo);
				if (scanPrintTempInfo?.goodsTotalCount > 0) {
					console.log('reresresresress', res);
					const outSid = val?.id?.trim();
					const texts = [`唯一码：${outSid}`];
					const isKd = false;
					const data = {
						outSid,
						orderList: res?.packageInfoVos || [],
						isIpcModel,
						isVideoMonitor,
						nvrDevices,
						selectedShootingDevices,
						texts,
						isKd
					};
					// console.log(data, scanTypeOptionsMemo, scanType, '0000000000000');
					// 开启监控
					startVideoMonitor(data, 5);
				}
			});	

		}).catch(res => {
			// 接口出错了，重置下记录的id
			newLabelId = '';
			if (mixScanMode) {
				mixScanModeArray = mixScanModeArray?.filter(item => item !== val.id) || [];
			}
			setPrevLabelId("");
			
			console.log('%c [ 获取标签出错 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', res);
			timer.stop(`小标签扫描`, {
				type: logTypeEnum['小标签'],
				desc: `${val.id}`
			});

			// 这里被请求重复拦截
			if (typeof res === 'string' && res?.includes('重复的请求被主动拦截')) {
				scanPrintTempInfo.scanInfoMsg = "重复扫描订单";
				playAudio(voice重复扫描);
				console.log('xxxxxx112', 'voice重复扫描');
			} else {
				scanPrintTempInfo.scanInfoMsg = <span className={ s['scan-info-content-error'] }>{res.errorMessage || '标签错误'}</span>;
				if (res.errorCode === 2003) {
					playAudio(voice权限不足);
				} else {
					playAudio(voice标签错误);
				}
			}
			
			window.errorCollection?.customMessageUpload({
				type: `扫描打印【标签错误】${res.errorCode === 2003 ? '权限不足' : ''}`, 
				data: { val, res }
			});
			focusLabelInput();
		}).finally(() => {
			// setTimeout(() => {
			// 	focusLabelInput();
			// }, 500);
		});
	}, [prevLabelId, scanPrintSetting, scanPrintFormSetting, scanPrintTempInfo]);

	const handleChange = (val: IScanPrintFormSetting) => {
		// console.log('%c [ handleChange val ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', val);
		setScanPrintFormSetting({
			...scanPrintFormSetting,
			...val,
			id: val?.id?.trim(),
		});
	};

	// const handleReconnect = async() => {
	// 	console.log('重新连接');
	// 	await checkKdzsPrintComponent();
	// 	connectWs();
	// };
	// useEffect(() => {
	// 	if (videoMonitorState && videoMonitor) {
	// 		handleReconnect(); // 初始化视频监控相关
	// 	} else {
	// 		disconnectWs(); // 断开ERP聚合控件连接
	// 	}
	// }, [videoMonitorState, videoMonitor]);
	return (
		<div className={ cs(s['scanPrintPage']) }>
			<ScanPrintSearchContainer
				loading={ loading }
				onFinish={ handleFinish }
				onChange={ handleChange }
			/>
			{!takeGoodsLabelInfos ? (
				<div className="r-flex r-fd-c r-ai-c r-jc-c r-flex-1">
					<img style={ { width: 200, height: 200 } } src={ scanPrintEmpty } alt="empty" />
					<p className="r-fs-14 r-c-black45 r-mt-12 r-mb-24">扫描打印前请先设置扫描打印小标签模版</p>
					<Button type="primary" onClick={ () => { history.push('/settings/goodsLabel'); } }>前往设置小标签</Button>
				</div>
			) : <ScanPrintTradeInfo /> }

			{isShowScanPrintSettingModal && (
				<ScanPrintSetting />
			)}

			<MergePrintModal />
		</div>
	);
};

export default observer(ScanPrint);
